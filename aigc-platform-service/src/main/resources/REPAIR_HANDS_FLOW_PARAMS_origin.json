{"client_id": "${clientId}", "prompt": {"1": {"inputs": {"noise": ["4", 0], "guider": ["5", 0], "sampler": ["7", 0], "sigmas": ["8", 0], "latent_image": ["94", 0]}, "class_type": "SamplerCustomAdvanced"}, "2": {"inputs": {"samples": ["1", 0], "vae": ["144", 0]}, "class_type": "VAEDecode"}, "4": {"inputs": {"noise_seed": "${seed}"}, "class_type": "RandomNoise"}, "5": {"inputs": {"model": ["6", 0], "conditioning": ["9", 0]}, "class_type": "BasicGuider"}, "6": {"inputs": {"model": ["146", 0]}, "class_type": "DifferentialDiffusion"}, "7": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "8": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 0.75, "model": ["146", 0]}, "class_type": "BasicScheduler"}, "9": {"inputs": {"positive": ["10", 0], "negative": ["11", 0], "pixels": ["72", 0], "mask": ["73", 0], "vae": ["144", 0]}, "class_type": "InpaintModelConditioning"}, "10": {"inputs": {"guidance": 3.5, "conditioning": ["16", 0]}, "class_type": "FluxGuidance"}, "11": {"inputs": {"text": "", "clip": ["145", 0]}, "class_type": "CLIPTextEncode"}, "16": {"inputs": {"text": "hand of young fashion model, clean nails, jewelry-free, high quality, high detail, realistic, no tattoos, no nail polish, no jewelry", "clip": ["145", 0]}, "class_type": "CLIPTextEncode"}, "20": {"inputs": {"VAE": ["144", 0]}, "class_type": "Anything Everywhere"}, "72": {"inputs": {"image": "${repairImage}", "upload": "image"}, "class_type": "LoadImage"}, "73": {"inputs": {"expand": 5, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["72", 1]}, "class_type": "ConrainGrowMaskWithBlur"}, "94": {"inputs": {"amount": 2, "samples": ["9", 2]}, "class_type": "RepeatLatentBatch"}, "95": {"inputs": {"expand_LRU": 40, "expand_B": 60, "image": ["72", 0], "mask": ["73", 0]}, "class_type": "BoundedImageCropWithMask_v3_LR"}, "104": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["95", 3], "image": ["95", 0]}, "class_type": "ImageScaleBy"}, "105": {"inputs": {"scale_by": ["95", 3], "mask": ["95", 2]}, "class_type": "MaskUpscale_LR"}, "118": {"inputs": {"noise": ["119", 0], "guider": ["120", 0], "sampler": ["121", 0], "sigmas": ["122", 0], "latent_image": ["127", 0]}, "class_type": "SamplerCustomAdvanced"}, "119": {"inputs": {"noise_seed": "${seed2}"}, "class_type": "RandomNoise"}, "120": {"inputs": {"model": ["6", 0], "conditioning": ["123", 0]}, "class_type": "BasicGuider"}, "121": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "122": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 0.75, "model": ["146", 0]}, "class_type": "BasicScheduler"}, "123": {"inputs": {"positive": ["10", 0], "negative": ["11", 0], "pixels": ["104", 0], "mask": ["105", 0], "vae": ["144", 0]}, "class_type": "InpaintModelConditioning"}, "125": {"inputs": {"samples": ["118", 0], "vae": ["144", 0]}, "class_type": "VAEDecode"}, "127": {"inputs": {"amount": 2, "samples": ["123", 2]}, "class_type": "RepeatLatentBatch"}, "130": {"inputs": {"amount": 2, "image": ["72", 0]}, "class_type": "RepeatImageBatch"}, "138": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["130", 0], "target_mask": ["140", 0], "target_bounds": ["95", 1], "source": ["125", 0]}, "class_type": "Bounded Image Blend with Mask"}, "140": {"inputs": {"expand": 15, "tapered_corners": false, "mask": ["72", 1]}, "class_type": "GrowMask"}, "141": {"inputs": {"image1": ["148", 0], "image2": ["138", 0]}, "class_type": "ImageBatch"}, "142": {"inputs": {"output_path": "${outputPath}", "filename_prefix": "${fileNamePrefix}", "extension": "jpg", "dpi": 300, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["141", 0]}, "class_type": "ConrainImageSave"}, "144": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "145": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "146": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "148": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["130", 0], "target_mask": ["140", 0], "target_bounds": ["150", 0], "source": ["2", 0]}, "class_type": "Bounded Image Blend with Mask"}, "150": {"inputs": {"image": ["72", 0]}, "class_type": "Image Bounds"}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 159, "last_link_id": 319, "nodes": [{"id": 5, "type": "BasicGuider", "pos": {"0": 390.81829833984375, "1": 174.06961059570312}, "size": {"0": 161.1999969482422, "1": 46}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 9}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [4], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}}, {"id": 7, "type": "KSamplerSelect", "pos": {"0": 340.81829833984375, "1": 274.0696105957031}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [6], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 8, "type": "BasicScheduler", "pos": {"0": 340.81829833984375, "1": 374.0696105957031}, "size": {"0": 210, "1": 106}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 27}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.75]}, {"id": 23, "type": "Reroute", "pos": {"0": -53.18169021606445, "1": 52.069610595703125}, "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 179}], "outputs": [{"name": "", "type": "MODEL", "links": [26, 27], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 90, "type": "Reroute", "pos": {"0": -866.1282348632812, "1": 72.44730377197266}, "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 291}], "outputs": [{"name": "", "type": "CLIP", "links": [183, 184], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 6, "type": "DifferentialDiffusion", "pos": {"0": 28.818309783935547, "1": 170.06961059570312}, "size": {"0": 184.8000030517578, "1": 26}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 26}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [5, 227], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}}, {"id": 89, "type": "Reroute", "pos": {"0": -859, "1": 25}, "size": [75, 26], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 290}], "outputs": [{"name": "", "type": "MODEL", "links": [179, 234], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 120, "type": "BasicGuider", "pos": {"0": 370, "1": 780}, "size": {"0": 161.1999969482422, "1": 46}, "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 227, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 228}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [223], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}}, {"id": 121, "type": "KSamplerSelect", "pos": {"0": 320, "1": 880}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [224], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 122, "type": "BasicScheduler", "pos": {"0": 320, "1": 990}, "size": {"0": 210, "1": 106}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 229}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [225], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.75]}, {"id": 124, "type": "Reroute", "pos": {"0": -63.704559326171875, "1": 621.5770874023438}, "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 234}], "outputs": [{"name": "", "type": "MODEL", "links": [229]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 125, "type": "VAEDecode", "pos": {"0": 1120, "1": 740}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 235}, {"name": "vae", "type": "VAE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [268], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 62, "type": "Reroute", "pos": {"0": -769.93896484375, "1": 748.3746948242188}, "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 130}], "outputs": [{"name": "", "type": "MASK", "links": [131, 272], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 73, "type": "ConrainGrowMaskWithBlur", "pos": {"0": -529.93896484375, "1": 596.3746948242188}, "size": {"0": 315, "1": 246}, "flags": {"collapsed": false}, "order": 20, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 131}], "outputs": [{"name": "mask", "type": "MASK", "links": [193, 240], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [5, 0, true, false, 10, 1, 1, false]}, {"id": 118, "type": "SamplerCustomAdvanced", "pos": {"0": 670, "1": 740}, "size": {"0": 355.20001220703125, "1": 106}, "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 222, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 223, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 224, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 225, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 226}], "outputs": [{"name": "output", "type": "LATENT", "links": [235], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}}, {"id": 119, "type": "RandomNoise", "pos": {"0": 315, "1": 626}, "size": {"0": 292.23980712890625, "1": 82.74410247802734}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [222], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed2}", "randomize"]}, {"id": 4, "type": "RandomNoise", "pos": {"0": 340.81829833984375, "1": 24.069612503051758}, "size": {"0": 277.9598693847656, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [3], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed}", "randomize"]}, {"id": 10, "type": "FluxGuidance", "pos": {"0": 28.818309783935547, "1": 261.0696105957031}, "size": {"0": 211.60000610351562, "1": 58}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 15, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10, 230], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 94, "type": "RepeatLatentBatch", "pos": {"0": 695.8182983398438, "1": 401.0696105957031}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 189}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [190], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2]}, {"id": 104, "type": "ImageScaleBy", "pos": {"0": -890, "1": 1220}, "size": {"0": 320, "1": 80}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 207, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 208, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [238], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 105, "type": "MaskUpscale_LR", "pos": {"0": -899, "1": 1477}, "size": {"0": 320, "1": 60}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 209, "label": "mask"}, {"name": "scale_by", "type": "FLOAT", "link": 210, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [239], "slot_index": 0, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "widgets_values": [4]}, {"id": 16, "type": "CLIPTextEncode", "pos": {"0": -588, "1": 64}, "size": {"0": 394.***********, "1": 153.04241943359375}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 183, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [15], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["hand of young fashion model, clean nails, jewelry-free, high quality, high detail, realistic, no tattoos, no nail polish, no jewelry"]}, {"id": 11, "type": "CLIPTextEncode", "pos": {"0": -569, "1": 340}, "size": {"0": 266.98651123046875, "1": 76}, "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 184}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11, 231], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 145, "type": "DualCLIPLoader", "pos": {"0": -1261.913330078125, "1": 162.11502075195312}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [291], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 144, "type": "VAELoader", "pos": {"0": -1262.913330078125, "1": 337.1151428222656}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [292], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 146, "type": "UNETLoader", "pos": {"0": -1259, "1": 25}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [290], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 20, "type": "Anything Everywhere", "pos": {"0": -983, "1": 367}, "size": {"0": 210, "1": 26}, "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"name": "VAE", "type": "*", "link": 292, "color_on": "#FF6E6E"}], "outputs": [], "properties": {"Node name for S&R": "Anything Everywhere", "group_restricted": 0, "color_restricted": 0}, "widgets_values": []}, {"id": 123, "type": "InpaintModelConditioning", "pos": {"0": 13, "1": 943}, "size": {"0": 216.59999084472656, "1": 106}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 230}, {"name": "negative", "type": "CONDITIONING", "link": 231, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": null}, {"name": "pixels", "type": "IMAGE", "link": 238}, {"name": "mask", "type": "MASK", "link": 239, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [228], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [237], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}}, {"id": 1, "type": "SamplerCustomAdvanced", "pos": {"0": 691, "1": 124}, "size": {"0": 355.20001220703125, "1": 106}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 3, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 4, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 6, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 7, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 190}], "outputs": [{"name": "output", "type": "LATENT", "links": [1], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}}, {"id": 9, "type": "InpaintModelConditioning", "pos": {"0": 22, "1": 383}, "size": {"0": 216.59999084472656, "1": 106}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 10}, {"name": "negative", "type": "CONDITIONING", "link": 11, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": null}, {"name": "pixels", "type": "IMAGE", "link": 135}, {"name": "mask", "type": "MASK", "link": 193, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [9], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [189], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}}, {"id": 127, "type": "RepeatLatentBatch", "pos": {"0": 670, "1": 970}, "size": {"0": 315, "1": 58}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 237}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [226], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2]}, {"id": 2, "type": "VAEDecode", "pos": {"0": 1121, "1": 124}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1}, {"name": "vae", "type": "VAE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [297], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 95, "type": "BoundedImageCropWithMask_v3_LR", "pos": {"0": -1274, "1": 1283}, "size": {"0": 285.6000061035156, "1": 142}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 195}, {"name": "mask", "type": "MASK", "link": 240}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [207], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [267], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [209], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [208, 210], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [40, 60]}, {"id": 130, "type": "RepeatImageBatch", "pos": {"0": -435, "1": 1369}, "size": {"0": 254.88865661621094, "1": 58}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 243, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [294, 295], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "RepeatImageBatch"}, "widgets_values": [2]}, {"id": 150, "type": "Image Bounds", "pos": {"0": -431, "1": 1230}, "size": {"0": 229.20001220703125, "1": 26}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 300}], "outputs": [{"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [299], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Image Bounds"}}, {"id": 140, "type": "GrowMask", "pos": {"0": -436, "1": 1534}, "size": {"0": 275.2359313964844, "1": 82.3905258178711}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 272}], "outputs": [{"name": "MASK", "type": "MASK", "links": [273, 296], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [15, false]}, {"id": 72, "type": "LoadImage", "pos": {"0": -1264, "1": 631}, "size": {"0": 420.75506591796875, "1": 397.5462646484375}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [168], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [130], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${repairImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 56, "type": "Reroute", "pos": {"0": -771.93896484375, "1": 668.3746948242188}, "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 168}], "outputs": [{"name": "", "type": "IMAGE", "links": [135, 195, 243, 300], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 148, "type": "Bounded Image Blend with Mask", "pos": {"0": 29, "1": 1237}, "size": {"0": 315, "1": 142}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 295}, {"name": "target_mask", "type": "MASK", "link": 296}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 299}, {"name": "source", "type": "IMAGE", "link": 297}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [302], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 138, "type": "Bounded Image Blend with Mask", "pos": {"0": 31, "1": 1491}, "size": {"0": 315, "1": 142}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 294}, {"name": "target_mask", "type": "MASK", "link": 273}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 267}, {"name": "source", "type": "IMAGE", "link": 268}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [281], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 141, "type": "ImageBatch", "pos": {"0": 612, "1": 1290}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 302}, {"name": "image2", "type": "IMAGE", "link": 281}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [289], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageBatch"}}, {"id": 143, "type": "PrimitiveNode", "pos": {"0": 621, "1": 1448}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [288], "slot_index": 0, "widget": {"name": "output_path"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["${outputPath}"], "color": "#232", "bgcolor": "#353"}, {"id": 142, "type": "ConrainImageSave", "pos": {"0": 1012, "1": 1292}, "size": {"0": 315, "1": 274}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 289}, {"name": "output_path", "type": "STRING", "link": 288, "widget": {"name": "output_path"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "jpg", 300, 100, "true", "false", "false", "true", "true"]}], "links": [[1, 1, 0, 2, 0, "LATENT"], [3, 4, 0, 1, 0, "NOISE"], [4, 5, 0, 1, 1, "GUIDER"], [5, 6, 0, 5, 0, "MODEL"], [6, 7, 0, 1, 2, "SAMPLER"], [7, 8, 0, 1, 3, "SIGMAS"], [9, 9, 0, 5, 1, "CONDITIONING"], [10, 10, 0, 9, 0, "CONDITIONING"], [11, 11, 0, 9, 1, "CONDITIONING"], [15, 16, 0, 10, 0, "CONDITIONING"], [26, 23, 0, 6, 0, "MODEL"], [27, 23, 0, 8, 0, "MODEL"], [83, 19, 0, 2, 1, "VAE"], [84, 19, 0, 9, 2, "VAE"], [85, 19, 0, 2, 1, "VAE"], [86, 19, 0, 9, 2, "VAE"], [90, 19, 0, 2, 1, "VAE"], [91, 19, 0, 9, 2, "VAE"], [130, 72, 1, 62, 0, "*"], [131, 62, 0, 73, 0, "MASK"], [135, 56, 0, 9, 3, "IMAGE"], [168, 72, 0, 56, 0, "*"], [179, 89, 0, 23, 0, "*"], [183, 90, 0, 16, 0, "CLIP"], [184, 90, 0, 11, 0, "CLIP"], [189, 9, 2, 94, 0, "LATENT"], [190, 94, 0, 1, 4, "LATENT"], [193, 73, 0, 9, 4, "MASK"], [195, 56, 0, 95, 0, "IMAGE"], [207, 95, 0, 104, 0, "IMAGE"], [208, 95, 3, 104, 1, "FLOAT"], [209, 95, 2, 105, 0, "MASK"], [210, 95, 3, 105, 1, "FLOAT"], [222, 119, 0, 118, 0, "NOISE"], [223, 120, 0, 118, 1, "GUIDER"], [224, 121, 0, 118, 2, "SAMPLER"], [225, 122, 0, 118, 3, "SIGMAS"], [226, 127, 0, 118, 4, "LATENT"], [227, 6, 0, 120, 0, "MODEL"], [228, 123, 0, 120, 1, "CONDITIONING"], [229, 124, 0, 122, 0, "MODEL"], [230, 10, 0, 123, 0, "CONDITIONING"], [231, 11, 0, 123, 1, "CONDITIONING"], [234, 89, 0, 124, 0, "*"], [235, 118, 0, 125, 0, "LATENT"], [237, 123, 2, 127, 0, "LATENT"], [238, 104, 0, 123, 3, "IMAGE"], [239, 105, 0, 123, 4, "MASK"], [240, 73, 0, 95, 1, "MASK"], [243, 56, 0, 130, 0, "IMAGE"], [267, 95, 1, 138, 2, "IMAGE_BOUNDS"], [268, 125, 0, 138, 3, "IMAGE"], [272, 62, 0, 140, 0, "MASK"], [273, 140, 0, 138, 1, "MASK"], [281, 138, 0, 141, 1, "IMAGE"], [283, 19, 0, 9, 2, "VAE"], [284, 19, 0, 125, 1, "VAE"], [285, 19, 0, 2, 1, "VAE"], [286, 19, 0, 123, 2, "VAE"], [288, 143, 0, 142, 1, "STRING"], [289, 141, 0, 142, 0, "IMAGE"], [290, 146, 0, 89, 0, "*"], [291, 145, 0, 90, 0, "*"], [292, 144, 0, 20, 0, "VAE"], [294, 130, 0, 138, 0, "IMAGE"], [295, 130, 0, 148, 0, "IMAGE"], [296, 140, 0, 148, 1, "MASK"], [297, 2, 0, 148, 3, "IMAGE"], [299, 150, 0, 148, 2, "IMAGE_BOUNDS"], [300, 56, 0, 150, 0, "IMAGE"], [302, 148, 0, 141, 0, "IMAGE"], [316, 144, 0, 125, 1, "VAE"], [317, 144, 0, 123, 2, "VAE"], [318, 144, 0, 9, 2, "VAE"], [319, 144, 0, 2, 1, "VAE"]], "groups": [{"title": "Group", "bounding": [-486, 1143, 1871, 528], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-1284, 1143, 782, 528], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-70, 529, 1456, 593], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-1284, 526, 1199, 596], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-1285, -51, 1200, 552], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "Group", "bounding": [-71, -50, 1456, 552], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.42409761837248483, "offset": [2156.8586530342695, 263.40671261173435]}}, "version": 0.4, "seed_widgets": {"4": 0, "119": 0}}}}}
{"client_id": "70719706bfb34cda8a3d57728c4fc3cb", "prompt": {"177": {"inputs": {"text": "product/20250609/1/344766", "text_b": "product_2295919", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["283", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1785", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["584", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": ""}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB", "_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "268": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "beta", "steps": "30", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "277": {"inputs": {"noise_seed": 548786345280158}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["268", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["378", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), front view,whole body,9-year-old Chinese boy is wearing A light pink t-shirt made from soft cotton, featuring a bold silver and blue cartoon graphic with the text 'DALE HARCA' in a sharp, stylized font and a character wearing a blue cap printed across the chest, Paired with light blue denim shorts, knee-length with a faded wash, and multiple frayed ruffle details along both side seams.9-year-old Chinese boy,  is wearing { a dark bucket hat}. 9-year-old Chinese boy,  is wearing mgs2222 white sneakers with green accents. The  mgs2222 white sneakers with green accents feature solid color and simple. \n\n\nmgs2222,a forest setting with trees and a river in the background.standing casually.standing with one leg slightly forward and hands relaxed by the sides.\n\n\na mgm3004 golden alabaster skin chinese male model,9-year-old, {smile|laugh},  short straight dark brown hair with a natural side part,", "seed": 2048}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": 1785}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": 1340}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "online_product/男童014_copy_24065_20250605_194722/男童014_copy_24065_20250605_194722-flux/男童014_copy_24065_20250605_194722-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "online_product/6-9男童模-凯凯_24870_20250607_202314/6-9男童模-凯凯_24870_20250607_202314-flux/6-9男童模-凯凯_24870_20250607_202314-flux.safetensors", "strength_model": "0.6", "strength_clip": "0.6", "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "298": {"inputs": {"lora_name": "online_product/芳羽绒服1_19701_20250521_212806/芳羽绒服1_19701_20250521_212806-flux/芳羽绒服1_19701_20250521_212806-flux.safetensors", "strength_model": "0.8", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "331": {"inputs": {"prompts": "a mgm3004 golden alabaster skin chinese male model,9-year-old, {smile|laugh},  short straight dark brown hair with a natural side part,", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["584", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "373": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "378": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["508", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "446": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["447", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "447": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["615", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "462": {"inputs": {"image": "1790e0557eb0431798bfb123e6c141fd.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "474": {"inputs": {"Input": ["547", 0], "image1": ["475", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "475": {"inputs": {"image": "MqD3_face.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "476": {"inputs": {"image": "prod_fix_a82119462f1b4b82b2e61a40a4b6ebd7.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "477": {"inputs": {"Input": ["548", 0], "image1": ["476", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "508": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "model": ["296", 0], "pulid_flux": ["512", 0], "eva_clip": ["513", 0], "face_analysis": ["515", 0], "image": ["536", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "512": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "513": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "515": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "525": {"inputs": {"method": "selfie_multiclass_256x256", "confidence": 0.4, "crop_multi": 0, "mask_components": "1,3", "image": ["462", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "536": {"inputs": {"padding_left": 96, "padding_right": 96, "padding_top": 96, "padding_bottom": 96, "image": ["462", 0], "mask": ["525", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "547": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "548": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "549": {"inputs": {"delimiter": ": ", "clean_whitespace": "true", "text_a": ["550", 0], "text_b": ["282", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "550": {"inputs": {"text": "Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "552": {"inputs": {"prompt": ["549", 0], "llm_model": "default"}, "class_type": "LLModel", "_meta": {"title": "llmodel"}}, "557": {"inputs": {"expand_LRU": 20, "expand_B": 20, "image": ["269", 0], "mask": ["585", 4]}, "class_type": "BoundedImageCropWithMask_v3_LR", "_meta": {"title": "Bounded Image Crop With Mask LR v3"}}, "561": {"inputs": {"amount": 1, "samples": ["578", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "562": {"inputs": {"samples": ["569", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "563": {"inputs": {"scale_by": ["564", 0], "mask": ["565", 0]}, "class_type": "MaskUpscale_LR", "_meta": {"title": "Mask Upscale LR"}}, "564": {"inputs": {"target_size": 1024, "image": ["557", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "565": {"inputs": {"expand": -10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 15, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["557", 2]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "567": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["564", 0], "image": ["557", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "568": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": true}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "569": {"inputs": {"noise": ["579", 0], "guider": ["580", 0], "sampler": ["581", 0], "sigmas": ["577", 0], "latent_image": ["561", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "570": {"inputs": {"text": ["331", 0], "clip": ["271", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "571": {"inputs": {"lora_name": "child_face_lora_20250607.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "572": {"inputs": {"lora_name": "online_product/10-14岁男内童模-果麦_22841_20250530_194005/10-14岁男内童模-果麦_22841_20250530_194005-flux/10-14岁男内童模-果麦_22841_20250530_194005-flux.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["571", 0], "clip": ["571", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "574": {"inputs": {"guidance": 2, "conditioning": ["570", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "575": {"inputs": {"conditioning": ["570", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "576": {"inputs": {"model": ["572", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "577": {"inputs": {"scheduler": "sgm_uniform", "steps": 8, "denoise": 0.15, "model": ["572", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "578": {"inputs": {"noise_mask": false, "positive": ["574", 0], "negative": ["575", 0], "vae": ["270", 0], "pixels": ["598", 0], "mask": ["607", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "579": {"inputs": {"noise_seed": 0}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "580": {"inputs": {"model": ["576", 0], "conditioning": ["578", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "581": {"inputs": {"sampler_name": "dpm_2"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "584": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["269", 0], "target_mask": ["565", 0], "target_bounds": ["557", 1], "source": ["597", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "585": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["568", 0], "image": ["269", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "588": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "589": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["567", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "590": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["589", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "591": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["590", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "592": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["568", 0], "image": ["591", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "593": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "594": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["588", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "595": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "596": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "597": {"inputs": {"images": ["567", 0], "face": ["590", 0], "crop": ["601", 0], "mask": ["602", 0], "warp": ["591", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "598": {"inputs": {"samples": ["599", 0], "vae": ["588", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "599": {"inputs": {"seed": 42029499340256, "steps": 4, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "denoise": 0.5, "model": ["606", 0], "positive": ["600", 0], "negative": ["600", 1], "latent_image": ["600", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "600": {"inputs": {"noise_mask": true, "positive": ["606", 1], "negative": ["606", 2], "vae": ["588", 2], "pixels": ["591", 0], "mask": ["607", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "601": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["591", 0], "image_target": ["562", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "602": {"inputs": {"expand": 40, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["607", 0]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "604": {"inputs": {"text": ["331", 0], "clip": ["588", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "605": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["591", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "606": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["595", 0], "insightface": ["596", 0], "control_net": ["593", 0], "image": ["446", 0], "model": ["588", 0], "positive": ["604", 0], "negative": ["594", 0], "image_kps": ["591", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "607": {"inputs": {"invert_mask": false, "grow": 5, "blur": 4, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["605", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "608": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "upscale if smaller", "multiple_of": 0, "image": ["629", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "609": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "610": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["609", 0], "image": ["608", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "611": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["610", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "612": {"inputs": {"segs": ["611", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "613": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["612", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "614": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["608", 0], "mask": ["613", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "615": {"inputs": {"images": ["614", 0]}, "class_type": "easy imageListToImageBatch", "_meta": {"title": "Image List To Image Batch"}}, "629": {"inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}, "class_type": "ImpactMakeImageList", "_meta": {"title": "Make Image List"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 635, "last_link_id": 1141, "nodes": [{"id": 177, "type": "Text String", "pos": [8034.98828125, -744.3985595703125], "size": [315, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250609/1/344766", "product_2295919", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 185, "type": "Text Concatenate", "pos": [9164.98046875, -594.3985595703125], "size": [315, 178], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "type": "String to Text", "pos": [7664.98828125, -534.3985595703125], "size": [315, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": [6898.775390625, -619.42529296875], "size": [315, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 216, "type": "ConrainImageSave", "pos": [9724.98046875, -874.3985595703125], "size": [320, 266], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 856, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "type": "Text Concatenate", "pos": [8514.9853515625, -774.3985595703125], "size": [250, 142], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [8024.98828125, -874.3985595703125], "size": [315, 58], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 240, "type": "Note", "pos": [8445.90234375, -1585.104736328125], "size": [260, 110], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 268, "type": "CLIPTextEncode", "pos": [1393.572998046875, -1348.140869140625], "size": [285.6000061035156, 54], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 513}, {"name": "text", "type": "STRING", "link": 453, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [464], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 269, "type": "VAEDecode", "pos": [2622.702880859375, -1422.1380615234375], "size": [210, 46], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 1111, 951], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 270, "type": "VAELoader", "pos": [2395.71435546875, -1196.9207763671875], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520, 1129], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 271, "type": "DualCLIPLoader", "pos": [-961.8040771484375, -912.8571166992188], "size": [315, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 664, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "type": "UNETLoader", "pos": [-949.96533203125, -1104.5126953125], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 647, 647, 944], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "fp8_e4m3fn"], "color": "#494949", "bgcolor": "#353535"}, {"id": 274, "type": "KSamplerSelect", "pos": [2010.49560546875, -1139.160888671875], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [2031.17724609375, -971.8941040039062], "size": [210, 106], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", "30", 1]}, {"id": 276, "type": "BasicGuider", "pos": [1994.201171875, -1380.5081787109375], "size": [161.1999969482422, 46], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 277, "type": "RandomNoise", "pos": [2059.2626953125, -1607.************], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [548786345280158, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 278, "type": "FluxGuidance", "pos": [1756.1866455078125, -1359.78466796875], "size": [211.60000610351562, 58], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 464}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1718.************, -1133.7596435546875], "size": [210, 86.50716400146484], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1701.7969970703125, -884.************], "size": [210, 122], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [789.1341552734375, -968.331787109375], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [981, 473, 1123], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), front view,whole body,9-year-old Chinese boy is wearing A light pink t-shirt made from soft cotton, featuring a bold silver and blue cartoon graphic with the text 'DALE HARCA' in a sharp, stylized font and a character wearing a blue cap printed across the chest, Paired with light blue denim shorts, knee-length with a faded wash, and multiple frayed ruffle details along both side seams.9-year-old Chinese boy,  is wearing { a dark bucket hat}. 9-year-old Chinese boy,  is wearing mgs2222 white sneakers with green accents. The  mgs2222 white sneakers with green accents feature solid color and simple. \n\n\nmgs2222,a forest setting with trees and a river in the background.standing casually.standing with one leg slightly forward and hands relaxed by the sides.\n\n\na mgm3004 golden alabaster skin chinese male model,9-year-old, {smile|laugh},  short straight dark brown hair with a natural side part,", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 283, "type": "ShowText|pysssss", "pos": [1400.793701171875, -1160.6016845703125], "size": [256.63372802734375, 226], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1123, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [453, 990], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), front view,whole body,9-year-old Chinese boy is wearing A light pink t-shirt made from soft cotton, featuring a bold silver and blue cartoon graphic with the text 'DALE HARCA' in a sharp, stylized font and a character wearing a blue cap printed across the chest, Paired with light blue denim shorts, knee-length with a faded wash, and multiple frayed ruffle details along both side seams.9-year-old Chinese boy,  is wearing  a dark bucket hat. 9-year-old Chinese boy,  is wearing mgs2222 white sneakers with green accents. The  mgs2222 white sneakers with green accents feature solid color and simple. \n\n\nmgs2222,a forest setting with trees and a river in the background.standing casually.standing with one leg slightly forward and hands relaxed by the sides.\n\n\na mgm3004 golden alabaster skin chinese male model,9-year-old, laugh,  short straight dark brown hair with a natural side part,"]}, {"id": 284, "type": "CR Seed", "pos": [1345.7989501953125, -503.8546447753906], "size": [243.4204864501953, 102], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 285, "type": "CR Seed", "pos": [1331.7989501953125, -689.8560791015625], "size": [243.4204864501953, 102], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1340, "fixed"]}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [798.8578491210938, -683.801025390625], "size": [411.6590881347656, 124], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 287, "type": "Reroute", "pos": [2535.691650390625, -838.6723022460938], "size": [75, 26], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 990, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2525.9609375, -664.3173217773438], "size": [75, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 320, "type": "ConrainTextSave", "pos": [9734.98046875, -524.3985595703125], "size": [315, 106], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2460.299072265625, -1021.8446655273438], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [575, 575, 1099], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 golden alabaster skin chinese male model,9-year-old, {smile|laugh},  short straight dark brown hair with a natural side part,", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1679.1009521484375, -613.5169067382812], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1702.960693359375, -512.515625], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [1911.972900390625, -635.5166625976562], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2161.5029296875, -639.5166625976562], "size": [210, 34], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [1982.985107421875, -505.5156555175781], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2215.380859375, -484.5157165527344], "size": [210, 34], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 355, "type": "ImageCrop", "pos": [9204.98046875, -904.3985595703125], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [856], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 365, "type": "Image Size to Number", "pos": [8905.8994140625, -1555.104736328125], "size": [229.20001220703125, 126], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1122, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [9205.8984375, -1475.104736328125], "size": [210, 67.1211166381836], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 373, "type": "JWStringToInteger", "pos": [-205.92677307128906, -1002.7108764648438], "size": [210, 58], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644, 644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-483.5779724121094, -705.3702392578125], "size": [315, 154], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 379, "type": "UNETLoader", "pos": [-951.9925537109375, -1307.653076171875], "size": [315, 82], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [662, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 385, "type": "Reroute", "pos": [-374.7147216796875, -1127.6103515625], "size": [75, 26], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 386, "type": "Reroute", "pos": [415.603759765625, -950.938232421875], "size": [75, 26], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665, 1113], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 446, "type": "CropFaces", "pos": [4748.59521484375, -1052.1627197265625], "size": [221.15121459960938, 146], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 760}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [762, 1097], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "type": "DetectFaces", "pos": [4506.21728515625, -1050.9080810546875], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1094}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [760], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 474, "type": "CR Image Input Switch", "pos": [3491.64697265625, -1152.820556640625], "size": [210, 74], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 813, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 978, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1126], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 477, "type": "CR Image Input Switch", "pos": [3484.615966796875, -1002.4156494140625], "size": [210, 74], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 815, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 979, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1127], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 537, "type": "Reroute", "pos": [-288.80596923828125, -1664.228271484375], "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 957}], "outputs": [{"name": "", "type": "IMAGE", "links": [959], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 547, "type": "JWStringToInteger", "pos": [3143.4921875, -1143.5277099609375], "size": [210, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [978], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 548, "type": "JWStringToInteger", "pos": [3122.81103515625, -989.7696533203125], "size": [213.92233276367188, 58], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [979], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 557, "type": "BoundedImageCropWithMask_v3_LR", "pos": [3830, -240], "size": [285.6000061035156, 149.66432189941406], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1007}, {"name": "mask", "type": "MASK", "link": 1008}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1017, 1019], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1011], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [1018], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [20, 20]}, {"id": 558, "type": "Reroute", "pos": [6640.31640625, -264.3552551269531], "size": [75, 26], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1009}], "outputs": [{"name": "", "type": "MASK", "links": [1043]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 559, "type": "Reroute", "pos": [6620.31640625, -344.3552551269531], "size": [75, 26], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1010}], "outputs": [{"name": "", "type": "IMAGE", "links": [1042]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 560, "type": "Reroute", "pos": [6640.31640625, -194.35525512695312], "size": [75, 26], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1011}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1044]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 561, "type": "RepeatLatentBatch", "pos": [6260.31640625, 145.64474487304688], "size": [210, 80.70018768310547], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1012}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1025], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [1]}, {"id": 563, "type": "MaskUpscale_LR", "pos": [4710, -130], "size": [248.52598571777344, 73.40138244628906], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1015, "label": "mask"}, {"name": "scale_by", "type": "FLOAT", "link": 1016, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [], "slot_index": 0, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "widgets_values": [4]}, {"id": 564, "type": "UpscaleSizeCalculator", "pos": [4180, 20], "size": [210, 136.1963348388672], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1017}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [1016, 1020], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [1024]}, {"id": 565, "type": "ConrainGrowMaskWithBlur", "pos": [4380, -270], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 106, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1018}], "outputs": [{"name": "mask", "type": "MASK", "links": [1009, 1015], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 566, "type": "Reroute", "pos": [3210, -310], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1111}], "outputs": [{"name": "", "type": "IMAGE", "links": [1007, 1010, 1047]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 567, "type": "ImageScaleBy", "pos": [4540, 120], "size": [250.58731079101562, 109.74829864501953], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1019, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 1020, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1041], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 568, "type": "Sapiens<PERSON><PERSON>der", "pos": [3110, -130], "size": [283.3903503417969, 298], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1046, 1117], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 569, "type": "SamplerCustomAdvanced", "pos": [6570.31640625, -94.35526275634766], "size": [236.8000030517578, 109.************], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1021, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1022, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1023, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1024, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1025}], "outputs": [{"name": "output", "type": "LATENT", "links": [1013], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 570, "type": "CLIPTextEncode", "pos": [5280.31640625, 185.64474487304688], "size": [296.32208251953125, 77.2895278930664], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1026}, {"name": "text", "type": "STRING", "link": 1104, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1031, 1032], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 571, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [5280.31640625, -4.355260848999023], "size": [324.723388671875, 134.23927307128906], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1130}, {"name": "clip", "type": "CLIP", "link": 1027}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1028], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [1029], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["child_face_lora_20250607.safetensors", 1, 1], "color": "#232", "bgcolor": "#353"}, {"id": 572, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [5280.31640625, -214.35525512695312], "size": [315, 126], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1028}, {"name": "clip", "type": "CLIP", "link": 1029}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1030], "slot_index": 0}, {"name": "CLIP", "type": "CLIP"}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/10-14岁男内童模-果麦_22841_20250530_194005/10-14岁男内童模-果麦_22841_20250530_194005-flux/10-14岁男内童模-果麦_22841_20250530_194005-flux.safetensors", 1, 1], "color": "#232", "bgcolor": "#353"}, {"id": 573, "type": "Reroute", "pos": [5610.31640625, -324.3552551269531], "size": [75, 26], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1030}], "outputs": [{"name": "", "type": "MODEL", "links": [1033, 1034]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 574, "type": "FluxGuidance", "pos": [5700.31640625, 165.64474487304688], "size": [211.60000610351562, 58], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1031, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1035], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [2]}, {"id": 575, "type": "ConditioningZeroOut", "pos": [5700.31640625, 25.644739151000977], "size": [222.26809692382812, 26], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1032}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1036], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 576, "type": "DifferentialDiffusion", "pos": [5700.31640625, -104.35526275634766], "size": [210, 26], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1033}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1039], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 577, "type": "BasicScheduler", "pos": [5990.31640625, 105.64473724365234], "size": [210, 106], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1034}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1024], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", 8, 0.15]}, {"id": 579, "type": "RandomNoise", "pos": [6250.31640625, -54.355255126953125], "size": [221.2050323486328, 83.35130310058594], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1021], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [0, "fixed"]}, {"id": 580, "type": "BasicGuider", "pos": [6270.31640625, -294.3552551269531], "size": [161.1999969482422, 46], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1039, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1040}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1022], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 581, "type": "KSamplerSelect", "pos": [6240.31640625, -174.35525512695312], "size": [210, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1023], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpm_2"]}, {"id": 582, "type": "Reroute", "pos": [4850, 50], "size": [75, 26], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1041}], "outputs": [{"name": "", "type": "IMAGE", "links": [1112], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 583, "type": "Reroute", "pos": [4890, 263.97613525390625], "size": [75, 26], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1115}], "outputs": [{"name": "", "type": "CLIP", "links": [1026, 1027]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 585, "type": "SapiensSampler", "pos": [3500, -130], "size": [247.4663848876953, 259.6097412109375], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1046}, {"name": "image", "type": "IMAGE", "link": 1047}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [1008], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 586, "type": "Reroute", "pos": [5330.31640625, -314.3552551269531], "size": [75, 26], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1129}], "outputs": [{"name": "", "type": "VAE", "links": [1014, 1037]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 587, "type": "Reroute", "pos": [5903.5830078125, -1092.927978515625], "size": [75, 26], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1112}], "outputs": [{"name": "", "type": "IMAGE", "links": [1048, 1053]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 588, "type": "CheckpointLoaderSimple", "pos": [5248.390625, -1644.19873046875], "size": [314.34735107421875, 128.43458557128906], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1078], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [1052, 1073], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [1059, 1066], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 589, "type": "DetectFaces", "pos": [5231.2734375, -1471.755615234375], "size": [210, 126], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1048}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [1049], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 590, "type": "OrderedFaceFilter", "pos": [5244.98828125, -1255.52685546875], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1049}], "outputs": [{"name": "filtered", "type": "FACE", "links": [1050, 1054], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 591, "type": "CropFaces", "pos": [5544.72607421875, -1191.357421875], "size": [221.15121459960938, 146], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1050}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [1051, 1069, 1072, 1074], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [1057], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 592, "type": "SapiensSampler", "pos": [5238.166015625, -1037.31396484375], "size": [265.8616027832031, 258], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1118}, {"name": "image", "type": "IMAGE", "link": 1051}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "23,24,25,26,27", false, 255, 255, 255]}, {"id": 593, "type": "ControlNetLoader", "pos": [5883.46044921875, -1502.615234375], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1077], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 594, "type": "CLIPTextEncode", "pos": [5541.3818359375, -1434.293701171875], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": false}, "order": 45, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1052}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1080], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}, {"id": 595, "type": "InstantIDModelLoader", "pos": [5847.34619140625, -1611.684326171875], "size": [315, 58], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [1075], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 596, "type": "InstantIDFaceAnalysis", "pos": [5859.9208984375, -1551.356689453125], "size": [315, 58], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [1076], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 600, "type": "InpaintModelConditioning", "pos": [6536.25048828125, -1435.2982177734375], "size": [210, 138], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1064, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 1065}, {"name": "vae", "type": "VAE", "link": 1066}, {"name": "pixels", "type": "IMAGE", "link": 1067}, {"name": "mask", "type": "MASK", "link": 1068}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1061], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [1062], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1063], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 603, "type": "Reroute", "pos": [6134.86328125, -1466.5614013671875], "size": [75, 26], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1072}], "outputs": [{"name": "", "type": "IMAGE", "links": [1067, 1081]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 604, "type": "CLIPTextEncode", "pos": [5641.8115234375, -1612.7529296875], "size": [210, 96], "flags": {"collapsed": false}, "order": 72, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1073}, {"name": "text", "type": "STRING", "link": 1103, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1079], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 606, "type": "ApplyInstantID", "pos": [6285.20849609375, -1638.934814453125], "size": [210, 266], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 1075, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 1076, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 1077, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 1098, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1078, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 1079, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1080, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 1081, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1060], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [1064], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [1065], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 608, "type": "ImageResize+", "pos": [4136.724609375, -1192.1729736328125], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1128}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1093, 1095], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 609, "type": "UltralyticsDetectorProvider", "pos": [3768.260986328125, -982.2147827148438], "size": [315, 78], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1087], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 610, "type": "ImpactSimpleDetectorSEGS", "pos": [4138.52880859375, -1613.1448974609375], "size": [277.6************, 319.97015380859375], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1087}, {"name": "image", "type": "IMAGE", "link": 1095}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1092], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 612, "type": "SegsToCombinedMask", "pos": [4633.7421875, -1696.**********], "size": [289.79998779296875, 26], "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1088}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1089], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 613, "type": "MaskFastGrow", "pos": [4735.19970703125, -1603.572265625], "size": [210, 178], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1089}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1090], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 614, "type": "Bounded Image Crop with Mask", "pos": [4466.734375, -1308.2440185546875], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1093}, {"name": "mask", "type": "MASK", "link": 1090}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1091], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 615, "type": "easy imageListToImageBatch", "pos": [4746.7099609375, -1312.882080078125], "size": [222.84095764160156, 26], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1091}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1094], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 616, "type": "Reroute", "pos": [5087.3310546875, -1635.439453125], "size": [75, 26], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1097}], "outputs": [{"name": "", "type": "IMAGE", "links": [1098], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 617, "type": "Reroute", "pos": [2989.97412109375, -844.7568359375], "size": [75, 26], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1099, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1100], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 618, "type": "Reroute", "pos": [4939.9091796875, -852.3460693359375], "size": [75, 26], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1100, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1102, 1104], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 619, "type": "Reroute", "pos": [5110.6943359375, -1569.9432373046875], "size": [75, 26], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1102, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1103], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 624, "type": "Reroute", "pos": [797.66748046875, 272.5708312988281], "size": [75, 26], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1113}], "outputs": [{"name": "", "type": "CLIP", "links": [1114], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 625, "type": "Reroute", "pos": [2962.908935546875, 261.55731201171875], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1114}], "outputs": [{"name": "", "type": "CLIP", "links": [1115], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 626, "type": "Reroute", "pos": [4947.50048828125, -748.3097534179688], "size": [75, 26], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1117}], "outputs": [{"name": "", "type": "MODEL_SAPIEN", "links": [1118], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 627, "type": "Reroute", "pos": [7310.9521484375, -1583.37744140625], "size": [75, 26], "flags": {}, "order": 132, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1120}], "outputs": [{"name": "", "type": "IMAGE", "links": [1121, 1122], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 629, "type": "ImpactMakeImageList", "pos": [3809.640625, -1187.49951171875], "size": [231.68582153320312, 104.573486328125], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1125}, {"name": "image2", "type": "IMAGE", "link": 1126}, {"name": "image3", "type": "IMAGE", "link": 1127}, {"name": "image4", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1128], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "widgets_values": []}, {"id": 611, "type": "ImpactSEGSOrderedFilter", "pos": [4463.92041015625, -1600.606201171875], "size": [210, 158.96408081054688], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1092}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1088], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 607, "type": "MaskFastGrow", "pos": [6203.44091796875, -1267.2886962890625], "size": [210, 178], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1082}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1068, 1071, 1136], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 5, 4, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 598, "type": "VAEDecode", "pos": [6902.4267578125, -1117.7196044921875], "size": [140, 46], "flags": {"collapsed": false}, "order": 123, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1058}, {"name": "vae", "type": "VAE", "link": 1059}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1135], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 562, "type": "VAEDecode", "pos": [6560.31640625, 145.64474487304688], "size": [210, 46], "flags": {"collapsed": false}, "order": 128, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1013}, {"name": "vae", "type": "VAE", "link": 1014}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1137], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 597, "type": "WarpFacesBack", "pos": [7109.482421875, 27.729785919189453], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1053}, {"name": "face", "type": "FACE", "link": 1054}, {"name": "crop", "type": "IMAGE", "link": 1055}, {"name": "mask", "type": "MASK", "link": 1056}, {"name": "warp", "type": "WARP", "link": 1057}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1138], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [726.767822265625, -1590.7061767578125], "size": [477.3377990722656, 128.31455993652344], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 946}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/6-9男童模-凯凯_24870_20250607_202314/6-9男童模-凯凯_24870_20250607_202314-flux/6-9男童模-凯凯_24870_20250607_202314-flux.safetensors", "0.6", "0.6"], "color": "#232", "bgcolor": "#353"}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [724.9411010742188, -1389.706298828125], "size": [491.7470703125, 126], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/芳羽绒服1_19701_20250521_212806/芳羽绒服1_19701_20250521_212806-flux/芳羽绒服1_19701_20250521_212806-flux.safetensors", "0.8", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [740.1643676757812, -1196.1575927734375], "size": [499.25970458984375, 126], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 654, 947], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [513, 510, 513, 630, 631], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/男童014_copy_24065_20250605_194722/男童014_copy_24065_20250605_194722-flux/男童014_copy_24065_20250605_194722-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 605, "type": "easy humanSegmentation", "pos": [5839.80078125, -1194.588134765625], "size": [300, 500], "flags": {"collapsed": false}, "order": 117, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1074}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [1082], "slot_index": 1}, {"name": "bbox", "type": "BBOX", "slot_index": 2}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 462, "type": "LoadImage", "pos": [3189.112548828125, -1583.8983154296875], "size": [235.8109893798828, 314], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [899, 957, 1125, 957], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["1790e0557eb0431798bfb123e6c141fd.png", "image"]}, {"id": 475, "type": "LoadImage", "pos": [3491.127197265625, -1589.1981201171875], "size": [249.60922241210938, 314], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [813], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["MqD3_face.jpg", "image"]}, {"id": 476, "type": "LoadImage", "pos": [3806.9091796875, -1597.4578857421875], "size": [234.48504638671875, 314], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [815], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["prod_fix_a82119462f1b4b82b2e61a40a4b6ebd7.jpg", "image"]}, {"id": 599, "type": "K<PERSON><PERSON><PERSON>", "pos": [6809.79248046875, -1451.95947265625], "size": [261.8017578125, 262], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1060}, {"name": "positive", "type": "CONDITIONING", "link": 1061}, {"name": "negative", "type": "CONDITIONING", "link": 1062}, {"name": "latent_image", "type": "LATENT", "link": 1063}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1058], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42029499340256, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.5]}, {"id": 584, "type": "Bounded Image Blend with Mask", "pos": [7059.74609375, -324.9255065917969], "size": [243.60000610351562, 172.84213256835938], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1042}, {"name": "target_mask", "type": "MASK", "link": 1043}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1044}, {"name": "source", "type": "IMAGE", "link": 1138}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1120], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 601, "type": "easy imageColorMatch", "pos": [6831.0185546875, 78.27633666992188], "size": [210, 102], "flags": {"collapsed": false}, "order": 129, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 1069}, {"name": "image_target", "type": "IMAGE", "link": 1137}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1055], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2432.************, -1611.155517578125], "size": [292.29705810546875, 127.71533966064453], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 578, "type": "InpaintModelConditioning", "pos": [5976.31982421875, -298.1543273925781], "size": [216.59999084472656, 138], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1035}, {"name": "negative", "type": "CONDITIONING", "link": 1036, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 1037}, {"name": "pixels", "type": "IMAGE", "link": 1135}, {"name": "mask", "type": "MASK", "link": 1136, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1040], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1012], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 602, "type": "ConrainGrowMaskWithBlur", "pos": [6449.83984375, -1199.398681640625], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 121, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1071}], "outputs": [{"name": "mask", "type": "MASK", "links": [1056], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [40, 0, false, false, 0, 1, 1, false]}, {"id": 377, "type": "CR Model Input Switch", "pos": [83.21484375, -725.4107666015625], "size": [257.191650390625, 78.78076171875], "flags": {"collapsed": false}, "order": 52, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 944, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [946, 1130], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["1"]}, {"id": 525, "type": "easy humanSegmentation", "pos": [-841.5623779296875, -1652.26416015625], "size": [300, 260], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 899}], "outputs": [{"name": "image", "type": "IMAGE"}, {"name": "mask", "type": "MASK", "links": [960], "slot_index": 1}, {"name": "bbox", "type": "BBOX"}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 512, "type": "PulidFluxModelLoader", "pos": [-466.6873779296875, -1493.7525634765625], "size": [315, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "PULIDFLUX", "type": "PULIDFLUX", "links": [879]}], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "type": "PulidFluxEvaClipLoader", "pos": [-468.8070068359375, -1372.1922607421875], "size": [327.5999755859375, 26], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "EVA_CLIP", "type": "EVA_CLIP", "links": [882]}], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "widgets_values": []}, {"id": 515, "type": "PulidFluxInsightFaceLoader", "pos": [-479.9522705078125, -1278.173095703125], "size": [365.4000244140625, 58], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [883]}], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "widgets_values": ["CUDA"]}, {"id": 508, "type": "ApplyPulidFlux", "pos": [183.51483154296875, -1463.5994873046875], "size": [315, 226], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 947}, {"name": "pulid_flux", "type": "PULIDFLUX", "link": 879}, {"name": "eva_clip", "type": "EVA_CLIP", "link": 882}, {"name": "face_analysis", "type": "FACEANALYSIS", "link": 883}, {"name": "image", "type": "IMAGE", "link": 966}, {"name": "attn_mask", "type": "MASK", "shape": 7}, {"name": "options", "type": "OPTIONS", "shape": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [948], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "widgets_values": [0.8, 0, 1]}, {"id": 536, "type": "Bounded Image Crop with Mask", "pos": [-54.86212158203125, -1627.6336669921875], "size": [236.27499389648438, 150], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 959}, {"name": "mask", "type": "MASK", "link": 960}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [966], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [96, 96, 96, 96]}, {"id": 378, "type": "ModelMergeFlux1", "pos": [209.58755493164062, -1070.8203125], "size": [315, 1566], "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 948}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 550, "type": "CR Text", "pos": [777.7445678710938, -1800.5491943359375], "size": [415.5107116699219, 126.7047348022461], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [980], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"]}, {"id": 549, "type": "Text Concatenate", "pos": [1418.9630126953125, -1600.443359375], "size": [213.69569396972656, 179.2432861328125], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 980, "widget": {"name": "text_a"}, "shape": 7}, {"name": "text_b", "type": "STRING", "link": 981, "widget": {"name": "text_b"}, "shape": 7}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "shape": 7}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "shape": 7}], "outputs": [{"name": "STRING", "type": "STRING", "links": [982], "slot_index": 0}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [": ", "true", "", "", "", ""]}, {"id": 552, "type": "LLModel", "pos": [1760.8935546875, -1600.6900634765625], "size": [254.6305389404297, 154.52664184570312], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "image_list", "type": "IMAGE", "shape": 7}, {"name": "ref_image", "type": "IMAGE", "shape": 7}, {"name": "prompt", "type": "STRING", "link": 982, "widget": {"name": "prompt"}}], "outputs": [{"name": "result_text", "type": "STRING", "links": [983], "slot_index": 0}, {"name": "result_detail", "type": "STRING"}], "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["你能干嘛", "default"]}, {"id": 553, "type": "Reroute", "pos": [1281.9537353515625, -1206.7755126953125], "size": [75, 26], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 983}], "outputs": [{"name": "", "type": "STRING", "links": [], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 236, "type": "CR Upscale Image", "pos": [7538.00634765625, -1321.619873046875], "size": [315, 222], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1121, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 261, "type": "Image Size to Number", "pos": [8025.9052734375, -1495.104736328125], "size": [229.20001220703125, 126], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 235, "type": "InspyrenetRembg", "pos": [8015.9052734375, -1265.104736328125], "size": [230, 90], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 248, "type": "EmptyImage", "pos": [8475.9033203125, -1405.104736328125], "size": [231.5089111328125, 120.12616729736328], "flags": {"collapsed": false}, "order": 139, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [8465.9033203125, -1185.104736328125], "size": [252, 26], "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [8895.8994140625, -1315.104736328125], "size": [252, 146], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [9475.8984375, -1395.104736328125], "size": [220, 118], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 263, "type": "ImageScaleBy", "pos": [9785.8984375, -1515.104736328125], "size": [228.9691162109375, 78], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [760, 447, 0, 446, 0, "FACE"], [813, 475, 0, 474, 0, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [978, 547, 0, 474, 2, "INT"], [979, 548, 0, 477, 2, "INT"], [980, 550, 0, 549, 0, "STRING"], [981, 282, 0, 549, 1, "STRING"], [982, 549, 0, 552, 2, "STRING"], [983, 552, 0, 553, 0, "*"], [990, 283, 0, 287, 0, "*"], [1007, 566, 0, 557, 0, "IMAGE"], [1008, 585, 4, 557, 1, "MASK"], [1009, 565, 0, 558, 0, "*"], [1010, 566, 0, 559, 0, "*"], [1011, 557, 1, 560, 0, "*"], [1012, 578, 2, 561, 0, "LATENT"], [1013, 569, 0, 562, 0, "LATENT"], [1014, 586, 0, 562, 1, "VAE"], [1015, 565, 0, 563, 0, "MASK"], [1016, 564, 0, 563, 1, "FLOAT"], [1017, 557, 0, 564, 0, "IMAGE"], [1018, 557, 2, 565, 0, "MASK"], [1019, 557, 0, 567, 0, "IMAGE"], [1020, 564, 0, 567, 1, "FLOAT"], [1021, 579, 0, 569, 0, "NOISE"], [1022, 580, 0, 569, 1, "GUIDER"], [1023, 581, 0, 569, 2, "SAMPLER"], [1024, 577, 0, 569, 3, "SIGMAS"], [1025, 561, 0, 569, 4, "LATENT"], [1026, 583, 0, 570, 0, "CLIP"], [1027, 583, 0, 571, 1, "CLIP"], [1028, 571, 0, 572, 0, "MODEL"], [1029, 571, 1, 572, 1, "CLIP"], [1030, 572, 0, 573, 0, "*"], [1031, 570, 0, 574, 0, "CONDITIONING"], [1032, 570, 0, 575, 0, "CONDITIONING"], [1033, 573, 0, 576, 0, "MODEL"], [1034, 573, 0, 577, 0, "MODEL"], [1035, 574, 0, 578, 0, "CONDITIONING"], [1036, 575, 0, 578, 1, "CONDITIONING"], [1037, 586, 0, 578, 2, "VAE"], [1039, 576, 0, 580, 0, "MODEL"], [1040, 578, 0, 580, 1, "CONDITIONING"], [1041, 567, 0, 582, 0, "*"], [1042, 559, 0, 584, 0, "IMAGE"], [1043, 558, 0, 584, 1, "MASK"], [1044, 560, 0, 584, 2, "IMAGE_BOUNDS"], [1046, 568, 0, 585, 0, "MODEL_SAPIEN"], [1047, 566, 0, 585, 1, "IMAGE"], [1048, 587, 0, 589, 0, "IMAGE"], [1049, 589, 0, 590, 0, "FACE"], [1050, 590, 0, 591, 0, "FACE"], [1051, 591, 0, 592, 1, "IMAGE"], [1052, 588, 1, 594, 0, "CLIP"], [1053, 587, 0, 597, 0, "IMAGE"], [1054, 590, 0, 597, 1, "FACE"], [1055, 601, 0, 597, 2, "IMAGE"], [1056, 602, 0, 597, 3, "MASK"], [1057, 591, 2, 597, 4, "WARP"], [1058, 599, 0, 598, 0, "LATENT"], [1059, 588, 2, 598, 1, "VAE"], [1060, 606, 0, 599, 0, "MODEL"], [1061, 600, 0, 599, 1, "CONDITIONING"], [1062, 600, 1, 599, 2, "CONDITIONING"], [1063, 600, 2, 599, 3, "LATENT"], [1064, 606, 1, 600, 0, "CONDITIONING"], [1065, 606, 2, 600, 1, "CONDITIONING"], [1066, 588, 2, 600, 2, "VAE"], [1067, 603, 0, 600, 3, "IMAGE"], [1068, 607, 0, 600, 4, "MASK"], [1069, 591, 0, 601, 0, "IMAGE"], [1071, 607, 0, 602, 0, "MASK"], [1072, 591, 0, 603, 0, "*"], [1073, 588, 1, 604, 0, "CLIP"], [1074, 591, 0, 605, 0, "IMAGE"], [1075, 595, 0, 606, 0, "INSTANTID"], [1076, 596, 0, 606, 1, "FACEANALYSIS"], [1077, 593, 0, 606, 2, "CONTROL_NET"], [1078, 588, 0, 606, 4, "MODEL"], [1079, 604, 0, 606, 5, "CONDITIONING"], [1080, 594, 0, 606, 6, "CONDITIONING"], [1081, 603, 0, 606, 7, "IMAGE"], [1082, 605, 1, 607, 0, "MASK"], [1087, 609, 0, 610, 0, "BBOX_DETECTOR"], [1088, 611, 0, 612, 0, "SEGS"], [1089, 612, 0, 613, 0, "MASK"], [1090, 613, 0, 614, 1, "MASK"], [1091, 614, 0, 615, 0, "IMAGE"], [1092, 610, 0, 611, 0, "SEGS"], [1093, 608, 0, 614, 0, "IMAGE"], [1094, 615, 0, 447, 0, "IMAGE"], [1095, 608, 0, 610, 1, "IMAGE"], [1097, 446, 0, 616, 0, "*"], [1098, 616, 0, 606, 3, "IMAGE"], [1099, 331, 0, 617, 0, "*"], [1100, 617, 0, 618, 0, "*"], [1102, 618, 0, 619, 0, "*"], [1103, 619, 0, 604, 1, "STRING"], [1104, 618, 0, 570, 1, "STRING"], [1111, 269, 0, 566, 0, "*"], [1112, 582, 0, 587, 0, "*"], [1113, 386, 0, 624, 0, "*"], [1114, 624, 0, 625, 0, "*"], [1115, 625, 0, 583, 0, "*"], [1117, 568, 0, 626, 0, "*"], [1118, 626, 0, 592, 0, "MODEL_SAPIEN"], [1120, 584, 0, 627, 0, "*"], [1121, 627, 0, 236, 0, "IMAGE"], [1122, 627, 0, 365, 0, "IMAGE"], [1123, 282, 0, 283, 0, "STRING"], [1125, 462, 0, 629, 0, "IMAGE"], [1126, 474, 0, 629, 1, "IMAGE"], [1127, 477, 0, 629, 2, "IMAGE"], [1128, 629, 0, 608, 0, "IMAGE"], [1129, 270, 0, 586, 0, "*"], [1130, 377, 0, 571, 0, "MODEL"], [1135, 598, 0, 578, 3, "IMAGE"], [1136, 607, 0, 578, 4, "MASK"], [1137, 562, 0, 601, 1, "IMAGE"], [1138, 597, 0, 584, 3, "IMAGE"]], "groups": [{"id": 3, "title": "换背景", "bounding": [7445.9052734375, -1685.104736328125, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [641.1348876953125, -1689.5174560546875, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [7444.98828125, -1004.390625, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "人脸原图", "bounding": [3084.989501953125, -1694.2711181640625, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "Group", "bounding": [3090, -410, 1893.4822998046875, 743.8132934570312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 13, "title": "Group", "bounding": [5220.31640625, -394.3552551269531, 2118.918701171875, 688.3203735351562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "换脸", "bounding": [5214.2060546875, -1708.161865234375, 1880.18798828125, 1196.9237060546875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.15772225476662663, "offset": [3362.6934963332183, 2771.7170066656527]}}, "version": 0.4, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "579": 0, "599": 0}}}}}
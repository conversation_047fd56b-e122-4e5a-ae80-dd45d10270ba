{"client_id": "${clientId}", "prompt": {"7": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "8": {"inputs": {"prompt": ["105", 0], "background": "white", "threshold": 0.2, "sam_model": ["7", 0], "grounding_dino_model": ["71", 0], "image": ["73", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "71": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "73": {"inputs": {"image": "${clotheImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "83": {"inputs": {"prompt": ["100", 0], "llm_model": "default", "image_list": ["108", 0]}, "class_type": "LLModel", "_meta": {"title": "抠图-衣服描述"}}, "86": {"inputs": {"text": "Return the clothing tags of input image based on the Input garment part provided. Tag options:dress, jacket, trousers, hoodie, T-shirt, shirt, sweater, bra, shorts, skirt, vest, leggings, coat, panties.\n\nOnly output the tags separated with \" . \".\n\nInput: upper garment\ncoat\n\nInput: upper garment\nshirt . jacket\n\nInput: lower garment\nskirt . leggings\n\nInput: outfit\ndress\n\nInput: upper garment\nsweater . scarf\n\nInput: outfit\nvest . T-shirt . trousers\n\nInput: target_cloth"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "87": {"inputs": {"text": "${clotheType}"}, "class_type": "CR Text", "_meta": {"title": "服装类型（上下装等）"}}, "100": {"inputs": {"source": ["86", 0], "to_replace": "target_cloth", "replace_with": ["87", 0]}, "class_type": "JWStringReplace", "_meta": {"title": "String Replace"}}, "105": {"inputs": {"call_code": "# 抠图词处理\ndef call(any_a, any_b, any_c, any_d):\n\tif any_a is None or len(any_a) == 0:\n\t\treturn [' ']\n\n\ttype = any_b.strip()\n\n\t# 处理大模型报错, 或者超过长度\n\tif 'Failed to make the request' in any_a or len(any_a) > 30:\n\t\tif type == 'upper garment':\n\t\t\treturn ['T-shirt . shirt . sweater . bra . jacket']\n\t\telif type == 'lower garment':\n\t\t\treturn ['trousers . shorts . skirt . panties']\n\t\telse:\n\t\t\treturn ['clothing . t-shirt . sweater . trousers']\n\n\ttext = any_a.strip()\n\tif type and 'outfit' in type and 'clothing' not in text:\n\t\ttext += ' . clothing'\n\treturn [text]", "any_a": ["83", 0], "any_b": ["87", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "gpt返回结果处理"}}, "108": {"inputs": {"width": 1024, "height": 1024, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 1, "image": ["73", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "112": {"inputs": {"text": "You are an expert garment descriptor. Analyze the image and generate a structured, detailed description based on the Input garment part provided following these rules:\n\n1. **Required Features**\nDescribe only features present in the garment(s), including:\n- Type, color, collar style, sleeve length, garment length (use precise terms: bust-length, waist-length, belly button-length, hips-length, upper thigh-length, mid-thigh-length, knee-length, calf-length, ankle-length, floor-length)\n- Pockets, buttons, graphics, lace, gradient color (specify color transitions), pleats, cutouts, belt, semi-transparency, text (describe in parentheses, e.g., has text on the chest (delighted)), and any other distinct details\n- For dresses, specify if there is a slit\n\n2. **Description Structure**\n- First Sentence:\n  - For single garments: \"[Color] [garment type].\"(Example: \"White shirt.\")\n  - For two-piece outfits: For two-piece outfits: \"[Color] [upper garment] and [color] [lower garment].\"(Example: \"Blue T-shirt and black jeans.\")\n\n- Subsequent Sentences: \n  - Begin with the garment type (e.g., \"The dress...\") or a pronoun (\"It\"/\"They\") after the first mention.\n  - Combine 2–3 related features per sentence using \"and\" or \"with.\"\n  - Only mention features that are present.\n\n3. **Special Instructions**\n- For text, include the content in parentheses.\n- For gradients, specify the direction and colors (e.g., \"gradient from light blue to dark blue\").\n- For two-piece outfits, describe upper and lower garments separately.\n\n4. **Prohibited**\n- Do **not** use commas, the word \"top\" or generic/ambiguous phrasing.\n\nExample:\nInput: upper garment\nDark blue dress. It has a V-neck collar and long sleeves. The dress is ankle length with a fitted waist and drawstring neckline.\n\nInput: outfit\nBlack velvet dress. It has a cowl neckline and mid-thigh length with semi-transparent lace sleeves. Features a fitted bodice and flared skirt with a slit.\n\nInput: upper garment\nLight pink puffer jacket. It has a high collar and knee-length cut with long sleeves. Features a zipper front and two side pockets. Includes text on the chest (delighted) and a sleeve patch.\n\nInput: upper garment\nLight beige puffer jacket. It has a hood and mid-thigh length with long sleeves. Features a zipper front and two side pockets. Includes a sleeve patch and solid light beige fabric.\n\nInput: outfit\nBlack shirt and light blue jeans. The shirt has a turtleneck collar and waist-length cut with sleeveless design. The jeans are ankle length and feature a button closure with front and back pockets. They have a solid light blue color.\n\nInput: outfit\nLight blue jacket and pants. The jacket has a round collar and waist-length cut with button-down front. Features two front pockets and lace trim. The pants are ankle length with a straight cut and solid light blue fabric.\n\nInput: outfit\nLight blue bathrobe. It has a V-neck collar and calf-length cut with long sleeves. Features a gradient from light to dark blue and lace trim on collar and pockets. Includes a button-down front two pockets and ruffled hem.\n\nInput: upper garment\nBlack and white shirt. It has a round collar and waist-length cut with long sleeves. Features a gradient from white shoulders to black hem. Made of solid fabric.\n\nInput: upper garment\nGray sweater and cardigan. The sweater has a V-neck collar and waist-length cut with long sleeves. The cardigan is hips length with a button-down front and striped left sleeve (red and white). Both are made of solid gray fabric.\n\nInput: outfit\nLight pink and black romper. It has a halter collar and upper thigh length with sleeveless design. Features a fitted waist and cow print pattern. Includes lace trim at the hem.\n\nInput: target_cloth"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "114": {"inputs": {"source": ["112", 0], "to_replace": "target_cloth", "replace_with": ["87", 0]}, "class_type": "JWStringReplace", "_meta": {"title": "String Replace"}}, "116": {"inputs": {"prompt": ["114", 0], "llm_model": "default", "image_list": ["108", 0]}, "class_type": "LLModel", "_meta": {"title": "抠图-衣服描述"}}, "117": {"inputs": {"call_code": "# 抠图词处理\ndef call(any_a, any_b, any_c, any_d):\n\tif any_a is None or len(any_a) == 0:\n\t\treturn [' ']\n\n\t# 处理大模型报错, 或者超过长度\n\tif 'Failed to make the request' in any_a:\n\t\treturn ['a garment']\n\n\treturn [any_a]", "any_a": ["116", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "gpt返回结果处理"}}, "119": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "121": {"inputs": {"text": ["117", 0], "path": ["122", 0], "filename": ["119", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "122": {"inputs": {"a": "output/", "b": ["119", 0]}, "class_type": "JWStringConcat", "_meta": {"title": "String Concatenate"}}, "126": {"inputs": {"dir": ["122", 0], "filename": ["128", 0], "mask": ["8", 1]}, "class_type": "Conrain_SaveMask", "_meta": {"title": "Conrain <PERSON>"}}, "128": {"inputs": {"a": ["119", 1], "b": ".png"}, "class_type": "JWStringConcat", "_meta": {"title": "String Concatenate"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 128, "last_link_id": 202, "nodes": [{"id": 7, "type": "Conrain_SAMModelLoader", "pos": [759.2940673828125, 183.6226043701172], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [13], "slot_index": 0, "label": "SAM_MODEL", "shape": 3}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 105, "type": "ConrainPythonExecutor", "pos": [1639, 694], "size": [487.4883117675781, 285.990478515625], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 149, "label": "any_a", "shape": 7}, {"name": "any_b", "type": "*", "link": 150, "label": "any_b", "shape": 7}, {"name": "any_c", "type": "*", "link": null, "label": "any_c", "shape": 7}, {"name": "any_d", "type": "*", "link": null, "label": "any_d", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [167], "slot_index": 0, "label": "any", "shape": 3}], "title": "gpt返回结果处理", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 抠图词处理\ndef call(any_a, any_b, any_c, any_d):\n\tif any_a is None or len(any_a) == 0:\n\t\treturn [' ']\n\n\ttype = any_b.strip()\n\n\t# 处理大模型报错, 或者超过长度\n\tif 'Failed to make the request' in any_a or len(any_a) > 30:\n\t\tif type == 'upper garment':\n\t\t\treturn ['T-shirt . shirt . sweater . bra . jacket']\n\t\telif type == 'lower garment':\n\t\t\treturn ['trousers . shorts . skirt . panties']\n\t\telse:\n\t\t\treturn ['clothing . t-shirt . sweater . trousers']\n\n\ttext = any_a.strip()\n\tif type and 'outfit' in type and 'clothing' not in text:\n\t\ttext += ' . clothing'\n\treturn [text]"]}, {"id": 100, "type": "JWStringReplace", "pos": [1010, 760], "size": [268.27655029296875, 88.76385498046875], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "source", "type": "STRING", "link": 146, "label": "source", "widget": {"name": "source"}}, {"name": "replace_with", "type": "STRING", "link": 147, "label": "replace_with", "widget": {"name": "replace_with"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [163], "slot_index": 0, "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "JWStringReplace"}, "widgets_values": ["", "target_cloth", "_cloth_mannequin"]}, {"id": 114, "type": "JWStringReplace", "pos": [983.1398315429688, 1139.2947998046875], "size": [268.27655029296875, 88.76385498046875], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "source", "type": "STRING", "link": 168, "label": "source", "widget": {"name": "source"}}, {"name": "replace_with", "type": "STRING", "link": 169, "label": "replace_with", "widget": {"name": "replace_with"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [174], "slot_index": 0, "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "JWStringReplace"}, "widgets_values": ["", "target_cloth", "_cloth_mannequin"]}, {"id": 108, "type": "ImageResize+", "pos": [239, 713], "size": [315, 218], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 157}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [159, 173], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1024, 1024, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 1]}, {"id": 117, "type": "ConrainPythonExecutor", "pos": [1607, 1115], "size": [500.6365661621094, 266.1195373535156], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 171, "label": "any_a", "shape": 7}, {"name": "any_b", "type": "*", "link": null, "label": "any_b", "shape": 7}, {"name": "any_c", "type": "*", "link": null, "label": "any_c", "shape": 7}, {"name": "any_d", "type": "*", "link": null, "label": "any_d", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [184], "slot_index": 0, "label": "any", "shape": 3}], "title": "gpt返回结果处理", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 抠图词处理\ndef call(any_a, any_b, any_c, any_d):\n\tif any_a is None or len(any_a) == 0:\n\t\treturn [' ']\n\n\t# 处理大模型报错, 或者超过长度\n\tif 'Failed to make the request' in any_a:\n\t\treturn ['a garment']\n\n\treturn [any_a]"]}, {"id": 71, "type": "Conrain_GroundingDinoModelLoader", "pos": [751.272705078125, 307.2388916015625], "size": [306.526123046875, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [104], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 4, "type": "Reroute", "pos": [661, 183], "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 115, "label": ""}], "outputs": [{"name": "", "type": "IMAGE", "links": [11, 157], "slot_index": 0, "label": ""}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 113, "type": "Note", "pos": [262, 1157], "size": [210, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["${clothDetailsPrompt}"], "color": "#432", "bgcolor": "#653"}, {"id": 109, "type": "Note", "pos": [271, 916], "size": [210, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["${clothType}"], "color": "#432", "bgcolor": "#653"}, {"id": 87, "type": "CR Text", "pos": [519, 895], "size": [310, 96], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [147, 150, 169], "slot_index": 0, "label": "text", "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "label": "show_help", "shape": 3}], "title": "服装类型（上下装等）", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${clotheType}"], "color": "#232", "bgcolor": "#353"}, {"id": 86, "type": "CR Text", "pos": [524, 645], "size": [391.66986083984375, 188.420166015625], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [146], "slot_index": 0, "label": "text", "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "label": "show_help", "shape": 3}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["Return the clothing tags of input image based on the Input garment part provided. Tag options:dress, jacket, trousers, hoodie, T-shirt, shirt, sweater, bra, shorts, skirt, vest, leggings, coat, panties, scarf.\n\nOnly output the tags separated with \" . \".\n\nInput: upper garment\ncoat\n\nInput: upper garment\nshirt . jacket\n\nInput: lower garment\nskirt . leggings\n\nInput: outfit\ndress\n\nInput: upper garment\nsweater . scarf\n\nInput: outfit\nvest . T-shirt . trousers\n\nInput: target_cloth"]}, {"id": 119, "type": "Text String", "pos": [1550, 186], "size": [308.0752258300781, 190], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [190], "slot_index": 0, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "links": [186, 201], "slot_index": 1, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 128, "type": "JWStringConcat", "pos": [1910, 224], "size": [210, 58], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [{"name": "a", "type": "STRING", "link": 201, "widget": {"name": "a"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [202], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["", ".png"]}, {"id": 122, "type": "JWStringConcat", "pos": [1923, 455], "size": [227.71702575683594, 58], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [{"name": "b", "type": "STRING", "link": 190, "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [188, 196], "slot_index": 0, "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 73, "type": "LoadImage", "pos": [241, 182], "size": [373.8504638671875, 329.5403747558594], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [115], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${clotheImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 8, "type": "Conrain_GroundingDinoSAMSegment", "pos": [1162, 185], "size": [310, 150], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 13, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 104, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 11, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 167, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "label": "IMAGE", "shape": 3}, {"name": "MASK", "type": "MASK", "links": [195], "slot_index": 1, "label": "MASK", "shape": 3}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["t-shirt . strip", "white", 0.2]}, {"id": 126, "type": "Conrain_SaveMask", "pos": [2190, 201], "size": [315, 82], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 195}, {"name": "dir", "type": "STRING", "link": 196, "widget": {"name": "dir"}}, {"name": "filename", "type": "STRING", "link": 202, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "Conrain_SaveMask"}, "widgets_values": ["", ""]}, {"id": 121, "type": "ConrainTextSave", "pos": [2202, 607], "size": [315, 106], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 184, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 188, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 186, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 83, "type": "LLModel", "pos": [1362, 688], "size": [210, 180], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image_list", "type": "IMAGE", "link": 159, "label": "image_list", "shape": 7}, {"name": "ref_image", "type": "IMAGE", "link": null, "label": "ref_image", "shape": 7}, {"name": "prompt", "type": "STRING", "link": 163, "label": "prompt", "widget": {"name": "prompt"}}], "outputs": [{"name": "result_text", "type": "STRING", "links": [149], "slot_index": 0, "label": "result_text", "shape": 3}, {"name": "result_detail", "type": "STRING", "links": [], "slot_index": 1, "label": "result_detail", "shape": 3}], "title": "抠图-衣服描述", "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["I need to use Segment Anything and Grounding DINO object detection algorithms to detect clothing in images for image segmentation. Please tell me how to describe the target clothing in English without adding a comma or period\n\nExamples:\nblue bikini\nblack long-sleeve dress with embroidered details\nbrown textured one-piece swimsuit\nsleeveless floral pattern dress\ncolorful floral pattern short-sleeve shirt\npink cardigan pleated skirt\nred bikini\nbeige floral pattern dress with brown belt\nblack sleeveless top beige textured skirt\nblack sleeveless top denim skirt\nblack long-sleeve shirt floral pattern pants\ncream long-sleeve top cream wide-leg pants\npink belted coat\nblack sequin dress\nblack bikini\nsleeveless orange floral dress\nblue and white checkered sweater gray high-waisted jeans\npink and red sweater\nnavy blue floral pattern dress\nbeige knee-length dress with short sleeves\noff-shoulder coral dress\nlong sleeve floral pattern dress\nlong sleeve floral pattern dress\nblue floral pattern dress\nblack sleeveless dress\ndark green trench coat\nblue and white floral pattern dress with three-quarter sleeves\nsleeveless blue dress\nblack blazer blue sleeveless dress\nwhite blazer white top white pants\nbeige trench coat white top white pants\nbeige trench coat white inner shirt white pants\ndark blue jeans\nlight blue jeans\nsleeveless beige floral pattern gown\nwhite short-sleeve button-up shirt\nlight blue wide-leg jeans\nlight blue high-waisted jeans\nwide-leg dark blue jeans\nbeige blouse dark blue wide-leg jeans\nwhite long-sleeve blouse\ndark blue wide-leg jeans\nbeige blouse with colorful embroidery\ncream floral embroidered short-sleeve dress\nwhite long-sleeve lace dress", "default"]}, {"id": 116, "type": "LLModel", "pos": [1320.14013671875, 1142.2947998046875], "size": [210, 180], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "image_list", "type": "IMAGE", "link": 173, "label": "image_list", "shape": 7}, {"name": "ref_image", "type": "IMAGE", "link": null, "label": "ref_image", "shape": 7}, {"name": "prompt", "type": "STRING", "link": 174, "label": "prompt", "widget": {"name": "prompt"}}], "outputs": [{"name": "result_text", "type": "STRING", "links": [171], "slot_index": 0, "label": "result_text", "shape": 3}, {"name": "result_detail", "type": "STRING", "links": [], "slot_index": 1, "label": "result_detail", "shape": 3}], "title": "抠图-衣服描述", "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["I need to use Segment Anything and Grounding DINO object detection algorithms to detect clothing in images for image segmentation. Please tell me how to describe the target clothing in English without adding a comma or period\n\nExamples:\nblue bikini\nblack long-sleeve dress with embroidered details\nbrown textured one-piece swimsuit\nsleeveless floral pattern dress\ncolorful floral pattern short-sleeve shirt\npink cardigan pleated skirt\nred bikini\nbeige floral pattern dress with brown belt\nblack sleeveless top beige textured skirt\nblack sleeveless top denim skirt\nblack long-sleeve shirt floral pattern pants\ncream long-sleeve top cream wide-leg pants\npink belted coat\nblack sequin dress\nblack bikini\nsleeveless orange floral dress\nblue and white checkered sweater gray high-waisted jeans\npink and red sweater\nnavy blue floral pattern dress\nbeige knee-length dress with short sleeves\noff-shoulder coral dress\nlong sleeve floral pattern dress\nlong sleeve floral pattern dress\nblue floral pattern dress\nblack sleeveless dress\ndark green trench coat\nblue and white floral pattern dress with three-quarter sleeves\nsleeveless blue dress\nblack blazer blue sleeveless dress\nwhite blazer white top white pants\nbeige trench coat white top white pants\nbeige trench coat white inner shirt white pants\ndark blue jeans\nlight blue jeans\nsleeveless beige floral pattern gown\nwhite short-sleeve button-up shirt\nlight blue wide-leg jeans\nlight blue high-waisted jeans\nwide-leg dark blue jeans\nbeige blouse dark blue wide-leg jeans\nwhite long-sleeve blouse\ndark blue wide-leg jeans\nbeige blouse with colorful embroidery\ncream floral embroidered short-sleeve dress\nwhite long-sleeve lace dress", "default"]}, {"id": 112, "type": "CR Text", "pos": [499, 1129], "size": [428.2350158691406, 248.01002502441406], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [168], "slot_index": 0, "label": "text", "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "label": "show_help", "shape": 3}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["You are an expert garment descriptor. Analyze the image and generate a structured, detailed description based on the Input garment part provided following these rules:\n\n1. **Required Features**\nDescribe only features present in the garment(s), including:\n- Type, color, collar style, sleeve length, garment length (use precise terms: bust-length, waist-length, belly button-length, hips-length, upper thigh-length, mid-thigh-length, knee-length, calf-length, ankle-length, floor-length)\n- Pockets, buttons, graphics, lace, gradient color (specify color transitions), pleats, cutouts, belt, semi-transparency, text (describe in parentheses, e.g., has text on the chest (delighted)), and any other distinct details\n- For dresses, specify if there is a slit\n\n2. **Description Structure**\n- First Sentence:\n  - For single garments: \"[Color] [garment type].\"(Example: \"White shirt.\")\n  - For two-piece outfits: For two-piece outfits: \"[Color] [upper garment] and [color] [lower garment].\"(Example: \"Blue T-shirt and black jeans.\")\n\n- Subsequent Sentences: \n  - Begin with the garment type (e.g., \"The dress...\") or a pronoun (\"It\"/\"They\") after the first mention.\n  - Combine 2–3 related features per sentence using \"and\" or \"with.\"\n  - Only mention features that are present.\n\n3. **Special Instructions**\n- For text, include the content in parentheses.\n- For gradients, specify the direction and colors (e.g., \"gradient from light blue to dark blue\").\n- For two-piece outfits, describe upper and lower garments separately.\n\n4. **Prohibited**\n- Do **not** use commas, the word \"top\" or generic/ambiguous phrasing.\n\nExample:\nInput: upper garment\nDark blue dress. It has a V-neck collar and long sleeves. The dress is ankle length with a fitted waist and drawstring neckline.\n\nInput: outfit\nBlack velvet dress. It has a cowl neckline and mid-thigh length with semi-transparent lace sleeves. Features a fitted bodice and flared skirt with a slit.\n\nInput: upper garment\nLight pink puffer jacket. It has a high collar and knee-length cut with long sleeves. Features a zipper front and two side pockets. Includes text on the chest (delighted) and a sleeve patch.\n\nInput: upper garment\nLight beige puffer jacket. It has a hood and mid-thigh length with long sleeves. Features a zipper front and two side pockets. Includes a sleeve patch and solid light beige fabric.\n\nInput: outfit\nBlack shirt and light blue jeans. The shirt has a turtleneck collar and waist-length cut with sleeveless design. The jeans are ankle length and feature a button closure with front and back pockets. They have a solid light blue color.\n\nInput: outfit\nLight blue jacket and pants. The jacket has a round collar and waist-length cut with button-down front. Features two front pockets and lace trim. The pants are ankle length with a straight cut and solid light blue fabric.\n\nInput: outfit\nLight blue bathrobe. It has a V-neck collar and calf-length cut with long sleeves. Features a gradient from light to dark blue and lace trim on collar and pockets. Includes a button-down front two pockets and ruffled hem.\n\nInput: upper garment\nBlack and white shirt. It has a round collar and waist-length cut with long sleeves. Features a gradient from white shoulders to black hem. Made of solid fabric.\n\nInput: upper garment\nGray sweater and cardigan. The sweater has a V-neck collar and waist-length cut with long sleeves. The cardigan is hips length with a button-down front and striped left sleeve (red and white). Both are made of solid gray fabric.\n\nInput: outfit\nLight pink and black romper. It has a halter collar and upper thigh length with sleeveless design. Features a fitted waist and cow print pattern. Includes lace trim at the hem.\n\nInput: target_cloth"], "color": "#232", "bgcolor": "#353"}], "links": [[11, 4, 0, 8, 2, "IMAGE"], [13, 7, 0, 8, 0, "SAM_MODEL"], [104, 71, 0, 8, 1, "GROUNDING_DINO_MODEL"], [115, 73, 0, 4, 0, "*"], [146, 86, 0, 100, 0, "STRING"], [147, 87, 0, 100, 1, "STRING"], [149, 83, 0, 105, 0, "*"], [150, 87, 0, 105, 1, "*"], [157, 4, 0, 108, 0, "IMAGE"], [159, 108, 0, 83, 0, "IMAGE"], [163, 100, 0, 83, 2, "STRING"], [167, 105, 0, 8, 3, "STRING"], [168, 112, 0, 114, 0, "STRING"], [169, 87, 0, 114, 1, "STRING"], [171, 116, 0, 117, 0, "*"], [173, 108, 0, 116, 0, "IMAGE"], [174, 114, 0, 116, 2, "STRING"], [184, 117, 0, 121, 0, "STRING"], [186, 119, 1, 121, 2, "STRING"], [188, 122, 0, 121, 1, "STRING"], [190, 119, 0, 122, 0, "STRING"], [195, 8, 1, 126, 0, "MASK"], [196, 122, 0, 126, 1, "STRING"], [201, 119, 1, 128, 0, "STRING"], [202, 128, 0, 126, 2, "STRING"]], "groups": [{"id": 4, "title": "Group", "bounding": [226.3644256591797, 106.80716705322266, 1903.818359375, 421.44561767578125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "Group", "bounding": [229, 576.4000244140625, 1907.48828125, 429.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Group", "bounding": [232.2926025390625, 1050.6676025390625, 1906.3692626953125, 344.60980224609375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8954302432552391, "offset": [-26.509703834392365, -63.93300695312096]}}, "version": 0.4, "seed_widgets": {}}}}}
{"client_id": "${clientId}", "prompt": {"71": {"_meta": {"title": "Conrain GroundingDinoModelLoader"}, "inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader"}, "83": {"_meta": {"title": "抠图-衣服描述"}, "inputs": {"prompt": ["86", 0], "llm_model": "default", "image_list": ["108", 0]}, "class_type": "LLModel"}, "86": {"_meta": {"title": "🔤 CR Text"}, "inputs": {"text": "Please identify and return the types of clothing in the image. Output the result in JSON format with the following fields:\n- upper: Type of upper garment (Options: jacket, hoodie, T-shirt, shirt, sweater, bra, vest, coat, scarf)\n- lower: Type of lower garment (Options: trousers, shorts, panties, skirt, leggings)\n- onepiece: Type of one-piece garment (Options: dress, clothing. clothing for other onepiece garment)\n\nrules:\n- If the image contains an upper or lower garment, return the corresponding type(s); leave the field empty if not present.\n- If there is only a one-piece garment, return only the onepiece field with its type, and set upper and lower to empty.\n- Do not return any extra information; only output the fields above.\n\nExample output:\n```json\n{\n  \"upper\": \"t-shirt, jacket\",\n  \"lower\": \"\",\n  \"onepiece\": \"\"\n}\n```\n```json\n{\n  \"upper\": \"T-shirt\",\n  \"lower\": \"jeans\",\n  \"onepiece\": \"\"\n}\n```\n```json\n{\n  \"upper\": \"\",\n  \"lower\": \"\",\n  \"onepiece\": \"dress\"\n}\n```\n```json\n{\n  \"upper\": \"\",\n  \"lower\": \"trousers\",\n  \"onepiece\": \"\"\n}\n```"}, "class_type": "CR Text"}, "105": {"_meta": {"title": "gpt返回结果处理"}, "inputs": {"any_a": ["83", 0], "call_code": "import json\ndef call(any_a, any_b, any_c, any_d):\n    upper_cloths = 'T-shirt . shirt . sweater . bra . jacket . scarf'\n    lower_cloths = 'trousers . shorts . skirt . panties . leggings'\n    one_piece = 'dress . clothing'\n    gpt_res = any_a.strip()\n    if gpt_res is None or len(gpt_res) == 0 or 'Failed to make the request' in gpt_res or len(gpt_res) > 200:\n        return [upper_cloths, lower_cloths, one_piece],\n    \n    try:\n        gpt_res = \"{\" + gpt_res.split(\"{\", maxsplit=1)[-1]\n        gpt_res =  gpt_res.rsplit(\"}\", maxsplit=1)[0] + \"}\"\n\n        res_json = json.loads(gpt_res)\n        upper_cloths = res_json.get(\"upper\", \"\")\n        lower_cloths = res_json.get(\"lower\", \"\")\n        one_piece = res_json.get(\"onepiece\", \"\")\n    except:\n        return [upper_cloths, lower_cloths, one_piece],\n\n    if not one_piece:\n        one_piece = ', '.join([upper_cloths, lower_cloths])\n    if 'clothing' not in one_piece:\n        one_piece += ', clothing'\n\n    if not upper_cloths:\n        upper_cloths = one_piece\n    if not lower_cloths:\n        lower_cloths = one_piece\n    \n    return [upper_cloths, lower_cloths, one_piece],"}, "class_type": "ConrainPythonExecutor"}, "108": {"_meta": {"title": "🔧 Image Resize"}, "inputs": {"image": ["152", 0], "width": 1024, "height": 1024, "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 1, "interpolation": "lanc<PERSON>s"}, "class_type": "ImageResize+"}, "119": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_mask_0", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "122": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["119", 0]}, "class_type": "JWStringConcat"}, "126": {"_meta": {"title": "Conrain <PERSON>"}, "inputs": {"dir": ["122", 0], "mask": ["131", 1], "filename": ["128", 0]}, "class_type": "Conrain_SaveMask"}, "128": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": ["119", 1], "b": ".png"}, "class_type": "JWStringConcat"}, "130": {"_meta": {"title": "SAMModelLoader (segment anything)"}, "inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "SAMModelLoader (segment anything)"}, "131": {"_meta": {"title": "Conrain GroundingDinoSAMSegment v2"}, "inputs": {"image": ["152", 0], "prompt": ["135", 0], "sam_model": ["130", 0], "threshold": 0.3, "background": "white", "grounding_dino_model": ["71", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment_v2"}, "135": {"_meta": {"title": "conrain python executor"}, "inputs": {"any_a": ["105", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn any_a[0],"}, "class_type": "ConrainPythonExecutor"}, "138": {"_meta": {"title": "conrain python executor"}, "inputs": {"any_a": ["105", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn any_a[1],"}, "class_type": "ConrainPythonExecutor"}, "140": {"_meta": {"title": "conrain python executor"}, "inputs": {"any_a": ["105", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn any_a[2],"}, "class_type": "ConrainPythonExecutor"}, "144": {"_meta": {"title": "🔤 CR Text"}, "inputs": {"text": "```json\n{\n  \"upper\": \"bra\",\n  \"lower\": \"panties\",\n  \"onepiece\": \"\"\n}\n```"}, "class_type": "CR Text"}, "146": {"_meta": {"title": "Conrain GroundingDinoSAMSegment v2"}, "inputs": {"image": ["152", 0], "prompt": ["138", 0], "sam_model": ["130", 0], "threshold": 0.3, "background": "white", "grounding_dino_model": ["71", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment_v2"}, "147": {"_meta": {"title": "Conrain GroundingDinoSAMSegment v2"}, "inputs": {"image": ["152", 0], "prompt": ["140", 0], "sam_model": ["130", 0], "threshold": 0.3, "background": "white", "grounding_dino_model": ["71", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment_v2"}, "152": {"_meta": {"title": "Load Images From String"}, "inputs": {"paths": "${originImagePath}", "ignore_missing_images": "false"}, "class_type": "JWLoadImagesFromString"}, "156": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_mask_2", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "158": {"_meta": {"title": "Conrain <PERSON>"}, "inputs": {"dir": ["167", 0], "mask": ["147", 1], "filename": ["168", 0]}, "class_type": "Conrain_SaveMask"}, "167": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["156", 0]}, "class_type": "JWStringConcat"}, "168": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": ["156", 1], "b": ".png"}, "class_type": "JWStringConcat"}, "169": {"_meta": {"title": "Conrain <PERSON>"}, "inputs": {"dir": ["170", 0], "mask": ["146", 1], "filename": ["172", 0]}, "class_type": "Conrain_SaveMask"}, "170": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["171", 0]}, "class_type": "JWStringConcat"}, "171": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_mask_1", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "172": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": ["171", 1], "b": ".png"}, "class_type": "JWStringConcat"}, "174": {"_meta": {"title": "conrain save image"}, "inputs": {"dpi": 300, "images": ["186", 0], "quality": 100, "extension": "png", "output_path": ["177", 0], "use_time_str": "false", "lossless_webp": "false", "embed_workflow": "true", "optimize_image": "true", "output_as_root": "false", "filename_prefix": ["183", 1]}, "class_type": "ConrainImageSave"}, "175": {"_meta": {"title": "conrain save image"}, "inputs": {"dpi": 300, "images": ["187", 0], "quality": 100, "extension": "png", "output_path": ["179", 0], "use_time_str": "false", "lossless_webp": "false", "embed_workflow": "true", "optimize_image": "true", "output_as_root": "false", "filename_prefix": ["184", 1]}, "class_type": "ConrainImageSave"}, "176": {"_meta": {"title": "conrain save image"}, "inputs": {"dpi": 300, "images": ["188", 0], "quality": 100, "extension": "png", "output_path": ["181", 0], "use_time_str": "false", "lossless_webp": "false", "embed_workflow": "true", "optimize_image": "true", "output_as_root": "false", "filename_prefix": ["185", 1]}, "class_type": "ConrainImageSave"}, "177": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["183", 0]}, "class_type": "JWStringConcat"}, "179": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["184", 0]}, "class_type": "JWStringConcat"}, "181": {"_meta": {"title": "String Concatenate"}, "inputs": {"a": "output/", "b": ["185", 0]}, "class_type": "JWStringConcat"}, "183": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_origin_0", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "184": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_origin_1", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "185": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}_origin_2", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "186": {"_meta": {"title": "Apply Mask to Image"}, "inputs": {"mask": ["131", 1], "image": ["131", 0]}, "class_type": "ETN_ApplyMaskToImage"}, "187": {"_meta": {"title": "Apply Mask to Image"}, "inputs": {"mask": ["146", 1], "image": ["146", 0]}, "class_type": "ETN_ApplyMaskToImage"}, "188": {"_meta": {"title": "Apply Mask to Image"}, "inputs": {"mask": ["147", 1], "image": ["147", 0]}, "class_type": "ETN_ApplyMaskToImage"}}, "extra_data": {"extra_pnginfo": {"workflow": {"extra": {"ds": {"scale": 0.7400249944258174, "offset": [716.8009653348582, 47.85808927854686]}}, "links": [[157, 4, 0, 108, 0, "IMAGE"], [159, 108, 0, 83, 0, "IMAGE"], [190, 119, 0, 122, 0, "STRING"], [201, 119, 1, 128, 0, "STRING"], [203, 130, 0, 131, 0, "SAM_MODEL"], [204, 71, 0, 131, 1, "GROUNDING_DINO_MODEL"], [205, 131, 1, 126, 0, "MASK"], [207, 4, 0, 131, 2, "IMAGE"], [210, 86, 0, 83, 2, "STRING"], [211, 83, 0, 132, 0, "*"], [213, 105, 0, 135, 0, "*"], [218, 105, 0, 138, 0, "*"], [220, 105, 0, 140, 0, "*"], [230, 83, 0, 105, 0, "*"], [231, 130, 0, 146, 0, "SAM_MODEL"], [232, 71, 0, 146, 1, "GROUNDING_DINO_MODEL"], [233, 4, 0, 146, 2, "IMAGE"], [234, 138, 0, 146, 3, "STRING"], [235, 135, 0, 131, 3, "STRING"], [236, 130, 0, 147, 0, "SAM_MODEL"], [237, 71, 0, 147, 1, "GROUNDING_DINO_MODEL"], [238, 4, 0, 147, 2, "IMAGE"], [240, 140, 0, 147, 3, "STRING"], [245, 152, 0, 4, 0, "*"], [279, 122, 0, 126, 1, "STRING"], [280, 128, 0, 126, 2, "STRING"], [295, 156, 0, 167, 0, "STRING"], [296, 147, 1, 158, 0, "MASK"], [297, 167, 0, 158, 1, "STRING"], [298, 156, 1, 168, 0, "STRING"], [299, 168, 0, 158, 2, "STRING"], [300, 146, 1, 169, 0, "MASK"], [301, 171, 0, 170, 0, "STRING"], [302, 170, 0, 169, 1, "STRING"], [303, 172, 0, 169, 2, "STRING"], [304, 171, 1, 172, 0, "STRING"], [306, 183, 0, 177, 0, "STRING"], [308, 177, 0, 174, 1, "STRING"], [310, 184, 0, 179, 0, "STRING"], [312, 179, 0, 175, 1, "STRING"], [314, 185, 0, 181, 0, "STRING"], [316, 181, 0, 176, 1, "STRING"], [321, 183, 1, 174, 2, "STRING"], [322, 184, 1, 175, 2, "STRING"], [323, 185, 1, 176, 2, "STRING"], [324, 131, 1, 186, 1, "MASK"], [325, 131, 0, 186, 0, "IMAGE"], [326, 186, 0, 174, 0, "IMAGE"], [327, 146, 0, 187, 0, "IMAGE"], [328, 146, 1, 187, 1, "MASK"], [329, 187, 0, 175, 0, "IMAGE"], [330, 147, 0, 188, 0, "IMAGE"], [331, 147, 1, 188, 1, "MASK"], [332, 188, 0, 176, 0, "IMAGE"]], "nodes": [{"id": 108, "pos": [313.5274658203125, 280.41973876953125], "mode": 0, "size": [315, 218], "type": "ImageResize+", "flags": {"collapsed": true}, "order": 21, "inputs": [{"link": 157, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [159], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1024, 1024, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 1]}, {"id": 86, "pos": [520.56005859375, 226.393310546875], "mode": 0, "size": [391.66986083984375, 188.420166015625], "type": "CR Text", "flags": {}, "order": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "label": "text", "links": [210], "shape": 3, "slot_index": 0}, {"name": "show_help", "type": "STRING", "label": "show_help", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["Please identify and return the types of clothing in the image. Output the result in JSON format with the following fields:\n- upper: Type of upper garment (Options: jacket, hoodie, T-shirt, shirt, sweater, bra, vest, coat, scarf)\n- lower: Type of lower garment (Options: trousers, shorts, panties, skirt, leggings)\n- onepiece: Type of one-piece garment (Options: dress, clothing. clothing for other onepiece garment)\n\nrules:\n- If the image contains an upper or lower garment, return the corresponding type(s); leave the field empty if not present.\n- If there is only a one-piece garment, return only the onepiece field with its type, and set upper and lower to empty.\n- Do not return any extra information; only output the fields above.\n\nExample output:\n```json\n{\n  \"upper\": \"t-shirt, jacket\",\n  \"lower\": \"\",\n  \"onepiece\": \"\"\n}\n```\n```json\n{\n  \"upper\": \"T-shirt\",\n  \"lower\": \"jeans\",\n  \"onepiece\": \"\"\n}\n```\n```json\n{\n  \"upper\": \"\",\n  \"lower\": \"\",\n  \"onepiece\": \"dress\"\n}\n```\n```json\n{\n  \"upper\": \"\",\n  \"lower\": \"trousers\",\n  \"onepiece\": \"\"\n}\n```"]}, {"id": 144, "pos": [1220.3079833984375, 474.7861022949219], "mode": 0, "size": [210, 159.1121063232422], "type": "CR Text", "flags": {}, "order": 1, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["```json\n{\n  \"upper\": \"bra\",\n  \"lower\": \"panties\",\n  \"onepiece\": \"\"\n}\n```"]}, {"id": 138, "pos": [385.2263793945313, 1240.496826171875], "mode": 0, "size": [255.64585876464844, 138.13394165039062], "type": "ConrainPythonExecutor", "flags": {"collapsed": true}, "order": 26, "inputs": [{"link": 218, "name": "any_a", "type": "*", "shape": 7}, {"link": null, "name": "any_b", "type": "*", "shape": 7}, {"link": null, "name": "any_c", "type": "*", "shape": 7}, {"link": null, "name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [234], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn any_a[1],"]}, {"id": 140, "pos": [391.6907958984375, 1462.51708984375], "mode": 0, "size": [255.64585876464844, 138.13394165039062], "type": "ConrainPythonExecutor", "flags": {"collapsed": true}, "order": 27, "inputs": [{"link": 220, "name": "any_a", "type": "*", "shape": 7}, {"link": null, "name": "any_b", "type": "*", "shape": 7}, {"link": null, "name": "any_c", "type": "*", "shape": 7}, {"link": null, "name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [240], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn any_a[2],"]}, {"id": 135, "pos": [381.60504150390625, 1088.551025390625], "mode": 0, "size": [255.64585876464844, 138.13394165039062], "type": "ConrainPythonExecutor", "flags": {"collapsed": true}, "order": 25, "inputs": [{"link": 213, "name": "any_a", "type": "*", "shape": 7}, {"link": null, "name": "any_b", "type": "*", "shape": 7}, {"link": null, "name": "any_c", "type": "*", "shape": 7}, {"link": null, "name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [235], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn any_a[0],"]}, {"id": 105, "pos": [1567.48876953125, 243.61398315429688], "mode": 0, "size": [487.4883117675781, 285.990478515625], "type": "ConrainPythonExecutor", "flags": {}, "order": 24, "title": "gpt返回结果处理", "inputs": [{"link": 230, "name": "any_a", "type": "*", "label": "any_a", "shape": 7}, {"link": null, "name": "any_b", "type": "*", "label": "any_b", "shape": 7}, {"link": null, "name": "any_c", "type": "*", "label": "any_c", "shape": 7}, {"link": null, "name": "any_d", "type": "*", "label": "any_d", "shape": 7}], "outputs": [{"name": "any", "type": "*", "label": "any", "links": [213, 218, 220], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["import json\ndef call(any_a, any_b, any_c, any_d):\n    upper_cloths = 'T-shirt . shirt . sweater . bra . jacket . scarf'\n    lower_cloths = 'trousers . shorts . skirt . panties . leggings'\n    one_piece = 'dress . clothing'\n    gpt_res = any_a.strip()\n    if gpt_res is None or len(gpt_res) == 0 or 'Failed to make the request' in gpt_res or len(gpt_res) > 200:\n        return [upper_cloths, lower_cloths, one_piece],\n    \n    try:\n        gpt_res = \"{\" + gpt_res.split(\"{\", maxsplit=1)[-1]\n        gpt_res =  gpt_res.rsplit(\"}\", maxsplit=1)[0] + \"}\"\n\n        res_json = json.loads(gpt_res)\n        upper_cloths = res_json.get(\"upper\", \"\")\n        lower_cloths = res_json.get(\"lower\", \"\")\n        one_piece = res_json.get(\"onepiece\", \"\")\n    except:\n        return [upper_cloths, lower_cloths, one_piece],\n\n    if not one_piece:\n        one_piece = ', '.join([upper_cloths, lower_cloths])\n    if 'clothing' not in one_piece:\n        one_piece += ', clothing'\n\n    if not upper_cloths:\n        upper_cloths = one_piece\n    if not lower_cloths:\n        lower_cloths = one_piece\n    \n    return [upper_cloths, lower_cloths, one_piece],"]}, {"id": 4, "pos": [162.10540771484375, 248.72406005859375], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 20, "inputs": [{"link": 245, "name": "", "type": "*", "label": ""}], "outputs": [{"name": "", "type": "IMAGE", "label": "", "links": [157, 207, 233, 238], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 130, "pos": [229.0234375, 764.19677734375], "mode": 0, "size": [319.2122497558594, 58], "type": "SAMModelLoader (segment anything)", "flags": {}, "order": 2, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [203, 231, 236], "slot_index": 0}], "properties": {"Node name for S&R": "SAMModelLoader (segment anything)"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 71, "pos": [238.57077026367188, 883.2874145507812], "mode": 0, "size": [306.52**********, 58], "type": "Conrain_GroundingDinoModelLoader", "flags": {}, "order": 3, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [204, 232, 237], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 83, "pos": [1209.634521484375, 240.3736267089844], "mode": 0, "size": [210, 180], "type": "LLModel", "flags": {}, "order": 22, "title": "抠图-衣服描述", "inputs": [{"link": 159, "name": "image_list", "type": "IMAGE", "label": "image_list", "shape": 7}, {"link": null, "name": "ref_image", "type": "IMAGE", "label": "ref_image", "shape": 7}, {"link": 210, "name": "prompt", "type": "STRING", "label": "prompt", "widget": {"name": "prompt"}}], "outputs": [{"name": "result_text", "type": "STRING", "label": "result_text", "links": [211, 230], "shape": 3, "slot_index": 0}, {"name": "result_detail", "type": "STRING", "label": "result_detail", "links": [], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["I need to use Segment Anything and Grounding DINO object detection algorithms to detect clothing in images for image segmentation. Please tell me how to describe the target clothing in English without adding a comma or period\n\nExamples:\nblue bikini\nblack long-sleeve dress with embroidered details\nbrown textured one-piece swimsuit\nsleeveless floral pattern dress\ncolorful floral pattern short-sleeve shirt\npink cardigan pleated skirt\nred bikini\nbeige floral pattern dress with brown belt\nblack sleeveless top beige textured skirt\nblack sleeveless top denim skirt\nblack long-sleeve shirt floral pattern pants\ncream long-sleeve top cream wide-leg pants\npink belted coat\nblack sequin dress\nblack bikini\nsleeveless orange floral dress\nblue and white checkered sweater gray high-waisted jeans\npink and red sweater\nnavy blue floral pattern dress\nbeige knee-length dress with short sleeves\noff-shoulder coral dress\nlong sleeve floral pattern dress\nlong sleeve floral pattern dress\nblue floral pattern dress\nblack sleeveless dress\ndark green trench coat\nblue and white floral pattern dress with three-quarter sleeves\nsleeveless blue dress\nblack blazer blue sleeveless dress\nwhite blazer white top white pants\nbeige trench coat white top white pants\nbeige trench coat white inner shirt white pants\ndark blue jeans\nlight blue jeans\nsleeveless beige floral pattern gown\nwhite short-sleeve button-up shirt\nlight blue wide-leg jeans\nlight blue high-waisted jeans\nwide-leg dark blue jeans\nbeige blouse dark blue wide-leg jeans\nwhite long-sleeve blouse\ndark blue wide-leg jeans\nbeige blouse with colorful embroidery\ncream floral embroidered short-sleeve dress\nwhite long-sleeve lace dress", "default"]}, {"id": 132, "pos": [1447.**********, 27.98810386657715], "mode": 4, "size": [210, 138.12196350097656], "type": "easy showAnything", "flags": {"collapsed": false}, "order": 23, "inputs": [{"link": 211, "name": "anything", "type": "*", "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["```json\n{\n  \"upper\": \"bra\",\n  \"lower\": \"panties\",\n  \"onepiece\": \"\"\n}\n```"]}, {"id": 168, "pos": [1803.1546630859375, 2436.6357421875], "mode": 0, "size": [210, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 19, "inputs": [{"link": 298, "name": "a", "type": "STRING", "widget": {"name": "a"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [299], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["", ".png"]}, {"id": 169, "pos": [2076.516845703125, 1744.2984619140625], "mode": 0, "size": [315, 82], "type": "Conrain_SaveMask", "flags": {}, "order": 33, "inputs": [{"link": 300, "name": "mask", "type": "MASK"}, {"link": 302, "name": "dir", "type": "STRING", "widget": {"name": "dir"}}, {"link": 303, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "Conrain_SaveMask"}, "widgets_values": ["", ""]}, {"id": 172, "pos": [1733.77587890625, 1807.344970703125], "mode": 0, "size": [210, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 14, "inputs": [{"link": 304, "name": "a", "type": "STRING", "widget": {"name": "a"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [303], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["", ".png"]}, {"id": 126, "pos": [2063.783935546875, 1116.9334716796875], "mode": 0, "size": [315, 82], "type": "Conrain_SaveMask", "flags": {}, "order": 31, "inputs": [{"link": 205, "name": "mask", "type": "MASK"}, {"link": 279, "name": "dir", "type": "STRING", "widget": {"name": "dir"}}, {"link": 280, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "Conrain_SaveMask"}, "widgets_values": ["", ""]}, {"id": 122, "pos": [1753.5606689453125, 1131.603515625], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 11, "inputs": [{"link": 190, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [279], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 170, "pos": [1731.7952880859375, 1754.2313232421875], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 13, "inputs": [{"link": 301, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [302], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 167, "pos": [1791.269775390625, 2377.284912109375], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 18, "inputs": [{"link": 295, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [297], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 119, "pos": [1272.080322265625, 1114.809326171875], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 4, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [190], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [201], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_mask_0", "", ""]}, {"id": 171, "pos": [1256.615966796875, 1738.1201171875], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 5, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [301], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [304], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_mask_1", "", ""]}, {"id": 183, "pos": [1270.7037353515625, 762.6768798828125], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 6, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [306], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [321], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_origin_0", "", ""]}, {"id": 174, "pos": [2045.646484375, 760.3165893554688], "mode": 0, "size": [315, 274], "type": "ConrainImageSave", "flags": {}, "order": 37, "inputs": [{"link": 326, "name": "images", "type": "IMAGE"}, {"link": 308, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"link": 321, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 300, 100, "true", "false", "true", "false", "false"]}, {"id": 175, "pos": [2068.8662109375, 1415.0831298828125], "mode": 0, "size": [315, 274], "type": "ConrainImageSave", "flags": {}, "order": 38, "inputs": [{"link": 329, "name": "images", "type": "IMAGE"}, {"link": 312, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"link": 322, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 300, 100, "true", "false", "true", "false", "false"]}, {"id": 158, "pos": [2064.38916015625, 2340.609375], "mode": 0, "size": [315, 82], "type": "Conrain_SaveMask", "flags": {}, "order": 35, "inputs": [{"link": 296, "name": "mask", "type": "MASK"}, {"link": 297, "name": "dir", "type": "STRING", "widget": {"name": "dir"}}, {"link": 299, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "Conrain_SaveMask"}, "widgets_values": ["", ""]}, {"id": 176, "pos": [2056.775146484375, 2009.9459228515625], "mode": 0, "size": [315, 274], "type": "ConrainImageSave", "flags": {}, "order": 39, "inputs": [{"link": 332, "name": "images", "type": "IMAGE"}, {"link": 316, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"link": 323, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 300, 100, "true", "false", "true", "false", "false"]}, {"id": 128, "pos": [1748.5001220703125, 1190.0518798828125], "mode": 0, "size": [210, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 12, "inputs": [{"link": 201, "name": "a", "type": "STRING", "widget": {"name": "a"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [280], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["", ".png"]}, {"id": 179, "pos": [1743.993408203125, 1466.35205078125], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 16, "inputs": [{"link": 310, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [312], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 181, "pos": [1779.4508056640625, 2074.48828125], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 17, "inputs": [{"link": 314, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [316], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 177, "pos": [1732.9786376953125, 826.7427978515625], "mode": 0, "size": [227.7170257568359, 58], "type": "JWStringConcat", "flags": {"collapsed": true}, "order": 15, "inputs": [{"link": 306, "name": "b", "type": "STRING", "widget": {"name": "b"}}], "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [308], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "JWStringConcat"}, "widgets_values": ["output/", ""]}, {"id": 131, "pos": [706.869140625, 765.3642578125], "mode": 0, "size": [334.321044921875, 146], "type": "Conrain_GroundingDinoSAMSegment_v2", "flags": {}, "order": 28, "inputs": [{"link": 203, "name": "sam_model", "type": "SAM_MODEL"}, {"link": 204, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"link": 207, "name": "image", "type": "IMAGE"}, {"link": 235, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [325], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [205, 324], "slot_index": 1}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment_v2"}, "widgets_values": ["", "white", 0.3]}, {"id": 186, "pos": [1334.8834228515625, 1003.8430786132812], "mode": 0, "size": [239.40000915527344, 46], "type": "ETN_ApplyMaskToImage", "flags": {}, "order": 32, "inputs": [{"link": 325, "name": "image", "type": "IMAGE"}, {"link": 324, "name": "mask", "type": "MASK"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [326], "slot_index": 0}], "properties": {"Node name for S&R": "ETN_ApplyMaskToImage"}}, {"id": 184, "pos": [1272.3709716796875, 1402.19091796875], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 7, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [310], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [322], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_origin_1", "", ""]}, {"id": 146, "pos": [715.5528564453125, 1405.611083984375], "mode": 0, "size": [334.321044921875, 146], "type": "Conrain_GroundingDinoSAMSegment_v2", "flags": {}, "order": 29, "inputs": [{"link": 231, "name": "sam_model", "type": "SAM_MODEL"}, {"link": 232, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"link": 233, "name": "image", "type": "IMAGE"}, {"link": 234, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [327], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [300, 328], "slot_index": 1}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment_v2"}, "widgets_values": ["", "white", 0.3]}, {"id": 187, "pos": [1329.3460693359375, 1646.65771484375], "mode": 0, "size": [239.40000915527344, 46], "type": "ETN_ApplyMaskToImage", "flags": {}, "order": 34, "inputs": [{"link": 327, "name": "image", "type": "IMAGE"}, {"link": 328, "name": "mask", "type": "MASK"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [329], "slot_index": 0}], "properties": {"Node name for S&R": "ETN_ApplyMaskToImage"}}, {"id": 185, "pos": [1266.70263671875, 1996.14453125], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 8, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [314], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [323], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_origin_2", "", ""]}, {"id": 156, "pos": [1263.8494873046875, 2357.711181640625], "mode": 0, "size": [308.0752258300781, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 9, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [295], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [298], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}_mask_2", "", ""]}, {"id": 147, "pos": [716.003173828125, 1997.2591552734375], "mode": 0, "size": [334.321044921875, 146], "type": "Conrain_GroundingDinoSAMSegment_v2", "flags": {}, "order": 30, "inputs": [{"link": 236, "name": "sam_model", "type": "SAM_MODEL"}, {"link": 237, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"link": 238, "name": "image", "type": "IMAGE"}, {"link": 240, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [330], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [296, 331], "slot_index": 1}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment_v2"}, "widgets_values": ["", "white", 0.3]}, {"id": 188, "pos": [1325.6370849609375, 2245.554443359375], "mode": 0, "size": [239.40000915527344, 46], "type": "ETN_ApplyMaskToImage", "flags": {}, "order": 36, "inputs": [{"link": 330, "name": "image", "type": "IMAGE"}, {"link": 331, "name": "mask", "type": "MASK"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [332], "slot_index": 0}], "properties": {"Node name for S&R": "ETN_ApplyMaskToImage"}}, {"id": 152, "pos": [-360.1861267089844, 254.0332794189453], "mode": 0, "size": [400, 200], "type": "JWLoadImagesFromString", "flags": {}, "order": 10, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [245], "slot_index": 0}], "properties": {"Node name for S&R": "JWLoadImagesFromString"}, "widgets_values": ["${originImagePath}", "false"]}], "config": {}, "groups": [{"id": 7, "color": "#3f789e", "flags": {}, "title": "Group", "bounding": [225.560302734375, 157.7933349609375, 1907.48828125, 429.6000061035156], "font_size": 24}], "version": 0.4, "last_link_id": 332, "last_node_id": 188, "seed_widgets": {}}}}}
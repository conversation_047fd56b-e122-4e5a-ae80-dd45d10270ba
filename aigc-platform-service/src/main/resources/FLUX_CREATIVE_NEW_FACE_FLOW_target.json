{"client_id": "a31ecb6528814eaa8a92c7db85c0bef6", "prompt": {"177": {"inputs": {"text": "product/20250704/100920/474268", "text_b": "product_2909145", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["283", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1536", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["574", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": ""}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB", "_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["610", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "sgm_uniform", "steps": "30", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["683", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["378", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), front view,upper body,A half-body photo of a model wearing A deep grey pullover vest-style jacket with a ribbed crew neckline and ribbed hem, layered over a light grey striped button-up shirt, The vest features a small metallic logo on the left chest, with no other visible design elements or embellishments. ,buttoned.A portrait of a person's upper body, no lower body visible,45-year-old Chinese male model, is wearing { mgs2222 black pants} underneath. . 45-year-old Chinese male model, is {holding a black and white disposable coffee cup in left hand}. \n\n\nmgs2222,an indoor studio with a plain light lavender-gray background and a brown faux leather armchair; no other objects visible.sitting and holding a coffee cup.sitting casually in the armchair with left leg crossed over right knee, right arm relaxed and resting on knee, left hand holding a disposable coffee cup near lap.\n\n\nA 30-year-old middle-aged Chinese male model. 45-year-old Chinese male model is with a fat build and a fat belly. Fair skin,45-year-old,fair face,Classic side split short hair, ", "seed": 2048}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["552", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": 1536}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": 1152}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "online_product/2304503_颜色2_35560_20250707_112130/2304503_颜色2_35560_20250707_112130-flux/2304503_颜色2_35560_20250707_112130-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "online_product/博成-白_5373_20250103_113438/博成-白_5373_20250103_113438-flux/博成-白_5373_20250103_113438-flux.safetensors", "strength_model": 0.8, "strength_clip": 0.8, "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "298": {"inputs": {"lora_name": "online_product/男士POLO衫坐姿_12195_20250410_201351/男士POLO衫坐姿_12195_20250410_201351-flux/男士POLO衫坐姿_12195_20250410_201351-flux.safetensors", "strength_model": "0.8", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "331": {"inputs": {"prompts": ",A 30-year-old middle-aged Chinese male model. 45-year-old Chinese male model is with a fat build and a fat belly. Fair skin,45-year-old,fair face,Classic side split short hair, ", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["574", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "373": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "378": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["508", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "389": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "390": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "391": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "392": {"inputs": {"noise_mask": true, "positive": ["412", 1], "negative": ["412", 2], "vae": ["416", 2], "pixels": ["414", 0], "mask": ["699", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "397": {"inputs": {"text": ["331", 0], "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "398": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "399": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["436", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "403": {"inputs": {"invert_mask": false, "grow": 0, "blur": 40, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["575", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "409": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["414", 0], "image_target": ["413", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "410": {"inputs": {"images": ["422", 0], "face": ["399", 0], "crop": ["409", 0], "mask": ["403", 0], "warp": ["414", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "411": {"inputs": {"seed": 704507590299021, "steps": 5, "cfg": 1, "sampler_name": "euler", "scheduler": "kl_optimal", "denoise": 0.6, "model": ["412", 0], "positive": ["392", 0], "negative": ["392", 1], "latent_image": ["392", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "412": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["389", 0], "insightface": ["390", 0], "control_net": ["391", 0], "image": ["446", 0], "model": ["416", 0], "positive": ["397", 0], "negative": ["398", 0], "image_kps": ["414", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "413": {"inputs": {"samples": ["411", 0], "vae": ["416", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "414": {"inputs": {"crop_size": 1024, "crop_factor": 1.5, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["399", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "416": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "417": {"inputs": {"target_size": ["420", 0], "image": ["590", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "418": {"inputs": {"value": 2048}, "class_type": "easy int", "_meta": {"title": "Int"}}, "420": {"inputs": {"mode": false, "a": ["421", 0], "b": ["418", 0]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "421": {"inputs": {"mode": true, "a": ["453", 0], "b": ["453", 1]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "422": {"inputs": {"upscale_method": "bicubic", "scale_by": ["417", 0], "image": ["499", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "429": {"inputs": {"mode": true, "a": ["435", 0], "b": ["435", 1]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "430": {"inputs": {"mode": false, "a": ["434", 0], "b": ["429", 0]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "431": {"inputs": {"target_size": ["430", 0], "image": ["469", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "433": {"inputs": {"upscale_method": "bicubic", "scale_by": ["431", 0], "image": ["469", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "434": {"inputs": {"value": 2048}, "class_type": "easy int", "_meta": {"title": "Int"}}, "435": {"inputs": {"image": ["469", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "436": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["422", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "446": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["447", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "447": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["433", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "453": {"inputs": {"image": ["590", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "462": {"inputs": {"image": "5e2f54f3e46b4515b7b49be15e1fc980.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "469": {"inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}, "class_type": "ImageBatchOneOrMore", "_meta": {"title": "Batch Images One or More"}}, "474": {"inputs": {"Input": ["547", 0], "image1": ["475", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "475": {"inputs": {"image": "9b597db25742495190023f881290f439.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "476": {"inputs": {"image": "725e089f31274ede937b5ab1768bbf05.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "477": {"inputs": {"Input": ["548", 0], "image1": ["476", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "498": {"inputs": {"swap_model": "inswapper_128.onnx", "facedetection_model": "retinaface_resnet50", "face_restore_model": "GFPGANv1.4.pth", "parse_model": "parsenet"}, "class_type": "LoadConrainReactorModels", "_meta": {"title": "Load ConrainReactor Models"}}, "499": {"inputs": {"enabled": true, "face_restore_visibility": 0.7, "codeformer_weight": 0.7, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "keep_largest": "yes", "input_image": ["590", 0], "swap_model": ["498", 0], "facedetection": ["498", 1], "face_restore_model": ["498", 2], "faceparse_model": ["498", 3], "source_image": ["462", 0]}, "class_type": "ConrainReActorFaceSwap", "_meta": {"title": "ConrainReactor Fast Face Swap"}}, "508": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "model": ["296", 0], "pulid_flux": ["512", 0], "eva_clip": ["513", 0], "face_analysis": ["515", 0], "image": ["536", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "512": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "513": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "515": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "525": {"inputs": {"method": "selfie_multiclass_256x256", "confidence": 0.4, "crop_multi": 0, "mask_components": "1,3", "image": ["462", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "536": {"inputs": {"padding_left": 96, "padding_right": 96, "padding_top": 96, "padding_bottom": 96, "image": ["462", 0], "mask": ["525", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "547": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "548": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "549": {"inputs": {"delimiter": ": ", "clean_whitespace": "true", "text_a": ["550", 0], "text_b": ["282", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "550": {"inputs": {"text": "Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "552": {"inputs": {"prompt": ["549", 0], "llm_model": "default"}, "class_type": "LLModel", "_meta": {"title": "llmodel"}}, "557": {"inputs": {"select": ["564", 0], "input": ["269", 0]}, "class_type": "ImpactInversedSwitch", "_meta": {"title": "Inversed Switch (Any)"}}, "564": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\timport torch\n\tkk = int(torch.max(any_a)+1)\n\tif any_b==1:\n\t\tkk = 1\n\treturn [kk]", "any_a": ["580", 0], "any_b": ["715", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "567": {"inputs": {"image": ["557", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "568": {"inputs": {"image": ["567", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "571": {"inputs": {"image": ["557", 1]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "572": {"inputs": {"image": ["571", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "573": {"inputs": {"image": ["605", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "574": {"inputs": {"image": ["573", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "575": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["414", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "577": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["578", 0], "image": ["269", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "578": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "579": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["577", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "580": {"inputs": {"segs": ["579", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "584": {"inputs": {"invert_mask": false, "grow": 10, "blur": 1, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["580", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "590": {"inputs": {"padding_left": 128, "padding_right": 128, "padding_top": 128, "padding_bottom": 128, "image": ["572", 0], "mask": ["584", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "602": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["572", 0], "target_mask": ["645", 0], "target_bounds": ["590", 1], "source": ["410", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "605": {"inputs": {"select": ["564", 0], "sel_mode": false, "input1": ["568", 0], "input2": ["602", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "Switch (Any)"}}, "610": {"inputs": {"noise_seed": 1125899906842624}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "645": {"inputs": {"expand": 64, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 0.99, "decay_factor": 1, "fill_holes": false, "mask": ["584", 0]}, "class_type": "GrowMaskWithBlur", "_meta": {"title": "Grow Mask With Blur"}}, "652": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 0.2, "positive": ["653", 0], "negative": ["653", 1], "control_net": ["660", 0], "vae": ["658", 0], "image": ["657", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "653": {"inputs": {"strength": 0.6, "start_percent": 0, "end_percent": 0.2, "positive": ["661", 0], "negative": ["662", 0], "control_net": ["659", 0], "vae": ["658", 0], "image": ["656", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "654": {"inputs": {"control_net_name": "FLUX.1-dev-Controlnet-Inpainting-Alpha.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "655": {"inputs": {"upscale_method": "nearest-exact", "width": ["285", 0], "height": ["284", 0], "crop": "disabled", "image": ["692", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "656": {"inputs": {"detect_hand": "enable", "detect_body": "enable", "detect_face": "enable", "resolution": 1024, "bbox_detector": "yolox_l.onnx", "pose_estimator": "dw-ll_ucoco_384.onnx", "image": ["655", 0]}, "class_type": "DWPreprocessor", "_meta": {"title": "DWPose Estimator"}}, "657": {"inputs": {"ckpt_name": "depth_anything_vits14.pth", "resolution": 1024, "image": ["655", 0]}, "class_type": "DepthAnythingPreprocessor", "_meta": {"title": "Depth Anything"}}, "658": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "659": {"inputs": {"type": "openpose", "control_net": ["654", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "660": {"inputs": {"type": "depth", "control_net": ["654", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "661": {"inputs": {"text": ["283", 0], "clip": ["681", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "662": {"inputs": {"text": "", "clip": ["681", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "681": {"inputs": {"select": ["716", 0], "sel_mode": false, "input": ["296", 1]}, "class_type": "ImpactInversedSwitch", "_meta": {"title": "Inversed Switch (Any)"}}, "683": {"inputs": {"select": ["716", 0], "sel_mode": false, "input1": ["652", 0], "input2": ["684", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "Switch (Any)"}}, "684": {"inputs": {"text": ["282", 0], "clip": ["681", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "692": {"inputs": {"image": "00.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "699": {"inputs": {"invert_mask": false, "grow": 48, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["575", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "715": {"inputs": {"text": "${(noshowFace)?then(1, 0)}"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "716": {"inputs": {"text": "${(isFixedPose)?then(1, 2)}"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 716, "last_link_id": 1270, "nodes": [{"id": 177, "type": "Text String", "pos": [7108.53466796875, -918.117919921875], "size": [315, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250704/100920/474268", "product_2909145", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 185, "type": "Text Concatenate", "pos": [8235.5361328125, -772.117919921875], "size": [315, 178], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "type": "String to Text", "pos": [6735.53466796875, -705.117919921875], "size": [315, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": [6713.53466796875, -899.117919921875], "size": [315, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 216, "type": "ConrainImageSave", "pos": [8800.2158203125, -1045.4833984375], "size": [320, 266], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 856, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "type": "Text Concatenate", "pos": [7590.53466796875, -951.117919921875], "size": [250, 142], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [7101.53466796875, -1047.117919921875], "size": [315, 58], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 240, "type": "Note", "pos": [7518.7724609375, -1801.3541259765625], "size": [260, 110], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 272, "type": "UNETLoader", "pos": [-752.4918212890625, -1244.5938720703125], "size": [315, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 647, 647, 944], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "fp8_e4m3fn"], "color": "#494949", "bgcolor": "#353535"}, {"id": 274, "type": "KSamplerSelect", "pos": [2272.177001953125, -1318.0************], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [2292.************, -1150.7994384765625], "size": [210, 106], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", "30", 1]}, {"id": 276, "type": "BasicGuider", "pos": [2820.************, -836.0202026367188], "size": [161.1999969482422, 46], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1980.421875, -1312.************], "size": [210, 86.50716400146484], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1152", "1536", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1963.4769287109375, -1063.************], "size": [210, 122], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1152", "1536"]}, {"id": 287, "type": "Reroute", "pos": [2798.178955078125, -1017.5775146484375], "size": [75, 26], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 990, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2763.178955078125, -886.5778198242188], "size": [75, 26], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 320, "type": "ConrainTextSave", "pos": [8806.533203125, -700.117919921875], "size": [315, 106], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1940.7808837890625, -792.6485595703125], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 47, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1964.6405029296875, -691.6353759765625], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 48, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [2173.654296875, -814.6461181640625], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2423.18359375, -818.6460571289062], "size": [210, 34], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [2244.66650390625, -684.6353149414062], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2477.0615234375, -663.6350708007812], "size": [210, 34], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 355, "type": "ImageCrop", "pos": [8277.5361328125, -1075.1180419921875], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 149, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [856], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 365, "type": "Image Size to Number", "pos": [7977.458984375, -1765.8021240234375], "size": [229.20001220703125, 126], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 783, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [8271.44921875, -1690.8021240234375], "size": [210, 67.1211166381836], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 373, "type": "JWStringToInteger", "pos": [-8.453181266784668, -1142.7919921875], "size": [210, 58], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644, 644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-286.10430908203125, -845.451416015625], "size": [315, 154], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "type": "CR Model Input Switch", "pos": [929.436767578125, -866.4072265625], "size": [257.191650390625, 78.78076171875], "flags": {"collapsed": false}, "order": 60, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 944, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [946], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["1"]}, {"id": 379, "type": "UNETLoader", "pos": [-754.51904296875, -1447.7342529296875], "size": [315, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [662, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 389, "type": "InstantIDModelLoader", "pos": [5328.48828125, -913.576171875], "size": [315, 58], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [710], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "type": "InstantIDFaceAnalysis", "pos": [5329.1015625, -843.3685913085938], "size": [315, 58], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [711], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 391, "type": "ControlNetLoader", "pos": [5330.10205078125, -773.3335571289062], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [712], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 398, "type": "CLIPTextEncode", "pos": [5005.51416015625, -755.2023315429688], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": true}, "order": 46, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 956}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [716], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}, {"id": 416, "type": "CheckpointLoaderSimple", "pos": [4600.4384765625, -889.787353515625], "size": [254.65628051757812, 146.35491943359375], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [954], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [955, 956], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [674, 719], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 417, "type": "UpscaleSizeCalculator", "pos": [4219.96875, -345.91033935546875], "size": [220, 113.94757080078125], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 776}, {"name": "target_size", "type": "INT", "link": 722, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [729], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [2048]}, {"id": 418, "type": "easy int", "pos": [4211.83154296875, -719.6390380859375], "size": [210, 63.99684524536133], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "int", "type": "INT", "links": [725], "slot_index": 0}], "properties": {"Node name for S&R": "easy int"}, "widgets_values": [2048]}, {"id": 420, "type": "ImpactMinMax", "pos": [4216.3291015625, -528.667724609375], "size": [210, 85.42294311523438], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 724}, {"name": "b", "type": "*", "link": 725}], "outputs": [{"name": "INT", "type": "INT", "links": [722], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [false]}, {"id": 421, "type": "ImpactMinMax", "pos": [4214.6884765625, -884.8746948242188], "size": [210, 78], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 726}, {"name": "b", "type": "*", "link": 727}], "outputs": [{"name": "INT", "type": "INT", "links": [724], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [true]}, {"id": 429, "type": "ImpactMinMax", "pos": [5849.576171875, -1414.25634765625], "size": [210, 82.59356689453125], "flags": {"collapsed": true}, "order": 76, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 739}, {"name": "b", "type": "*", "link": 740}], "outputs": [{"name": "INT", "type": "INT", "links": [742], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [true]}, {"id": 430, "type": "ImpactMinMax", "pos": [5849.576171875, -1354.25390625], "size": [210, 85.89134216308594], "flags": {"collapsed": true}, "order": 79, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 741}, {"name": "b", "type": "*", "link": 742}], "outputs": [{"name": "INT", "type": "INT", "links": [744], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactMinMax"}, "widgets_values": [false]}, {"id": 431, "type": "UpscaleSizeCalculator", "pos": [5719.576171875, -1274.247802734375], "size": [220, 94], "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 808}, {"name": "target_size", "type": "INT", "link": 744, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [748], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [2048]}, {"id": 433, "type": "ImageScaleBy", "pos": [5719.576171875, -1204.24169921875], "size": [210, 82], "flags": {"collapsed": true}, "order": 89, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 809}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["bicubic", 0.5]}, {"id": 434, "type": "easy int", "pos": [5709.576171875, -1344.25390625], "size": [210, 63.99684524536133], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "int", "type": "INT", "links": [741], "slot_index": 0}], "properties": {"Node name for S&R": "easy int"}, "widgets_values": [2048]}, {"id": 435, "type": "GetImageSize+", "pos": [5669.576171875, -1404.25634765625], "size": [214.20001220703125, 66], "flags": {"collapsed": true}, "order": 72, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 807}], "outputs": [{"name": "width", "type": "INT", "links": [739], "slot_index": 0}, {"name": "height", "type": "INT", "links": [740], "slot_index": 1}, {"name": "count", "type": "INT", "slot_index": 2}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 446, "type": "CropFaces", "pos": [6217.7548828125, -1762.4124755859375], "size": [221.15121459960938, 146], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 760}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [713, 762], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "type": "DetectFaces", "pos": [5942.67333984375, -1759.6707763671875], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 812}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [760], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 456, "type": "Reroute", "pos": [6088.88037109375, -206.9046173095703], "size": [75, 26], "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1029}], "outputs": [{"name": "", "type": "IMAGE", "links": [782, 783], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 469, "type": "ImageBatchOneOrMore", "pos": [5422.12890625, -1334.54931640625], "size": [201.60000610351562, 126], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 803}, {"name": "image2", "type": "IMAGE", "link": 806, "shape": 7}, {"name": "image3", "type": "IMAGE", "link": 814, "shape": 7}, {"name": "image4", "type": "IMAGE", "shape": 7}, {"name": "image5", "type": "IMAGE", "shape": 7}, {"name": "image6", "type": "IMAGE", "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807, 808, 809], "slot_index": 0}], "properties": {"Node name for S&R": "ImageBatchOneOrMore"}, "widgets_values": []}, {"id": 474, "type": "CR Image Input Switch", "pos": [5023.24853515625, -1353.22216796875], "size": [210, 74], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 813, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 978, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [806], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 477, "type": "CR Image Input Switch", "pos": [5013.24853515625, -1213.22216796875], "size": [210, 74], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 815, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 979, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [814], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 537, "type": "Reroute", "pos": [-91.33226776123047, -1804.3094482421875], "size": [75, 26], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 957}], "outputs": [{"name": "", "type": "IMAGE", "links": [959], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 547, "type": "JWStringToInteger", "pos": [4617.12646484375, -1352.8480224609375], "size": [315, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [978], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 548, "type": "JWStringToInteger", "pos": [4596.44580078125, -1198.3968505859375], "size": [315, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [979], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 571, "type": "ImageInvert", "pos": [4244.26220703125, -1753.230712890625], "size": [210, 26], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1014}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1015], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 573, "type": "ImageInvert", "pos": [6350.69970703125, -59.70892333984375], "size": [210, 26], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1090}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1028], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 386, "type": "Reroute", "pos": [1226.308349609375, -1119.0677490234375], "size": [75, 26], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665, 968], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 572, "type": "ImageInvert", "pos": [4243.45556640625, -1603.160400390625], "size": [210, 26], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1015}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1055, 1084], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 453, "type": "GetImageSize+", "pos": [4204.9345703125, -1005.6846313476562], "size": [214.20001220703125, 66], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 774}], "outputs": [{"name": "width", "type": "INT", "links": [726], "slot_index": 0}, {"name": "height", "type": "INT", "links": [727], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 455, "type": "Reroute", "pos": [4450.54541015625, -979.43212890625], "size": [75, 26], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1057}], "outputs": [{"name": "", "type": "IMAGE", "links": [774, 776, 789, 991], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 584, "type": "MaskFastGrow", "pos": [4202.44677734375, -1463.525634765625], "size": [315, 178], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1046}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1056, 1161], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 10, 1, 0, 1, true]}, {"id": 645, "type": "GrowMaskWithBlur", "pos": [5325.70166015625, -11.308588027954102], "size": [315, 246], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1161}], "outputs": [{"name": "mask", "type": "MASK", "links": [1163], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "GrowMaskWithBlur"}, "widgets_values": [64, 0, false, false, 0, 0.99, 1, false]}, {"id": 656, "type": "DWPreprocessor", "pos": [1639.70751953125, -2429.300048828125], "size": [315, 198], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1185}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1183], "slot_index": 0}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "DWPreprocessor"}, "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 657, "type": "DepthAnythingPreprocessor", "pos": [1641.2066650390625, -2119.08447265625], "size": [315, 82], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1186}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1178], "slot_index": 0}], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 658, "type": "VAELoader", "pos": [1641.1822509765625, -2881.340576171875], "size": [315, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1177, 1182], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 659, "type": "SetUnionControlNetType", "pos": [1704.322998046875, -2746.50341796875], "size": [210, 58], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1187}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1181], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["openpose"]}, {"id": 660, "type": "SetUnionControlNetType", "pos": [1697.9901123046875, -2607.01171875], "size": [210, 58], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1188}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1176], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["depth"]}, {"id": 285, "type": "CR Seed", "pos": [1593.478515625, -868.7614135742188], "size": [243.4204864501953, 102], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591, 1191], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"]}, {"id": 284, "type": "CR Seed", "pos": [1607.478515625, -682.7598266601562], "size": [243.4204864501953, 102], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589, 1192], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"]}, {"id": 579, "type": "ImpactSEGSOrderedFilter", "pos": [3505.20751953125, -1794.609375], "size": [315, 150], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1039}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1040], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS"}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 602, "type": "Bounded Image Blend with Mask", "pos": [5729.3486328125, 33.77896499633789], "size": [365.4000244140625, 142], "flags": {"collapsed": true}, "order": 133, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1084}, {"name": "target_mask", "type": "MASK", "link": 1163}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1081}, {"name": "source", "type": "IMAGE", "link": 1078}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1097], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 409, "type": "easy imageColorMatch", "pos": [4732.91845703125, 86.32450866699219], "size": [210, 102], "flags": {"collapsed": false}, "order": 131, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 1204}, {"name": "image_target", "type": "IMAGE", "link": 1265}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1166], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 414, "type": "CropFaces", "pos": [4902.79052734375, -390.1572570800781], "size": [221.15121459960938, 146], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 720}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [675, 717, 1032, 1204], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [705], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 392, "type": "InpaintModelConditioning", "pos": [5967.10400390625, -957.076904296875], "size": [210, 138], "flags": {}, "order": 128, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 672, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 673}, {"name": "vae", "type": "VAE", "link": 674}, {"name": "pixels", "type": "IMAGE", "link": 675}, {"name": "mask", "type": "MASK", "link": 1238}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [707], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [708], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [709], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 399, "type": "OrderedFaceFilter", "pos": [4614.96630859375, -391.91558837890625], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 684}], "outputs": [{"name": "filtered", "type": "FACE", "links": [702, 720], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 436, "type": "DetectFaces", "pos": [4899.41162109375, -585.5820922851562], "size": [210, 126], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 750}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [684], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 498, "type": "LoadConrainReactorModels", "pos": [4180.31396484375, -2307.305908203125], "size": [327.5999755859375, 190], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [860], "slot_index": 0}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [861], "slot_index": 1}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [862], "slot_index": 2}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [863], "slot_index": 3}], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 422, "type": "ImageScaleBy", "pos": [4609.6904296875, -559.43212890625], "size": [210, 82], "flags": {"collapsed": false}, "order": 120, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 953}, {"name": "scale_by", "type": "FLOAT", "link": 729, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [701, 750], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["bicubic", 0.5]}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2694.10986328125, -1790.061279296875], "size": [292.29705810546875, 127.71533966064453], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1100, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 653, "type": "ControlNetApplySD3", "pos": [2391.3154296875, -2775.152587890625], "size": [315, 186], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1179}, {"name": "negative", "type": "CONDITIONING", "link": 1180}, {"name": "control_net", "type": "CONTROL_NET", "link": 1181}, {"name": "vae", "type": "VAE", "link": 1182}, {"name": "image", "type": "IMAGE", "link": 1183}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1174], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1175], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.6, 0, 0.2]}, {"id": 652, "type": "ControlNetApplySD3", "pos": [2832.12939453125, -2572.30419921875], "size": [315, 186], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1174}, {"name": "negative", "type": "CONDITIONING", "link": 1175}, {"name": "control_net", "type": "CONTROL_NET", "link": 1176}, {"name": "vae", "type": "VAE", "link": 1177}, {"name": "image", "type": "IMAGE", "link": 1178}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1221], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING"}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.5, 0, 0.2]}, {"id": 684, "type": "CLIPTextEncode", "pos": [1870.7403564453125, -1454.3802490234375], "size": [285.6000061035156, 57.11636734008789], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1223}, {"name": "text", "type": "STRING", "link": 1227, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1224], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 410, "type": "WarpFacesBack", "pos": [5029.35791015625, 66.64826965332031], "size": [182.46627807617188, 157.38844299316406], "flags": {"collapsed": false}, "order": 132, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 701}, {"name": "face", "type": "FACE", "link": 702}, {"name": "crop", "type": "IMAGE", "link": 1166}, {"name": "mask", "type": "MASK", "link": 704}, {"name": "warp", "type": "WARP", "link": 705}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1078], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 605, "type": "ImpactSwitch", "pos": [6089.61181640625, -63.44559097290039], "size": [210, 118], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "input1", "type": "IMAGE", "link": 1092, "shape": 7}, {"name": "input2", "type": "IMAGE", "link": 1097}, {"name": "select", "type": "INT", "link": 1094, "widget": {"name": "select"}}, {"name": "input3", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1090], "slot_index": 0, "label": "IMAGE"}, {"name": "selected_label", "type": "STRING"}, {"name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch"}, "widgets_values": [1, false]}, {"id": 681, "type": "ImpactInversedSwitch", "pos": [1591.8997802734375, -1458.39697265625], "size": [210, 122], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "input", "type": "CLIP", "link": 1218}, {"name": "select", "type": "INT", "link": 1269, "widget": {"name": "select"}}], "outputs": [{"name": "output1", "type": "CLIP", "links": [1219, 1220], "slot_index": 0}, {"name": "output2", "type": "CLIP", "links": [1223], "slot_index": 1}, {"name": "output3", "type": "CLIP", "links": null}], "properties": {"Node name for S&R": "ImpactInversedSwitch"}, "widgets_values": [1, false]}, {"id": 655, "type": "ImageScale", "pos": [1261.4132080078125, -2297.064697265625], "size": [315, 130], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1233}, {"name": "width", "type": "INT", "link": 1191, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1192, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1185, 1186], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1344, 1792, "disabled"]}, {"id": 661, "type": "CLIPTextEncode", "pos": [2106.194580078125, -2114.135009765625], "size": [285.6000061035156, 57.11636734008789], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1219}, {"name": "text", "type": "STRING", "link": 1193, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1179]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 692, "type": "LoadImage", "pos": [890.4814453125, -2605.983154296875], "size": [315, 314], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1233], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["00.png", "image"]}, {"id": 654, "type": "ControlNetLoader", "pos": [936.7407836914062, -2879.2412109375], "size": [577.4558715820312, 58.57819366455078], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1187, 1188], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["FLUX.1-dev-Controlnet-Inpainting-Alpha.safetensors"]}, {"id": 688, "type": "Note", "pos": [111.90326690673828, -2005.06640625], "size": [210, 60], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["启用固定姿势创作，输入1.不启用，输入2"], "color": "#432", "bgcolor": "#653"}, {"id": 695, "type": "Note", "pos": [843.668701171875, -2716.392578125], "size": [210, 60], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["这里需要垫图，进去，就算不启用也需要穿一张图进去，comfy ui启动前都会检测这些"], "color": "#432", "bgcolor": "#653"}, {"id": 696, "type": "Note", "pos": [5656.16357421875, -1762.945068359375], "size": [210, 60], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["三张人脸也需要垫图，需要有图片传入，不然不用的话也会报错"], "color": "#432", "bgcolor": "#653"}, {"id": 557, "type": "ImpactInversedSwitch", "pos": [3904.572998046875, -1771.973388671875], "size": [210, 74], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "input", "type": "IMAGE", "link": 1054}, {"name": "sel_mode", "type": "IMAGE", "widget": {"name": "sel_mode"}, "shape": 7}, {"name": "select", "type": "INT", "link": 1013, "widget": {"name": "select"}}], "outputs": [{"name": "output1", "type": "IMAGE", "links": [1008], "slot_index": 0}, {"name": "output2", "type": "IMAGE", "links": [1014], "slot_index": 1}, {"name": "output3", "type": "IMAGE"}], "properties": {"Node name for S&R": "ImpactInversedSwitch"}, "widgets_values": [1, false]}, {"id": 580, "type": "SegsToCombinedMask", "pos": [3508.232421875, -1531.************], "size": [289.79998779296875, 26], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1040}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1045, 1046], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 278, "type": "FluxGuidance", "pos": [2528.37841796875, -1433.034423828125], "size": [211.60000610351562, 58], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1222}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 683, "type": "ImpactSwitch", "pos": [2272.************, -1485.5447998046875], "size": [210, 122], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "input1", "type": "CONDITIONING", "link": 1221, "shape": 7}, {"name": "input2", "type": "CONDITIONING", "link": 1224}, {"name": "input3", "type": "CONDITIONING", "link": null}, {"name": "select", "type": "INT", "link": 1270, "widget": {"name": "select"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1222], "slot_index": 0, "label": "CONDITIONING"}, {"name": "selected_label", "type": "STRING", "links": null}, {"name": "selected_index", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImpactSwitch"}, "widgets_values": [1, false]}, {"id": 564, "type": "ConrainPythonExecutor", "pos": [3491.139892578125, -1433.************], "size": [400, 200], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1045, "shape": 7}, {"name": "any_b", "type": "*", "link": 1268, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1013, 1030, 1094], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\timport torch\n\tkk = int(torch.max(any_a)+1)\n\tif any_b==1:\n\t\tkk = 1\n\treturn [kk]"]}, {"id": 578, "type": "UltralyticsDetectorProvider", "pos": [3126.**********, -1785.18017578125], "size": [340.20001220703125, 78], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1037], "slot_index": 0}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 567, "type": "ImageInvert", "pos": [3914.385498046875, -1362.9998779296875], "size": [210, 26], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1008}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1009], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 568, "type": "ImageInvert", "pos": [3900.605712890625, -1136.447509765625], "size": [210, 26], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1009}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1092], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 697, "type": "Note", "pos": [3136.867919921875, -1039.258056640625], "size": [314.1627502441406, 109.24244689941406], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["无头场景value=1，其他场景0"], "color": "#432", "bgcolor": "#653"}, {"id": 499, "type": "ConrainReActorFaceSwap", "pos": [4916.921875, -2321.333251953125], "size": [367.79998779296875, 370], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 991}, {"name": "swap_model", "type": "FACE_MODEL", "link": 860}, {"name": "facedetection", "type": "FACE_MODEL", "link": 861}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 862}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 863}, {"name": "source_image", "type": "IMAGE", "link": 859, "shape": 7}, {"name": "face_model", "type": "FACE_MODEL", "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [953], "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "slot_index": 1}], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, 0.7, 0.7, "no", "no", "0", "0", 1, "yes"]}, {"id": 590, "type": "Bounded Image Crop with Mask", "pos": [4190.095703125, -1224.7315673828125], "size": [352.79998779296875, 150], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1055}, {"name": "mask", "type": "MASK", "link": 1056}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1057], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1081], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 128, 128, 128]}, {"id": 575, "type": "easy humanSegmentation", "pos": [5240.50048828125, -731.8556518554688], "size": [300, 500], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1032}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [1033, 1237], "slot_index": 1}, {"name": "bbox", "type": "BBOX"}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 411, "type": "K<PERSON><PERSON><PERSON>", "pos": [6240.14697265625, -939.5289306640625], "size": [261.8017578125, 262], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 706}, {"name": "positive", "type": "CONDITIONING", "link": 707}, {"name": "negative", "type": "CONDITIONING", "link": 708}, {"name": "latent_image", "type": "LATENT", "link": 709}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [718], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [704507590299021, "fixed", 5, 1, "euler", "kl_optimal", 0.6]}, {"id": 412, "type": "ApplyInstantID", "pos": [5735.16259765625, -958.6578979492188], "size": [210, 266], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 710, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 711, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 712, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 713, "label": "image"}, {"name": "model", "type": "MODEL", "link": 954, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 715, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 716, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 717, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [706], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [672], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [673], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 403, "type": "MaskFastGrow", "pos": [5544.41259765625, -449.4131774902344], "size": [210, 178], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1033}], "outputs": [{"name": "MASK", "type": "MASK", "links": [704], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 0, 40, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 271, "type": "DualCLIPLoader", "pos": [-764.33056640625, -1052.938232421875], "size": [315, 106], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 664, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 397, "type": "CLIPTextEncode", "pos": [4996.8603515625, -923.70947265625], "size": [210, 96], "flags": {"collapsed": false}, "order": 67, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 955}, {"name": "text", "type": "STRING", "link": 788, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [715], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 457, "type": "Reroute", "pos": [4653.6513671875, -686.2103271484375], "size": [75, 26], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 786, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [788, 788], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 270, "type": "VAELoader", "pos": [2740.021728515625, -1350.376708984375], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520, 971], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [1010.146240234375, -1505.6231689453125], "size": [491.7470703125, 126], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/男士POLO衫坐姿_12195_20250410_201351/男士POLO衫坐姿_12195_20250410_201351-flux/男士POLO衫坐姿_12195_20250410_201351-flux.safetensors", "0.8", 1], "color": "#232", "bgcolor": "#353"}, {"id": 462, "type": "LoadImage", "pos": [4705.64697265625, -1796.65185546875], "size": [235.8109893798828, 314], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [803, 859, 899, 957], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["5e2f54f3e46b4515b7b49be15e1fc980.png", "image"]}, {"id": 475, "type": "LoadImage", "pos": [5037.59375, -1794.5908203125], "size": [249.60922241210938, 314], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [813], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["9b597db25742495190023f881290f439.png", "image"]}, {"id": 476, "type": "LoadImage", "pos": [5377.1533203125, -1775.562744140625], "size": [234.48504638671875, 314], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [815], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["725e089f31274ede937b5ab1768bbf05.png", "image"]}, {"id": 662, "type": "CLIPTextEncode", "pos": [2109.902099609375, -1971.928955078125], "size": [285.6000061035156, 76], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1220}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1180]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 269, "type": "VAEDecode", "pos": [2696.910888671875, -1547.292724609375], "size": [210, 46], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 1038, 1054], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [1023.1715087890625, -827.7898559570312], "size": [411.6590881347656, 124], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [1001.3509521484375, -1126.1610107421875], "size": [400, 198.0215606689453], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [981, 473, 1227], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), front view,upper body,A half-body photo of a model wearing A deep grey pullover vest-style jacket with a ribbed crew neckline and ribbed hem, layered over a light grey striped button-up shirt, The vest features a small metallic logo on the left chest, with no other visible design elements or embellishments. ,buttoned.A portrait of a person's upper body, no lower body visible,45-year-old Chinese male model, is wearing { mgs2222 black pants} underneath. . 45-year-old Chinese male model, is {holding a black and white disposable coffee cup in left hand}. \n\n\nmgs2222,an indoor studio with a plain light lavender-gray background and a brown faux leather armchair; no other objects visible.sitting and holding a coffee cup.sitting casually in the armchair with left leg crossed over right knee, right arm relaxed and resting on knee, left hand holding a disposable coffee cup near lap.\n\n\nA 30-year-old middle-aged Chinese male model. 45-year-old Chinese male model is with a fat build and a fat belly. Fair skin,45-year-old,fair face,Classic side split short hair, ", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2622.48779296875, -1199.384033203125], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [575, 786, 575], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": [",A 30-year-old middle-aged Chinese male model. 45-year-old Chinese male model is with a fat build and a fat belly. Fair skin,45-year-old,fair face,Classic side split short hair, ", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 413, "type": "VAEDecode", "pos": [6552.12158203125, -626.3185424804688], "size": [140, 46], "flags": {"collapsed": false}, "order": 130, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 718}, {"name": "vae", "type": "VAE", "link": 719}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1265], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [985.0911254882812, -1746.033447265625], "size": [587.3407592773438, 126], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 946}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/博成-白_5373_20250103_113438/博成-白_5373_20250103_113438-flux/博成-白_5373_20250103_113438-flux.safetensors", 0.8, 0.8], "color": "#232", "bgcolor": "#353"}, {"id": 610, "type": "RandomNoise", "pos": [2286.822998046875, -1732.85498046875], "size": [315, 82], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1100], "slot_index": 0}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [1125899906842624, "fixed"]}, {"id": 577, "type": "ImpactSimpleDetectorSEGS", "pos": [3168.805908203125, -1570.7054443359375], "size": [315, 310], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1037}, {"name": "image", "type": "IMAGE", "link": 1038}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1039], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 525, "type": "easy humanSegmentation", "pos": [-644.0889282226562, -1792.3453369140625], "size": [300, 260], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 899}], "outputs": [{"name": "image", "type": "IMAGE"}, {"name": "mask", "type": "MASK", "links": [960], "slot_index": 1}, {"name": "bbox", "type": "BBOX"}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 512, "type": "PulidFluxModelLoader", "pos": [-269.21380615234375, -1633.833740234375], "size": [315, 58], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"name": "PULIDFLUX", "type": "PULIDFLUX", "links": [879]}], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "type": "PulidFluxEvaClipLoader", "pos": [-271.33343505859375, -1512.2734375], "size": [327.5999755859375, 26], "flags": {"collapsed": false}, "order": 38, "mode": 0, "inputs": [], "outputs": [{"name": "EVA_CLIP", "type": "EVA_CLIP", "links": [882]}], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "widgets_values": []}, {"id": 515, "type": "PulidFluxInsightFaceLoader", "pos": [-282.47869873046875, -1418.2542724609375], "size": [365.4000244140625, 58], "flags": {}, "order": 39, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [883]}], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "widgets_values": ["CUDA"]}, {"id": 536, "type": "Bounded Image Crop with Mask", "pos": [142.61143493652344, -1767.71484375], "size": [236.27499389648438, 150], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 959}, {"name": "mask", "type": "MASK", "link": 960}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [966], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [96, 96, 96, 96]}, {"id": 550, "type": "CR Text", "pos": [935.0255737304688, -1927.9835205078125], "size": [415.5107116699219, 126.7047348022461], "flags": {}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [980], "slot_index": 0}, {"name": "show_help", "type": "STRING", "slot_index": 1}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"]}, {"id": 508, "type": "ApplyPulidFlux", "pos": [387.3575439453125, -1554.111328125], "size": [315, 226], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 947}, {"name": "pulid_flux", "type": "PULIDFLUX", "link": 879}, {"name": "eva_clip", "type": "EVA_CLIP", "link": 882}, {"name": "face_analysis", "type": "FACEANALYSIS", "link": 883}, {"name": "image", "type": "IMAGE", "link": 966}, {"name": "attn_mask", "type": "MASK", "shape": 7}, {"name": "options", "type": "OPTIONS", "shape": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [948], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "widgets_values": [0.8, 0, 1]}, {"id": 549, "type": "Text Concatenate", "pos": [1680.6427001953125, -1779.34912109375], "size": [213.69569396972656, 179.2432861328125], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 980, "widget": {"name": "text_a"}, "shape": 7}, {"name": "text_b", "type": "STRING", "link": 981, "widget": {"name": "text_b"}, "shape": 7}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "shape": 7}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "shape": 7}], "outputs": [{"name": "STRING", "type": "STRING", "links": [982], "slot_index": 0}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [": ", "true", "", "", "", ""]}, {"id": 552, "type": "LLModel", "pos": [1997.574462890625, -1762.02880859375], "size": [254.6305389404297, 154.52664184570312], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "image_list", "type": "IMAGE", "shape": 7}, {"name": "ref_image", "type": "IMAGE", "shape": 7}, {"name": "prompt", "type": "STRING", "link": 982, "widget": {"name": "prompt"}}], "outputs": [{"name": "result_text", "type": "STRING", "links": [983], "slot_index": 0}, {"name": "result_detail", "type": "STRING"}], "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["你能干嘛", "default"]}, {"id": 553, "type": "Reroute", "pos": [1552.4200439453125, -1306.8441162109375], "size": [75, 26], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 983, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1266], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 283, "type": "ShowText|pysssss", "pos": [1661.213623046875, -1288.0704345703125], "size": [256.63372802734375, 326], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1266, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [990, 1193], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), front view, upper body, half-body portrait of a 45-year-old Chinese male model with a fat build and fair skin, classic side-split short hair, and a fair face. He is wearing a deep grey pullover vest-style jacket with a ribbed crew neckline and ribbed hem, featuring a small metallic logo on the left chest, layered over a light grey striped button-up shirt, and mgs2222 black pants. The model is seated casually in a brown faux leather armchair with a plain light lavender-gray studio background, left leg crossed over right knee, right arm relaxed and resting on knee, and left hand holding a black and white disposable coffee cup near his lap. No other objects or embellishments visible."]}, {"id": 574, "type": "ImageInvert", "pos": [6352.4091796875, 64.93700408935547], "size": [210, 26], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1028}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1029], "slot_index": 0}], "properties": {"Node name for S&R": "ImageInvert"}, "widgets_values": []}, {"id": 699, "type": "MaskFastGrow", "pos": [5562.67236328125, -687.98583984375], "size": [315, 178], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1237}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1238], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 48, 0, 0, 1, true]}, {"id": 236, "type": "CR Upscale Image", "pos": [6632.7724609375, -1550.3541259765625], "size": [315, 222], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 782, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1536", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 261, "type": "Image Size to Number", "pos": [7094.7724609375, -1707.3541259765625], "size": [229.20001220703125, 126], "flags": {}, "order": 145, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 235, "type": "InspyrenetRembg", "pos": [7088.7724609375, -1481.3541259765625], "size": [230, 90], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 248, "type": "EmptyImage", "pos": [7543.7724609375, -1620.3541259765625], "size": [231.5089111328125, 120.12616729736328], "flags": {"collapsed": false}, "order": 144, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [7529.7724609375, -1403.3541259765625], "size": [252, 26], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [7961.7724609375, -1527.3541259765625], "size": [252, 146], "flags": {}, "order": 148, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [8539.7734375, -1613.3541259765625], "size": [220, 118], "flags": {}, "order": 147, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1536"], "color": "#494949", "bgcolor": "#353535"}, {"id": 263, "type": "ImageScaleBy", "pos": [8853.7607421875, -1725.3541259765625], "size": [228.9691162109375, 78], "flags": {}, "order": 146, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 385, "type": "Reroute", "pos": [-177.24099731445312, -1267.6915283203125], "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [997.97021484375, -1317.723876953125], "size": [499.25970458984375, 126], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 654, 947], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [510, 513, 630, 631, 1218], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/2304503_颜色2_35560_20250707_112130/2304503_颜色2_35560_20250707_112130-flux/2304503_颜色2_35560_20250707_112130-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 378, "type": "ModelMergeFlux1", "pos": [490.74951171875, -1213.1884765625], "size": [315, 1566], "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 948}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 715, "type": "JWStringToInteger", "pos": [3181.384521484375, -1166.71142578125], "size": [315, 58], "flags": {}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1268], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["${(noshowFace)?then(1, 0)}"], "color": "#232", "bgcolor": "#353"}, {"id": 716, "type": "JWStringToInteger", "pos": [396.9916687011719, -1969.816162109375], "size": [315, 58], "flags": {}, "order": 42, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1269, 1270], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["${(isFixedPose)?then(1, 2)}"], "color": "#232", "bgcolor": "#353"}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [455, 270, 0, 269, 1, "VAE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [672, 412, 1, 392, 0, "CONDITIONING"], [673, 412, 2, 392, 1, "CONDITIONING"], [674, 416, 2, 392, 2, "VAE"], [675, 414, 0, 392, 3, "IMAGE"], [684, 436, 0, 399, 0, "FACE"], [701, 422, 0, 410, 0, "IMAGE"], [702, 399, 0, 410, 1, "FACE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [707, 392, 0, 411, 1, "CONDITIONING"], [708, 392, 1, 411, 2, "CONDITIONING"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [717, 414, 0, 412, 7, "IMAGE"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [722, 420, 0, 417, 1, "INT"], [724, 421, 0, 420, 0, "*"], [725, 418, 0, 420, 1, "*"], [726, 453, 0, 421, 0, "*"], [727, 453, 1, 421, 1, "*"], [729, 417, 0, 422, 1, "FLOAT"], [739, 435, 0, 429, 0, "*"], [740, 435, 1, 429, 1, "*"], [741, 434, 0, 430, 0, "*"], [742, 429, 0, 430, 1, "*"], [744, 430, 0, 431, 1, "INT"], [748, 431, 0, 433, 1, "FLOAT"], [750, 422, 0, 436, 0, "IMAGE"], [760, 447, 0, 446, 0, "FACE"], [774, 455, 0, 453, 0, "IMAGE"], [776, 455, 0, 417, 0, "IMAGE"], [782, 456, 0, 236, 0, "IMAGE"], [783, 456, 0, 365, 0, "IMAGE"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [803, 462, 0, 469, 0, "IMAGE"], [806, 474, 0, 469, 1, "IMAGE"], [807, 469, 0, 435, 0, "IMAGE"], [808, 469, 0, 431, 0, "IMAGE"], [809, 469, 0, 433, 0, "IMAGE"], [812, 433, 0, 447, 0, "IMAGE"], [813, 475, 0, 474, 0, "IMAGE"], [814, 477, 0, 469, 2, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [859, 462, 0, 499, 5, "IMAGE"], [860, 498, 0, 499, 1, "FACE_MODEL"], [861, 498, 1, 499, 2, "FACE_MODEL"], [862, 498, 2, 499, 3, "FACE_MODEL"], [863, 498, 3, 499, 4, "FACE_MODEL"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [953, 499, 0, 422, 0, "IMAGE"], [954, 416, 0, 412, 4, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [978, 547, 0, 474, 2, "INT"], [979, 548, 0, 477, 2, "INT"], [980, 550, 0, 549, 0, "STRING"], [981, 282, 0, 549, 1, "STRING"], [982, 549, 0, 552, 2, "STRING"], [983, 552, 0, 553, 0, "*"], [990, 283, 0, 287, 0, "*"], [991, 455, 0, 499, 0, "IMAGE"], [1008, 557, 0, 567, 0, "IMAGE"], [1009, 567, 0, 568, 0, "IMAGE"], [1013, 564, 0, 557, 2, "INT"], [1014, 557, 1, 571, 0, "IMAGE"], [1015, 571, 0, 572, 0, "IMAGE"], [1028, 573, 0, 574, 0, "IMAGE"], [1029, 574, 0, 456, 0, "*"], [1030, 564, 0, 563, 1, "INT"], [1032, 414, 0, 575, 0, "IMAGE"], [1033, 575, 1, 403, 0, "MASK"], [1037, 578, 0, 577, 0, "BBOX_DETECTOR"], [1038, 269, 0, 577, 1, "IMAGE"], [1039, 577, 0, 579, 0, "SEGS"], [1040, 579, 0, 580, 0, "SEGS"], [1045, 580, 0, 564, 0, "*"], [1046, 580, 0, 584, 0, "MASK"], [1054, 269, 0, 557, 0, "IMAGE"], [1055, 572, 0, 590, 0, "IMAGE"], [1056, 584, 0, 590, 1, "MASK"], [1057, 590, 0, 455, 0, "*"], [1078, 410, 0, 602, 3, "IMAGE"], [1081, 590, 1, 602, 2, "IMAGE_BOUNDS"], [1084, 572, 0, 602, 0, "IMAGE"], [1090, 605, 0, 573, 0, "IMAGE"], [1092, 568, 0, 605, 0, "IMAGE"], [1094, 564, 0, 605, 2, "INT"], [1097, 602, 0, 605, 1, "IMAGE"], [1100, 610, 0, 273, 0, "NOISE"], [1161, 584, 0, 645, 0, "MASK"], [1163, 645, 0, 602, 1, "MASK"], [1166, 409, 0, 410, 2, "IMAGE"], [1174, 653, 0, 652, 0, "CONDITIONING"], [1175, 653, 1, 652, 1, "CONDITIONING"], [1176, 660, 0, 652, 2, "CONTROL_NET"], [1177, 658, 0, 652, 3, "VAE"], [1178, 657, 0, 652, 4, "IMAGE"], [1179, 661, 0, 653, 0, "CONDITIONING"], [1180, 662, 0, 653, 1, "CONDITIONING"], [1181, 659, 0, 653, 2, "CONTROL_NET"], [1182, 658, 0, 653, 3, "VAE"], [1183, 656, 0, 653, 4, "IMAGE"], [1185, 655, 0, 656, 0, "IMAGE"], [1186, 655, 0, 657, 0, "IMAGE"], [1187, 654, 0, 659, 0, "CONTROL_NET"], [1188, 654, 0, 660, 0, "CONTROL_NET"], [1191, 285, 0, 655, 1, "INT"], [1192, 284, 0, 655, 2, "INT"], [1193, 283, 0, 661, 1, "STRING"], [1204, 414, 0, 409, 0, "IMAGE"], [1218, 296, 1, 681, 0, "*"], [1219, 681, 0, 661, 0, "CLIP"], [1220, 681, 0, 662, 0, "CLIP"], [1221, 652, 0, 683, 0, "*"], [1222, 683, 0, 278, 0, "CONDITIONING"], [1223, 681, 1, 684, 0, "CLIP"], [1224, 684, 0, 683, 1, "CONDITIONING"], [1227, 282, 0, 684, 1, "STRING"], [1233, 692, 0, 655, 0, "IMAGE"], [1237, 575, 1, 699, 0, "MASK"], [1238, 699, 0, 392, 4, "MASK"], [1265, 413, 0, 409, 1, "IMAGE"], [1266, 553, 0, 283, 0, "STRING"], [1268, 715, 0, 564, 1, "*"], [1269, 716, 0, 681, 1, "INT"], [1270, 716, 0, 683, 3, "INT"]], "groups": [{"id": 3, "title": "换背景", "bounding": [6515.6005859375, -1904.582275390625, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [902.8150634765625, -1868.65283203125, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [6517.884765625, -1180.8446044921875, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-834.1985473632812, -1870.67333984375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "换脸", "bounding": [4562.44580078125, -1004.2750854492188, 1951.62158203125, 797.1675415039062], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "再把人脸贴回去", "bounding": [4654.04833984375, -158.63250732421875, 1918.953857421875, 535.1592407226562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "人脸原图", "bounding": [4558.6240234375, -1903.5914306640625, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "限制最大尺寸", "bounding": [4138.9306640625, -1893.307861328125, 416.799560546875, 1684.5103759765625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "controlnet", "bounding": [816.070556640625, -2967.172607421875, 2488.378173828125, 1055.947021484375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.33809166040571464, "offset": [767.520670988001, 2413.7385004220496]}}, "version": 0.4, "seed_widgets": {"282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "610": 0}}}}}
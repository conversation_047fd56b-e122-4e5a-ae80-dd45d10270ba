{"client_id": "${clientId}", "prompt": {"12": {"_meta": {"title": "色值"}, "inputs": {"value": "${targetHexColor}"}, "class_type": "easy string"}, "14": {"_meta": {"title": "texture_preserve"}, "inputs": {"value": "0.7"}, "class_type": "easy string"}, "15": {"_meta": {"title": "🔧 CR String To Number"}, "inputs": {"text": ["14", 0], "round_integer": "round"}, "class_type": "CR String To Number"}, "28": {"_meta": {"title": "<PERSON> Grow Fast"}, "inputs": {"blur": 0, "grow": 0, "mask": ["64", 0], "enabled": true, "low_limit": 0, "high_limit": 1, "invert_mask": false}, "class_type": "MaskFastGrow"}, "29": {"_meta": {"title": "<PERSON> Grow Fast"}, "inputs": {"blur": 1, "grow": 30, "mask": ["28", 0], "enabled": true, "low_limit": 0, "high_limit": 1, "invert_mask": false}, "class_type": "MaskFastGrow"}, "30": {"_meta": {"title": "<PERSON> Grow Fast"}, "inputs": {"blur": 1, "grow": -5, "mask": ["28", 0], "enabled": true, "low_limit": 0, "high_limit": 1, "invert_mask": false}, "class_type": "MaskFastGrow"}, "31": {"_meta": {"title": "MaskComposite"}, "inputs": {"x": 0, "y": 0, "source": ["30", 0], "operation": "subtract", "destination": ["29", 0]}, "class_type": "MaskComposite"}, "43": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "inputs": {"clip": ["54", 0], "text": ""}, "class_type": "CLIPTextEncode"}, "44": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "inputs": {"clip": ["54", 0], "text": ""}, "class_type": "CLIPTextEncode"}, "45": {"_meta": {"title": "VAE Encode"}, "inputs": {"vae": ["53", 0], "pixels": ["114", 0]}, "class_type": "VAEEncode"}, "46": {"_meta": {"title": "Set Latent Noise Mask"}, "inputs": {"mask": ["31", 0], "samples": ["45", 0]}, "class_type": "SetLatentNoiseMask"}, "47": {"_meta": {"title": "VAE Decode"}, "inputs": {"vae": ["53", 0], "samples": ["50", 0]}, "class_type": "VAEDecode"}, "48": {"_meta": {"title": "Image Color Match"}, "inputs": {"method": "adain", "image_ref": ["114", 0], "save_prefix": "ComfyUI", "image_output": "<PERSON>de", "image_target": ["86", 0]}, "class_type": "easy imageColorMatch"}, "50": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "inputs": {"cfg": 1, "seed": 89245266737477, "model": ["52", 0], "steps": 20, "denoise": 0.5, "negative": ["43", 0], "positive": ["79", 0], "scheduler": "simple", "latent_image": ["46", 0], "sampler_name": "euler"}, "class_type": "K<PERSON><PERSON><PERSON>"}, "52": {"_meta": {"title": "Load Diffusion Model"}, "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "fp8_e5m2"}, "class_type": "UNETLoader"}, "53": {"_meta": {"title": "Load VAE"}, "inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "54": {"_meta": {"title": "DualCLIPLoader"}, "inputs": {"type": "flux", "device": "default", "clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors"}, "class_type": "DualCLIPLoader"}, "62": {"_meta": {"title": "Bounded Image Crop with Mask"}, "inputs": {"mask": ["131", 0], "image": ["128", 0], "padding_top": 64, "padding_left": 64, "padding_right": 64, "padding_bottom": 64}, "class_type": "Bounded Image Crop with Mask"}, "63": {"_meta": {"title": "Bounded Image Crop with Mask"}, "inputs": {"mask": ["131", 0], "image": ["130", 0], "padding_top": 64, "padding_left": 64, "padding_right": 64, "padding_bottom": 64}, "class_type": "Bounded Image Crop with Mask"}, "64": {"_meta": {"title": "Convert Image to Mask"}, "inputs": {"image": ["63", 0], "channel": "red"}, "class_type": "ImageToMask"}, "68": {"_meta": {"title": "<PERSON> Grow Fast"}, "inputs": {"blur": 2, "grow": 30, "mask": ["131", 0], "enabled": true, "low_limit": 0, "high_limit": 1, "invert_mask": false}, "class_type": "MaskFastGrow"}, "79": {"_meta": {"title": "FluxGuidance"}, "inputs": {"guidance": 30, "conditioning": ["44", 0]}, "class_type": "FluxGuidance"}, "84": {"_meta": {"title": "🔍 CR Upscale Image"}, "inputs": {"mode": "resize", "image": ["47", 0], "supersample": "true", "resize_width": ["85", 0], "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "rescale_factor": 1, "rounding_modulus": 8, "resampling_method": "bicubic"}, "class_type": "CR Upscale Image"}, "85": {"_meta": {"title": "🔧 Get Image Size"}, "inputs": {"image": ["62", 0]}, "class_type": "GetImageSize+"}, "86": {"_meta": {"title": "Upscale Image"}, "inputs": {"crop": "disabled", "image": ["84", 0], "width": ["85", 0], "height": ["85", 1], "upscale_method": "bicubic"}, "class_type": "ImageScale"}, "97": {"_meta": {"title": "Bounded Image Blend with Mask"}, "inputs": {"source": ["48", 0], "target": ["128", 0], "feathering": 16, "target_mask": ["68", 0], "blend_factor": 1, "target_bounds": ["62", 1]}, "class_type": "Bounded Image Blend with Mask"}, "114": {"_meta": {"title": "conrain recolor"}, "inputs": {"mask": ["64", 0], "images": ["62", 0], "target_color": ["12", 0], "texture_preserve": ["15", 1], "smart_color_adjust": ["139", 0]}, "class_type": "ConrainRecolor"}, "128": {"_meta": {"title": "原图Load Images From String"}, "inputs": {"paths": "${originImagePath}", "ignore_missing_images": "false"}, "class_type": "JWLoadImagesFromString"}, "130": {"_meta": {"title": "mask图Load Images From String"}, "inputs": {"paths": "${maskImagePath}", "ignore_missing_images": "false"}, "class_type": "JWLoadImagesFromString"}, "131": {"_meta": {"title": "Conrain Image To Mask"}, "inputs": {"image": ["130", 0], "method": "intensity"}, "class_type": "ConrainImageToMask"}, "132": {"_meta": {"title": "conrain save image"}, "inputs": {"dpi": 100, "images": ["97", 0], "quality": 100, "extension": "jpg", "output_path": "${outputPath}", "use_time_str": "true", "lossless_webp": "false", "embed_workflow": "false", "optimize_image": "true", "output_as_root": "true", "filename_prefix": "${fileNamePrefix}"}, "class_type": "ConrainImageSave"}, "139": {"_meta": {"title": "🔧 CR String To Bo<PERSON>an"}, "inputs": {"text": ["140", 0]}, "class_type": "CR String To <PERSON>an"}, "140": {"_meta": {"title": "String"}, "inputs": {"value": "${smartRecolorMode}"}, "class_type": "easy string"}}, "extra_data": {"extra_pnginfo": {"workflow": {"extra": {"ds": {"scale": 0.8769226950000026, "offset": [2140.0812767044395, -202.53935511827112]}}, "links": [[15, 14, 0, 15, 0, "STRING"], [43, 28, 0, 29, 0, "MASK"], [44, 28, 0, 30, 0, "MASK"], [45, 29, 0, 31, 0, "MASK"], [46, 30, 0, 31, 1, "MASK"], [61, 54, 0, 43, 0, "CLIP"], [62, 54, 0, 44, 0, "CLIP"], [63, 53, 0, 45, 1, "VAE"], [64, 45, 0, 46, 0, "LATENT"], [65, 50, 0, 47, 0, "LATENT"], [66, 53, 0, 47, 1, "VAE"], [71, 43, 0, 50, 2, "CONDITIONING"], [72, 46, 0, 50, 3, "LATENT"], [91, 63, 0, 64, 0, "IMAGE"], [118, 44, 0, 79, 0, "CONDITIONING"], [119, 79, 0, 50, 1, "CONDITIONING"], [120, 31, 0, 46, 1, "MASK"], [126, 52, 0, 50, 0, "MODEL"], [128, 85, 0, 84, 1, "INT"], [132, 47, 0, 84, 0, "IMAGE"], [135, 85, 0, 86, 1, "INT"], [136, 85, 1, 86, 2, "INT"], [137, 84, 0, 86, 0, "IMAGE"], [139, 86, 0, 48, 1, "IMAGE"], [144, 62, 0, 91, 0, "*"], [145, 91, 0, 85, 0, "IMAGE"], [161, 48, 0, 97, 3, "IMAGE"], [162, 62, 1, 97, 2, "IMAGE_BOUNDS"], [163, 68, 0, 97, 1, "MASK"], [166, 64, 0, 28, 0, "MASK"], [182, 103, 0, 48, 0, "IMAGE"], [183, 103, 0, 45, 0, "IMAGE"], [201, 62, 0, 114, 0, "IMAGE"], [202, 64, 0, 114, 1, "MASK"], [203, 12, 0, 114, 2, "STRING"], [204, 15, 1, 114, 3, "FLOAT"], [205, 114, 0, 103, 0, "*"], [210, 116, 0, 117, 0, "*"], [212, 98, 0, 62, 0, "IMAGE"], [213, 98, 0, 118, 0, "*"], [214, 118, 0, 97, 0, "IMAGE"], [216, 128, 0, 98, 0, "*"], [220, 130, 0, 131, 0, "IMAGE"], [224, 130, 0, 63, 0, "IMAGE"], [226, 116, 0, 63, 1, "MASK"], [227, 116, 0, 62, 1, "MASK"], [228, 117, 0, 68, 0, "MASK"], [229, 97, 0, 132, 0, "IMAGE"], [231, 131, 0, 116, 0, "*"], [233, 139, 0, 114, 4, "BOOLEAN"], [234, 140, 0, 139, 0, "STRING"]], "nodes": [{"id": 43, "pos": [530, 670], "mode": 0, "size": [284.2975158691406, 76], "type": "CLIPTextEncode", "flags": {"collapsed": false}, "order": 12, "inputs": [{"link": 61, "name": "clip", "type": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [71], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 46, "pos": [930, 590], "mode": 0, "size": [176.39999389648438, 46], "type": "SetLatentNoiseMask", "flags": {}, "order": 35, "inputs": [{"link": 64, "name": "samples", "type": "LATENT"}, {"link": 120, "name": "mask", "type": "MASK"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [72], "slot_index": 0}], "properties": {"Node name for S&R": "SetLatentNoiseMask"}, "widgets_values": []}, {"id": 31, "pos": [920, 210], "mode": 0, "size": [210, 126], "type": "MaskComposite", "flags": {"collapsed": false}, "order": 33, "inputs": [{"link": 45, "name": "destination", "type": "MASK"}, {"link": 46, "name": "source", "type": "MASK"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [120], "slot_index": 0}], "properties": {"Node name for S&R": "MaskComposite"}, "widgets_values": [0, 0, "subtract"]}, {"id": 44, "pos": [880, 720], "mode": 0, "size": [221.6905059814453, 96], "type": "CLIPTextEncode", "color": "#222", "flags": {"collapsed": false}, "order": 13, "inputs": [{"link": 62, "name": "clip", "type": "CLIP"}], "bgcolor": "#000", "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [118], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 28, "pos": [110, 220], "mode": 0, "size": [210, 178], "type": "MaskFastGrow", "color": "#432", "flags": {}, "order": 27, "inputs": [{"link": 166, "name": "mask", "type": "MASK"}], "bgcolor": "#653", "outputs": [{"name": "MASK", "type": "MASK", "links": [43, 44], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 0, 0, 0, 1, true]}, {"id": 54, "pos": [120, 590], "mode": 0, "size": [315, 106], "type": "DualCLIPLoader", "flags": {}, "order": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [61, 62], "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 52, "pos": [560, 490], "mode": 0, "size": [315, 82], "type": "UNETLoader", "flags": {}, "order": 1, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [126], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "fp8_e5m2"]}, {"id": 79, "pos": [1140, 610], "mode": 0, "size": [211.6000061035156, 58], "type": "FluxGuidance", "flags": {}, "order": 18, "inputs": [{"link": 118, "name": "conditioning", "type": "CONDITIONING"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [119], "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 47, "pos": [1750, 620], "mode": 0, "size": [140, 46], "type": "VAEDecode", "flags": {}, "order": 37, "inputs": [{"link": 65, "name": "samples", "type": "LATENT"}, {"link": 66, "name": "vae", "type": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [132], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 84, "pos": [1570, 230], "mode": 0, "size": [284.6628112792969, 223.08346557617188], "type": "CR Upscale Image", "flags": {}, "order": 38, "inputs": [{"link": 132, "name": "image", "type": "IMAGE"}, {"link": 128, "name": "resize_width", "type": "INT", "widget": {"name": "resize_width"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [137], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, 1024, "bicubic", "true", 8]}, {"id": 53, "pos": [120, 770], "mode": 0, "size": [315, 58], "type": "VAELoader", "flags": {}, "order": 2, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [63, 66], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 30, "pos": [650, 240], "mode": 0, "size": [210, 178], "type": "MaskFastGrow", "color": "#432", "flags": {}, "order": 31, "inputs": [{"link": 44, "name": "mask", "type": "MASK"}], "bgcolor": "#653", "outputs": [{"name": "MASK", "type": "MASK", "links": [46], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, -5, 1, 0, 1, true]}, {"id": 29, "pos": [380, 220], "mode": 0, "size": [210, 178], "type": "MaskFastGrow", "color": "#432", "flags": {"collapsed": false}, "order": 30, "inputs": [{"link": 43, "name": "mask", "type": "MASK"}], "bgcolor": "#653", "outputs": [{"name": "MASK", "type": "MASK", "links": [45], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 30, 1, 0, 1, true]}, {"id": 50, "pos": [1420, 550], "mode": 0, "size": [283.63671875, 272.9201354980469], "type": "K<PERSON><PERSON><PERSON>", "flags": {}, "order": 36, "inputs": [{"link": 126, "name": "model", "type": "MODEL"}, {"link": 119, "name": "positive", "type": "CONDITIONING"}, {"link": 71, "name": "negative", "type": "CONDITIONING"}, {"link": 72, "name": "latent_image", "type": "LATENT"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [65], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [89245266737477, "randomize", 20, 1, "euler", "simple", 0.5]}, {"id": 48, "pos": [1980, 400], "mode": 0, "size": [210, 102], "type": "easy imageColorMatch", "flags": {"collapsed": false}, "order": 40, "inputs": [{"link": 182, "name": "image_ref", "type": "IMAGE"}, {"link": 139, "name": "image_target", "type": "IMAGE"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [161], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 45, "pos": [600, 860], "mode": 0, "size": [140, 46], "type": "VAEEncode", "flags": {}, "order": 34, "inputs": [{"link": 183, "name": "pixels", "type": "IMAGE"}, {"link": 63, "name": "vae", "type": "VAE"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [64], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 86, "pos": [1960, 200], "mode": 0, "size": [268.5074157714844, 131.08346557617188], "type": "ImageScale", "flags": {}, "order": 39, "inputs": [{"link": 137, "name": "image", "type": "IMAGE"}, {"link": 135, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 136, "name": "height", "type": "INT", "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [139], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 103, "pos": [-15.016511917114258, 791.7860717773438], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 32, "inputs": [{"link": 205, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [182, 183], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 91, "pos": [-18.009580612182617, 997.7781982421876], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 26, "inputs": [{"link": 144, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [145], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 85, "pos": [554.9732055664062, 992.04443359375], "mode": 0, "size": [214.20001220703125, 66], "type": "GetImageSize+", "flags": {}, "order": 29, "inputs": [{"link": 145, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "width", "type": "INT", "links": [128, 135], "slot_index": 0}, {"name": "height", "type": "INT", "links": [136], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 118, "pos": [1165.8670654296875, 355.2380065917969], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 20, "inputs": [{"link": 213, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [214], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 63, "pos": [-1152.9075927734375, 739.1576538085938], "mode": 0, "size": [352.79998779296875, 150], "type": "Bounded Image Crop with Mask", "flags": {}, "order": 22, "inputs": [{"link": 224, "name": "image", "type": "IMAGE"}, {"link": 226, "name": "mask", "type": "MASK"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [91], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": null}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 62, "pos": [-1151.197265625, 413.9660949707031], "mode": 0, "size": [352.79998779296875, 150], "type": "Bounded Image Crop with Mask", "flags": {}, "order": 23, "inputs": [{"link": 212, "name": "image", "type": "IMAGE"}, {"link": 227, "name": "mask", "type": "MASK"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [144, 201], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [162], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 64, "pos": [-626.2295532226562, 605.3793334960938], "mode": 0, "size": [210, 58], "type": "ImageToMask", "flags": {}, "order": 25, "inputs": [{"link": 91, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [166, 202], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 68, "pos": [1980, 560], "mode": 0, "size": [210, 178], "type": "MaskFastGrow", "color": "#432", "flags": {}, "order": 24, "inputs": [{"link": 228, "name": "mask", "type": "MASK"}], "bgcolor": "#653", "outputs": [{"name": "MASK", "type": "MASK", "links": [163], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 30, 2, 0, 1, true]}, {"id": 97, "pos": [2340, 550], "mode": 0, "size": [243.6000061035156, 175.1684112548828], "type": "Bounded Image Blend with Mask", "flags": {}, "order": 41, "inputs": [{"link": 214, "name": "target", "type": "IMAGE"}, {"link": 163, "name": "target_mask", "type": "MASK"}, {"link": 162, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 161, "name": "source", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [229], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 16]}, {"id": 117, "pos": [1456.61181640625, 962.9002075195312], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 21, "title": "mask", "inputs": [{"link": 210, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "MASK", "links": [228], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 134, "pos": [-2151.40185546875, 503.8933715820313], "mode": 0, "size": [255.50064086914065, 114.39942932128906], "type": "Note", "color": "#432", "flags": {}, "order": 3, "inputs": [], "bgcolor": "#653", "outputs": [], "properties": {}, "widgets_values": ["mask图的完整路径，如\n/home/<USER>/aigc/ComfyUI/input/temp_nxnkr_00001_.png"]}, {"id": 132, "pos": [2693.47216796875, 549.0928955078125], "mode": 0, "size": [315, 274], "type": "ConrainImageSave", "flags": {}, "order": 42, "inputs": [{"link": 229, "name": "images", "type": "IMAGE"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 116, "pos": [-1268.6549072265625, 594.9894409179688], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 19, "inputs": [{"link": 231, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "MASK", "links": [210, 226, 227], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 130, "pos": [-1814.455810546875, 517.2516479492188], "mode": 0, "size": [433.8365173339844, 110.00152587890624], "type": "JWLoadImagesFromString", "flags": {}, "order": 4, "title": "mask图Load Images From String", "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [220, 224], "slot_index": 0}], "properties": {"Node name for S&R": "JWLoadImagesFromString"}, "widgets_values": ["${maskImagePath}", "false"]}, {"id": 131, "pos": [-1709.1844482421875, 703.231201171875], "mode": 0, "size": [315, 58], "type": "ConrainImageToMask", "flags": {}, "order": 14, "inputs": [{"link": 220, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [231], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainImageToMask"}, "widgets_values": ["intensity"]}, {"id": 98, "pos": [-1287.2313232421875, 295.3448486328125], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 15, "inputs": [{"link": 216, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [212, 213], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 133, "pos": [-2152.8056640625, 267.88531494140625], "mode": 0, "size": [253.06741333007812, 114.00948333740234], "type": "Note", "color": "#432", "flags": {}, "order": 5, "inputs": [], "bgcolor": "#653", "outputs": [], "properties": {}, "widgets_values": ["原图的完整路径，如 /home/<USER>/aigc/ComfyUI/input/image (1626).png"]}, {"id": 128, "pos": [-1820.5096435546875, 296.512939453125], "mode": 0, "size": [421.65380859375, 128.3952178955078], "type": "JWLoadImagesFromString", "flags": {}, "order": 6, "title": "原图Load Images From String", "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [216], "slot_index": 0}], "properties": {"Node name for S&R": "JWLoadImagesFromString"}, "widgets_values": ["${originImagePath}", "false"]}, {"id": 12, "pos": [-1802.744140625, 827.3170166015625], "mode": 0, "size": [210, 58], "type": "easy string", "flags": {}, "order": 7, "title": "色值", "inputs": [], "outputs": [{"name": "string", "type": "STRING", "links": [203], "slot_index": 0}], "properties": {"Node name for S&R": "easy string"}, "widgets_values": ["${targetHexColor}"]}, {"id": 14, "pos": [-1804.3363037109375, 957.2620849609376], "mode": 0, "size": [210, 58], "type": "easy string", "flags": {}, "order": 8, "title": "texture_preserve", "inputs": [], "outputs": [{"name": "string", "type": "STRING", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "easy string"}, "widgets_values": ["0.7"]}, {"id": 15, "pos": [-1536.394775390625, 855.6756591796875], "mode": 0, "size": [315, 122], "type": "CR String To Number", "flags": {"collapsed": false}, "order": 16, "inputs": [{"link": 15, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": null}, {"name": "FLOAT", "type": "FLOAT", "links": [204], "slot_index": 1}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR String To Number"}, "widgets_values": ["text", "round"]}, {"id": 135, "pos": [-2150.529296875, 807.60009765625], "mode": 0, "size": [250.4833984375, 95.9993896484375], "type": "Note", "color": "#432", "flags": {}, "order": 9, "inputs": [], "bgcolor": "#653", "outputs": [], "properties": {}, "widgets_values": ["要换色的色值，如\n#ae7979"]}, {"id": 141, "pos": [-2134.20556640625, 1056.4227294921875], "mode": 0, "size": [229.2378387451172, 78.86479949951172], "type": "Note", "color": "#432", "flags": {}, "order": 10, "inputs": [], "bgcolor": "#653", "outputs": [], "properties": {}, "widgets_values": ["是否开启智能换色模式，如\ntrue"]}, {"id": 140, "pos": [-1806.968017578125, 1073.401123046875], "mode": 0, "size": [315, 58], "type": "easy string", "flags": {}, "order": 11, "inputs": [], "outputs": [{"name": "string", "type": "STRING", "links": [234], "slot_index": 0}], "properties": {"Node name for S&R": "easy string"}, "widgets_values": ["${smartRecolorMode}"]}, {"id": 114, "pos": [-667.603515625, 961.822998046875], "mode": 0, "size": [291.2896423339844, 124.77789306640624], "type": "ConrainRecolor", "flags": {}, "order": 28, "inputs": [{"link": 201, "name": "images", "type": "IMAGE"}, {"link": 202, "name": "mask", "type": "MASK"}, {"link": 203, "name": "target_color", "type": "STRING", "widget": {"name": "target_color"}}, {"link": 204, "name": "texture_preserve", "type": "FLOAT", "shape": 7, "widget": {"name": "texture_preserve"}}, {"link": 233, "name": "smart_color_adjust", "type": "BOOLEAN", "shape": 7, "widget": {"name": "smart_color_adjust"}}], "outputs": [{"name": "recolored_img", "type": "IMAGE", "links": [205], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainRecolor"}, "widgets_values": ["", 0.7, true]}, {"id": 139, "pos": [-1452.0703125, 1046.363037109375], "mode": 0, "size": [315, 78], "type": "CR String To <PERSON>an", "flags": {}, "order": 17, "inputs": [{"link": 234, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"name": "BOOLEAN", "type": "BOOLEAN", "links": [233], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR String To <PERSON>an"}, "widgets_values": [""]}], "config": {}, "groups": [{"id": 2, "color": "#3f789e", "flags": {}, "title": "精修边缘", "bounding": [70, 120, 3030.8408203125, 1043.81787109375], "font_size": 24}, {"id": 3, "color": "#3f789e", "flags": {}, "title": "换色", "bounding": [-1836.9322509765625, 202.9395446777344, 1832.8260498046875, 936.3485717773438], "font_size": 24}], "version": 0.4, "last_link_id": 234, "last_node_id": 141, "seed_widgets": {"50": 0}}}}}
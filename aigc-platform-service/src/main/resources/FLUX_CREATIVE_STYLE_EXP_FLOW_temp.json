{"client_id": "${clientId}", "prompt": {"152": {"_meta": {"title": "导入人脸图片"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo.faceImage}", "upload": "image"}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "209": {"_meta": {"title": "导入换脸模型"}, "class_type": "LoadConrainReactorModels", "inputs": {"face_restore_model": "GFPGANv1.4.pth", "facedetection_model": "retinaface_resnet50", "parse_model": "parsenet", "swap_model": "inswapper_128.onnx"}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "210": {"_meta": {"title": "换脸"}, "class_type": "ConrainReActorFaceSwap", "inputs": {"codeformer_weight": "0.7", "console_log_level": 1, "detect_gender_input": "no", "detect_gender_source": "no", "enabled": true, "face_restore_model": ["209", 2], "face_restore_visibility": "${FACE.extInfo.faceRestoreVisibility}", "facedetection": ["209", 1], "faceparse_model": ["209", 3], "input_faces_index": "0", "input_image": ["${(!isNeedRepair)?then(269,isFluxRepair?then(299,328))}", 0], "keep_largest": "yes", "source_faces_index": "0", "source_image": ["152", 0], "swap_model": ["209", 0]}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "216": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["${(isLoraFace&&!isLoraSwapFace)?then(((!isNeedRepair)?then(269,isFluxRepair?then(299,328))),isBackView?then((!isNeedRepair)?then(269,isFluxRepair?then(299,328)),210))}", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}, "class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": ["367", 0]}, "disable": "${isPureBg?then('false','true')}"}, "268": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "${baseModelName!'flux1-dev.safetensors'}", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["268", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||(isForcePW!false))?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["282", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,377)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "299": {"_meta": {"title": "FaceDetailer (pipe)"}, "class_type": "FaceDetailerPipe", "inputs": {"bbox_crop_factor": "${faceDetailerCropFactor!3}", "bbox_dilation": 2, "bbox_threshold": 0.5, "cfg": "${faceCfg}", "cycle": 1, "denoise": "${(repairFaceDenoise?trim)!0.55}", "detailer_pipe": ["300", 0], "drop_size": 40, "feather": 3, "force_inpaint": false, "guide_size": 512, "guide_size_for": true, "image": ["269", 0], "inpaint_model": 0, "max_size": "${faceDetailerMaxSize!1024}", "noise_mask": true, "noise_mask_feather": false, "refiner_ratio": 0.2, "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "beta", "seed": "${faceSeed}", "steps": 10}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "300": {"_meta": {"title": "BasicPipe -> DetailerPipe"}, "class_type": "BasicPipeToDetailerPipe", "inputs": {"Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["301", 0], "bbox_detector": ["303", 0], "wildcard": ""}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "301": {"_meta": {"title": "ToBasicPipe"}, "class_type": "ToBasicPipe", "inputs": {"clip": ["296", 1], "model": ["380", 0], "negative": ["304", 0], "positive": ["302", 0], "vae": ["270", 0]}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "302": {"_meta": {"title": "修脸prompt"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["331", 0]}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "303": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "304": {"_meta": {"title": "Negative Cond Placeholder"}, "class_type": "ImpactNegativeConditioningPlaceholder", "inputs": {}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "316": {"_meta": {"title": "conrain image composite masked"}, "class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "324": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "325": {"_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}, "class_type": "SAMLoader", "inputs": {"device_mode": "Prefer GPU", "model_name": "sam_vit_b_01ec64.pth"}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "326": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["329", 1], "text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "327": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["329", 1], "text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "328": {"_meta": {"title": "FaceDetailer"}, "class_type": "FaceDetailer", "inputs": {"bbox_crop_factor": 3, "bbox_detector": ["324", 0], "bbox_dilation": 500, "bbox_threshold": 0.5, "cfg": "${faceCfg}", "clip": ["329", 1], "cycle": 1, "denoise": 0.4, "drop_size": 40, "feather": 5, "force_inpaint": true, "guide_size": 384, "guide_size_for": true, "image": ["269", 0], "inpaint_model": 1, "max_size": 512, "model": ["329", 0], "negative": ["327", 0], "noise_mask": true, "noise_mask_feather": 0, "positive": ["326", 0], "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_model_opt": ["325", 0], "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "normal", "seed": "${faceSeed}", "steps": 8, "vae": ["329", 2], "wildcard": ""}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "329": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "disable": "${(!isNeedRepair||isFluxRepair)?then('true','false')}"}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,(isLoraFace&&!isLoraSwapFace)?then((!isNeedRepair)?then(269,isFluxRepair?then(299,328)),isBackView?then((!isNeedRepair)?then(269,isFluxRepair?then(299,328)),210)))}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["${(isLoraFace&&!isLoraSwapFace)?then((!isNeedRepair)?then(269,isFluxRepair?then(299,328)),isBackView?then((!isNeedRepair)?then(269,isFluxRepair?then(299,328)),210))}", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "369": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["374", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${(isLoraFace&&isFaceAfter&&isPWModel)?then('false','true')}"}, "371": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["${isPWModel?then(369,296)}", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}, "disable": "${(isLoraFace&&isFaceAfter)?then('false','true')}"}, "372": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": 2, "model1": ["371", 0], "model2": ["${(isLoraFace&&isFaceAfter&&isPWModel)?then(369,272)}", 0]}, "disable": "${(isLoraFace&&isFaceAfter)?then('false','true')}"}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "374": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["272", 0], "strength_clip": 1, "strength_model": "${FACE.extInfo.faceAfterStrength!1}"}, "disable": "${(isLoraFace&&isFaceAfter)?then('false','true')}"}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "378": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["296", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${((!isFaceAfter&&isPWModel)||(isForcePW!false))?then('false','true')}"}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "380": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["381", 0], "model1": ["${(isLoraFace&&isFaceAfter)?then(372,'272')}", 0], "model2": ["296", 0]}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}, "381": {"_meta": {"title": "模特后置开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${(isLoraFace&&isFaceAfter)?then(1,2)}"}, "disable": "${(isNeedRepair&&isFluxRepair)?then('false','true')}"}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [-2139.907780635149, 3547.5423110244756], "scale": 0.20992832109438067}}, "groups": [{"id": 1, "bounding": [4431.9775390625, -1752.8297119140625, 1069, 1391], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 2, "bounding": [2992, -734, 1347, 1200], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "1.5修脸"}, {"id": 3, "bounding": [5626.828125, -1745.7041015625, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 4, "bounding": [2984, -1753, 1350, 956], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "修脸换脸"}, {"id": 5, "bounding": [631.6390380859375, -1742.131591796875, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [5624.35009765625, -1042.7269287109375, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}], "last_link_id": 665, "last_node_id": 386, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [342, 209, 0, 210, 1, "FACE_MODEL"], [343, 209, 1, 210, 2, "FACE_MODEL"], [344, 209, 2, 210, 3, "FACE_MODEL"], [345, 209, 3, 210, 4, "FACE_MODEL"], [346, 152, 0, 210, 5, "IMAGE"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [503, 300, 0, 299, 1, "DETAILER_PIPE"], [504, 301, 0, 300, 0, "BASIC_PIPE"], [505, 303, 0, 300, 1, "BBOX_DETECTOR"], [508, 302, 0, 301, 3, "CONDITIONING"], [509, 304, 0, 301, 4, "CONDITIONING"], [513, 296, 1, 268, 0, "CLIP"], [516, 210, 0, 236, 0, "IMAGE"], [517, 299, 0, 210, 0, "IMAGE"], [520, 270, 0, 301, 2, "VAE"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [545, 329, 1, 326, 0, "CLIP"], [546, 329, 1, 327, 0, "CLIP"], [547, 329, 0, 328, 1, "MODEL"], [548, 329, 1, 328, 2, "CLIP"], [549, 329, 2, 328, 3, "VAE"], [550, 326, 0, 328, 4, "CONDITIONING"], [551, 327, 0, 328, 5, "CONDITIONING"], [552, 324, 0, 328, 6, "BBOX_DETECTOR"], [553, 325, 0, 328, 7, "SAM_MODEL"], [554, 331, 0, 302, 1, "STRING"], [555, 331, 0, 326, 1, "STRING"], [557, 328, 0, 299, 0, "IMAGE"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [603, 355, 0, 216, 0, "IMAGE"], [611, 269, 0, 328, 0, "IMAGE"], [612, 210, 0, 365, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [619, 369, 0, 301, 0, "MODEL"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [630, 296, 1, 301, 1, "CLIP"], [631, 296, 1, 302, 0, "CLIP"], [632, 371, 0, 372, 0, "MODEL"], [633, 369, 0, 371, 0, "MODEL"], [638, 272, 0, 374, 0, "MODEL"], [639, 271, 0, 374, 1, "CLIP"], [641, 374, 0, 369, 0, "MODEL"], [642, 375, 0, 377, 0, "MODEL"], [643, 369, 0, 372, 1, "MODEL"], [644, 373, 0, 377, 2, "INT"], [645, 272, 0, 377, 1, "MODEL"], [646, 377, 0, 297, 0, "MODEL"], [647, 272, 0, 375, 0, "MODEL"], [650, 296, 0, 378, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [653, 381, 0, 380, 2, "INT"], [654, 296, 0, 380, 1, "MODEL"], [656, 379, 0, 382, 0, "*"], [657, 382, 0, 369, 1, "MODEL"], [658, 372, 0, 383, 0, "*"], [659, 383, 0, 380, 0, "MODEL"], [660, 380, 0, 384, 0, "*"], [661, 384, 0, 301, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [665, 386, 0, 297, 1, "CLIP"]], "nodes": [{"id": 152, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 0, "outputs": [{"label": "IMAGE", "links": [346], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "name": "MASK", "shape": 3, "type": "MASK"}], "pos": [4503, -965], "properties": {"Node name for S&R": "LoadImage"}, "size": [320, 314], "title": "导入人脸图片", "type": "LoadImage", "widgets_values": ["${FACE.extInfo.faceImage}", "image"]}, {"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [6215.19775390625, -780.4785766601562], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 49, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7342.19775390625, -634.4783935546875], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [5842.15087890625, -567.2930297851562], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [5820.15087890625, -761.2932739257812], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 209, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 4, "outputs": [{"label": "faceswapper_model", "links": [342], "name": "faceswapper_model", "shape": 3, "slot_index": 0, "type": "FACE_MODEL"}, {"label": "facedetection_model", "links": [343], "name": "facedetection_model", "shape": 3, "slot_index": 1, "type": "FACE_MODEL"}, {"label": "facerestore_model", "links": [344], "name": "facerestore_model", "shape": 3, "slot_index": 2, "type": "FACE_MODEL"}, {"label": "faceparse_model", "links": [345], "name": "faceparse_model", "shape": 3, "slot_index": 3, "type": "FACE_MODEL"}], "pos": [4492, -1416], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "size": [324.5391845703125, 190], "title": "导入换脸模型", "type": "LoadConrainReactorModels", "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 210, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "input_image", "link": 517, "name": "input_image", "type": "IMAGE"}, {"label": "swap_model", "link": 342, "name": "swap_model", "type": "FACE_MODEL"}, {"label": "facedetection", "link": 343, "name": "facedetection", "type": "FACE_MODEL"}, {"label": "face_restore_model", "link": 344, "name": "face_restore_model", "type": "FACE_MODEL"}, {"label": "faceparse_model", "link": 345, "name": "faceparse_model", "type": "FACE_MODEL"}, {"label": "source_image", "link": 346, "name": "source_image", "shape": 7, "type": "IMAGE"}, {"label": "face_model", "name": "face_model", "shape": 7, "type": "FACE_MODEL"}], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 76, "outputs": [{"label": "IMAGE", "links": [516, 612], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "FACE_MODEL", "name": "FACE_MODEL", "shape": 3, "type": "FACE_MODEL"}], "pos": [5031, -1425], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "size": [367.79998779296875, 370], "title": "换脸", "type": "ConrainReActorFaceSwap", "widgets_values": [true, "${FACE.extInfo.faceRestoreVisibility}", "0.7", "no", "no", "0", "0", 1, "yes"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 603, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 82, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [7899, -940], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 33, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6697.27783203125, -813.4215087890625], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6208.19775390625, -909.4784545898438], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 79, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [6200, -1323], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 516, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 77, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [5706, -1450], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [], "pos": [6630, -1643], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 83, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6655, -1462], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 81, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6641, -1245], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 84, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [6206, -1548], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 85, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7965, -1567], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 86, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [7651, -1455], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 268, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 513, "name": "clip", "type": "CLIP"}, {"link": 453, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 62, "outputs": [{"links": [464], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1356.314453125, -1442.6668701171875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 73, "outputs": [{"links": [611, 611], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2618, -1436], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [455, 520], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2319.339111328125, -1210.8780517578125], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [622, 639, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-974.3479614257812, -943.7987060546875], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [621, 638, 645, 647], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-969.5924682617188, -1139.4793701171875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["${baseModelName!'flux1-dev.safetensors'}", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 72, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2306.54931640625, -1559.1309814453125], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 112.51068878173828], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [2001, -1191.7747802734375], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 69, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [1992.************, -1071.4217529296875], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 70, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [1984.70556640625, -1433.122314453125], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [1854.52099609375, -1600.************], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 464, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 66, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [1690.1234130859375, -1421.2938232421875], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 57, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1709.2464599609375, -1186.37353515625], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 65, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1692.301513671875, -937.4694213867188], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [472, 473], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [779.6383056640625, -1020.9454956054688], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 472, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 37, "outputs": [{"links": [453], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1304.6009521484375, -1079.596923828125], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 226], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A deep grey t-shirt, The t-shirt has a round neckline and short sleeves.The model is wearing  black wide-leg pants underneath. The model is wearing black chunky shoes. The black chunky shoes have a fashionable style. \n\n\nlinrun2010,a model stands against a plain white background with even lighting.standing still.standing with legs slightly apart, left hand in pocket, right hand relaxed by side.\n\n\nThis is a 23 year old European female model,The model wore lipstick the color of bean paste, sweet and cute face,smile,The model has natural orange-brown long wavy hair, voluminous and layered."]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1336.303466796875, -556.4685668945312], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1322.303466796875, -742.4697875976562], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [789.3619995117188, -736.4147338867188], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 473, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 38, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2526.195556640625, -891.2860107421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 41, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2491.195556640625, -760.2861328125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 59, "outputs": [{"links": [514, 650, 654], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [513, 510, 513, 630, 631], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [749.9860229492188, -1247.851806640625], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 646, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 51, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [754.6295166015625, -1641.997314453125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [489.4413757324219, 126], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 56, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [754.8997192382812, -1445.91943359375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 299, "flags": {}, "inputs": [{"link": 557, "name": "image", "type": "IMAGE"}, {"link": 503, "name": "detailer_pipe", "type": "DETAILER_PIPE"}, {"name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC"}], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 75, "outputs": [{"links": [517], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE"}, {"links": [], "name": "mask", "shape": 3, "slot_index": 3, "type": "MASK"}, {"name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": [3954.708984375, -1634.5810546875], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "size": [346, 782], "type": "FaceDetailerPipe", "widgets_values": [512, true, "${faceDetailerMaxSize!1024}", "${faceSeed}", "fixed", 10, "${faceCfg}", "euler", "beta", "${(repairFaceDenoise?trim)!0.55}", 3, true, false, 0.5, 2, "${faceDetailerCropFactor!3}", "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 300, "flags": {}, "inputs": [{"link": 504, "name": "basic_pipe", "type": "BASIC_PIPE"}, {"link": 505, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}, {"name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK"}], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 71, "outputs": [{"links": [503], "name": "detailer_pipe", "shape": 3, "slot_index": 0, "type": "DETAILER_PIPE"}], "pos": [3642, -1629], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "size": [262, 204.*************], "type": "BasicPipeToDetailerPipe", "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 301, "flags": {}, "inputs": [{"link": 661, "name": "model", "type": "MODEL"}, {"link": 630, "name": "clip", "type": "CLIP"}, {"link": 520, "name": "vae", "type": "VAE"}, {"link": 508, "name": "positive", "type": "CONDITIONING"}, {"link": 509, "name": "negative", "type": "CONDITIONING"}], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 67, "outputs": [{"links": [504], "name": "basic_pipe", "shape": 3, "slot_index": 0, "type": "BASIC_PIPE"}], "pos": [3341, -1629], "properties": {"Node name for S&R": "ToBasicPipe"}, "size": [241.79998779296875, 106], "type": "ToBasicPipe", "widgets_values": []}, {"id": 302, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 631, "name": "clip", "type": "CLIP"}, {"link": 554, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 63, "outputs": [{"links": [508], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [3033, -1410], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 116.85224914550781], "title": "修脸prompt", "type": "CLIPTextEncode", "widgets_values": ["${FACE.extTags}"]}, {"id": 303, "flags": {}, "inputs": [], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 16, "outputs": [{"links": [505], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "shape": 3, "type": "SEGM_DETECTOR"}], "pos": [3316, -1018], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [226.8000030517578, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 304, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 17, "outputs": [{"links": [509], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3014, -1119], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "size": [210, 26], "type": "ImpactNegativeConditioningPlaceholder", "widgets_values": []}, {"id": 306, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [], "pos": [3314, -1446], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"]}, {"id": 307, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [], "pos": [1326.650146484375, -1662.**********], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["去模糊lora节点\n纯色背景流程：关闭\n其他流程：打开"]}, {"id": 308, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 20, "outputs": [], "pos": [4546.26318359375, -1586.704345703125], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 309, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [], "pos": [5000.97509765625, -1611.552001953125], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 310, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 22, "outputs": [], "pos": [5739.2734375, -1633.447998046875], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 87, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7073, -1369], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 319, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 23, "outputs": [], "pos": [4536.26318359375, -1155.704345703125], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 54, "outputs": [], "pos": [7913.19775390625, -562.4788208007812], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 324, "flags": {}, "inputs": [], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 24, "outputs": [{"label": "BBOX_DETECTOR", "links": [552], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [3061, 99], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 325, "flags": {}, "inputs": [], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 25, "outputs": [{"label": "SAM_MODEL", "links": [553], "name": "SAM_MODEL", "shape": 3, "slot_index": 0, "type": "SAM_MODEL"}], "pos": [3094, 253], "properties": {"Node name for S&R": "SAMLoader"}, "size": [315, 82], "type": "SAMLoader", "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 326, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"label": "clip", "link": 545, "name": "clip", "type": "CLIP"}, {"link": 555, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 43, "outputs": [{"label": "CONDITIONING", "links": [550], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3039, -427], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [389.95330810546875, 157.**********4062], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["", "none", "A1111"]}, {"id": 327, "flags": {}, "inputs": [{"label": "clip", "link": 546, "name": "clip", "type": "CLIP"}], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 42, "outputs": [{"label": "CONDITIONING", "links": [551], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3061, -177], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [400, 200], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 328, "flags": {}, "inputs": [{"label": "image", "link": 611, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 547, "name": "model", "type": "MODEL"}, {"label": "clip", "link": 548, "name": "clip", "type": "CLIP"}, {"label": "vae", "link": 549, "name": "vae", "type": "VAE"}, {"label": "positive", "link": 550, "name": "positive", "type": "CONDITIONING"}, {"label": "negative", "link": 551, "name": "negative", "slot_index": 5, "type": "CONDITIONING"}, {"label": "bbox_detector", "link": 552, "name": "bbox_detector", "slot_index": 6, "type": "BBOX_DETECTOR"}, {"label": "sam_model_opt", "link": 553, "name": "sam_model_opt", "shape": 7, "slot_index": 7, "type": "SAM_MODEL"}, {"label": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "slot_index": 8, "type": "SEGM_DETECTOR"}, {"label": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK"}, {"name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC"}], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 74, "outputs": [{"label": "image", "links": [557], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "cropped_refined", "links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"label": "cropped_enhanced_alpha", "links": [], "name": "cropped_enhanced_alpha", "shape": 6, "slot_index": 2, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 3, "type": "MASK"}, {"label": "detailer_pipe", "name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"label": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": [3800, -600], "properties": {"Node name for S&R": "FaceDetailer"}, "size": [350.5302734375, 902.3991088867188], "type": "FaceDetailer", "widgets_values": [384, true, 512, "${faceSeed}", "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 329, "flags": {}, "inputs": [], "mode": "${(!isNeedRepair||isFluxRepair)?then(4,0)}", "order": 26, "outputs": [{"label": "MODEL", "links": [547], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "CLIP", "links": [545, 546, 548], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}, {"label": "VAE", "links": [549], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE"}], "pos": [3033, -615], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [315, 98], "type": "CheckpointLoaderSimple", "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [554, 555, 575], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2518.53955078125, -1076.130859375], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 332, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [], "pos": [3705, -1348], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"]}, {"id": 333, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [], "pos": [3524, 40], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 40, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1669.60546875, -666.1307373046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 39, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1692.6005859375, -565.1295776367188], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 48, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1901.601806640625, -688.1304931640625], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 53, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2151.55419921875, -692.1304931640625], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 47, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1972.60302734375, -558.1295776367188], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 52, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2205.5419921875, -537.129638671875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 88, "outputs": [{"links": [603], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7384.19775390625, -937.478515625], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 612, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 78, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [7088.2734375, -1607.447998046875], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 80, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [7382.2734375, -1532.447998046875], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 369, "flags": {"collapsed": true}, "inputs": [{"link": 641, "name": "model1", "type": "MODEL"}, {"link": 657, "name": "model2", "type": "MODEL"}], "mode": "${(isLoraFace&&isFaceAfter&&isPWModel)?then(0,4)}", "order": 50, "outputs": [{"links": [619, 633, 643], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-64.76937866210938, -1602.9716796875], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 371, "flags": {}, "inputs": [{"link": 633, "name": "model", "type": "MODEL"}], "mode": "${(isLoraFace&&isFaceAfter)?then(0,4)}", "order": 55, "outputs": [{"links": [632], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-271.2709045410156, -1294.364501953125], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 372, "flags": {}, "inputs": [{"link": 632, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 643, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 636, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": "${(isLoraFace&&isFaceAfter)?then(0,4)}", "order": 58, "outputs": [{"links": [658, 635], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [150.6240997314453, -1296.3515625], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [242.97930908203125, 78], "type": "CR Model Input Switch", "widgets_values": ["2"]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 32, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-164.47792053222656, -939.6198120117188], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 374, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 638, "name": "model", "type": "MODEL"}, {"link": 639, "name": "clip", "type": "CLIP"}], "mode": "${(isLoraFace&&isFaceAfter)?then(0,4)}", "order": 35, "outputs": [{"links": [641], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [-504.43853759765625, -1522.910400390625], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [395.2306213378906, 126.47626495361328], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceAfterStrength!1}", 1]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 36, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-483.5779724121094, -705.3702392578125], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 645, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 46, "outputs": [{"links": [646], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [170.24545288085938, -701.9577026367188], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 650, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||(isForcePW!false))?then(0,4)}", "order": 61, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [283.6439208984375, -1048.404052734375], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 30, "outputs": [{"links": [656, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-971.5514526367188, -1367.3934326171875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 380, "flags": {}, "inputs": [{"link": 659, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 654, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 653, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 64, "outputs": [{"links": [660], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [2002.4271240234375, -1845.9454345703125], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [315, 78], "type": "CR Model Input Switch", "widgets_values": ["${(isLoraFace&&isFaceAfter)?then(1,2)}"]}, {"id": 381, "flags": {"collapsed": false}, "inputs": [], "mode": "${(isNeedRepair&&isFluxRepair)?then(0,4)}", "order": 31, "outputs": [{"links": [653], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [1568.322265625, -1807.3868408203125], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "模特后置开关", "type": "JWStringToInteger", "widgets_values": ["${(isLoraFace&&isFaceAfter)?then(1,2)}"]}, {"id": 382, "flags": {}, "inputs": [{"link": 656, "name": "", "type": "*"}], "mode": 0, "order": 44, "outputs": [{"links": [657], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-495.542724609375, -1627.7763671875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 383, "flags": {}, "inputs": [{"link": 658, "name": "", "type": "*"}], "mode": 0, "order": 60, "outputs": [{"links": [659], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [612.795654296875, -1851.9267578125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 384, "flags": {}, "inputs": [{"link": 660, "name": "", "type": "*"}], "mode": 0, "order": 68, "outputs": [{"links": [661], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [3162.654541015625, -1830.18701171875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 45, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-474.9272766113281, -1071.6295166015625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 34, "outputs": [{"links": [665], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [415.603759765625, -950.938232421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "299": 3, "328": 3, "331": 1}, "version": 0.4}}}}
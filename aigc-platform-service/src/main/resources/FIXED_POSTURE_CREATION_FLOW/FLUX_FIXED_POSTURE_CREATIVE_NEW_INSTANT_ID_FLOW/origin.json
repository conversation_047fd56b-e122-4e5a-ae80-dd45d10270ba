{"client_id": "05983328289343f2b9c3dd6eb95c5fdd", "prompt": {"177": {"inputs": {"text": "product/20250530/1/312147", "text_b": "product_2127841", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "beta", "steps": "30", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "277": {"inputs": {"noise_seed": "704516429941238"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["645", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["296", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), side view, (full-body portrait:1.2), full body, The model is wearing A navy blue jacket top and the inner layer of the jacket is black and beige wide-leg pants set, The jacket is hip-length with a round neckline, long sleeves, and large silver buttons down the front without any visible logos or patterns. The fabric of this jacket appears to be lightweight and slightly sheer, with a subtle sheen that suggests it may be made from a silk blend or a fine synthetic material.  The pants are loose-fitting, with a wide leg cut and an ankle-length hem, made from a light, textured fabric. ,buttoned.20-year-old European female model, is wearing {black sleeveless top} inside. 20-year-old European female model, is wearing sneakers. The  sneakers feature solid color and simple. \n\n\nA softly lit studio with neutral, diffused lighting that creates gentle shadows and a balanced, open atmosphere. The background is a soft, light beige or pale grey, with clean lines and minimalistic walls that subtly reflect the light. The light is directional but softly filtered, coming from one side and casting natural shadows on the walls and floor, enhancing the depth of the space. The overall effect is calm, elegant, and spacious, with a focus on creating a sense of openness and subtle contrast between light and shadow without overwhelming warmth.{20-year-old European female model stands casually with hands in his pockets and his legs bent.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model is walking with one hand in pocket and one hand naturally perpendicular to side.|20-year-old European female model is walking with hands in his pockets.|20-year-old European female model is walking with hands hanging down naturally.},\n\n\na mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", "seed": "849333078228669"}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": "1785"}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": "1340"}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "online_product/女士中式大龄开衫_19429_20250521_103121/女士中式大龄开衫_19429_20250521_103121-flux/女士中式大龄开衫_19429_20250521_103121-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "online_product/1808第二批_颜色3_22727_20250530_164444/1808第二批_颜色3_22727_20250530_164444-flux/1808第二批_颜色3_22727_20250530_164444-flux.safetensors", "strength_model": "0.3", "strength_clip": "0.3", "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "331": {"inputs": {"prompts": "a mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["608", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["608", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "373": {"inputs": {"text": "2"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "389": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "390": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "391": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "392": {"inputs": {"noise_mask": true, "positive": ["618", 0], "negative": ["618", 1], "vae": ["416", 2], "pixels": ["614", 0], "mask": ["402", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "397": {"inputs": {"text": ["331", 0], "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "398": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "399": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["436", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "402": {"inputs": {"invert_mask": false, "grow": 32, "blur": 4, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["624", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "403": {"inputs": {"invert_mask": false, "grow": 72, "blur": 40, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["414", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "409": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["414", 0], "image_target": ["413", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "410": {"inputs": {"images": ["602", 0], "face": ["399", 0], "crop": ["409", 0], "mask": ["403", 0], "warp": ["414", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "411": {"inputs": {"seed": "704516429941238", "steps": 5, "cfg": 1, "sampler_name": "euler", "scheduler": "kl_optimal", "denoise": 0.6, "model": ["412", 0], "positive": ["392", 0], "negative": ["392", 1], "latent_image": ["392", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "412": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["389", 0], "insightface": ["390", 0], "control_net": ["391", 0], "image": ["446", 0], "model": ["416", 0], "positive": ["397", 0], "negative": ["398", 0], "image_kps": ["614", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "413": {"inputs": {"samples": ["411", 0], "vae": ["416", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "414": {"inputs": {"crop_size": 1024, "crop_factor": 1.5, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["399", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "416": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "436": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["602", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "446": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["447", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "447": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["592", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "462": {"inputs": {"image": ".jpg.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "474": {"inputs": {"Input": ["631", 0], "image1": ["475", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "475": {"inputs": {"image": ".jpg.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "476": {"inputs": {"image": ".jpg.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "477": {"inputs": {"Input": ["632", 0], "image1": ["476", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "559": {"inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}, "class_type": "ImpactMakeImageList", "_meta": {"title": "Make Image List"}}, "561": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "upscale if smaller", "multiple_of": 0, "image": ["559", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "570": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["610", 0], "image": ["561", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "574": {"inputs": {"segs": ["597", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "576": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["561", 0], "mask": ["580", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "580": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["574", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "592": {"inputs": {"images": ["576", 0]}, "class_type": "easy imageListToImageBatch", "_meta": {"title": "Image List To Image Batch"}}, "597": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["570", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "600": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["610", 0], "image": ["269", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "601": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["600", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "602": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["269", 0], "mask": ["605", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "603": {"inputs": {"segs": ["601", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "605": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["603", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "608": {"inputs": {"blend_factor": 1, "feathering": 4, "target": ["269", 0], "target_bounds": ["602", 1], "source": ["410", 0]}, "class_type": "Bounded Image Blend", "_meta": {"title": "Bounded Image Blend"}}, "610": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "612": {"inputs": {"text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["615", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "613": {"inputs": {"text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["615", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "614": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 512, "seed": 393443463819939, "steps": 8, "cfg": "3", "sampler_name": "euler", "scheduler": "normal", "denoise": 0.4, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 500, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 2, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "wildcard": "", "cycle": 1, "inpaint_model": 1, "noise_mask_feather": 0, "image": ["414", 0], "model": ["615", 0], "clip": ["615", 1], "vae": ["615", 2], "positive": ["612", 0], "negative": ["613", 0], "bbox_detector": ["610", 0], "sam_model_opt": ["628", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "615": {"inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "618": {"inputs": {"strength": 0.1, "start_percent": 0, "end_percent": 0.5, "positive": ["412", 1], "negative": ["412", 2], "control_net": ["619", 0], "image": ["614", 0], "vae": ["416", 2]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "619": {"inputs": {"control_net_name": "TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "624": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["414", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "628": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "631": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "632": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "633": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "634": {"inputs": {"image": ".jpg.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "637": {"inputs": {"upscale_method": "nearest-exact", "width": 1344, "height": 1792, "crop": "disabled", "image": ["634", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "638": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "639": {"inputs": {"type": "openpose", "control_net": ["633", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "640": {"inputs": {"type": "depth", "control_net": ["633", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "641": {"inputs": {"detect_hand": "enable", "detect_body": "enable", "detect_face": "enable", "resolution": 1024, "bbox_detector": "yolox_l.onnx", "pose_estimator": "dw-ll_ucoco_384.onnx", "image": ["637", 0]}, "class_type": "DWPreprocessor", "_meta": {"title": "DWPose Estimator"}}, "642": {"inputs": {"ckpt_name": "depth_anything_vits14.pth", "resolution": 1024, "image": ["637", 0]}, "class_type": "DepthAnythingPreprocessor", "_meta": {"title": "Depth Anything"}}, "644": {"inputs": {"strength": 0.6, "start_percent": 0, "end_percent": 0.2, "positive": ["647", 0], "negative": ["648", 0], "control_net": ["639", 0], "vae": ["638", 0], "image": ["641", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "645": {"inputs": {"strength": 0.5, "start_percent": 0, "end_percent": 0.2, "positive": ["644", 0], "negative": ["644", 1], "control_net": ["640", 0], "vae": ["638", 0], "image": ["642", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "647": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "648": {"inputs": {"text": "", "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 648, "last_link_id": 1183, "nodes": [{"id": 177, "type": "Text String", "pos": [6636.9169921875, -744.2022705078125], "size": [315, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250530/1/312147", "product_2127841", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 185, "type": "Text Concatenate", "pos": [7763.9169921875, -598.2022705078125], "size": [315, 178], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "type": "String to Text", "pos": [6263.9169921875, -531.2022705078125], "size": [315, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": [6241.9169921875, -725.2022705078125], "size": [315, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 216, "type": "ConrainImageSave", "pos": [8328.595703125, -871.1327514648438], "size": [320, 266], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 856, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "type": "Text Concatenate", "pos": [7118.9169921875, -777.2022705078125], "size": [250, 142], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [6629.9169921875, -873.2022094726562], "size": [315, 58], "flags": {"collapsed": false}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 235, "type": "InspyrenetRembg", "pos": [6518.8974609375, -1330.87353515625], "size": [230, 90], "flags": {}, "order": 126, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 236, "type": "CR Upscale Image", "pos": [6062.8974609375, -1399.87353515625], "size": [315, 222], "flags": {}, "order": 124, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1150, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 240, "type": "Note", "pos": [6948.8974609375, -1650.87353515625], "size": [260, 110], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 248, "type": "EmptyImage", "pos": [6973.8974609375, -1469.87353515625], "size": [231.5089111328125, 120.12616729736328], "flags": {"collapsed": false}, "order": 130, "mode": 4, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [6959.8974609375, -1252.87353515625], "size": [252, 26], "flags": {}, "order": 128, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 261, "type": "Image Size to Number", "pos": [6524.8974609375, -1556.87353515625], "size": [229.20001220703125, 126], "flags": {}, "order": 131, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 263, "type": "ImageScaleBy", "pos": [8283.8857421875, -1574.87353515625], "size": [228.9691162109375, 78], "flags": {}, "order": 132, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [7969.8974609375, -1462.87353515625], "size": [220, 118], "flags": {}, "order": 133, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 269, "type": "VAEDecode", "pos": [2494.43505859375, -1510.82861328125], "size": [210, 46], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 1160, 951], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 270, "type": "VAELoader", "pos": [2209.7861328125, -1211.8885498046875], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520, 971], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 271, "type": "DualCLIPLoader", "pos": [-1124.32275390625, -963.4298095703125], "size": [315, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 664, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "type": "UNETLoader", "pos": [-1143.5081787109375, -1168.266845703125], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 647, 647, 944], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2159.7138671875, -1607.7777099609375], "size": [236.8000030517578, 127.99258422851562], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 274, "type": "KSamplerSelect", "pos": [1891.447265625, -1192.7852783203125], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [1912.1287841796875, -1025.************], "size": [210, 106], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", "30", 1]}, {"id": 276, "type": "BasicGuider", "pos": [1875.15283203125, -1434.1328125], "size": [161.1999969482422, 46], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 277, "type": "RandomNoise", "pos": [1737.************, -1606.10498046875], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["704516429941238", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 278, "type": "FluxGuidance", "pos": [1580.5706787109375, -1422.3043212890625], "size": [211.60000610351562, 58], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1183}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1599.6937255859375, -1187.************], "size": [210, 86.50716400146484], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1582.************, -938.47998046875], "size": [210, 122], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [670.85205078125, -1021.9560546875], "size": [400, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [472, 473], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), side view, (full-body portrait:1.2), full body, The model is wearing A navy blue jacket top and the inner layer of the jacket is black and beige wide-leg pants set, The jacket is hip-length with a round neckline, long sleeves, and large silver buttons down the front without any visible logos or patterns. The fabric of this jacket appears to be lightweight and slightly sheer, with a subtle sheen that suggests it may be made from a silk blend or a fine synthetic material.  The pants are loose-fitting, with a wide leg cut and an ankle-length hem, made from a light, textured fabric. ,buttoned.20-year-old European female model, is wearing {black sleeveless top} inside. 20-year-old European female model, is wearing sneakers. The  sneakers feature solid color and simple. \n\n\nA softly lit studio with neutral, diffused lighting that creates gentle shadows and a balanced, open atmosphere. The background is a soft, light beige or pale grey, with clean lines and minimalistic walls that subtly reflect the light. The light is directional but softly filtered, coming from one side and casting natural shadows on the walls and floor, enhancing the depth of the space. The overall effect is calm, elegant, and spacious, with a focus on creating a sense of openness and subtle contrast between light and shadow without overwhelming warmth.{20-year-old European female model stands casually with hands in his pockets and his legs bent.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model is walking with one hand in pocket and one hand naturally perpendicular to side.|20-year-old European female model is walking with hands in his pockets.|20-year-old European female model is walking with hands hanging down naturally.},\n\n\na mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", "849333078228669", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 284, "type": "CR Seed", "pos": [1226.750732421875, -557.4791259765625], "size": [243.4204864501953, 102], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": ["1785", "fixed"]}, {"id": 285, "type": "CR Seed", "pos": [1212.750732421875, -743.4803466796875], "size": [243.4204864501953, 102], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": ["1340", "fixed"]}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [679.8088989257812, -737.42529296875], "size": [411.6590881347656, 124], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 287, "type": "Reroute", "pos": [2416.642578125, -892.2965698242188], "size": [75, 26], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 473, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2381.642578125, -761.2966918945312], "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [7391.8974609375, -1376.87353515625], "size": [252, 146], "flags": {}, "order": 134, "mode": 4, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 320, "type": "ConrainTextSave", "pos": [8334.912109375, -526.2022705078125], "size": [315, 106], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2333.675048828125, -1062.658203125], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [575, 786, 575, 1125], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1560.52734375, -667.1412963867188], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 44, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1583.478515625, -566.14013671875], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [1792.49072265625, -689.1410522460938], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2042.1220703125, -693.1410522460938], "size": [210, 34], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [1863.5029296875, -559.14013671875], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2095.989013671875, -538.1401977539062], "size": [210, 34], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 355, "type": "ImageCrop", "pos": [7805.9169921875, -901.202392578125], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [856], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 365, "type": "Image Size to Number", "pos": [7407.1708984375, -1615.321533203125], "size": [229.20001220703125, 126], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1151, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [7701.1708984375, -1540.321533203125], "size": [210, 67.1211166381836], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 373, "type": "JWStringToInteger", "pos": [-355.901611328125, -1022.3419799804688], "size": [210, 58], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644, 644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["2"]}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-633.5529174804688, -725.13427734375], "size": [315, 154], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "type": "CR Model Input Switch", "pos": [-31.242746353149414, -717.9091186523438], "size": [257.191650390625, 78.78076171875], "flags": {"collapsed": false}, "order": 60, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 944, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [946], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["2"]}, {"id": 378, "type": "ModelMergeFlux1", "pos": [59.612728118896484, -1090.4515380859375], "size": [315, 1566], "flags": {"collapsed": true}, "order": 84, "mode": 4, "inputs": [{"name": "model1", "type": "MODEL", "link": 948}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "type": "UNETLoader", "pos": [-1121.5262451171875, -1387.24658203125], "size": [315, 82], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [662, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 385, "type": "Reroute", "pos": [-524.6895141601562, -1147.2415771484375], "size": [75, 26], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 386, "type": "Reroute", "pos": [265.62896728515625, -970.5693359375], "size": [75, 26], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665, 968], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 389, "type": "InstantIDModelLoader", "pos": [4393, -805.2838134765625], "size": [315, 58], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [710], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "type": "InstantIDFaceAnalysis", "pos": [4406.5166015625, -699.9271850585938], "size": [315, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [711], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 391, "type": "ControlNetLoader", "pos": [4435.88427734375, -610.9759521484375], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [712], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "type": "InpaintModelConditioning", "pos": [4979.54736328125, -550.9163208007812], "size": [210, 138], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1136, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 1137}, {"name": "vae", "type": "VAE", "link": 674}, {"name": "pixels", "type": "IMAGE", "link": 979}, {"name": "mask", "type": "MASK", "link": 676}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1134], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [1135], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [709], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 397, "type": "CLIPTextEncode", "pos": [4117.23779296875, -790.20458984375], "size": [210, 96], "flags": {"collapsed": false}, "order": 64, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 955}, {"name": "text", "type": "STRING", "link": 788, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [715], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 398, "type": "CLIPTextEncode", "pos": [3991.1611328125, -612.1043701171875], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": false}, "order": 48, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 956}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [716], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}, {"id": 399, "type": "OrderedFaceFilter", "pos": [3716.439208984375, -299.1335754394531], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 684}], "outputs": [{"name": "filtered", "type": "FACE", "links": [702, 720], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 402, "type": "MaskFastGrow", "pos": [4301.9423828125, -254.13845825195312], "size": [210, 178], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1148}], "outputs": [{"name": "MASK", "type": "MASK", "links": [676, 687, 690], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 32, 4, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 403, "type": "MaskFastGrow", "pos": [4614.6142578125, -244.55067443847656], "size": [210, 178], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 689}], "outputs": [{"name": "MASK", "type": "MASK", "links": [704], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 72, 40, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 409, "type": "easy imageColorMatch", "pos": [5279.58203125, -368.6326904296875], "size": [210, 102], "flags": {"collapsed": false}, "order": 120, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 989}, {"name": "image_target", "type": "IMAGE", "link": 1142}], "outputs": [{"name": "image", "type": "IMAGE", "links": [703], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "type": "WarpFacesBack", "pos": [4906.482421875, -279.61639404296875], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1104}, {"name": "face", "type": "FACE", "link": 702}, {"name": "crop", "type": "IMAGE", "link": 703}, {"name": "mask", "type": "MASK", "link": 704}, {"name": "warp", "type": "WARP", "link": 705}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [781], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 411, "type": "K<PERSON><PERSON><PERSON>", "pos": [5269.8623046875, -863.2468872070312], "size": [261.8017578125, 262], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 706}, {"name": "positive", "type": "CONDITIONING", "link": 1134}, {"name": "negative", "type": "CONDITIONING", "link": 1135}, {"name": "latent_image", "type": "LATENT", "link": 709}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [718], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": ["704516429941238", "fixed", 5, 1, "euler", "kl_optimal", 0.6]}, {"id": 412, "type": "ApplyInstantID", "pos": [4677.8056640625, -657.3215942382812], "size": [210, 266], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 710, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 711, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 712, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 713, "label": "image"}, {"name": "model", "type": "MODEL", "link": 954, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 715, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 716, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 980, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [706], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [1127], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [1128], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 413, "type": "VAEDecode", "pos": [5358.111328125, -481.2630310058594], "size": [140, 46], "flags": {"collapsed": false}, "order": 119, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 718}, {"name": "vae", "type": "VAE", "link": 719}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1142], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 414, "type": "CropFaces", "pos": [4010.4345703125, -402.2294921875], "size": [221.15121459960938, 146], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 720}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [989, 1121, 1145], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [689, 689], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [705], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "type": "CheckpointLoaderSimple", "pos": [3718.30908203125, -840.8453979492188], "size": [314.34735107421875, 128.43458557128906], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [954], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [955, 956], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [674, 719, 1133], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 436, "type": "DetectFaces", "pos": [3738.894775390625, -529.5217895507812], "size": [210, 126], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1103}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [684], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 446, "type": "CropFaces", "pos": [5317.95458984375, -2141.50634765625], "size": [221.15121459960938, 146], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1071}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [713, 762], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "type": "DetectFaces", "pos": [5064.96484375, -2149.68798828125], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1073}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [1071], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 455, "type": "Reroute", "pos": [2794.47265625, -1274.2305908203125], "size": [75, 26], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1160}], "outputs": [{"name": "", "type": "IMAGE", "links": [789, 1111, 789], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 456, "type": "Reroute", "pos": [5190.1982421875, -171.50381469726562], "size": [75, 26], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 781}], "outputs": [{"name": "", "type": "IMAGE", "links": [1107, 783], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 457, "type": "Reroute", "pos": [3770.6259765625, -681.1192016601562], "size": [75, 26], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 786, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [788, 788], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 474, "type": "CR Image Input Switch", "pos": [4197.35693359375, -1760.59130859375], "size": [210, 74], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 813, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 1161, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [998, 1069], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 477, "type": "CR Image Input Switch", "pos": [4193.537109375, -1612.7928466796875], "size": [210, 74], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1089, "shape": 7}, {"name": "image2", "type": "IMAGE", "shape": 7}, {"name": "Input", "type": "INT", "link": 1162, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1044, 1060, 1070], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 508, "type": "ApplyPulidFlux", "pos": [33.53999710083008, -1483.230712890625], "size": [315, 226], "flags": {}, "order": 80, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 947}, {"name": "pulid_flux", "type": "PULIDFLUX", "link": 879}, {"name": "eva_clip", "type": "EVA_CLIP", "link": 882}, {"name": "face_analysis", "type": "FACEANALYSIS", "link": 883}, {"name": "image", "type": "IMAGE", "link": 966}, {"name": "attn_mask", "type": "MASK", "shape": 7}, {"name": "options", "type": "OPTIONS", "shape": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [948], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "widgets_values": [0.8, 0, 1]}, {"id": 512, "type": "PulidFluxModelLoader", "pos": [-616.6622924804688, -1513.3837890625], "size": [315, 58], "flags": {}, "order": 21, "mode": 4, "inputs": [], "outputs": [{"name": "PULIDFLUX", "type": "PULIDFLUX", "links": [879]}], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "type": "PulidFluxEvaClipLoader", "pos": [-618.7819213867188, -1391.823486328125], "size": [327.5999755859375, 26], "flags": {"collapsed": false}, "order": 22, "mode": 4, "inputs": [], "outputs": [{"name": "EVA_CLIP", "type": "EVA_CLIP", "links": [882]}], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "widgets_values": []}, {"id": 515, "type": "PulidFluxInsightFaceLoader", "pos": [-629.92724609375, -1297.8043212890625], "size": [365.4000244140625, 58], "flags": {}, "order": 23, "mode": 4, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [883]}], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "widgets_values": ["CUDA"]}, {"id": 525, "type": "easy humanSegmentation", "pos": [-968.95849609375, -1714.54443359375], "size": [300, 260], "flags": {}, "order": 55, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 899}], "outputs": [{"name": "image", "type": "IMAGE"}, {"name": "mask", "type": "MASK", "links": [960], "slot_index": 1}, {"name": "bbox", "type": "BBOX"}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 536, "type": "Bounded Image Crop with Mask", "pos": [-204.83697509765625, -1647.264892578125], "size": [236.27499389648438, 150], "flags": {}, "order": 67, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 959}, {"name": "mask", "type": "MASK", "link": 960}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [966], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [96, 96, 96, 96]}, {"id": 537, "type": "Reroute", "pos": [-438.78076171875, -1683.8594970703125], "size": [75, 26], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 957}], "outputs": [{"name": "", "type": "IMAGE", "links": [959], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 549, "type": "Reroute", "pos": [4441.5126953125, -554.5872192382812], "size": [75, 26], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 985}], "outputs": [{"name": "", "type": "IMAGE", "links": [979, 980, 1132], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 550, "type": "Reroute", "pos": [4180.76416015625, -906.14404296875], "size": [75, 26], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1123}], "outputs": [{"name": "", "type": "IMAGE", "links": [985], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 559, "type": "ImpactMakeImageList", "pos": [4528.984375, -1783.8060302734375], "size": [140, 86], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1001}, {"name": "image2", "type": "IMAGE", "link": 1069}, {"name": "image3", "type": "IMAGE", "link": 1070}, {"name": "image4", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1009], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "widgets_values": []}, {"id": 561, "type": "ImageResize+", "pos": [4475.1279296875, -1605.535888671875], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1009}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1092, 1093], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 570, "type": "ImpactSimpleDetectorSEGS", "pos": [4129.994140625, -1363.3128662109375], "size": [277.6741943359375, 319.97015380859375], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1155}, {"name": "image", "type": "IMAGE", "link": 1093}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1084], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 574, "type": "SegsToCombinedMask", "pos": [4766.97509765625, -1133.2860107421875], "size": [289.79998779296875, 26], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1086}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1039], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 576, "type": "Bounded Image Crop with Mask", "pos": [4740.59912109375, -2150.749755859375], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1037}, {"name": "mask", "type": "MASK", "link": 1040}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1072], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 579, "type": "Reroute", "pos": [4805.14599609375, -1606.9412841796875], "size": [75, 26], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1092}], "outputs": [{"name": "", "type": "IMAGE", "links": [1037], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 580, "type": "MaskFastGrow", "pos": [4809.68701171875, -1402.79443359375], "size": [210, 178], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1039}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1040], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 592, "type": "easy imageListToImageBatch", "pos": [4832.5732421875, -1937.627197265625], "size": [315, 26], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1072}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1073], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 594, "type": "ImageResize+", "pos": [2970.1611328125, -868.4249267578125], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 98, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1111}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1099], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 597, "type": "ImpactSEGSOrderedFilter", "pos": [4494.18310546875, -1262.16796875], "size": [210, 158.96408081054688], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1084}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1086], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 600, "type": "ImpactSimpleDetectorSEGS", "pos": [3365.37109375, -873.4877319335938], "size": [277.6741943359375, 319.97015380859375], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1156}, {"name": "image", "type": "IMAGE", "link": 1100}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1096], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 601, "type": "ImpactSEGSOrderedFilter", "pos": [2992.445068359375, -562.107421875], "size": [210, 158.96408081054688], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1096}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1097], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 602, "type": "Bounded Image Crop with Mask", "pos": [3374.4775390625, -387.49652099609375], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1102}, {"name": "mask", "type": "MASK", "link": 1106}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1103, 1104], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1109], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 603, "type": "SegsToCombinedMask", "pos": [3347.696044921875, -507.6412353515625], "size": [203.30526733398438, 27.29315948486328], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1097}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1105], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 604, "type": "Reroute", "pos": [3266.8720703125, -881.5452880859375], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1099}], "outputs": [{"name": "", "type": "IMAGE", "links": [1100, 1102, 1108], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 605, "type": "MaskFastGrow", "pos": [3011.666748046875, -366.3255615234375], "size": [210, 178], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1105}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1106], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 608, "type": "Bounded Image Blend", "pos": [5312.66455078125, -152.3022918701172], "size": [210, 135.17051696777344], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1108}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1109}, {"name": "source", "type": "IMAGE", "link": 1107}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1150, 1151], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Blend"}, "widgets_values": [1, 4]}, {"id": 610, "type": "UltralyticsDetectorProvider", "pos": [3751.15673828125, -2575.84716796875], "size": [315, 78], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1119, 1154], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 612, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [3779.155517578125, -3142.57373046875], "size": [311.57757568359375, 186.89004516601562], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1112, "label": "clip"}, {"name": "text", "type": "STRING", "link": 1125, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1117], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 613, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [3736.486083984375, -2849.99609375], "size": [400, 200], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1113, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1118], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 614, "type": "FaceDetailer", "pos": [4257.23876953125, -3428.75146484375], "size": [350.5302734375, 902.3991088867188], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1122, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1114, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 1115, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 1116, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 1117, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1118, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1119, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 1157, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1123], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, 393443463819939, "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 2, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 615, "type": "CheckpointLoaderSimple", "pos": [3744.654052734375, -3407.46875], "size": [315, 98], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1114], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [1112, 1113, 1115], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [1116], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 616, "type": "Reroute", "pos": [4132.41357421875, -2412.88818359375], "size": [75, 26], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1121}], "outputs": [{"name": "", "type": "IMAGE", "links": [1122], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 618, "type": "ControlNetApplyAdvanced", "pos": [4966.9052734375, -837.8536987304688], "size": [243.52598571777344, 186], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1127}, {"name": "negative", "type": "CONDITIONING", "link": 1128}, {"name": "control_net", "type": "CONTROL_NET", "link": 1126}, {"name": "image", "type": "IMAGE", "link": 1132}, {"name": "vae", "type": "VAE", "link": 1133, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1136], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1137], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.1, 0, 0.5]}, {"id": 619, "type": "ControlNetLoader", "pos": [4352.673828125, -904.6546630859375], "size": [315, 58], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1126], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"]}, {"id": 624, "type": "easy humanSegmentation", "pos": [3948.153564453125, -155.76644897460938], "size": [300, 500], "flags": {"collapsed": false}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1145}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [1148], "slot_index": 1}, {"name": "bbox", "type": "BBOX", "slot_index": 2}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 627, "type": "Reroute", "pos": [3830.228271484375, -1238.8267822265625], "size": [75, 26], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1154}], "outputs": [{"name": "", "type": "BBOX_DETECTOR", "links": [1155, 1156], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 628, "type": "SAMLoader", "pos": [3766.485595703125, -2433.7041015625], "size": [315, 82], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1157], "slot_index": 0}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"]}, {"id": 631, "type": "JWStringToInteger", "pos": [3707.870361328125, -1688.587646484375], "size": [315, 58], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1161], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 632, "type": "JWStringToInteger", "pos": [3733.91650390625, -1520.59716796875], "size": [315, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1162], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 635, "type": "PrimitiveNode", "pos": [635.9981689453125, -2166.799072265625], "size": [210, 82], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1164], "slot_index": 0, "widget": {"name": "width"}}], "title": "width", "properties": {"Run widget replace on values": false}, "widgets_values": [1344, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 636, "type": "PrimitiveNode", "pos": [638.7860717773438, -2018.697998046875], "size": [210, 82], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1165], "slot_index": 0, "widget": {"name": "height"}}], "title": "height", "properties": {"Run widget replace on values": false}, "widgets_values": [1792, "fixed"], "color": "#323", "bgcolor": "#535"}, {"id": 633, "type": "ControlNetLoader", "pos": [580.6925659179688, -2673.3115234375], "size": [581.6453247070312, 58], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1166, 1167], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 638, "type": "VAELoader", "pos": [1387.25341796875, -2682.68505859375], "size": [315, 58], "flags": {}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1170, 1171], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 639, "type": "SetUnionControlNetType", "pos": [1431.8035888671875, -2553.281982421875], "size": [210, 59.09758758544922], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1166}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1176], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["openpose"]}, {"id": 640, "type": "SetUnionControlNetType", "pos": [1434.1259765625, -2430.77392578125], "size": [210, 58], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1167}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1177], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["depth"]}, {"id": 283, "type": "ShowText|pysssss", "pos": [1197.807861328125, -1068.64892578125], "size": [256.63372802734375, 226], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 472, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1182], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 641, "type": "DWPreprocessor", "pos": [1352.1402587890625, -2323.666015625], "size": [315, 198], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1168}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1174], "slot_index": 0}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT", "links": null}], "properties": {"Node name for S&R": "DWPreprocessor"}, "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 637, "type": "ImageScale", "pos": [951.579345703125, -2292.708251953125], "size": [315, 130], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1163}, {"name": "width", "type": "INT", "link": 1164, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1165, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1168, 1169], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["nearest-exact", 1344, 1792, "disabled"]}, {"id": 642, "type": "DepthAnythingPreprocessor", "pos": [1358.7843017578125, -2073.822021484375], "size": [315, 82], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1169}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1175], "slot_index": 0}], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 647, "type": "CLIPTextEncode", "pos": [1727.30029296875, -2034.4564208984375], "size": [301.349365234375, 54], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1180}, {"name": "text", "type": "STRING", "link": 1182, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1178], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 644, "type": "ControlNetApplySD3", "pos": [1832.408203125, -2536.71044921875], "size": [315, 186], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1178}, {"name": "negative", "type": "CONDITIONING", "link": 1179}, {"name": "control_net", "type": "CONTROL_NET", "link": 1176}, {"name": "vae", "type": "VAE", "link": 1170}, {"name": "image", "type": "IMAGE", "link": 1174}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1172], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1173], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.6, 0, 0.2]}, {"id": 648, "type": "CLIPTextEncode", "pos": [1764.1572265625, -1893.5286865234375], "size": [285.6000061035156, 76], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1181}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1179]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 634, "type": "LoadImage", "pos": [581.966552734375, -2553.326416015625], "size": [315, 314], "flags": {}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1163]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": [".jpg.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 645, "type": "ControlNetApplySD3", "pos": [2261.227294921875, -2442.93994140625], "size": [315, 186], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1172}, {"name": "negative", "type": "CONDITIONING", "link": 1173}, {"name": "control_net", "type": "CONTROL_NET", "link": 1177}, {"name": "vae", "type": "VAE", "link": 1171}, {"name": "image", "type": "IMAGE", "link": 1175}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1183], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": null}], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "widgets_values": [0.5, 0, 0.2]}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [607.9300537109375, -1440.7767333984375], "size": [491.7470703125, 126], "flags": {}, "order": 75, "mode": 4, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["open_lora/FLUX-dev-lora-AntiBlur.safetensors", "1.5", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [607.7188720703125, -1644.330810546875], "size": [477.3377990722656, 128.31455993652344], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 946}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/1808第二批_颜色3_22727_20250530_164444/1808第二批_颜色3_22727_20250530_164444-flux/1808第二批_颜色3_22727_20250530_164444-flux.safetensors", "0.3", "0.3"], "color": "#232", "bgcolor": "#353"}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [621.1154174804688, -1249.7822265625], "size": [499.25970458984375, 126], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 654, 947], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [510, 513, 630, 631, 1180, 1181], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/女士中式大龄开衫_19429_20250521_103121/女士中式大龄开衫_19429_20250521_103121-flux/女士中式大龄开衫_19429_20250521_103121-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 462, "type": "LoadImage", "pos": [3773.521728515625, -2166.29052734375], "size": [235.8109893798828, 314], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [899, 957, 1001, 957], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": [".jpg.png", "image"]}, {"id": 475, "type": "LoadImage", "pos": [4088.89013671875, -2179.81396484375], "size": [268.51116943359375, 314], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [813], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": [".jpg.png", "image"]}, {"id": 476, "type": "LoadImage", "pos": [4457.65234375, -2186.2099609375], "size": [234.48504638671875, 314], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1003, 1007, 1051, 1064, 1089], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": [".jpg.png", "image"]}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [674, 416, 2, 392, 2, "VAE"], [676, 402, 0, 392, 4, "MASK"], [684, 436, 0, 399, 0, "FACE"], [689, 414, 1, 403, 0, "MASK"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [781, 410, 0, 456, 0, "*"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [813, 475, 0, 474, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [954, 416, 0, 412, 4, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [979, 549, 0, 392, 3, "IMAGE"], [980, 549, 0, 412, 7, "IMAGE"], [985, 550, 0, 549, 0, "*"], [989, 414, 0, 409, 0, "IMAGE"], [998, 474, 0, 559, 0, "IMAGE"], [1001, 462, 0, 559, 0, "IMAGE"], [1003, 476, 0, 559, 1, "IMAGE"], [1007, 476, 0, 559, 1, "IMAGE"], [1009, 559, 0, 561, 0, "IMAGE"], [1037, 579, 0, 576, 0, "IMAGE"], [1039, 574, 0, 580, 0, "MASK"], [1040, 580, 0, 576, 1, "MASK"], [1044, 477, 0, 559, 1, "IMAGE"], [1051, 476, 0, 559, 1, "IMAGE"], [1060, 477, 0, 559, 1, "IMAGE"], [1064, 476, 0, 559, 1, "IMAGE"], [1069, 474, 0, 559, 1, "IMAGE"], [1070, 477, 0, 559, 2, "IMAGE"], [1071, 447, 0, 446, 0, "FACE"], [1072, 576, 0, 592, 0, "IMAGE"], [1073, 592, 0, 447, 0, "IMAGE"], [1084, 570, 0, 597, 0, "SEGS"], [1086, 597, 0, 574, 0, "SEGS"], [1089, 476, 0, 477, 0, "IMAGE"], [1092, 561, 0, 579, 0, "*"], [1093, 561, 0, 570, 1, "IMAGE"], [1096, 600, 0, 601, 0, "SEGS"], [1097, 601, 0, 603, 0, "SEGS"], [1099, 594, 0, 604, 0, "*"], [1100, 604, 0, 600, 1, "IMAGE"], [1102, 604, 0, 602, 0, "IMAGE"], [1103, 602, 0, 436, 0, "IMAGE"], [1104, 602, 0, 410, 0, "IMAGE"], [1105, 603, 0, 605, 0, "MASK"], [1106, 605, 0, 602, 1, "MASK"], [1107, 456, 0, 608, 2, "IMAGE"], [1108, 604, 0, 608, 0, "IMAGE"], [1109, 602, 1, 608, 1, "IMAGE_BOUNDS"], [1111, 455, 0, 594, 0, "IMAGE"], [1112, 615, 1, 612, 0, "CLIP"], [1113, 615, 1, 613, 0, "CLIP"], [1114, 615, 0, 614, 1, "MODEL"], [1115, 615, 1, 614, 2, "CLIP"], [1116, 615, 2, 614, 3, "VAE"], [1117, 612, 0, 614, 4, "CONDITIONING"], [1118, 613, 0, 614, 5, "CONDITIONING"], [1119, 610, 0, 614, 6, "BBOX_DETECTOR"], [1121, 414, 0, 616, 0, "*"], [1122, 616, 0, 614, 0, "IMAGE"], [1123, 614, 0, 550, 0, "*"], [1125, 331, 0, 612, 1, "STRING"], [1126, 619, 0, 618, 2, "CONTROL_NET"], [1127, 412, 1, 618, 0, "CONDITIONING"], [1128, 412, 2, 618, 1, "CONDITIONING"], [1132, 549, 0, 618, 3, "IMAGE"], [1133, 416, 2, 618, 4, "VAE"], [1134, 392, 0, 411, 1, "CONDITIONING"], [1135, 392, 1, 411, 2, "CONDITIONING"], [1136, 618, 0, 392, 0, "CONDITIONING"], [1137, 618, 1, 392, 1, "CONDITIONING"], [1142, 413, 0, 409, 1, "IMAGE"], [1145, 414, 0, 624, 0, "IMAGE"], [1148, 624, 1, 402, 0, "MASK"], [1150, 608, 0, 236, 0, "IMAGE"], [1151, 608, 0, 365, 0, "IMAGE"], [1154, 610, 0, 627, 0, "*"], [1155, 627, 0, 570, 0, "BBOX_DETECTOR"], [1156, 627, 0, 600, 0, "BBOX_DETECTOR"], [1157, 628, 0, 614, 7, "SAM_MODEL"], [1160, 269, 0, 455, 0, "*"], [1161, 631, 0, 474, 2, "INT"], [1162, 632, 0, 477, 2, "INT"], [1163, 634, 0, 637, 0, "IMAGE"], [1164, 635, 0, 637, 1, "INT"], [1165, 636, 0, 637, 2, "INT"], [1166, 633, 0, 639, 0, "CONTROL_NET"], [1167, 633, 0, 640, 0, "CONTROL_NET"], [1168, 637, 0, 641, 0, "IMAGE"], [1169, 637, 0, 642, 0, "IMAGE"], [1170, 638, 0, 644, 3, "VAE"], [1171, 638, 0, 645, 3, "VAE"], [1172, 644, 0, 645, 0, "CONDITIONING"], [1173, 644, 1, 645, 1, "CONDITIONING"], [1174, 641, 0, 644, 4, "IMAGE"], [1175, 642, 0, 645, 4, "IMAGE"], [1176, 639, 0, 644, 2, "CONTROL_NET"], [1177, 640, 0, 645, 2, "CONTROL_NET"], [1178, 647, 0, 644, 0, "CONDITIONING"], [1179, 648, 0, 644, 1, "CONDITIONING"], [1180, 296, 1, 647, 0, "CLIP"], [1181, 296, 1, 648, 0, "CLIP"], [1182, 283, 0, 647, 1, "STRING"], [1183, 645, 0, 278, 0, "CONDITIONING"]], "groups": [{"id": 3, "title": "换背景", "bounding": [5945.7255859375, -1753.57763671875, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [522.859375, -1743.14208984375, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [6046.26708984375, -1006.9291381835938, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-1181.6468505859375, -1750.223388671875, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "换脸", "bounding": [3680.31640625, -960.367431640625, 1901.2747802734375, 953.2968139648438], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "人脸原图", "bounding": [3692.35400390625, -2275.424072265625, 1887.6405029296875, 1281.4771728515625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "前置检测人脸", "bounding": [2947.870361328125, -961.119873046875, 699.4480590820312, 790.8760375976562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 13, "title": "1.5修脸", "bounding": [3682.15673828125, -3535.468017578125, 1347, 1200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "controlnet", "bounding": [541.1925659179688, -2768.327880859375, 2175.0947265625, 980.9996337890625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9646149645000016, "offset": [-6397.364623009266, 1537.7829920060472]}}, "version": 0.4, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "614": 3}}}}}
{"client_id": "${clientId}", "prompt": {"177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "216": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["645", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["282", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,608)}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["608", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "389": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}}, "390": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}}, "391": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}}, "392": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["402", 0], "negative": ["618", 1], "noise_mask": true, "pixels": ["614", 0], "positive": ["618", 0], "vae": ["416", 2]}}, "397": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": ["331", 0]}}, "398": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}}, "399": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["436", 0], "order": "descending", "take_count": 1, "take_start": 0}}, "402": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 4, "enabled": true, "grow": 32, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["624", 1]}}, "403": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 40, "enabled": true, "grow": 72, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["414", 1]}}, "409": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["414", 0], "image_target": ["413", 0], "method": "adain", "save_prefix": "ComfyUI"}}, "410": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["409", 0], "face": ["399", 0], "images": ["602", 0], "mask": ["403", 0], "warp": ["414", 2]}}, "411": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.6, "latent_image": ["392", 2], "model": ["412", 0], "negative": ["392", 1], "positive": ["392", 0], "sampler_name": "euler", "scheduler": "kl_optimal", "seed": "${seed}", "steps": 5}}, "412": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["391", 0], "end_at": 1, "image": ["446", 0], "image_kps": ["614", 0], "insightface": ["390", 0], "instantid": ["389", 0], "model": ["416", 0], "negative": ["398", 0], "positive": ["397", 0], "start_at": 0, "weight": 1}}, "413": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["411", 0], "vae": ["416", 2]}}, "414": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 1.5, "crop_size": 1024, "faces": ["399", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "416": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"}}, "436": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["602", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "446": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["447", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "447": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["592", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "462": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}}, "474": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["631", 0], "image1": ["475", 0]}}, "475": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "476": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "477": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["632", 0], "image1": ["476", 0]}}, "559": {"_meta": {"title": "Make Image List"}, "class_type": "ImpactMakeImageList", "inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}}, "561": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "upscale if smaller", "height": 2048, "image": ["559", 0], "interpolation": "lanc<PERSON>s", "method": "keep proportion", "multiple_of": 0, "width": 2048}}, "570": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["610", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["561", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}}, "574": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["597", 0]}}, "576": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["561", 0], "mask": ["580", 0], "padding_bottom": 64, "padding_left": 64, "padding_right": 64, "padding_top": 64}}, "580": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 256, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["574", 0]}}, "592": {"_meta": {"title": "Image List To Image Batch"}, "class_type": "easy imageListToImageBatch", "inputs": {"images": ["576", 0]}}, "597": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["570", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}}, "600": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["610", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["269", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}}, "601": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["600", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}}, "602": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["269", 0], "mask": ["605", 0], "padding_bottom": 64, "padding_left": 64, "padding_right": 64, "padding_top": 64}}, "603": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["601", 0]}}, "605": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 256, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["603", 0]}}, "608": {"_meta": {"title": "Bounded Image Blend"}, "class_type": "Bounded Image Blend", "inputs": {"blend_factor": 1, "feathering": 4, "source": ["410", 0], "target": ["269", 0], "target_bounds": ["602", 1]}}, "610": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}}, "612": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["615", 1], "text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111"}}, "613": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["615", 1], "text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111"}}, "614": {"_meta": {"title": "FaceDetailer"}, "class_type": "FaceDetailer", "inputs": {"bbox_crop_factor": 3, "bbox_detector": ["610", 0], "bbox_dilation": 500, "bbox_threshold": 0.5, "cfg": "3", "clip": ["615", 1], "cycle": 1, "denoise": 0.4, "drop_size": 40, "feather": 5, "force_inpaint": true, "guide_size": 384, "guide_size_for": true, "image": ["414", 0], "inpaint_model": 1, "max_size": 512, "model": ["615", 0], "negative": ["613", 0], "noise_mask": true, "noise_mask_feather": 0, "positive": ["612", 0], "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 2, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_model_opt": ["628", 0], "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "normal", "seed": 393443463819939, "steps": 8, "vae": ["615", 2], "wildcard": ""}}, "615": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}}, "618": {"_meta": {"title": "Apply ControlNet"}, "class_type": "ControlNetApplyAdvanced", "inputs": {"control_net": ["619", 0], "end_percent": 0.5, "image": ["614", 0], "negative": ["412", 2], "positive": ["412", 1], "start_percent": 0, "strength": 0.1, "vae": ["416", 2]}}, "619": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"}}, "624": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["414", 0], "mask_components": "13", "method": "human_parsing_lip"}}, "628": {"_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}, "class_type": "SAMLoader", "inputs": {"device_mode": "AUTO", "model_name": "sam_vit_b_01ec64.pth"}}, "631": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "1"}}, "632": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "1"}}, "633": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}}, "634": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${referenceImage}", "upload": "image"}}, "637": {"_meta": {"title": "Upscale Image"}, "class_type": "ImageScale", "inputs": {"crop": "disabled", "height": 1792, "image": ["634", 0], "upscale_method": "nearest-exact", "width": 1344}}, "638": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "639": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["633", 0], "type": "openpose"}}, "640": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["633", 0], "type": "depth"}}, "641": {"_meta": {"title": "DWPose Estimator"}, "class_type": "DWPreprocessor", "inputs": {"bbox_detector": "yolox_l.onnx", "detect_body": "enable", "detect_face": "enable", "detect_hand": "enable", "image": ["637", 0], "pose_estimator": "dw-ll_ucoco_384.onnx", "resolution": 1024}}, "642": {"_meta": {"title": "Depth Anything"}, "class_type": "DepthAnythingPreprocessor", "inputs": {"ckpt_name": "depth_anything_vits14.pth", "image": ["637", 0], "resolution": 1024}}, "644": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["639", 0], "end_percent": 0.2, "image": ["641", 0], "negative": ["648", 0], "positive": ["647", 0], "start_percent": 0, "strength": 0.6, "vae": ["638", 0]}}, "645": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["640", 0], "end_percent": 0.2, "image": ["642", 0], "negative": ["644", 1], "positive": ["644", 0], "start_percent": 0, "strength": 0.5, "vae": ["638", 0]}}, "647": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "648": {"_meta": {"title": "CLIP Text Encode (Negative Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ""}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [-6397.364623009266, 1537.7829920060472], "scale": 0.9646149645000016}}, "groups": [{"id": 3, "bounding": [5945.7255859375, -1753.57763671875, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 5, "bounding": [522.859375, -1743.14208984375, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [6046.26708984375, -1006.9291381835938, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-1181.6468505859375, -1750.223388671875, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}, {"id": 8, "bounding": [3680.31640625, -960.367431640625, 1901.2747802734375, 953.2968139648438], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 10, "bounding": [3692.35400390625, -2275.424072265625, 1887.6405029296875, 1281.4771728515625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 12, "bounding": [2947.870361328125, -961.119873046875, 699.4480590820312, 790.8760375976562], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "前置检测人脸"}, {"id": 13, "bounding": [3682.15673828125, -3535.468017578125, 1347, 1200], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "1.5修脸"}, {"id": 14, "bounding": [541.1925659179688, -2768.327880859375, 2175.947265625, 980.9996337890625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "controlnet"}], "last_link_id": 1183, "last_node_id": 648, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [674, 416, 2, 392, 2, "VAE"], [676, 402, 0, 392, 4, "MASK"], [684, 436, 0, 399, 0, "FACE"], [689, 414, 1, 403, 0, "MASK"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [781, 410, 0, 456, 0, "*"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [813, 475, 0, 474, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [954, 416, 0, 412, 4, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [979, 549, 0, 392, 3, "IMAGE"], [980, 549, 0, 412, 7, "IMAGE"], [985, 550, 0, 549, 0, "*"], [989, 414, 0, 409, 0, "IMAGE"], [998, 474, 0, 559, 0, "IMAGE"], [1001, 462, 0, 559, 0, "IMAGE"], [1003, 476, 0, 559, 1, "IMAGE"], [1007, 476, 0, 559, 1, "IMAGE"], [1009, 559, 0, 561, 0, "IMAGE"], [1037, 579, 0, 576, 0, "IMAGE"], [1039, 574, 0, 580, 0, "MASK"], [1040, 580, 0, 576, 1, "MASK"], [1044, 477, 0, 559, 1, "IMAGE"], [1051, 476, 0, 559, 1, "IMAGE"], [1060, 477, 0, 559, 1, "IMAGE"], [1064, 476, 0, 559, 1, "IMAGE"], [1069, 474, 0, 559, 1, "IMAGE"], [1070, 477, 0, 559, 2, "IMAGE"], [1071, 447, 0, 446, 0, "FACE"], [1072, 576, 0, 592, 0, "IMAGE"], [1073, 592, 0, 447, 0, "IMAGE"], [1084, 570, 0, 597, 0, "SEGS"], [1086, 597, 0, 574, 0, "SEGS"], [1089, 476, 0, 477, 0, "IMAGE"], [1092, 561, 0, 579, 0, "*"], [1093, 561, 0, 570, 1, "IMAGE"], [1096, 600, 0, 601, 0, "SEGS"], [1097, 601, 0, 603, 0, "SEGS"], [1099, 594, 0, 604, 0, "*"], [1100, 604, 0, 600, 1, "IMAGE"], [1102, 604, 0, 602, 0, "IMAGE"], [1103, 602, 0, 436, 0, "IMAGE"], [1104, 602, 0, 410, 0, "IMAGE"], [1105, 603, 0, 605, 0, "MASK"], [1106, 605, 0, 602, 1, "MASK"], [1107, 456, 0, 608, 2, "IMAGE"], [1108, 604, 0, 608, 0, "IMAGE"], [1109, 602, 1, 608, 1, "IMAGE_BOUNDS"], [1111, 455, 0, 594, 0, "IMAGE"], [1112, 615, 1, 612, 0, "CLIP"], [1113, 615, 1, 613, 0, "CLIP"], [1114, 615, 0, 614, 1, "MODEL"], [1115, 615, 1, 614, 2, "CLIP"], [1116, 615, 2, 614, 3, "VAE"], [1117, 612, 0, 614, 4, "CONDITIONING"], [1118, 613, 0, 614, 5, "CONDITIONING"], [1119, 610, 0, 614, 6, "BBOX_DETECTOR"], [1121, 414, 0, 616, 0, "*"], [1122, 616, 0, 614, 0, "IMAGE"], [1123, 614, 0, 550, 0, "*"], [1125, 331, 0, 612, 1, "STRING"], [1126, 619, 0, 618, 2, "CONTROL_NET"], [1127, 412, 1, 618, 0, "CONDITIONING"], [1128, 412, 2, 618, 1, "CONDITIONING"], [1132, 549, 0, 618, 3, "IMAGE"], [1133, 416, 2, 618, 4, "VAE"], [1134, 392, 0, 411, 1, "CONDITIONING"], [1135, 392, 1, 411, 2, "CONDITIONING"], [1136, 618, 0, 392, 0, "CONDITIONING"], [1137, 618, 1, 392, 1, "CONDITIONING"], [1142, 413, 0, 409, 1, "IMAGE"], [1145, 414, 0, 624, 0, "IMAGE"], [1148, 624, 1, 402, 0, "MASK"], [1150, 608, 0, 236, 0, "IMAGE"], [1151, 608, 0, 365, 0, "IMAGE"], [1154, 610, 0, 627, 0, "*"], [1155, 627, 0, 570, 0, "BBOX_DETECTOR"], [1156, 627, 0, 600, 0, "BBOX_DETECTOR"], [1157, 628, 0, 614, 7, "SAM_MODEL"], [1160, 269, 0, 455, 0, "*"], [1161, 631, 0, 474, 2, "INT"], [1162, 632, 0, 477, 2, "INT"], [1163, 634, 0, 637, 0, "IMAGE"], [1164, 635, 0, 637, 1, "INT"], [1165, 636, 0, 637, 2, "INT"], [1166, 633, 0, 639, 0, "CONTROL_NET"], [1167, 633, 0, 640, 0, "CONTROL_NET"], [1168, 637, 0, 641, 0, "IMAGE"], [1169, 637, 0, 642, 0, "IMAGE"], [1170, 638, 0, 644, 3, "VAE"], [1171, 638, 0, 645, 3, "VAE"], [1172, 644, 0, 645, 0, "CONDITIONING"], [1173, 644, 1, 645, 1, "CONDITIONING"], [1174, 641, 0, 644, 4, "IMAGE"], [1175, 642, 0, 645, 4, "IMAGE"], [1176, 639, 0, 644, 2, "CONTROL_NET"], [1177, 640, 0, 645, 2, "CONTROL_NET"], [1178, 647, 0, 644, 0, "CONDITIONING"], [1179, 648, 0, 644, 1, "CONDITIONING"], [1180, 296, 1, 647, 0, "CLIP"], [1181, 296, 1, 648, 0, "CLIP"], [1182, 283, 0, 647, 1, "STRING"], [1183, 645, 0, 278, 0, "CONDITIONING"]], "nodes": [{"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [6636.9169921875, -744.2022705078125], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 63, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7763.9169921875, -598.2022705078125], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6263.9169921875, -531.2022705078125], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6241.9169921875, -725.2022705078125], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 856, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 129, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [8328.595703125, -871.1327514648438], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 38, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7118.9169921875, -777.2022705078125], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6629.9169921875, -873.2022094726562], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 126, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [6518.8974609375, -1330.87353515625], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1150, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 124, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [6062.8974609375, -1399.87353515625], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [], "pos": [6948.8974609375, -1650.87353515625], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {"collapsed": false}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 130, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6973.8974609375, -1469.87353515625], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 128, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6959.8974609375, -1252.87353515625], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 131, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [6524.8974609375, -1556.87353515625], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 132, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8283.8857421875, -1574.87353515625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 133, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [7969.8974609375, -1462.87353515625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 96, "outputs": [{"links": [611, 1160, 951], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2494.43505859375, -1510.82861328125], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"links": [455, 520, 971], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2209.7861328125, -1211.8885498046875], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [622, 664, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-1124.32275390625, -963.4298095703125], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [621, 647, 647, 944], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-1143.5081787109375, -1168.************], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 95, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2159.7138671875, -1607.7777099609375], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 127.99258422851562], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [1891.447265625, -1192.7852783203125], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 90, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [1912.1287841796875, -1025.************], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 93, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [1875.15283203125, -1434.1328125], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [1737.************, -1606.10498046875], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1183, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 91, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [1580.5706787109375, -1422.3043212890625], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 76, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1599.6937255859375, -1187.************], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 87, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1582.748779296875, -938.47998046875], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [472, 473], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [670.85205078125, -1021.9560546875], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 472, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 41, "outputs": [{"links": [1182], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1197.807861328125, -1068.64892578125], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 226], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1226.750732421875, -557.4791259765625], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1212.750732421875, -743.4803466796875], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [679.8088989257812, -737.42529296875], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 473, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 42, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2416.642578125, -892.2965698242188], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 45, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2381.642578125, -761.2966918945312], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 78, "outputs": [{"links": [514, 654, 947], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [510, 513, 630, 631, 1180, 1181], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [621.1154174804688, -1249.7822265625], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 946, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 69, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [607.7188720703125, -1644.330810546875], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [477.3377990722656, 128.31455993652344], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 75, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [607.9300537109375, -1440.7767333984375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 134, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7391.8974609375, -1376.87353515625], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 72, "outputs": [], "pos": [8334.912109375, -526.2022705078125], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [575, 786, 575, 1125], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2333.675048828125, -1062.658203125], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 44, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1560.52734375, -667.1412963867188], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 43, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1583.478515625, -566.14013671875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 62, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1792.49072265625, -689.1410522460938], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 71, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2042.1220703125, -693.1410522460938], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 61, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1863.5029296875, -559.14013671875], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 70, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2095.989013671875, -538.1401977539062], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 135, "outputs": [{"links": [856], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7805.9169921875, -901.202392578125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1151, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 125, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [7407.1708984375, -1615.321533203125], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 127, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [7701.1708984375, -1540.321533203125], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-355.901611328125, -1022.3419799804688], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 40, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-633.5529174804688, -725.13427734375], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {"collapsed": false}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 944, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 60, "outputs": [{"links": [946], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [-31.242746353149414, -717.9091186523438], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 948, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(0,4)}", "order": 84, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [59.612728118896484, -1090.4515380859375], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 16, "outputs": [{"links": [662, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-1121.5262451171875, -1387.24658203125], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 47, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-524.6895141601562, -1147.2415771484375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 39, "outputs": [{"links": [665, 968], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [265.62896728515625, -970.5693359375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 389, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"label": "INSTANTID", "links": [710], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [4393, -805.2838134765625], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"label": "FACEANALYSIS", "links": [711], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [4406.5166015625, -699.9271850585938], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 391, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"label": "CONTROL_NET", "links": [712], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [4435.88427734375, -610.9759521484375], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "flags": {}, "inputs": [{"link": 1136, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 1137, "name": "negative", "type": "CONDITIONING"}, {"link": 674, "name": "vae", "type": "VAE"}, {"link": 979, "name": "pixels", "type": "IMAGE"}, {"link": 676, "name": "mask", "type": "MASK"}], "mode": 0, "order": 117, "outputs": [{"links": [1134], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [1135], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [709], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [4979.54736328125, -550.9163208007812], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 397, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 955, "name": "clip", "type": "CLIP"}, {"link": 788, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 64, "outputs": [{"links": [715], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4117.23779296875, -790.20458984375], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 398, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": false}, "inputs": [{"link": 956, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 48, "outputs": [{"links": [716], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [3991.1611328125, -612.1043701171875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 399, "flags": {}, "inputs": [{"link": 684, "name": "faces", "type": "FACE"}], "mode": 0, "order": 106, "outputs": [{"links": [702, 720], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [3716.439208984375, -299.1335754394531], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 402, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1148, "name": "mask", "type": "MASK"}], "mode": 0, "order": 112, "outputs": [{"links": [676, 687, 690], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4301.9423828125, -254.13845825195312], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 32, 4, 0, 1, true]}, {"id": 403, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 689, "name": "mask", "type": "MASK"}], "mode": 0, "order": 110, "outputs": [{"links": [704], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4614.6142578125, -244.55067443847656], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 72, 40, 0, 1, true]}, {"id": 409, "flags": {"collapsed": false}, "inputs": [{"link": 989, "name": "image_ref", "type": "IMAGE"}, {"link": 1142, "name": "image_target", "type": "IMAGE"}], "mode": 0, "order": 120, "outputs": [{"links": [703], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [5279.58203125, -368.6326904296875], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "flags": {}, "inputs": [{"link": 1104, "name": "images", "type": "IMAGE"}, {"link": 702, "name": "face", "type": "FACE"}, {"link": 703, "name": "crop", "type": "IMAGE"}, {"link": 704, "name": "mask", "type": "MASK"}, {"link": 705, "name": "warp", "type": "WARP"}], "mode": 0, "order": 121, "outputs": [{"links": [781], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [4906.482421875, -279.61639404296875], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 411, "flags": {}, "inputs": [{"link": 706, "name": "model", "type": "MODEL"}, {"link": 1134, "name": "positive", "type": "CONDITIONING"}, {"link": 1135, "name": "negative", "type": "CONDITIONING"}, {"link": 709, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 118, "outputs": [{"links": [718], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [5269.8623046875, -863.2468872070312], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "fixed", 5, 1, "euler", "kl_optimal", 0.6]}, {"id": 412, "flags": {}, "inputs": [{"label": "instantid", "link": 710, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 711, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 712, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 713, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 954, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 715, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 716, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 980, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 115, "outputs": [{"label": "MODEL", "links": [706], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [1127], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [1128], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [4677.8056640625, -657.3215942382812], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 413, "flags": {"collapsed": false}, "inputs": [{"link": 718, "name": "samples", "type": "LATENT"}, {"link": 719, "name": "vae", "type": "VAE"}], "mode": 0, "order": 119, "outputs": [{"links": [1142], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5358.111328125, -481.2630310058594], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 414, "flags": {}, "inputs": [{"link": 720, "name": "faces", "type": "FACE"}], "mode": 0, "order": 107, "outputs": [{"links": [989, 1121, 1145], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [689, 689], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [705], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [4010.4345703125, -402.2294921875], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "flags": {}, "inputs": [], "mode": 0, "order": 20, "outputs": [{"links": [954], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [955, 956], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [674, 719, 1133], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [3718.30908203125, -840.8453979492188], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [314.34735107421875, 128.43458557128906], "type": "CheckpointLoaderSimple", "widgets_values": ["${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"]}, {"id": 436, "flags": {}, "inputs": [{"link": 1103, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 105, "outputs": [{"links": [684], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [3738.894775390625, -529.5217895507812], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 446, "flags": {}, "inputs": [{"link": 1071, "name": "faces", "type": "FACE"}], "mode": 0, "order": 94, "outputs": [{"links": [713, 762], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [5317.95458984375, -2141.50634765625], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "flags": {}, "inputs": [{"link": 1073, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 92, "outputs": [{"links": [1071], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [5064.96484375, -2149.68798828125], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 455, "flags": {}, "inputs": [{"link": 1160, "name": "", "type": "*"}], "mode": 0, "order": 97, "outputs": [{"links": [789, 1111, 789], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [2794.47265625, -1274.2305908203125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 456, "flags": {}, "inputs": [{"link": 781, "name": "", "type": "*"}], "mode": 0, "order": 122, "outputs": [{"links": [1107, 783], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [5190.1982421875, -171.50381469726562], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 457, "flags": {}, "inputs": [{"link": 786, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 46, "outputs": [{"links": [788, 788], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [3770.6259765625, -681.1192016601562], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 462, "flags": {}, "inputs": [], "mode": 0, "order": 35, "outputs": [{"links": [899, 957, 1001, 957], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3773.521728515625, -2166.29052734375], "properties": {"Node name for S&R": "LoadImage"}, "size": [235.8109893798828, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 474, "flags": {}, "inputs": [{"link": 813, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1161, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 57, "outputs": [{"links": [998, 1069], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4197.35693359375, -1760.59130859375], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 475, "flags": {}, "inputs": [], "mode": 0, "order": 36, "outputs": [{"links": [813], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4088.89013671875, -2179.81396484375], "properties": {"Node name for S&R": "LoadImage"}, "size": [268.51116943359375, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 476, "flags": {}, "inputs": [], "mode": 0, "order": 37, "outputs": [{"links": [1003, 1007, 1051, 1064, 1089], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4457.65234375, -2186.2099609375], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 477, "flags": {}, "inputs": [{"link": 1089, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1162, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 59, "outputs": [{"links": [1044, 1060, 1070], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4193.537109375, -1612.7928466796875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 508, "flags": {}, "inputs": [{"link": 947, "name": "model", "type": "MODEL"}, {"link": 879, "name": "pulid_flux", "type": "PULIDFLUX"}, {"link": 882, "name": "eva_clip", "type": "EVA_CLIP"}, {"link": 883, "name": "face_analysis", "type": "FACEANALYSIS"}, {"link": 966, "name": "image", "type": "IMAGE"}, {"name": "attn_mask", "shape": 7, "type": "MASK"}, {"name": "options", "shape": 7, "type": "OPTIONS"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 80, "outputs": [{"links": [948], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [33.53999710083008, -1483.230712890625], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "size": [315, 226], "type": "ApplyPulidFlux", "widgets_values": [0.8, 0, 1]}, {"id": 512, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 21, "outputs": [{"links": [879], "name": "PULIDFLUX", "type": "PULIDFLUX"}], "pos": [-616.6622924804688, -1513.3837890625], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "size": [315, 58], "type": "PulidFluxModelLoader", "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "flags": {"collapsed": false}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 22, "outputs": [{"links": [882], "name": "EVA_CLIP", "type": "EVA_CLIP"}], "pos": [-618.7819213867188, -1391.823486328125], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "size": [327.5999755859375, 26], "type": "PulidFluxEvaClipLoader", "widgets_values": []}, {"id": 515, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 23, "outputs": [{"links": [883], "name": "FACEANALYSIS", "type": "FACEANALYSIS"}], "pos": [-629.92724609375, -1297.8043212890625], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "size": [365.4000244140625, 58], "type": "PulidFluxInsightFaceLoader", "widgets_values": ["CUDA"]}, {"id": 525, "flags": {}, "inputs": [{"link": 899, "name": "image", "type": "IMAGE"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 55, "outputs": [{"name": "image", "type": "IMAGE"}, {"links": [960], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "type": "BBOX"}], "pos": [-968.95849609375, -1714.54443359375], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "size": [300, 260], "type": "easy humanSegmentation", "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 536, "flags": {}, "inputs": [{"link": 959, "name": "image", "type": "IMAGE"}, {"link": 960, "name": "mask", "type": "MASK"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 67, "outputs": [{"links": [966], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [-204.83697509765625, -1647.264892578125], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [236.27499389648438, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [96, 96, 96, 96]}, {"id": 537, "flags": {}, "inputs": [{"link": 957, "name": "", "type": "*"}], "mode": 0, "order": 56, "outputs": [{"links": [959], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-438.78076171875, -1683.8594970703125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 549, "flags": {}, "inputs": [{"link": 985, "name": "", "type": "*"}], "mode": 0, "order": 114, "outputs": [{"links": [979, 980, 1132], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4441.5126953125, -554.5872192382812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 550, "flags": {}, "inputs": [{"link": 1123, "name": "", "type": "*"}], "mode": 0, "order": 113, "outputs": [{"links": [985], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4180.76416015625, -906.14404296875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 559, "flags": {}, "inputs": [{"link": 1001, "name": "image1", "type": "IMAGE"}, {"link": 1069, "name": "image2", "type": "IMAGE"}, {"link": 1070, "name": "image3", "type": "IMAGE"}, {"name": "image4", "type": "IMAGE"}], "mode": 0, "order": 58, "outputs": [{"links": [1009], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [4528.984375, -1783.8060302734375], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "size": [140, 86], "type": "ImpactMakeImageList", "widgets_values": []}, {"id": 561, "flags": {}, "inputs": [{"link": 1009, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 68, "outputs": [{"links": [1092, 1093], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [4475.1279296875, -1605.535888671875], "properties": {"Node name for S&R": "ImageResize+"}, "size": [261.8576965332031, 224.5092315673828], "type": "ImageResize+", "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 570, "flags": {}, "inputs": [{"link": 1155, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1093, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": 0, "order": 74, "outputs": [{"links": [1084], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [4129.994140625, -1363.3128662109375], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [277.6741943359375, 319.97015380859375], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 574, "flags": {}, "inputs": [{"link": 1086, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 79, "outputs": [{"links": [1039], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4766.97509765625, -1133.2860107421875], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [289.79998779296875, 26], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 576, "flags": {}, "inputs": [{"link": 1037, "name": "image", "type": "IMAGE"}, {"link": 1040, "name": "mask", "type": "MASK"}], "mode": 0, "order": 86, "outputs": [{"links": [1072], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [4740.59912109375, -2150.749755859375], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [235.1999969482422, 150.37045288085938], "type": "Bounded Image Crop with Mask", "widgets_values": [64, 64, 64, 64]}, {"id": 579, "flags": {}, "inputs": [{"link": 1092, "name": "", "type": "*"}], "mode": 0, "order": 73, "outputs": [{"links": [1037], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4805.14599609375, -1606.9412841796875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 580, "flags": {}, "inputs": [{"link": 1039, "name": "mask", "type": "MASK"}], "mode": 0, "order": 83, "outputs": [{"links": [1040], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4809.68701171875, -1402.79443359375], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 592, "flags": {}, "inputs": [{"link": 1072, "name": "images", "type": "IMAGE"}], "mode": 0, "order": 89, "outputs": [{"links": [1073], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4832.5732421875, -1937.627197265625], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "size": [315, 26], "type": "easy imageListToImageBatch", "widgets_values": []}, {"id": 594, "flags": {}, "inputs": [{"link": 1111, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 98, "outputs": [{"links": [1099], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [2970.1611328125, -868.4249267578125], "properties": {"Node name for S&R": "ImageResize+"}, "size": [261.8576965332031, 224.5092315673828], "type": "ImageResize+", "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 597, "flags": {}, "inputs": [{"link": 1084, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 77, "outputs": [{"links": [1086], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"links": [], "name": "remained_SEGS", "slot_index": 1, "type": "SEGS"}], "pos": [4494.18310546875, -1262.16796875], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [210, 158.96408081054688], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 600, "flags": {}, "inputs": [{"link": 1156, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1100, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": 0, "order": 100, "outputs": [{"links": [1096], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [3365.37109375, -873.4877319335938], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [277.6741943359375, 319.97015380859375], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 601, "flags": {}, "inputs": [{"link": 1096, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 101, "outputs": [{"links": [1097], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"links": [], "name": "remained_SEGS", "slot_index": 1, "type": "SEGS"}], "pos": [2992.445068359375, -562.107421875], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [210, 158.96408081054688], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 602, "flags": {}, "inputs": [{"link": 1102, "name": "image", "type": "IMAGE"}, {"link": 1106, "name": "mask", "type": "MASK"}], "mode": 0, "order": 104, "outputs": [{"links": [1103, 1104], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [1109], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [3374.4775390625, -387.49652099609375], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [235.1999969482422, 150.37045288085938], "type": "Bounded Image Crop with Mask", "widgets_values": [64, 64, 64, 64]}, {"id": 603, "flags": {}, "inputs": [{"link": 1097, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 102, "outputs": [{"links": [1105], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [3347.696044921875, -507.6412353515625], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [203.30526733398438, 27.29315948486328], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 604, "flags": {}, "inputs": [{"link": 1099, "name": "", "type": "*"}], "mode": 0, "order": 99, "outputs": [{"links": [1100, 1102, 1108], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [3266.8720703125, -881.5452880859375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 605, "flags": {}, "inputs": [{"link": 1105, "name": "mask", "type": "MASK"}], "mode": 0, "order": 103, "outputs": [{"links": [1106], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [3011.666748046875, -366.3255615234375], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 608, "flags": {}, "inputs": [{"link": 1108, "name": "target", "type": "IMAGE"}, {"link": 1109, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 1107, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 123, "outputs": [{"links": [1150, 1151], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5312.66455078125, -152.3022918701172], "properties": {"Node name for S&R": "Bounded Image Blend"}, "size": [210, 135.17051696777344], "type": "Bounded Image Blend", "widgets_values": [1, 4]}, {"id": 610, "flags": {}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"label": "BBOX_DETECTOR", "links": [1119, 1154], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [3751.15673828125, -2575.84716796875], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 612, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"label": "clip", "link": 1112, "name": "clip", "type": "CLIP"}, {"link": 1125, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 50, "outputs": [{"label": "CONDITIONING", "links": [1117], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3779.155517578125, -3142.57373046875], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [311.57757568359375, 186.89004516601562], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["", "none", "A1111"]}, {"id": 613, "flags": {}, "inputs": [{"label": "clip", "link": 1113, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 51, "outputs": [{"label": "CONDITIONING", "links": [1118], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3736.486083984375, -2849.99609375], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [400, 200], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 614, "flags": {}, "inputs": [{"label": "image", "link": 1122, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 1114, "name": "model", "type": "MODEL"}, {"label": "clip", "link": 1115, "name": "clip", "type": "CLIP"}, {"label": "vae", "link": 1116, "name": "vae", "type": "VAE"}, {"label": "positive", "link": 1117, "name": "positive", "type": "CONDITIONING"}, {"label": "negative", "link": 1118, "name": "negative", "slot_index": 5, "type": "CONDITIONING"}, {"label": "bbox_detector", "link": 1119, "name": "bbox_detector", "slot_index": 6, "type": "BBOX_DETECTOR"}, {"label": "sam_model_opt", "link": 1157, "name": "sam_model_opt", "shape": 7, "slot_index": 7, "type": "SAM_MODEL"}, {"label": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "slot_index": 8, "type": "SEGM_DETECTOR"}, {"label": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK"}, {"name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC"}], "mode": 0, "order": 111, "outputs": [{"label": "image", "links": [1123], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "cropped_refined", "links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"label": "cropped_enhanced_alpha", "links": [], "name": "cropped_enhanced_alpha", "shape": 6, "slot_index": 2, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 3, "type": "MASK"}, {"label": "detailer_pipe", "name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"label": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": [4257.23876953125, -3428.75146484375], "properties": {"Node name for S&R": "FaceDetailer"}, "size": [350.5302734375, 902.3991088867188], "type": "FaceDetailer", "widgets_values": [384, true, 512, 393443463819939, "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 2, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 615, "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"label": "MODEL", "links": [1114], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "CLIP", "links": [1112, 1113, 1115], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}, {"label": "VAE", "links": [1116], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE"}], "pos": [3744.654052734375, -3407.46875], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [315, 98], "type": "CheckpointLoaderSimple", "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 616, "flags": {}, "inputs": [{"link": 1121, "name": "", "type": "*"}], "mode": 0, "order": 108, "outputs": [{"links": [1122], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4132.41357421875, -2412.88818359375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 618, "flags": {}, "inputs": [{"link": 1127, "name": "positive", "type": "CONDITIONING"}, {"link": 1128, "name": "negative", "type": "CONDITIONING"}, {"link": 1126, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1132, "name": "image", "type": "IMAGE"}, {"link": 1133, "name": "vae", "shape": 7, "type": "VAE"}], "mode": 0, "order": 116, "outputs": [{"links": [1136], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1137], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [4966.9052734375, -837.8536987304688], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "size": [243.52598571777344, 186], "type": "ControlNetApplyAdvanced", "widgets_values": [0.1, 0, 0.5]}, {"id": 619, "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [{"links": [1126], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [4352.673828125, -904.6546630859375], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [315, 58], "type": "ControlNetLoader", "widgets_values": ["TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"]}, {"id": 624, "flags": {"collapsed": false}, "inputs": [{"link": 1145, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 109, "outputs": [{"links": [], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"links": [1148], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "slot_index": 2, "type": "BBOX"}], "pos": [3948.153564453125, -155.76644897460938], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "size": [300, 500], "type": "easy humanSegmentation", "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 627, "flags": {}, "inputs": [{"link": 1154, "name": "", "type": "*"}], "mode": 0, "order": 49, "outputs": [{"links": [1155, 1156], "name": "", "slot_index": 0, "type": "BBOX_DETECTOR"}], "pos": [3830.228271484375, -1238.8267822265625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 628, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [1157], "name": "SAM_MODEL", "slot_index": 0, "type": "SAM_MODEL"}], "pos": [3766.485595703125, -2433.7041015625], "properties": {"Node name for S&R": "SAMLoader"}, "size": [315, 82], "type": "SAMLoader", "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"]}, {"id": 631, "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [1161], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3707.870361328125, -1688.587646484375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 632, "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [1162], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3733.91650390625, -1520.59716796875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 633, "flags": {}, "inputs": [], "mode": 0, "order": 32, "outputs": [{"links": [1166, 1167], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [580.6925659179688, -2673.3115234375], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [581.6453247070312, 58], "type": "ControlNetLoader", "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 634, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 34, "outputs": [{"links": [1163], "name": "IMAGE", "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [581.966552734375, -2553.326416015625], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${referenceImage}", "image"]}, {"id": 635, "bgcolor": "#535", "color": "#323", "flags": {}, "inputs": [], "mode": 0, "order": 30, "outputs": [{"links": [1164], "name": "INT", "slot_index": 0, "type": "INT", "widget": {"name": "width"}}], "pos": [635.9981689453125, -2166.799072265625], "properties": {"Run widget replace on values": false}, "size": [210, 82], "title": "width", "type": "PrimitiveNode", "widgets_values": [1344, "fixed"]}, {"id": 636, "bgcolor": "#535", "color": "#323", "flags": {}, "inputs": [], "mode": 0, "order": 31, "outputs": [{"links": [1165], "name": "INT", "slot_index": 0, "type": "INT", "widget": {"name": "height"}}], "pos": [638.7860717773438, -2018.697998046875], "properties": {"Run widget replace on values": false}, "size": [210, 82], "title": "height", "type": "PrimitiveNode", "widgets_values": [1792, "fixed"]}, {"id": 637, "flags": {}, "inputs": [{"link": 1163, "name": "image", "type": "IMAGE"}, {"link": 1164, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1165, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 54, "outputs": [{"links": [1168, 1169], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [951.579345703125, -2292.708251953125], "properties": {"Node name for S&R": "ImageScale"}, "size": [315, 130], "type": "ImageScale", "widgets_values": ["nearest-exact", 1344, 1792, "disabled"]}, {"id": 638, "flags": {}, "inputs": [], "mode": 0, "order": 33, "outputs": [{"links": [1170, 1171], "name": "VAE", "slot_index": 0, "type": "VAE"}], "pos": [1387.25341796875, -2682.68505859375], "properties": {"Node name for S&R": "VAELoader"}, "size": [315, 58], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 639, "flags": {}, "inputs": [{"link": 1166, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 52, "outputs": [{"links": [1176], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [1431.8035888671875, -2553.281982421875], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 59.9758758544922], "type": "SetUnionControlNetType", "widgets_values": ["openpose"]}, {"id": 640, "flags": {}, "inputs": [{"link": 1167, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 53, "outputs": [{"links": [1177], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [1434.1259765625, -2430.77392578125], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 58], "type": "SetUnionControlNetType", "widgets_values": ["depth"]}, {"id": 641, "flags": {}, "inputs": [{"link": 1168, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 65, "outputs": [{"links": [1174], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "pos": [1352.1402587890625, -2323.666015625], "properties": {"Node name for S&R": "DWPreprocessor"}, "size": [315, 198], "type": "DWPreprocessor", "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 642, "flags": {}, "inputs": [{"link": 1169, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 66, "outputs": [{"links": [1175], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [1358.7843017578125, -2073.822021484375], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "size": [315, 82], "type": "DepthAnythingPreprocessor", "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 644, "flags": {}, "inputs": [{"link": 1178, "name": "positive", "type": "CONDITIONING"}, {"link": 1179, "name": "negative", "type": "CONDITIONING"}, {"link": 1176, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1170, "name": "vae", "type": "VAE"}, {"link": 1174, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 85, "outputs": [{"links": [1172], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1173], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [1832.408203125, -2536.71044921875], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.6, 0, 0.2]}, {"id": 645, "flags": {}, "inputs": [{"link": 1172, "name": "positive", "type": "CONDITIONING"}, {"link": 1173, "name": "negative", "type": "CONDITIONING"}, {"link": 1177, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1171, "name": "vae", "type": "VAE"}, {"link": 1175, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 88, "outputs": [{"links": [1183], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "type": "CONDITIONING"}], "pos": [2261.227294921875, -2442.93994140625], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.5, 0, 0.2]}, {"id": 647, "flags": {}, "inputs": [{"link": 1180, "name": "clip", "type": "CLIP"}, {"link": 1182, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 81, "outputs": [{"links": [1178], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1727.30029296875, -2034.4564208984375], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [301.349365234375, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 648, "flags": {}, "inputs": [{"link": 1181, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 82, "outputs": [{"links": [1179], "name": "CONDITIONING", "type": "CONDITIONING"}], "pos": [1764.1572265625, -1893.5286865234375], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 76], "title": "CLIP Text Encode (Negative Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "614": 3}, "version": 0.4}}}}
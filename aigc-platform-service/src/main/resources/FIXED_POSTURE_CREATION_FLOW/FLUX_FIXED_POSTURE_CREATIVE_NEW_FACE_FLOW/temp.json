{"client_id": "${clientId}", "prompt": {"177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["283", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["574", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}, "class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": ["367", 0]}, "disable": "${isPureBg?then('false','true')}"}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["610", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["683", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["${isPromptCorrect?then(552,282)}", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,377)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "316": {"_meta": {"title": "conrain image composite masked"}, "class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,574)}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["574", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "378": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["508", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then('false','true')}"}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "389": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}}, "390": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}}, "391": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}}, "392": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["575", 1], "negative": ["412", 2], "noise_mask": true, "pixels": ["414", 0], "positive": ["412", 1], "vae": ["416", 2]}}, "397": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": ["331", 0]}}, "398": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}}, "399": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["436", 0], "order": "descending", "take_count": 1, "take_start": 0}}, "403": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 40, "enabled": true, "grow": 0, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["575", 1]}}, "409": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["414", 0], "image_target": ["413", 0], "method": "adain", "save_prefix": "ComfyUI"}}, "410": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["409", 0], "face": ["399", 0], "images": ["422", 0], "mask": ["403", 0], "warp": ["414", 2]}}, "411": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.6, "latent_image": ["392", 2], "model": ["412", 0], "negative": ["392", 1], "positive": ["392", 0], "sampler_name": "euler", "scheduler": "kl_optimal", "seed": "${seed}", "steps": 5}}, "412": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["391", 0], "end_at": 1, "image": ["446", 0], "image_kps": ["414", 0], "insightface": ["390", 0], "instantid": ["389", 0], "model": ["416", 0], "negative": ["398", 0], "positive": ["397", 0], "start_at": 0, "weight": 1}}, "413": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["411", 0], "vae": ["416", 2]}}, "414": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 1.5, "crop_size": 1024, "faces": ["399", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "416": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"}}, "417": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["590", 0], "target_size": ["420", 0]}}, "418": {"_meta": {"title": "Int"}, "class_type": "easy int", "inputs": {"value": 2048}}, "420": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["421", 0], "b": ["418", 0], "mode": false}}, "421": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["453", 0], "b": ["453", 1], "mode": true}}, "422": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["${isReactorAndInstantId?then('499','590')}", 0], "scale_by": ["417", 0], "upscale_method": "bicubic"}}, "429": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["435", 0], "b": ["435", 1], "mode": true}}, "430": {"_meta": {"title": "ImpactMinMax"}, "class_type": "ImpactMinMax", "inputs": {"a": ["434", 0], "b": ["429", 0], "mode": false}}, "431": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["469", 0], "target_size": ["430", 0]}}, "433": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["469", 0], "scale_by": ["431", 0], "upscale_method": "bicubic"}}, "434": {"_meta": {"title": "Int"}, "class_type": "easy int", "inputs": {"value": 2048}}, "435": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["469", 0]}}, "436": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["422", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "446": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["447", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "447": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["433", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "453": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["590", 0]}}, "462": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}}, "469": {"_meta": {"title": "Batch Images One or More"}, "class_type": "ImageBatchOneOrMore", "inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}}, "474": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["547", 0], "image1": ["475", 0]}}, "475": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "476": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "477": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["548", 0], "image1": ["476", 0]}}, "498": {"_meta": {"title": "Load ConrainReactor Models"}, "class_type": "LoadConrainReactorModels", "inputs": {"face_restore_model": "GFPGANv1.4.pth", "facedetection_model": "retinaface_resnet50", "parse_model": "parsenet", "swap_model": "inswapper_128.onnx"}, "disable": "${isReactorAndInstantId?then('false','true')}"}, "499": {"_meta": {"title": "ConrainReactor Fast Face Swap"}, "class_type": "ConrainReActorFaceSwap", "inputs": {"codeformer_weight": 0.7, "console_log_level": 1, "detect_gender_input": "no", "detect_gender_source": "no", "enabled": true, "face_restore_model": ["498", 2], "face_restore_visibility": 0.7, "facedetection": ["498", 1], "faceparse_model": ["498", 3], "input_faces_index": "0", "input_image": ["590", 0], "keep_largest": "yes", "source_faces_index": "0", "source_image": ["462", 0], "swap_model": ["498", 0]}, "disable": "${isReactorAndInstantId?then('false','true')}"}, "508": {"_meta": {"title": "Apply PuLID Flux"}, "class_type": "ApplyPulidFlux", "inputs": {"end_at": 1, "eva_clip": ["513", 0], "face_analysis": ["515", 0], "image": ["536", 0], "model": ["296", 0], "pulid_flux": ["512", 0], "start_at": 0, "weight": 0.8}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "512": {"_meta": {"title": "Load PuLID Flux Model"}, "class_type": "PulidFluxModelLoader", "inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "513": {"_meta": {"title": "Load <PERSON> (PuLID Flux)"}, "class_type": "PulidFluxEvaClipLoader", "inputs": {}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "515": {"_meta": {"title": "Load InsightFace (PuLID Flux)"}, "class_type": "PulidFluxInsightFaceLoader", "inputs": {"provider": "CUDA"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "525": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["462", 0], "mask_components": "1,3", "method": "selfie_multiclass_256x256"}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "536": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["462", 0], "mask": ["525", 1], "padding_bottom": 96, "padding_left": 96, "padding_right": 96, "padding_top": 96}, "disable": "${isPulidAndInstantId?then('false','true')}"}, "547": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}}, "548": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}}, "549": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": ": ", "text_a": ["550", 0], "text_b": ["282", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}, "550": {"_meta": {"title": "🔤 CR Text"}, "class_type": "CR Text", "inputs": {"text": "Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"}, "disable": "${isPromptCorrect?then('false','true')}"}, "552": {"_meta": {"title": "llmodel"}, "class_type": "LLModel", "inputs": {"llm_model": "default", "prompt": ["549", 0]}, "disable": "${isPromptCorrect?then('false','true')}"}, "557": {"_meta": {"title": "Inversed Switch (Any)"}, "class_type": "ImpactInversedSwitch", "inputs": {"input": ["701", 0], "select": ["564", 0]}}, "564": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["580", 0], "any_b": ["699", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\timport torch\n\tkk = int(torch.max(any_a)+1)\n\tif any_b==1:\n\t\tkk = 1\n\treturn [kk]"}}, "567": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["557", 0]}}, "568": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["567", 0]}}, "571": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["557", 1]}}, "572": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["571", 0]}}, "573": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["605", 0]}}, "574": {"_meta": {"title": "Invert Image"}, "class_type": "ImageInvert", "inputs": {"image": ["573", 0]}}, "575": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["414", 0], "mask_components": "13", "method": "human_parsing_lip"}}, "577": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["578", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["701", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}}, "578": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}}, "579": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["577", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}}, "580": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["579", 0]}}, "584": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 1, "enabled": true, "grow": 10, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["580", 0]}}, "590": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["572", 0], "mask": ["584", 0], "padding_bottom": 128, "padding_left": 128, "padding_right": 128, "padding_top": 128}}, "602": {"_meta": {"title": "Bounded Image Blend with Mask"}, "class_type": "Bounded Image Blend with Mask", "inputs": {"blend_factor": 1, "feathering": 0, "source": ["410", 0], "target": ["572", 0], "target_bounds": ["590", 1], "target_mask": ["645", 0]}}, "605": {"_meta": {"title": "Switch (Any)"}, "class_type": "ImpactSwitch", "inputs": {"input1": ["568", 0], "input2": ["602", 0], "sel_mode": false, "select": ["564", 0]}}, "610": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "645": {"_meta": {"title": "Grow Mask With Blur"}, "class_type": "GrowMaskWithBlur", "inputs": {"blur_radius": 0, "decay_factor": 1, "expand": 64, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 0.99, "mask": ["584", 0], "tapered_corners": false}}, "652": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["660", 0], "end_percent": 0.2, "image": ["657", 0], "negative": ["653", 1], "positive": ["653", 0], "start_percent": 0, "strength": 0.5, "vae": ["658", 0]}}, "653": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["659", 0], "end_percent": 0.2, "image": ["716", 0], "negative": ["662", 0], "positive": ["661", 0], "start_percent": 0, "strength": 0.6, "vae": ["658", 0]}}, "654": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}}, "656": {"_meta": {"title": "DWPose Estimator"}, "class_type": "DWPreprocessor", "inputs": {"bbox_detector": "yolox_l.onnx", "detect_body": "enable", "detect_face": "enable", "detect_hand": "enable", "image": ["715", 0], "pose_estimator": "dw-ll_ucoco_384.onnx", "resolution": 1024}}, "657": {"_meta": {"title": "Depth Anything"}, "class_type": "DepthAnythingPreprocessor", "inputs": {"ckpt_name": "depth_anything_vits14.pth", "image": ["715", 0], "resolution": 1024}}, "658": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "659": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["654", 0], "type": "openpose"}}, "660": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["654", 0], "type": "depth"}}, "661": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["681", 0], "text": ["283", 0]}}, "662": {"_meta": {"title": "CLIP Text Encode (Negative Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["681", 0], "text": ""}}, "681": {"_meta": {"title": "Inversed Switch (Any)"}, "class_type": "ImpactInversedSwitch", "inputs": {"input": ["296", 1], "sel_mode": false, "select": ["700", 0]}}, "683": {"_meta": {"title": "Switch (Any)"}, "class_type": "ImpactSwitch", "inputs": {"input1": ["652", 0], "input2": ["684", 0], "sel_mode": false, "select": ["700", 0]}}, "684": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["681", 1], "text": ["282", 0]}}, "692": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${referenceImage!FACE.extInfo['faceImage']}", "upload": "image"}}, "699": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "${(noshowFace)?then(1, 0)}"}}, "700": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "${(isFixedPose)?then(1, 2)}"}}, "701": {"_meta": {"title": "Image Batch To Image List"}, "class_type": "easy imageBatchToImageList", "inputs": {"image": ["269", 0]}}, "703": {"_meta": {"title": "conrain save image v2"}, "class_type": "ConrainImageSaveV2", "inputs": {"dpi": 100, "embed_workflow": "false", "ext_info": ["704", 0], "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "704": {"_meta": {"title": "String"}, "class_type": "easy string", "inputs": {"value": "${userAdditionalInfo}"}}, "706": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["692", 0]}}, "707": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["706", 0], "any_b": ["706", 1], "any_c": ["285", 0], "any_d": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),"}}, "708": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["707", 0], "expression": "a[0][0]"}}, "709": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["707", 0], "expression": "a[0][1]"}}, "710": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["707", 0], "expression": "a[1][0]"}}, "711": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["707", 0], "expression": "a[1][1]"}}, "712": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 16777215, "height": ["709", 0], "width": ["708", 0]}}, "714": {"_meta": {"title": "🔧 Image Composite"}, "class_type": "ImageComposite+", "inputs": {"destination": ["712", 0], "offset_x": 0, "offset_y": 0, "source": ["692", 0], "x": ["710", 0], "y": ["711", 0]}}, "715": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "downscale if bigger", "height": 1024, "image": ["720", 0], "interpolation": "nearest", "method": "keep proportion", "multiple_of": 0, "width": 1024}}, "716": {"_meta": {"title": "Get Image Size & Count"}, "class_type": "GetImageSizeAndCount", "inputs": {"image": ["656", 0]}}, "717": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["706", 1], "b": ["285", 0], "c": ["284", 0], "expression": "b/c*a"}}, "718": {"_meta": {"title": "🔧 Image Crop"}, "class_type": "ImageCrop+", "inputs": {"height": ["706", 1], "image": ["692", 0], "position": "center", "width": ["717", 0], "x_offset": 0, "y_offset": 0}}, "719": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["708", 0], "any_b": ["706", 0], "any_c": ["699", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    no_head = any_c==1\n    return any_a==any_b and no_head,"}}, "720": {"_meta": {"title": "Conrain If"}, "class_type": "ConrainIfExecute", "inputs": {"ANY": ["719", 0], "IF_FALSE": ["714", 0], "IF_TRUE": ["718", 0]}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [3342.618271110461, 7008.697859524549], "scale": 0.1}}, "groups": [{"id": 3, "bounding": [6623.3046875, -1681.5994873046875, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 5, "bounding": [829.9994506835938, -1838.8150634765625, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [6604.279296875, -995.303955078125, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-834.1985473632812, -1870.67333984375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}, {"id": 8, "bounding": [4593.40673828125, -1039.6580810546875, 1951.62158203125, 797.1675415039062], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 9, "bounding": [4627.36669921875, -178.8089141845703, 1918.953857421875, 535.1592407226562], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "再把人脸贴回去"}, {"id": 10, "bounding": [4617.59619140625, -1903.5914306640625, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 11, "bounding": [4138.9306640625, -1893.307861328125, 416.799560546875, 1684.5103759765625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "限制最大尺寸"}, {"id": 12, "bounding": [786.358642578125, -3616.400146484375, 3279.14013671875, 1631.1348876953125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "controlnet"}], "last_link_id": 1284, "last_node_id": 720, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [455, 270, 0, 269, 1, "VAE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [672, 412, 1, 392, 0, "CONDITIONING"], [673, 412, 2, 392, 1, "CONDITIONING"], [674, 416, 2, 392, 2, "VAE"], [675, 414, 0, 392, 3, "IMAGE"], [684, 436, 0, 399, 0, "FACE"], [701, 422, 0, 410, 0, "IMAGE"], [702, 399, 0, 410, 1, "FACE"], [704, 403, 0, 410, 3, "MASK"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [707, 392, 0, 411, 1, "CONDITIONING"], [708, 392, 1, 411, 2, "CONDITIONING"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [717, 414, 0, 412, 7, "IMAGE"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [722, 420, 0, 417, 1, "INT"], [724, 421, 0, 420, 0, "*"], [725, 418, 0, 420, 1, "*"], [726, 453, 0, 421, 0, "*"], [727, 453, 1, 421, 1, "*"], [729, 417, 0, 422, 1, "FLOAT"], [739, 435, 0, 429, 0, "*"], [740, 435, 1, 429, 1, "*"], [741, 434, 0, 430, 0, "*"], [742, 429, 0, 430, 1, "*"], [744, 430, 0, 431, 1, "INT"], [748, 431, 0, 433, 1, "FLOAT"], [750, 422, 0, 436, 0, "IMAGE"], [760, 447, 0, 446, 0, "FACE"], [774, 455, 0, 453, 0, "IMAGE"], [776, 455, 0, 417, 0, "IMAGE"], [782, 456, 0, 236, 0, "IMAGE"], [783, 456, 0, 365, 0, "IMAGE"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [803, 462, 0, 469, 0, "IMAGE"], [806, 474, 0, 469, 1, "IMAGE"], [807, 469, 0, 435, 0, "IMAGE"], [808, 469, 0, 431, 0, "IMAGE"], [809, 469, 0, 433, 0, "IMAGE"], [812, 433, 0, 447, 0, "IMAGE"], [813, 475, 0, 474, 0, "IMAGE"], [814, 477, 0, 469, 2, "IMAGE"], [815, 476, 0, 477, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [859, 462, 0, 499, 5, "IMAGE"], [860, 498, 0, 499, 1, "FACE_MODEL"], [861, 498, 1, 499, 2, "FACE_MODEL"], [862, 498, 2, 499, 3, "FACE_MODEL"], [863, 498, 3, 499, 4, "FACE_MODEL"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [879, 512, 0, 508, 1, "PULIDFLUX"], [882, 513, 0, 508, 2, "EVA_CLIP"], [883, 515, 0, 508, 3, "FACEANALYSIS"], [899, 462, 0, 525, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [947, 296, 0, 508, 0, "MODEL"], [948, 508, 0, 378, 0, "MODEL"], [953, 499, 0, 422, 0, "IMAGE"], [954, 416, 0, 412, 4, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [957, 462, 0, 537, 0, "*"], [959, 537, 0, 536, 0, "IMAGE"], [960, 525, 1, 536, 1, "MASK"], [966, 536, 0, 508, 4, "IMAGE"], [978, 547, 0, 474, 2, "INT"], [979, 548, 0, 477, 2, "INT"], [980, 550, 0, 549, 0, "STRING"], [981, 282, 0, 549, 1, "STRING"], [982, 549, 0, 552, 2, "STRING"], [983, 552, 0, 553, 0, "*"], [990, 283, 0, 287, 0, "*"], [991, 455, 0, 499, 0, "IMAGE"], [1008, 557, 0, 567, 0, "IMAGE"], [1009, 567, 0, 568, 0, "IMAGE"], [1013, 564, 0, 557, 2, "INT"], [1014, 557, 1, 571, 0, "IMAGE"], [1015, 571, 0, 572, 0, "IMAGE"], [1028, 573, 0, 574, 0, "IMAGE"], [1029, 574, 0, 456, 0, "*"], [1030, 564, 0, 563, 1, "INT"], [1032, 414, 0, 575, 0, "IMAGE"], [1033, 575, 1, 403, 0, "MASK"], [1037, 578, 0, 577, 0, "BBOX_DETECTOR"], [1039, 577, 0, 579, 0, "SEGS"], [1040, 579, 0, 580, 0, "SEGS"], [1045, 580, 0, 564, 0, "*"], [1046, 580, 0, 584, 0, "MASK"], [1055, 572, 0, 590, 0, "IMAGE"], [1056, 584, 0, 590, 1, "MASK"], [1057, 590, 0, 455, 0, "*"], [1078, 410, 0, 602, 3, "IMAGE"], [1081, 590, 1, 602, 2, "IMAGE_BOUNDS"], [1084, 572, 0, 602, 0, "IMAGE"], [1090, 605, 0, 573, 0, "IMAGE"], [1092, 568, 0, 605, 0, "IMAGE"], [1094, 564, 0, 605, 2, "INT"], [1097, 602, 0, 605, 1, "IMAGE"], [1100, 610, 0, 273, 0, "NOISE"], [1135, 628, 0, 629, 1, "MASK"], [1136, 627, 0, 629, 0, "MASK"], [1139, 575, 1, 392, 4, "MASK"], [1140, 575, 1, 627, 0, "MASK"], [1141, 575, 1, 628, 0, "MASK"], [1161, 584, 0, 645, 0, "MASK"], [1163, 645, 0, 602, 1, "MASK"], [1166, 409, 0, 410, 2, "IMAGE"], [1173, 413, 0, 409, 1, "IMAGE"], [1174, 653, 0, 652, 0, "CONDITIONING"], [1175, 653, 1, 652, 1, "CONDITIONING"], [1176, 660, 0, 652, 2, "CONTROL_NET"], [1177, 658, 0, 652, 3, "VAE"], [1178, 657, 0, 652, 4, "IMAGE"], [1179, 661, 0, 653, 0, "CONDITIONING"], [1180, 662, 0, 653, 1, "CONDITIONING"], [1181, 659, 0, 653, 2, "CONTROL_NET"], [1182, 658, 0, 653, 3, "VAE"], [1187, 654, 0, 659, 0, "CONTROL_NET"], [1188, 654, 0, 660, 0, "CONTROL_NET"], [1193, 283, 0, 661, 1, "STRING"], [1204, 414, 0, 409, 0, "IMAGE"], [1218, 296, 1, 681, 0, "*"], [1219, 681, 0, 661, 0, "CLIP"], [1220, 681, 0, 662, 0, "CLIP"], [1221, 652, 0, 683, 0, "*"], [1222, 683, 0, 278, 0, "CONDITIONING"], [1223, 681, 1, 684, 0, "CLIP"], [1224, 684, 0, 683, 1, "CONDITIONING"], [1227, 282, 0, 684, 1, "STRING"], [1236, 699, 0, 564, 1, "*"], [1237, 700, 0, 681, 1, "INT"], [1238, 700, 0, 683, 3, "INT"], [1241, 269, 0, 701, 0, "IMAGE"], [1242, 701, 0, 577, 1, "IMAGE"], [1243, 701, 0, 557, 0, "IMAGE"], [1245, 553, 0, 283, 0, "STRING"], [1246, 355, 0, 703, 0, "IMAGE"], [1247, 177, 0, 703, 1, "STRING"], [1248, 177, 1, 703, 2, "STRING"], [1249, 704, 0, 703, 3, "STRING"], [1250, 692, 0, 705, 0, "*"], [1251, 705, 0, 706, 0, "IMAGE"], [1252, 706, 0, 707, 0, "*"], [1253, 706, 1, 707, 1, "*"], [1254, 285, 0, 707, 2, "*"], [1255, 284, 0, 707, 3, "*"], [1256, 707, 0, 711, 0, "INT,FLOAT"], [1257, 707, 0, 710, 0, "INT,FLOAT"], [1258, 707, 0, 709, 0, "INT,FLOAT"], [1259, 707, 0, 708, 0, "INT,FLOAT"], [1260, 708, 0, 712, 0, "INT"], [1261, 709, 0, 712, 1, "INT"], [1262, 710, 0, 714, 3, "INT"], [1263, 711, 0, 714, 4, "INT"], [1264, 715, 0, 656, 0, "IMAGE"], [1265, 715, 0, 657, 0, "IMAGE"], [1266, 712, 0, 714, 0, "IMAGE"], [1267, 705, 0, 714, 1, "IMAGE"], [1269, 656, 0, 716, 0, "IMAGE"], [1270, 716, 0, 653, 4, "IMAGE"], [1271, 705, 0, 718, 0, "IMAGE"], [1272, 717, 0, 718, 1, "INT"], [1273, 706, 1, 718, 2, "INT"], [1274, 706, 1, 717, 0, "INT,FLOAT"], [1275, 285, 0, 717, 1, "INT,FLOAT"], [1276, 284, 0, 717, 2, "INT,FLOAT"], [1277, 708, 0, 719, 0, "*"], [1278, 706, 0, 719, 1, "*"], [1279, 699, 0, 719, 2, "*"], [1280, 720, 0, 715, 0, "IMAGE"], [1282, 719, 0, 720, 0, "*"], [1283, 714, 0, 720, 2, "*"], [1284, 718, 0, 720, 1, "*"]], "nodes": [{"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [394, 1247], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [539, 1248], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [7194.92919921875, -732.3036499023438], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 94, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [8321.927734375, -586.3036499023438], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6821.92919921875, -519.3036499023438], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6799.92919921875, -713.3036499023438], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 44, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7676.92919921875, -765.3036499023438], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7187.92919921875, -861.3037109375], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 155, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [7196.4765625, -1258.371337890625], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 782, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 153, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [6740.4765625, -1327.371337890625], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [], "pos": [7626.4765625, -1578.371337890625], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {"collapsed": false}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 158, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7681.2646484375, -1403.2314453125], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 157, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7637.4765625, -1180.371337890625], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 159, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [7202.4765625, -1484.371337890625], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 160, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8961.462890625, -1502.371337890625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 161, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [8647.4755859375, -1390.371337890625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 116, "outputs": [{"links": [611, 1241, 1054], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2624.9423828125, -1517.4549560546875], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"links": [455, 520, 971], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2667.205078125, -1320.5389404296875], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [622, 664, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-764.33056640625, -1052.938232421875], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [621, 647, 647, 944], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-752.4918212890625, -1244.5938720703125], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 1100, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 115, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2621.************, -1760.2235107421875], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [292.29705810546875, 127.71533966064453], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [2199.3603515625, -1288.************], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 105, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [2220.41748046875, -1120.************], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 114, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [2747.447265625, -806.1827392578125], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1222, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 113, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [2455.56201171875, -1403.1966552734375], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 83, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1907.6065673828125, -1282.8275146484375], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 102, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1890.66162109375, -1033.9232177734375], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [981, 473, 1227], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [928.5354614257812, -1096.3232421875], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 1245, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 84, "outputs": [{"links": [990, 1193], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1588.3983154296875, -1258.232666015625], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 326], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 38, "outputs": [{"links": [587, 589, 1255, 1276], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1534.663330078125, -652.92236328125], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 37, "outputs": [{"links": [590, 591, 1254, 1275], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1520.663330078125, -838.9239501953125], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [950.3558959960938, -797.952392578125], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 990, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 89, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2725.3623046875, -987.7400512695312], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 47, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2690.3623046875, -856.7403564453125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 85, "outputs": [{"links": [514, 654, 947], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [510, 513, 630, 631, 1218], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [925.7359008789062, -1267.852783203125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 946, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 69, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [912.2755126953125, -1716.1956787109375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [587.3407592773438, 126], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 76, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [930.5007934570312, -1497.79345703125], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 162, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8069.4765625, -1304.371337890625], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 101, "outputs": [], "pos": [8892.9248046875, -514.3036499023438], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [575, 786, 575], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2649.163330078125, -1170.912109375], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 58, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1867.965576171875, -762.8110961914062], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 59, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1891.8251953125, -661.7979125976562], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 66, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2100.837890625, -784.8086547851562], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 73, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2350.3671875, -788.80859375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 67, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2171.849853515625, -654.7978515625], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 74, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2404.2451171875, -633.797607421875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 163, "outputs": [{"links": [1246], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8363.927734375, -889.303955078125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 783, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 154, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [8085.1630859375, -1542.8193359375], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 156, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [8379.1513671875, -1467.8193359375], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-8.453181266784668, -1142.7919921875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 46, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-286.10430908203125, -845.451416015625], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {"collapsed": false}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 944, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 61, "outputs": [{"links": [946], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [316.2055358886719, -838.3591918945312], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 948, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(0,4)}", "order": 95, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [407.61126708984375, -1210.9014892578125], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 13, "outputs": [{"links": [662, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-754.51904296875, -1447.7342529296875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 49, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-177.24099731445312, -1267.6915283203125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 45, "outputs": [{"links": [665, 968], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [613.77392578125, -1091.194091796875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 389, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"label": "INSTANTID", "links": [710], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [5359.44921875, -948.9583129882812], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"label": "FACEANALYSIS", "links": [711], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [5360.625, -878.750732421875], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 391, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 16, "outputs": [{"label": "CONTROL_NET", "links": [712], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [5361.6298828125, -808.7156982421875], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "flags": {}, "inputs": [{"link": 672, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 673, "name": "negative", "type": "CONDITIONING"}, {"link": 674, "name": "vae", "type": "VAE"}, {"link": 675, "name": "pixels", "type": "IMAGE"}, {"link": 1139, "name": "mask", "type": "MASK"}], "mode": 0, "order": 143, "outputs": [{"links": [707], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [708], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [709], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [5998.6494140625, -993.1511840820312], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 397, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 955, "name": "clip", "type": "CLIP"}, {"link": 788, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 62, "outputs": [{"links": [715], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5027.8212890625, -959.9161376953125], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 398, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": true}, "inputs": [{"link": 956, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 50, "outputs": [{"links": [716], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [5036.47509765625, -790.58447265625], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 399, "flags": {}, "inputs": [{"link": 684, "name": "faces", "type": "FACE"}], "mode": 0, "order": 138, "outputs": [{"links": [702, 720], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [4645.92724609375, -427.2978515625], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 403, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1033, "name": "mask", "type": "MASK"}], "mode": 0, "order": 142, "outputs": [{"links": [704], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [5904.244140625, -706.970703125], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 0, 40, 0, 1, true]}, {"id": 409, "flags": {"collapsed": false}, "inputs": [{"link": 1204, "name": "image_ref", "type": "IMAGE"}, {"link": 1173, "name": "image_target", "type": "IMAGE"}], "mode": 0, "order": 146, "outputs": [{"links": [1166], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [4706.23681640625, 66.1480941772461], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "flags": {"collapsed": false}, "inputs": [{"link": 701, "name": "images", "type": "IMAGE"}, {"link": 702, "name": "face", "type": "FACE"}, {"link": 1166, "name": "crop", "type": "IMAGE"}, {"link": 704, "name": "mask", "type": "MASK"}, {"link": 705, "name": "warp", "type": "WARP"}], "mode": 0, "order": 147, "outputs": [{"links": [1078], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [5002.67626953125, 46.47186279296875], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 411, "flags": {}, "inputs": [{"link": 706, "name": "model", "type": "MODEL"}, {"link": 707, "name": "positive", "type": "CONDITIONING"}, {"link": 708, "name": "negative", "type": "CONDITIONING"}, {"link": 709, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 144, "outputs": [{"links": [718], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [6184.869140625, -875.563720703125], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "fixed", 5, 1, "euler", "kl_optimal", 0.6]}, {"id": 412, "flags": {}, "inputs": [{"label": "instantid", "link": 710, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 711, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 712, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 713, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 954, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 715, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 716, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 717, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 140, "outputs": [{"label": "MODEL", "links": [706], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [672], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [673], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [5613.408203125, -939.6572265625], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 413, "flags": {"collapsed": false}, "inputs": [{"link": 718, "name": "samples", "type": "LATENT"}, {"link": 719, "name": "vae", "type": "VAE"}], "mode": 0, "order": 145, "outputs": [{"links": [1173], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [6378.17822265625, -558.2137451171875], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 414, "flags": {}, "inputs": [{"link": 720, "name": "faces", "type": "FACE"}], "mode": 0, "order": 139, "outputs": [{"links": [675, 717, 1032, 1204], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [705], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [4933.75146484375, -425.5395202636719], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 1.5, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "flags": {}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"links": [954], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [955, 956], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [674, 719], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [4631.3994140625, -925.1694946289062], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [254.65628051757812, 146.35491943359375], "type": "CheckpointLoaderSimple", "widgets_values": ["${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"]}, {"id": 417, "flags": {}, "inputs": [{"link": 776, "name": "image", "type": "IMAGE"}, {"link": 722, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 135, "outputs": [{"links": [729], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [4219.96875, -345.91033935546875], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 113.94757080078125], "type": "UpscaleSizeCalculator", "widgets_values": [2048]}, {"id": 418, "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"links": [725], "name": "int", "slot_index": 0, "type": "INT"}], "pos": [4211.83154296875, -719.6390380859375], "properties": {"Node name for S&R": "easy int"}, "size": [210, 63.99684524536133], "type": "easy int", "widgets_values": [2048]}, {"id": 420, "flags": {}, "inputs": [{"link": 724, "name": "a", "type": "*"}, {"link": 725, "name": "b", "type": "*"}], "mode": 0, "order": 134, "outputs": [{"links": [722], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4216.3291015625, -528.667724609375], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 85.42294311523438], "type": "ImpactMinMax", "widgets_values": [false]}, {"id": 421, "flags": {}, "inputs": [{"link": 726, "name": "a", "type": "*"}, {"link": 727, "name": "b", "type": "*"}], "mode": 0, "order": 133, "outputs": [{"links": [724], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4214.6884765625, -884.8746948242188], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 78], "type": "ImpactMinMax", "widgets_values": [true]}, {"id": 422, "flags": {"collapsed": false}, "inputs": [{"link": 953, "name": "image", "type": "IMAGE"}, {"link": 729, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 136, "outputs": [{"links": [701, 750], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4640.6513671875, -594.8142700195312], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 82], "type": "ImageScaleBy", "widgets_values": ["bicubic", 0.5]}, {"id": 429, "flags": {"collapsed": true}, "inputs": [{"link": 739, "name": "a", "type": "*"}, {"link": 740, "name": "b", "type": "*"}], "mode": 0, "order": 77, "outputs": [{"links": [742], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [5908.54833984375, -1414.25634765625], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 82.59356689453125], "type": "ImpactMinMax", "widgets_values": [true]}, {"id": 430, "flags": {"collapsed": true}, "inputs": [{"link": 741, "name": "a", "type": "*"}, {"link": 742, "name": "b", "type": "*"}], "mode": 0, "order": 86, "outputs": [{"links": [744], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [5908.54833984375, -1354.25390625], "properties": {"Node name for S&R": "ImpactMinMax"}, "size": [210, 85.89134216308594], "type": "ImpactMinMax", "widgets_values": [false]}, {"id": 431, "flags": {"collapsed": true}, "inputs": [{"link": 808, "name": "image", "type": "IMAGE"}, {"link": 744, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 92, "outputs": [{"links": [748], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [5778.54833984375, -1274.247802734375], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 94], "type": "UpscaleSizeCalculator", "widgets_values": [2048]}, {"id": 433, "flags": {"collapsed": true}, "inputs": [{"link": 809, "name": "image", "type": "IMAGE"}, {"link": 748, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 99, "outputs": [{"links": [812], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5778.54833984375, -1204.24169921875], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 82], "type": "ImageScaleBy", "widgets_values": ["bicubic", 0.5]}, {"id": 434, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"links": [741], "name": "int", "slot_index": 0, "type": "INT"}], "pos": [5768.54833984375, -1344.25390625], "properties": {"Node name for S&R": "easy int"}, "size": [210, 63.99684524536133], "type": "easy int", "widgets_values": [2048]}, {"id": 435, "flags": {"collapsed": true}, "inputs": [{"link": 807, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 70, "outputs": [{"links": [739], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [740], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "slot_index": 2, "type": "INT"}], "pos": [5728.54833984375, -1404.25634765625], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 436, "flags": {}, "inputs": [{"link": 750, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 137, "outputs": [{"links": [684], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [4930.37255859375, -620.9642333984375], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 446, "flags": {}, "inputs": [{"link": 760, "name": "faces", "type": "FACE"}], "mode": 0, "order": 106, "outputs": [{"links": [713, 762], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [6276.72705078125, -1762.4124755859375], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "flags": {}, "inputs": [{"link": 812, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 103, "outputs": [{"links": [760], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [6001.6455078125, -1759.6707763671875], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 453, "flags": {}, "inputs": [{"link": 774, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 131, "outputs": [{"links": [726], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [727], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [4204.9345703125, -1005.6846313476562], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 455, "flags": {}, "inputs": [{"link": 1057, "name": "", "type": "*"}], "mode": 0, "order": 130, "outputs": [{"links": [774, 776, 789, 991], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4450.54541015625, -979.43212890625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 456, "flags": {}, "inputs": [{"link": 1029, "name": "", "type": "*"}], "mode": 0, "order": 152, "outputs": [{"links": [782, 783], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [6088.88037109375, -206.9046173095703], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 457, "flags": {}, "inputs": [{"link": 786, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 48, "outputs": [{"links": [788, 788], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [4684.6123046875, -721.5924682617188], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 462, "flags": {}, "inputs": [], "mode": 0, "order": 20, "outputs": [{"links": [803, 859, 899, 957], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4764.619140625, -1796.65185546875], "properties": {"Node name for S&R": "LoadImage"}, "size": [235.8109893798828, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 469, "flags": {}, "inputs": [{"link": 803, "name": "image1", "type": "IMAGE"}, {"link": 806, "name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 814, "name": "image3", "shape": 7, "type": "IMAGE"}, {"name": "image4", "shape": 7, "type": "IMAGE"}, {"name": "image5", "shape": 7, "type": "IMAGE"}, {"name": "image6", "shape": 7, "type": "IMAGE"}], "mode": 0, "order": 64, "outputs": [{"links": [807, 808, 809], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5481.10107421875, -1334.54931640625], "properties": {"Node name for S&R": "ImageBatchOneOrMore"}, "size": [201.60000610351562, 126], "type": "ImageBatchOneOrMore", "widgets_values": []}, {"id": 474, "flags": {}, "inputs": [{"link": 813, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 978, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 53, "outputs": [{"links": [806], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [5082.220703125, -1353.22216796875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 475, "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [{"links": [813], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [5096.56591796875, -1794.5908203125], "properties": {"Node name for S&R": "LoadImage"}, "size": [249.60922241210938, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 476, "flags": {}, "inputs": [], "mode": 0, "order": 22, "outputs": [{"links": [815], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [5436.12548828125, -1775.562744140625], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 477, "flags": {}, "inputs": [{"link": 815, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 979, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 54, "outputs": [{"links": [814], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [5072.220703125, -1213.22216796875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 498, "flags": {}, "inputs": [], "mode": "${isReactorAndInstantId?then('0','4')}", "order": 23, "outputs": [{"links": [860], "name": "faceswapper_model", "slot_index": 0, "type": "FACE_MODEL"}, {"links": [861], "name": "facedetection_model", "slot_index": 1, "type": "FACE_MODEL"}, {"links": [862], "name": "facerestore_model", "slot_index": 2, "type": "FACE_MODEL"}, {"links": [863], "name": "faceparse_model", "slot_index": 3, "type": "FACE_MODEL"}], "pos": [4180.31396484375, -2307.305908203125], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "size": [327.5999755859375, 190], "type": "LoadConrainReactorModels", "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 499, "flags": {}, "inputs": [{"link": 991, "name": "input_image", "type": "IMAGE"}, {"link": 860, "name": "swap_model", "type": "FACE_MODEL"}, {"link": 861, "name": "facedetection", "type": "FACE_MODEL"}, {"link": 862, "name": "face_restore_model", "type": "FACE_MODEL"}, {"link": 863, "name": "faceparse_model", "type": "FACE_MODEL"}, {"link": 859, "name": "source_image", "shape": 7, "type": "IMAGE"}, {"name": "face_model", "shape": 7, "type": "FACE_MODEL"}], "mode": "${isReactorAndInstantId?then('0','4')}", "order": 132, "outputs": [{"links": [953], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "FACE_MODEL", "slot_index": 1, "type": "FACE_MODEL"}], "pos": [4588.84765625, -2330.515869140625], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "size": [367.79998779296875, 370], "type": "ConrainReActorFaceSwap", "widgets_values": [true, 0.7, 0.7, "no", "no", "0", "0", 1, "yes"]}, {"id": 508, "flags": {}, "inputs": [{"link": 947, "name": "model", "type": "MODEL"}, {"link": 879, "name": "pulid_flux", "type": "PULIDFLUX"}, {"link": 882, "name": "eva_clip", "type": "EVA_CLIP"}, {"link": 883, "name": "face_analysis", "type": "FACEANALYSIS"}, {"link": 966, "name": "image", "type": "IMAGE"}, {"name": "attn_mask", "shape": 7, "type": "MASK"}, {"name": "options", "shape": 7, "type": "OPTIONS"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 90, "outputs": [{"links": [948], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [380.9883728027344, -1603.6806640625], "properties": {"Node name for S&R": "ApplyPulidFlux"}, "size": [315, 226], "type": "ApplyPulidFlux", "widgets_values": [0.8, 0, 1]}, {"id": 512, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 41, "outputs": [{"links": [879], "name": "PULIDFLUX", "type": "PULIDFLUX"}], "pos": [-269.21380615234375, -1633.833740234375], "properties": {"Node name for S&R": "PulidFluxModelLoader"}, "size": [315, 58], "type": "PulidFluxModelLoader", "widgets_values": ["pulid_flux_v0.9.1.safetensors"]}, {"id": 513, "flags": {"collapsed": false}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 42, "outputs": [{"links": [882], "name": "EVA_CLIP", "type": "EVA_CLIP"}], "pos": [-271.33343505859375, -1512.2734375], "properties": {"Node name for S&R": "PulidFluxEvaClipLoader"}, "size": [327.5999755859375, 26], "type": "PulidFluxEvaClipLoader", "widgets_values": []}, {"id": 515, "flags": {}, "inputs": [], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 43, "outputs": [{"links": [883], "name": "FACEANALYSIS", "type": "FACEANALYSIS"}], "pos": [-282.47869873046875, -1418.2542724609375], "properties": {"Node name for S&R": "PulidFluxInsightFaceLoader"}, "size": [365.4000244140625, 58], "type": "PulidFluxInsightFaceLoader", "widgets_values": ["CUDA"]}, {"id": 525, "flags": {}, "inputs": [{"link": 899, "name": "image", "type": "IMAGE"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 51, "outputs": [{"name": "image", "type": "IMAGE"}, {"links": [960], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "type": "BBOX"}], "pos": [-644.8892822265625, -1792.3453369140625], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [1, 3]}, "size": [300, 260], "type": "easy humanSegmentation", "widgets_values": ["selfie_multiclass_256x256", 0.4, 0, "1,3"]}, {"id": 536, "flags": {}, "inputs": [{"link": 959, "name": "image", "type": "IMAGE"}, {"link": 960, "name": "mask", "type": "MASK"}], "mode": "${isPulidAndInstantId?then('0','4')}", "order": 63, "outputs": [{"links": [966], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [142.61143493652344, -1767.71484375], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [236.27499389648438, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [96, 96, 96, 96]}, {"id": 537, "flags": {}, "inputs": [{"link": 957, "name": "", "type": "*"}], "mode": 0, "order": 52, "outputs": [{"links": [959], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-91.33226776123047, -1804.3094482421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 547, "flags": {}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"links": [978], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4676.986328125, -1352.8480224609375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 548, "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"links": [979], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [4655.41796875, -1198.3968505859375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 549, "flags": {}, "inputs": [{"link": 980, "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}}, {"link": 981, "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}}, {"name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}}, {"name": "text_d", "shape": 7, "type": "STRING", "widget": {"name": "text_d"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 60, "outputs": [{"links": [982], "name": "STRING", "slot_index": 0, "type": "STRING"}], "pos": [1607.827392578125, -1749.5113525390625], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [213.69569396972656, 179.2432861328125], "type": "Text Concatenate", "widgets_values": [": ", "true", "", "", "", ""]}, {"id": 550, "flags": {}, "inputs": [], "mode": "${isPromptCorrect?then('0','4')}", "order": 40, "outputs": [{"links": [980], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "slot_index": 1, "type": "STRING"}], "pos": [935.2557373046875, -1927.9835205078125], "properties": {"Node name for S&R": "CR Text"}, "size": [415.5107116699219, 126.7047348022461], "type": "CR Text", "widgets_values": ["Please optimize this prompt by resolving any conflicts and redundancies, and return only the optimized version"]}, {"id": 552, "flags": {}, "inputs": [{"name": "image_list", "shape": 7, "type": "IMAGE"}, {"name": "ref_image", "shape": 7, "type": "IMAGE"}, {"link": 982, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 68, "outputs": [{"links": [983], "name": "result_text", "slot_index": 0, "type": "STRING"}, {"name": "result_detail", "type": "STRING"}], "pos": [1924.7591552734375, -1732.1910400390625], "properties": {"Node name for S&R": "LLModel"}, "size": [254.6305389404297, 154.52664184570312], "type": "LLModel", "widgets_values": ["你能干嘛", "default"]}, {"id": 553, "flags": {}, "inputs": [{"link": 983, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": "${isPromptCorrect?then('0','4')}", "order": 75, "outputs": [{"links": [1245], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [1470.8182373046875, -1355.843505859375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 557, "flags": {}, "inputs": [{"link": 1243, "name": "input", "type": "IMAGE"}, {"name": "sel_mode", "shape": 7, "type": "IMAGE", "widget": {"name": "sel_mode"}}, {"link": 1013, "name": "select", "type": "INT", "widget": {"name": "select"}}], "mode": 0, "order": 123, "outputs": [{"links": [1008], "name": "output1", "slot_index": 0, "type": "IMAGE"}, {"links": [1014], "name": "output2", "slot_index": 1, "type": "IMAGE"}, {"name": "output3", "type": "IMAGE"}], "pos": [3904.572998046875, -1771.973388671875], "properties": {"Node name for S&R": "ImpactInversedSwitch"}, "size": [210, 74], "type": "ImpactInversedSwitch", "widgets_values": [1, false]}, {"id": 564, "flags": {}, "inputs": [{"link": 1045, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1236, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 121, "outputs": [{"links": [1013, 1030, 1094], "name": "any", "slot_index": 0, "type": "*"}], "pos": [3506.775634765625, -1420.552734375], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\timport torch\n\tkk = int(torch.max(any_a)+1)\n\tif any_b==1:\n\t\tkk = 1\n\treturn [kk]"]}, {"id": 567, "flags": {}, "inputs": [{"link": 1008, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 125, "outputs": [{"links": [1009], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [3914.385498046875, -1362.9998779296875], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 568, "flags": {}, "inputs": [{"link": 1009, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 127, "outputs": [{"links": [1092], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [3900.605712890625, -1136.447509765625], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 571, "flags": {}, "inputs": [{"link": 1014, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 126, "outputs": [{"links": [1015], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4244.26220703125, -1753.230712890625], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 572, "flags": {}, "inputs": [{"link": 1015, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 128, "outputs": [{"links": [1055, 1084], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4243.45556640625, -1603.160400390625], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 573, "flags": {}, "inputs": [{"link": 1090, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 150, "outputs": [{"links": [1028], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [6324.1806640625, -79.88534545898438], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 574, "flags": {}, "inputs": [{"link": 1028, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 151, "outputs": [{"links": [1029], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [6325.7275390625, 44.76059341430664], "properties": {"Node name for S&R": "ImageInvert"}, "size": [210, 26], "type": "ImageInvert", "widgets_values": []}, {"id": 575, "flags": {}, "inputs": [{"link": 1032, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 141, "outputs": [{"links": [], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"links": [1033, 1139, 1140, 1141], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "type": "BBOX"}], "pos": [5285.59765625, -758.28466796875], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "size": [300, 500], "type": "easy humanSegmentation", "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 577, "flags": {}, "inputs": [{"link": 1037, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1242, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": 0, "order": 118, "outputs": [{"links": [1039], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [3126.39501953125, -1564.343994140625], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [315, 310], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 578, "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [{"links": [1037], "name": "BBOX_DETECTOR", "slot_index": 0, "type": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR"}], "pos": [3126.**********, -1785.18017578125], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [340.20001220703125, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 579, "flags": {}, "inputs": [{"link": 1039, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 119, "outputs": [{"links": [1040], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"name": "remained_SEGS", "type": "SEGS"}], "pos": [3505.20751953125, -1794.609375], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [315, 150], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 580, "flags": {}, "inputs": [{"link": 1040, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 120, "outputs": [{"links": [1045, 1046, 1082], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [3508.232421875, -1531.214599609375], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [289.79998779296875, 26], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 584, "flags": {}, "inputs": [{"link": 1046, "name": "mask", "type": "MASK"}], "mode": 0, "order": 122, "outputs": [{"links": [1056, 1161], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4202.44677734375, -1463.525634765625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [315, 178], "type": "MaskFastGrow", "widgets_values": [false, 10, 1, 0, 1, true]}, {"id": 590, "flags": {}, "inputs": [{"link": 1055, "name": "image", "type": "IMAGE"}, {"link": 1056, "name": "mask", "type": "MASK"}], "mode": 0, "order": 129, "outputs": [{"links": [1057], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [1081], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [4190.95703125, -1224.7315673828125], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [352.79998779296875, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [128, 128, 128, 128]}, {"id": 602, "flags": {"collapsed": true}, "inputs": [{"link": 1084, "name": "target", "type": "IMAGE"}, {"link": 1163, "name": "target_mask", "type": "MASK"}, {"link": 1081, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 1078, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 148, "outputs": [{"links": [1097], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5702.6669921875, 13.602551460266113], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "size": [365.4000244140625, 142], "type": "Bounded Image Blend with Mask", "widgets_values": [1, 0]}, {"id": 605, "flags": {}, "inputs": [{"link": 1092, "name": "input1", "shape": 7, "type": "IMAGE"}, {"link": 1097, "name": "input2", "type": "IMAGE"}, {"link": 1094, "name": "select", "type": "INT", "widget": {"name": "select"}}, {"name": "input3", "type": "IMAGE"}], "mode": 0, "order": 149, "outputs": [{"label": "IMAGE", "links": [1090], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "selected_label", "type": "STRING"}, {"name": "selected_index", "type": "INT"}], "pos": [6062.93017578125, -83.62200927734375], "properties": {"Node name for S&R": "ImpactSwitch"}, "size": [210, 118], "type": "ImpactSwitch", "widgets_values": [1, false]}, {"id": 610, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [1100], "name": "NOISE", "slot_index": 0, "type": "NOISE"}], "pos": [2269.305908203125, -1766.2166748046875], "properties": {"Node name for S&R": "RandomNoise"}, "size": [315, 82], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 645, "flags": {}, "inputs": [{"link": 1161, "name": "mask", "type": "MASK"}], "mode": 0, "order": 124, "outputs": [{"links": [1163], "name": "mask", "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "type": "MASK"}], "pos": [5299.2001953125, -31.485000610351562], "properties": {"Node name for S&R": "GrowMaskWithBlur"}, "size": [315, 246], "type": "GrowMaskWithBlur", "widgets_values": [64, 0, false, false, 0, 0.99, 1, false]}, {"id": 652, "flags": {}, "inputs": [{"link": 1174, "name": "positive", "type": "CONDITIONING"}, {"link": 1175, "name": "negative", "type": "CONDITIONING"}, {"link": 1176, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1177, "name": "vae", "type": "VAE"}, {"link": 1178, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 111, "outputs": [{"links": [1221], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "type": "CONDITIONING"}], "pos": [3645.36865234375, -2796.296630859375], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.5, 0, 0.2]}, {"id": 653, "flags": {}, "inputs": [{"link": 1179, "name": "positive", "type": "CONDITIONING"}, {"link": 1180, "name": "negative", "type": "CONDITIONING"}, {"link": 1181, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1182, "name": "vae", "type": "VAE"}, {"link": 1270, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 110, "outputs": [{"links": [1174], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1175], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [3386.283935546875, -3172.769287109375], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.6, 0, 0.2]}, {"id": 654, "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [1187, 1188], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [1771.7567138671875, -3519.367431640625], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [577.4558715820312, 58.57819366455078], "type": "ControlNetLoader", "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 656, "flags": {}, "inputs": [{"link": 1264, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 107, "outputs": [{"links": [1269], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "pos": [2694.86181640625, -2985.199462890625], "properties": {"Node name for S&R": "DWPreprocessor"}, "size": [315, 198], "type": "DWPreprocessor", "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 657, "flags": {}, "inputs": [{"link": 1265, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 108, "outputs": [{"links": [1178], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2937.15771484375, -2456.539306640625], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "size": [315, 82], "type": "DepthAnythingPreprocessor", "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 658, "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [1177, 1182], "name": "VAE", "slot_index": 0, "type": "VAE"}], "pos": [2438.595703125, -3517.5810546875], "properties": {"Node name for S&R": "VAELoader"}, "size": [315, 58], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 659, "flags": {}, "inputs": [{"link": 1187, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 55, "outputs": [{"links": [1181], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [2493.7822265625, -3407.330810546875], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 58], "type": "SetUnionControlNetType", "widgets_values": ["openpose"]}, {"id": 660, "flags": {}, "inputs": [{"link": 1188, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 56, "outputs": [{"links": [1176], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [2493.560302734375, -3291.578125], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 58], "type": "SetUnionControlNetType", "widgets_values": ["depth"]}, {"id": 661, "flags": {}, "inputs": [{"link": 1219, "name": "clip", "type": "CLIP"}, {"link": 1193, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 96, "outputs": [{"links": [1179], "name": "CONDITIONING", "type": "CONDITIONING"}], "pos": [3255.923583984375, -2292.935302734375], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 57.11636734008789], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 662, "flags": {}, "inputs": [{"link": 1220, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 97, "outputs": [{"links": [1180], "name": "CONDITIONING", "type": "CONDITIONING"}], "pos": [3355.289306640625, -2202.133544921875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 76], "title": "CLIP Text Encode (Negative Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 681, "flags": {}, "inputs": [{"link": 1218, "name": "input", "type": "CLIP"}, {"link": 1237, "name": "select", "type": "INT", "widget": {"name": "select"}}], "mode": 0, "order": 91, "outputs": [{"links": [1219, 1220], "name": "output1", "slot_index": 0, "type": "CLIP"}, {"links": [1223], "name": "output2", "slot_index": 1, "type": "CLIP"}, {"name": "output3", "type": "CLIP"}], "pos": [1552.491943359375, -1486.2423095703125], "properties": {"Node name for S&R": "ImpactInversedSwitch"}, "size": [210, 122], "type": "ImpactInversedSwitch", "widgets_values": [1, false]}, {"id": 683, "flags": {}, "inputs": [{"link": 1221, "name": "input1", "shape": 7, "type": "CONDITIONING"}, {"link": 1224, "name": "input2", "type": "CONDITIONING"}, {"name": "input3", "type": "CONDITIONING"}, {"link": 1238, "name": "select", "type": "INT", "widget": {"name": "select"}}], "mode": 0, "order": 112, "outputs": [{"label": "CONDITIONING", "links": [1222], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}, {"name": "selected_label", "type": "STRING"}, {"name": "selected_index", "type": "INT"}], "pos": [2199.91162109375, -1455.70703125], "properties": {"Node name for S&R": "ImpactSwitch"}, "size": [210, 122], "type": "ImpactSwitch", "widgets_values": [1, false]}, {"id": 684, "flags": {}, "inputs": [{"link": 1223, "name": "clip", "type": "CLIP"}, {"link": 1227, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 98, "outputs": [{"links": [1224], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1797.925048828125, -1424.54248046875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 57.11636734008789], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 688, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 30, "outputs": [], "pos": [463.56463623046875, -2100.2578125], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["启用固定姿势创作，输入1.不启用，输入2"]}, {"id": 692, "flags": {}, "inputs": [], "mode": 0, "order": 31, "outputs": [{"links": [1250], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [840.7416381835938, -3397.8447265625], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${referenceImage!FACE.extInfo['faceImage']}", "image"]}, {"id": 695, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 32, "outputs": [], "pos": [887.2567138671875, -3518.56982421875], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["这里需要垫图，进去，就算不启用也需要穿一张图进去，comfy ui启动前都会检测这些"]}, {"id": 696, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 33, "outputs": [], "pos": [5715.1357421875, -1762.945068359375], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["三张人脸也需要垫图，需要有图片传入，不然不用的话也会报错"]}, {"id": 697, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 34, "outputs": [], "pos": [3136.867919921875, -1039.258056640625], "properties": {}, "size": [314.1627502441406, 109.24244689941406], "type": "Note", "widgets_values": ["无头场景value=1，其他场景0"]}, {"id": 699, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 39, "outputs": [{"links": [1236, 1279], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3138.83056640625, -1173.9619140625], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [260.7904052734375, 58], "type": "JWStringToInteger", "widgets_values": ["${(noshowFace)?then(1, 0)}"]}, {"id": 700, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 35, "outputs": [{"links": [1237, 1238], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [424.6300048828125, -1975.6888427734375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [315, 58], "type": "JWStringToInteger", "widgets_values": ["${(isFixedPose)?then(1, 2)}"]}, {"id": 701, "flags": {}, "inputs": [{"link": 1241, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 117, "outputs": [{"links": [1242, 1243], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [2863.47021484375, -1533.1336669921875], "properties": {"Node name for S&R": "easy imageBatchToImageList"}, "size": [210, 26], "type": "easy imageBatchToImageList", "widgets_values": []}, {"id": 703, "flags": {}, "inputs": [{"link": 1246, "name": "images", "type": "IMAGE"}, {"link": 1247, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"link": 1248, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}, {"link": 1249, "name": "ext_info", "shape": 7, "type": "STRING", "widget": {"name": "ext_info"}}], "mode": 0, "order": 164, "outputs": [{"name": "image_cnt", "type": "INT"}], "pos": [8948.5048828125, -901.4288940429688], "properties": {"Node name for S&R": "ConrainImageSaveV2"}, "size": [315, 298], "type": "ConrainImageSaveV2", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true", "{\"deviceInfo\": \"iPhone\"}"]}, {"id": 704, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 36, "outputs": [{"links": [1249], "name": "string", "slot_index": 0, "type": "STRING"}], "pos": [8625.2705078125, -738.89892578125], "properties": {"Node name for S&R": "easy string"}, "size": [210, 58.217201232910156], "type": "easy string", "widgets_values": ["${userAdditionalInfo}"]}, {"id": 705, "flags": {}, "inputs": [{"link": 1250, "name": "", "type": "*"}], "mode": 0, "order": 57, "outputs": [{"links": [1251, 1267, 1271], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [1497.1873779296875, -3115.681640625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 706, "flags": {}, "inputs": [{"link": 1251, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 65, "outputs": [{"links": [1252, 1278], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1253, 1273, 1274], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [892.8004150390625, -2880.66845703125], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 707, "flags": {}, "inputs": [{"link": 1252, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1253, "name": "any_b", "shape": 7, "type": "*"}, {"link": 1254, "name": "any_c", "shape": 7, "type": "*"}, {"link": 1255, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 71, "outputs": [{"links": [1256, 1257, 1258, 1259], "name": "any", "slot_index": 0, "type": "*"}], "pos": [894.1156616210938, -2646.623291015625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),"]}, {"id": 708, "flags": {}, "inputs": [{"link": 1259, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 81, "outputs": [{"links": [1260, 1277], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [1397.4970703125, -2851.95458984375], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [210, 98], "type": "MathExpressionPlus", "widgets_values": ["a[0][0]"]}, {"id": 709, "flags": {}, "inputs": [{"link": 1258, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 80, "outputs": [{"links": [1261], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [1391.5926513671875, -2691.157958984375], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [210, 98], "type": "MathExpressionPlus", "widgets_values": ["a[0][1]"]}, {"id": 710, "flags": {}, "inputs": [{"link": 1257, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 79, "outputs": [{"links": [1262], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [1398.6280517578125, -2501.444091796875], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [210, 98], "type": "MathExpressionPlus", "widgets_values": ["a[1][0]"]}, {"id": 711, "flags": {}, "inputs": [{"link": 1256, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 78, "outputs": [{"links": [1263], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [1396.1710205078125, -2334.51025390625], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [210, 98], "type": "MathExpressionPlus", "widgets_values": ["a[1][1]"]}, {"id": 712, "flags": {}, "inputs": [{"link": 1260, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1261, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 87, "outputs": [{"links": [1266], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [1701.57861328125, -2822.7724609375], "properties": {"Node name for S&R": "EmptyImage"}, "size": [315, 130], "type": "EmptyImage", "widgets_values": [512, 512, 1, 16777215]}, {"id": 714, "flags": {}, "inputs": [{"link": 1266, "name": "destination", "type": "IMAGE"}, {"link": 1267, "name": "source", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}, {"link": 1262, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1263, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 93, "outputs": [{"links": [1283], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [1707.2451171875, -2447.477294921875], "properties": {"Node name for S&R": "ImageComposite+"}, "size": [315, 170], "type": "ImageComposite+", "widgets_values": [0, 0, 0, 0]}, {"id": 715, "flags": {}, "inputs": [{"link": 1280, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 104, "outputs": [{"links": [1264, 1265], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [2272.36083984375, -2688.710693359375], "properties": {"Node name for S&R": "ImageResize+"}, "size": [315, 218], "type": "ImageResize+", "widgets_values": [1024, 1024, "nearest", "keep proportion", "downscale if bigger", 0]}, {"id": 716, "flags": {}, "inputs": [{"link": 1269, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 109, "outputs": [{"links": [1270], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [3053.13671875, -3009.24658203125], "properties": {"Node name for S&R": "GetImageSizeAndCount"}, "size": [277.20001220703125, 86], "type": "GetImageSizeAndCount", "widgets_values": []}, {"id": 717, "flags": {}, "inputs": [{"link": 1274, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"link": 1275, "name": "b", "shape": 7, "type": "INT,FLOAT"}, {"link": 1276, "name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 72, "outputs": [{"links": [1272], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [1610.528564453125, -3336.166015625], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [210, 98], "type": "MathExpressionPlus", "widgets_values": ["b/c*a"]}, {"id": 718, "flags": {}, "inputs": [{"link": 1271, "name": "image", "type": "IMAGE"}, {"link": 1272, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1273, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 82, "outputs": [{"links": [1284], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "x", "type": "INT"}, {"name": "y", "type": "INT"}], "pos": [1933.931884765625, -3176.19775390625], "properties": {"Node name for S&R": "ImageCrop+"}, "size": [315, 194], "type": "ImageCrop+", "widgets_values": [256, 256, "center", 0, 0]}, {"id": 719, "flags": {}, "inputs": [{"link": 1277, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1278, "name": "any_b", "shape": 7, "type": "*"}, {"link": 1279, "name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 88, "outputs": [{"links": [1282], "name": "any", "slot_index": 0, "type": "*"}], "pos": [1719.7210693359375, -2209.364013671875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [302.3248291015625, 159.2863311767578], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    no_head = any_c==1\n    return any_a==any_b and no_head,"]}, {"id": 720, "flags": {}, "inputs": [{"link": 1282, "name": "ANY", "type": "*"}, {"link": 1284, "name": "IF_TRUE", "type": "*"}, {"link": 1283, "name": "IF_FALSE", "type": "*"}], "mode": 0, "order": 100, "outputs": [{"links": [1280], "name": "?", "slot_index": 0, "type": "*"}], "pos": [2181.170166015625, -2230.229248046875], "properties": {"Node name for S&R": "ConrainIfExecute"}, "size": [210, 66], "type": "ConrainIfExecute"}], "seed_widgets": {"282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "610": 0}, "version": 0.4}}}}
{"client_id": "${clientId}", "prompt": {"1": {"inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "4": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "resize", "rescale_factor": 1, "resize_width": "${resizeWidth?number}", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["1", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "8": {"inputs": {"output_path": ["10", 0], "filename_prefix": ["10", 1], "extension": "png", "dpi": 300, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["28", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "10": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "28": {"inputs": {"upscale_method": "bicubic", "width": "${originWidth?number}", "height": "${originHeight?number}", "crop": "disabled", "image": ["4", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 28, "last_link_id": 24, "nodes": [{"id": 8, "type": "ConrainImageSave", "pos": [1641, 150], "size": [320, 270], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 24, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 22, "widget": {"name": "output_path"}}, {"name": "filename_prefix", "type": "STRING", "link": 23, "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 300, 100, "true", "false", "false", "true", "true"]}, {"id": 1, "type": "LoadImage", "pos": [537, 146], "size": [240, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 4, "type": "CR Upscale Image", "pos": [803, 146], "size": [380, 260], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 13, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [20], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, "${resizeWidth?number}", "lanc<PERSON>s", "true", 8]}, {"id": 28, "type": "ImageScale", "pos": [1210, 147], "size": [315, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 20}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [24], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", "${originWidth?number}", "${originHeight?number}", "disabled"]}, {"id": 10, "type": "Text String", "pos": [1224, 347], "size": [360, 190], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [22], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [23], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": null, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": null, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}], "links": [[13, 1, 0, 4, 0, "IMAGE"], [20, 4, 0, 28, 0, "IMAGE"], [22, 10, 0, 8, 1, "STRING"], [23, 10, 1, 8, 2, "STRING"], [24, 28, 0, 8, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9646149645000013, "offset": [-224.47767837689497, 135.23518921101856]}}, "version": 0.4, "widget_idx_map": {}, "seed_widgets": {}}}}}
{"client_id": "${clientId}", "prompt": {"136": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "137": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "138": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "200": {"inputs": {"samples": ["201", 0], "vae": ["137", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "201": {"inputs": {"noise": ["373", 0], "guider": ["204", 0], "sampler": ["202", 0], "sigmas": ["203", 0], "latent_image": ["209", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "202": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "203": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": "${denoised?number}", "model": ["330", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "204": {"inputs": {"model": ["330", 0], "conditioning": ["214", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "207": {"inputs": {"text": ["261", 0], "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "208": {"inputs": {"guidance": 3.5, "conditioning": ["207", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "209": {"inputs": {"noise_mask": true, "positive": ["208", 0], "negative": ["210", 0], "vae": ["137", 0], "pixels": ["368", 0], "mask": ["222", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "210": {"inputs": {"text": "", "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "213": {"inputs": {"switch_1": "On", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "start_percent_1": 0, "end_percent_1": 0.2, "switch_2": "On", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_2": 0.1, "start_percent_2": 0.2, "end_percent_2": 0.6, "switch_3": "Off", "controlnet_3": "None", "controlnet_strength_3": 1, "start_percent_3": 0, "end_percent_3": 1, "image_1": ["215", 0], "image_2": ["215", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "214": {"inputs": {"switch": "On", "base_positive": ["209", 0], "base_negative": ["210", 0], "controlnet_stack": ["213", 0]}, "class_type": "CR Apply Multi-ControlNet", "_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "215": {"inputs": {"resolution": 512, "image": ["411", 0]}, "class_type": "Zoe-DepthMapPreprocessor", "_meta": {"title": "<PERSON>"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "219": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "220": {"inputs": {"output_path": ["219", 0], "filename_prefix": ["219", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["${(isSwapFace)?then(200,368)}", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "222": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["451", 4]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "224": {"inputs": {"torchscript_jit": "on", "image": ["238", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "229": {"inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "目标图片"}}, "232": {"inputs": {"width": 1536, "height": 1536, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 1, "image": ["229", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "233": {"inputs": {"text": "1536", "anything": ["232", 2]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "234": {"inputs": {"image": ["232", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "235": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["234", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "236": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["234", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "238": {"inputs": {"width": ["235", 0], "height": ["236", 0], "position": "top-left", "x_offset": 0, "y_offset": 0, "image": ["232", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "🔧 Image Crop"}}, "250": {"inputs": {"lora_name": "${FACE.extInfo.faceLora}", "strength_model": 1, "strength_clip": "1", "model": ["136", 0], "clip": ["138", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "261": {"inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 188}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "模特描述"}, "disable": "${(!isSwapFace)?then('true','false')}"}, "326": {"inputs": {"noise": ["373", 0], "guider": ["329", 0], "sampler": ["331", 0], "sigmas": ["332", 0], "latent_image": ["333", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "327": {"inputs": {"samples": ["326", 0], "vae": ["137", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "329": {"inputs": {"model": ["330", 0], "conditioning": ["333", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "330": {"inputs": {"model": ["${(isSwapFace)?then(250,136)}", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "331": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "332": {"inputs": {"scheduler": "simple", "steps": 10, "denoise": 0.5, "model": ["330", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "333": {"inputs": {"noise_mask": true, "positive": ["334", 0], "negative": ["335", 0], "vae": ["137", 0], "pixels": ["348", 0], "mask": ["340", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "334": {"inputs": {"guidance": 3.5, "conditioning": ["336", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "335": {"inputs": {"text": "", "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "336": {"inputs": {"text": "empty scene, landscape only, (realistic:1.15)", "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "340": {"inputs": {"expand": 20, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 50, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["353", 0]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "348": {"inputs": {"mask_threshold": 200, "gaussblur_radius": 20, "invert_mask": false, "images": ["350", 0], "masks": ["353", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Big lama Remover"}}, "350": {"inputs": {"width": 512, "height": 768, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 1, "image": ["362", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "351": {"inputs": {"torchscript_jit": "on", "image": ["350", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "353": {"inputs": {"expand": 5, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["351", 1]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "362": {"inputs": {"image": "${customSceneImg}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "参考背景"}}, "368": {"inputs": {"samples": ["369", 0], "vae": ["137", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "369": {"inputs": {"noise": ["373", 0], "guider": ["372", 0], "sampler": ["370", 0], "sigmas": ["371", 0], "latent_image": ["388", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "370": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "371": {"inputs": {"scheduler": "simple", "steps": 10, "denoise": 0.4, "model": ["330", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "372": {"inputs": {"model": ["374", 0], "conditioning": ["377", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "373": {"inputs": {"noise_seed": "${seed}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "374": {"inputs": {"model": ["330", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "375": {"inputs": {"text": "", "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "376": {"inputs": {"guidance": 3.5, "conditioning": ["375", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "377": {"inputs": {"noise_mask": true, "positive": ["376", 0], "negative": ["378", 0], "vae": ["137", 0], "pixels": ["411", 0], "mask": ["387", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "378": {"inputs": {"text": "", "clip": ["${(isSwapFace)?then(250,138)}", "${(isSwapFace)?then(1,0)?number}"]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "387": {"inputs": {"expand": -5, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 5, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["224", 1]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "388": {"inputs": {"amount": 1, "samples": ["377", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "408": {"inputs": {"upscale_method": "lanc<PERSON>s", "width": ["235", 0], "height": ["236", 0], "crop": "disabled", "image": ["428", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "411": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["238", 0], "destination": ["408", 0], "mask": ["224", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "421": {"inputs": {"face_mask": true, "background_mask": false, "hair_mask": true, "body_mask": false, "clothes_mask": false, "confidence": 0.3, "images": ["350", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "426": {"inputs": {"mask": ["421", 0]}, "class_type": "IsMaskEmpty", "_meta": {"title": "IsMaskEmpty"}}, "428": {"inputs": {"ANY": ["426", 0], "IF_TRUE": ["362", 0], "IF_FALSE": ["327", 0]}, "class_type": "ConrainIfExecute", "_meta": {"title": "Conrain If"}}, "450": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "451": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["450", 0], "image": ["238", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 451, "last_link_id": 893, "nodes": [{"id": 136, "type": "UNETLoader", "pos": [-1349.504150390625, 156.21701049804688], "size": [315, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [508], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"]}, {"id": 137, "type": "VAELoader", "pos": [-1341.004638671875, 741.2391967773438], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 138, "type": "DualCLIPLoader", "pos": [-1349.504150390625, 310.2170715332031], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [468], "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux"]}, {"id": 214, "type": "CR Apply Multi-ControlNet", "pos": [542.4715576171875, 1787.08837890625], "size": [274.56842041015625, 98], "flags": {}, "order": 84, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "base_positive", "type": "CONDITIONING", "link": 426}, {"name": "base_negative", "type": "CONDITIONING", "link": 427}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": 428}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [414], "slot_index": 0, "shape": 3}, {"name": "base_neg", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 218, "type": "Reroute", "pos": [-1222.689208984375, 1947.343017578125], "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 822}], "outputs": [{"name": "", "type": "CLIP", "links": [416, 422]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 230, "type": "Reroute", "pos": [-1115.68310546875, -436.527587890625], "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 881}], "outputs": [{"name": "", "type": "IMAGE", "links": [441], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 232, "type": "ImageResize+", "pos": [-1017.68310546875, -435.527587890625], "size": [315, 218], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [453, 492], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": [442], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 1]}, {"id": 234, "type": "GetImageSize+", "pos": [-657.68310546875, -352.527587890625], "size": [214.20001220703125, 66], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 453}], "outputs": [{"name": "width", "type": "INT", "links": [604], "slot_index": 0}, {"name": "height", "type": "INT", "links": [445], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 236, "type": "ConrainPythonExecutor", "pos": [-393.68304443359375, -222.52761840820312], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 445, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [449, 806], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 238, "type": "ImageCrop+", "pos": [-112.68306732177734, -438.527587890625], "size": [316.8935546875, 239.64944458007812], "flags": {"collapsed": false}, "order": 50, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 492}, {"name": "width", "type": "INT", "link": 448, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 449, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [454], "slot_index": 0}, {"name": "x", "type": "INT", "links": [], "slot_index": 1}, {"name": "y", "type": "INT", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 298, "type": "Reroute", "pos": [-837.9940185546875, 481.6735534667969], "size": [75, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 833}], "outputs": [{"name": "", "type": "MODEL", "links": [808, 835], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 302, "type": "Reroute", "pos": [-836.9940185546875, 672.6735229492188], "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "VAE", "links": [681, 810], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "KSamplerSelect", "pos": [853.607177734375, 397.3435363769531], "size": [210, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [638], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 333, "type": "InpaintModelConditioning", "pos": [547.6074829101562, 487.343505859375], "size": [216.59999084472656, 138], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 647}, {"name": "negative", "type": "CONDITIONING", "link": 648, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 682}, {"name": "pixels", "type": "IMAGE", "link": 803}, {"name": "mask", "type": "MASK", "link": 651, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [644], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [857], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 334, "type": "FluxGuidance", "pos": [545.5105590820312, 380.4967346191406], "size": [211.60000610351562, 58], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 652, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [647], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 335, "type": "CLIPTextEncode", "pos": [275.60736083984375, 511.3437194824219], "size": [266.98651123046875, 76], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 653}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [648], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 336, "type": "CLIPTextEncode", "pos": [268.60736083984375, 402.3434753417969], "size": [394.06396484375, 153.04241943359375], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 680, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [652], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["empty scene, landscape only, (realistic:1.15)"]}, {"id": 340, "type": "ConrainGrowMaskWithBlur", "pos": [157.60740661621094, 841.3436889648438], "size": [315, 246], "flags": {"collapsed": true}, "order": 52, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 798}], "outputs": [{"name": "mask", "type": "MASK", "links": [651], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [20, 0, true, false, 50, 1, 1, false]}, {"id": 342, "type": "Reroute", "pos": [-141, 344], "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 679}], "outputs": [{"name": "", "type": "CLIP", "links": [653, 680], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 351, "type": "InspyrenetRembg", "pos": [-391.39276123046875, 896.3436279296875], "size": [218.5735321044922, 78], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 667}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [668], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["on"]}, {"id": 353, "type": "ConrainGrowMaskWithBlur", "pos": [-118.39260864257812, 841.3436889648438], "size": [315, 246], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 668}], "outputs": [{"name": "mask", "type": "MASK", "links": [664, 798], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [5, 0, true, false, 0, 1, 1, false]}, {"id": 354, "type": "Reroute", "pos": [-837.9940185546875, 551.6735229492188], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 674}], "outputs": [{"name": "", "type": "CLIP", "links": [679, 809], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 355, "type": "Reroute", "pos": [-144, 449], "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 681}], "outputs": [{"name": "", "type": "VAE", "links": [682, 683], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 370, "type": "KSamplerSelect", "pos": [310, 1390], "size": [314.0994873046875, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [704], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 375, "type": "CLIPTextEncode", "pos": [-907, 1234], "size": [210, 125.07953643798828], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 711}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [713], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 376, "type": "FluxGuidance", "pos": [-612, 1197], "size": [211.60000610351562, 58], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 713}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [714], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 378, "type": "CLIPTextEncode", "pos": [-918, 1312], "size": [210, 125.07953643798828], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 719}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [715], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 368, "type": "VAEDecode", "pos": [1090, 1120], "size": [190.54541015625, 46], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 700}, {"name": "vae", "type": "VAE", "link": 701}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [886], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 369, "type": "SamplerCustomAdvanced", "pos": [736, 1112], "size": [314.0994873046875, 106], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 826, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 703, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 704, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 705, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 748, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [700], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 210, "type": "CLIPTextEncode", "pos": [-591.8812255859375, 2211.************], "size": [210, 125.07953643798828], "flags": {"collapsed": true}, "order": 49, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 422}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [419, 427], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 208, "type": "FluxGuidance", "pos": [-516.88134765625, 1786.6341552734375], "size": [211.60000610351562, 58], "flags": {}, "order": 54, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 417}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [418], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 207, "type": "CLIPTextEncode", "pos": [-816.8812255859375, 1952.6341552734375], "size": [210, 125.07953643798828], "flags": {"collapsed": false}, "order": 48, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "clip", "type": "CLIP", "link": 416}, {"name": "text", "type": "STRING", "link": 488, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [417], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 384, "type": "Reroute", "pos": [-1260, 1130], "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 808}], "outputs": [{"name": "", "type": "MODEL", "links": [707, 710, 821], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 385, "type": "Reroute", "pos": [-1250, 1260], "size": [75, 26], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 809}], "outputs": [{"name": "", "type": "CLIP", "links": [711, 719, 822], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 367, "type": "Reroute", "pos": [-1250, 1430], "size": [75, 26], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 810}], "outputs": [{"name": "", "type": "VAE", "links": [701, 716, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 262, "type": "Reroute", "pos": [-1221.0789794921875, 2055.1630859375], "size": [75, 26], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 823}], "outputs": [{"name": "", "type": "VAE", "links": [490, 491], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 261, "type": "ConrainRandomPrompts", "pos": [-1315.0789794921875, 2145.1630859375], "size": [423.53680419921875, 150.89576721191406], "flags": {}, "order": 5, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [488], "slot_index": 0, "shape": 3}], "title": "模特描述", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 188, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 204, "type": "BasicGuider", "pos": [880, 1950], "size": [263.1893615722656, 46], "flags": {}, "order": 84, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "model", "type": "MODEL", "link": 858, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 414, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [408], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 420, "type": "Reroute", "pos": [607, 1108], "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 824}], "outputs": [{"name": "", "type": "NOISE", "links": [825, 826, 827], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 329, "type": "BasicGuider", "pos": [871, 298], "size": [161.1999969482422, 46], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 831, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 644}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [637], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 209, "type": "InpaintModelConditioning", "pos": [-17.078716278076172, 1795.1632080078125], "size": [216.59999084472656, 138], "flags": {}, "order": 82, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 418}, {"name": "negative", "type": "CONDITIONING", "link": 419}, {"name": "vae", "type": "VAE", "link": 490}, {"name": "pixels", "type": "IMAGE", "link": 889}, {"name": "mask", "type": "MASK", "link": 421}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [426], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [617], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 374, "type": "DifferentialDiffusion", "pos": [-907, 1167], "size": [210, 26], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 710}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [708], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 250, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-1342.638916015625, 497.0940246582031], "size": [436.6028137207031, 128.1446533203125], "flags": {}, "order": 13, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "model", "type": "MODEL", "link": 508}, {"name": "clip", "type": "CLIP", "link": 468}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [832], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [674], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}z", 1, "1"], "color": "#232", "bgcolor": "#353"}, {"id": 217, "type": "Reroute", "pos": [-1227.0789794921875, 1820.1632080078125], "size": [75, 26], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 821}], "outputs": [{"name": "", "type": "MODEL", "links": [412, 858]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 341, "type": "Reroute", "pos": [-135, 227], "size": [75, 26], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 835}], "outputs": [{"name": "", "type": "MODEL", "links": [685, 831], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 332, "type": "BasicScheduler", "pos": [860, 502], "size": [210, 106], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 685}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [639], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 10, 0.5]}, {"id": 423, "type": "Reroute", "pos": [-26, 1077], "size": [75, 26], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 836}], "outputs": [{"name": "", "type": "IMAGE", "links": [837, 838], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 213, "type": "CR Multi-ControlNet Stack", "pos": [258.9212341308594, 1975.163330078125], "size": [563.9595947265625, 454], "flags": {"collapsed": false}, "order": 77, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "image_1", "type": "IMAGE", "link": 424, "shape": 7}, {"name": "image_2", "type": "IMAGE", "link": 425, "shape": 7}, {"name": "image_3", "type": "IMAGE", "link": null, "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": null, "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [428], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "links": null, "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.1, 0.2, 0.6, "Off", "None", 1, 0, 1]}, {"id": 215, "type": "Zoe-DepthMapPreprocessor", "pos": [-25.309606552124023, 2087.5625], "size": [210, 58], "flags": {}, "order": 74, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "image", "type": "IMAGE", "link": 838}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [424, 425], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [512]}, {"id": 350, "type": "ImageResize+", "pos": [-401, 591], "size": [264.2375183105469, 218], "flags": {"collapsed": false}, "order": 22, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 692}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663, 667, 849], "slot_index": 0}, {"name": "width", "type": "INT", "links": [], "slot_index": 1}, {"name": "height", "type": "INT", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 768, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 1]}, {"id": 360, "type": "Reroute", "pos": [-139, 128], "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 885}], "outputs": [{"name": "", "type": "IMAGE", "links": [692, 851], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#323", "bgcolor": "#535"}, {"id": 411, "type": "ConrainImageCompositeMasked", "pos": [1173.23974609375, -161.19200134277344], "size": [318.6751403808594, 146], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 774}, {"name": "destination", "type": "IMAGE", "link": 777}, {"name": "mask", "type": "MASK", "link": 781, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [836], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 327, "type": "VAEDecode", "pos": [1218, 493], "size": [210, 46], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 641}, {"name": "vae", "type": "VAE", "link": 683}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [850], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 326, "type": "SamplerCustomAdvanced", "pos": [1149, 293], "size": [355.20001220703125, 106], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 827, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 637, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 638, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 639, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 857}], "outputs": [{"name": "output", "type": "LATENT", "links": [641], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 421, "type": "APersonMaskGenerator", "pos": [1639.9697265625, 637.9391479492188], "size": [315, 178], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 849}], "outputs": [{"name": "masks", "type": "MASK", "links": [844], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [true, false, true, false, false, 0.3]}, {"id": 426, "type": "IsMaskEmpty", "pos": [1660.9697265625, 410.9391174316406], "size": [241.79998779296875, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 844}], "outputs": [{"name": "boolean_number", "type": "NUMBER", "links": [845], "slot_index": 0}], "properties": {"Node name for S&R": "IsMaskEmpty"}, "widgets_values": []}, {"id": 428, "type": "ConrainIfExecute", "pos": [1664.9697265625, 206.93911743164062], "size": [210, 66], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "ANY", "type": "*", "link": 845}, {"name": "IF_TRUE", "type": "*", "link": 851}, {"name": "IF_FALSE", "type": "*", "link": 850}], "outputs": [{"name": "?", "type": "*", "links": [852], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainIfExecute"}, "widgets_values": []}, {"id": 202, "type": "KSamplerSelect", "pos": [880, 2060], "size": [314.0994873046875, 58], "flags": {}, "order": 6, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [409], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 399, "type": "Reroute", "pos": [1054.23974609375, -453.1920471191406], "size": [75, 26], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 852}], "outputs": [{"name": "", "type": "*", "links": [769], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 330, "type": "DifferentialDiffusion", "pos": [-853, 406], "size": [184.8000030517578, 26], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 832}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [833], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 233, "type": "easy showAnything", "pos": [-967, -109], "size": [210, 80], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 442, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["1536"]}, {"id": 235, "type": "ConrainPythonExecutor", "pos": [-388, -371], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 604, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [448, 805], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 377, "type": "InpaintModelConditioning", "pos": [-313, 1189], "size": [216.59999084472656, 138], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 714}, {"name": "negative", "type": "CONDITIONING", "link": 715}, {"name": "vae", "type": "VAE", "link": 716}, {"name": "pixels", "type": "IMAGE", "link": 837}, {"name": "mask", "type": "MASK", "link": 783}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [811], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [785], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 229, "type": "LoadImage", "pos": [-1348.68310546875, -330.527587890625], "size": [303.2363586425781, 365.75640869140625], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [881], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "title": "目标图片", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 348, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": [69, 599], "size": [315, 126], "flags": {"collapsed": false}, "order": 51, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 663}, {"name": "masks", "type": "MASK", "link": 664}], "outputs": [{"name": "images", "type": "IMAGE", "links": [803], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": [200, 20, false]}, {"id": 372, "type": "BasicGuider", "pos": [355, 1264], "size": [263.1893615722656, 46], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 708, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 811, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [703], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 203, "type": "BasicScheduler", "pos": [880, 2190], "size": [309.76611328125, 106], "flags": {}, "order": 53, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "model", "type": "MODEL", "link": 412, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [410], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, "${denoised?number}"], "color": "#232", "bgcolor": "#353"}, {"id": 373, "type": "RandomNoise", "pos": [220, 1105], "size": [313.7782897949219, 82], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [824], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed}", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 388, "type": "RepeatLatentBatch", "pos": [36, 1325], "size": [210, 58], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 785}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [748], "slot_index": 0}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2], "color": "#232", "bgcolor": "#353"}, {"id": 371, "type": "BasicScheduler", "pos": [315, 1526], "size": [309.76611328125, 106], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 707, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [705], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 10, 0.4]}, {"id": 387, "type": "ConrainGrowMaskWithBlur", "pos": [-695, 1386], "size": [340.20001220703125, 246], "flags": {"collapsed": false}, "order": 64, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 814}], "outputs": [{"name": "mask", "type": "MASK", "links": [], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": [783], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [-5, 0, false, false, 5, 1, 1, false]}, {"id": 201, "type": "SamplerCustomAdvanced", "pos": [1236, 1872], "size": [314.0994873046875, 106], "flags": {}, "order": 85, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "noise", "type": "NOISE", "link": 825, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 408, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 409, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 410, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 617, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [406], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 219, "type": "Text String", "pos": [1622.************, 2154.1474609375], "size": [315, 190], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [432], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [433], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 200, "type": "VAEDecode", "pos": [1312, 2078], "size": [190.54541015625, 46], "flags": {}, "order": 86, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "samples", "type": "LATENT", "link": 406}, {"name": "vae", "type": "VAE", "link": 491}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [430, 431], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 362, "type": "LoadImage", "pos": [-535, 150], "size": [315, 314], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [885], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "title": "参考背景", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${customSceneImg}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 413, "type": "Reroute", "pos": [518, -172], "size": [75, 26], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 780}], "outputs": [{"name": "", "type": "MASK", "links": [781, 814], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScale", "pos": [1184, -452], "size": [315, 130], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 769}, {"name": "width", "type": "INT", "link": 805, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 806, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [777], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["lanc<PERSON>s", 512, 512, "disabled"]}, {"id": 226, "type": "PreviewImage", "pos": [740, -280], "size": [210, 246], "flags": {}, "order": 60, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 438}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 224, "type": "InspyrenetRembg", "pos": [388, -311], "size": [218.5735321044922, 78], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 436}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [438], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [780], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["on"]}, {"id": 231, "type": "Reroute", "pos": [238, -440], "size": [75, 26], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 454}], "outputs": [{"name": "", "type": "IMAGE", "links": [436, 774, 829], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 366, "type": "Reroute", "pos": [1364, 1116], "size": [75, 26], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 886}], "outputs": [{"name": "", "type": "IMAGE", "links": [812, 889], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 389, "type": "PreviewImage", "pos": [1085, 1224], "size": [444.47198486328125, 427.4486999511719], "flags": {}, "order": 81, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 812}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 260, "type": "Reroute", "pos": [1658.5208740234375, 1392.8792724609375], "size": [75, 26], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 829}], "outputs": [{"name": "", "type": "IMAGE", "links": [891], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 450, "type": "Sapiens<PERSON><PERSON>der", "pos": [1791, 1097], "size": [659.2952270507812, 298], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 451, "type": "SapiensSampler", "pos": [2544, 1091], "size": [315, 258], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 890}, {"name": "image", "type": "IMAGE", "link": 891}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [892, 893], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 222, "type": "ConrainGrowMaskWithBlur", "pos": [-720, 2324], "size": [340.20001220703125, 246], "flags": {"collapsed": false}, "order": 66, "mode": "${(!isSwapFace)?then(4,0)}", "inputs": [{"name": "mask", "type": "MASK", "link": 893}], "outputs": [{"name": "mask", "type": "MASK", "links": [421], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, false, false, 10, 1, 1, true]}, {"id": 253, "type": "MaskPreview+", "pos": [2568, 1398], "size": [238.38063049316406, 256.3565368652344], "flags": {}, "order": 65, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 892}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 216, "type": "PreviewImage", "pos": [2315, 1784], "size": [562.7261962890625, 767.3224487304688], "flags": {}, "order": 87, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 430}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 220, "type": "ConrainImageSave", "pos": [1983, 2135], "size": [320, 266], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 431, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 432, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 433, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}], "links": [[406, 201, 0, 200, 0, "LATENT"], [408, 204, 0, 201, 1, "GUIDER"], [409, 202, 0, 201, 2, "SAMPLER"], [410, 203, 0, 201, 3, "SIGMAS"], [412, 217, 0, 203, 0, "MODEL"], [414, 214, 0, 204, 1, "CONDITIONING"], [416, 218, 0, 207, 0, "CLIP"], [417, 207, 0, 208, 0, "CONDITIONING"], [418, 208, 0, 209, 0, "CONDITIONING"], [419, 210, 0, 209, 1, "CONDITIONING"], [421, 222, 0, 209, 4, "MASK"], [422, 218, 0, 210, 0, "CLIP"], [424, 215, 0, 213, 0, "IMAGE"], [425, 215, 0, 213, 1, "IMAGE"], [426, 209, 0, 214, 0, "CONDITIONING"], [427, 210, 0, 214, 1, "CONDITIONING"], [428, 213, 0, 214, 2, "CONTROL_NET_STACK"], [430, 200, 0, 216, 0, "IMAGE"], [431, 200, 0, 220, 0, "IMAGE"], [432, 219, 0, 220, 1, "STRING"], [433, 219, 1, 220, 2, "STRING"], [436, 231, 0, 224, 0, "IMAGE"], [438, 224, 0, 226, 0, "IMAGE"], [441, 230, 0, 232, 0, "IMAGE"], [442, 232, 2, 233, 0, "*"], [445, 234, 1, 236, 0, "*"], [448, 235, 0, 238, 1, "INT"], [449, 236, 0, 238, 2, "INT"], [453, 232, 0, 234, 0, "IMAGE"], [454, 238, 0, 231, 0, "*"], [468, 138, 0, 250, 1, "CLIP"], [488, 261, 0, 207, 1, "STRING"], [490, 262, 0, 209, 2, "VAE"], [491, 262, 0, 200, 1, "VAE"], [492, 232, 0, 238, 0, "IMAGE"], [508, 136, 0, 250, 0, "MODEL"], [604, 234, 0, 235, 0, "*"], [617, 209, 2, 201, 4, "LATENT"], [637, 329, 0, 326, 1, "GUIDER"], [638, 331, 0, 326, 2, "SAMPLER"], [639, 332, 0, 326, 3, "SIGMAS"], [641, 326, 0, 327, 0, "LATENT"], [644, 333, 0, 329, 1, "CONDITIONING"], [647, 334, 0, 333, 0, "CONDITIONING"], [648, 335, 0, 333, 1, "CONDITIONING"], [651, 340, 0, 333, 4, "MASK"], [652, 336, 0, 334, 0, "CONDITIONING"], [653, 342, 0, 335, 0, "CLIP"], [663, 350, 0, 348, 0, "IMAGE"], [664, 353, 0, 348, 1, "MASK"], [667, 350, 0, 351, 0, "IMAGE"], [668, 351, 1, 353, 0, "MASK"], [674, 250, 1, 354, 0, "*"], [679, 354, 0, 342, 0, "*"], [680, 342, 0, 336, 0, "CLIP"], [681, 302, 0, 355, 0, "*"], [682, 355, 0, 333, 2, "VAE"], [683, 355, 0, 327, 1, "VAE"], [685, 341, 0, 332, 0, "MODEL"], [692, 360, 0, 350, 0, "IMAGE"], [700, 369, 0, 368, 0, "LATENT"], [701, 367, 0, 368, 1, "VAE"], [703, 372, 0, 369, 1, "GUIDER"], [704, 370, 0, 369, 2, "SAMPLER"], [705, 371, 0, 369, 3, "SIGMAS"], [707, 384, 0, 371, 0, "MODEL"], [708, 374, 0, 372, 0, "MODEL"], [710, 384, 0, 374, 0, "MODEL"], [711, 385, 0, 375, 0, "CLIP"], [713, 375, 0, 376, 0, "CONDITIONING"], [714, 376, 0, 377, 0, "CONDITIONING"], [715, 378, 0, 377, 1, "CONDITIONING"], [716, 367, 0, 377, 2, "VAE"], [719, 385, 0, 378, 0, "CLIP"], [748, 388, 0, 369, 4, "LATENT"], [769, 399, 0, 408, 0, "IMAGE"], [774, 231, 0, 411, 0, "IMAGE"], [777, 408, 0, 411, 1, "IMAGE"], [780, 224, 1, 413, 0, "*"], [781, 413, 0, 411, 2, "MASK"], [783, 387, 1, 377, 4, "MASK"], [785, 377, 2, 388, 0, "LATENT"], [798, 353, 0, 340, 0, "MASK"], [803, 348, 0, 333, 3, "IMAGE"], [805, 235, 0, 408, 1, "INT"], [806, 236, 0, 408, 2, "INT"], [807, 137, 0, 302, 0, "*"], [808, 298, 0, 384, 0, "*"], [809, 354, 0, 385, 0, "*"], [810, 302, 0, 367, 0, "*"], [811, 377, 0, 372, 1, "CONDITIONING"], [812, 366, 0, 389, 0, "IMAGE"], [814, 413, 0, 387, 0, "MASK"], [821, 384, 0, 217, 0, "*"], [822, 385, 0, 218, 0, "*"], [823, 367, 0, 262, 0, "*"], [824, 373, 0, 420, 0, "*"], [825, 420, 0, 201, 0, "NOISE"], [826, 420, 0, 369, 0, "NOISE"], [827, 420, 0, 326, 0, "NOISE"], [829, 231, 0, 260, 0, "*"], [831, 341, 0, 329, 0, "MODEL"], [832, 250, 0, 330, 0, "MODEL"], [833, 330, 0, 298, 0, "*"], [835, 298, 0, 341, 0, "*"], [836, 411, 0, 423, 0, "*"], [837, 423, 0, 377, 3, "IMAGE"], [838, 423, 0, 215, 0, "IMAGE"], [844, 421, 0, 426, 0, "MASK"], [845, 426, 0, 428, 0, "*"], [849, 350, 0, 421, 0, "IMAGE"], [850, 327, 0, 428, 2, "*"], [851, 360, 0, 428, 1, "*"], [852, 428, 0, 399, 0, "*"], [857, 333, 2, 326, 4, "LATENT"], [858, 217, 0, 204, 0, "MODEL"], [881, 229, 0, 230, 0, "*"], [885, 362, 0, 360, 0, "*"], [886, 368, 0, 366, 0, "*"], [889, 366, 0, 209, 3, "IMAGE"], [890, 450, 0, 451, 0, "MODEL_SAPIEN"], [891, 260, 0, 451, 1, "IMAGE"], [892, 451, 4, 253, 0, "MASK"], [893, 451, 4, 222, 0, "MASK"]], "groups": [{"id": 11, "title": "Group", "bounding": [-1356.761962890625, 1706.6116943359375, 2930.3095703125, 1024.4742431640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "Group", "bounding": [-1360.035400390625, -534.1700439453125, 2375.26953125, 587.347900390625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "Group", "bounding": [1617.0472412109375, 1020.6619262695312, 1272.0667724609375, 642.2556762695312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 18, "title": "Group", "bounding": [-568.4020385742188, 79.44583129882812, 2139.557861328125, 901.0925903320312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 19, "title": "Group", "bounding": [-1362.52099609375, 1018.2937622070312, 2938.8330078125, 647.0528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 20, "title": "Group", "bounding": [-1359.504150390625, 82.61701202392578, 755.9859008789062, 896.8026733398438], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 21, "title": "Group", "bounding": [1038.79296875, -529.8279418945312, 530.4352416992188, 583.1613159179688], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 22, "title": "Group", "bounding": [1617.201904296875, 84.3310317993164, 417.35052490234375, 888.804931640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "Group", "bounding": [1612.************, 1713.5477294921875, 1280.4439697265625, 1013.0407104492188], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.17349448024328948, "offset": [4775.450544941849, 2955.0868228570653]}}, "version": 0.4, "widget_idx_map": {"202": {"sampler_name": 0}, "203": {"scheduler": 0}, "261": {"seed": 1}, "331": {"sampler_name": 0}, "332": {"scheduler": 0}, "370": {"sampler_name": 0}, "371": {"scheduler": 0}, "373": {"noise_seed": 0}}, "seed_widgets": {"261": 1, "373": 0}}}}}
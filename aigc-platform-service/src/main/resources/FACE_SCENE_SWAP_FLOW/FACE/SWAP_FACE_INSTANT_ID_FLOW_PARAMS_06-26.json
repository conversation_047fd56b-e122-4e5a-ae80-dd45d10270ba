{"client_id": "${clientId}", "prompt": {"1102": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "1222": {"inputs": {"samples": ["1224", 0], "vae": ["1102", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1224": {"inputs": {"noise": ["1484", 0], "guider": ["1227", 0], "sampler": ["1225", 0], "sigmas": ["1226", 0], "latent_image": ["1491", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "1225": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1226": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": "${denoised?number}", "model": ["1592", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "1227": {"inputs": {"model": ["1488", 0], "conditioning": ["1531", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "1484": {"inputs": {"noise_seed": "${seed?number}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1488": {"inputs": {"model": ["1592", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "1489": {"inputs": {"text": ["1568", 0], "clip": ["1592", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1490": {"inputs": {"guidance": 3.5, "conditioning": ["1489", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "1491": {"inputs": {"noise_mask": true, "positive": ["1490", 0], "negative": ["1492", 0], "vae": ["1102", 0], "pixels": ["1611", 0], "mask": ["1603", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "1492": {"inputs": {"text": "", "clip": ["1592", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1530": {"inputs": {"switch_1": "On", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "start_percent_1": 0, "end_percent_1": 0.2, "switch_2": "On", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_2": 0.2, "start_percent_2": 0.2, "end_percent_2": 0.8, "switch_3": "On", "controlnet_3": "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", "controlnet_strength_3": 0.4, "start_percent_3": 0, "end_percent_3": 0.6, "image_1": ["1532", 0], "image_2": ["1532", 0], "image_3": ["1653", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}}, "1531": {"inputs": {"switch": "On", "base_positive": ["1491", 0], "base_negative": ["1492", 0], "controlnet_stack": ["1530", 0]}, "class_type": "CR Apply Multi-ControlNet", "_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}}, "1532": {"inputs": {"resolution": 1024, "image": ["1611", 0]}, "class_type": "Zoe-DepthMapPreprocessor", "_meta": {"title": "<PERSON>"}}, "1565": {"inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1568": {"inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 991}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "1590": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "1591": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "1592": {"inputs": {"lora_name": "${FACE.extInfo.faceLora}", "strength_model": "1", "strength_clip": "1", "model": ["1591", 0], "clip": ["1590", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "1601": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "1602": {"inputs": {"output_path": ["1601", 0], "filename_prefix": ["1601", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["1777", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "1603": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["1647", 4]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "1611": {"inputs": {"width": ["1622", 0], "height": ["1628", 0], "position": "top-left", "x_offset": 0, "y_offset": 0, "image": ["1644", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "🔧 Image Crop"}}, "1616": {"inputs": {"image": ["1644", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "1622": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "1628": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["1616", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "1644": {"inputs": {"width": 1536, "height": 1536, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "image": ["1565", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "1646": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "1647": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["1646", 0], "image": ["1611", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "1648": {"inputs": {"image": ["1565", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "1649": {"inputs": {"upscale_method": "bicubic", "width": ["1648", 0], "height": ["1648", 1], "crop": "disabled", "image": ["1650", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "1650": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "resize", "rescale_factor": 1, "resize_width": ["1648", 0], "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["1222", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "1653": {"inputs": {"low_threshold": 0.2, "high_threshold": 0.8, "image": ["1611", 0]}, "class_type": "<PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON>"}}, "1709": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["1710", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "1710": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["1760", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "1711": {"inputs": {"Input": ["1713", 0], "image1": ["1774", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "1712": {"inputs": {"Input": ["1714", 0], "image1": ["1775", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "1713": {"inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "1714": {"inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "1715": {"inputs": {"expand_LRU": 20, "expand_B": 20, "image": ["1649", 0], "mask": ["1738", 4]}, "class_type": "BoundedImageCropWithMask_v3_LR", "_meta": {"title": "Bounded Image Crop With Mask LR v3"}}, "1719": {"inputs": {"amount": 1, "samples": ["1782", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "1720": {"inputs": {"target_size": 1024, "image": ["1715", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "1721": {"inputs": {"expand": -10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 15, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["1715", 2]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "1723": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["1720", 0], "image": ["1715", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "1724": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": true}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "1725": {"inputs": {"noise": ["1734", 0], "guider": ["1735", 0], "sampler": ["1736", 0], "sigmas": ["1733", 0], "latent_image": ["1719", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "1726": {"inputs": {"text": ["1568", 0], "clip": ["1592", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "1730": {"inputs": {"guidance": 2, "conditioning": ["1726", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "1731": {"inputs": {"conditioning": ["1726", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "1732": {"inputs": {"model": ["1592", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "1733": {"inputs": {"scheduler": "sgm_uniform", "steps": 8, "denoise": 0.1, "model": ["1592", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "1734": {"inputs": {"noise_seed": 0}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "1735": {"inputs": {"model": ["1732", 0], "conditioning": ["1782", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "1736": {"inputs": {"sampler_name": "dpm_2"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "1738": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["1724", 0], "image": ["1649", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "1741": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "1742": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["1723", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "1743": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["1742", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "1744": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["1743", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "1745": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["1724", 0], "image": ["1744", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "1746": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "1747": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["1741", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1748": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "1749": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "1750": {"inputs": {"noise_mask": true, "positive": ["1753", 1], "negative": ["1753", 2], "vae": ["1741", 2], "pixels": ["1744", 0], "mask": ["1783", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "1752": {"inputs": {"text": ["1568", 0], "clip": ["1741", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "1753": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["1748", 0], "insightface": ["1749", 0], "control_net": ["1746", 0], "image": ["1709", 0], "model": ["1741", 0], "positive": ["1752", 0], "negative": ["1747", 0], "image_kps": ["1744", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "1754": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "upscale if smaller", "multiple_of": 0, "image": ["1768", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "1755": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "1756": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["1755", 0], "image": ["1754", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "1757": {"inputs": {"segs": ["1769", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "1758": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["1757", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "1759": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["1754", 0], "mask": ["1758", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "1760": {"inputs": {"images": ["1759", 0]}, "class_type": "easy imageListToImageBatch", "_meta": {"title": "Image List To Image Batch"}}, "1768": {"inputs": {"image1": ["1773", 0], "image2": ["1711", 0], "image3": ["1712", 0]}, "class_type": "ImpactMakeImageList", "_meta": {"title": "Make Image List"}}, "1769": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["1756", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "1770": {"inputs": {"samples": ["1776", 0], "vae": ["1741", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1771": {"inputs": {"samples": ["1725", 0], "vae": ["1102", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "1772": {"inputs": {"images": ["1723", 0], "face": ["1743", 0], "crop": ["1778", 0], "mask": ["1784", 0], "warp": ["1744", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "1773": {"inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1774": {"inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1775": {"inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "1776": {"inputs": {"seed": 42029499340256, "steps": 4, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "denoise": 0.5, "model": ["1753", 0], "positive": ["1750", 0], "negative": ["1750", 1], "latent_image": ["1750", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "1777": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["1649", 0], "target_mask": ["1721", 0], "target_bounds": ["1715", 1], "source": ["1772", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "1778": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["1744", 0], "image_target": ["1771", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "1781": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["1744", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "1782": {"inputs": {"noise_mask": false, "positive": ["1730", 0], "negative": ["1731", 0], "vae": ["1102", 0], "pixels": ["1770", 0], "mask": ["1800", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "1783": {"inputs": {"invert_mask": false, "grow": 5, "blur": 4, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["1781", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "1784": {"inputs": {"expand": 40, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["1783", 0]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "1800": {"inputs": {"invert_mask": true, "grow": 20, "blur": 10, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["1783", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 1800, "last_link_id": 2466, "nodes": [{"id": 1102, "type": "VAELoader", "pos": [-8946, 2337], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1999], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 1490, "type": "FluxGuidance", "pos": [-8027.3125, 3369.19775390625], "size": [211.60000610351562, 58], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1897}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1900], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 1531, "type": "CR Apply Multi-ControlNet", "pos": [-6867, 3179], "size": [274.56842041015625, 98], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "base_positive", "type": "CONDITIONING", "link": 1962}, {"name": "base_negative", "type": "CONDITIONING", "link": 1963}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": 1954}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [1964], "slot_index": 0, "shape": 3}, {"name": "base_neg", "type": "CONDITIONING", "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 1590, "type": "DualCLIPLoader", "pos": [-8947, 2143], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [2070], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 1591, "type": "UNETLoader", "pos": [-8950, 1970], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2069], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 1492, "type": "CLIPTextEncode", "pos": [-8017, 3469], "size": [210, 125.79536437988281], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2081}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1904, 1963], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1489, "type": "CLIPTextEncode", "pos": [-8333, 3422], "size": [210, 125.79536437988281], "flags": {"collapsed": false}, "order": 40, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2082}, {"name": "text", "type": "STRING", "link": 2067, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1897], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1488, "type": "DifferentialDiffusion", "pos": [-8321, 3349], "size": [210, 26], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2083}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1895], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 1227, "type": "BasicGuider", "pos": [-6245, 3322], "size": [263.1893615722656, 46], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1895, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1964, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1502], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 1224, "type": "SamplerCustomAdvanced", "pos": [-5836, 3193], "size": [314.************, 106], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1881, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1502, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1503, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 2024, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1908, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1497], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1628, "type": "ConrainPythonExecutor", "pos": [-8330, 2750], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 54, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2130, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2131], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1622, "type": "ConrainPythonExecutor", "pos": [-8330, 2670], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 53, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 2125, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2128], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1611, "type": "ImageCrop+", "pos": [-8079, 2479], "size": [316.8935546875, 239.64944458007812], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2152}, {"name": "width", "type": "INT", "link": 2128, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 2131, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2111], "slot_index": 0}, {"name": "x", "type": "INT"}, {"name": "y", "type": "INT"}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 1587, "type": "Reroute", "pos": [-7624.634765625, 1965.79296875], "size": [75, 26], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2056}], "outputs": [{"name": "", "type": "IMAGE", "links": [2166], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1646, "type": "Sapiens<PERSON><PERSON>der", "pos": [-7584, 2101], "size": [659.2952270507812, 298], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [2165], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 1222, "type": "VAEDecode", "pos": [-5430, 3233], "size": [190.54541015625, 46], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1497}, {"name": "vae", "type": "VAE", "link": 2005}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2173], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1532, "type": "Zoe-DepthMapPreprocessor", "pos": [-7770, 3658], "size": [210, 58], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2012}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1951, 1965, 2076], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [1024]}, {"id": 1510, "type": "Reroute", "pos": [-7989, 3556], "size": [75, 26], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2029}], "outputs": [{"name": "", "type": "IMAGE", "links": [1920, 2012, 2177], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1653, "type": "<PERSON><PERSON>", "pos": [-7773.06982421875, 3821.1767578125], "size": [212.60569763183594, 82], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2177}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2178, 2179], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [0.2, 0.8]}, {"id": 1484, "type": "RandomNoise", "pos": [-6244.537109375, 3184.927490234375], "size": [255.33419799804688, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1881], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed?number}", "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 1225, "type": "KSamplerSelect", "pos": [-6244, 3448], "size": [314.************, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 1603, "type": "ConrainGrowMaskWithBlur", "pos": [-7513, 2612], "size": [340.20001220703125, 246], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2167}], "outputs": [{"name": "mask", "type": "MASK", "links": [2093, 2094], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK"}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 1519, "type": "PreviewImage", "pos": [-7427, 3478], "size": [210, 246], "flags": {}, "order": 69, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1965}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1642, "type": "Reroute", "pos": [-8528, 2465], "size": [75, 26], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2465}], "outputs": [{"name": "", "type": "IMAGE", "links": [2151, 2152], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1647, "type": "SapiensSampler", "pos": [-6854.60546875, 2013.6029052734375], "size": [315, 258], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 2165}, {"name": "image", "type": "IMAGE", "link": 2166}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [2167], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 1711, "type": "CR Image Input Switch", "pos": [-8620, 4810], "size": [210, 74], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 2269, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 2270, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2340], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 1712, "type": "CR Image Input Switch", "pos": [-8620, 4960], "size": [210, 74], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 2271, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 2272, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2341], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 1713, "type": "JWStringToInteger", "pos": [-8960, 4820], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [2270], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"]}, {"id": 1714, "type": "JWStringToInteger", "pos": [-8980, 4970], "size": [213.92233276367188, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [2272], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"]}, {"id": 1716, "type": "Reroute", "pos": [-5466.326171875, 5417.2080078125], "size": [75, 26], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2275}], "outputs": [{"name": "", "type": "MASK", "links": [2357]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1717, "type": "Reroute", "pos": [-5486.326171875, 5337.2080078125], "size": [75, 26], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2276}], "outputs": [{"name": "", "type": "IMAGE", "links": [2356]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1718, "type": "Reroute", "pos": [-5466.326171875, 5487.2080078125], "size": [75, 26], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2277}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [2358]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1720, "type": "UpscaleSizeCalculator", "pos": [-7930, 5690], "size": [210, 136.1963348388672], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2279}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [2282], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [1024]}, {"id": 1729, "type": "Reroute", "pos": [-6496.326171875, 5357.2080078125], "size": [75, 26], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2378}], "outputs": [{"name": "", "type": "MODEL", "links": [2296, 2297]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1730, "type": "FluxGuidance", "pos": [-6406.326171875, 5847.2080078125], "size": [211.60000610351562, 58], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 2294, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2366], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [2]}, {"id": 1731, "type": "ConditioningZeroOut", "pos": [-6406.326171875, 5707.2080078125], "size": [222.26809692382812, 26], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 2295}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2367], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 1734, "type": "RandomNoise", "pos": [-5856.326171875, 5627.2080078125], "size": [221.2050323486328, 83.35130310058594], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [2283], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [0, "fixed"]}, {"id": 1735, "type": "BasicGuider", "pos": [-5836.326171875, 5387.2080078125], "size": [161.1999969482422, 46], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2298, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 2299}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [2284], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 1736, "type": "KSamplerSelect", "pos": [-5866.326171875, 5507.2080078125], "size": [210, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [2285], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpm_2"]}, {"id": 1738, "type": "SapiensSampler", "pos": [-8610, 5540], "size": [247.4663848876953, 259.6097412109375], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 2301}, {"name": "image", "type": "IMAGE", "link": 2302}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [2274], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 1754, "type": "ImageResize+", "pos": [-7970, 4770], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2326}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2328, 2331], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 1755, "type": "UltralyticsDetectorProvider", "pos": [-8340, 4980], "size": [315, 78], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [2327], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 1758, "type": "MaskFastGrow", "pos": [-7370, 4360], "size": [210, 178], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2330}], "outputs": [{"name": "MASK", "type": "MASK", "links": [2332], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 1762, "type": "Reroute", "pos": [-9120, 5120], "size": [75, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2373, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [2335]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1765, "type": "Reroute", "pos": [-9150, 5920], "size": [75, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2374}], "outputs": [{"name": "", "type": "CLIP", "links": [2300]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1768, "type": "ImpactMakeImageList", "pos": [-8300, 4780], "size": [231.68582153320312, 104.573486328125], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 2339}, {"name": "image2", "type": "IMAGE", "link": 2340}, {"name": "image3", "type": "IMAGE", "link": 2341}, {"name": "image4", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2326], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "widgets_values": []}, {"id": 1769, "type": "ImpactSEGSOrderedFilter", "pos": [-7640, 4360], "size": [210, 158.96408081054688], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 2342}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [2329], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 1568, "type": "ConrainRandomPrompts", "pos": [-8985, 3539], "size": [602.1065673828125, 245.61325073242188], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [2067, 2373], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 991, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 1594, "type": "Reroute", "pos": [-8752, 3338], "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2086}], "outputs": [{"name": "", "type": "CLIP", "links": [2081, 2082, 2374], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 1739, "type": "Reroute", "pos": [-6776.326171875, 5367.2080078125], "size": [75, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2457}], "outputs": [{"name": "", "type": "VAE", "links": [2346, 2368]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1551, "type": "Reroute", "pos": [-8519, 2329], "size": [75, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1999}], "outputs": [{"name": "", "type": "VAE", "links": [2004, 2005, 2457], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 1732, "type": "DifferentialDiffusion", "pos": [-6416.326171875, 5577.2080078125], "size": [210, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2296}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2298], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 1592, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-8534.6708984375, 1971.5068359375], "size": [516.7993774414062, 126], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2069}, {"name": "clip", "type": "CLIP", "link": 2070}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2085], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [2086], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 1593, "type": "Reroute", "pos": [-8756, 3211], "size": [75, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2085}], "outputs": [{"name": "", "type": "MODEL", "links": [2083, 2084, 2378], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 1752, "type": "CLIPTextEncode", "pos": [-6480, 4380], "size": [210, 96], "flags": {"collapsed": false}, "order": 49, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2316}, {"name": "text", "type": "STRING", "link": 2317, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2323], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 1746, "type": "ControlNetLoader", "pos": [-6240, 4580], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [2320], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 1749, "type": "InstantIDFaceAnalysis", "pos": [-6240, 4460], "size": [315, 58], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [2319], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 1748, "type": "InstantIDModelLoader", "pos": [-6240, 4360], "size": [315, 58], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [2318], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 1710, "type": "DetectFaces", "pos": [-7600, 4900], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2268}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [2267], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 1753, "type": "ApplyInstantID", "pos": [-5820, 4330], "size": [210, 266], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 2318, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 2319, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 2320, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 2321, "label": "image"}, {"name": "model", "type": "MODEL", "link": 2322, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 2323, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 2324, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 2325, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "link": null, "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2352], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [2310], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [2311], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 1760, "type": "easy imageListToImageBatch", "pos": [-7360, 4650], "size": [222.84095764160156, 26], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2333}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2268], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 1742, "type": "DetectFaces", "pos": [-6860, 4480], "size": [210, 126], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2304}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [2305], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 1709, "type": "CropFaces", "pos": [-7360, 4900], "size": [221.15121459960938, 146], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 2267}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [2388], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 1761, "type": "Reroute", "pos": [-7020, 4330], "size": [75, 26], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2388}], "outputs": [{"name": "", "type": "IMAGE", "links": [2321], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1750, "type": "InpaintModelConditioning", "pos": [-5570, 4520], "size": [210, 138], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 2310, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 2311}, {"name": "vae", "type": "VAE", "link": 2312}, {"name": "pixels", "type": "IMAGE", "link": 2387}, {"name": "mask", "type": "MASK", "link": 2314}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [2353], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [2354], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [2355], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 1764, "type": "Reroute", "pos": [-7020, 4410], "size": [75, 26], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2336, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [2317]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1725, "type": "SamplerCustomAdvanced", "pos": [-5536.326171875, 5587.2080078125], "size": [236.8000030517578, 109.************], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 2283, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 2284, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 2285, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 2286, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 2287}], "outputs": [{"name": "output", "type": "LATENT", "links": [2345], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1772, "type": "WarpFacesBack", "pos": [-4996.326171875, 5717.2080078125], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2347}, {"name": "face", "type": "FACE", "link": 2348}, {"name": "crop", "type": "IMAGE", "link": 2349}, {"name": "mask", "type": "MASK", "link": 2350}, {"name": "warp", "type": "WARP", "link": 2351}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2359], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 1722, "type": "Reroute", "pos": [-8900, 5360], "size": [75, 26], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2428}], "outputs": [{"name": "", "type": "IMAGE", "links": [2273, 2276, 2302]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1724, "type": "Sapiens<PERSON><PERSON>der", "pos": [-8950, 5540], "size": [283.3903503417969, 298], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [2301, 2460], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 1737, "type": "Reroute", "pos": [-7220, 5940], "size": [75, 26], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2300}], "outputs": [{"name": "", "type": "CLIP", "links": [2288]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1726, "type": "CLIPTextEncode", "pos": [-6866.326171875, 5857.2080078125], "size": [296.32208251953125, 77.2895278930664], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2288}, {"name": "text", "type": "STRING", "link": 2289, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2294, 2295], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 1715, "type": "BoundedImageCropWithMask_v3_LR", "pos": [-8240, 5400], "size": [285.6000061035156, 149.66432189941406], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2273}, {"name": "mask", "type": "MASK", "link": 2274}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2279, 2281], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [2277], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [2280], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [20, 20]}, {"id": 1757, "type": "SegsToCombinedMask", "pos": [-7480, 4310], "size": [289.79998779296875, 26], "flags": {"collapsed": true}, "order": 59, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 2329}], "outputs": [{"name": "MASK", "type": "MASK", "links": [2330], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 1756, "type": "ImpactSimpleDetectorSEGS", "pos": [-7970, 4350], "size": [277.6741943359375, 319.97015380859375], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 2327}, {"name": "image", "type": "IMAGE", "link": 2328}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [2342], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 1759, "type": "Bounded Image Crop with Mask", "pos": [-7640, 4660], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2331}, {"name": "mask", "type": "MASK", "link": 2332}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2333], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 1770, "type": "VAEDecode", "pos": [-5230, 4860], "size": [140, 46], "flags": {"collapsed": false}, "order": 107, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 2343}, {"name": "vae", "type": "VAE", "link": 2344}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2369], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1751, "type": "Reroute", "pos": [-6040, 4640], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2315}], "outputs": [{"name": "", "type": "IMAGE", "links": [2325, 2387], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1744, "type": "CropFaces", "pos": [-6550, 4700], "size": [221.15121459960938, 146], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 2306}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [2308, 2315, 2360, 2396], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [2351], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 1588, "type": "MaskPreview+", "pos": [-6836, 2612], "size": [210, 246], "flags": {}, "order": 74, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 2093}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 1719, "type": "RepeatLatentBatch", "pos": [-5846.326171875, 5827.2080078125], "size": [210, 80.70018768310547], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 2278}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2287], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [1]}, {"id": 1491, "type": "InpaintModelConditioning", "pos": [-7426, 3182], "size": [216.59999084472656, 138], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1900}, {"name": "negative", "type": "CONDITIONING", "link": 1904}, {"name": "vae", "type": "VAE", "link": 2004}, {"name": "pixels", "type": "IMAGE", "link": 1920}, {"name": "mask", "type": "MASK", "link": 2094}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1962], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1908], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 1530, "type": "CR Multi-ControlNet Stack", "pos": [-7100.19384765625, 3650.299072265625], "size": [563.9595947265625, 454], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "image_1", "type": "IMAGE", "link": 1951, "shape": 7}, {"name": "image_2", "type": "IMAGE", "link": 2076, "shape": 7}, {"name": "image_3", "type": "IMAGE", "link": 2178, "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [1954], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.2, 0.2, 0.8, "On", "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", 0.4, 0, 0.6]}, {"id": 1771, "type": "VAEDecode", "pos": [-5546.326171875, 5827.2080078125], "size": [210, 46], "flags": {"collapsed": false}, "order": 112, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 2345}, {"name": "vae", "type": "VAE", "link": 2346}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2361], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1778, "type": "easy imageColorMatch", "pos": [-5276.326171875, 5767.2080078125], "size": [210, 102], "flags": {"collapsed": false}, "order": 113, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 2360}, {"name": "image_target", "type": "IMAGE", "link": 2361}], "outputs": [{"name": "image", "type": "IMAGE", "links": [2349], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 1566, "type": "Reroute", "pos": [-7915, 2967], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2111}], "outputs": [{"name": "", "type": "IMAGE", "links": [2029, 2056], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1733, "type": "BasicScheduler", "pos": [-6116.326171875, 5787.2080078125], "size": [210, 106], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2297}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [2286], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", 8, 0.1]}, {"id": 1650, "type": "CR Upscale Image", "pos": [-5477.185546875, 3375.064208984375], "size": [315, 222], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2173}, {"name": "resize_width", "type": "INT", "link": 2171, "widget": {"name": "resize_width"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2172], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, 1024, "lanc<PERSON>s", "true", 8]}, {"id": 1776, "type": "K<PERSON><PERSON><PERSON>", "pos": [-5310, 4500], "size": [261.8017578125, 262], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2352}, {"name": "positive", "type": "CONDITIONING", "link": 2353}, {"name": "negative", "type": "CONDITIONING", "link": 2354}, {"name": "latent_image", "type": "LATENT", "link": 2355}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [2343], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42029499340256, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.5]}, {"id": 1743, "type": "OrderedFaceFilter", "pos": [-6860, 4660], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 2305}], "outputs": [{"name": "filtered", "type": "FACE", "links": [2306, 2348], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 1763, "type": "Reroute", "pos": [-7200, 5120], "size": [75, 26], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2335, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [2289, 2336]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1745, "type": "SapiensSampler", "pos": [-6850, 4880], "size": [265.8616027832031, 258], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 2460}, {"name": "image", "type": "IMAGE", "link": 2308}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "23,24,25,26,27", false, 255, 255, 255]}, {"id": 1721, "type": "ConrainGrowMaskWithBlur", "pos": [-7700, 5390], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 91, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2280}], "outputs": [{"name": "mask", "type": "MASK", "links": [2275], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 1723, "type": "ImageScaleBy", "pos": [-7590, 5730], "size": [250.58731079101562, 109.74829864501953], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2281, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 2282, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2462], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 1740, "type": "Reroute", "pos": [-7260, 5730], "size": [75, 26], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2462}], "outputs": [{"name": "", "type": "IMAGE", "links": [2304, 2347], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1565, "type": "LoadImage", "pos": [-8958.837890625, 2479.285888671875], "size": [315, 314], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2461], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 1649, "type": "ImageScale", "pos": [-5870.81591796875, 3610.82763671875], "size": [315, 130], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2172}, {"name": "width", "type": "INT", "link": 2169, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 2170, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2175, 2428], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 1774, "type": "LoadImage", "pos": [-8641.890625, 4349.2548828125], "size": [249.60922241210938, 314], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2269], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 1775, "type": "LoadImage", "pos": [-8295.3369140625, 4350.82958984375], "size": [234.48504638671875, 314], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2271], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 1784, "type": "ConrainGrowMaskWithBlur", "pos": [-5590, 4770], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 104, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2372}], "outputs": [{"name": "mask", "type": "MASK", "links": [2350], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [40, 0, false, false, 0, 1, 1, false]}, {"id": 1782, "type": "InpaintModelConditioning", "pos": [-6136.326171875, 5387.2080078125], "size": [216.59999084472656, 138], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 2366}, {"name": "negative", "type": "CONDITIONING", "link": 2367, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 2368}, {"name": "pixels", "type": "IMAGE", "link": 2369}, {"name": "mask", "type": "MASK", "link": 2464, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [2299], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [2278], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 1781, "type": "easy humanSegmentation", "pos": [-6190, 4750], "size": [300, 500], "flags": {"collapsed": false}, "order": 100, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2396}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [2371], "slot_index": 1}, {"name": "bbox", "type": "BBOX", "slot_index": 2}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 1783, "type": "MaskFastGrow", "pos": [-5920, 4710], "size": [210, 178], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2371}], "outputs": [{"name": "MASK", "type": "MASK", "links": [2314, 2372, 2463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 5, 4, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 1800, "type": "MaskFastGrow", "pos": [-5918.13916015625, 4957.04052734375], "size": [212.8609161376953, 178], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 2463}], "outputs": [{"name": "MASK", "type": "MASK", "links": [2464], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [true, 20, 10, 0, 1, true]}, {"id": 1616, "type": "GetImageSize+", "pos": [-8585.2841796875, 2647.29345703125], "size": [214.20001220703125, 66], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2151}], "outputs": [{"name": "width", "type": "INT", "links": [2125], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2130], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 1656, "type": "Reroute", "pos": [-8918.9462890625, 2917.056396484375], "size": [75, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 2461}], "outputs": [{"name": "", "type": "IMAGE", "links": [2427, 2430], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 1644, "type": "ImageResize+", "pos": [-8749.8896484375, 2913.16845703125], "size": [315, 218], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2427}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2465], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 1648, "type": "GetImageSize+", "pos": [-5801.48486328125, 3374.087890625], "size": [214.20001220703125, 66], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 2430}], "outputs": [{"name": "width", "type": "INT", "links": [2169, 2171], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2170], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 1654, "type": "PreviewImage", "pos": [-7428.3212890625, 3852.811279296875], "size": [210, 246], "flags": {}, "order": 71, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 2179}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1601, "type": "Text String", "pos": [-5894.345703125, 3842.14111328125], "size": [315, 190], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [2089], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [2090], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 1226, "type": "BasicScheduler", "pos": [-6236.58642578125, 3590.7333984375], "size": [309.76611328125, 106], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2084, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [2024], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, "${denoised?number}"], "color": "#232", "bgcolor": "#353"}, {"id": 1785, "type": "PreviewImage", "pos": [-4655.3701171875, 5356.54443359375], "size": [393.20245361328125, 409.597412109375], "flags": {}, "order": 116, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 2375}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1651, "type": "PreviewImage", "pos": [-5050.1826171875, 3610.868408203125], "size": [236.9750518798828, 273.07080078125], "flags": {}, "order": 84, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 2175}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1777, "type": "Bounded Image Blend with Mask", "pos": [-5046.326171875, 5357.2080078125], "size": [243.60000610351562, 172.84213256835938], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 2356}, {"name": "target_mask", "type": "MASK", "link": 2357}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 2358}, {"name": "source", "type": "IMAGE", "link": 2359}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2375, 2466], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 1602, "type": "ConrainImageSave", "pos": [-5492.1669921875, 3821.91259765625], "size": [320, 266], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2466, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 2089, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 2090, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 1773, "type": "LoadImage", "pos": [-8977.61328125, 4351.041015625], "size": [235.8109893798828, 314], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2339], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 1741, "type": "CheckpointLoaderSimple", "pos": [-6860, 4310], "size": [314.34735107421875, 128.43458557128906], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2322], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2309, 2316], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [2312, 2344], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 1747, "type": "CLIPTextEncode", "pos": [-6560, 4540], "size": [239.4051971435547, 91.89************], "flags": {"collapsed": false}, "order": 28, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2309}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [2324], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}], "links": [[1497, 1224, 0, 1222, 0, "LATENT"], [1502, 1227, 0, 1224, 1, "GUIDER"], [1503, 1225, 0, 1224, 2, "SAMPLER"], [1881, 1484, 0, 1224, 0, "NOISE"], [1895, 1488, 0, 1227, 0, "MODEL"], [1897, 1489, 0, 1490, 0, "CONDITIONING"], [1900, 1490, 0, 1491, 0, "CONDITIONING"], [1904, 1492, 0, 1491, 1, "CONDITIONING"], [1908, 1491, 2, 1224, 4, "LATENT"], [1920, 1510, 0, 1491, 3, "IMAGE"], [1951, 1532, 0, 1530, 0, "IMAGE"], [1954, 1530, 0, 1531, 2, "CONTROL_NET_STACK"], [1962, 1491, 0, 1531, 0, "CONDITIONING"], [1963, 1492, 0, 1531, 1, "CONDITIONING"], [1964, 1531, 0, 1227, 1, "CONDITIONING"], [1965, 1532, 0, 1519, 0, "IMAGE"], [1999, 1102, 0, 1551, 0, "*"], [2004, 1551, 0, 1491, 2, "VAE"], [2005, 1551, 0, 1222, 1, "VAE"], [2012, 1510, 0, 1532, 0, "IMAGE"], [2024, 1226, 0, 1224, 3, "SIGMAS"], [2029, 1566, 0, 1510, 0, "*"], [2056, 1566, 0, 1587, 0, "*"], [2067, 1568, 0, 1489, 1, "STRING"], [2069, 1591, 0, 1592, 0, "MODEL"], [2070, 1590, 0, 1592, 1, "CLIP"], [2076, 1532, 0, 1530, 1, "IMAGE"], [2081, 1594, 0, 1492, 0, "CLIP"], [2082, 1594, 0, 1489, 0, "CLIP"], [2083, 1593, 0, 1488, 0, "MODEL"], [2084, 1593, 0, 1226, 0, "MODEL"], [2085, 1592, 0, 1593, 0, "*"], [2086, 1592, 1, 1594, 0, "*"], [2089, 1601, 0, 1602, 1, "STRING"], [2090, 1601, 1, 1602, 2, "STRING"], [2093, 1603, 0, 1588, 0, "MASK"], [2094, 1603, 0, 1491, 4, "MASK"], [2111, 1611, 0, 1566, 0, "*"], [2125, 1616, 0, 1622, 0, "*"], [2128, 1622, 0, 1611, 1, "INT"], [2130, 1616, 1, 1628, 0, "*"], [2131, 1628, 0, 1611, 2, "INT"], [2151, 1642, 0, 1616, 0, "IMAGE"], [2152, 1642, 0, 1611, 0, "IMAGE"], [2165, 1646, 0, 1647, 0, "MODEL_SAPIEN"], [2166, 1587, 0, 1647, 1, "IMAGE"], [2167, 1647, 4, 1603, 0, "MASK"], [2169, 1648, 0, 1649, 1, "INT"], [2170, 1648, 1, 1649, 2, "INT"], [2171, 1648, 0, 1650, 1, "INT"], [2172, 1650, 0, 1649, 0, "IMAGE"], [2173, 1222, 0, 1650, 0, "IMAGE"], [2175, 1649, 0, 1651, 0, "IMAGE"], [2177, 1510, 0, 1653, 0, "IMAGE"], [2178, 1653, 0, 1530, 2, "IMAGE"], [2179, 1653, 0, 1654, 0, "IMAGE"], [2267, 1710, 0, 1709, 0, "FACE"], [2268, 1760, 0, 1710, 0, "IMAGE"], [2269, 1774, 0, 1711, 0, "IMAGE"], [2270, 1713, 0, 1711, 2, "INT"], [2271, 1775, 0, 1712, 0, "IMAGE"], [2272, 1714, 0, 1712, 2, "INT"], [2273, 1722, 0, 1715, 0, "IMAGE"], [2274, 1738, 4, 1715, 1, "MASK"], [2275, 1721, 0, 1716, 0, "*"], [2276, 1722, 0, 1717, 0, "*"], [2277, 1715, 1, 1718, 0, "*"], [2278, 1782, 2, 1719, 0, "LATENT"], [2279, 1715, 0, 1720, 0, "IMAGE"], [2280, 1715, 2, 1721, 0, "MASK"], [2281, 1715, 0, 1723, 0, "IMAGE"], [2282, 1720, 0, 1723, 1, "FLOAT"], [2283, 1734, 0, 1725, 0, "NOISE"], [2284, 1735, 0, 1725, 1, "GUIDER"], [2285, 1736, 0, 1725, 2, "SAMPLER"], [2286, 1733, 0, 1725, 3, "SIGMAS"], [2287, 1719, 0, 1725, 4, "LATENT"], [2288, 1737, 0, 1726, 0, "CLIP"], [2289, 1763, 0, 1726, 1, "STRING"], [2294, 1726, 0, 1730, 0, "CONDITIONING"], [2295, 1726, 0, 1731, 0, "CONDITIONING"], [2296, 1729, 0, 1732, 0, "MODEL"], [2297, 1729, 0, 1733, 0, "MODEL"], [2298, 1732, 0, 1735, 0, "MODEL"], [2299, 1782, 0, 1735, 1, "CONDITIONING"], [2300, 1765, 0, 1737, 0, "*"], [2301, 1724, 0, 1738, 0, "MODEL_SAPIEN"], [2302, 1722, 0, 1738, 1, "IMAGE"], [2304, 1740, 0, 1742, 0, "IMAGE"], [2305, 1742, 0, 1743, 0, "FACE"], [2306, 1743, 0, 1744, 0, "FACE"], [2308, 1744, 0, 1745, 1, "IMAGE"], [2309, 1741, 1, 1747, 0, "CLIP"], [2310, 1753, 1, 1750, 0, "CONDITIONING"], [2311, 1753, 2, 1750, 1, "CONDITIONING"], [2312, 1741, 2, 1750, 2, "VAE"], [2314, 1783, 0, 1750, 4, "MASK"], [2315, 1744, 0, 1751, 0, "*"], [2316, 1741, 1, 1752, 0, "CLIP"], [2317, 1764, 0, 1752, 1, "STRING"], [2318, 1748, 0, 1753, 0, "INSTANTID"], [2319, 1749, 0, 1753, 1, "FACEANALYSIS"], [2320, 1746, 0, 1753, 2, "CONTROL_NET"], [2321, 1761, 0, 1753, 3, "IMAGE"], [2322, 1741, 0, 1753, 4, "MODEL"], [2323, 1752, 0, 1753, 5, "CONDITIONING"], [2324, 1747, 0, 1753, 6, "CONDITIONING"], [2325, 1751, 0, 1753, 7, "IMAGE"], [2326, 1768, 0, 1754, 0, "IMAGE"], [2327, 1755, 0, 1756, 0, "BBOX_DETECTOR"], [2328, 1754, 0, 1756, 1, "IMAGE"], [2329, 1769, 0, 1757, 0, "SEGS"], [2330, 1757, 0, 1758, 0, "MASK"], [2331, 1754, 0, 1759, 0, "IMAGE"], [2332, 1758, 0, 1759, 1, "MASK"], [2333, 1759, 0, 1760, 0, "IMAGE"], [2335, 1762, 0, 1763, 0, "*"], [2336, 1763, 0, 1764, 0, "*"], [2339, 1773, 0, 1768, 0, "IMAGE"], [2340, 1711, 0, 1768, 1, "IMAGE"], [2341, 1712, 0, 1768, 2, "IMAGE"], [2342, 1756, 0, 1769, 0, "SEGS"], [2343, 1776, 0, 1770, 0, "LATENT"], [2344, 1741, 2, 1770, 1, "VAE"], [2345, 1725, 0, 1771, 0, "LATENT"], [2346, 1739, 0, 1771, 1, "VAE"], [2347, 1740, 0, 1772, 0, "IMAGE"], [2348, 1743, 0, 1772, 1, "FACE"], [2349, 1778, 0, 1772, 2, "IMAGE"], [2350, 1784, 0, 1772, 3, "MASK"], [2351, 1744, 2, 1772, 4, "WARP"], [2352, 1753, 0, 1776, 0, "MODEL"], [2353, 1750, 0, 1776, 1, "CONDITIONING"], [2354, 1750, 1, 1776, 2, "CONDITIONING"], [2355, 1750, 2, 1776, 3, "LATENT"], [2356, 1717, 0, 1777, 0, "IMAGE"], [2357, 1716, 0, 1777, 1, "MASK"], [2358, 1718, 0, 1777, 2, "IMAGE_BOUNDS"], [2359, 1772, 0, 1777, 3, "IMAGE"], [2360, 1744, 0, 1778, 0, "IMAGE"], [2361, 1771, 0, 1778, 1, "IMAGE"], [2366, 1730, 0, 1782, 0, "CONDITIONING"], [2367, 1731, 0, 1782, 1, "CONDITIONING"], [2368, 1739, 0, 1782, 2, "VAE"], [2369, 1770, 0, 1782, 3, "IMAGE"], [2371, 1781, 1, 1783, 0, "MASK"], [2372, 1783, 0, 1784, 0, "MASK"], [2373, 1568, 0, 1762, 0, "*"], [2374, 1594, 0, 1765, 0, "*"], [2375, 1777, 0, 1785, 0, "IMAGE"], [2378, 1593, 0, 1729, 0, "*"], [2387, 1751, 0, 1750, 3, "IMAGE"], [2388, 1709, 0, 1761, 0, "*"], [2396, 1744, 0, 1781, 0, "IMAGE"], [2427, 1656, 0, 1644, 0, "IMAGE"], [2428, 1649, 0, 1722, 0, "*"], [2430, 1656, 0, 1648, 0, "IMAGE"], [2457, 1551, 0, 1739, 0, "*"], [2460, 1724, 0, 1745, 0, "MODEL_SAPIEN"], [2461, 1565, 0, 1656, 0, "*"], [2462, 1723, 0, 1740, 0, "*"], [2463, 1783, 0, 1800, 0, "MASK"], [2464, 1800, 0, 1782, 4, "MASK"], [2465, 1644, 0, 1642, 0, "*"], [2466, 1777, 0, 1602, 0, "IMAGE"]], "groups": [{"id": 2, "title": "TEXT | GENERATION", "bounding": [-9014, 3105, 3862.244384765625, 1043.714111328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "", "bounding": [-9015.4423828125, 1898.2938232421875, 1295.358154296875, 1176.14306640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-7670.44873046875, 1903.500732421875, 1559.69091796875, 1167.5008544921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "人脸原图", "bounding": [-9020, 4270, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 9, "title": "Group", "bounding": [-9020, 5260, 1893.4822998046875, 743.8132934570312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "Group", "bounding": [-6886.326171875, 5287.2080078125, 2118.918701171875, 722.1393432617188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "换脸", "bounding": [-6890, 4260, 1894.3173828125, 895.3728637695312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.3109994191499956, "offset": [9342.761504369764, -4144.5101615257545]}}, "version": 0.4, "seed_widgets": {"1484": 0, "1568": 1, "1734": 0, "1776": 0}}}}}
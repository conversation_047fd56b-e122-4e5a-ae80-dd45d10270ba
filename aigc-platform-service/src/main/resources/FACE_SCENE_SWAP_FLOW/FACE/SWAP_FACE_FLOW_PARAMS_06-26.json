{"client_id": "${clientId}", "prompt": {"1102": {"_meta": {"title": "Load VAE"}, "inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "1222": {"_meta": {"title": "VAE Decode"}, "inputs": {"vae": ["1102", 0], "samples": ["1224", 0]}, "class_type": "VAEDecode"}, "1224": {"_meta": {"title": "SamplerCustomAdvanced"}, "inputs": {"noise": ["1484", 0], "guider": ["1227", 0], "sigmas": ["1226", 0], "sampler": ["1225", 0], "latent_image": ["1491", 2]}, "class_type": "SamplerCustomAdvanced"}, "1225": {"_meta": {"title": "KSamplerSelect"}, "inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "1226": {"_meta": {"title": "BasicScheduler"}, "inputs": {"model": ["1592", 0], "steps": 20, "denoise": "${denoised?number}", "scheduler": "simple"}, "class_type": "BasicScheduler"}, "1227": {"_meta": {"title": "BasicGuider"}, "inputs": {"model": ["1488", 0], "conditioning": ["1531", 0]}, "class_type": "BasicGuider"}, "1484": {"_meta": {"title": "RandomNoise"}, "inputs": {"noise_seed": "${seed?number}"}, "class_type": "RandomNoise"}, "1488": {"_meta": {"title": "Differential Diffusion"}, "inputs": {"model": ["1592", 0]}, "class_type": "DifferentialDiffusion"}, "1489": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "inputs": {"clip": ["1592", 1], "text": ["1568", 0]}, "class_type": "CLIPTextEncode"}, "1490": {"_meta": {"title": "FluxGuidance"}, "inputs": {"guidance": 3.5, "conditioning": ["1489", 0]}, "class_type": "FluxGuidance"}, "1491": {"_meta": {"title": "InpaintModelConditioning"}, "inputs": {"vae": ["1102", 0], "mask": ["1603", 0], "pixels": ["1611", 0], "negative": ["1492", 0], "positive": ["1490", 0], "noise_mask": true}, "class_type": "InpaintModelConditioning"}, "1492": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "inputs": {"clip": ["1592", 1], "text": ""}, "class_type": "CLIPTextEncode"}, "1530": {"_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}, "inputs": {"image_1": ["1532", 0], "image_2": ["1532", 0], "switch_1": "On", "switch_2": "On", "switch_3": "Off", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_3": "None", "end_percent_1": 0.2, "end_percent_2": 0.5, "end_percent_3": 1, "start_percent_1": 0, "start_percent_2": 0.2, "start_percent_3": 0, "controlnet_strength_1": 0.6, "controlnet_strength_2": 0.1, "controlnet_strength_3": 1}, "class_type": "CR Multi-ControlNet Stack"}, "1531": {"_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}, "inputs": {"switch": "On", "base_negative": ["1492", 0], "base_positive": ["1491", 0], "controlnet_stack": ["1530", 0]}, "class_type": "CR Apply Multi-ControlNet"}, "1532": {"_meta": {"title": "<PERSON>"}, "inputs": {"image": ["1611", 0], "resolution": 1024}, "class_type": "Zoe-DepthMapPreprocessor"}, "1565": {"_meta": {"title": "Load Image"}, "inputs": {"image": "${targetImage}", "upload": "image"}, "class_type": "LoadImage"}, "1568": {"_meta": {"title": "正向提示词"}, "inputs": {"seed": 991, "prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}"}, "class_type": "ConrainRandomPrompts"}, "1590": {"_meta": {"title": "DualCLIPLoader"}, "inputs": {"type": "flux", "device": "default", "clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors"}, "class_type": "DualCLIPLoader"}, "1591": {"_meta": {"title": "Load Diffusion Model"}, "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "1592": {"_meta": {"title": "人脸lora"}, "inputs": {"clip": ["1590", 0], "model": ["1591", 0], "lora_name": "${FACE.extInfo.faceLora}", "strength_clip": "1", "strength_model": "1"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "1601": {"_meta": {"title": "Text String"}, "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "1602": {"_meta": {"title": "conrain save image"}, "inputs": {"dpi": 100, "images": ["1649", 0], "quality": 100, "extension": "jpg", "output_path": ["1601", 0], "use_time_str": "true", "lossless_webp": "false", "embed_workflow": "false", "optimize_image": "true", "output_as_root": "true", "filename_prefix": ["1601", 1]}, "class_type": "ConrainImageSave"}, "1603": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "inputs": {"mask": ["1647", 4], "expand": 2, "fill_holes": true, "flip_input": false, "lerp_alpha": 1, "blur_radius": 10, "decay_factor": 1, "tapered_corners": true, "incremental_expandrate": 0}, "class_type": "ConrainGrowMaskWithBlur"}, "1611": {"_meta": {"title": "🔧 Image Crop"}, "inputs": {"image": ["1644", 0], "width": ["1622", 0], "height": ["1628", 0], "position": "top-left", "x_offset": 0, "y_offset": 0}, "class_type": "ImageCrop+"}, "1616": {"_meta": {"title": "🔧 Get Image Size"}, "inputs": {"image": ["1644", 0]}, "class_type": "GetImageSize+"}, "1622": {"_meta": {"title": "conrain python executor"}, "inputs": {"any_a": ["1616", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"}, "class_type": "ConrainPythonExecutor"}, "1628": {"_meta": {"title": "conrain python executor"}, "inputs": {"any_a": ["1616", 1], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"}, "class_type": "ConrainPythonExecutor"}, "1644": {"_meta": {"title": "🔧 Image Resize"}, "inputs": {"image": ["1565", 0], "width": 1536, "height": 1536, "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "interpolation": "lanc<PERSON>s"}, "class_type": "ImageResize+"}, "1646": {"_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "inputs": {"dtype": "float32_torch", "seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "use_yolo": false, "pose_ckpt": "none", "depth_ckpt": "none", "normal_ckpt": "none", "seg_pellete": true, "show_pose_object": false, "remove_background": true, "minimum_person_height": 0.5, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der"}, "1647": {"_meta": {"title": "SapiensSampler"}, "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "image": ["1611", 0], "model": ["1646", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27"}, "class_type": "SapiensSampler"}, "1648": {"_meta": {"title": "🔧 Get Image Size"}, "inputs": {"image": ["1565", 0]}, "class_type": "GetImageSize+"}, "1649": {"_meta": {"title": "Upscale Image"}, "inputs": {"crop": "disabled", "image": ["1650", 0], "width": ["1648", 0], "height": ["1648", 1], "upscale_method": "bicubic"}, "class_type": "ImageScale"}, "1650": {"_meta": {"title": "🔍 CR Upscale Image"}, "inputs": {"mode": "resize", "image": ["1222", 0], "supersample": "true", "resize_width": ["1648", 0], "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "rescale_factor": 1, "rounding_modulus": 8, "resampling_method": "lanc<PERSON>s"}, "class_type": "CR Upscale Image"}}, "extra_data": {"extra_pnginfo": {"workflow": {"extra": {"ds": {"scale": 0.37974983358324305, "offset": [8045.3141121687395, -1889.************]}}, "links": [[1497, 1224, 0, 1222, 0, "LATENT"], [1502, 1227, 0, 1224, 1, "GUIDER"], [1503, 1225, 0, 1224, 2, "SAMPLER"], [1881, 1484, 0, 1224, 0, "NOISE"], [1895, 1488, 0, 1227, 0, "MODEL"], [1897, 1489, 0, 1490, 0, "CONDITIONING"], [1900, 1490, 0, 1491, 0, "CONDITIONING"], [1904, 1492, 0, 1491, 1, "CONDITIONING"], [1908, 1491, 2, 1224, 4, "LATENT"], [1920, 1510, 0, 1491, 3, "IMAGE"], [1951, 1532, 0, 1530, 0, "IMAGE"], [1954, 1530, 0, 1531, 2, "CONTROL_NET_STACK"], [1962, 1491, 0, 1531, 0, "CONDITIONING"], [1963, 1492, 0, 1531, 1, "CONDITIONING"], [1964, 1531, 0, 1227, 1, "CONDITIONING"], [1965, 1532, 0, 1519, 0, "IMAGE"], [1999, 1102, 0, 1551, 0, "*"], [2004, 1551, 0, 1491, 2, "VAE"], [2005, 1551, 0, 1222, 1, "VAE"], [2012, 1510, 0, 1532, 0, "IMAGE"], [2024, 1226, 0, 1224, 3, "SIGMAS"], [2029, 1566, 0, 1510, 0, "*"], [2056, 1566, 0, 1587, 0, "*"], [2067, 1568, 0, 1489, 1, "STRING"], [2069, 1591, 0, 1592, 0, "MODEL"], [2070, 1590, 0, 1592, 1, "CLIP"], [2076, 1532, 0, 1530, 1, "IMAGE"], [2081, 1594, 0, 1492, 0, "CLIP"], [2082, 1594, 0, 1489, 0, "CLIP"], [2083, 1593, 0, 1488, 0, "MODEL"], [2084, 1593, 0, 1226, 0, "MODEL"], [2085, 1592, 0, 1593, 0, "*"], [2086, 1592, 1, 1594, 0, "*"], [2089, 1601, 0, 1602, 1, "STRING"], [2090, 1601, 1, 1602, 2, "STRING"], [2093, 1603, 0, 1588, 0, "MASK"], [2094, 1603, 0, 1491, 4, "MASK"], [2111, 1611, 0, 1566, 0, "*"], [2125, 1616, 0, 1622, 0, "*"], [2128, 1622, 0, 1611, 1, "INT"], [2130, 1616, 1, 1628, 0, "*"], [2131, 1628, 0, 1611, 2, "INT"], [2151, 1642, 0, 1616, 0, "IMAGE"], [2152, 1642, 0, 1611, 0, "IMAGE"], [2162, 1565, 0, 1644, 0, "IMAGE"], [2163, 1644, 0, 1642, 0, "*"], [2165, 1646, 0, 1647, 0, "MODEL_SAPIEN"], [2166, 1587, 0, 1647, 1, "IMAGE"], [2167, 1647, 4, 1603, 0, "MASK"], [2168, 1565, 0, 1648, 0, "IMAGE"], [2169, 1648, 0, 1649, 1, "INT"], [2170, 1648, 1, 1649, 2, "INT"], [2171, 1648, 0, 1650, 1, "INT"], [2172, 1650, 0, 1649, 0, "IMAGE"], [2173, 1222, 0, 1650, 0, "IMAGE"], [2174, 1649, 0, 1602, 0, "IMAGE"]], "nodes": [{"id": 1102, "pos": [-8946, 2337], "mode": 0, "size": [315, 58], "type": "VAELoader", "flags": {}, "order": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1999], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 1490, "pos": [-8027.3125, 3369.19775390625], "mode": 0, "size": [211.6000061035156, 58], "type": "FluxGuidance", "flags": {}, "order": 21, "inputs": [{"link": 1897, "name": "conditioning", "type": "CONDITIONING"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1900], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 1531, "pos": [-6867, 3179], "mode": 0, "size": [274.56842041015625, 98], "type": "CR Apply Multi-ControlNet", "flags": {}, "order": 35, "inputs": [{"link": 1962, "name": "base_positive", "type": "CONDITIONING"}, {"link": 1963, "name": "base_negative", "type": "CONDITIONING"}, {"link": 1954, "name": "controlnet_stack", "type": "CONTROL_NET_STACK"}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [1964], "shape": 3, "slot_index": 0}, {"name": "base_neg", "type": "CONDITIONING", "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 1551, "pos": [-8519, 2329], "mode": 0, "size": [75, 26], "type": "Reroute", "color": "#322", "flags": {}, "order": 9, "inputs": [{"link": 1999, "name": "", "type": "*"}], "bgcolor": "#533", "outputs": [{"name": "", "type": "VAE", "links": [2004, 2005], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1590, "pos": [-8947, 2143], "mode": 0, "size": [315, 106], "type": "DualCLIPLoader", "flags": {}, "order": 1, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [2070], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 1591, "pos": [-8950, 1970], "mode": 0, "size": [315, 82], "type": "UNETLoader", "color": "#494949", "flags": {}, "order": 2, "inputs": [], "bgcolor": "#353535", "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2069], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"]}, {"id": 1492, "pos": [-8017, 3469], "mode": 0, "size": [210, 125.7953643798828], "type": "CLIPTextEncode", "flags": {"collapsed": true}, "order": 18, "inputs": [{"link": 2081, "name": "clip", "type": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1904, 1963], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1489, "pos": [-8333, 3422], "mode": 0, "size": [210, 125.7953643798828], "type": "CLIPTextEncode", "flags": {"collapsed": false}, "order": 19, "inputs": [{"link": 2082, "name": "clip", "type": "CLIP"}, {"link": 2067, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1897], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 1488, "pos": [-8321, 3349], "mode": 0, "size": [210, 26], "type": "DifferentialDiffusion", "flags": {"collapsed": true}, "order": 16, "inputs": [{"link": 2083, "name": "model", "type": "MODEL"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1895], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 1593, "pos": [-8756, 3211], "mode": 0, "size": [75, 26], "type": "Reroute", "color": "#223", "flags": {}, "order": 13, "inputs": [{"link": 2085, "name": "", "type": "*"}], "bgcolor": "#335", "outputs": [{"name": "", "type": "MODEL", "links": [2083, 2084], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1594, "pos": [-8752, 3338], "mode": 0, "size": [75, 26], "type": "Reroute", "color": "#432", "flags": {}, "order": 14, "inputs": [{"link": 2086, "name": "", "type": "*"}], "bgcolor": "#653", "outputs": [{"name": "", "type": "CLIP", "links": [2081, 2082], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1225, "pos": [-6244, 3448], "mode": 0, "size": [314.************, 58], "type": "KSamplerSelect", "flags": {}, "order": 3, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1503], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 1227, "pos": [-6245, 3322], "mode": 0, "size": [263.1893615722656, 46], "type": "BasicGuider", "flags": {}, "order": 36, "inputs": [{"link": 1895, "name": "model", "type": "MODEL", "slot_index": 0}, {"link": 1964, "name": "conditioning", "type": "CONDITIONING", "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1502], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 1530, "pos": [-7114, 3629], "mode": 0, "size": [563.9595947265625, 454], "type": "CR Multi-ControlNet Stack", "flags": {}, "order": 31, "inputs": [{"link": 1951, "name": "image_1", "type": "IMAGE", "shape": 7}, {"link": 2076, "name": "image_2", "type": "IMAGE", "shape": 7}, {"name": "image_3", "type": "IMAGE", "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [1954], "shape": 3, "slot_index": 0}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.1, 0.2, 0.5, "Off", "None", 1, 0, 1]}, {"id": 1226, "pos": [-6252, 3587], "mode": 0, "size": [309.76611328125, 106], "type": "BasicScheduler", "color": "#232", "flags": {}, "order": 17, "inputs": [{"link": 2084, "name": "model", "type": "MODEL", "slot_index": 0}], "bgcolor": "#353", "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [2024], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, "${denoised?number}"]}, {"id": 1491, "pos": [-7426, 3182], "mode": 0, "size": [216.59999084472656, 138], "type": "InpaintModelConditioning", "flags": {}, "order": 34, "inputs": [{"link": 1900, "name": "positive", "type": "CONDITIONING"}, {"link": 1904, "name": "negative", "type": "CONDITIONING"}, {"link": 2004, "name": "vae", "type": "VAE"}, {"link": 1920, "name": "pixels", "type": "IMAGE"}, {"link": 2094, "name": "mask", "type": "MASK"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1962], "shape": 3, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1908], "shape": 3, "slot_index": 2}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 1224, "pos": [-5836, 3193], "mode": 0, "size": [314.************, 106], "type": "SamplerCustomAdvanced", "flags": {}, "order": 37, "inputs": [{"link": 1881, "name": "noise", "type": "NOISE", "slot_index": 0}, {"link": 1502, "name": "guider", "type": "GUIDER", "slot_index": 1}, {"link": 1503, "name": "sampler", "type": "SAMPLER", "slot_index": 2}, {"link": 2024, "name": "sigmas", "type": "SIGMAS", "slot_index": 3}, {"link": 1908, "name": "latent_image", "type": "LATENT", "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1497], "shape": 3, "slot_index": 0}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 1510, "pos": [-7989, 3556], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 26, "inputs": [{"link": 2029, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [1920, 2012], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1532, "pos": [-7770, 3658], "mode": 0, "size": [210, 58], "type": "Zoe-DepthMapPreprocessor", "flags": {}, "order": 28, "inputs": [{"link": 2012, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1951, 1965, 2076], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [1024]}, {"id": 1628, "pos": [-8330, 2750], "mode": 0, "size": [354.8526611328125, 193.51210021972656], "type": "ConrainPythonExecutor", "flags": {"collapsed": true}, "order": 23, "inputs": [{"link": 2130, "name": "any_a", "type": "*", "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2131], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1566, "pos": [-7915, 2967], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 25, "inputs": [{"link": 2111, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [2029, 2056], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1622, "pos": [-8330, 2670], "mode": 0, "size": [354.8526611328125, 193.51210021972656], "type": "ConrainPythonExecutor", "flags": {"collapsed": true}, "order": 22, "inputs": [{"link": 2125, "name": "any_a", "type": "*", "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [2128], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 1642, "pos": [-8528, 2465], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 15, "inputs": [{"link": 2163, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [2151, 2152], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1568, "pos": [-8985, 3539], "mode": 0, "size": [602.1065673828125, 245.61325073242188], "type": "ConrainRandomPrompts", "color": "#232", "flags": {}, "order": 4, "title": "正向提示词", "inputs": [], "bgcolor": "#353", "outputs": [{"name": "prompt", "type": "STRING", "links": [2067], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 991, "fixed"]}, {"id": 1484, "pos": [-6249, 3175], "mode": 0, "size": [255.33419799804688, 82], "type": "RandomNoise", "color": "#232", "flags": {}, "order": 5, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1881], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed?number}", "fixed"]}, {"id": 1519, "pos": [-7427, 3478], "mode": 4, "size": [210, 246], "type": "PreviewImage", "flags": {}, "order": 30, "inputs": [{"link": 1965, "name": "images", "type": "IMAGE"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 1616, "pos": [-8581, 2653], "mode": 0, "size": [214.20001220703125, 66], "type": "GetImageSize+", "flags": {}, "order": 20, "inputs": [{"link": 2151, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "width", "type": "INT", "links": [2125], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2130], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 1611, "pos": [-8079, 2479], "mode": 0, "size": [316.8935546875, 239.64944458007812], "type": "ImageCrop+", "flags": {}, "order": 24, "inputs": [{"link": 2152, "name": "image", "type": "IMAGE"}, {"link": 2128, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 2131, "name": "height", "type": "INT", "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2111], "slot_index": 0}, {"name": "x", "type": "INT"}, {"name": "y", "type": "INT"}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 1644, "pos": [-8753, 2941], "mode": 0, "size": [315, 218], "type": "ImageResize+", "flags": {"collapsed": true}, "order": 11, "inputs": [{"link": 2162, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2163], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 1587, "pos": [-7624.634765625, 1965.79296875], "mode": 0, "size": [75, 26], "type": "Reroute", "flags": {}, "order": 27, "inputs": [{"link": 2056, "name": "", "type": "*"}], "outputs": [{"name": "", "type": "IMAGE", "links": [2166], "slot_index": 0}], "properties": {"horizontal": false, "showOutputText": false}}, {"id": 1646, "pos": [-7584, 2101], "mode": 0, "size": [659.2952270507812, 298], "type": "Sapiens<PERSON><PERSON>der", "flags": {}, "order": 6, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [2165], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 1647, "pos": [-6796, 2111], "mode": 0, "size": [315, 258], "type": "SapiensSampler", "flags": {}, "order": 29, "inputs": [{"link": 2165, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 2166, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [2167], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 1603, "pos": [-7513, 2612], "mode": 0, "size": [340.20001220703125, 246], "type": "ConrainGrowMaskWithBlur", "flags": {}, "order": 32, "inputs": [{"link": 2167, "name": "mask", "type": "MASK"}], "outputs": [{"name": "mask", "type": "MASK", "links": [2093, 2094], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK"}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 1588, "pos": [-6836, 2612], "mode": 4, "size": [210, 246], "type": "MaskPreview+", "flags": {}, "order": 33, "inputs": [{"link": 2093, "name": "mask", "type": "MASK"}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 1222, "pos": [-5430, 3233], "mode": 0, "size": [190.54541015625, 46], "type": "VAEDecode", "flags": {}, "order": 38, "inputs": [{"link": 1497, "name": "samples", "type": "LATENT"}, {"link": 2005, "name": "vae", "type": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2173], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 1650, "pos": [-5533.28759765625, 3411.595947265625], "mode": 0, "size": [315, 222], "type": "CR Upscale Image", "flags": {}, "order": 39, "inputs": [{"link": 2173, "name": "image", "type": "IMAGE"}, {"link": 2171, "name": "resize_width", "type": "INT", "widget": {"name": "resize_width"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2172], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, 1024, "lanc<PERSON>s", "true", 8]}, {"id": 1602, "pos": [-5533.2548828125, 3699.4931640625], "mode": 0, "size": [320, 266], "type": "ConrainImageSave", "flags": {}, "order": 41, "inputs": [{"link": 2174, "name": "images", "type": "IMAGE", "label": "images"}, {"link": 2089, "name": "output_path", "type": "STRING", "label": "output_path", "widget": {"name": "output_path"}}, {"link": 2090, "name": "filename_prefix", "type": "STRING", "label": "filename_prefix", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "label": "image_cnt", "links": [], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 1601, "pos": [-5918.41162109375, 3818.391357421875], "mode": 0, "size": [315, 190], "type": "Text String", "color": "#232", "flags": {}, "order": 7, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "STRING", "type": "STRING", "label": "STRING", "links": [2089], "shape": 3, "slot_index": 0}, {"name": "STRING", "type": "STRING", "label": "STRING", "links": [2090], "shape": 3, "slot_index": 1}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 1649, "pos": [-5903.705078125, 3602.07958984375], "mode": 0, "size": [315, 130], "type": "ImageScale", "flags": {}, "order": 40, "inputs": [{"link": 2172, "name": "image", "type": "IMAGE"}, {"link": 2169, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 2170, "name": "height", "type": "INT", "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2174], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 1648, "pos": [-5868.47998046875, 3431.07666015625], "mode": 0, "size": [214.20001220703125, 66], "type": "GetImageSize+", "flags": {}, "order": 12, "inputs": [{"link": 2168, "name": "image", "type": "IMAGE"}], "outputs": [{"name": "width", "type": "INT", "links": [2169, 2171], "slot_index": 0}, {"name": "height", "type": "INT", "links": [2170], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}}, {"id": 1565, "pos": [-8948, 2488], "mode": 0, "size": [315, 314], "type": "LoadImage", "color": "#232", "flags": {}, "order": 8, "inputs": [], "bgcolor": "#353", "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2162, 2168], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${targetImage}", "image"]}, {"id": 1592, "pos": [-8507, 1979], "mode": 0, "size": [339.9307556152344, 126], "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color": "#232", "flags": {}, "order": 10, "title": "人脸lora", "inputs": [{"link": 2069, "name": "model", "type": "MODEL"}, {"link": 2070, "name": "clip", "type": "CLIP"}], "bgcolor": "#353", "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2085], "shape": 3, "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2086], "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"]}], "config": {}, "groups": [{"id": 2, "color": "#3f789e", "flags": {}, "title": "TEXT | GENERATION", "bounding": [-9014, 3105, 3862.244384765625, 1043.714111328125], "font_size": 24}, {"id": 3, "color": "#3f789e", "flags": {}, "title": "", "bounding": [-9015.4423828125, 1898.2938232421875, 1295.358154296875, 1176.14306640625], "font_size": 24}, {"id": 4, "color": "#3f789e", "flags": {}, "title": "Group", "bounding": [-7670.44873046875, 1903.500732421875, 1559.69091796875, 1167.5008544921875], "font_size": 24}], "version": 0.4, "last_link_id": 2174, "last_node_id": 1650, "seed_widgets": {"1484": 0, "1568": 1}}}}}
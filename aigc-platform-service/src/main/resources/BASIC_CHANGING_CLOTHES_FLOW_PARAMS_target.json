{"client_id": "${clientId}", "prompt": {"49": {"inputs": {"images": ["321", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["322", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["175", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存目录和文件名前缀"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["397", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "175": {"inputs": {"image": ["397", 0], "image_bounds": ["176", 0]}, "class_type": "Bounded Image Crop", "_meta": {"title": "Bounded Image Crop"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "195": {"inputs": {"image": ["194", 0], "image_bounds": ["176", 0]}, "class_type": "Bounded Image Crop", "_meta": {"title": "Bounded Image Crop"}}, "211": {"inputs": {"channel": "red", "image": ["195", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": 10, "padding_right": 10, "padding_top": 10, "padding_bottom": 10, "image": ["346", 0], "mask": ["317", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": 1152}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": 1536}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 661045452417164, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["436", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting.", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["321", 0], "mask": ["56", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "290": {"inputs": {"conditioning": ["277", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["276", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["317", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "301": {"inputs": {"image": ["300", 0], "image_bounds": ["242", 1]}, "class_type": "Bounded Image Crop", "_meta": {"title": "Bounded Image Crop"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["397", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["397", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 40, "tapered_corners": false, "mask": ["366", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "317": {"inputs": {"expand": 20, "tapered_corners": false, "mask": ["351", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["175", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "322": {"inputs": {"expand": 0, "tapered_corners": true, "mask": ["211", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["408", 0], "mask": ["369", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+450", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+450", "a": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["369", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["369", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "363": {"inputs": {"value": "a*2", "a": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "365": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "366": {"inputs": {"prompt": ["419", 0], "background": "white", "threshold": 0.3, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["397", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "367": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "368": {"inputs": {"text": ""}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "369": {"inputs": {"prompt": ["419", 0], "background": "white", "threshold": 0.3, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["408", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": 10, "padding_right": 10, "padding_top": 10, "padding_bottom": 10, "image": ["300", 0], "mask": ["317", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "397": {"inputs": {"upscale_method": "area", "scale_by": ["398", 0], "image": ["669", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "398": {"inputs": {"target_size": ["399", 0], "image": ["669", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "399": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "400": {"inputs": {"image": ["669", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "401": {"inputs": {"seed": 1785}, "class_type": "CR Seed", "_meta": {"title": "最大支持尺寸"}}, "404": {"inputs": {"image": ["462", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "405": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "406": {"inputs": {"target_size": ["405", 0], "image": ["462", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "408": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["462", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "417": {"inputs": {"prompt": "请分析图片中衣服的类型，判断是上装、下装、还是套装。\n如果是上装输出coat，如果是下装输出pants,如果是套装输出outfit\n示例：\ncoat\npants\npants\noutfit", "llm_model": "default", "image_list": ["450", 0]}, "class_type": "LLModel", "_meta": {"title": "llmodel"}}, "418": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\n\tif any_a is not None and len(any_a) > 0 and any_a != \"\":\n\t\treturn [any_a]\n\t\t\n\treturn [any_b]", "any_a": ["368", 0], "any_b": ["417", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "419": {"inputs": {"text": ["418", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "433": {"inputs": {"text": "1"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "434": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]", "any_a": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "435": {"inputs": {"text": ["434", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "436": {"inputs": {"multiply_by": ["435", 0], "latents": ["282", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "449": {"inputs": {"target_size": 512, "image": ["408", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "450": {"inputs": {"upscale_method": "nearest-exact", "scale_by": ["449", 0], "image": ["408", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "451": {"inputs": {"images": ["306", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "456": {"inputs": {"image": "${maskImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "459": {"inputs": {"width": ["404", 0], "height": ["404", 1], "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["456", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "460": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["459", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "461": {"inputs": {"channel": "red", "image": ["460", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "462": {"inputs": {"image": "${referenceImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "463": {"inputs": {"image": "${clotheImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "666": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "是否使用风格lora生成图"}}, "667": {"inputs": {"comparison": "a == b", "a": ["666", 0], "b": ["668", 0]}, "class_type": "easy compare", "_meta": {"title": "Compare"}}, "668": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "669": {"inputs": {"boolean": ["667", 0], "on_true": ["761", 0], "on_false": ["673", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "672": {"inputs": {"text": "1", "anything": ["436", 1]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "673": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n", "any_b": ["463", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "674": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "675": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "676": {"inputs": {"text": "", "clip": ["675", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "677": {"inputs": {"noise_mask": false, "positive": ["682", 0], "negative": ["676", 0], "vae": ["674", 0], "pixels": ["697", 0], "mask": ["693", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "678": {"inputs": {"text": "Replace the face in [IMAGE2] with the face in [IMAGE1]", "clip": ["675", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "679": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "680": {"inputs": {"lora_name": "ACE_Plus/comfyui_portrait_lora64.safetensors", "strength_model": 1, "model": ["679", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "681": {"inputs": {"model": ["680", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "682": {"inputs": {"guidance": 50, "conditioning": ["678", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "683": {"inputs": {"samples": ["685", 0], "vae": ["674", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "684": {"inputs": {"image": ["683", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "685": {"inputs": {"seed": 201421469478745, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["681", 0], "positive": ["677", 0], "negative": ["677", 1], "latent_image": ["702", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "686": {"inputs": {"width": ["698", 0], "height": ["699", 0], "x": 0, "y": 0, "image": ["683", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "687": {"inputs": {"width": ["716", 1], "height": ["716", 2], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "688": {"inputs": {"mask": ["704", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "689": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["687", 0], "source": ["709", 0], "mask": ["711", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "690": {"inputs": {"width": ["709", 1], "height": ["709", 2], "red": 0, "green": 0, "blue": 0}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "693": {"inputs": {"channel": "red", "image": ["696", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "694": {"inputs": {"image": ["697", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "695": {"inputs": {"image": ["723", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "696": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["688", 0], "image2": ["690", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "697": {"inputs": {"direction": "right", "match_image_size": true, "image1": ["723", 0], "image2": ["689", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "698": {"inputs": {"value": "a*b/c", "a": ["684", 0], "b": ["695", 0], "c": ["694", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "699": {"inputs": {"value": "a*b/c", "a": ["684", 1], "b": ["695", 1], "c": ["694", 1]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "700": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "701": {"inputs": {"seg_select": "3.<PERSON>", "add_seg_index": "2,23,24,25,26", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["700", 0], "image": ["463", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "702": {"inputs": {"multiply_by": 1, "latents": ["677", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "703": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["463", 0], "mask": ["701", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "704": {"inputs": {"expand": 15, "tapered_corners": true, "mask": ["721", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "705": {"inputs": {"blend_factor": 1, "feathering": 16, "target": ["463", 0], "target_bounds": ["703", 1], "source": ["686", 0]}, "class_type": "Bounded Image Blend", "_meta": {"title": "Bounded Image Blend"}}, "708": {"inputs": {"seg_select": "3.<PERSON>", "add_seg_index": "2,23,24,25,26", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["700", 0], "image": ["729", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "709": {"inputs": {"width": 768, "height": 768, "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["713", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "711": {"inputs": {"expand": 10, "tapered_corners": true, "mask": ["717", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "712": {"inputs": {"seed": 128}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "713": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["729", 0], "mask": ["708", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "714": {"inputs": {"mask": ["708", 4]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "715": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["714", 0], "mask": ["708", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "716": {"inputs": {"width": 768, "height": 768, "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["715", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "717": {"inputs": {"channel": "red", "image": ["716", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "718": {"inputs": {"mask": ["701", 4]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "719": {"inputs": {"padding_left": ["712", 0], "padding_right": ["712", 0], "padding_top": ["712", 0], "padding_bottom": ["712", 0], "image": ["718", 0], "mask": ["701", 4]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "720": {"inputs": {"width": ["722", 0], "height": ["722", 0], "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["719", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "721": {"inputs": {"channel": "red", "image": ["720", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "722": {"inputs": {"seed": 768}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "723": {"inputs": {"width": ["722", 0], "height": ["722", 0], "upscale_method": "area", "keep_proportion": true, "divisible_by": 0, "image": ["703", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "726": {"inputs": {"blend_factor": 1, "feathering": 16, "target": ["463", 0], "target_bounds": ["703", 1], "source": ["742", 0]}, "class_type": "Bounded Image Blend", "_meta": {"title": "Bounded Image Blend"}}, "729": {"inputs": {"image": "pasted/image (659).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "738": {"inputs": {"images": ["686", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "741": {"inputs": {"prompts": "A 23-year-old Asian male, handsome, fair skinned,", "seed": 288}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "conrain random prompts"}}, "742": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 591459844315081, "steps": 10, "cfg": 2, "sampler_name": "euler", "scheduler": "beta", "denoise": 0.6, "feather": 3, "noise_mask": true, "force_inpaint": false, "bbox_threshold": 0.5, "bbox_dilation": 2, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": 0, "noise_mask_feather": false, "image": ["686", 0], "detailer_pipe": ["743", 0]}, "class_type": "FaceDetailerPipe", "_meta": {"title": "FaceDetailer (pipe)"}}, "743": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["744", 0], "bbox_detector": ["746", 0]}, "class_type": "BasicPipeToDetailerPipe", "_meta": {"title": "BasicPipe -> DetailerPipe"}}, "744": {"inputs": {"model": ["752", 0], "clip": ["750", 1], "vae": ["754", 0], "positive": ["745", 0], "negative": ["747", 0]}, "class_type": "ToBasicPipe", "_meta": {"title": "ToBasicPipe"}}, "745": {"inputs": {"text": ["741", 0], "clip": ["750", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "修脸prompt"}}, "746": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "747": {"inputs": {}, "class_type": "ImpactNegativeConditioningPlaceholder", "_meta": {"title": "Negative Cond Placeholder"}}, "748": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "749": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "750": {"inputs": {"lora_name": "online_product/子期_5225_20241229_112310/子期_5225_20241229_112310-flux/子期_5225_20241229_112310-flux.safetensors", "strength_model": "1", "strength_clip": "1", "model": ["749", 0], "clip": ["748", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "751": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "752": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["750", 0], "model2": ["751", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "ModelMergeFlux1"}}, "753": {"inputs": {"images": ["742", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "754": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "755": {"inputs": {"images": ["726", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "758": {"inputs": {"text": "1"}, "class_type": "CR Text", "_meta": {"title": "输入图是否要换头"}}, "759": {"inputs": {"comparison": "a == b", "a": ["758", 0], "b": ["760", 0]}, "class_type": "easy compare", "_meta": {"title": "Compare"}}, "760": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "761": {"inputs": {"boolean": ["759", 0], "on_true": ["463", 0], "on_false": ["726", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "762": {"inputs": {"images": ["366", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 762, "last_link_id": 1415, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3768.00537109375, 815.0321655273438], "size": [261.10693359375, 178], "flags": {}, "order": 268, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 820}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2910.0048828125, 882.0318603515625], "size": [264.5999755859375, 26], "flags": {}, "order": 272, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2914.0048828125, 1064.0301513671875], "size": [264.5999755859375, 26], "flags": {}, "order": 255, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 56, "type": "ImageToMask", "pos": [-2102.005859375, 906.0316772460938], "size": [210, 59.905555725097656], "flags": {}, "order": 280, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [792], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 60, "type": "Get Image Size", "pos": [-3979.00537109375, 1269.0284423828125], "size": [298.42425537109375, 46], "flags": {}, "order": 269, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 821}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 61, "type": "Get Image Size", "pos": [-3224.4384765625, 2070.94091796875], "size": [210, 46], "flags": {}, "order": 254, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 113, "type": "Reroute", "pos": [-4104.23046875, 671.5852661132812], "size": [75, 26], "flags": {}, "order": 250, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 819}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 167, "type": "Reroute", "pos": [-4471.00634765625, 789.0323486328125], "size": [75, 26], "flags": {}, "order": 267, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 441}], "outputs": [{"name": "", "type": "IMAGE", "links": [820, 821, 822, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-7177.376953125, 260.0857238769531], "size": [248.14456176757812, 150], "flags": {}, "order": 226, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 175, "type": "Bounded Image Crop", "pos": [-5230.376953125, 345.08587646484375], "size": [229.20001220703125, 46], "flags": {}, "order": 243, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 609}, {"name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 331}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [359], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5849.376953125, 470.0864562988281], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 238, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [331, 336, 544], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "type": "MaskToImage", "pos": [-5882.376953125, 959.0874633789062], "size": [264.5999755859375, 26], "flags": {}, "order": 227, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [335], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 195, "type": "Bounded Image Crop", "pos": [-5384.376953125, 966.0874633789062], "size": [229.20001220703125, 46], "flags": {}, "order": 244, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 335}, {"name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 336}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [357], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 208, "type": "Reroute", "pos": [-4375.00634765625, 471.0330810546875], "size": [75, 26], "flags": {}, "order": 247, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 359}], "outputs": [{"name": "", "type": "IMAGE", "links": [819], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-5059.376953125, 1082.086669921875], "size": [210, 84.04366302490234], "flags": {}, "order": 248, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 357}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-7150.09130859375, 1636.882568359375], "size": [243.56057739257812, 150], "flags": {}, "order": 252, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 575}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [746], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5069.09130859375, 1738.2135009765625], "size": [210, 138], "flags": {}, "order": 265, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "link": null, "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 420, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 421, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 245, "type": "EmptyImage", "pos": [-5775.09130859375, 1565.8828125], "size": [243.03533935546875, 102], "flags": {}, "order": 241, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 759, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SimpleMath+", "pos": [-5419.09130859375, 1743.21337890625], "size": [220.35072326660156, 98], "flags": {}, "order": 264, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5419.09130859375, 1544.214111328125], "size": [210, 112.43743896484375], "flags": {}, "order": 263, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6550.53466796875, 1644.1640625], "size": [336.6972351074219, 216.9816436767578], "flags": {}, "order": 259, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "type": "ImageScaleBy", "pos": [-6098.09130859375, 1692.213623046875], "size": [261.7075500488281, 81.00054931640625], "flags": {}, "order": 260, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6242.7333984375, 534.659423828125], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 234, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "type": "JWIntegerMin", "pos": [-6982.376953125, 666.0879516601562], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 222, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 259, "type": "JWIntegerMin", "pos": [-6988.376953125, 1016.08740234375], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 223, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6629.376953125, 642.0877075195312], "size": [210, 71.68185424804688], "flags": {}, "order": 229, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 265, "type": "JWIntegerMax", "pos": [-6671.376953125, 933.0877685546875], "size": [210, 71.68185424804688], "flags": {}, "order": 230, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 676, 756], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-6980.376953125, 802.0880126953125], "size": [210, 67.93143463134766], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 269, "type": "Note", "pos": [-6616.376953125, 800.0880126953125], "size": [210, 67.93143463134766], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2964.4384765625, 1663.9404296875], "size": [234.29580688476562, 262], "flags": {}, "order": 283, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 850, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [661045452417164, "fixed", 20, 1, "euler", "simple", 1]}, {"id": 276, "type": "VAEDecode", "pos": [-2601.4384765625, 1604.9405517578125], "size": [210, 46], "flags": {}, "order": 285, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [512, 793, 1260], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4118.8310546875, 1714.2647705078125], "size": [210, 84.2347640991211], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500, 513], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting."], "color": "#222", "bgcolor": "#000"}, {"id": 278, "type": "FluxGuidance", "pos": [-3865.8359375, 1826.************], "size": [210, 58], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 279, "type": "UNETLoader", "pos": [-3845.8359375, 1619.2647705078125], "size": [210, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "default"]}, {"id": 280, "type": "VAELoader", "pos": [-4452.8623046875, 1858.************], "size": [300, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4479.83203125, 1664.2646484375], "size": [315, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux"]}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3258.4384765625, 1734.940673828125], "size": [210, 138], "flags": {}, "order": 281, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 791, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 792, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [848], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3256.462890625, 1645.7598876953125], "size": [184.8000030517578, 26], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4473.435546875, 2063.94091796875], "size": [370, 60], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3867.4384765625, 1947.9410400390625], "size": [210, 78], "flags": {}, "order": 270, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 822, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": []}, {"id": 286, "type": "StyleModelApply", "pos": [-3565.4384765625, 1817.9403076171875], "size": [210, 122], "flags": {}, "order": 273, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "shape": 7, "label": "CLIP视觉输出"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": []}, {"id": 287, "type": "StyleModelLoader", "pos": [-4462.0771484375, 1981.087158203125], "size": [340, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "type": "InpaintStitch", "pos": [-2274.4384765625, 1607.9405517578125], "size": [256.60272216796875, 78], "flags": {}, "order": 286, "mode": 4, "inputs": [{"name": "stitch", "type": "STITCH", "link": 511, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 512, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 289, "type": "InpaintCrop", "pos": [-3717.4384765625, 2123.94091796875], "size": [245.64613342285156, 386], "flags": {}, "order": 275, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": null, "label": "图像"}, {"name": "mask", "type": "MASK", "link": null, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "shape": 7, "label": "上下文遮罩(可选)"}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [511], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [723], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1536, 32]}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3822.0810546875, 1755.397216796875], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 100, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 513, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 291, "type": "ImageCrop", "pos": [-2490.54296875, 1867.29833984375], "size": [210, 118], "flags": {}, "order": 287, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 793}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 297, "type": "ImageToMask", "pos": [-2102.005859375, 609.0330200195312], "size": [210, 59.905555725097656], "flags": {}, "order": 279, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 300, "type": "MaskToImage", "pos": [-7108.09130859375, 1939.8818359375], "size": [176.39999389648438, 26], "flags": {}, "order": 253, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 576}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [538, 765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 301, "type": "Bounded Image Crop", "pos": [-5419.09130859375, 1927.2132568359375], "size": [229.20001220703125, 46], "flags": {}, "order": 257, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 538}, {"name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 746}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2961.54296875, 2145.298828125], "size": [75, 26], "flags": {}, "order": 220, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 305, "type": "Reroute", "pos": [-2614.54296875, 2141.298828125], "size": [75, 26], "flags": {}, "order": 245, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2154.469970703125, 1830.81201171875], "size": [239.0650634765625, 122], "flags": {}, "order": 289, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555, 879], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 308, "type": "ConrainImageSave", "pos": [-1897.437744140625, 2066.94091796875], "size": [231.75296020507812, 266], "flags": {}, "order": 290, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 873, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 874, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 314, "type": "Get Image Size", "pos": [-7483.376953125, 398.0863037109375], "size": [210, 46], "flags": {}, "order": 219, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 316, "type": "Get Image Size", "pos": [-6822.376953125, 270.0857238769531], "size": [210, 46], "flags": {}, "order": 228, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 319, "type": "GetImageSize+", "pos": [-6840.09130859375, 1630.8826904296875], "size": [214.20001220703125, 66], "flags": {}, "order": 256, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 320, "type": "GetImageSize+", "pos": [-5736.09130859375, 1877.8817138671875], "size": [214.20001220703125, 66], "flags": {}, "order": 262, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 321, "type": "easy imageConcat", "pos": [-3834.8525390625, 403.8241271972656], "size": [315, 102], "flags": {}, "order": 271, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 823}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [588, 589, 791], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 322, "type": "GrowMask", "pos": [-3365.26123046875, 981.890625], "size": [315, 82], "flags": {}, "order": 251, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 591}], "outputs": [{"name": "MASK", "type": "MASK", "links": [592], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, true]}, {"id": 323, "type": "easy imageConcat", "pos": [-2429.0048828125, 610.0330200195312], "size": [315, 102], "flags": {}, "order": 276, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 324, "type": "easy imageConcat", "pos": [-2513.51904296875, 930.753173828125], "size": [315, 102], "flags": {"collapsed": false}, "order": 277, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 328, "type": "Reroute", "pos": [-8867.5751953125, 396.0829162597656], "size": [75, 26], "flags": {}, "order": 218, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 609, 610, 794], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 330, "type": "Reroute", "pos": [-9245.07421875, 1500.900634765625], "size": [75, 26], "flags": {}, "order": 152, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 818}], "outputs": [{"name": "", "type": "IMAGE", "links": [646, 796], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-7835.09130859375, 1527.214111328125], "size": [75, 26], "flags": {}, "order": 179, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1261}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7586.09130859375, 1980.21337890625], "size": [243.56057739257812, 150], "flags": {}, "order": 239, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 646}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 347, "type": "SimpleMath+", "pos": [-7894.65673828125, 1953.538330078125], "size": [210, 98], "flags": {"collapsed": false}, "order": 231, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "link": null, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+450"]}, {"id": 348, "type": "SimpleMath+", "pos": [-7893.09130859375, 2175.8818359375], "size": [210, 98], "flags": {"collapsed": false}, "order": 235, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "link": null, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+450"]}, {"id": 349, "type": "MaskToImage", "pos": [-7912.09130859375, 2250.212890625], "size": [176.39999389648438, 26], "flags": {}, "order": 184, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7588.09130859375, 2230.212890625], "size": [243.56057739257812, 150], "flags": {}, "order": 240, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 351, "type": "ImageToMask", "pos": [-7278.09130859375, 2225.212890625], "size": [210, 83.63514709472656], "flags": {}, "order": 246, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [664], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 358, "type": "Note", "pos": [-7291.09130859375, 2075.21337890625], "size": [210, 67.93143463134766], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["先裁剪图片再grow mask提升速度"], "color": "#432", "bgcolor": "#653"}, {"id": 362, "type": "SimpleMath+", "pos": [-4408.435546875, 2163.94091796875], "size": [210, 98], "flags": {"collapsed": false}, "order": 232, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "link": null, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 363, "type": "SimpleMath+", "pos": [-5690.376953125, 1264.0855712890625], "size": [210, 98], "flags": {"collapsed": false}, "order": 236, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 676, "shape": 7}, {"name": "b", "type": "*", "link": null, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2"]}, {"id": 365, "type": "Conrain_SAMModelLoader", "pos": [-9051.5751953125, 987.0823974609375], "size": [269.19927978515625, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [709, 712], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 367, "type": "Conrain_GroundingDinoModelLoader", "pos": [-9037.5751953125, 1227.0826416015625], "size": [285.80181884765625, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [710, 713], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 368, "type": "CR Text", "pos": [-10282.5751953125, 1974.08447265625], "size": [261.96600341796875, 134.15919494628906], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [834], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": [""], "color": "#232", "bgcolor": "#353"}, {"id": 369, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8530.5751953125, 1448.0833740234375], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 175, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 712, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 713, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 796, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 838, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1261], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 370, "type": "Note", "pos": [-9243.904296875, 1751.2406005859375], "size": [269.7032470703125, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["上装输出coat，如果是下装输出pants,如果是套装输出outfit"], "color": "#432", "bgcolor": "#653"}, {"id": 376, "type": "PreviewImage", "pos": [-3400.4384765625, 2155.94091796875], "size": [210, 246], "flags": {}, "order": 278, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 723}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 384, "type": "ImageScaleBy", "pos": [-6132.09130859375, 2137.21337890625], "size": [261.7075500488281, 81.00054931640625], "flags": {}, "order": 261, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5093.09130859375, 2161.21337890625], "size": [210, 138], "flags": {}, "order": 266, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "link": null, "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 771, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 770, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 386, "type": "EmptyImage", "pos": [-5739.09130859375, 2071.21337890625], "size": [243.03533935546875, 102], "flags": {}, "order": 242, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 762, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "Reroute", "pos": [-6088.09130859375, 1511.2144775390625], "size": [75, 26], "flags": {}, "order": 233, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 391, "type": "Reroute", "pos": [-6071.09130859375, 1601.2139892578125], "size": [75, 26], "flags": {}, "order": 237, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-6692.09130859375, 2160.882080078125], "size": [243.56057739257812, 150], "flags": {}, "order": 258, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 764}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 397, "type": "ImageScaleBy", "pos": [-9650.5751953125, 402.0829772949219], "size": [217.8218994140625, 125.52959442138672], "flags": {}, "order": 217, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 806}, {"name": "scale_by", "type": "FLOAT", "link": 798, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 398, "type": "UpscaleSizeCalculator", "pos": [-9980.5751953125, 561.0836181640625], "size": [220, 118], "flags": {}, "order": 216, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 805}, {"name": "target_size", "type": "INT", "link": 797, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [798], "slot_index": 0}, {"name": "rescale_width", "type": "INT", "links": null}, {"name": "recover_factor", "type": "FLOAT", "links": null}, {"name": "recover_width", "type": "INT", "links": null}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 399, "type": "ConrainPythonExecutor", "pos": [-10581.5751953125, 327.0826416015625], "size": [365.00079345703125, 195.28152465820312], "flags": {}, "order": 215, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 800, "shape": 7}, {"name": "any_b", "type": "*", "link": 801, "shape": 7}, {"name": "any_c", "type": "*", "link": 802, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [797], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "type": "Get Image Size", "pos": [-10979.5751953125, 388.0828552246094], "size": [210, 46], "flags": {}, "order": 214, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 804}], "outputs": [{"name": "width", "type": "INT", "links": [800], "slot_index": 0}, {"name": "height", "type": "INT", "links": [801], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 401, "type": "CR Seed", "pos": [-11149.5751953125, 683.0833740234375], "size": [270.7088317871094, 109.29169464111328], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [802, 811], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "title": "最大支持尺寸", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 402, "type": "Reroute", "pos": [-11131.5751953125, 240.08245849609375], "size": [75, 26], "flags": {}, "order": 213, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1259}], "outputs": [{"name": "", "type": "*", "links": [804, 805, 806], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 404, "type": "Get Image Size", "pos": [-10600.8505859375, 1706.2664794921875], "size": [210, 46], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 827}], "outputs": [{"name": "width", "type": "INT", "links": [809, 895], "slot_index": 0}, {"name": "height", "type": "INT", "links": [810, 896], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 405, "type": "ConrainPythonExecutor", "pos": [-10844.5751953125, 1233.2913818359375], "size": [400, 200], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 809, "shape": 7}, {"name": "any_b", "type": "*", "link": 810, "shape": 7}, {"name": "any_c", "type": "*", "link": 811, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "type": "UpscaleSizeCalculator", "pos": [-10299.5751953125, 1368.0830078125], "size": [220, 102.22442626953125], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 828}, {"name": "target_size", "type": "INT", "link": 812, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [817, 897], "slot_index": 0}, {"name": "rescale_width", "type": "INT", "links": null}, {"name": "recover_factor", "type": "FLOAT", "links": null}, {"name": "recover_width", "type": "INT", "links": null}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 407, "type": "Reroute", "pos": [-10897.1416015625, 1661.578857421875], "size": [75, 26], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1257}], "outputs": [{"name": "", "type": "IMAGE", "links": [827, 828, 829], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScaleBy", "pos": [-10267.5751953125, 1607.0850830078125], "size": [210, 78], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 829}, {"name": "scale_by", "type": "FLOAT", "link": 817, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [818, 875, 877], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 417, "type": "LLModel", "pos": [-9660.5751953125, 1738.0843505859375], "size": [253.52423095703125, 143.5610809326172], "flags": {}, "order": 162, "mode": 0, "inputs": [{"name": "image_list", "type": "IMAGE", "link": 878, "shape": 7}, {"name": "ref_image", "type": "IMAGE", "link": null, "shape": 7}], "outputs": [{"name": "result_text", "type": "STRING", "links": [833], "slot_index": 0}, {"name": "result_detail", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "LLModel"}, "widgets_values": ["请分析图片中衣服的类型，判断是上装、下装、还是套装。\n如果是上装输出coat，如果是下装输出pants,如果是套装输出outfit\n示例：\ncoat\npants\npants\noutfit", "default"]}, {"id": 418, "type": "ConrainPythonExecutor", "pos": [-9724.5751953125, 1948.08447265625], "size": [400, 200], "flags": {}, "order": 166, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 834, "shape": 7}, {"name": "any_b", "type": "*", "link": 833, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [837], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\n\tif any_a is not None and len(any_a) > 0 and any_a != \"\":\n\t\treturn [any_a]\n\t\t\n\treturn [any_b]"]}, {"id": 419, "type": "ShowText|pysssss", "pos": [-9274.5751953125, 1618.0849609375], "size": [315, 126], "flags": {}, "order": 172, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 837, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [838, 839], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["coat"], "coat"]}, {"id": 434, "type": "ConrainPythonExecutor", "pos": [-4173.435546875, 2273.9404296875], "size": [255.5079803466797, 218.5600128173828], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 846, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [847], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "type": "JWStringToInteger", "pos": [-3963.4384765625, 2066.94091796875], "size": [210, 56.551239013671875], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 847, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [849], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 436, "type": "VHS_DuplicateLatents", "pos": [-3311.4384765625, 1941.9407958984375], "size": [260.3999938964844, 58.512535095214844], "flags": {}, "order": 282, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 848}, {"name": "multiply_by", "type": "INT", "link": 849, "widget": {"name": "multiply_by"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [850], "slot_index": 0}, {"name": "count", "type": "INT", "links": [1262], "slot_index": 1}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 449, "type": "UpscaleSizeCalculator", "pos": [-10285.5751953125, 1752.0843505859375], "size": [210, 118], "flags": {}, "order": 153, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 875}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [876], "slot_index": 0}, {"name": "rescale_width", "type": "INT", "links": null}, {"name": "recover_factor", "type": "FLOAT", "links": null}, {"name": "recover_width", "type": "INT", "links": null}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 450, "type": "ImageScaleBy", "pos": [-9958.5751953125, 1718.0845947265625], "size": [210, 79.86241149902344], "flags": {}, "order": 158, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 877}, {"name": "scale_by", "type": "FLOAT", "link": 876, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [878], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["nearest-exact", 1]}, {"id": 451, "type": "PreviewImage", "pos": [-1881.437744140625, 1736.940673828125], "size": [210, 246], "flags": {}, "order": 291, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 879}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 457, "type": "Reroute", "pos": [-11071.5751953125, 1005.3117065429688], "size": [75, 26], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 880}], "outputs": [{"name": "", "type": "IMAGE", "links": [885], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 459, "type": "ImageResize+", "pos": [-10483.5751953125, 1052.08**********], "size": [315, 218], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 885}, {"name": "width", "type": "INT", "link": 895, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 896, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [889], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "type": "ImageScaleBy", "pos": [-9953.5751953125, 1371.0830078125], "size": [214.26881408691406, 94.09839630126953], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 889}, {"name": "scale_by", "type": "FLOAT", "link": 897, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 590, "type": "LoadImage", "pos": [-6606, -4921], "size": [320, 314], "flags": {}, "order": 13, "mode": 4, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1162], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "shape": 3, "label": "MASK"}], "title": "导入人脸图片", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["prod_fix_3f43e4fc73ee4dadb1a21cbb17a38056.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 591, "type": "Text String", "pos": [-4893.80224609375, -4736.478515625], "size": [315, 190], "flags": {}, "order": 14, "mode": 4, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1164, 1167], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1165, 1221], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250301/100005/108683", "product_1071770", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 592, "type": "Text Concatenate", "pos": [-3766.80224609375, -4590.478515625], "size": [315, 178], "flags": {}, "order": 104, "mode": 4, "inputs": [{"name": "text_a", "type": "STRING", "link": 1153, "widget": {"name": "text_a"}, "label": "text_a", "shape": 7}, {"name": "text_b", "type": "STRING", "link": 1154, "widget": {"name": "text_b"}, "label": "text_b", "shape": 7}, {"name": "text_c", "type": "STRING", "link": 1155, "widget": {"name": "text_c"}, "label": "text_c", "shape": 7}, {"name": "text_d", "type": "STRING", "link": 1156, "widget": {"name": "text_d"}, "label": "text_d", "shape": 7}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1219], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 593, "type": "String to Text", "pos": [-5266.84912109375, -4523.29296875], "size": [315, 58], "flags": {"collapsed": true}, "order": 15, "mode": 4, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1155], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 594, "type": "String to Text", "pos": [-5288.84912109375, -4717.29345703125], "size": [315, 58], "flags": {"collapsed": true}, "order": 16, "mode": 4, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1153], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 595, "type": "LoadConrainReactorModels", "pos": [-6617, -5372], "size": [324.5391845703125, 190], "flags": {}, "order": 17, "mode": 4, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [1158], "slot_index": 0, "shape": 3, "label": "faceswapper_model"}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [1159], "slot_index": 1, "shape": 3, "label": "facedetection_model"}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [1160], "slot_index": 2, "shape": 3, "label": "facerestore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [1161], "slot_index": 3, "shape": 3, "label": "faceparse_model"}], "title": "导入换脸模型", "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"], "color": "#494949", "bgcolor": "#353535"}, {"id": 596, "type": "ConrainReActorFaceSwap", "pos": [-6078, -5381], "size": [367.79998779296875, 370], "flags": {}, "order": 173, "mode": 4, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 1157, "label": "input_image"}, {"name": "swap_model", "type": "FACE_MODEL", "link": 1158, "label": "swap_model"}, {"name": "facedetection", "type": "FACE_MODEL", "link": 1159, "label": "facedetection"}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 1160, "label": "face_restore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 1161, "label": "faceparse_model"}, {"name": "source_image", "type": "IMAGE", "link": 1162, "shape": 7, "label": "source_image"}, {"name": "face_model", "type": "FACE_MODEL", "link": null, "shape": 7, "label": "face_model"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1169, 1242], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "shape": 3, "label": "FACE_MODEL"}], "title": "换脸", "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, "0.7", "0.7", "no", "no", "0", "0", 1, "yes"], "color": "#494949", "bgcolor": "#353535"}, {"id": 597, "type": "ConrainImageSave", "pos": [-3210, -4896], "size": [320, 266], "flags": {}, "order": 199, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1163, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1164, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1165, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 598, "type": "Text Concatenate", "pos": [-4411.72216796875, -4769.42138671875], "size": [250, 142], "flags": {}, "order": 76, "mode": 4, "inputs": [{"name": "text_a", "type": "STRING", "link": 1166, "widget": {"name": "text_a"}, "label": "text_a", "shape": 7}, {"name": "text_b", "type": "STRING", "link": 1167, "widget": {"name": "text_b"}, "label": "text_b", "shape": 7}, {"name": "text_c", "type": "STRING", "link": null, "widget": {"name": "text_c"}, "label": "text_c", "shape": 7}, {"name": "text_d", "type": "STRING", "link": null, "widget": {"name": "text_d"}, "label": "text_d", "shape": 7}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1220], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 599, "type": "String to Text", "pos": [-4900.80224609375, -4865.478515625], "size": [315, 58], "flags": {"collapsed": false}, "order": 18, "mode": 4, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1166], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 600, "type": "InspyrenetRembg", "pos": [-4909, -5279], "size": [230, 90], "flags": {}, "order": 180, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1168, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1172], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1218], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 601, "type": "CR Upscale Image", "pos": [-5403, -5406], "size": [315, 222], "flags": {}, "order": 176, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1169, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1168, 1173], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 602, "type": "Note", "pos": [-4479, -5599], "size": [260, 110], "flags": {}, "order": 19, "mode": 2, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 603, "type": "EmptyImage", "pos": [-4454, -5418], "size": [231.5089111328125, 120.12616729736328], "flags": {}, "order": 186, "mode": 2, "inputs": [{"name": "width", "type": "INT", "link": 1170, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 1171, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1217], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 604, "type": "ImageRGBA2RGB", "pos": [-4468, -5201], "size": [252, 26], "flags": {}, "order": 185, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1172, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1216], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 605, "type": "Image Size to Number", "pos": [-4903, -5504], "size": [229.20001220703125, 126], "flags": {}, "order": 181, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1173, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [1170], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [1171], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 606, "type": "ImageScaleBy", "pos": [-3144, -5523], "size": [228.9691162109375, 78], "flags": {}, "order": 193, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1174, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 1175, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1239], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 607, "type": "UpscaleSizeCalculator", "pos": [-3458, -5411], "size": [220, 118], "flags": {}, "order": 190, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1176, "label": "image"}, {"name": "target_size", "type": "INT", "link": 1177, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [1175], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 608, "type": "CLIPTextEncode", "pos": [-9773.**********, -5126.47021484375], "size": [285.6000061035156, 54], "flags": {}, "order": 126, "mode": 2, "inputs": [{"name": "clip", "type": "CLIP", "link": 1178}, {"name": "text", "type": "STRING", "link": 1179, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1190], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 609, "type": "VAEDecode", "pos": [-8491, -5392], "size": [210, 46], "flags": {}, "order": 159, "mode": 2, "inputs": [{"name": "samples", "type": "LATENT", "link": 1180}, {"name": "vae", "type": "VAE", "link": 1181}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1225], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 610, "type": "VAELoader", "pos": [-8848.451171875, -5157.130859375], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 20, "mode": 2, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1181, 1211], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 611, "type": "DualCLIPLoader", "pos": [-10447.6953125, -5324.47021484375], "size": [315, 106], "flags": {}, "order": 21, "mode": 2, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1202], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 612, "type": "UNETLoader", "pos": [-10435, -5526], "size": [315, 82], "flags": {}, "order": 22, "mode": 2, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1201], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 613, "type": "SamplerCustomAdvanced", "pos": [-8802.451171875, -5515.130859375], "size": [236.8000030517578, 112.51068878173828], "flags": {}, "order": 155, "mode": 2, "inputs": [{"name": "noise", "type": "NOISE", "link": 1182, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1183, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1184, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1185, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1186, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1180], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 614, "type": "KSamplerSelect", "pos": [-9137.763671875, -5246.73828125], "size": [210, 58], "flags": {}, "order": 23, "mode": 2, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1184], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 615, "type": "BasicScheduler", "pos": [-9115.6982421875, -5034.4697265625], "size": [210, 106], "flags": {}, "order": 145, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1187, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1185], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", "20", 1]}, {"id": 616, "type": "BasicGuider", "pos": [-9099.7646484375, -5366.73828125], "size": [161.1999969482422, 46], "flags": {}, "order": 146, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1188, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1189, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1183], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 617, "type": "RandomNoise", "pos": [-9203.3984375, -5534.130859375], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 24, "mode": 2, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1182], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [211535066683165, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 618, "type": "FluxGuidance", "pos": [-9431.6982421875, -5204.47021484375], "size": [211.60000610351562, 58], "flags": {}, "order": 137, "mode": 2, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1190}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1189], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 619, "type": "EmptySD3LatentImage", "pos": [-9427.6982421875, -5081.4697265625], "size": [210, 86.50716400146484], "flags": {}, "order": 128, "mode": 2, "inputs": [{"name": "width", "type": "INT", "link": 1191, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1192, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1186], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 620, "type": "ModelSamplingFlux", "pos": [-9416.6982421875, -4893.46923828125], "size": [210, 122], "flags": {}, "order": 136, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1193, "slot_index": 0}, {"name": "width", "type": "INT", "link": 1194, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1195, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1187, 1188], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 621, "type": "ConrainRandomPrompts", "pos": [-10316, -5084], "size": [400, 200], "flags": {}, "order": 25, "mode": 2, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1196, 1197], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing { pink leggings}. The model is wearing { colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head}. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Ra<PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 622, "type": "ShowText|pysssss", "pos": [-9777.375, -4980.130859375], "size": [256.63372802734375, 226], "flags": {}, "order": 78, "mode": 2, "inputs": [{"name": "text", "type": "STRING", "link": 1196, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1179], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has <PERSON><PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),"], "(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has <PERSON><PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has <PERSON><PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has <PERSON><PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "(linrun2111:1.3),front view,whole body,The model is wearing A light yellow hoodie, The hoodie has a full-length zipper and long sleeves. ,zipped ,hood down.The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has <PERSON><PERSON><PERSON> Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),"]}, {"id": 623, "type": "CR Seed", "pos": [-9772.**********, -4512.46875], "size": [243.4204864501953, 102], "flags": {}, "order": 26, "mode": 2, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1234, 1241], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 624, "type": "CR Seed", "pos": [-9786.**********, -4698.4697265625], "size": [243.4204864501953, 102], "flags": {}, "order": 27, "mode": 2, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1233, 1240], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1340, "fixed"]}, {"id": 625, "type": "ConrainRandomPrompts", "pos": [-10301.099609375, -4757.529296875], "size": [411.6590881347656, 124], "flags": {}, "order": 28, "mode": 2, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1198], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 626, "type": "Reroute", "pos": [-8582.8046875, -4847.2861328125], "size": [75, 26], "flags": {}, "order": 79, "mode": 2, "inputs": [{"name": "", "type": "*", "link": 1197, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1154]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 627, "type": "Reroute", "pos": [-8617.8046875, -4716.2861328125], "size": [75, 26], "flags": {}, "order": 82, "mode": 2, "inputs": [{"name": "", "type": "*", "link": 1198, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1156]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 628, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-9780, -5331], "size": [377.5603942871094, 126], "flags": {}, "order": 114, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1199}, {"name": "clip", "type": "CLIP", "link": 1200}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1209, 1245], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1178, 1210, 1214], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/紫葡萄皮肤衣测试_7648_20250301_141220/紫葡萄皮肤衣测试_7648_20250301_141220-flux/紫葡萄皮肤衣测试_7648_20250301_141220-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 629, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-10065, -5515], "size": [228.4159698486328, 126], "flags": {}, "order": 77, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1201}, {"name": "clip", "type": "CLIP", "link": 1202}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1203], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1204], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 630, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-9768, -5506], "size": [395.14263916015625, 126], "flags": {}, "order": 101, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1203}, {"name": "clip", "type": "CLIP", "link": 1204}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1199], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1200], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/风格-瑜伽服海边_5509_20250106_172128/风格-瑜伽服海边_5509_20250106_172128-flux/风格-瑜伽服海边_5509_20250106_172128-flux.safetensors", "0.8", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 631, "type": "FaceDetailerPipe", "pos": [-7154.291015625, -5590.5810546875], "size": [346, 782], "flags": {}, "order": 167, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1205}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 1206}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null, "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1157], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 299360578992323, "randomize", 10, "0.8", "euler", "beta", 0.4, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 632, "type": "BasicPipeToDetailerPipe", "pos": [-7467, -5585], "size": [262, 204.4281768798828], "flags": {}, "order": 147, "mode": 2, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 1207}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1208}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "shape": 7}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [1206], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 633, "type": "ToBasicPipe", "pos": [-7768, -5585], "size": [241.79998779296875, 106], "flags": {}, "order": 138, "mode": 2, "inputs": [{"name": "model", "type": "MODEL", "link": 1209}, {"name": "clip", "type": "CLIP", "link": 1210}, {"name": "vae", "type": "VAE", "link": 1211}, {"name": "positive", "type": "CONDITIONING", "link": 1212}, {"name": "negative", "type": "CONDITIONING", "link": 1213}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [1207], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 634, "type": "CLIPTextEncode", "pos": [-8076, -5366], "size": [210, 116.85224914550781], "flags": {}, "order": 127, "mode": 2, "inputs": [{"name": "clip", "type": "CLIP", "link": 1214}, {"name": "text", "type": "STRING", "link": 1215, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1212], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,"], "color": "#232", "bgcolor": "#353"}, {"id": 635, "type": "UltralyticsDetectorProvider", "pos": [-7793, -4974], "size": [226.8000030517578, 78], "flags": {}, "order": 29, "mode": 2, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1208], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 636, "type": "ImpactNegativeConditioningPlaceholder", "pos": [-8095, -5075], "size": [210, 26], "flags": {}, "order": 30, "mode": 2, "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1213], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 637, "type": "Note", "pos": [-7795, -5402], "size": [210, 91.33761596679688], "flags": {}, "order": 31, "mode": 2, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 638, "type": "Note", "pos": [-9743.**********, -5639.470703125], "size": [210, 91.33761596679688], "flags": {}, "order": 32, "mode": 2, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["去模糊lora节点\n纯色背景流程：关闭\n其他流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 639, "type": "Note", "pos": [-6562.73681640625, -5542.7041015625], "size": [210, 91.33761596679688], "flags": {}, "order": 33, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 640, "type": "Note", "pos": [-6108.02490234375, -5567.5517578125], "size": [210, 91.33761596679688], "flags": {}, "order": 34, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 641, "type": "Note", "pos": [-5369.7265625, -5589.4482421875], "size": [210, 91.33761596679688], "flags": {}, "order": 35, "mode": 2, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 642, "type": "ConrainImageCompositeMasked", "pos": [-4036, -5325], "size": [252, 146], "flags": {}, "order": 188, "mode": 2, "inputs": [{"name": "source", "type": "IMAGE", "link": 1216}, {"name": "destination", "type": "IMAGE", "link": 1217}, {"name": "mask", "type": "MASK", "link": 1218, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1174, 1176], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 643, "type": "Note", "pos": [-6572.73681640625, -5111.7041015625], "size": [210, 91.33761596679688], "flags": {}, "order": 36, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 644, "type": "ConrainTextSave", "pos": [-3195.80224609375, -4518.47900390625], "size": [315, 106], "flags": {}, "order": 117, "mode": 4, "inputs": [{"name": "text", "type": "STRING", "link": 1219, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 1220, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 1221, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 645, "type": "UltralyticsDetectorProvider", "pos": [-8048, -3857], "size": [315, 78], "flags": {}, "order": 37, "mode": 4, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1231], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 646, "type": "SAMLoader", "pos": [-8015, -3703], "size": [315, 82], "flags": {}, "order": 38, "mode": 4, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1232], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 647, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-8070, -4383], "size": [389.95330810546875, 157.71157836914062], "flags": {}, "order": 84, "mode": 4, "inputs": [{"name": "clip", "type": "CLIP", "link": 1222, "label": "clip"}, {"name": "text", "type": "STRING", "link": 1223, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1229], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 648, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-8048, -4133], "size": [400, 200], "flags": {}, "order": 83, "mode": 4, "inputs": [{"name": "clip", "type": "CLIP", "link": 1224, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1230], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 649, "type": "FaceDetailer", "pos": [-7309, -4556], "size": [350.**********, 902.3991088867188], "flags": {}, "order": 163, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1225, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1226, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 1227, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 1228, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 1229, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1230, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1231, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 1232, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null, "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1205], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, 889757844059893, "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 650, "type": "CheckpointLoaderSimple", "pos": [-8076, -4571], "size": [315, 98], "flags": {}, "order": 39, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1226], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [1222, 1224, 1227], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [1228], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 651, "type": "ConrainRandomPrompts", "pos": [-8590.4609375, -5032.130859375], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 40, "mode": 2, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1215, 1223], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,A French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", 1293, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 652, "type": "Note", "pos": [-7404, -5304], "size": [210, 91.33761596679688], "flags": {}, "order": 41, "mode": 2, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"], "color": "#322", "bgcolor": "#533"}, {"id": 653, "type": "Note", "pos": [-7585, -3916], "size": [210, 91.33761596679688], "flags": {}, "order": 42, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 654, "type": "ConrainPythonExecutor", "pos": [-9439.39453125, -4622.130859375], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 81, "mode": 2, "inputs": [{"name": "any_a", "type": "*", "link": 1233, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1235], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 655, "type": "ConrainPythonExecutor", "pos": [-9416.3994140625, -4521.12939453125], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 80, "mode": 2, "inputs": [{"name": "any_a", "type": "*", "link": 1234, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1237], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 656, "type": "ConrainAnyToStrings", "pos": [-9207.3984375, -4644.13037109375], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 103, "mode": 2, "inputs": [{"name": "any_a", "type": "*", "link": 1235}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1236], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 657, "type": "JWStringToInteger", "pos": [-8957.4453125, -4648.13037109375], "size": [210, 34], "flags": {}, "order": 116, "mode": 2, "inputs": [{"name": "text", "type": "STRING", "link": 1236, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1191, 1194], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 658, "type": "ConrainAnyToStrings", "pos": [-9136.396484375, -4514.12939453125], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 102, "mode": 2, "inputs": [{"name": "any_a", "type": "*", "link": 1237}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1238], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 659, "type": "JWStringToInteger", "pos": [-8903.4580078125, -4493.1298828125], "size": [210, 34], "flags": {}, "order": 115, "mode": 2, "inputs": [{"name": "text", "type": "STRING", "link": 1238, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1192, 1195], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 660, "type": "ImageCrop", "pos": [-3724.80224609375, -4893.478515625], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 195, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1239}, {"name": "width", "type": "INT", "link": 1240, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1241, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1163, 1247, 1256], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 661, "type": "Image Size to Number", "pos": [-4020.7265625, -5563.4482421875], "size": [229.20001220703125, 126], "flags": {}, "order": 177, "mode": 2, "inputs": [{"name": "image", "type": "IMAGE", "link": 1242, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [1243], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [1244], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 662, "type": "JWIntegerMax", "pos": [-3726.7265625, -5488.4482421875], "size": [210, 67.1211166381836], "flags": {}, "order": 182, "mode": 2, "inputs": [{"name": "a", "type": "INT", "link": 1243, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 1244, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [1177], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 663, "type": "UNETLoader", "pos": [-11275, -5651], "size": [315, 82], "flags": {}, "order": 43, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1246], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 664, "type": "ModelMergeFlux1", "pos": [-10858, -5655], "size": [315, 1566], "flags": {}, "order": 125, "mode": 4, "inputs": [{"name": "model1", "type": "MODEL", "link": 1245}, {"name": "model2", "type": "MODEL", "link": 1246}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1193], "slot_index": 0}], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 670, "type": "PreviewImage", "pos": [-3466.0126953125, -4728.1904296875], "size": [210, 246], "flags": {}, "order": 201, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1256}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 671, "type": "PreviewImage", "pos": [-1946.438720703125, 1538.9407958984375], "size": [210, 246], "flags": {}, "order": 288, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1260}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 672, "type": "easy showAnything", "pos": [-2964.4384765625, 2021.940673828125], "size": [210, 76], "flags": {}, "order": 284, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 1262, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["1"]}, {"id": 674, "type": "VAELoader", "pos": [-7801.88916015625, -2528.757080078125], "size": [315, 58], "flags": {}, "order": 44, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1269, 1277], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 675, "type": "DualCLIPLoader", "pos": [-7799.88916015625, -2711.755126953125], "size": [315, 106], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1266, 1272], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux"]}, {"id": 676, "type": "CLIPTextEncode", "pos": [-7356.88916015625, -2498.757080078125], "size": [425.27801513671875, 180.6060791015625], "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1266, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1268], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 677, "type": "InpaintModelConditioning", "pos": [-6984.88623046875, -2563.756591796875], "size": [302.4000244140625, 138], "flags": {}, "order": 174, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1267, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 1268, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 1269, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1270, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1271, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1280], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [1281], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [1311], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 678, "type": "CLIPTextEncode", "pos": [-7404.88916015625, -2694.75537109375], "size": [379.641845703125, 94.1251449584961], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1272, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1275], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Replace the face in [IMAGE2] with the face in [IMAGE1]"], "color": "#232", "bgcolor": "#353"}, {"id": 679, "type": "UNETLoader", "pos": [-7763.88916015625, -2891.75439453125], "size": [315, 82], "flags": {}, "order": 46, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1273], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "fp8_e4m3fn_fast"]}, {"id": 680, "type": "LoraLoaderModelOnly", "pos": [-7418.88916015625, -2896.75439453125], "size": [315, 82], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1273, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1274], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["ACE_Plus/comfyui_portrait_lora64.safetensors", 1]}, {"id": 681, "type": "DifferentialDiffusion", "pos": [-6991.88623046875, -2888.75439453125], "size": [210, 26], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1274, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1279], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 682, "type": "FluxGuidance", "pos": [-6983.88623046875, -2697.75537109375], "size": [317.4000244140625, 58], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1275, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1267], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [50]}, {"id": 684, "type": "GetImageSize+", "pos": [-6174.888671875, -2591.************], "size": [210, 66], "flags": {}, "order": 189, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1278, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1303], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1306], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 685, "type": "K<PERSON><PERSON><PERSON>", "pos": [-6597.888671875, -2935.75439453125], "size": [315, 474], "flags": {}, "order": 183, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1279, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 1280, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 1281, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 1282, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1276], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [201421469478745, "fixed", 20, 1, "euler", "normal", 1]}, {"id": 687, "type": "Image Blank", "pos": [-8866.0361328125, -1415.919677734375], "size": [315, 154], "flags": {}, "order": 149, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1286, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1287, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1289], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 688, "type": "MaskToImage", "pos": [-8387.974609375, -2410.2353515625], "size": [176.39999389648438, 33.2907600402832], "flags": {}, "order": 161, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1288, "label": "遮罩"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1299], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 689, "type": "ImageCompositeMasked", "pos": [-8361.037109375, -1355.919189453125], "size": [212.18775939941406, 146], "flags": {}, "order": 160, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 1289, "label": "目标图像"}, {"name": "source", "type": "IMAGE", "link": 1290, "label": "源图像"}, {"name": "mask", "type": "MASK", "link": 1291, "shape": 7, "label": "遮罩"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1302], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 690, "type": "Image Blank", "pos": [-8897.0361328125, -1690.9195556640625], "size": [315, 154], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1292, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1293, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1300], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 0, 0, 0]}, {"id": 691, "type": "PreviewImage", "pos": [-7032.88720703125, -2256.7578125], "size": [210, 246], "flags": {}, "order": 170, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1294, "label": "图像"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 692, "type": "PreviewImage", "pos": [-7328.88916015625, -2238.7578125], "size": [210, 246], "flags": {}, "order": 168, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1295, "label": "图像"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 693, "type": "ImageToMask", "pos": [-7381.88916015625, -2450.757080078125], "size": [315, 58], "flags": {}, "order": 171, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1296, "label": "图像"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1271], "slot_index": 0, "label": "遮罩"}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 694, "type": "GetImageSize+", "pos": [-6144.888671875, -2271.7578125], "size": [210, 66], "flags": {}, "order": 169, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1297, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1305], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1308], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 695, "type": "GetImageSize+", "pos": [-6176.888671875, -2445.757080078125], "size": [210, 66], "flags": {}, "order": 150, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1298, "label": "图像"}], "outputs": [{"name": "width", "type": "INT", "links": [1304], "slot_index": 0, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1307], "slot_index": 1, "label": "高度"}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 696, "type": "ImageConcanate", "pos": [-7777.88916015625, -2439.757080078125], "size": [315, 102], "flags": {}, "order": 165, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1299, "label": "图像_1"}, {"name": "image2", "type": "IMAGE", "link": 1300, "label": "图像_2"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1294, 1296], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageConcanate"}, "widgets_values": ["right", true]}, {"id": 697, "type": "ImageConcanate", "pos": [-7769.88916015625, -2251.7578125], "size": [315, 102], "flags": {}, "order": 164, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1301, "label": "图像_1"}, {"name": "image2", "type": "IMAGE", "link": 1302, "label": "图像_2"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1270, 1295, 1297], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageConcanate"}, "widgets_values": ["right", true]}, {"id": 698, "type": "SimpleMath+", "pos": [-5798.888671875, -2623.************], "size": [315, 98], "flags": {}, "order": 191, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1303, "shape": 7}, {"name": "b", "type": "*", "link": 1304, "shape": 7}, {"name": "c", "type": "*", "link": 1305, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1284], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*b/c"]}, {"id": 699, "type": "SimpleMath+", "pos": [-5824.888671875, -2429.757080078125], "size": [315, 98], "flags": {}, "order": 192, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1306, "shape": 7}, {"name": "b", "type": "*", "link": 1307, "shape": 7}, {"name": "c", "type": "*", "link": 1308, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [1285], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT", "links": null}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*b/c"]}, {"id": 700, "type": "Sapiens<PERSON><PERSON>der", "pos": [-10646.974609375, -2533.2353515625], "size": [315, 298], "flags": {}, "order": 47, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1309, 1324], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 701, "type": "SapiensSampler", "pos": [-10170.974609375, -2929.23486328125], "size": [315, 258], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1309}, {"name": "image", "type": "IMAGE", "link": 1310}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": null}, {"name": "depth_img", "type": "IMAGE", "links": null}, {"name": "normal_img", "type": "IMAGE", "links": null}, {"name": "pose_img", "type": "IMAGE", "links": null}, {"name": "mask", "type": "MASK", "links": [1313, 1344, 1346], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["3.<PERSON>", "2,23,24,25,26", false, 255, 255, 255]}, {"id": 703, "type": "Bounded Image Crop with Mask", "pos": [-9346.974609375, -2898.234619140625], "size": [279.7300720214844, 150], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1312}, {"name": "mask", "type": "MASK", "link": 1313}, {"name": "padding_left", "type": "INT", "link": 1314, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1315, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1316, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1317, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1355], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1368], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 704, "type": "GrowMask", "pos": [-8440.974609375, -2754.234619140625], "size": [210, 96.5464859008789], "flags": {}, "order": 157, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1318, "label": "遮罩"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1288], "slot_index": 0, "label": "遮罩"}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [15, true]}, {"id": 706, "type": "Reroute", "pos": [-8366.974609375, -2936.23486328125], "size": [75, 26], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1322}], "outputs": [{"name": "", "type": "IMAGE", "links": [1298, 1301]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 708, "type": "SapiensSampler", "pos": [-10181.03515625, -1488.919921875], "size": [315, 258], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1324}, {"name": "image", "type": "IMAGE", "link": 1325}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": null}, {"name": "normal_img", "type": "IMAGE", "links": null}, {"name": "pose_img", "type": "IMAGE", "links": null}, {"name": "mask", "type": "MASK", "links": [1330, 1335, 1337], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["3.<PERSON>", "2,23,24,25,26", false, 255, 255, 255]}, {"id": 709, "type": "ImageResizeKJ", "pos": [-9353.1005859375, -1629.6171875], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1326, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "link": null, "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "link": null, "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "link": null, "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1290], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [1292], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1293], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 710, "type": "Reroute", "pos": [-10711.7470703125, -1481.2568359375], "size": [75, 26], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1327}], "outputs": [{"name": "", "type": "IMAGE", "links": [1325, 1329]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 711, "type": "GrowMask", "pos": [-8748.0361328125, -1140.91748046875], "size": [210, 82], "flags": {}, "order": 156, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1328}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1291], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [10, true]}, {"id": 712, "type": "CR Seed", "pos": [-10097.974609375, -2448.2353515625], "size": [244.4950714111328, 102], "flags": {}, "order": 48, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1314, 1315, 1316, 1317, 1331, 1332, 1333, 1334, 1338, 1339, 1340, 1341, 1347, 1348, 1349, 1350], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [128, "fixed"]}, {"id": 713, "type": "Bounded Image Crop with Mask", "pos": [-9697.03515625, -1623.919921875], "size": [279.7300720214844, 150], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1329}, {"name": "mask", "type": "MASK", "link": 1330}, {"name": "padding_left", "type": "INT", "link": 1331, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1332, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1333, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1334, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1326, 1363], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 714, "type": "MaskToImage", "pos": [-9822.03515625, -1370.91943359375], "size": [176.39999389648438, 55.10683822631836], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1335}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1336], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 715, "type": "Bounded Image Crop with Mask", "pos": [-9692.03515625, -1200.91796875], "size": [279.7300720214844, 150], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1336}, {"name": "mask", "type": "MASK", "link": 1337}, {"name": "padding_left", "type": "INT", "link": 1338, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1339, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1340, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1341, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1342], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 716, "type": "ImageResizeKJ", "pos": [-9360.1005859375, -1403.616943359375], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1342, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "link": null, "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "link": null, "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "link": null, "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1343], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [1286], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [1287], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 717, "type": "ImageToMask", "pos": [-9008.0361328125, -1134.9176025390625], "size": [210, 81.39774322509766], "flags": {}, "order": 148, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1343}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1328], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 718, "type": "MaskToImage", "pos": [-9655.974609375, -2583.2353515625], "size": [176.39999389648438, 55.10683822631836], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1344}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1345], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 719, "type": "Bounded Image Crop with Mask", "pos": [-9434.974609375, -2307.2353515625], "size": [279.7300720214844, 150], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1345}, {"name": "mask", "type": "MASK", "link": 1346}, {"name": "padding_left", "type": "INT", "link": 1347, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1348, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1349, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1350, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1351], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [128, 64, 64, 64]}, {"id": 720, "type": "ImageResizeKJ", "pos": [-8913.974609375, -2431.2353515625], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1351, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "link": null, "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "link": null, "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "link": null, "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}, {"name": "width", "type": "INT", "link": 1352, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1353, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1354], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 721, "type": "ImageToMask", "pos": [-8696.974609375, -2576.2353515625], "size": [220.76467895507812, 68.50335693359375], "flags": {}, "order": 151, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1354}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1318], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 722, "type": "CR Seed", "pos": [-9312.974609375, -2607.2353515625], "size": [244.4950714111328, 102], "flags": {}, "order": 49, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1352, 1353, 1356, 1357], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [768, "fixed"]}, {"id": 723, "type": "ImageResizeKJ", "pos": [-8853.974609375, -2912.23486328125], "size": [231.54051208496094, 264.70440673828125], "flags": {}, "order": 132, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1355, "label": "图像"}, {"name": "get_image_size", "type": "IMAGE", "link": null, "shape": 7, "label": "参考图像"}, {"name": "width_input", "type": "INT", "link": null, "widget": {"name": "width_input"}, "shape": 7, "label": "宽度"}, {"name": "height_input", "type": "INT", "link": null, "widget": {"name": "height_input"}, "shape": 7, "label": "高度"}, {"name": "width", "type": "INT", "link": 1356, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1357, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1322], "slot_index": 0, "label": "图像"}, {"name": "width", "type": "INT", "links": [], "slot_index": 1, "label": "宽度"}, {"name": "height", "type": "INT", "links": [], "slot_index": 2, "label": "高度"}], "properties": {"Node name for S&R": "ImageResizeKJ"}, "widgets_values": [768, 768, "area", true, 0, 0, 0]}, {"id": 731, "type": "UltralyticsDetectorProvider", "pos": [-4517.1875, -949.4442138671875], "size": [315, 78], "flags": {}, "order": 50, "mode": 4, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1378], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 732, "type": "SAMLoader", "pos": [-4522.**********, -985.2335815429688], "size": [315, 82], "flags": {}, "order": 51, "mode": 4, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1379], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 735, "type": "FaceDetailer", "pos": [-3682.05712890625, -1659.04931640625], "size": [350.**********, 902.3991088867188], "flags": {}, "order": 202, "mode": 4, "inputs": [{"name": "image", "type": "IMAGE", "link": 1372, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1373, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 1374, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 1375, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 1376, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1377, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1378, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 1379, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null, "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1358, 1381], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, 699374016755788, "randomize", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 744, "type": "ToBasicPipe", "pos": [-3123.**********, -2946.55517578125], "size": [241.79998779296875, 106], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1387}, {"name": "clip", "type": "CLIP", "link": 1388}, {"name": "vae", "type": "VAE", "link": 1389}, {"name": "positive", "type": "CONDITIONING", "link": 1390}, {"name": "negative", "type": "CONDITIONING", "link": 1391}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [1385], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 745, "type": "CLIPTextEncode", "pos": [-3496.**********, -2757.556884765625], "size": [210, 116.85224914550781], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1392}, {"name": "text", "type": "STRING", "link": 1393, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1390], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,"], "color": "#232", "bgcolor": "#353"}, {"id": 746, "type": "UltralyticsDetectorProvider", "pos": [-3135.**********, -2697.557373046875], "size": [226.8000030517578, 78], "flags": {}, "order": 52, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1386], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 747, "type": "ImpactNegativeConditioningPlaceholder", "pos": [-3507.**********, -2540.55908203125], "size": [210, 26], "flags": {}, "order": 53, "mode": 0, "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1391], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 748, "type": "DualCLIPLoader", "pos": [-4576.**********, -2712.229248046875], "size": [315, 106], "flags": {}, "order": 54, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1395], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 749, "type": "UNETLoader", "pos": [-4571.**********, -2884.**********], "size": [315, 82], "flags": {}, "order": 55, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1394], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 752, "type": "ModelMergeFlux1", "pos": [-3758.50390625, -2782.373291015625], "size": [315, 1566], "flags": {"collapsed": true}, "order": 107, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 1396}, {"name": "model2", "type": "MODEL", "link": 1397}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1387], "slot_index": 0}], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 754, "type": "VAELoader", "pos": [-3528.**********, -2907.555419921875], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 56, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1389], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 738, "type": "PreviewImage", "pos": [-5569.814453125, -2280.36376953125], "size": [385.619384765625, 246], "flags": {}, "order": 197, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1380}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 686, "type": "ImageCrop", "pos": [-5432.814453125, -2587.36376953125], "size": [315, 130], "flags": {}, "order": 194, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1283, "label": "图像"}, {"name": "width", "type": "INT", "link": 1284, "widget": {"name": "width"}, "label": "宽度"}, {"name": "height", "type": "INT", "link": 1285, "widget": {"name": "height"}, "label": "高度"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1321, 1380, 1383], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [712, 1280, 0, 0]}, {"id": 733, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-4170.494140625, -1584.9520263671875], "size": [389.95330810546875, 157.71157836914062], "flags": {}, "order": 92, "mode": 4, "inputs": [{"name": "clip", "type": "CLIP", "link": 1369, "label": "clip"}, {"name": "text", "type": "STRING", "link": 1370, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1376], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 736, "type": "CheckpointLoaderSimple", "pos": [-4625.48876953125, -1714.9512939453125], "size": [315, 98], "flags": {}, "order": 57, "mode": 4, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1373], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [1369, 1371, 1374], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [1375], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 737, "type": "Note", "pos": [-4051.494140625, -884.9523315429688], "size": [210, 91.33761596679688], "flags": {}, "order": 58, "mode": 4, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 734, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [-4209.494140625, -1315.952392578125], "size": [400, 200], "flags": {}, "order": 89, "mode": 4, "inputs": [{"name": "clip", "type": "CLIP", "link": 1371, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1377], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 739, "type": "PreviewImage", "pos": [-2921.493896484375, -1723.951171875], "size": [516.7872314453125, 246], "flags": {}, "order": 206, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1381}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 728, "type": "Reroute", "pos": [-5862.814453125, -2721.36376953125], "size": [75, 26], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1368}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1320, 1365]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 683, "type": "VAEDecode", "pos": [-6196.888671875, -2800.75439453125], "size": [210, 46], "flags": {}, "order": 187, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1276, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 1277, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1278, 1283], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 702, "type": "VHS_DuplicateLatents", "pos": [-6806.888671875, -2325.7578125], "size": [260.3999938964844, 78], "flags": {}, "order": 178, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 1311}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1282], "slot_index": 0}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 159, "type": "Text String", "pos": [-2981.1318359375, 2313.708740234375], "size": [228.5572052001953, 190], "flags": {}, "order": 59, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [873], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [874], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存目录和文件名前缀", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 433, "type": "CR Text", "pos": [-4495.435546875, 2317.9404296875], "size": [211.76846313476562, 168.80604553222656], "flags": {}, "order": 60, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [846], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["1"], "color": "#232", "bgcolor": "#353"}, {"id": 753, "type": "PreviewImage", "pos": [-1421, -2250], "size": [318.008056640625, 246], "flags": {}, "order": 203, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1398}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 727, "type": "Reroute", "pos": [-5873.634765625, -2876.246826171875], "size": [75, 26], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1367}], "outputs": [{"name": "", "type": "IMAGE", "links": [1319, 1414], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 755, "type": "PreviewImage", "pos": [-1278, -1317], "size": [210, 246], "flags": {}, "order": 209, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1399}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 726, "type": "Bounded Image Blend", "pos": [-1730, -910], "size": [210, 152.77088928222656], "flags": {}, "order": 208, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1414}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1365}, {"name": "source", "type": "IMAGE", "link": 1413}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1399, 1404], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Blend"}, "widgets_values": [1, 16]}, {"id": 760, "type": "CR Text", "pos": [-12941, 840], "size": [210, 110.8138198852539], "flags": {}, "order": 61, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1406], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"]}, {"id": 759, "type": "easy compare", "pos": [-12557, 705], "size": [216.3546905517578, 95.56697845458984], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1405}, {"name": "b", "type": "*", "link": 1406}], "outputs": [{"name": "boolean", "type": "BOOLEAN", "links": [1409], "slot_index": 0}], "properties": {"Node name for S&R": "easy compare"}, "widgets_values": ["a == b"]}, {"id": 761, "type": "easy ifElse", "pos": [-12046, 736], "size": [210, 80.45691680908203], "flags": {}, "order": 211, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1407}, {"name": "on_false", "type": "*", "link": 1408}, {"name": "boolean", "type": "BOOLEAN", "link": 1409, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1410], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 665, "type": "Reroute", "pos": [-12581, 161], "size": [75, 26], "flags": {}, "order": 200, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1247}], "outputs": [{"name": "", "type": "IMAGE", "links": [1263], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 673, "type": "ConrainPythonExecutor", "pos": [-12182, 297], "size": [210, 166.10035705566406], "flags": {}, "order": 204, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1263, "shape": 7}, {"name": "any_b", "type": "*", "link": 1264, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1265], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 461, "type": "ImageToMask", "pos": [-8480.208984375, 1736.0838623046875], "size": [226.7721405029297, 83.94685363769531], "flags": {}, "order": 154, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 890}], "outputs": [{"name": "MASK", "type": "MASK", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 667, "type": "easy compare", "pos": [-11972, 1008], "size": [216.3546905517578, 95.56697845458984], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1248}, {"name": "b", "type": "*", "link": 1249}], "outputs": [{"name": "boolean", "type": "BOOLEAN", "links": [1250], "slot_index": 0}], "properties": {"Node name for S&R": "easy compare"}, "widgets_values": ["a == b"]}, {"id": 757, "type": "Reroute", "pos": [-12154, 596], "size": [75, 26], "flags": {}, "order": 210, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1404}], "outputs": [{"name": "", "type": "IMAGE", "links": [1408], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 666, "type": "CR Text", "pos": [-12437, 920], "size": [210, 106.75990295410156], "flags": {}, "order": 62, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1248], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "title": "是否使用风格lora生成图", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"], "color": "#232", "bgcolor": "#353"}, {"id": 668, "type": "CR Text", "pos": [-12430, 1125], "size": [210, 110.8138198852539], "flags": {}, "order": 63, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1249], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"]}, {"id": 669, "type": "easy ifElse", "pos": [-11709, 339], "size": [210, 80.45691680908203], "flags": {}, "order": 212, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1410}, {"name": "on_false", "type": "*", "link": 1265}, {"name": "boolean", "type": "BOOLEAN", "link": 1250, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1259], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 740, "type": "PreviewImage", "pos": [-2432, -1410], "size": [375.4495544433594, 315.32049560546875], "flags": {}, "order": 207, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1382}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 725, "type": "ConrainReActorFaceSwap", "pos": [-2853, -1279], "size": [367.79998779296875, 370], "flags": {}, "order": 205, "mode": 4, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 1358}, {"name": "swap_model", "type": "FACE_MODEL", "link": 1359}, {"name": "facedetection", "type": "FACE_MODEL", "link": 1360}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 1361}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 1362}, {"name": "source_image", "type": "IMAGE", "link": 1363, "shape": 7}, {"name": "face_model", "type": "FACE_MODEL", "link": null, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1382, 1413], "slot_index": 0}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "links": null}], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, 0.7000000000000001, 0.7000000000000001, "no", "no", "0", "0", 1, "no"]}, {"id": 724, "type": "LoadConrainReactorModels", "pos": [-3288, -1275], "size": [327.5999755859375, 190], "flags": {}, "order": 64, "mode": 4, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [1359], "slot_index": 0}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [1360], "slot_index": 1}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [1361], "slot_index": 2}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [1362], "slot_index": 3}], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 705, "type": "Bounded Image Blend", "pos": [-5454, -2975], "size": [261.95465087890625, 156.17076110839844], "flags": {}, "order": 196, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1319, "label": "target"}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1320, "label": "target_bounds"}, {"name": "source", "type": "IMAGE", "link": 1321, "label": "source"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "Bounded Image Blend"}, "widgets_values": [1, 16]}, {"id": 707, "type": "Reroute", "pos": [-10508, -2911], "size": [75, 26], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1402}], "outputs": [{"name": "", "type": "IMAGE", "links": [1310, 1312, 1367]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 741, "type": "ConrainRandomPrompts", "pos": [-4596, -2491], "size": [400, 200], "flags": {}, "order": 65, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1370, 1393], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["A 23-year-old Asian male, handsome, fair skinned,", 288, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 751, "type": "UNETLoader", "pos": [-4080, -2580], "size": [380.9853515625, 82], "flags": {}, "order": 66, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1397], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 750, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-4117, -2862], "size": [282.71966552734375, 126], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1394}, {"name": "clip", "type": "CLIP", "link": 1395}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1396], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1388, 1392], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/子期_5225_20241229_112310/子期_5225_20241229_112310-flux/子期_5225_20241229_112310-flux.safetensors", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 743, "type": "BasicPipeToDetailerPipe", "pos": [-2820, -2881], "size": [262, 204.4281768798828], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 1385}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1386}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "shape": 7}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [1384], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 742, "type": "FaceDetailerPipe", "pos": [-2483, -2907], "size": [346, 782], "flags": {}, "order": 198, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1383}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 1384}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null, "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1372, 1398], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 591459844315081, "randomize", 10, 2, "euler", "beta", 0.6, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3582, 1610], "size": [271.6474304199219, 86.10514068603516], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 729, "type": "LoadImage", "pos": [-11071, -1488], "size": [315, 314], "flags": {}, "order": 67, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1327], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (659).png", "image"]}, {"id": 366, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8430.5751953125, 810.0828247070312], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 221, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 709, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 710, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 794, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 839, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1415], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [907], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 315, "type": "GrowMask", "pos": [-7449.376953125, 589.0875244140625], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 225, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 907}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [40, false]}, {"id": 762, "type": "PreviewImage", "pos": [-7863, 273], "size": [210, 246], "flags": {}, "order": 224, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1415}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 49, "type": "PreviewImage", "pos": [-2909, 320], "size": [210, 246], "flags": {}, "order": 274, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 588}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 246, "type": "CR Seed", "pos": [-7475.376953125, 751.088134765625], "size": [281.7162780761719, 102], "flags": {}, "order": 68, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 247, "type": "CR Seed", "pos": [-7465.9208984375, 995.6329345703125], "size": [278.3121032714844, 102], "flags": {}, "order": 69, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 317, "type": "GrowMask", "pos": [-7502, 1726], "size": [210, 82], "flags": {}, "order": 249, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 664}], "outputs": [{"name": "MASK", "type": "MASK", "links": [575, 576, 764], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [20, false], "color": "#232", "bgcolor": "#353"}, {"id": 758, "type": "CR Text", "pos": [-12950, 662], "size": [210, 106.75990295410156], "flags": {}, "order": 70, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1405], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "title": "输入图是否要换头", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["1"], "color": "#232", "bgcolor": "#353"}, {"id": 463, "type": "LoadImage", "pos": [-13473, 262], "size": [315, 314], "flags": {}, "order": 71, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1264, 1402, 1407], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${clotheImage}", "image"]}, {"id": 456, "type": "LoadImage", "pos": [-13457, 962], "size": [315, 314], "flags": {}, "order": 72, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [880], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${maskImage}", "image"]}, {"id": 462, "type": "LoadImage", "pos": [-13421, 1636], "size": [315, 314], "flags": {}, "order": 73, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1257], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${referenceImage}", "image"]}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [331, 176, 0, 175, 1, "IMAGE_BOUNDS"], [335, 194, 0, 195, 0, "IMAGE"], [336, 176, 0, 195, 1, "IMAGE_BOUNDS"], [357, 195, 0, 211, 0, "IMAGE"], [359, 175, 0, 208, 0, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [511, 289, 0, 288, 0, "STITCH"], [512, 276, 0, 288, 1, "IMAGE"], [513, 277, 0, 290, 0, "CONDITIONING"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [538, 300, 0, 301, 0, "IMAGE"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [575, 317, 0, 242, 1, "MASK"], [576, 317, 0, 300, 0, "MASK"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [588, 321, 0, 49, 0, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [609, 328, 0, 175, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [646, 330, 0, 346, 0, "IMAGE"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [664, 351, 0, 317, 0, "MASK"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [676, 265, 0, 363, 0, "*"], [709, 365, 0, 366, 0, "SAM_MODEL"], [710, 367, 0, 366, 1, "GROUNDING_DINO_MODEL"], [712, 365, 0, 369, 0, "SAM_MODEL"], [713, 367, 0, 369, 1, "GROUNDING_DINO_MODEL"], [723, 289, 1, 376, 0, "IMAGE"], [746, 242, 1, 301, 1, "IMAGE_BOUNDS"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [764, 317, 0, 392, 1, "MASK"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [791, 321, 0, 282, 3, "IMAGE"], [792, 56, 0, 282, 4, "MASK"], [793, 276, 0, 291, 0, "IMAGE"], [794, 328, 0, 366, 2, "IMAGE"], [796, 330, 0, 369, 2, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [818, 408, 0, 330, 0, "*"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [833, 417, 0, 418, 1, "*"], [834, 368, 0, 418, 0, "*"], [837, 418, 0, 419, 0, "STRING"], [838, 419, 0, 369, 3, "STRING"], [839, 419, 0, 366, 3, "STRING"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [875, 408, 0, 449, 0, "IMAGE"], [876, 449, 0, 450, 1, "FLOAT"], [877, 408, 0, 450, 0, "IMAGE"], [878, 450, 0, 417, 0, "IMAGE"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [907, 366, 1, 315, 0, "MASK"], [1153, 594, 0, 592, 0, "STRING"], [1154, 626, 0, 592, 1, "STRING"], [1155, 593, 0, 592, 2, "STRING"], [1156, 627, 0, 592, 3, "STRING"], [1157, 631, 0, 596, 0, "IMAGE"], [1158, 595, 0, 596, 1, "FACE_MODEL"], [1159, 595, 1, 596, 2, "FACE_MODEL"], [1160, 595, 2, 596, 3, "FACE_MODEL"], [1161, 595, 3, 596, 4, "FACE_MODEL"], [1162, 590, 0, 596, 5, "IMAGE"], [1163, 660, 0, 597, 0, "IMAGE"], [1164, 591, 0, 597, 1, "STRING"], [1165, 591, 1, 597, 2, "STRING"], [1166, 599, 0, 598, 0, "STRING"], [1167, 591, 0, 598, 1, "STRING"], [1168, 601, 0, 600, 0, "IMAGE"], [1169, 596, 0, 601, 0, "IMAGE"], [1170, 605, 4, 603, 0, "INT"], [1171, 605, 5, 603, 1, "INT"], [1172, 600, 0, 604, 0, "IMAGE"], [1173, 601, 0, 605, 0, "IMAGE"], [1174, 642, 0, 606, 0, "IMAGE"], [1175, 607, 0, 606, 1, "FLOAT"], [1176, 642, 0, 607, 0, "IMAGE"], [1177, 662, 0, 607, 1, "INT"], [1178, 628, 1, 608, 0, "CLIP"], [1179, 622, 0, 608, 1, "STRING"], [1180, 613, 0, 609, 0, "LATENT"], [1181, 610, 0, 609, 1, "VAE"], [1182, 617, 0, 613, 0, "NOISE"], [1183, 616, 0, 613, 1, "GUIDER"], [1184, 614, 0, 613, 2, "SAMPLER"], [1185, 615, 0, 613, 3, "SIGMAS"], [1186, 619, 0, 613, 4, "LATENT"], [1187, 620, 0, 615, 0, "MODEL"], [1188, 620, 0, 616, 0, "MODEL"], [1189, 618, 0, 616, 1, "CONDITIONING"], [1190, 608, 0, 618, 0, "CONDITIONING"], [1191, 657, 0, 619, 0, "INT"], [1192, 659, 0, 619, 1, "INT"], [1193, 664, 0, 620, 0, "MODEL"], [1194, 657, 0, 620, 1, "INT"], [1195, 659, 0, 620, 2, "INT"], [1196, 621, 0, 622, 0, "STRING"], [1197, 621, 0, 626, 0, "*"], [1198, 625, 0, 627, 0, "*"], [1199, 630, 0, 628, 0, "MODEL"], [1200, 630, 1, 628, 1, "CLIP"], [1201, 612, 0, 629, 0, "MODEL"], [1202, 611, 0, 629, 1, "CLIP"], [1203, 629, 0, 630, 0, "MODEL"], [1204, 629, 1, 630, 1, "CLIP"], [1205, 649, 0, 631, 0, "IMAGE"], [1206, 632, 0, 631, 1, "DETAILER_PIPE"], [1207, 633, 0, 632, 0, "BASIC_PIPE"], [1208, 635, 0, 632, 1, "BBOX_DETECTOR"], [1209, 628, 0, 633, 0, "MODEL"], [1210, 628, 1, 633, 1, "CLIP"], [1211, 610, 0, 633, 2, "VAE"], [1212, 634, 0, 633, 3, "CONDITIONING"], [1213, 636, 0, 633, 4, "CONDITIONING"], [1214, 628, 1, 634, 0, "CLIP"], [1215, 651, 0, 634, 1, "STRING"], [1216, 604, 0, 642, 0, "IMAGE"], [1217, 603, 0, 642, 1, "IMAGE"], [1218, 600, 1, 642, 2, "MASK"], [1219, 592, 0, 644, 0, "STRING"], [1220, 598, 0, 644, 1, "STRING"], [1221, 591, 1, 644, 2, "STRING"], [1222, 650, 1, 647, 0, "CLIP"], [1223, 651, 0, 647, 1, "STRING"], [1224, 650, 1, 648, 0, "CLIP"], [1225, 609, 0, 649, 0, "IMAGE"], [1226, 650, 0, 649, 1, "MODEL"], [1227, 650, 1, 649, 2, "CLIP"], [1228, 650, 2, 649, 3, "VAE"], [1229, 647, 0, 649, 4, "CONDITIONING"], [1230, 648, 0, 649, 5, "CONDITIONING"], [1231, 645, 0, 649, 6, "BBOX_DETECTOR"], [1232, 646, 0, 649, 7, "SAM_MODEL"], [1233, 624, 0, 654, 0, "*"], [1234, 623, 0, 655, 0, "*"], [1235, 654, 0, 656, 0, "*"], [1236, 656, 0, 657, 0, "STRING"], [1237, 655, 0, 658, 0, "*"], [1238, 658, 0, 659, 0, "STRING"], [1239, 606, 0, 660, 0, "IMAGE"], [1240, 624, 0, 660, 1, "INT"], [1241, 623, 0, 660, 2, "INT"], [1242, 596, 0, 661, 0, "IMAGE"], [1243, 661, 4, 662, 0, "INT"], [1244, 661, 5, 662, 1, "INT"], [1245, 628, 0, 664, 0, "MODEL"], [1246, 663, 0, 664, 1, "MODEL"], [1247, 660, 0, 665, 0, "*"], [1248, 666, 0, 667, 0, "*"], [1249, 668, 0, 667, 1, "*"], [1250, 667, 0, 669, 2, "BOOLEAN"], [1256, 660, 0, 670, 0, "IMAGE"], [1257, 462, 0, 407, 0, "*"], [1259, 669, 0, 402, 0, "*"], [1260, 276, 0, 671, 0, "IMAGE"], [1261, 369, 1, 331, 0, "*"], [1262, 436, 1, 672, 0, "*"], [1263, 665, 0, 673, 0, "*"], [1264, 463, 0, 673, 1, "*"], [1265, 673, 0, 669, 1, "*"], [1266, 675, 0, 676, 0, "CLIP"], [1267, 682, 0, 677, 0, "CONDITIONING"], [1268, 676, 0, 677, 1, "CONDITIONING"], [1269, 674, 0, 677, 2, "VAE"], [1270, 697, 0, 677, 3, "IMAGE"], [1271, 693, 0, 677, 4, "MASK"], [1272, 675, 0, 678, 0, "CLIP"], [1273, 679, 0, 680, 0, "MODEL"], [1274, 680, 0, 681, 0, "MODEL"], [1275, 678, 0, 682, 0, "CONDITIONING"], [1276, 685, 0, 683, 0, "LATENT"], [1277, 674, 0, 683, 1, "VAE"], [1278, 683, 0, 684, 0, "IMAGE"], [1279, 681, 0, 685, 0, "MODEL"], [1280, 677, 0, 685, 1, "CONDITIONING"], [1281, 677, 1, 685, 2, "CONDITIONING"], [1282, 702, 0, 685, 3, "LATENT"], [1283, 683, 0, 686, 0, "IMAGE"], [1284, 698, 0, 686, 1, "INT"], [1285, 699, 0, 686, 2, "INT"], [1286, 716, 1, 687, 0, "INT"], [1287, 716, 2, 687, 1, "INT"], [1288, 704, 0, 688, 0, "MASK"], [1289, 687, 0, 689, 0, "IMAGE"], [1290, 709, 0, 689, 1, "IMAGE"], [1291, 711, 0, 689, 2, "MASK"], [1292, 709, 1, 690, 0, "INT"], [1293, 709, 2, 690, 1, "INT"], [1294, 696, 0, 691, 0, "IMAGE"], [1295, 697, 0, 692, 0, "IMAGE"], [1296, 696, 0, 693, 0, "IMAGE"], [1297, 697, 0, 694, 0, "IMAGE"], [1298, 706, 0, 695, 0, "IMAGE"], [1299, 688, 0, 696, 0, "IMAGE"], [1300, 690, 0, 696, 1, "IMAGE"], [1301, 706, 0, 697, 0, "IMAGE"], [1302, 689, 0, 697, 1, "IMAGE"], [1303, 684, 0, 698, 0, "*"], [1304, 695, 0, 698, 1, "*"], [1305, 694, 0, 698, 2, "*"], [1306, 684, 1, 699, 0, "*"], [1307, 695, 1, 699, 1, "*"], [1308, 694, 1, 699, 2, "*"], [1309, 700, 0, 701, 0, "MODEL_SAPIEN"], [1310, 707, 0, 701, 1, "IMAGE"], [1311, 677, 2, 702, 0, "LATENT"], [1312, 707, 0, 703, 0, "IMAGE"], [1313, 701, 4, 703, 1, "MASK"], [1314, 712, 0, 703, 2, "INT"], [1315, 712, 0, 703, 3, "INT"], [1316, 712, 0, 703, 4, "INT"], [1317, 712, 0, 703, 5, "INT"], [1318, 721, 0, 704, 0, "MASK"], [1319, 727, 0, 705, 0, "IMAGE"], [1320, 728, 0, 705, 1, "IMAGE_BOUNDS"], [1321, 686, 0, 705, 2, "IMAGE"], [1322, 723, 0, 706, 0, "*"], [1324, 700, 0, 708, 0, "MODEL_SAPIEN"], [1325, 710, 0, 708, 1, "IMAGE"], [1326, 713, 0, 709, 0, "IMAGE"], [1327, 729, 0, 710, 0, "*"], [1328, 717, 0, 711, 0, "MASK"], [1329, 710, 0, 713, 0, "IMAGE"], [1330, 708, 4, 713, 1, "MASK"], [1331, 712, 0, 713, 2, "INT"], [1332, 712, 0, 713, 3, "INT"], [1333, 712, 0, 713, 4, "INT"], [1334, 712, 0, 713, 5, "INT"], [1335, 708, 4, 714, 0, "MASK"], [1336, 714, 0, 715, 0, "IMAGE"], [1337, 708, 4, 715, 1, "MASK"], [1338, 712, 0, 715, 2, "INT"], [1339, 712, 0, 715, 3, "INT"], [1340, 712, 0, 715, 4, "INT"], [1341, 712, 0, 715, 5, "INT"], [1342, 715, 0, 716, 0, "IMAGE"], [1343, 716, 0, 717, 0, "IMAGE"], [1344, 701, 4, 718, 0, "MASK"], [1345, 718, 0, 719, 0, "IMAGE"], [1346, 701, 4, 719, 1, "MASK"], [1347, 712, 0, 719, 2, "INT"], [1348, 712, 0, 719, 3, "INT"], [1349, 712, 0, 719, 4, "INT"], [1350, 712, 0, 719, 5, "INT"], [1351, 719, 0, 720, 0, "IMAGE"], [1352, 722, 0, 720, 4, "INT"], [1353, 722, 0, 720, 5, "INT"], [1354, 720, 0, 721, 0, "IMAGE"], [1355, 703, 0, 723, 0, "IMAGE"], [1356, 722, 0, 723, 4, "INT"], [1357, 722, 0, 723, 5, "INT"], [1358, 735, 0, 725, 0, "IMAGE"], [1359, 724, 0, 725, 1, "FACE_MODEL"], [1360, 724, 1, 725, 2, "FACE_MODEL"], [1361, 724, 2, 725, 3, "FACE_MODEL"], [1362, 724, 3, 725, 4, "FACE_MODEL"], [1363, 713, 0, 725, 5, "IMAGE"], [1365, 728, 0, 726, 1, "IMAGE_BOUNDS"], [1367, 707, 0, 727, 0, "*"], [1368, 703, 1, 728, 0, "*"], [1369, 736, 1, 733, 0, "CLIP"], [1370, 741, 0, 733, 1, "STRING"], [1371, 736, 1, 734, 0, "CLIP"], [1372, 742, 0, 735, 0, "IMAGE"], [1373, 736, 0, 735, 1, "MODEL"], [1374, 736, 1, 735, 2, "CLIP"], [1375, 736, 2, 735, 3, "VAE"], [1376, 733, 0, 735, 4, "CONDITIONING"], [1377, 734, 0, 735, 5, "CONDITIONING"], [1378, 731, 0, 735, 6, "BBOX_DETECTOR"], [1379, 732, 0, 735, 7, "SAM_MODEL"], [1380, 686, 0, 738, 0, "IMAGE"], [1381, 735, 0, 739, 0, "IMAGE"], [1382, 725, 0, 740, 0, "IMAGE"], [1383, 686, 0, 742, 0, "IMAGE"], [1384, 743, 0, 742, 1, "DETAILER_PIPE"], [1385, 744, 0, 743, 0, "BASIC_PIPE"], [1386, 746, 0, 743, 1, "BBOX_DETECTOR"], [1387, 752, 0, 744, 0, "MODEL"], [1388, 750, 1, 744, 1, "CLIP"], [1389, 754, 0, 744, 2, "VAE"], [1390, 745, 0, 744, 3, "CONDITIONING"], [1391, 747, 0, 744, 4, "CONDITIONING"], [1392, 750, 1, 745, 0, "CLIP"], [1393, 741, 0, 745, 1, "STRING"], [1394, 749, 0, 750, 0, "MODEL"], [1395, 748, 0, 750, 1, "CLIP"], [1396, 750, 0, 752, 0, "MODEL"], [1397, 751, 0, 752, 1, "MODEL"], [1398, 742, 0, 753, 0, "IMAGE"], [1399, 726, 0, 755, 0, "IMAGE"], [1402, 463, 0, 707, 0, "*"], [1404, 726, 0, 757, 0, "*"], [1405, 758, 0, 759, 0, "*"], [1406, 760, 0, 759, 1, "*"], [1407, 463, 0, 761, 0, "*"], [1408, 757, 0, 761, 1, "*"], [1409, 759, 0, 761, 2, "BOOLEAN"], [1410, 761, 0, 669, 0, "*"], [1413, 725, 0, 726, 2, "IMAGE"], [1414, 727, 0, 726, 0, "IMAGE"], [1415, 366, 0, 762, 0, "IMAGE"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4589.86474609375, 176.38858032226562, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "涂抹logo原图", "bounding": [-7946.21142578125, 1443.7940673828125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "涂抹需要修复的logo区域", "bounding": [-7920.58935546875, 171.33734130859375, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4552.4736328125, 1515.76904296875, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "图片尺寸最大2048", "bounding": [-11204.9794921875, 158.43821716308594, 3120.570556640625, 2013.73291015625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 14, "title": "换脸", "bounding": [-6677.0224609375, -5708.82958984375, 1069, 1391], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 15, "title": "1.5修脸", "bounding": [-8117, -4690, 1347, 1200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "换背景", "bounding": [-5482.171875, -5701.7041015625, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "修脸换脸", "bounding": [-8125, -5709, 1350, 956], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 18, "title": "出图", "bounding": [-10477.361328125, -5698.1318359375, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 19, "title": "保存图片", "bounding": [-5484.64990234375, -4998.72705078125, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 20, "title": "Group", "bounding": [-11083.49609375, -3086.067138671875, 2901.240966796875, 1043.7391357421875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 21, "title": "Group", "bounding": [-11107.4833984375, -1844.8060302734375, 3006.705078125, 836.988525390625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 23, "title": "Group", "bounding": [-7878.7021484375, -3056.65478515625, 2831.052978515625, 1073.604736328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 22, "title": "Group", "bounding": [-7680.63623046875, -2523.78173828125, 140, 80], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 24, "title": "Group", "bounding": [-4650.77099609375, -1896.4578857421875, 2645.1259765625, 1180.4041748046875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 25, "title": "Group", "bounding": [-4691.125, -3054.486328125, 2616.3447265625, 992.4853515625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 26, "title": "Group", "bounding": [-13021.1826171875, 130.66616821289062, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.45949729863572325, "offset": [13971.811667584569, 227.935502270151]}}, "version": 0.4, "widget_idx_map": {"246": {"seed": 0}, "247": {"seed": 0}, "275": {"seed": 0, "sampler_name": 4, "scheduler": 5}, "401": {"seed": 0}, "614": {"sampler_name": 0}, "615": {"scheduler": 0}, "617": {"noise_seed": 0}, "621": {"seed": 1}, "623": {"seed": 0}, "624": {"seed": 0}, "625": {"seed": 1}, "631": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "649": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "651": {"seed": 1}, "685": {"seed": 0, "sampler_name": 4, "scheduler": 5}, "712": {"seed": 0}, "722": {"seed": 0}, "735": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "741": {"seed": 1}, "742": {"seed": 3, "sampler_name": 7, "scheduler": 8}}, "seed_widgets": {"246": 0, "247": 0, "275": 0, "401": 0, "617": 0, "621": 1, "623": 0, "624": 0, "625": 1, "631": 3, "649": 3, "651": 1, "685": 0, "712": 0, "722": 0, "735": 3, "741": 1, "742": 3}}}}}
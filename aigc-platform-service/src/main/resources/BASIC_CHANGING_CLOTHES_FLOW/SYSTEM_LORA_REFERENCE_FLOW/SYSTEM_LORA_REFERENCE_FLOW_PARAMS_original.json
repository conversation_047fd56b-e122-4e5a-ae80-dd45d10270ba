{"client_id": "${clientId}", "prompt": {"159": {"_meta": {"title": "保存换装结果图片"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "168": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["397", 0], "mask": ["315", 0], "padding_bottom": 0, "padding_left": 0, "padding_right": 0, "padding_top": 0}}, "176": {"_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"}}, "194": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["315", 0]}}, "211": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["767", 0]}}, "242": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["346", 0], "mask": ["351", 0], "padding_bottom": ["779", 0], "padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0]}}, "243": {"_meta": {"title": "ImageCompositeMasked"}, "class_type": "ImageCompositeMasked", "inputs": {"destination": ["245", 0], "resize_source": false, "source": ["254", 0], "x": ["250", 0], "y": ["249", 0]}}, "245": {"_meta": {"title": "设置底图颜色"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 16777215, "height": ["265", 0], "width": ["264", 0]}}, "246": {"_meta": {"title": "默认宽"}, "class_type": "CR Seed", "inputs": {"seed": ["774", 0]}}, "247": {"_meta": {"title": "默认高"}, "class_type": "CR Seed", "inputs": {"seed": ["775", 0]}}, "249": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["320", 1], "b": ["265", 0], "value": "b/2-a/2"}}, "250": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["320", 0], "b": ["264", 0], "value": "b/2-a/2"}}, "253": {"_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"}}, "254": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["242", 0], "scale_by": ["253", 0], "upscale_method": "area"}}, "256": {"_meta": {"title": "concat的图片大小"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["264", 0], "any_b": ["265", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"}}, "258": {"_meta": {"title": "Minimum宽"}, "class_type": "JWIntegerMin", "inputs": {"a": ["314", 0], "b": ["246", 0]}}, "259": {"_meta": {"title": "Minimum高"}, "class_type": "JWIntegerMin", "inputs": {"a": ["314", 1], "b": ["247", 0]}}, "264": {"_meta": {"title": "Maximum宽"}, "class_type": "JWIntegerMax", "inputs": {"a": ["316", 0], "b": ["258", 0]}}, "265": {"_meta": {"title": "Maximum高"}, "class_type": "JWIntegerMax", "inputs": {"a": ["316", 1], "b": ["259", 0]}}, "275": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 2.5, "denoise": 1, "latent_image": ["436", 0], "model": ["283", 0], "negative": ["282", 1], "positive": ["282", 0], "sampler_name": "euler", "scheduler": "simple", "seed": 661045452417164, "steps": 20}}, "276": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["275", 0], "vae": ["280", 0]}}, "277": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["281", 0], "text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["277", 0], "guidance": 30}}, "279": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}}, "280": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "281": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "device": "default", "type": "flux"}}, "282": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["289", 2], "negative": ["290", 0], "noise_mask": true, "pixels": ["289", 1], "positive": ["286", 0], "vae": ["280", 0]}}, "283": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["293", 0]}}, "284": {"_meta": {"title": "Load CLIP Vision"}, "class_type": "CLIPVisionLoader", "inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}}, "285": {"_meta": {"title": "CLIP Vision Encode"}, "class_type": "CLIPVisionEncode", "inputs": {"clip_vision": ["284", 0], "crop": "center", "image": ["243", 0]}}, "286": {"_meta": {"title": "Apply Style Model"}, "class_type": "StyleModelApply", "inputs": {"clip_vision_output": ["285", 0], "conditioning": ["278", 0], "strength": 1, "strength_type": "multiply", "style_model": ["287", 0]}}, "287": {"_meta": {"title": "Load Style Model"}, "class_type": "StyleModelLoader", "inputs": {"style_model_name": "flux1-redux-dev.safetensors"}}, "288": {"_meta": {"title": "✂️ Inpaint <PERSON>itch"}, "class_type": "InpaintStitch", "inputs": {"inpainted_image": ["794", 0], "rescale_algorithm": "bislerp", "stitch": ["793", 0]}}, "289": {"_meta": {"title": "✂️ Inpaint Crop"}, "class_type": "InpaintCrop", "inputs": {"blend_pixels": 16, "blur_mask_pixels": 0, "context_expand_factor": 1, "context_expand_pixels": 10, "fill_mask_holes": true, "force_height": 1024, "force_width": 1024, "image": ["321", 0], "invert_mask": false, "mask": ["56", 0], "max_height": 1785, "max_width": 1536, "min_height": 512, "min_width": 512, "mode": "ranged size", "optional_context_mask": ["297", 0], "padding": 32, "rescale_algorithm": "bicubic", "rescale_factor": 1}}, "290": {"_meta": {"title": "ConditioningZeroOut"}, "class_type": "ConditioningZeroOut", "inputs": {"conditioning": ["847", 0]}}, "291": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["61", 1], "image": ["288", 0], "width": ["61", 0], "x": ["60", 0], "y": 0}}, "293": {"_meta": {"title": "LoraLoaderModelOnly"}, "class_type": "LoraLoaderModelOnly", "inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "model": ["279", 0], "strength_model": 1}}, "297": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["323", 0]}}, "300": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["351", 0]}}, "306": {"_meta": {"title": "Bounded Image Blend LR"}, "class_type": "Bounded_Image_Blend_LR", "inputs": {"blend_factor": 1, "feathering": 5, "source": ["291", 0], "target": ["397", 0], "target_bounds": ["176", 0]}}, "308": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["159", 1], "images": ["306", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["159", 0], "quality": 100, "use_time_str": "true"}}, "314": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["397", 0]}}, "315": {"_meta": {"title": "替换区域扩张大小"}, "class_type": "GrowMask", "inputs": {"expand": 10, "mask": ["812", 0], "tapered_corners": false}}, "316": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["168", 0]}}, "319": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["242", 0]}}, "320": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["254", 0]}}, "321": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["243", 0], "image2": ["764", 0], "match_image_size": false}}, "322": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["211", 0], "tapered_corners": true}}, "323": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["385", 0], "image2": ["53", 0], "match_image_size": false}}, "324": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["53", 0], "image2": ["54", 0], "match_image_size": false}}, "346": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["408", 0], "mask": ["461", 0], "padding_bottom": ["348", 0], "padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0]}}, "347": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["264", 0], "b": ["779", 0], "value": "a/2+b"}}, "348": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["265", 0], "b": ["779", 0], "value": "a/2+b"}}, "349": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["461", 0]}}, "350": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["349", 0], "mask": ["461", 0], "padding_bottom": ["348", 0], "padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0]}}, "351": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["350", 0]}}, "362": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["264", 0], "value": "a*2*2"}}, "365": {"_meta": {"title": "Conrain SAMModelLoader"}, "class_type": "Conrain_SAMModelLoader", "inputs": {"model_name": "sam_vit_h_cloth"}}, "366": {"_meta": {"title": "Conrain GroundingDinoSAMSegment"}, "class_type": "Conrain_GroundingDinoSAMSegment", "inputs": {"background": "white", "grounding_dino_model": ["367", 0], "image": ["844", 0], "prompt": ["419", 0], "sam_model": ["365", 0], "threshold": 0.3}}, "367": {"_meta": {"title": "Conrain GroundingDinoModelLoader"}, "class_type": "Conrain_GroundingDinoModelLoader", "inputs": {"model_name": "groundingdino_cloth"}}, "368": {"_meta": {"title": "抠图词"}, "class_type": "CR Text", "inputs": {"text": "${clotheType}"}}, "369": {"_meta": {"title": "Conrain GroundingDinoSAMSegment"}, "class_type": "Conrain_GroundingDinoSAMSegment", "inputs": {"background": "white", "grounding_dino_model": ["367", 0], "image": ["844", 0], "prompt": ["820", 0], "sam_model": ["365", 0], "threshold": 0.3}}, "384": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["392", 0], "scale_by": ["253", 0], "upscale_method": "area"}}, "385": {"_meta": {"title": "ImageCompositeMasked"}, "class_type": "ImageCompositeMasked", "inputs": {"destination": ["386", 0], "resize_source": false, "source": ["384", 0], "x": ["250", 0], "y": ["249", 0]}}, "386": {"_meta": {"title": "设置底图颜色"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 0, "height": ["265", 0], "width": ["264", 0]}}, "392": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["300", 0], "mask": ["351", 0], "padding_bottom": ["779", 0], "padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0]}}, "397": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["669", 0], "scale_by": ["398", 0], "upscale_method": "area"}}, "398": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["669", 0], "target_size": ["399", 0]}}, "399": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"}}, "400": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["669", 0]}}, "401": {"_meta": {"title": "最大支持尺寸"}, "class_type": "CR Seed", "inputs": {"seed": 2100}}, "404": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["462", 0]}}, "405": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"}}, "406": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["462", 0], "target_size": ["405", 0]}}, "408": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["462", 0], "scale_by": ["406", 0], "upscale_method": "area"}}, "418": {"_meta": {"title": "需要替换的区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["368", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"}}, "419": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["418", 0]}}, "433": {"_meta": {"title": "tryon生成图片张数"}, "class_type": "CR Text", "inputs": {"text": "${imageNum}"}}, "434": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["433", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"}}, "435": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["434", 0]}}, "436": {"_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}, "class_type": "VHS_DuplicateLatents", "inputs": {"latents": ["282", 2], "multiply_by": ["435", 0]}}, "456": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${maskImage}", "upload": "image"}}, "459": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "always", "height": ["404", 1], "image": ["456", 0], "interpolation": "nearest", "method": "keep proportion", "multiple_of": 0, "width": ["404", 0]}}, "460": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["459", 0], "scale_by": ["406", 0], "upscale_method": "area"}}, "461": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["460", 0]}}, "462": {"_meta": {"title": "模特图"}, "class_type": "LoadImage", "inputs": {"image": "${clotheImage}", "upload": "image"}}, "463": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${referenceImage}", "upload": "image"}}, "52": {"_meta": {"title": "A Person Mask Generator"}, "class_type": "APersonMaskGenerator", "inputs": {"background_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "face_mask": false, "hair_mask": false, "images": ["243", 0]}}, "53": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["52", 0]}}, "54": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["322", 0]}}, "56": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["324", 0]}}, "590": {"_meta": {"title": "导入人脸图片"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo.faceImage}", "upload": "image"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "591": {"_meta": {"title": "保存生成图片"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "face_${fileNamePrefix}", "text_c": "", "text_d": ""}}, "592": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["594", 0], "text_b": ["621", 0], "text_c": ["593", 0], "text_d": ["625", 0]}}, "593": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "594": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "595": {"_meta": {"title": "导入换脸模型"}, "class_type": "LoadConrainReactorModels", "inputs": {"face_restore_model": "GFPGANv1.4.pth", "facedetection_model": "retinaface_resnet50", "parse_model": "parsenet", "swap_model": "inswapper_128.onnx"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "596": {"_meta": {"title": "换脸"}, "class_type": "ConrainReActorFaceSwap", "inputs": {"codeformer_weight": "0.7", "console_log_level": 1, "detect_gender_input": "no", "detect_gender_source": "no", "enabled": true, "face_restore_model": ["595", 2], "face_restore_visibility": "0.7", "facedetection": ["595", 1], "faceparse_model": ["595", 3], "input_faces_index": "0", "input_image": ["${isUseLoraFace?then(631,649)}", 0], "keep_largest": "yes", "source_faces_index": "0", "source_image": ["590", 0], "swap_model": ["595", 0]}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "597": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["591", 1], "images": ["660", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["591", 0], "quality": 100, "use_time_str": "true"}}, "598": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["599", 0], "text_b": ["591", 0]}}, "599": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "60": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["243", 0]}}, "608": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["630", 1], "text": ["622", 0]}}, "609": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["613", 0], "vae": ["610", 0]}}, "61": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["764", 0]}}, "610": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "611": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "612": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}}, "613": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["616", 0], "latent_image": ["619", 0], "noise": ["617", 0], "sampler": ["614", 0], "sigmas": ["615", 0]}}, "614": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "euler"}}, "615": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["620", 0], "scheduler": "beta", "steps": "20"}}, "616": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["618", 0], "model": ["620", 0]}}, "617": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": 766055086762299}}, "618": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["870", 0], "guidance": "3.5"}}, "619": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": 1, "height": ["659", 0], "width": ["657", 0]}}, "620": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["659", 0], "max_shift": 1.15, "model": ["630", 0], "width": ["657", 0]}}, "621": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${pictureMattingPrompt}${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 2048}}, "622": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["621", 0]}}, "623": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": 2100}}, "624": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": 1610}}, "625": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}}, "629": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["611", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["612", 0], "strength_clip": "1", "strength_model": 0.8}, "disable": "${isUseLoraFace?then('false','true')}"}, "630": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isUseLoraFace?then(629,611)}", "${isUseLoraFace?then(1,0)?number}"], "lora_name": "${SCENE.extInfo.loraPath}", "model": ["${isUseLoraFace?then(629,612)}", 0], "strength_clip": 1, "strength_model": 1}}, "631": {"_meta": {"title": "FaceDetailer (pipe)"}, "class_type": "FaceDetailerPipe", "inputs": {"bbox_crop_factor": 3, "bbox_dilation": 2, "bbox_threshold": 0.5, "cfg": 2.5, "cycle": 1, "denoise": 0.55, "detailer_pipe": ["632", 0], "drop_size": 40, "feather": 3, "force_inpaint": false, "guide_size": 512, "guide_size_for": true, "image": ["${isUseLoraFace?then(609,649)}", 0], "inpaint_model": 0, "max_size": 1024, "noise_mask": true, "noise_mask_feather": false, "refiner_ratio": 0.2, "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "beta", "seed": 516268210931281, "steps": 10}, "disable": "${isUseLoraFace?then('false','true')}"}, "632": {"_meta": {"title": "BasicPipe -> DetailerPipe"}, "class_type": "BasicPipeToDetailerPipe", "inputs": {"Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["633", 0], "bbox_detector": ["635", 0], "wildcard": ""}, "disable": "${isUseLoraFace?then('false','true')}"}, "633": {"_meta": {"title": "ToBasicPipe"}, "class_type": "ToBasicPipe", "inputs": {"clip": ["630", 1], "model": ["629", 0], "negative": ["636", 0], "positive": ["634", 0], "vae": ["610", 0]}, "disable": "${isUseLoraFace?then('false','true')}"}, "634": {"_meta": {"title": "修脸prompt"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["630", 1], "text": ["651", 0]}, "disable": "${isUseLoraFace?then('false','true')}"}, "635": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${isUseLoraFace?then('false','true')}"}, "636": {"_meta": {"title": "Negative Cond Placeholder"}, "class_type": "ImpactNegativeConditioningPlaceholder", "inputs": {}, "disable": "${isUseLoraFace?then('false','true')}"}, "644": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["591", 1], "path": ["598", 0], "text": ["592", 0]}}, "645": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "646": {"_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}, "class_type": "SAMLoader", "inputs": {"device_mode": "Prefer GPU", "model_name": "sam_vit_b_01ec64.pth"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "647": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["650", 1], "text": ["651", 0], "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "648": {"_meta": {"title": "CLIP Text Encode (Advanced)"}, "class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["650", 1], "text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "649": {"_meta": {"title": "FaceDetailer"}, "class_type": "FaceDetailer", "inputs": {"bbox_crop_factor": 3, "bbox_detector": ["645", 0], "bbox_dilation": 500, "bbox_threshold": 0.5, "cfg": "3", "clip": ["650", 1], "cycle": 1, "denoise": 0.4, "drop_size": 40, "feather": 5, "force_inpaint": true, "guide_size": 384, "guide_size_for": true, "image": ["609", 0], "inpaint_model": 1, "max_size": 512, "model": ["650", 0], "negative": ["648", 0], "noise_mask": true, "noise_mask_feather": 0, "positive": ["647", 0], "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_model_opt": ["646", 0], "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "normal", "seed": 889757844059893, "steps": 8, "vae": ["650", 2], "wildcard": ""}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "650": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "disable": "${(!isUseLoraFace)?then('false','true')}"}, "651": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1858}}, "654": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["624", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "655": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["623", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "656": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["654", 0]}}, "657": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["656", 0]}}, "658": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["655", 0]}}, "659": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["658", 0]}}, "660": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["623", 0], "image": ["${isUseLoraFace?then(631,596)}", 0], "width": ["624", 0], "x": 0, "y": 0}}, "666": {"_meta": {"title": "是否使用风格lora生成图"}, "class_type": "CR Text", "inputs": {"text": "1"}}, "667": {"_meta": {"title": "Compare"}, "class_type": "easy compare", "inputs": {"a": ["666", 0], "b": ["668", 0], "comparison": "a == b"}}, "668": {"_meta": {"title": "🔤 CR Text"}, "class_type": "CR Text", "inputs": {"text": "0"}}, "669": {"_meta": {"title": "If else"}, "class_type": "easy ifElse", "inputs": {"boolean": ["667", 0], "on_false": ["673", 0], "on_true": ["463", 0]}}, "672": {"_meta": {"title": "Show Any"}, "class_type": "easy showAnything", "inputs": {"anything": ["436", 1], "text": "4"}}, "673": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["660", 0], "any_b": ["463", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"}}, "764": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["265", 0], "image": ["397", 0], "width": ["264", 0], "x": ["765", 0], "y": ["766", 0]}}, "765": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["176", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"}}, "766": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["176", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"}}, "767": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["265", 0], "image": ["194", 0], "width": ["264", 0], "x": ["765", 0], "y": ["766", 0]}}, "774": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["168", 1], "call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"}}, "775": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["168", 1], "call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"}}, "779": {"_meta": {"title": "🌱 CR Seed"}, "class_type": "CR Seed", "inputs": {"seed": 0}}, "793": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["289", 0], "any_b": ["435", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"}}, "794": {"_meta": {"title": "Image Batch to Image List"}, "class_type": "ImpactImageBatchToImageList", "inputs": {"image": ["276", 0]}}, "800": {"_meta": {"title": "DensePose Estimator"}, "class_type": "DensePosePreprocessor", "inputs": {"cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "image": ["844", 0], "model": "densepose_r50_fpn_dl.torchscript", "resolution": 512}}, "801": {"_meta": {"title": "Conrain Mask From Colors"}, "class_type": "ConrainMaskFromColors", "inputs": {"color_list": ["803", 0], "image": ["800", 0], "threshold": 2}}, "803": {"_meta": {"title": "densepose提取mask区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["368", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"}}, "808": {"_meta": {"title": "需要排除的mask"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["368", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"}}, "810": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["801", 0], "tapered_corners": false}}, "812": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["366", 1], "tapered_corners": false}}, "814": {"_meta": {"title": "Mask Resize"}, "class_type": "JWMaskResize", "inputs": {"height": ["815", 1], "interpolation_mode": "bilinear", "mask": ["810", 0], "width": ["815", 0]}}, "815": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["366", 0]}}, "818": {"_meta": {"title": "Image Blank"}, "class_type": "Image Blank", "inputs": {"blue": 255, "green": 255, "height": ["815", 1], "red": 255, "width": ["815", 0]}}, "819": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["818", 0]}}, "820": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["808", 0]}}, "821": {"_meta": {"title": "需要排除的区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["368", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"}}, "823": {"_meta": {"title": "If else"}, "class_type": "easy ifElse", "inputs": {"boolean": ["821", 0], "on_false": ["819", 0], "on_true": ["369", 1]}}, "837": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["812", 0]}}, "839": {"_meta": {"title": "保存参看图的mask"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "mask_${fileNamePrefix}", "text_c": "", "text_d": ""}}, "840": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["839", 1], "images": ["837", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["839", 0], "quality": 100, "use_time_str": "true"}}, "844": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["397", 0], "torchscript_jit": "default"}}, "847": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["281", 0], "text": ""}}, "850": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["651", 0]}}, "860": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["862", 0], "type": "openpose"}}, "861": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["862", 0], "type": "depth"}}, "862": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}}, "863": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["630", 1], "text": ["621", 0]}}, "865": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "866": {"_meta": {"title": "Get Image Size & Count"}, "class_type": "GetImageSizeAndCount", "inputs": {"image": ["872", 0]}}, "867": {"_meta": {"title": "CLIP Text Encode (Negative Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["630", 1], "text": ""}}, "868": {"_meta": {"title": "Depth Anything"}, "class_type": "DepthAnythingPreprocessor", "inputs": {"ckpt_name": "depth_anything_vits14.pth", "image": ["883", 0], "resolution": 1024}}, "869": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["860", 0], "end_percent": 0.2, "image": ["866", 0], "negative": ["867", 0], "positive": ["863", 0], "start_percent": 0, "strength": 0.6, "vae": ["865", 0]}}, "870": {"_meta": {"title": "Apply Controlnet with VAE"}, "class_type": "ControlNetApplySD3", "inputs": {"control_net": ["861", 0], "end_percent": 0.2, "image": ["868", 0], "negative": ["869", 1], "positive": ["869", 0], "start_percent": 0, "strength": 0.4, "vae": ["865", 0]}}, "872": {"_meta": {"title": "DWPose Estimator"}, "class_type": "DWPreprocessor", "inputs": {"bbox_detector": "yolox_l.onnx", "detect_body": "enable", "detect_face": "enable", "detect_hand": "enable", "image": ["883", 0], "pose_estimator": "dw-ll_ucoco_384.onnx", "resolution": 1024}}, "874": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["880", 0], "expression": "a[1][0]"}}, "875": {"_meta": {"title": "🔧 Image Composite"}, "class_type": "ImageComposite+", "inputs": {"destination": ["884", 0], "offset_x": 0, "offset_y": 0, "source": ["882", 0], "x": ["874", 0], "y": ["879", 0]}}, "877": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["880", 0], "expression": "a[0][0]"}}, "878": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["880", 0], "expression": "a[0][1]"}}, "879": {"_meta": {"title": "Math Expression +"}, "class_type": "MathExpressionPlus", "inputs": {"a": ["880", 0], "expression": "a[1][1]"}}, "880": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["881", 0], "any_b": ["881", 1], "any_c": ["624", 0], "any_d": ["623", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),"}}, "881": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["882", 0]}}, "882": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${referenceImage}", "upload": "image"}}, "883": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "downscale if bigger", "height": 1024, "image": ["875", 0], "interpolation": "nearest", "method": "keep proportion", "multiple_of": 0, "width": 1024}}, "884": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 16777215, "height": ["878", 0], "width": ["877", 0]}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [8387.268533042414, 654.9470240119424], "scale": 0.30735605491428325}}, "groups": [{"id": 2, "bounding": [-4589.86474609375, 176.38858032226562, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "cat图片准备"}, {"id": 5, "bounding": [-7916.77880859375, 1547.8116455078125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理服装图mask"}, {"id": 6, "bounding": [-7896.23095703125, 199.59716796875, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理参考图mask"}, {"id": 7, "bounding": [-4552.4736328125, 1515.76904296875, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "重绘"}, {"id": 8, "bounding": [-11184.8447265625, 188.96893310546875, 3120.570556640625, 2013.73291015625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "图片尺寸最大2048"}, {"id": 14, "bounding": [-8260, -3310, 1037.859130859375, 1197.2232666015625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 15, "bounding": [-9690, -3310, 1347, 1200], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "1.5修脸"}, {"id": 16, "bounding": [-7120, -3960, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 17, "bounding": [-9690, -4330, 1350, 956], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "修脸换脸"}, {"id": 18, "bounding": [-12137.796875, -4320.5615234375, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 19, "bounding": [-7120, -3290, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 26, "bounding": [-12964.205078125, 204.96542358398438, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 27, "bounding": [-9863.421875, -857.2464599609375, 3980.1259765625, 983.1404418945312], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理模特图的mask"}, {"id": 28, "bounding": [-11510, -5480, 3157.2744140625, 1028.9208984375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "controlnet"}], "last_link_id": 1667, "last_node_id": 884, "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [709, 365, 0, 366, 0, "SAM_MODEL"], [710, 367, 0, 366, 1, "GROUNDING_DINO_MODEL"], [712, 365, 0, 369, 0, "SAM_MODEL"], [713, 367, 0, 369, 1, "GROUNDING_DINO_MODEL"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [834, 368, 0, 418, 0, "*"], [837, 418, 0, 419, 0, "STRING"], [839, 419, 0, 366, 3, "STRING"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1153, 594, 0, 592, 0, "STRING"], [1154, 626, 0, 592, 1, "STRING"], [1155, 593, 0, 592, 2, "STRING"], [1156, 627, 0, 592, 3, "STRING"], [1157, 631, 0, 596, 0, "IMAGE"], [1158, 595, 0, 596, 1, "FACE_MODEL"], [1159, 595, 1, 596, 2, "FACE_MODEL"], [1160, 595, 2, 596, 3, "FACE_MODEL"], [1161, 595, 3, 596, 4, "FACE_MODEL"], [1162, 590, 0, 596, 5, "IMAGE"], [1163, 660, 0, 597, 0, "IMAGE"], [1164, 591, 0, 597, 1, "STRING"], [1165, 591, 1, 597, 2, "STRING"], [1166, 599, 0, 598, 0, "STRING"], [1167, 591, 0, 598, 1, "STRING"], [1168, 601, 0, 600, 0, "IMAGE"], [1169, 596, 0, 601, 0, "IMAGE"], [1170, 605, 4, 603, 0, "INT"], [1171, 605, 5, 603, 1, "INT"], [1172, 600, 0, 604, 0, "IMAGE"], [1173, 601, 0, 605, 0, "IMAGE"], [1174, 642, 0, 606, 0, "IMAGE"], [1175, 607, 0, 606, 1, "FLOAT"], [1176, 642, 0, 607, 0, "IMAGE"], [1177, 662, 0, 607, 1, "INT"], [1178, 628, 1, 608, 0, "CLIP"], [1179, 622, 0, 608, 1, "STRING"], [1180, 613, 0, 609, 0, "LATENT"], [1181, 610, 0, 609, 1, "VAE"], [1182, 617, 0, 613, 0, "NOISE"], [1183, 616, 0, 613, 1, "GUIDER"], [1184, 614, 0, 613, 2, "SAMPLER"], [1185, 615, 0, 613, 3, "SIGMAS"], [1186, 619, 0, 613, 4, "LATENT"], [1187, 620, 0, 615, 0, "MODEL"], [1188, 620, 0, 616, 0, "MODEL"], [1189, 618, 0, 616, 1, "CONDITIONING"], [1191, 657, 0, 619, 0, "INT"], [1192, 659, 0, 619, 1, "INT"], [1194, 657, 0, 620, 1, "INT"], [1195, 659, 0, 620, 2, "INT"], [1196, 621, 0, 622, 0, "STRING"], [1197, 621, 0, 626, 0, "*"], [1198, 625, 0, 627, 0, "*"], [1199, 630, 0, 628, 0, "MODEL"], [1200, 630, 1, 628, 1, "CLIP"], [1206, 632, 0, 631, 1, "DETAILER_PIPE"], [1207, 633, 0, 632, 0, "BASIC_PIPE"], [1208, 635, 0, 632, 1, "BBOX_DETECTOR"], [1210, 628, 1, 633, 1, "CLIP"], [1211, 610, 0, 633, 2, "VAE"], [1212, 634, 0, 633, 3, "CONDITIONING"], [1213, 636, 0, 633, 4, "CONDITIONING"], [1214, 628, 1, 634, 0, "CLIP"], [1216, 604, 0, 642, 0, "IMAGE"], [1217, 603, 0, 642, 1, "IMAGE"], [1218, 600, 1, 642, 2, "MASK"], [1219, 592, 0, 644, 0, "STRING"], [1220, 598, 0, 644, 1, "STRING"], [1221, 591, 1, 644, 2, "STRING"], [1222, 650, 1, 647, 0, "CLIP"], [1224, 650, 1, 648, 0, "CLIP"], [1225, 609, 0, 649, 0, "IMAGE"], [1226, 650, 0, 649, 1, "MODEL"], [1227, 650, 1, 649, 2, "CLIP"], [1228, 650, 2, 649, 3, "VAE"], [1229, 647, 0, 649, 4, "CONDITIONING"], [1230, 648, 0, 649, 5, "CONDITIONING"], [1231, 645, 0, 649, 6, "BBOX_DETECTOR"], [1232, 646, 0, 649, 7, "SAM_MODEL"], [1233, 624, 0, 654, 0, "*"], [1234, 623, 0, 655, 0, "*"], [1235, 654, 0, 656, 0, "*"], [1236, 656, 0, 657, 0, "STRING"], [1237, 655, 0, 658, 0, "*"], [1238, 658, 0, 659, 0, "STRING"], [1239, 606, 0, 660, 0, "IMAGE"], [1240, 624, 0, 660, 1, "INT"], [1241, 623, 0, 660, 2, "INT"], [1242, 596, 0, 661, 0, "IMAGE"], [1243, 661, 4, 662, 0, "INT"], [1244, 661, 5, 662, 1, "INT"], [1248, 666, 0, 667, 0, "*"], [1249, 668, 0, 667, 1, "*"], [1250, 667, 0, 669, 2, "BOOLEAN"], [1256, 660, 0, 670, 0, "IMAGE"], [1257, 462, 0, 407, 0, "*"], [1259, 669, 0, 402, 0, "*"], [1262, 436, 1, 672, 0, "*"], [1265, 673, 0, 669, 1, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1452, 660, 0, 665, 0, "*"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1473, 407, 0, 787, 0, "*"], [1496, 289, 0, 793, 0, "*"], [1497, 793, 0, 288, 0, "STITCH"], [1498, 276, 0, 794, 0, "IMAGE"], [1499, 794, 0, 288, 1, "IMAGE"], [1500, 435, 0, 793, 1, "*"], [1510, 461, 0, 331, 0, "*"], [1512, 800, 0, 801, 0, "IMAGE"], [1513, 368, 0, 803, 0, "*"], [1514, 803, 0, 801, 1, "STRING"], [1518, 801, 0, 810, 0, "MASK"], [1522, 366, 1, 812, 0, "MASK"], [1527, 810, 0, 814, 0, "MASK"], [1528, 366, 0, 815, 0, "IMAGE"], [1533, 815, 0, 814, 2, "INT"], [1534, 815, 1, 814, 1, "INT"], [1536, 368, 0, 808, 0, "*"], [1540, 815, 0, 818, 0, "INT"], [1541, 815, 1, 818, 1, "INT"], [1542, 818, 0, 819, 0, "IMAGE"], [1543, 808, 0, 820, 0, "STRING"], [1544, 820, 0, 369, 3, "STRING"], [1545, 368, 0, 821, 0, "*"], [1546, 821, 0, 823, 2, "BOOLEAN"], [1547, 819, 0, 823, 1, "*"], [1548, 369, 1, 823, 0, "*"], [1549, 823, 0, 822, 1, "MASK"], [1550, 811, 0, 822, 0, "MASK"], [1552, 822, 0, 315, 0, "MASK"], [1563, 831, 0, 669, 0, "*"], [1564, 831, 0, 673, 1, "*"], [1567, 463, 0, 831, 0, "*"], [1572, 822, 0, 837, 0, "MASK"], [1575, 611, 0, 629, 1, "CLIP"], [1576, 629, 0, 630, 0, "MODEL"], [1577, 629, 1, 630, 1, "CLIP"], [1579, 839, 0, 840, 1, "STRING"], [1580, 839, 1, 840, 2, "STRING"], [1581, 837, 0, 840, 0, "IMAGE"], [1585, 328, 0, 843, 0, "*"], [1586, 328, 0, 844, 0, "IMAGE"], [1588, 844, 0, 845, 0, "*"], [1589, 845, 0, 366, 2, "IMAGE"], [1590, 845, 0, 369, 2, "IMAGE"], [1591, 845, 0, 800, 0, "IMAGE"], [1593, 281, 0, 847, 0, "CLIP"], [1594, 847, 0, 290, 0, "CONDITIONING"], [1599, 651, 0, 634, 1, "STRING"], [1600, 651, 0, 647, 1, "STRING"], [1602, 651, 0, 850, 0, "STRING"], [1603, 609, 0, 851, 0, "IMAGE"], [1604, 628, 0, 620, 0, "MODEL"], [1609, 612, 0, 629, 0, "MODEL"], [1611, 631, 0, 853, 0, "IMAGE"], [1613, 609, 0, 631, 0, "IMAGE"], [1614, 665, 0, 673, 0, "*"], [1615, 408, 0, 773, 0, "*"], [1620, 629, 0, 633, 0, "MODEL"], [1622, 812, 0, 811, 0, "MASK"], [1623, 814, 0, 811, 1, "MASK"], [1624, 862, 0, 860, 0, "CONTROL_NET"], [1625, 862, 0, 861, 0, "CONTROL_NET"], [1626, 628, 1, 863, 0, "CLIP"], [1627, 621, 0, 863, 1, "STRING"], [1628, 872, 0, 866, 0, "IMAGE"], [1629, 628, 1, 867, 0, "CLIP"], [1631, 863, 0, 869, 0, "CONDITIONING"], [1632, 867, 0, 869, 1, "CONDITIONING"], [1633, 860, 0, 869, 2, "CONTROL_NET"], [1634, 865, 0, 869, 3, "VAE"], [1635, 866, 0, 869, 4, "IMAGE"], [1636, 869, 0, 870, 0, "CONDITIONING"], [1637, 869, 1, 870, 1, "CONDITIONING"], [1638, 861, 0, 870, 2, "CONTROL_NET"], [1639, 865, 0, 870, 3, "VAE"], [1640, 868, 0, 870, 4, "IMAGE"], [1641, 882, 0, 871, 0, "*"], [1646, 880, 0, 874, 0, "INT,FLOAT"], [1648, 871, 0, 875, 1, "IMAGE"], [1649, 874, 0, 875, 3, "INT"], [1650, 879, 0, 875, 4, "INT"], [1653, 880, 0, 877, 0, "INT,FLOAT"], [1654, 880, 0, 878, 0, "INT,FLOAT"], [1655, 880, 0, 879, 0, "INT,FLOAT"], [1656, 881, 0, 880, 0, "*"], [1657, 881, 1, 880, 1, "*"], [1658, 624, 0, 880, 2, "*"], [1659, 623, 0, 880, 3, "*"], [1660, 871, 0, 881, 0, "IMAGE"], [1661, 875, 0, 883, 0, "IMAGE"], [1662, 870, 0, 618, 0, "CONDITIONING"], [1663, 883, 0, 868, 0, "IMAGE"], [1664, 883, 0, 872, 0, "IMAGE"], [1665, 884, 0, 875, 0, "IMAGE"], [1666, 877, 0, 884, 0, "INT"], [1667, 878, 0, 884, 1, "INT"]], "nodes": [{"id": 52, "flags": {}, "inputs": [{"link": 820, "name": "images", "type": "IMAGE"}], "mode": 0, "order": 232, "outputs": [{"links": [109], "name": "masks", "slot_index": 0, "type": "MASK"}], "pos": [-3768.537109375, 815.3216552734375], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "size": [261.10693359375, 178], "type": "APersonMaskGenerator", "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "flags": {}, "inputs": [{"link": 109, "name": "mask", "type": "MASK"}], "mode": 0, "order": 236, "outputs": [{"links": [594, 596], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2910.48828125, 882.318603515625], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 54, "flags": {}, "inputs": [{"link": 592, "name": "mask", "type": "MASK"}], "mode": 0, "order": 223, "outputs": [{"links": [597], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2914.48828125, 1064.301513671875], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 56, "flags": {}, "inputs": [{"link": 598, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 242, "outputs": [{"links": [1434], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-2015, 901], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 59.905555725097656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 60, "flags": {}, "inputs": [{"link": 821, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 233, "outputs": [{"links": [522], "name": "width", "slot_index": 0, "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-3979.537109375, 1269.284423828125], "properties": {"Node name for S&R": "Get Image Size"}, "size": [298.42425537109375, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 61, "flags": {}, "inputs": [{"link": 186, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 222, "outputs": [{"links": [520], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [521], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-3224, 2098], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 113, "flags": {}, "inputs": [{"link": 819, "name": "", "type": "*"}], "mode": 0, "order": 219, "outputs": [{"links": [186, 587], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4104.23046875, 671.5852661132812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 159, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [873], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [874], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-2256, 2284], "properties": {"Node name for S&R": "Text String"}, "size": [228.5572052001953, 190], "title": "保存换装结果图片", "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 167, "flags": {}, "inputs": [{"link": 441, "name": "", "type": "*"}], "mode": 0, "order": 231, "outputs": [{"links": [820, 821, 822, 823], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4471.634765625, 789.323486328125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 168, "flags": {}, "inputs": [{"link": 606, "name": "image", "type": "IMAGE"}, {"link": 569, "name": "mask", "type": "MASK"}], "mode": 0, "order": 183, "outputs": [{"links": [571], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [292, 1448], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-6783.6259765625, 303.************], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [248.14456176757812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 176, "flags": {"collapsed": true}, "inputs": [{"link": 566, "name": "any_a", "shape": 7, "type": "*"}, {"link": 567, "name": "any_b", "shape": 7, "type": "*"}, {"link": 292, "name": "any_c", "shape": 7, "type": "*"}, {"link": 451, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 202, "outputs": [{"links": [544, 1423, 1424], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5825.6416015625, 656.2598876953125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [423.4119567871094, 402.7642517089844], "title": "以涂抹区域的为中心点画一个矩形框", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "flags": {}, "inputs": [{"link": 570, "name": "mask", "type": "MASK"}], "mode": 0, "order": 184, "outputs": [{"links": [1429], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5858.185546875, 987.3473510742188], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 208, "flags": {}, "inputs": [{"link": 1428, "name": "", "type": "*"}], "mode": 0, "order": 215, "outputs": [{"links": [819], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4375.634765625, 471.330810546875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 211, "flags": {}, "inputs": [{"link": 1432, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 216, "outputs": [{"links": [591], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-5035.185546875, 1110.34619140625], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 84.43663024902344], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 242, "flags": {}, "inputs": [{"link": 655, "name": "image", "type": "IMAGE"}, {"link": 1465, "name": "mask", "type": "MASK"}, {"link": 1456, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 1457, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 1458, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 1459, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 213, "outputs": [{"links": [439, 580], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-6862, 1899.17578125], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [10, 10, 10, 10]}, {"id": 243, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "destination", "link": 418, "name": "destination", "type": "IMAGE"}, {"label": "source", "link": 440, "name": "source", "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "x", "link": 420, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"label": "y", "link": 421, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 229, "outputs": [{"label": "IMAGE", "links": [441], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5039.65869140625, 1842.2310791015625], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "size": [210, 138], "type": "ImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 245, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "width", "link": 757, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 759, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 205, "outputs": [{"label": "IMAGE", "links": [418], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5749.5673828125, 1642.17578125], "properties": {"Node name for S&R": "EmptyImage"}, "size": [243.3533935546875, 102], "title": "设置底图颜色", "type": "EmptyImage", "widgets_values": [512, 512, 1, 16777215]}, {"id": 246, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1446, "name": "seed", "type": "INT", "widget": {"name": "seed"}}], "mode": 0, "order": 190, "outputs": [{"links": [453], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7410.6416015625, 779.2598876953125], "properties": {"Node name for S&R": "CR Seed"}, "size": [281.7162780761719, 102], "title": "默认宽", "type": "CR Seed", "widgets_values": [1152, "fixed"]}, {"id": 247, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1451, "name": "seed", "type": "INT", "widget": {"name": "seed"}}], "mode": 0, "order": 191, "outputs": [{"links": [455], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7388.6416015625, 1075.259521484375], "properties": {"Node name for S&R": "CR Seed"}, "size": [278.3121032714844, 102], "title": "默认高", "type": "CR Seed", "widgets_values": [1536, "fixed"]}, {"id": 249, "flags": {}, "inputs": [{"link": 585, "name": "a", "shape": 7, "type": "*"}, {"link": 760, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 228, "outputs": [{"links": [421, 770], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-5389.65869140625, 1847.23095703125], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [220.35072326660156, 98], "type": "SimpleMath+", "widgets_values": ["b/2-a/2"]}, {"id": 250, "flags": {}, "inputs": [{"link": 584, "name": "a", "shape": 7, "type": "*"}, {"link": 758, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 227, "outputs": [{"links": [420, 771], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-5389.65869140625, 1648.231689453125], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 112.43743896484375], "type": "SimpleMath+", "widgets_values": ["b/2-a/2"]}, {"id": 253, "flags": {}, "inputs": [{"link": 481, "name": "any_a", "shape": 7, "type": "*"}, {"link": 478, "name": "any_b", "shape": 7, "type": "*"}, {"link": 581, "name": "any_c", "shape": 7, "type": "*"}, {"link": 582, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 221, "outputs": [{"links": [486, 748], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-6450.5673828125, 1647.17578125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [282.12066650390625, 195.71939086914062], "title": "原logo比目标尺寸大时缩小到目标尺寸", "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "flags": {}, "inputs": [{"link": 439, "name": "image", "type": "IMAGE"}, {"link": 486, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 224, "outputs": [{"links": [440, 583], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6106.5673828125, 1903.17578125], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [261.7075500488281, 81.54931640625], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 256, "flags": {}, "inputs": [{"link": 474, "name": "any_a", "shape": 7, "type": "*"}, {"link": 477, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 199, "outputs": [{"links": [451], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-6218.6416015625, 596.2598876953125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [270.53582763671875, 200.77845764160156], "title": "concat的图片大小", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "flags": {}, "inputs": [{"link": 564, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 453, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 192, "outputs": [{"links": [473], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6958.185546875, 694.3478393554688], "properties": {"Node name for S&R": "JWIntegerMin"}, "size": [213.4287567138672, 79.92222595214844], "title": "Minimum宽", "type": "JWIntegerMin", "widgets_values": [0, 0]}, {"id": 259, "flags": {}, "inputs": [{"link": 565, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 455, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 193, "outputs": [{"links": [476], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6964.185546875, 1044.346923828125], "properties": {"Node name for S&R": "JWIntegerMin"}, "size": [213.4287567138672, 79.92222595214844], "title": "Minimum高", "type": "JWIntegerMin", "widgets_values": [0, 0]}, {"id": 264, "flags": {}, "inputs": [{"link": 572, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 473, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 194, "outputs": [{"links": [474, 481, 648, 675, 755, 1426, 1430], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6605.185546875, 670.3475952148438], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 71.68185424804688], "title": "Maximum宽", "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 265, "flags": {}, "inputs": [{"link": 573, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 476, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 195, "outputs": [{"links": [477, 478, 652, 756, 1427, 1431], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6647.185546875, 961.34765625], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 71.68185424804688], "title": "Maximum高", "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 268, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 1, "outputs": [], "pos": [-6955.6416015625, 830.2598876953125], "properties": {}, "size": [210, 67.93143463134766], "type": "Note", "widgets_values": ["模特图较小时，以模特图的大小为准"]}, {"id": 269, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 2, "outputs": [], "pos": [-6592.185546875, 828.347900390625], "properties": {}, "size": [210, 67.93143463134766], "type": "Note", "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"]}, {"id": 275, "flags": {}, "inputs": [{"label": "模型", "link": 493, "name": "model", "type": "MODEL"}, {"label": "正面条件", "link": 494, "name": "positive", "type": "CONDITIONING"}, {"label": "负面条件", "link": 495, "name": "negative", "type": "CONDITIONING"}, {"label": "Latent", "link": 850, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 248, "outputs": [{"label": "Latent", "links": [497], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [-2921, 1656], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [234.29580688476562, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": [661045452417164, "fixed", 20, 2.5, "euler", "simple", 1]}, {"id": 276, "flags": {}, "inputs": [{"label": "Latent", "link": 497, "name": "samples", "type": "LATENT"}, {"label": "VAE", "link": 498, "name": "vae", "type": "VAE"}], "mode": 0, "order": 250, "outputs": [{"label": "图像", "links": [1498], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2651, 1594], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 277, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "CLIP", "link": 499, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 58, "outputs": [{"label": "条件", "links": [500], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-4136.73095703125, 1819.626220703125], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [269.3892822265625, 89.79380798339844], "type": "CLIPTextEncode", "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "]}, {"id": 278, "flags": {}, "inputs": [{"label": "条件", "link": 500, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 85, "outputs": [{"label": "条件", "links": [508], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3830.************, 1833.23193359375], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [210, 58], "type": "FluxGuidance", "widgets_values": [30]}, {"id": 279, "flags": {}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "模型", "links": [516], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-4035, 1583], "properties": {"Node name for S&R": "UNETLoader"}, "size": [326.5174865722656, 82], "type": "UNETLoader", "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [{"links": [498, 503], "name": "VAE", "slot_index": 0, "type": "VAE"}], "pos": [-4452.8623046875, 1858.************], "properties": {"Node name for S&R": "VAELoader"}, "size": [300, 60], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 281, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"label": "CLIP", "links": [499, 1593], "name": "CLIP", "slot_index": 0, "type": "CLIP"}], "pos": [-4479.83203125, 1664.2646484375], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 282, "flags": {}, "inputs": [{"label": "正面条件", "link": 501, "name": "positive", "type": "CONDITIONING"}, {"label": "负面条件", "link": 502, "name": "negative", "type": "CONDITIONING"}, {"label": "VAE", "link": 503, "name": "vae", "type": "VAE"}, {"label": "图像", "link": 1437, "name": "pixels", "type": "IMAGE"}, {"label": "遮罩", "link": 1438, "name": "mask", "type": "MASK"}], "mode": 0, "order": 246, "outputs": [{"label": "正面条件", "links": [494], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"label": "负面条件", "links": [495], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}, {"label": "Latent", "links": [848], "name": "latent", "slot_index": 2, "type": "LATENT"}], "pos": [-3258, 1766], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 283, "flags": {}, "inputs": [{"label": "模型", "link": 506, "name": "model", "type": "MODEL"}], "mode": 0, "order": 84, "outputs": [{"label": "模型", "links": [493], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-3256.462890625, 1645.7598876953125], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [184.8000030517578, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"label": "CLIP视觉", "links": [507], "name": "CLIP_VISION", "slot_index": 0, "type": "CLIP_VISION"}], "pos": [-4473.435546875, 2063.94091796875], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "size": [370, 60], "type": "CLIPVisionLoader", "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "flags": {}, "inputs": [{"label": "CLIP视觉", "link": 507, "name": "clip_vision", "type": "CLIP_VISION"}, {"label": "图像", "link": 822, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 234, "outputs": [{"label": "CLIP视觉输出", "links": [510], "name": "CLIP_VISION_OUTPUT", "slot_index": 0, "type": "CLIP_VISION_OUTPUT"}], "pos": [-3917.685302734375, 1980.57470703125], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "size": [210, 78], "type": "CLIPVisionEncode", "widgets_values": ["center"]}, {"id": 286, "flags": {}, "inputs": [{"label": "条件", "link": 508, "name": "conditioning", "type": "CONDITIONING"}, {"label": "风格模型", "link": 509, "name": "style_model", "type": "STYLE_MODEL"}, {"label": "CLIP视觉输出", "link": 510, "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT"}], "mode": 0, "order": 237, "outputs": [{"label": "条件", "links": [501], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3552.980712890625, 1847.6009521484375], "properties": {"Node name for S&R": "StyleModelApply"}, "size": [210, 122], "type": "StyleModelApply", "widgets_values": [1, "multiply"]}, {"id": 287, "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"label": "风格模型", "links": [509], "name": "STYLE_MODEL", "type": "STYLE_MODEL"}], "pos": [-4462.771484375, 1981.87158203125], "properties": {"Node name for S&R": "StyleModelLoader"}, "size": [340, 60], "type": "StyleModelLoader", "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "flags": {}, "inputs": [{"label": "接缝", "link": 1497, "name": "stitch", "type": "STITCH"}, {"label": "图像", "link": 1499, "name": "inpainted_image", "type": "IMAGE"}], "mode": 0, "order": 252, "outputs": [{"label": "图像", "links": [1440], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [-2232, 1585], "properties": {"Node name for S&R": "InpaintStitch"}, "size": [256.60272216796875, 78], "type": "InpaintStitch", "widgets_values": ["bislerp"]}, {"id": 289, "flags": {}, "inputs": [{"label": "图像", "link": 1435, "name": "image", "type": "IMAGE"}, {"label": "遮罩", "link": 1436, "name": "mask", "type": "MASK"}, {"label": "上下文遮罩(可选)", "link": 530, "name": "optional_context_mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 243, "outputs": [{"label": "接缝", "links": [1496], "name": "stitch", "slot_index": 0, "type": "STITCH"}, {"links": [1437], "name": "cropped_image", "slot_index": 1, "type": "IMAGE"}, {"links": [1438], "name": "cropped_mask", "slot_index": 2, "type": "MASK"}], "pos": [-3546, 2132], "properties": {"Node name for S&R": "InpaintCrop"}, "size": [245.64613342285156, 386], "type": "InpaintCrop", "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "flags": {"collapsed": false}, "inputs": [{"label": "条件", "link": 1594, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 86, "outputs": [{"label": "条件", "links": [502], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3840.737548828125, 1724.7645263671875], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "size": [317.4000244140625, 26], "type": "ConditioningZeroOut", "widgets_values": []}, {"id": 291, "flags": {}, "inputs": [{"link": 1440, "name": "image", "type": "IMAGE"}, {"link": 520, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 521, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 522, "name": "x", "type": "INT", "widget": {"name": "x"}}], "mode": 0, "order": 253, "outputs": [{"links": [551], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2483, 1925], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 118], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 293, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 516, "name": "model", "type": "MODEL"}], "mode": 0, "order": 57, "outputs": [{"links": [506], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-3582, 1611], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "size": [271.6474304199219, 86.10514068603516], "type": "LoraLoaderModelOnly", "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1]}, {"id": 297, "flags": {}, "inputs": [{"link": 595, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 241, "outputs": [{"links": [530], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-2061, 614], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 59.905555725097656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 300, "flags": {}, "inputs": [{"link": 1466, "name": "mask", "type": "MASK"}], "mode": 0, "order": 214, "outputs": [{"links": [765], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6910, 2387.185546875], "properties": {"Node name for S&R": "MaskToImage"}, "size": [176.39999389648438, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 304, "flags": {}, "inputs": [{"link": 610, "name": "", "type": "*"}], "mode": 0, "order": 164, "outputs": [{"links": [547], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-2961.54296875, 2145.298828125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 305, "flags": {}, "inputs": [{"link": 544, "name": "", "type": "*"}], "mode": 0, "order": 207, "outputs": [{"links": [550], "name": "", "slot_index": 0, "type": "*"}], "pos": [-2614.54296875, 2141.298828125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 306, "flags": {}, "inputs": [{"link": 547, "name": "target", "type": "IMAGE"}, {"link": 550, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 551, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 254, "outputs": [{"links": [555, 879], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2227, 2015], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "size": [239.650634765625, 122], "type": "Bounded_Image_Blend_LR", "widgets_values": [1, 5]}, {"id": 308, "flags": {}, "inputs": [{"label": "images", "link": 555, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 873, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 874, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 255, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-1859, 2255], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [231.75296020507812, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 314, "flags": {}, "inputs": [{"link": 608, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 163, "outputs": [{"links": [564, 566], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [565, 567], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-7221.40380859375, 327.5995788574219], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 315, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1552, "name": "mask", "type": "MASK"}], "mode": 0, "order": 181, "outputs": [{"links": [569, 570], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7805, 381], "properties": {"Node name for S&R": "GrowMask"}, "size": [232.74205017089844, 84.21175384521484], "title": "替换区域扩张大小", "type": "GrowMask", "widgets_values": [10, false]}, {"id": 316, "flags": {}, "inputs": [{"link": 571, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 186, "outputs": [{"links": [572], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [573], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-6240.74169921875, 312.345947265625], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 319, "flags": {}, "inputs": [{"link": 580, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 217, "outputs": [{"links": [581], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [582], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-6619.5673828125, 1719.17578125], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [144.6750030517578, 71.8825912475586], "type": "GetImageSize+", "widgets_values": []}, {"id": 320, "flags": {}, "inputs": [{"link": 583, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 226, "outputs": [{"links": [584], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [585], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-5706.65869140625, 1981.8992919921875], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 321, "flags": {}, "inputs": [{"link": 823, "name": "image1", "type": "IMAGE"}, {"link": 587, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 235, "outputs": [{"links": [589, 1433], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-3834.8525390625, 403.8241271972656], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 322, "flags": {}, "inputs": [{"link": 591, "name": "mask", "type": "MASK"}], "mode": 0, "order": 220, "outputs": [{"links": [592], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-3365.26123046875, 981.890625], "properties": {"Node name for S&R": "GrowMask"}, "size": [315, 82], "type": "GrowMask", "widgets_values": [0, true]}, {"id": 323, "flags": {}, "inputs": [{"link": 773, "name": "image1", "type": "IMAGE"}, {"link": 594, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 239, "outputs": [{"links": [595], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2429.48828125, 610.3302001953125], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 324, "flags": {"collapsed": false}, "inputs": [{"link": 596, "name": "image1", "type": "IMAGE"}, {"link": 597, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 240, "outputs": [{"links": [598], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2486, 925], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 328, "flags": {}, "inputs": [{"link": 807, "name": "", "type": "*"}], "mode": 0, "order": 162, "outputs": [{"links": [606, 608, 610, 1425, 1585, 1586], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-8748.2783203125, 435.44891357421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 331, "flags": {}, "inputs": [{"link": 1510, "name": "", "type": "*"}], "mode": 0, "order": 124, "outputs": [{"links": [647, 657, 670], "name": "", "slot_index": 0, "type": "MASK"}], "pos": [-7862.5673828125, 1620.17578125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 346, "flags": {}, "inputs": [{"link": 1444, "name": "image", "type": "IMAGE"}, {"link": 647, "name": "mask", "type": "MASK"}, {"link": 651, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 650, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 653, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 654, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 203, "outputs": [{"links": [655], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-7434, 1899.17578125], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 347, "flags": {"collapsed": false}, "inputs": [{"link": 648, "name": "a", "shape": 7, "type": "*"}, {"link": 1468, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 196, "outputs": [{"links": [650, 651, 659, 660], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-7797, 1927.17578125], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a/2+b"]}, {"id": 348, "flags": {"collapsed": false}, "inputs": [{"link": 652, "name": "a", "shape": 7, "type": "*"}, {"link": 1469, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 200, "outputs": [{"links": [653, 654, 661, 662], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-7785.5673828125, 2150.185546875], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a/2+b"]}, {"id": 349, "flags": {}, "inputs": [{"link": 670, "name": "mask", "type": "MASK"}], "mode": 0, "order": 126, "outputs": [{"links": [658], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-7774, 2329.185546875], "properties": {"Node name for S&R": "MaskToImage"}, "size": [176.39999389648438, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 350, "flags": {}, "inputs": [{"link": 658, "name": "image", "type": "IMAGE"}, {"link": 657, "name": "mask", "type": "MASK"}, {"link": 659, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 660, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 661, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 662, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 204, "outputs": [{"links": [663], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-7423, 2207.185546875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 351, "flags": {}, "inputs": [{"link": 663, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 210, "outputs": [{"links": [1465, 1466, 1467], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7171.5673828125, 2274.185546875], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 83.63514709472656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 362, "flags": {"collapsed": false}, "inputs": [{"link": 675, "name": "a", "shape": 7, "type": "*"}, {"name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 197, "outputs": [{"links": [], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-4408.435546875, 2163.94091796875], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a*2*2"]}, {"id": 365, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"label": "SAM_MODEL", "links": [709, 712], "name": "SAM_MODEL", "shape": 3, "slot_index": 0, "type": "SAM_MODEL"}], "pos": [-8832.4033203125, -166.285400390625], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "size": [269.19927978515625, 58], "type": "Conrain_SAMModelLoader", "widgets_values": ["sam_vit_h_cloth"]}, {"id": 366, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "sam_model", "link": 709, "name": "sam_model", "type": "SAM_MODEL"}, {"label": "grounding_dino_model", "link": 710, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"label": "image", "link": 1589, "name": "image", "type": "IMAGE"}, {"link": 839, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": 0, "order": 168, "outputs": [{"label": "IMAGE", "links": [1528], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [1522], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "IMAGE", "slot_index": 2, "type": "IMAGE"}], "pos": [-8237.40234375, -448.2854919433594], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "size": [260.3999938964844, 151.95924377441406], "type": "Conrain_GroundingDinoSAMSegment", "widgets_values": ["jacket", "white", 0.3]}, {"id": 367, "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [710, 713], "name": "GROUNDING_DINO_MODEL", "slot_index": 0, "type": "GROUNDING_DINO_MODEL"}], "pos": [-8843.6650390625, -305.6015625], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "size": [285.80181884765625, 58], "type": "Conrain_GroundingDinoModelLoader", "widgets_values": ["groundingdino_cloth"]}, {"id": 368, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [834, 1513, 1536, 1545], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-9829.40234375, -398.2855529785156], "properties": {"Node name for S&R": "CR Text"}, "size": [217.36741638183594, 128.27645874023438], "title": "抠图词", "type": "CR Text", "widgets_values": ["${clotheType}"]}, {"id": 369, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "sam_model", "link": 712, "name": "sam_model", "type": "SAM_MODEL"}, {"label": "grounding_dino_model", "link": 713, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"label": "image", "link": 1590, "name": "image", "type": "IMAGE"}, {"link": 1544, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": 0, "order": 169, "outputs": [{"label": "IMAGE", "links": [], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [1548], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "IMAGE", "slot_index": 2, "type": "IMAGE"}], "pos": [-7178.40771484375, -141.28543090820312], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "size": [260.3999938964844, 151.95924377441406], "type": "Conrain_GroundingDinoSAMSegment", "widgets_values": ["jacket", "white", 0.3]}, {"id": 384, "flags": {}, "inputs": [{"link": 766, "name": "image", "type": "IMAGE"}, {"link": 748, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 225, "outputs": [{"links": [768], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6080.5673828125, 2347.185546875], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [261.7075500488281, 81.54931640625], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 385, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "destination", "link": 749, "name": "destination", "type": "IMAGE"}, {"label": "source", "link": 768, "name": "source", "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "x", "link": 771, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"label": "y", "link": 770, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 230, "outputs": [{"label": "IMAGE", "links": [773], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5063.65869140625, 2265.23193359375], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "size": [210, 138], "type": "ImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 386, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "width", "link": 761, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 762, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 206, "outputs": [{"label": "IMAGE", "links": [749], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5709.65869140625, 2175.23193359375], "properties": {"Node name for S&R": "EmptyImage"}, "size": [243.3533935546875, 102], "title": "设置底图颜色", "type": "EmptyImage", "widgets_values": [512, 512, 1, 0]}, {"id": 390, "flags": {}, "inputs": [{"link": 755, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 198, "outputs": [{"links": [757, 758, 761], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-6058.65869140625, 1615.2320556640625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 391, "flags": {}, "inputs": [{"link": 756, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 201, "outputs": [{"links": [759, 760, 762], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-6041.65869140625, 1705.2315673828125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 392, "flags": {}, "inputs": [{"link": 765, "name": "image", "type": "IMAGE"}, {"link": 1467, "name": "mask", "type": "MASK"}, {"link": 1461, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 1462, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 1463, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 1464, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 218, "outputs": [{"links": [766], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-6673, 2271.185546875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [10, 10, 10, 10]}, {"id": 397, "flags": {}, "inputs": [{"link": 806, "name": "image", "type": "IMAGE"}, {"link": 798, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 161, "outputs": [{"links": [807], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9207, 439], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [217.8218994140625, 125.52959442138672], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 398, "flags": {}, "inputs": [{"link": 805, "name": "image", "type": "IMAGE"}, {"link": 797, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 160, "outputs": [{"links": [798], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-9680, 398], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": [512]}, {"id": 399, "flags": {}, "inputs": [{"link": 800, "name": "any_a", "shape": 7, "type": "*"}, {"link": 801, "name": "any_b", "shape": 7, "type": "*"}, {"link": 802, "name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 159, "outputs": [{"links": [797], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-10235, 516], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [365.79345703125, 195.28152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "flags": {}, "inputs": [{"link": 804, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 158, "outputs": [{"links": [800], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [801], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-10582, 556], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 401, "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [802, 811], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-11021, 844], "properties": {"Node name for S&R": "CR Seed"}, "size": [270.7088317871094, 109.29169464111328], "title": "最大支持尺寸", "type": "CR Seed", "widgets_values": [2100, "fixed"]}, {"id": 402, "flags": {}, "inputs": [{"link": 1259, "name": "", "type": "*"}], "mode": 0, "order": 157, "outputs": [{"links": [804, 805, 806], "name": "", "slot_index": 0, "type": "*"}], "pos": [-10955, 416], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 404, "flags": {}, "inputs": [{"link": 827, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 90, "outputs": [{"links": [809, 895], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [810, 896], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-11018, 1175], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 405, "flags": {}, "inputs": [{"link": 809, "name": "any_a", "shape": 7, "type": "*"}, {"link": 810, "name": "any_b", "shape": 7, "type": "*"}, {"link": 811, "name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 97, "outputs": [{"links": [812], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-10711.865234375, 1396.52978515625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [353.50982666015625, 168.5362548828125], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "flags": {}, "inputs": [{"link": 828, "name": "image", "type": "IMAGE"}, {"link": 812, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 104, "outputs": [{"links": [817, 897], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-10250.865234375, 1398.52978515625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 102.22442626953125], "type": "UpscaleSizeCalculator", "widgets_values": [512]}, {"id": 407, "flags": {}, "inputs": [{"link": 1257, "name": "", "type": "*"}], "mode": 0, "order": 66, "outputs": [{"links": [827, 828, 829, 1473], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-11098.865234375, 1623.52978515625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 408, "flags": {}, "inputs": [{"link": 829, "name": "image", "type": "IMAGE"}, {"link": 817, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 115, "outputs": [{"links": [1615], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9744, 1571], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 78], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 418, "flags": {"collapsed": true}, "inputs": [{"link": 834, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 60, "outputs": [{"links": [837], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9433.40234375, -255.2854766845703], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [296.9554443359375, 286.73455810546875], "title": "需要替换的区域", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 419, "flags": {}, "inputs": [{"link": 837, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 87, "outputs": [{"links": [839], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [-9081.40234375, -354.2854919433594], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [210, 326], "type": "ShowText|pysssss", "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "clothing", "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 433, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [846], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-4494, 2341], "properties": {"Node name for S&R": "CR Text"}, "size": [211.76846313476562, 168.80604553222656], "title": "tryon生成图片张数", "type": "CR Text", "widgets_values": ["${imageNum}"]}, {"id": 434, "flags": {}, "inputs": [{"link": 846, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 64, "outputs": [{"links": [847], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-4173.435546875, 2273.9404296875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [255.5079803466797, 218.5600128173828], "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "flags": {}, "inputs": [{"link": 847, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 89, "outputs": [{"links": [849, 1500], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-3869, 2279], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 55.71077346801758], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 436, "flags": {}, "inputs": [{"link": 848, "name": "latents", "type": "LATENT"}, {"link": 849, "name": "multiply_by", "type": "INT", "widget": {"name": "multiply_by"}}], "mode": 0, "order": 247, "outputs": [{"links": [850], "name": "LATENT", "slot_index": 0, "type": "LATENT"}, {"links": [1262], "name": "count", "slot_index": 1, "type": "INT"}], "pos": [-3320, 1941], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "size": [260.3999938964844, 58.512535095214844], "type": "VHS_DuplicateLatents", "widgets_values": {"multiply_by": 1}}, {"id": 451, "flags": {}, "inputs": [{"link": 879, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 256, "outputs": [], "pos": [-1892, 1739], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 456, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [880], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12923, 726], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${maskImage}", "image"]}, {"id": 457, "flags": {}, "inputs": [{"link": 880, "name": "", "type": "*"}], "mode": 0, "order": 65, "outputs": [{"links": [885], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-11051.4404296875, 1035.841796875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 459, "flags": {}, "inputs": [{"link": 885, "name": "image", "type": "IMAGE"}, {"link": 895, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 896, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 98, "outputs": [{"links": [889], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-10249.865234375, 1021.5302734375], "properties": {"Node name for S&R": "ImageResize+"}, "size": [315, 218], "type": "ImageResize+", "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "flags": {}, "inputs": [{"link": 889, "name": "image", "type": "IMAGE"}, {"link": 897, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 116, "outputs": [{"links": [890], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9742, 1040], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [214.26881408691406, 94.98396301269531], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 461, "flags": {}, "inputs": [{"link": 890, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 121, "outputs": [{"links": [1510], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-8799.865234375, 1392.52978515625], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 74.41427612304688], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 462, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [1257], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12928, 1139], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "title": "模特图", "type": "LoadImage", "widgets_values": ["${clotheImage}", "image"]}, {"id": 463, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"links": [1567], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12917, 294], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${referenceImage}", "image"]}, {"id": 590, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 53, "outputs": [{"label": "IMAGE", "links": [1162], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "name": "MASK", "shape": 3, "type": "MASK"}], "pos": [-8190, -2520], "properties": {"Node name for S&R": "LoadImage"}, "size": [320, 314], "title": "导入人脸图片", "type": "LoadImage", "widgets_values": ["${FACE.extInfo.faceImage}", "image"]}, {"id": 591, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 16, "outputs": [{"label": "STRING", "links": [1164, 1167], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [1165, 1221], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-6490, -3020], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "title": "保存生成图片", "type": "Text String", "widgets_values": ["${outputPath}", "face_${fileNamePrefix}", "", ""]}, {"id": 592, "flags": {}, "inputs": [{"label": "text_a", "link": 1153, "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 1154, "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 1155, "name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 1156, "name": "text_d", "shape": 7, "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 95, "outputs": [{"label": "STRING", "links": [1219], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-5440, -2860], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 593, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"label": "STRING", "links": [1155], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-6950, -2820], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 594, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"label": "STRING", "links": [1153], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-6930, -3010], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 595, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 52, "outputs": [{"label": "faceswapper_model", "links": [1158], "name": "faceswapper_model", "shape": 3, "slot_index": 0, "type": "FACE_MODEL"}, {"label": "facedetection_model", "links": [1159], "name": "facedetection_model", "shape": 3, "slot_index": 1, "type": "FACE_MODEL"}, {"label": "facerestore_model", "links": [1160], "name": "facerestore_model", "shape": 3, "slot_index": 2, "type": "FACE_MODEL"}, {"label": "faceparse_model", "links": [1161], "name": "faceparse_model", "shape": 3, "slot_index": 3, "type": "FACE_MODEL"}], "pos": [-8200, -2970], "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "size": [324.5391845703125, 190], "title": "导入换脸模型", "type": "LoadConrainReactorModels", "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 596, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "input_image", "link": 1157, "name": "input_image", "type": "IMAGE"}, {"label": "swap_model", "link": 1158, "name": "swap_model", "type": "FACE_MODEL"}, {"label": "facedetection", "link": 1159, "name": "facedetection", "type": "FACE_MODEL"}, {"label": "face_restore_model", "link": 1160, "name": "face_restore_model", "type": "FACE_MODEL"}, {"label": "faceparse_model", "link": 1161, "name": "faceparse_model", "type": "FACE_MODEL"}, {"label": "source_image", "link": 1162, "name": "source_image", "shape": 7, "type": "IMAGE"}, {"label": "face_model", "name": "face_model", "shape": 7, "type": "FACE_MODEL"}], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 139, "outputs": [{"label": "IMAGE", "links": [1169, 1242], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "FACE_MODEL", "name": "FACE_MODEL", "shape": 3, "type": "FACE_MODEL"}], "pos": [-7660, -2980], "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "size": [367.79998779296875, 370], "title": "换脸", "type": "ConrainReActorFaceSwap", "widgets_values": [true, "0.7", "0.7", "no", "no", "0", "0", 1, "yes"]}, {"id": 597, "flags": {}, "inputs": [{"label": "images", "link": 1163, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 1164, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 1165, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 152, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-4850, -3190], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 598, "flags": {}, "inputs": [{"label": "text_a", "link": 1166, "name": "text_a", "shape": 7, "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 1167, "name": "text_b", "shape": 7, "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "shape": 7, "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "shape": 7, "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 68, "outputs": [{"label": "STRING", "links": [1220], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-5990, -3170], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 599, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"label": "STRING", "links": [1166], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-6540, -3160], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 600, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1168, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 143, "outputs": [{"label": "IMAGE", "links": [1172], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [1218], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [-6550, -3530], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 601, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1169, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 141, "outputs": [{"label": "IMAGE", "links": [1168, 1173], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [-7040, -3660], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8]}, {"id": 602, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 4, "order": 20, "outputs": [], "pos": [-6120, -3850], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 603, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "width", "link": 1170, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 1171, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 4, "order": 147, "outputs": [{"label": "IMAGE", "links": [1217], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-6090, -3670], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, ""]}, {"id": 604, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 1172, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 146, "outputs": [{"label": "IMAGE", "links": [1216], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-6110, -3460], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 605, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1173, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 144, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [1170], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [1171], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [-6540, -3760], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 606, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 1174, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 1175, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 4, "order": 150, "outputs": [{"label": "IMAGE", "links": [1239], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-4780, -3780], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 607, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 1176, "name": "image", "type": "IMAGE"}, {"link": 1177, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 4, "order": 149, "outputs": [{"label": "rescale_factor", "links": [1175], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [-5100, -3670], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["1785"]}, {"id": 608, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 1178, "name": "clip", "type": "CLIP"}, {"link": 1179, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 105, "outputs": [{"links": [], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-11377.796875, -3820.************], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 609, "flags": {}, "inputs": [{"link": 1180, "name": "samples", "type": "LATENT"}, {"link": 1181, "name": "vae", "type": "VAE"}], "mode": 0, "order": 135, "outputs": [{"links": [1225, 1603, 1613], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-10147.796875, -4010.************], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 610, "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [{"links": [1181, 1211], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [-10507.796875, -3780.************], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 611, "flags": {}, "inputs": [], "mode": 0, "order": 22, "outputs": [{"links": [1575], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-12107.796875, -4040.************], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [282.20135498046875, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 612, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 23, "outputs": [{"links": [1609], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-12117.796875, -4230.5615234375], "properties": {"Node name for S&R": "UNETLoader"}, "size": [271.2318420410156, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "default"]}, {"id": 613, "flags": {}, "inputs": [{"link": 1182, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 1183, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 1184, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 1185, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 1186, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 134, "outputs": [{"links": [1180], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [-10457.796875, -4140.5615234375], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 112.51068878173828], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 614, "flags": {}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"links": [1184], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [-10797.796875, -3870.************], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["euler"]}, {"id": 615, "flags": {}, "inputs": [{"link": 1187, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 118, "outputs": [{"links": [1185], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [-10777.796875, -3660.************], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["beta", "20", 1]}, {"id": 616, "flags": {}, "inputs": [{"link": 1188, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1189, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 133, "outputs": [{"links": [1183], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [-10757.796875, -3990.************], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 617, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"links": [1182], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [-10857.796875, -4190.5615234375], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": [766055086762299, "decrement"]}, {"id": 618, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1662, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 132, "outputs": [{"links": [1189], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-11027.796875, -3950.************], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["3.5"]}, {"id": 619, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1191, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1192, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 109, "outputs": [{"links": [1186], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [-11087.796875, -3700.************], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["1340", "1785", 1]}, {"id": 620, "flags": {}, "inputs": [{"link": 1604, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1194, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1195, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 110, "outputs": [{"links": [1187, 1188], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-11077.796875, -3520.************], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 621, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [{"links": [1196, 1197, 1627], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-11947.796875, -3770.************], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [426.6739196777344, 307.13214111328125], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${pictureMattingPrompt}${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 2048, "fixed"]}, {"id": 622, "flags": {}, "inputs": [{"link": 1196, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 70, "outputs": [{"links": [1179], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [-11437.796875, -3870.************], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 476], "type": "ShowText|pysssss", "widgets_values": [["(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),"], "(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "$backTags$extTags$styleLoraConfig.posture$styleLoraConfig.lens$styleLoraConfig.style$FACE.tags$FACE.extInfo.expression$FACE.extInfo.hairstyle$pictureMattingPrompt", "(linrun2111:1.3),front view,whole body,The model is wearing A white t-shirt. The model is wearing  pink leggings. The model is wearing  colorful socks.wearing a beaded necklace.wearing earrings.wearing a bracelet.wearing sunglasses on head. The model is wearing green sneakers. The  green sneakers feature solid color and simple. \n\n\nlinrun2010,a model poses on a seaside promenade with a backdrop of the ocean and distant mountains.posing confidently.standing with legs slightly apart, left hand on hip, right hand raised above head.\n\n\nA French model,(the  model with a slim, tall figure, with long legs and a slender waist.), (the model has Radiant Smile with open smile that lights up the face),(the model has  a relaxed and chic hairstyle with light blond hair featuring soft, beachy waves. The hair is parted slightly to the side, allowing the waves to frame the face naturally),", "linrun2010,a model stands on a city street with buildings and power lines in the background.standing casually.standing with one hand relaxed by their side and the other slightly bent.front view,upper body,A 23 year old Asian male model, clean and handsome,The model sported short mid-part brown hair that was trendy and cool, A beige and black striped shirt. It has a polo collar and short sleeves. The shirt is hips length with a button-down front. Features a small chest graphic and solid fabric."]}, {"id": 623, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [1234, 1241, 1659], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [-11427.796875, -3130.************], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": [2100, "fixed"]}, {"id": 624, "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [1233, 1240, 1658], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [-11447.796875, -3320.************], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": [1610, "fixed"]}, {"id": 625, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [1198], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-11957.796875, -3380.************], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [411.6590881347656, 124], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 626, "flags": {}, "inputs": [{"link": 1197, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 71, "outputs": [{"links": [1154], "name": "", "type": "STRING"}], "pos": [-10237.796875, -3470.************], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 627, "flags": {}, "inputs": [{"link": 1198, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 74, "outputs": [{"links": [1156], "name": "", "type": "STRING"}], "pos": [-10277.796875, -3340.************], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 628, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1199, "name": "model", "type": "MODEL"}, {"link": 1200, "name": "clip", "type": "CLIP"}], "mode": 4, "order": 99, "outputs": [{"links": [1604], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [1178, 1210, 1214, 1626, 1629], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [-11208.8623046875, -4189.42138671875], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [255.225341796875, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["online_product/紫葡萄皮肤衣测试_7648_20250301_141220/紫葡萄皮肤衣测试_7648_20250301_141220-flux/紫葡萄皮肤衣测试_7648_20250301_141220-flux.safetensors", "1", 1]}, {"id": 629, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1609, "name": "model", "type": "MODEL"}, {"link": 1575, "name": "clip", "type": "CLIP"}], "mode": "${isUseLoraFace?then(0,4)}", "order": 69, "outputs": [{"links": [1576, 1620], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [1577], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [-11757.796875, -4250.5615234375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [508.2063293457031, 169.1739501953125], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", 0.8, "1"]}, {"id": 630, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 1576, "name": "model", "type": "MODEL"}, {"link": 1577, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 92, "outputs": [{"links": [1199], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [1200], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [-11757.796875, -4010.************], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [512.595703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${SCENE.extInfo.loraPath}", 1, 1]}, {"id": 631, "flags": {}, "inputs": [{"link": 1613, "name": "image", "type": "IMAGE"}, {"link": 1206, "name": "detailer_pipe", "type": "DETAILER_PIPE"}, {"name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC"}], "mode": "${isUseLoraFace?then(0,4)}", "order": 138, "outputs": [{"links": [1157, 1611], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE"}, {"links": [], "name": "mask", "shape": 3, "slot_index": 3, "type": "MASK"}, {"name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": [-8720, -4210], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "size": [346, 782], "type": "FaceDetailerPipe", "widgets_values": [512, true, 1024, 516268210931281, "fixed", 10, 2.5, "euler", "beta", 0.55, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 632, "flags": {}, "inputs": [{"link": 1207, "name": "basic_pipe", "type": "BASIC_PIPE"}, {"link": 1208, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}, {"name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK"}], "mode": "${isUseLoraFace?then(0,4)}", "order": 122, "outputs": [{"links": [1206], "name": "detailer_pipe", "shape": 3, "slot_index": 0, "type": "DETAILER_PIPE"}], "pos": [-9040, -4200], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "size": [262, 204.*************], "type": "BasicPipeToDetailerPipe", "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 633, "flags": {}, "inputs": [{"link": 1620, "name": "model", "type": "MODEL"}, {"link": 1210, "name": "clip", "type": "CLIP"}, {"link": 1211, "name": "vae", "type": "VAE"}, {"link": 1212, "name": "positive", "type": "CONDITIONING"}, {"link": 1213, "name": "negative", "type": "CONDITIONING"}], "mode": "${isUseLoraFace?then(0,4)}", "order": 117, "outputs": [{"links": [1207], "name": "basic_pipe", "shape": 3, "slot_index": 0, "type": "BASIC_PIPE"}], "pos": [-9340, -4200], "properties": {"Node name for S&R": "ToBasicPipe"}, "size": [241.79998779296875, 106], "type": "ToBasicPipe", "widgets_values": []}, {"id": 634, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1214, "name": "clip", "type": "CLIP"}, {"link": 1599, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isUseLoraFace?then(0,4)}", "order": 106, "outputs": [{"links": [1212], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9630, -4150], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 116.85224914550781], "title": "修脸prompt", "type": "CLIPTextEncode", "widgets_values": ["A French model,clear face, blue eyes,happy expression,looking at viewer, realistic, A young woman with soft, voluminous blonde hair and bright blue eyes. She has a radiant complexion, a subtle smile, and a friendly expression,blue eyes,"]}, {"id": 635, "flags": {}, "inputs": [], "mode": "${isUseLoraFace?then(0,4)}", "order": 30, "outputs": [{"links": [1208], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "shape": 3, "type": "SEGM_DETECTOR"}], "pos": [-9360, -3590], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [226.8000030517578, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 636, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${isUseLoraFace?then(0,4)}", "order": 31, "outputs": [{"links": [1213], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9660, -3690], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "size": [210, 26], "type": "ImpactNegativeConditioningPlaceholder", "widgets_values": []}, {"id": 637, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 32, "outputs": [], "pos": [-9360, -4020], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"]}, {"id": 639, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 33, "outputs": [], "pos": [-8150, -3140], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 640, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 34, "outputs": [], "pos": [-7690, -3170], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 641, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 4, "order": 35, "outputs": [], "pos": [-7010, -3840], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"]}, {"id": 642, "flags": {}, "inputs": [{"link": 1216, "name": "source", "type": "IMAGE"}, {"link": 1217, "name": "destination", "type": "IMAGE"}, {"link": 1218, "name": "mask", "shape": 7, "type": "MASK"}], "mode": 4, "order": 148, "outputs": [{"links": [1174, 1176], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5680, -3580], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 643, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 36, "outputs": [], "pos": [-8160, -2710], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 644, "flags": {}, "inputs": [{"link": 1219, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 1220, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 1221, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 102, "outputs": [], "pos": [-4830, -2810], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 645, "flags": {}, "inputs": [], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 55, "outputs": [{"label": "BBOX_DETECTOR", "links": [1231], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [-9620, -2480], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 646, "flags": {}, "inputs": [], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 56, "outputs": [{"label": "SAM_MODEL", "links": [1232], "name": "SAM_MODEL", "shape": 3, "slot_index": 0, "type": "SAM_MODEL"}], "pos": [-9580, -2320], "properties": {"Node name for S&R": "SAMLoader"}, "size": [315, 82], "type": "SAMLoader", "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 647, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"label": "clip", "link": 1222, "name": "clip", "type": "CLIP"}, {"link": 1600, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 82, "outputs": [{"label": "CONDITIONING", "links": [1229], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9640, -3000], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [389.95330810546875, 157.71157836914062], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["", "none", "A1111"]}, {"id": 648, "flags": {}, "inputs": [{"label": "clip", "link": 1224, "name": "clip", "type": "CLIP"}], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 83, "outputs": [{"label": "CONDITIONING", "links": [1230], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9620, -2750], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": [400, 200], "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 649, "flags": {}, "inputs": [{"label": "image", "link": 1225, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 1226, "name": "model", "type": "MODEL"}, {"label": "clip", "link": 1227, "name": "clip", "type": "CLIP"}, {"label": "vae", "link": 1228, "name": "vae", "type": "VAE"}, {"label": "positive", "link": 1229, "name": "positive", "type": "CONDITIONING"}, {"label": "negative", "link": 1230, "name": "negative", "slot_index": 5, "type": "CONDITIONING"}, {"label": "bbox_detector", "link": 1231, "name": "bbox_detector", "slot_index": 6, "type": "BBOX_DETECTOR"}, {"label": "sam_model_opt", "link": 1232, "name": "sam_model_opt", "shape": 7, "slot_index": 7, "type": "SAM_MODEL"}, {"label": "segm_detector_opt", "name": "segm_detector_opt", "shape": 7, "slot_index": 8, "type": "SEGM_DETECTOR"}, {"label": "detailer_hook", "name": "detailer_hook", "shape": 7, "type": "DETAILER_HOOK"}, {"name": "scheduler_func_opt", "shape": 7, "type": "SCHEDULER_FUNC"}], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 136, "outputs": [{"label": "image", "links": [], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "cropped_refined", "links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"label": "cropped_enhanced_alpha", "links": [], "name": "cropped_enhanced_alpha", "shape": 6, "slot_index": 2, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 3, "type": "MASK"}, {"label": "detailer_pipe", "name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"label": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": [-8880, -3180], "properties": {"Node name for S&R": "FaceDetailer"}, "size": [350.5302734375, 902.3991088867188], "type": "FaceDetailer", "widgets_values": [384, true, 512, 889757844059893, "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 650, "flags": {}, "inputs": [], "mode": "${(!isUseLoraFace)?then(0,4)}", "order": 54, "outputs": [{"label": "MODEL", "links": [1226], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "CLIP", "links": [1222, 1224, 1227], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}, {"label": "VAE", "links": [1228], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE"}], "pos": [-9650, -3190], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [315, 98], "type": "CheckpointLoaderSimple", "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 651, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 37, "outputs": [{"links": [1599, 1600, 1602], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-10317.796875, -3700.************], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [397.79266357421875, 181.14865112304688], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1858, "randomize"]}, {"id": 652, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 38, "outputs": [], "pos": [-8970, -3920], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"]}, {"id": 653, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 39, "outputs": [], "pos": [-9150, -2540], "properties": {}, "size": [210, 91.33761596679688], "type": "Note", "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"]}, {"id": 654, "flags": {"collapsed": true}, "inputs": [{"link": 1233, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 73, "outputs": [{"links": [1235], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [-11097.796875, -3240.************], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 655, "flags": {"collapsed": true}, "inputs": [{"link": 1234, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 72, "outputs": [{"links": [1237], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [-11077.796875, -3140.************], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 656, "flags": {}, "inputs": [{"link": 1235, "name": "any_a", "type": "*"}], "mode": 0, "order": 94, "outputs": [{"links": [1236], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-10867.796875, -3270.************], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 657, "flags": {}, "inputs": [{"link": 1236, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 101, "outputs": [{"links": [1191, 1194], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-10617.796875, -3270.************], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 658, "flags": {}, "inputs": [{"link": 1237, "name": "any_a", "type": "*"}], "mode": 0, "order": 93, "outputs": [{"links": [1238], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-10797.796875, -3140.************], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 659, "flags": {}, "inputs": [{"link": 1238, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 100, "outputs": [{"links": [1192, 1195], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-10557.796875, -3120.************], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 660, "flags": {}, "inputs": [{"link": 1239, "name": "image", "type": "IMAGE"}, {"link": 1240, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1241, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 151, "outputs": [{"links": [1163, 1256, 1452], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5380, -3190], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 661, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1242, "name": "image", "type": "IMAGE"}], "mode": 4, "order": 142, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [1243], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [1244], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [-5660, -3820], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 662, "flags": {}, "inputs": [{"link": 1243, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 1244, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 4, "order": 145, "outputs": [{"links": [1177], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-5370, -3740], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 665, "flags": {}, "inputs": [{"link": 1452, "name": "", "type": "*"}], "mode": 0, "order": 154, "outputs": [{"links": [1614], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-12392, 403], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 666, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 40, "outputs": [{"links": [1248], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-12468, 580], "properties": {"Node name for S&R": "CR Text"}, "size": [210, 106.75990295410156], "title": "是否使用风格lora生成图", "type": "CR Text", "widgets_values": ["1"]}, {"id": 667, "flags": {}, "inputs": [{"link": 1248, "name": "a", "type": "*"}, {"link": 1249, "name": "b", "type": "*"}], "mode": 0, "order": 76, "outputs": [{"links": [1250], "name": "boolean", "slot_index": 0, "type": "BOOLEAN"}], "pos": [-12026, 774], "properties": {"Node name for S&R": "easy compare"}, "size": [216.3546905517578, 95.56697845458984], "type": "easy compare", "widgets_values": ["a == b"]}, {"id": 668, "flags": {}, "inputs": [], "mode": 0, "order": 41, "outputs": [{"links": [1249], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-12461, 845], "properties": {"Node name for S&R": "CR Text"}, "size": [210, 110.8138198852539], "type": "CR Text", "widgets_values": ["0"]}, {"id": 669, "flags": {}, "inputs": [{"link": 1563, "name": "on_true", "type": "*"}, {"link": 1265, "name": "on_false", "type": "*"}, {"link": 1250, "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}}], "mode": 0, "order": 156, "outputs": [{"links": [1259], "name": "*", "slot_index": 0, "type": "*"}], "pos": [-11663, 339], "properties": {"Node name for S&R": "easy ifElse"}, "size": [210, 80.45691680908203], "type": "easy ifElse", "widgets_values": [false]}, {"id": 670, "flags": {}, "inputs": [{"link": 1256, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 153, "outputs": [], "pos": [-5090, -3080], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 672, "flags": {}, "inputs": [{"link": 1262, "name": "anything", "shape": 7, "type": "*"}], "mode": 0, "order": 249, "outputs": [], "pos": [-2964.4384765625, 2021.940673828125], "properties": {"Node name for S&R": "easy showAnything"}, "size": [210, 76], "type": "easy showAnything", "widgets_values": ["4"]}, {"id": 673, "flags": {"collapsed": false}, "inputs": [{"link": 1614, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1564, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 155, "outputs": [{"links": [1265], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-12093, 371], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [327.3860778808594, 248.97320556640625], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 764, "flags": {}, "inputs": [{"link": 1425, "name": "image", "type": "IMAGE"}, {"link": 1426, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1427, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1419, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1420, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 211, "outputs": [{"links": [1428], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5062.6416015625, 426.25982666015625], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 114], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 765, "flags": {"collapsed": true}, "inputs": [{"link": 1424, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 209, "outputs": [{"links": [1419, 1421], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5436.6416015625, 726.2598876953125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [385.54595947265625, 187.53152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "flags": {"collapsed": true}, "inputs": [{"link": 1423, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 208, "outputs": [{"links": [1420, 1422], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5428.6416015625, 613.2598876953125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [385.54595947265625, 187.53152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "flags": {}, "inputs": [{"link": 1429, "name": "image", "type": "IMAGE"}, {"link": 1430, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1431, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1421, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1422, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 212, "outputs": [{"links": [1432], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5100.6416015625, 867.2598876953125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 114], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 768, "flags": {}, "inputs": [{"link": 1433, "name": "", "type": "*"}], "mode": 0, "order": 238, "outputs": [{"links": [1435], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-3580.128173828125, 1965.8056640625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 769, "flags": {}, "inputs": [{"link": 1434, "name": "", "type": "*"}], "mode": 0, "order": 244, "outputs": [{"links": [1436], "name": "", "slot_index": 0, "type": "MASK"}], "pos": [-3670, 2148], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 773, "flags": {}, "inputs": [{"link": 1615, "name": "", "type": "*"}], "mode": 0, "order": 120, "outputs": [{"links": [1444], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-8720, 1622], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 774, "flags": {}, "inputs": [{"link": 1449, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 188, "outputs": [{"links": [1446], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7860.6416015625, 773.2598876953125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "flags": {}, "inputs": [{"link": 1450, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 189, "outputs": [{"links": [1451], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7853.6416015625, 1077.259521484375], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "flags": {}, "inputs": [{"link": 1448, "name": "", "type": "*"}], "mode": 0, "order": 187, "outputs": [{"links": [1449, 1450], "name": "", "slot_index": 0, "type": "IMAGE_BOUNDS"}], "pos": [-7837.6416015625, 614.2598876953125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 777, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 42, "outputs": [], "pos": [-9406.40234375, -212.285400390625], "properties": {}, "size": [210, 111.45333862304688], "title": "抠图词", "type": "Note", "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"]}, {"id": 779, "flags": {}, "inputs": [], "mode": 0, "order": 43, "outputs": [{"links": [1455, 1460, 1468, 1469], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7522, 1666.17578125], "properties": {"Node name for S&R": "CR Seed"}, "size": [315, 102], "type": "CR Seed", "widgets_values": [0, "fixed"]}, {"id": 780, "flags": {}, "inputs": [{"link": 1455, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 77, "outputs": [{"links": [1456, 1457, 1458, 1459], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-7045, 1988.17578125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 781, "flags": {}, "inputs": [{"link": 1460, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 78, "outputs": [{"links": [1461, 1462, 1463, 1464], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-7010.5673828125, 2188.185546875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 784, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 44, "outputs": [], "pos": [-7697.6416015625, 520.2598266601562], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"]}, {"id": 787, "flags": {}, "inputs": [{"link": 1473, "name": "", "type": "*"}], "mode": 0, "order": 91, "outputs": [{"links": [], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-8800.865234375, 1351.52978515625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 790, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 45, "outputs": [], "pos": [-6490, -2710], "properties": {}, "size": [241.35919189453125, 58], "type": "Note", "widgets_values": ["保存风格lora出图"]}, {"id": 793, "flags": {}, "inputs": [{"link": 1496, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1500, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 245, "outputs": [{"links": [1497], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-3234, 2188], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [336.5685119628906, 190.69573974609375], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 794, "flags": {}, "inputs": [{"link": 1498, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 251, "outputs": [{"links": [1499], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [-2561, 1732], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "size": [227.2136688232422, 39.664772033691406], "type": "ImpactImageBatchToImageList", "widgets_values": []}, {"id": 800, "flags": {}, "inputs": [{"link": 1591, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 170, "outputs": [{"links": [1512], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-8613.943359375, -683.7633056640625], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "size": [315, 106], "type": "DensePosePreprocessor", "widgets_values": ["densepose_r50_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 801, "flags": {}, "inputs": [{"link": 1512, "name": "image", "type": "IMAGE"}, {"link": 1514, "name": "color_list", "type": "STRING", "widget": {"name": "color_list"}}], "mode": 0, "order": 173, "outputs": [{"links": [1518], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-8217.4580078125, -668.181640625], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "size": [228.88619995117188, 124.55016326904297], "type": "ConrainMaskFromColors", "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 803, "flags": {"collapsed": true}, "inputs": [{"link": 1513, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 61, "outputs": [{"links": [1514], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9155.13671875, -610.7435913085938], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [372.4948425292969, 392.6221008300781], "title": "densepose提取mask区域", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 808, "flags": {"collapsed": true}, "inputs": [{"link": 1536, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 62, "outputs": [{"links": [1543], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9426.40234375, -17.285396575927734], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [300.1857604980469, 284.6385192871094], "title": "需要排除的mask", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 810, "flags": {}, "inputs": [{"link": 1518, "name": "mask", "type": "MASK"}], "mode": 0, "order": 175, "outputs": [{"links": [1527], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7843.46240234375, -661.1817016601562], "properties": {"Node name for S&R": "GrowMask"}, "size": [210, 95.39423370361328], "type": "GrowMask", "widgets_values": [0, false]}, {"id": 811, "flags": {}, "inputs": [{"link": 1622, "name": "masks_a", "type": "MASK"}, {"link": 1623, "name": "masks_b", "type": "MASK"}], "mode": 4, "order": 179, "outputs": [{"links": [1550], "name": "MASKS", "slot_index": 0, "type": "MASK"}], "pos": [-6810.185546875, -508.7435302734375], "properties": {"Node name for S&R": "Masks Add"}, "size": [210, 46], "type": "Masks Add", "widgets_values": []}, {"id": 812, "flags": {}, "inputs": [{"link": 1522, "name": "mask", "type": "MASK"}], "mode": 0, "order": 172, "outputs": [{"links": [1622], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7853.40771484375, -335.28546142578125], "properties": {"Node name for S&R": "GrowMask"}, "size": [210, 93.73921966552734], "type": "GrowMask", "widgets_values": [0, false]}, {"id": 814, "flags": {}, "inputs": [{"link": 1527, "name": "mask", "type": "MASK"}, {"link": 1534, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1533, "name": "width", "type": "INT", "widget": {"name": "width"}}], "mode": 0, "order": 177, "outputs": [{"links": [1623], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7547.40771484375, -645.2855834960938], "properties": {"Node name for S&R": "JWMaskResize"}, "size": [259.6524353027344, 135.95858764648438], "type": "JWMaskResize", "widgets_values": [512, 512, "bilinear"]}, {"id": 815, "flags": {}, "inputs": [{"link": 1528, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 171, "outputs": [{"links": [1533, 1540], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1534, 1541], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-7865.46240234375, -483.18170166015625], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 818, "flags": {}, "inputs": [{"link": 1540, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1541, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 174, "outputs": [{"links": [1542], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-7199.40771484375, -660.2855834960938], "properties": {"Node name for S&R": "Image Blank"}, "size": [315, 154], "type": "Image Blank", "widgets_values": [512, 512, 255, 255, 255]}, {"id": 819, "flags": {}, "inputs": [{"link": 1542, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 176, "outputs": [{"links": [1547], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-6824.40771484375, -654.2855834960938], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 68.49308776855469], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 820, "flags": {}, "inputs": [{"link": 1543, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 88, "outputs": [{"links": [1544], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [-8468.4677734375, -514.1475830078125], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [210, 326], "type": "ShowText|pysssss", "widgets_values": [["trousers"], "trousers", "clothing", "trousers", "trousers"]}, {"id": 821, "flags": {}, "inputs": [{"link": 1545, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 63, "outputs": [{"links": [1546], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7197.40771484375, -444.2854309082031], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [304.83380126953125, 204.48739624023438], "title": "需要排除的区域", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 822, "flags": {}, "inputs": [{"link": 1550, "name": "masks_a", "type": "MASK"}, {"link": 1549, "name": "masks_b", "type": "MASK"}], "mode": 4, "order": 180, "outputs": [{"links": [1552, 1572], "name": "MASKS", "slot_index": 0, "type": "MASK"}], "pos": [-6504.185546875, -507.7434997558594], "properties": {"Node name for S&R": "Masks Subtract"}, "size": [210, 46], "type": "Masks Subtract", "widgets_values": []}, {"id": 823, "flags": {}, "inputs": [{"link": 1548, "name": "on_true", "type": "*"}, {"link": 1547, "name": "on_false", "type": "*"}, {"link": 1546, "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}}], "mode": 0, "order": 178, "outputs": [{"links": [1549], "name": "*", "slot_index": 0, "type": "*"}], "pos": [-6517.40771484375, -659.2855834960938], "properties": {"Node name for S&R": "easy ifElse"}, "size": [210, 82.75738525390625], "type": "easy ifElse", "widgets_values": [false]}, {"id": 831, "flags": {}, "inputs": [{"link": 1567, "name": "", "type": "*"}], "mode": 0, "order": 67, "outputs": [{"links": [1563, 1564], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-12361, 269], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 837, "flags": {}, "inputs": [{"link": 1572, "name": "mask", "type": "MASK"}], "mode": 0, "order": 182, "outputs": [{"links": [1581], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6369.185546875, -374.74365234375], "properties": {"Node name for S&R": "MaskToImage"}, "size": [188.457763671875, 48.499874114990234], "type": "MaskToImage", "widgets_values": []}, {"id": 839, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 51, "outputs": [{"label": "STRING", "links": [1579], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [1580], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-6698.2548828125, -258.5939636230469], "properties": {"Node name for S&R": "Text String"}, "size": [300.3023681640625, 216.65859985351562], "title": "保存参看图的mask", "type": "Text String", "widgets_values": ["${outputPath}", "mask_${fileNamePrefix}", "", ""]}, {"id": 840, "flags": {}, "inputs": [{"label": "images", "link": 1581, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 1579, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 1580, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 185, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-6148.185546875, -211.74365234375], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [231.75296020507812, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 841, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 46, "outputs": [], "pos": [-9351.4609375, -707.3843994140625], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"]}, {"id": 843, "flags": {}, "inputs": [{"link": 1585, "name": "", "type": "*"}], "mode": 0, "order": 165, "outputs": [{"name": "", "type": "IMAGE"}], "pos": [-8505.95703125, 499.120849609375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 844, "flags": {}, "inputs": [{"link": 1586, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 166, "outputs": [{"links": [1588], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-8031.57275390625, 26.234153747558594], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [211.82809448242188, 78], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 845, "flags": {}, "inputs": [{"link": 1588, "name": "", "type": "*"}], "mode": 0, "order": 167, "outputs": [{"links": [1589, 1590, 1591], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-7748.40576171875, 63.28346633911133], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 847, "flags": {}, "inputs": [{"link": 1593, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 59, "outputs": [{"links": [1594], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-4108.36572265625, 1693.341796875], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 93.48091125488281], "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 850, "flags": {}, "inputs": [{"link": 1602, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 75, "outputs": [{"name": "STRING", "shape": 6, "type": "STRING"}], "pos": [-9670, -3740], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [315, 76], "type": "ShowText|pysssss", "widgets_values": ["", "$FACE.tags$FACE.extInfo.expression$FACE.extInfo.hairstyle"]}, {"id": 851, "flags": {}, "inputs": [{"link": 1603, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 137, "outputs": [], "pos": [-10097.796875, -4270.5615234375], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 853, "flags": {}, "inputs": [{"link": 1611, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 140, "outputs": [], "pos": [-8220, -4220], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 859, "flags": {}, "inputs": [{"name": "", "type": "*"}], "mode": 0, "order": 47, "outputs": [{"name": "", "type": "*"}], "pos": [-6808.34814453125, -422.577880859375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 860, "flags": {}, "inputs": [{"link": 1624, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 79, "outputs": [{"links": [1633], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [-9890, -5250], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 58], "type": "SetUnionControlNetType", "widgets_values": ["openpose"]}, {"id": 861, "flags": {}, "inputs": [{"link": 1625, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 80, "outputs": [{"links": [1638], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [-9900, -5110], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [210, 58], "type": "SetUnionControlNetType", "widgets_values": ["depth"]}, {"id": 862, "flags": {}, "inputs": [], "mode": 0, "order": 48, "outputs": [{"links": [1624, 1625], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [-10660, -5390], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [577.4558715820312, 58.57819366455078], "type": "ControlNetLoader", "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 863, "flags": {}, "inputs": [{"link": 1626, "name": "clip", "type": "CLIP"}, {"link": 1627, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 107, "outputs": [{"links": [1631], "name": "CONDITIONING", "type": "CONDITIONING"}], "pos": [-9500, -4760], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 57.11636734008789], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 865, "flags": {}, "inputs": [], "mode": 0, "order": 49, "outputs": [{"links": [1634, 1639], "name": "VAE", "slot_index": 0, "type": "VAE"}], "pos": [-9960, -5390], "properties": {"Node name for S&R": "VAELoader"}, "size": [315, 58], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 866, "flags": {}, "inputs": [{"link": 1628, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 129, "outputs": [{"links": [1635], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-9560, -4980], "properties": {"Node name for S&R": "GetImageSizeAndCount"}, "size": [277.20001220703125, 86], "type": "GetImageSizeAndCount", "widgets_values": []}, {"id": 867, "flags": {}, "inputs": [{"link": 1629, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 108, "outputs": [{"links": [1632], "name": "CONDITIONING", "type": "CONDITIONING"}], "pos": [-9500, -4590], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 76], "title": "CLIP Text Encode (Negative Prompt)", "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 868, "flags": {}, "inputs": [{"link": 1663, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 127, "outputs": [{"links": [1640], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9950, -4560], "properties": {"Node name for S&R": "DepthAnythingPreprocessor"}, "size": [315, 82], "type": "DepthAnythingPreprocessor", "widgets_values": ["depth_anything_vits14.pth", 1024]}, {"id": 869, "flags": {}, "inputs": [{"link": 1631, "name": "positive", "type": "CONDITIONING"}, {"link": 1632, "name": "negative", "type": "CONDITIONING"}, {"link": 1633, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1634, "name": "vae", "type": "VAE"}, {"link": 1635, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 130, "outputs": [{"links": [1636], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1637], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [-9230, -5330], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.6, 0, 0.2]}, {"id": 870, "flags": {}, "inputs": [{"link": 1636, "name": "positive", "type": "CONDITIONING"}, {"link": 1637, "name": "negative", "type": "CONDITIONING"}, {"link": 1638, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1639, "name": "vae", "type": "VAE"}, {"link": 1640, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 131, "outputs": [{"links": [1662], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "type": "CONDITIONING"}], "pos": [-8760, -5080], "properties": {"Node name for S&R": "ControlNetApplySD3"}, "size": [315, 186], "type": "ControlNetApplySD3", "widgets_values": [0.4, 0, 0.2]}, {"id": 871, "flags": {}, "inputs": [{"link": 1641, "name": "", "type": "*"}], "mode": 0, "order": 81, "outputs": [{"links": [1648, 1660], "name": "", "type": "IMAGE"}], "pos": [-10880, -5380], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 872, "flags": {}, "inputs": [{"link": 1664, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 128, "outputs": [{"links": [1628], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "pos": [-9960, -4940], "properties": {"Node name for S&R": "DWPreprocessor"}, "size": [315, 198], "type": "DWPreprocessor", "widgets_values": ["enable", "enable", "enable", 1024, "yolox_l.onnx", "dw-ll_ucoco_384.onnx"]}, {"id": 874, "flags": {}, "inputs": [{"link": 1646, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 111, "outputs": [{"links": [1649], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-11020, -4690], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [217.53543090820312, 98], "type": "MathExpressionPlus", "widgets_values": ["a[1][0]"]}, {"id": 875, "flags": {}, "inputs": [{"link": 1665, "name": "destination", "type": "IMAGE"}, {"link": 1648, "name": "source", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}, {"link": 1649, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1650, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 123, "outputs": [{"links": [1661], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-10700, -4650], "properties": {"Node name for S&R": "ImageComposite+"}, "size": [315, 170], "type": "ImageComposite+", "widgets_values": [0, 0, 0, 0]}, {"id": 877, "flags": {}, "inputs": [{"link": 1653, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 112, "outputs": [{"links": [1666], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-11010, -4960], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [217.53543090820312, 98], "type": "MathExpressionPlus", "widgets_values": ["a[0][0]"]}, {"id": 878, "flags": {}, "inputs": [{"link": 1654, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 113, "outputs": [{"links": [1667], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-11010, -4820], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [217.53543090820312, 98], "type": "MathExpressionPlus", "widgets_values": ["a[0][1]"]}, {"id": 879, "flags": {}, "inputs": [{"link": 1655, "name": "a", "shape": 7, "type": "INT,FLOAT"}, {"name": "b", "shape": 7, "type": "INT,FLOAT"}, {"name": "c", "shape": 7, "type": "INT,FLOAT"}], "mode": 0, "order": 114, "outputs": [{"links": [1650], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-11020, -4540], "properties": {"Node name for S&R": "MathExpressionPlus"}, "size": [217.53543090820312, 98], "type": "MathExpressionPlus", "widgets_values": ["a[1][1]"]}, {"id": 880, "flags": {"collapsed": false}, "inputs": [{"link": 1656, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1657, "name": "any_b", "shape": 7, "type": "*"}, {"link": 1658, "name": "any_c", "shape": 7, "type": "*"}, {"link": 1659, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 103, "outputs": [{"links": [1646, 1653, 1654, 1655], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-11470, -4690], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    img_w, img_h = any_a, any_b\n    tgt_w, tgt_h = any_c, any_d\n\t\n    if tgt_w/tgt_h > img_w/img_h:\n        _img_w = int(img_h*tgt_w/tgt_h)\n        _img_h = img_h\n        coordinates = [(_img_w-img_w)//2, 0]\n    else:\n        _img_h = int(img_w*tgt_h/tgt_w)\n        _img_w = img_w\n        coordinates = [0, (_img_h-img_h)//2]\n\n    return ([_img_w, _img_h], coordinates),"]}, {"id": 881, "flags": {}, "inputs": [{"link": 1660, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 96, "outputs": [{"links": [1656], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1657], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-11450, -4870], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 882, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 50, "outputs": [{"links": [1641], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-11450, -5380], "properties": {"Node name for S&R": "LoadImage"}, "size": [300.1754455566406, 314], "type": "LoadImage", "widgets_values": ["${referenceImage}", "image"]}, {"id": 883, "flags": {}, "inputs": [{"link": 1661, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 125, "outputs": [{"links": [1663, 1664], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-10330, -5000], "properties": {"Node name for S&R": "ImageResize+"}, "size": [315, 218], "type": "ImageResize+", "widgets_values": [1024, 1024, "nearest", "keep proportion", "downscale if bigger", 0]}, {"id": 884, "flags": {}, "inputs": [{"link": 1666, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1667, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 119, "outputs": [{"links": [1665], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-10717.1796875, -4920.31494140625], "properties": {"Node name for S&R": "EmptyImage"}, "size": [315, 130], "type": "EmptyImage", "widgets_values": [512, 512, 1, 16777215]}], "seed_widgets": {"275": 0, "401": 0, "617": 0, "621": 1, "623": 0, "624": 0, "625": 1, "631": 3, "649": 3, "651": 1, "779": 0}, "version": 0.4}}}}
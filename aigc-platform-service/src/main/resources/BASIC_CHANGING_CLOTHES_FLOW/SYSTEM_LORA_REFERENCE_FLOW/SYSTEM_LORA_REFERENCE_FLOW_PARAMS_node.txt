client_id : ${clientId}

//===================== tryon部分节点 ============================
"463 :463 : ${referenceImage}
"456 :456 : ${maskImage}
"462 :462 : ${clotheImage}

"433 :433 : ${imageNum}

"159 :159 : ${outputPath}、${fileNamePrefix}

//===================== mask 图层节点节点 ============================
"368 :368 : ${clotheType}
"839 :839 : ${outputPath}、mask_${fileNamePrefix}

//===================== 换脸部分节点 ============================
"621 :621 : ${backTags}${extTags}${styleLoraConfig.posture}${styleLoraConfig.lens}${styleLoraConfig.style}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}${pictureMattingPrompt}
"629 :629 : ${FACE.extInfo.faceLora}
"630 :630 : ${styleLoraConfig.loraPath}
"651 :651 : ${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}
"590 :590 : ${FACE.extInfo.faceImage}
"591 :591 : ${outputPath}、face_${fileNamePrefix}


//===================== 流程切换节点 ============================
630 : "model":[
          "${isUseLoraFace?then(629,612)}",
          0
       ],
       "clip":[
           "${isUseLoraFace?then(629,611)}",
           "${isUseLoraFace?then(1,0)?number}"
       ]



631 : "image":[
      	  "${isUseLoraFace?then(609,649)}",
      	   0
       ],



660 : "image":[
            "${isUseLoraFace?then(631,596)}",
             0
        ],


596 : "input_image":[
            "${isUseLoraFace?then(631,649)}",
             0
        ],
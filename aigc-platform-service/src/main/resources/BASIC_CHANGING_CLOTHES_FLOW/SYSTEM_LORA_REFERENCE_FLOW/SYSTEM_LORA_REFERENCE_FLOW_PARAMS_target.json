{"client_id": "1c9feb0923f3400b906af4b37a95a594", "prompt": {"52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["322", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["764", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存换装结果图片"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["397", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "211": {"inputs": {"channel": "red", "image": ["767", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["346", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": ["774", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": ["775", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 661045452417164, "steps": 20, "cfg": 2.5, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["436", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. ", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["289", 1], "mask": ["289", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"crop": "center", "clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "288": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["793", 0], "inpainted_image": ["794", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "✂️ Inpaint <PERSON>itch"}}, "289": {"inputs": {"context_expand_pixels": 10, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1536, "max_height": 1785, "padding": 32, "image": ["321", 0], "mask": ["56", 0], "optional_context_mask": ["297", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "✂️ Inpaint Crop"}}, "290": {"inputs": {"conditioning": ["847", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["288", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["351", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["397", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["397", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 10, "tapered_corners": false, "mask": ["812", 0]}, "class_type": "GrowMask", "_meta": {"title": "替换区域扩张大小"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["764", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "322": {"inputs": {"expand": 0, "tapered_corners": true, "mask": ["211", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["408", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+b", "a": ["264", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+b", "a": ["265", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["461", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "365": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "366": {"inputs": {"prompt": ["419", 0], "background": "white", "threshold": 0.6, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["844", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "367": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "368": {"inputs": {"text": "${clotheType}"}, "class_type": "CR Text", "_meta": {"title": "抠图词"}}, "369": {"inputs": {"prompt": ["820", 0], "background": "white", "threshold": 0.3, "sam_model": ["365", 0], "grounding_dino_model": ["367", 0], "image": ["844", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["300", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "397": {"inputs": {"upscale_method": "area", "scale_by": ["398", 0], "image": ["463", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "398": {"inputs": {"target_size": ["399", 0], "image": ["463", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "399": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "400": {"inputs": {"image": ["463", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "401": {"inputs": {"seed": 2100}, "class_type": "CR Seed", "_meta": {"title": "最大支持尺寸"}}, "404": {"inputs": {"image": ["462", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "405": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "406": {"inputs": {"target_size": ["405", 0], "image": ["462", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "408": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["462", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "418": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要替换的区域"}}, "419": {"inputs": {"text": ["418", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "433": {"inputs": {"text": "${imageNum}"}, "class_type": "CR Text", "_meta": {"title": "tryon生成图片张数"}}, "434": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]", "any_a": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "435": {"inputs": {"text": ["434", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "436": {"inputs": {"multiply_by": ["435", 0], "latents": ["282", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "456": {"inputs": {"image": "${maskImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "459": {"inputs": {"width": ["404", 0], "height": ["404", 1], "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["456", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "460": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["459", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "461": {"inputs": {"channel": "red", "image": ["460", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "462": {"inputs": {"image": "${clotheImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "463": {"inputs": {"image": "${referenceImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "672": {"inputs": {"text": "4", "anything": ["436", 1]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "764": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["397", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "765": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "766": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "767": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["194", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "774": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "775": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "779": {"inputs": {"seed": 0}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "793": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b", "any_a": ["289", 0], "any_b": ["435", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "794": {"inputs": {"image": ["276", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "800": {"inputs": {"model": "densepose_r101_fpn_dl.torchscript", "cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "resolution": 512, "image": ["844", 0]}, "class_type": "DensePosePreprocessor", "_meta": {"title": "DensePose Estimator"}}, "801": {"inputs": {"color_list": ["803", 0], "threshold": 2, "image": ["800", 0]}, "class_type": "ConrainMaskFromColors", "_meta": {"title": "Conrain Mask From Colors"}}, "803": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "densepose提取mask区域"}}, "808": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的mask"}}, "810": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["801", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "812": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["366", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "814": {"inputs": {"height": ["815", 1], "width": ["815", 0], "interpolation_mode": "bilinear", "mask": ["810", 0]}, "class_type": "JWMaskResize", "_meta": {"title": "Mask Resize"}}, "815": {"inputs": {"image": ["366", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "818": {"inputs": {"width": ["815", 0], "height": ["815", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "819": {"inputs": {"channel": "red", "image": ["818", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "820": {"inputs": {"text": ["808", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "821": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]", "any_a": ["368", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的区域"}}, "823": {"inputs": {"boolean": ["821", 0], "on_true": ["369", 1], "on_false": ["819", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "837": {"inputs": {"mask": ["812", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "839": {"inputs": {"text": "${outputPath}", "text_b": "mask_${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存参看图的mask"}}, "840": {"inputs": {"output_path": ["839", 0], "filename_prefix": ["839", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["837", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "844": {"inputs": {"torchscript_jit": "default", "image": ["397", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "847": {"inputs": {"text": "", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 884, "last_link_id": 1694, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3768.537109375, 815.3216552734375], "size": [261.10693359375, 178], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 820}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2910.48828125, 882.318603515625], "size": [264.5999755859375, 26], "flags": {}, "order": 128, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2914.48828125, 1064.301513671875], "size": [264.5999755859375, 26], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 56, "type": "ImageToMask", "pos": [-2015, 901], "size": [210, 59.905555725097656], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1434], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 60, "type": "Get Image Size", "pos": [-3979.537109375, 1269.284423828125], "size": [298.42425537109375, 46], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 821}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 61, "type": "Get Image Size", "pos": [-3224, 2098], "size": [210, 46], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 113, "type": "Reroute", "pos": [-4104.23046875, 671.5852661132812], "size": [75, 26], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 819}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 159, "type": "Text String", "pos": [-2256, 2284], "size": [228.5572052001953, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [873], "slot_index": 0, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "links": [874], "slot_index": 1, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "title": "保存换装结果图片", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 167, "type": "Reroute", "pos": [-4471.634765625, 789.323486328125], "size": [75, 26], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 441}], "outputs": [{"name": "", "type": "IMAGE", "links": [820, 821, 822, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-6783.6259765625, 303.************], "size": [248.14456176757812, 150], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292, 1448], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5825.6416015625, 656.2598876953125], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 94, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [544, 1423, 1424], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "type": "MaskToImage", "pos": [-5858.185546875, 987.3473510742188], "size": [264.5999755859375, 26], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1429], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 208, "type": "Reroute", "pos": [-4375.634765625, 471.330810546875], "size": [75, 26], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1428}], "outputs": [{"name": "", "type": "IMAGE", "links": [819], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-5035.185546875, 1110.34619140625], "size": [210, 84.43663024902344], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1432}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-6862, 1899.17578125], "size": [243.56057739257812, 150], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 1465}, {"name": "padding_left", "type": "INT", "link": 1456, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1457, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1458, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1459, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5039.65869140625, 1842.2310791015625], "size": [210, 138], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "label": "mask", "shape": 7}, {"name": "x", "type": "INT", "link": 420, "label": "x", "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 421, "label": "y", "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441], "slot_index": 0, "label": "IMAGE", "shape": 3}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 245, "type": "EmptyImage", "pos": [-5749.5673828125, 1642.17578125], "size": [243.3533935546875, 102], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "label": "width", "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 759, "label": "height", "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "label": "IMAGE", "shape": 3}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 246, "type": "CR Seed", "pos": [-7410.6416015625, 779.2598876953125], "size": [281.7162780761719, 102], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1446, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 247, "type": "CR Seed", "pos": [-7388.6416015625, 1075.259521484375], "size": [278.3121032714844, 102], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1451, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SimpleMath+", "pos": [-5389.65869140625, 1847.23095703125], "size": [220.35072326660156, 98], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5389.65869140625, 1648.231689453125], "size": [210, 112.43743896484375], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6450.5673828125, 1647.17578125], "size": [282.12066650390625, 195.71939086914062], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "type": "ImageScaleBy", "pos": [-6106.5673828125, 1903.17578125], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6218.6416015625, 596.2598876953125], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "type": "JWIntegerMin", "pos": [-6958.185546875, 694.3478393554688], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 259, "type": "JWIntegerMin", "pos": [-6964.185546875, 1044.346923828125], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6605.185546875, 670.3475952148438], "size": [210, 71.68185424804688], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755, 1426, 1430], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 265, "type": "JWIntegerMax", "pos": [-6647.185546875, 961.34765625], "size": [210, 71.68185424804688], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 756, 1427, 1431], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-6955.6416015625, 830.2598876953125], "size": [210, 67.93143463134766], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 269, "type": "Note", "pos": [-6592.185546875, 828.347900390625], "size": [210, 67.93143463134766], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2921, 1656], "size": [234.29580688476562, 262], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 850, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [661045452417164, "fixed", 20, 2.5, "euler", "simple", 1]}, {"id": 276, "type": "VAEDecode", "pos": [-2651, 1594], "size": [210, 46], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1498], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4136.73095703125, 1819.626220703125], "size": [269.3892822265625, 89.79380798339844], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "], "color": "#222", "bgcolor": "#000"}, {"id": 278, "type": "FluxGuidance", "pos": [-3830.************, 1833.23193359375], "size": [210, 58], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "label": "条件", "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 279, "type": "UNETLoader", "pos": [-4035, 1583], "size": [326.5174865722656, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "type": "VAELoader", "pos": [-4452.8623046875, 1858.************], "size": [300, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4479.83203125, 1664.2646484375], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499, 1593], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3258, 1766], "size": [210, 138], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1437, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1438, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [848], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3256.462890625, 1645.7598876953125], "size": [184.8000030517578, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4473.435546875, 2063.94091796875], "size": [370, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3917.685302734375, 1980.57470703125], "size": [210, 78], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 822, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 286, "type": "StyleModelApply", "pos": [-3552.980712890625, 1847.6009521484375], "size": [210, 122], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "label": "CLIP视觉输出", "shape": 7}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 287, "type": "StyleModelLoader", "pos": [-4462.771484375, 1981.87158203125], "size": [340, 60], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "type": "InpaintStitch", "pos": [-2232, 1585], "size": [256.60272216796875, 78], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "stitch", "type": "STITCH", "link": 1497, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 1499, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1440], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 289, "type": "InpaintCrop", "pos": [-3546, 2132], "size": [245.64613342285156, 386], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1435, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1436, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "label": "上下文遮罩(可选)", "shape": 7}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [1496], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [1437], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [1438], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3840.737548828125, 1724.7645263671875], "size": [317.4000244140625, 26], "flags": {"collapsed": false}, "order": 36, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1594, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 291, "type": "ImageCrop", "pos": [-2483, 1925], "size": [210, 118], "flags": {}, "order": 145, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1440}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3582, 1611], "size": [271.6474304199219, 86.10514068603516], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 297, "type": "ImageToMask", "pos": [-2061, 614], "size": [210, 59.905555725097656], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 300, "type": "MaskToImage", "pos": [-6910, 2387.185546875], "size": [176.39999389648438, 26], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1466}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2961.54296875, 2145.298828125], "size": [75, 26], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 305, "type": "Reroute", "pos": [-2614.54296875, 2141.298828125], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2227, 2015], "size": [239.650634765625, 122], "flags": {}, "order": 146, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555, 879], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 308, "type": "ConrainImageSave", "pos": [-1859, 2255], "size": [231.75296020507812, 266], "flags": {}, "order": 147, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 873, "label": "output_path", "widget": {"name": "output_path"}}, {"name": "filename_prefix", "type": "STRING", "link": 874, "label": "filename_prefix", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "label": "image_cnt", "shape": 3}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 314, "type": "Get Image Size", "pos": [-7221.40380859375, 327.5995788574219], "size": [210, 46], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 315, "type": "GrowMask", "pos": [-7805, 381], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1552}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "title": "替换区域扩张大小", "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [10, false], "color": "#232", "bgcolor": "#353"}, {"id": 316, "type": "Get Image Size", "pos": [-6240.74169921875, 312.345947265625], "size": [210, 46], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 319, "type": "GetImageSize+", "pos": [-6619.5673828125, 1719.17578125], "size": [144.6750030517578, 71.8825912475586], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 320, "type": "GetImageSize+", "pos": [-5706.65869140625, 1981.8992919921875], "size": [214.20001220703125, 66], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 321, "type": "easy imageConcat", "pos": [-3834.8525390625, 403.8241271972656], "size": [315, 102], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 823}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [589, 1433], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 322, "type": "GrowMask", "pos": [-3365.26123046875, 981.890625], "size": [315, 82], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 591}], "outputs": [{"name": "MASK", "type": "MASK", "links": [592], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, true]}, {"id": 323, "type": "easy imageConcat", "pos": [-2429.48828125, 610.3302001953125], "size": [315, 102], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 324, "type": "easy imageConcat", "pos": [-2486, 925], "size": [315, 102], "flags": {"collapsed": false}, "order": 132, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 328, "type": "Reroute", "pos": [-8747.9287109375, 437.60040283203125], "size": [75, 26], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 610, 1425, 1585, 1586], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-7862.5673828125, 1620.17578125], "size": [75, 26], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1510}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7434, 1899.17578125], "size": [243.56057739257812, 150], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1444}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 347, "type": "SimpleMath+", "pos": [-7797, 1927.17578125], "size": [210, 98], "flags": {"collapsed": false}, "order": 88, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "link": 1468, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 348, "type": "SimpleMath+", "pos": [-7785.5673828125, 2150.185546875], "size": [210, 98], "flags": {"collapsed": false}, "order": 92, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "link": 1469, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 349, "type": "MaskToImage", "pos": [-7774, 2329.185546875], "size": [176.39999389648438, 26], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7423, 2207.185546875], "size": [243.56057739257812, 150], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 351, "type": "ImageToMask", "pos": [-7171.5673828125, 2274.185546875], "size": [210, 83.63514709472656], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1465, 1466, 1467], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 362, "type": "SimpleMath+", "pos": [-4408.435546875, 2163.94091796875], "size": [210, 98], "flags": {"collapsed": false}, "order": 89, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 365, "type": "Conrain_SAMModelLoader", "pos": [-8832.4033203125, -166.285400390625], "size": [269.19927978515625, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [709, 712], "slot_index": 0, "label": "SAM_MODEL", "shape": 3}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 367, "type": "Conrain_GroundingDinoModelLoader", "pos": [-8843.6650390625, -305.6015625], "size": [285.80181884765625, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [710, 713], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 368, "type": "CR Text", "pos": [-9829.40234375, -398.2855529785156], "size": [217.36741638183594, 128.27645874023438], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [834, 1513, 1536, 1545], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "抠图词", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${clotheType}"], "color": "#232", "bgcolor": "#353"}, {"id": 384, "type": "ImageScaleBy", "pos": [-6080.5673828125, 2347.185546875], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5063.65869140625, 2265.23193359375], "size": [210, 138], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "label": "mask", "shape": 7}, {"name": "x", "type": "INT", "link": 771, "label": "x", "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 770, "label": "y", "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "label": "IMAGE", "shape": 3}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 386, "type": "EmptyImage", "pos": [-5709.65869140625, 2175.23193359375], "size": [243.3533935546875, 102], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "label": "width", "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 762, "label": "height", "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "label": "IMAGE", "shape": 3}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "Reroute", "pos": [-6058.65869140625, 1615.2320556640625], "size": [75, 26], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 391, "type": "Reroute", "pos": [-6041.65869140625, 1705.2315673828125], "size": [75, 26], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-6673, 2271.185546875], "size": [243.56057739257812, 150], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 1467}, {"name": "padding_left", "type": "INT", "link": 1461, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1462, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1463, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1464, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 397, "type": "ImageScaleBy", "pos": [-9206.650390625, 441.1514892578125], "size": [217.8218994140625, 125.52959442138672], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 806}, {"name": "scale_by", "type": "FLOAT", "link": 798, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 398, "type": "UpscaleSizeCalculator", "pos": [-9679.650390625, 400.1514892578125], "size": [220, 118], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 805}, {"name": "target_size", "type": "INT", "link": 797, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [798], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 399, "type": "ConrainPythonExecutor", "pos": [-10234.650390625, 518.1514892578125], "size": [365.79345703125, 195.28152465820312], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 800, "shape": 7}, {"name": "any_b", "type": "*", "link": 801, "shape": 7}, {"name": "any_c", "type": "*", "link": 802, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [797], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "type": "Get Image Size", "pos": [-10581.650390625, 558.1514892578125], "size": [210, 46], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 804}], "outputs": [{"name": "width", "type": "INT", "links": [800], "slot_index": 0}, {"name": "height", "type": "INT", "links": [801], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 401, "type": "CR Seed", "pos": [-11020.650390625, 846.1514892578125], "size": [270.7088317871094, 109.29169464111328], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [802, 811], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "最大支持尺寸", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [2100, "fixed"]}, {"id": 402, "type": "Reroute", "pos": [-10954.650390625, 418.1514892578125], "size": [75, 26], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1693}], "outputs": [{"name": "", "type": "IMAGE", "links": [804, 805, 806], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 404, "type": "Get Image Size", "pos": [-11017.650390625, 1177.1514892578125], "size": [210, 46], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 827}], "outputs": [{"name": "width", "type": "INT", "links": [809, 895], "slot_index": 0}, {"name": "height", "type": "INT", "links": [810, 896], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 405, "type": "ConrainPythonExecutor", "pos": [-10711.515625, 1398.6812744140625], "size": [353.50982666015625, 168.5362548828125], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 809, "shape": 7}, {"name": "any_b", "type": "*", "link": 810, "shape": 7}, {"name": "any_c", "type": "*", "link": 811, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "type": "UpscaleSizeCalculator", "pos": [-10250.515625, 1400.6812744140625], "size": [220, 102.22442626953125], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 828}, {"name": "target_size", "type": "INT", "link": 812, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [817, 897], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 407, "type": "Reroute", "pos": [-11098.515625, 1625.6812744140625], "size": [75, 26], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1257}], "outputs": [{"name": "", "type": "IMAGE", "links": [827, 828, 829], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScaleBy", "pos": [-9743.650390625, 1573.1514892578125], "size": [210, 78], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 829}, {"name": "scale_by", "type": "FLOAT", "link": 817, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1615], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 433, "type": "CR Text", "pos": [-4494, 2341], "size": [211.76846313476562, 168.80604553222656], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [846], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "tryon生成图片张数", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["${imageNum}"], "color": "#232", "bgcolor": "#353"}, {"id": 434, "type": "ConrainPythonExecutor", "pos": [-4173.435546875, 2273.9404296875], "size": [255.5079803466797, 218.5600128173828], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 846, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [847], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "type": "JWStringToInteger", "pos": [-3869, 2279], "size": [210, 55.71077346801758], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 847, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [849, 1500], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 436, "type": "VHS_DuplicateLatents", "pos": [-3320, 1941], "size": [260.3999938964844, 58.512535095214844], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 848}, {"name": "multiply_by", "type": "INT", "link": 849, "widget": {"name": "multiply_by"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [850], "slot_index": 0}, {"name": "count", "type": "INT", "links": [1262], "slot_index": 1}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 451, "type": "PreviewImage", "pos": [-1892, 1739], "size": [210, 246], "flags": {}, "order": 148, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 879}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 456, "type": "LoadImage", "pos": [-11795.619140625, 713.8290405273438], "size": [315, 314], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [880], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${maskImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 457, "type": "Reroute", "pos": [-11051.0908203125, 1037.9932861328125], "size": [75, 26], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 880}], "outputs": [{"name": "", "type": "IMAGE", "links": [885], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 459, "type": "ImageResize+", "pos": [-10249.515625, 1023.6817626953125], "size": [315, 218], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 885}, {"name": "width", "type": "INT", "link": 895, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 896, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [889], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "type": "ImageScaleBy", "pos": [-9741.650390625, 1042.1514892578125], "size": [214.26881408691406, 94.98396301269531], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 889}, {"name": "scale_by", "type": "FLOAT", "link": 897, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 461, "type": "ImageToMask", "pos": [-8799.515625, 1394.6812744140625], "size": [210, 74.41427612304688], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 890}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1510], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 462, "type": "LoadImage", "pos": [-11800.619140625, 1126.829345703125], "size": [315, 314], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1257], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${clotheImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 672, "type": "easy showAnything", "pos": [-2964.4384765625, 2021.940673828125], "size": [210, 76], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 1262, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["4"]}, {"id": 764, "type": "ImageCrop", "pos": [-5062.6416015625, 426.25982666015625], "size": [210, 114], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1425}, {"name": "width", "type": "INT", "link": 1426, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1427, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1419, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1420, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1428], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 765, "type": "ConrainPythonExecutor", "pos": [-5436.6416015625, 726.2598876953125], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 101, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1424, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1419, 1421], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "type": "ConrainPythonExecutor", "pos": [-5428.6416015625, 613.2598876953125], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 100, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1423, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1420, 1422], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "type": "ImageCrop", "pos": [-5100.6416015625, 867.2598876953125], "size": [210, 114], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1429}, {"name": "width", "type": "INT", "link": 1430, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1431, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1421, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1422, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1432], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 768, "type": "Reroute", "pos": [-3580.128173828125, 1965.8056640625], "size": [75, 26], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1433}], "outputs": [{"name": "", "type": "IMAGE", "links": [1435], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 769, "type": "Reroute", "pos": [-3670, 2148], "size": [75, 26], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1434}], "outputs": [{"name": "", "type": "MASK", "links": [1436], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 773, "type": "Reroute", "pos": [-8719.650390625, 1624.1514892578125], "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1615}], "outputs": [{"name": "", "type": "IMAGE", "links": [1444], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 774, "type": "ConrainPythonExecutor", "pos": [-7860.6416015625, 773.2598876953125], "size": [400, 200], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1449, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1446], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "type": "ConrainPythonExecutor", "pos": [-7853.6416015625, 1077.259521484375], "size": [400, 200], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1450, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1451], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "type": "Reroute", "pos": [-7837.6416015625, 614.2598876953125], "size": [75, 26], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1448}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1449, 1450], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 779, "type": "CR Seed", "pos": [-7522, 1666.17578125], "size": [315, 102], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1455, 1460, 1468, 1469], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [0, "fixed"]}, {"id": 780, "type": "Reroute", "pos": [-7045, 1988.17578125], "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1455, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1456, 1457, 1458, 1459], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 781, "type": "Reroute", "pos": [-7010.5673828125, 2188.185546875], "size": [75, 26], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1460, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1461, 1462, 1463, 1464], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 784, "type": "Note", "pos": [-7697.6416015625, 520.2598266601562], "size": [210, 60], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"], "color": "#432", "bgcolor": "#653"}, {"id": 793, "type": "ConrainPythonExecutor", "pos": [-3234, 2188], "size": [336.5685119628906, 190.69573974609375], "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1496, "shape": 7}, {"name": "any_b", "type": "*", "link": 1500, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1497], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 794, "type": "ImpactImageBatchToImageList", "pos": [-2561, 1732], "size": [227.2136688232422, 39.664772033691406], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1498}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1499], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": []}, {"id": 801, "type": "ConrainMaskFromColors", "pos": [-8217.4580078125, -668.181640625], "size": [228.88619995117188, 124.55016326904297], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1512}, {"name": "color_list", "type": "STRING", "link": 1514, "widget": {"name": "color_list"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1518], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 803, "type": "ConrainPythonExecutor", "pos": [-9155.13671875, -610.7435913085938], "size": [372.4948425292969, 392.6221008300781], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1513, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1514], "slot_index": 0}], "title": "densepose提取mask区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 810, "type": "GrowMask", "pos": [-7843.46240234375, -661.1817016601562], "size": [210, 95.39423370361328], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1518}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1527], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 814, "type": "JWMaskResize", "pos": [-7547.40771484375, -645.2855834960938], "size": [259.6524353027344, 135.95858764648438], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1527}, {"name": "height", "type": "INT", "link": 1534, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 1533, "widget": {"name": "width"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1623], "slot_index": 0}], "properties": {"Node name for S&R": "JWMaskResize"}, "widgets_values": [512, 512, "bilinear"]}, {"id": 818, "type": "Image Blank", "pos": [-7199.40771484375, -660.2855834960938], "size": [315, 154], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1540, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1541, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1542], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 822, "type": "Masks Subtract", "pos": [-6504.185546875, -507.7434997558594], "size": [210, 46], "flags": {}, "order": 72, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1550}, {"name": "masks_b", "type": "MASK", "link": 1549}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1552, 1572], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Subtract"}, "widgets_values": []}, {"id": 841, "type": "Note", "pos": [-9351.4609375, -707.3843994140625], "size": [210, 60], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"], "color": "#432", "bgcolor": "#653"}, {"id": 843, "type": "Reroute", "pos": [-8505.607421875, 501.2723388671875], "size": [75, 26], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1585}], "outputs": [{"name": "", "type": "IMAGE"}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 844, "type": "InspyrenetRembg", "pos": [-8031.57275390625, 26.234153747558594], "size": [211.82809448242188, 78], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1586}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1588], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"]}, {"id": 845, "type": "Reroute", "pos": [-7748.40576171875, 63.28346633911133], "size": [75, 26], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1588}], "outputs": [{"name": "", "type": "IMAGE", "links": [1589, 1590, 1591], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 847, "type": "CLIPTextEncode", "pos": [-4108.36572265625, 1693.341796875], "size": [210, 93.48091125488281], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1593}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1594], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 800, "type": "DensePosePreprocessor", "pos": [-8613.943359375, -683.7633056640625], "size": [315, 106], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1591}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1512], "slot_index": 0}], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "widgets_values": ["densepose_r101_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 823, "type": "easy ifElse", "pos": [-6519.3525390625, -685.8197631835938], "size": [210, 82.75738525390625], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1548}, {"name": "on_false", "type": "*", "link": 1547}, {"name": "boolean", "type": "BOOLEAN", "link": 1546, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1549], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 463, "type": "LoadImage", "pos": [-11789.619140625, 281.8291320800781], "size": [315, 314], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1693], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${referenceImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 820, "type": "ShowText|pysssss", "pos": [-8411.46875, -66.26930236816406], "size": [210, 326], "flags": {"collapsed": false}, "order": 38, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1543, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1544], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["trousers"], "trousers", "trousers", "trousers", "clothing", "trousers", "trousers"]}, {"id": 777, "type": "Note", "pos": [-9403.56640625, -250.84007263183594], "size": [210, 111.45333862304688], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "title": "抠图词", "properties": {}, "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"], "color": "#432", "bgcolor": "#653"}, {"id": 808, "type": "ConrainPythonExecutor", "pos": [-9408.1435546875, -27.050457000732422], "size": [300.1857604980469, 284.6385192871094], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1536, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1543], "slot_index": 0}], "title": "需要排除的mask", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 418, "type": "ConrainPythonExecutor", "pos": [-9401.05859375, -340.7513427734375], "size": [296.9554443359375, 286.73455810546875], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 834, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [837], "slot_index": 0}], "title": "需要替换的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 419, "type": "ShowText|pysssss", "pos": [-9085.595703125, -384.034423828125], "size": [210, 326], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 837, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [839], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "coat. shirt. jacket", "coat. shirt. jacket", "clothing", "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 815, "type": "GetImageSize+", "pos": [-7865.46240234375, -483.18170166015625], "size": [214.20001220703125, 66], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1528}], "outputs": [{"name": "width", "type": "INT", "links": [1533, 1540], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1534, 1541], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 821, "type": "ConrainPythonExecutor", "pos": [-7190.876953125, -426.6115417480469], "size": [304.83380126953125, 204.48739624023438], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1545, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1546], "slot_index": 0}], "title": "需要排除的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 369, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-7176.89208984375, -129.6724090576172], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 712, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 713, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1590, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1544, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "label": "IMAGE", "shape": 3}, {"name": "MASK", "type": "MASK", "links": [1548], "slot_index": 1, "label": "MASK", "shape": 3}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 819, "type": "ImageToMask", "pos": [-6817.92578125, -661.918212890625], "size": [210, 68.49308776855469], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1542}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1547], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 812, "type": "GrowMask", "pos": [-7867.0048828125, -349.5039978027344], "size": [210, 93.73921966552734], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1522}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1622], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 840, "type": "ConrainImageSave", "pos": [-6167.8896484375, -284.8390808105469], "size": [231.75296020507812, 266], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1581, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1579, "label": "output_path", "widget": {"name": "output_path"}}, {"name": "filename_prefix", "type": "STRING", "link": 1580, "label": "filename_prefix", "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "label": "image_cnt", "shape": 3}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 839, "type": "Text String", "pos": [-6599.95654296875, -261.0054626464844], "size": [300.3023681640625, 216.65859985351562], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1579], "slot_index": 0, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "links": [1580], "slot_index": 1, "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}, {"name": "STRING", "type": "STRING", "label": "STRING", "shape": 3}], "title": "保存参看图的mask", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "mask_${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 837, "type": "MaskToImage", "pos": [-6173.64111328125, -509.6376647949219], "size": [188.457763671875, 48.499874114990234], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1572}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1581], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 811, "type": "Masks Add", "pos": [-6802.341796875, -505.9018249511719], "size": [202.08831787109375, 46], "flags": {}, "order": 71, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1622}, {"name": "masks_b", "type": "MASK", "link": 1623}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1550], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Add"}, "widgets_values": []}, {"id": 366, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8221.2822265625, -461.5549011230469], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 709, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 710, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1589, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 839, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1528], "slot_index": 0, "label": "IMAGE", "shape": 3}, {"name": "MASK", "type": "MASK", "links": [1522], "slot_index": 1, "label": "MASK", "shape": 3}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.6], "color": "#474747", "bgcolor": "#333333"}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [709, 365, 0, 366, 0, "SAM_MODEL"], [710, 367, 0, 366, 1, "GROUNDING_DINO_MODEL"], [712, 365, 0, 369, 0, "SAM_MODEL"], [713, 367, 0, 369, 1, "GROUNDING_DINO_MODEL"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [834, 368, 0, 418, 0, "*"], [837, 418, 0, 419, 0, "STRING"], [839, 419, 0, 366, 3, "STRING"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1257, 462, 0, 407, 0, "*"], [1262, 436, 1, 672, 0, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1496, 289, 0, 793, 0, "*"], [1497, 793, 0, 288, 0, "STITCH"], [1498, 276, 0, 794, 0, "IMAGE"], [1499, 794, 0, 288, 1, "IMAGE"], [1500, 435, 0, 793, 1, "*"], [1510, 461, 0, 331, 0, "*"], [1512, 800, 0, 801, 0, "IMAGE"], [1513, 368, 0, 803, 0, "*"], [1514, 803, 0, 801, 1, "STRING"], [1518, 801, 0, 810, 0, "MASK"], [1522, 366, 1, 812, 0, "MASK"], [1527, 810, 0, 814, 0, "MASK"], [1528, 366, 0, 815, 0, "IMAGE"], [1533, 815, 0, 814, 2, "INT"], [1534, 815, 1, 814, 1, "INT"], [1536, 368, 0, 808, 0, "*"], [1540, 815, 0, 818, 0, "INT"], [1541, 815, 1, 818, 1, "INT"], [1542, 818, 0, 819, 0, "IMAGE"], [1543, 808, 0, 820, 0, "STRING"], [1544, 820, 0, 369, 3, "STRING"], [1545, 368, 0, 821, 0, "*"], [1546, 821, 0, 823, 2, "BOOLEAN"], [1547, 819, 0, 823, 1, "*"], [1548, 369, 1, 823, 0, "*"], [1549, 823, 0, 822, 1, "MASK"], [1550, 811, 0, 822, 0, "MASK"], [1552, 822, 0, 315, 0, "MASK"], [1572, 822, 0, 837, 0, "MASK"], [1579, 839, 0, 840, 1, "STRING"], [1580, 839, 1, 840, 2, "STRING"], [1581, 837, 0, 840, 0, "IMAGE"], [1585, 328, 0, 843, 0, "*"], [1586, 328, 0, 844, 0, "IMAGE"], [1588, 844, 0, 845, 0, "*"], [1589, 845, 0, 366, 2, "IMAGE"], [1590, 845, 0, 369, 2, "IMAGE"], [1591, 845, 0, 800, 0, "IMAGE"], [1593, 281, 0, 847, 0, "CLIP"], [1594, 847, 0, 290, 0, "CONDITIONING"], [1615, 408, 0, 773, 0, "*"], [1622, 812, 0, 811, 0, "MASK"], [1623, 814, 0, 811, 1, "MASK"], [1693, 463, 0, 402, 0, "*"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4589.86474609375, 176.38858032226562, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "处理服装图mask", "bounding": [-7916.77880859375, 1547.8116455078125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "处理参考图mask", "bounding": [-7896.23095703125, 199.59716796875, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4552.4736328125, 1515.76904296875, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "图片尺寸最大2048", "bounding": [-11184.4951171875, 191.1204376220703, 3120.570556640625, 2013.73291015625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 26, "title": "Group", "bounding": [-11836.82421875, 192.79454040527344, 461.9967041015625, 1313.12548828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 27, "title": "处理模特图的mask", "bounding": [-9863.421875, -857.2464599609375, 3980.1259765625, 983.1404418945312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.10772642221612362, "offset": [14253.477554500643, 4675.10437835663]}}, "version": 0.4, "seed_widgets": {"275": 0, "401": 0, "779": 0}}}}}
{"client_id": "48e7da9cdf784dc4aa692eaea67144db", "prompt": {"52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["322", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["764", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "product/20250719/100014/235799", "text_b": "product_1011153", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存tryon结果"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["397", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "211": {"inputs": {"channel": "red", "image": ["767", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["346", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": ["774", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": ["775", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 661045452417164, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["436", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. ", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["289", 1], "mask": ["289", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"crop": "center", "clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "288": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["792", 0], "inpainted_image": ["793", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "✂️ Inpaint <PERSON>itch"}}, "289": {"inputs": {"context_expand_pixels": 10, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1536, "max_height": 1785, "padding": 32, "image": ["321", 0], "mask": ["56", 0], "optional_context_mask": ["297", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "✂️ Inpaint Crop"}}, "290": {"inputs": {"conditioning": ["277", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["288", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["351", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["397", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["397", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 5, "tapered_corners": false, "mask": ["847", 0]}, "class_type": "GrowMask", "_meta": {"title": "替换区域扩张大小"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["764", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "322": {"inputs": {"expand": 0, "tapered_corners": true, "mask": ["211", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["797", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+b", "a": ["264", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+b", "a": ["265", 0], "b": ["779", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["461", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["461", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0], "padding_bottom": ["779", 0], "image": ["300", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "397": {"inputs": {"upscale_method": "area", "scale_by": ["398", 0], "image": ["761", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "398": {"inputs": {"target_size": ["399", 0], "image": ["761", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "399": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "400": {"inputs": {"image": ["761", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "401": {"inputs": {"seed": 1785}, "class_type": "CR Seed", "_meta": {"title": "最大支持尺寸"}}, "404": {"inputs": {"image": ["462", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "405": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]", "any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "406": {"inputs": {"target_size": ["405", 0], "image": ["462", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "408": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["462", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "433": {"inputs": {"text": "4"}, "class_type": "CR Text", "_meta": {"title": "tryon生成图片张数"}}, "434": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]", "any_a": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "435": {"inputs": {"text": ["434", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "436": {"inputs": {"multiply_by": ["435", 0], "latents": ["282", 2]}, "class_type": "VHS_DuplicateLatents", "_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}}, "456": {"inputs": {"image": "product_2971815_DPvfb.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "459": {"inputs": {"width": ["404", 0], "height": ["404", 1], "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["456", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "460": {"inputs": {"upscale_method": "area", "scale_by": ["406", 0], "image": ["459", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "461": {"inputs": {"channel": "red", "image": ["460", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "462": {"inputs": {"image": "14d1fa373786449da51799a0a251dd5d.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "463": {"inputs": {"image": "pasted/image (1995).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "758": {"inputs": {"text": "1"}, "class_type": "CR Text", "_meta": {"title": "输入图是否要换头"}}, "759": {"inputs": {"comparison": "a == b", "a": ["758", 0], "b": ["760", 0]}, "class_type": "easy compare", "_meta": {"title": "Compare"}}, "760": {"inputs": {"text": "0"}, "class_type": "CR Text", "_meta": {"title": "🔤 CR Text"}}, "761": {"inputs": {"boolean": ["759", 0], "on_true": ["463", 0], "on_false": ["763", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "763": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n", "any_a": ["985", 0], "any_b": ["463", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "764": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["397", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "765": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "766": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "767": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["765", 0], "y": ["766", 0], "image": ["194", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "774": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "775": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]", "any_a": ["168", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "779": {"inputs": {"seed": 0}, "class_type": "CR Seed", "_meta": {"title": "🌱 CR Seed"}}, "792": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b", "any_a": ["289", 0], "any_b": ["435", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "793": {"inputs": {"image": ["276", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "797": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["799", 0], "source": ["408", 0], "mask": ["461", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "799": {"inputs": {"width": ["800", 0], "height": ["800", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "800": {"inputs": {"image": ["408", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "834": {"inputs": {"model_name": "sam_vit_h_cloth"}, "class_type": "Conrain_SAMModelLoader", "_meta": {"title": "Conrain SAMModelLoader"}}, "835": {"inputs": {"prompt": ["840", 0], "background": "white", "threshold": 0.3, "sam_model": ["834", 0], "grounding_dino_model": ["836", 0], "image": ["397", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "836": {"inputs": {"model_name": "groundingdino_cloth"}, "class_type": "Conrain_GroundingDinoModelLoader", "_meta": {"title": "Conrain GroundingDinoModelLoader"}}, "837": {"inputs": {"text": "upper garment"}, "class_type": "CR Text", "_meta": {"title": "抠图词"}}, "838": {"inputs": {"prompt": ["853", 0], "background": "white", "threshold": 0.3, "sam_model": ["834", 0], "grounding_dino_model": ["836", 0], "image": ["397", 0]}, "class_type": "Conrain_GroundingDinoSAMSegment", "_meta": {"title": "Conrain GroundingDinoSAMSegment"}}, "839": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要替换的区域"}}, "840": {"inputs": {"text": ["839", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "842": {"inputs": {"model": "densepose_r50_fpn_dl.torchscript", "cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "resolution": 512, "image": ["397", 0]}, "class_type": "DensePosePreprocessor", "_meta": {"title": "DensePose Estimator"}}, "843": {"inputs": {"color_list": ["844", 0], "threshold": 2, "image": ["842", 0]}, "class_type": "ConrainMaskFromColors", "_meta": {"title": "Conrain Mask From Colors"}}, "844": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "densepose提取mask区域"}}, "845": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的mask"}}, "846": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["843", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "847": {"inputs": {"masks_a": ["849", 0], "masks_b": ["848", 0]}, "class_type": "Masks Add", "_meta": {"title": "Masks Add"}}, "848": {"inputs": {"expand": 0, "tapered_corners": false, "mask": ["835", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "849": {"inputs": {"height": ["850", 1], "width": ["850", 0], "interpolation_mode": "bilinear", "mask": ["846", 0]}, "class_type": "JWMaskResize", "_meta": {"title": "Mask Resize"}}, "850": {"inputs": {"image": ["835", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "851": {"inputs": {"width": ["850", 0], "height": ["850", 1], "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "852": {"inputs": {"channel": "red", "image": ["851", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "853": {"inputs": {"text": ["845", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "854": {"inputs": {"call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]", "any_a": ["837", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "需要排除的区域"}}, "856": {"inputs": {"boolean": ["854", 0], "on_true": ["838", 1], "on_false": ["852", 0]}, "class_type": "easy ifElse", "_meta": {"title": "If else"}}, "862": {"inputs": {"mask": ["847", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "864": {"inputs": {"text": "product/20250719/100014/235799", "text_b": "mask_product_1011153", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存参看图的mask"}}, "865": {"inputs": {"output_path": ["864", 0], "filename_prefix": ["864", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["862", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "870": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "871": {"inputs": {"guidance": 3.5, "conditioning": ["876", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "872": {"inputs": {"switch": "On", "base_positive": ["952", 0], "base_negative": ["875", 0], "controlnet_stack": ["953", 0]}, "class_type": "CR Apply Multi-ControlNet", "_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}}, "873": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "874": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "875": {"inputs": {"text": "", "clip": ["923", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "876": {"inputs": {"text": ["918", 0], "clip": ["923", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "877": {"inputs": {"model": ["923", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "878": {"inputs": {"model": ["877", 0], "conditioning": ["872", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "879": {"inputs": {"noise": ["889", 0], "guider": ["878", 0], "sampler": ["890", 0], "sigmas": ["982", 0], "latent_image": ["952", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "880": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["976", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "881": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]", "any_a": ["976", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "882": {"inputs": {"width": ["881", 0], "height": ["880", 0], "position": "top-left", "x_offset": 0, "y_offset": 0, "image": ["978", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "🔧 Image Crop"}}, "884": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": false}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "885": {"inputs": {"samples": ["879", 0], "vae": ["870", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "886": {"inputs": {"resolution": 1024, "image": ["882", 0]}, "class_type": "Zoe-DepthMapPreprocessor", "_meta": {"title": "<PERSON>"}}, "888": {"inputs": {"low_threshold": 0.2, "high_threshold": 0.8, "image": ["882", 0]}, "class_type": "<PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON>"}}, "889": {"inputs": {"noise_seed": 960145982391288}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "890": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "891": {"inputs": {"expand": 2, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": true, "mask": ["894", 4]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "894": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["884", 0], "image": ["882", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "895": {"inputs": {"Input": ["897", 0], "image1": ["970", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "896": {"inputs": {"Input": ["898", 0], "image1": ["971", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "897": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "898": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "902": {"inputs": {"target_size": 1024, "image": ["943", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "904": {"inputs": {"guidance": 2, "conditioning": ["942", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "905": {"inputs": {"conditioning": ["942", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "906": {"inputs": {"noise_seed": 0}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "907": {"inputs": {"model": ["922", 0], "conditioning": ["973", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "908": {"inputs": {"sampler_name": "dpm_2"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "909": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["940", 0], "image": ["968", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "910": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["959", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "911": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "upscale if smaller", "multiple_of": 0, "image": ["916", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "912": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "913": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["944", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "916": {"inputs": {"image1": ["969", 0], "image2": ["895", 0], "image3": ["896", 0]}, "class_type": "ImpactMakeImageList", "_meta": {"title": "Make Image List"}}, "917": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["945", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "918": {"inputs": {"prompts": "a mgm3004 Golden alabaster European female model,20-year-old,  long wavy auburn hair, ", "seed": 991}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "922": {"inputs": {"model": ["923", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "923": {"inputs": {"lora_name": "online_product/Katriina-小头2_copy_copy_10352_20250326_143128/<PERSON><PERSON><PERSON>-小头2_copy_copy_10352_20250326_143128-flux/Katriina-小头2_copy_copy_10352_20250326_143128-flux.safetensors", "strength_model": "1", "strength_clip": "1", "model": ["874", 0], "clip": ["873", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "925": {"inputs": {"text": ["918", 0], "clip": ["959", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "926": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "927": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "928": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "929": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["931", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "930": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["928", 0], "insightface": ["927", 0], "control_net": ["926", 0], "image": ["933", 0], "model": ["959", 0], "positive": ["925", 0], "negative": ["910", 0], "image_kps": ["949", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "931": {"inputs": {"images": ["946", 0]}, "class_type": "easy imageListToImageBatch", "_meta": {"title": "Image List To Image Batch"}}, "932": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["965", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "933": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["929", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "935": {"inputs": {"noise_mask": true, "positive": ["930", 1], "negative": ["930", 2], "vae": ["959", 2], "pixels": ["949", 0], "mask": ["974", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "937": {"inputs": {"noise": ["906", 0], "guider": ["907", 0], "sampler": ["908", 0], "sigmas": ["957", 0], "latent_image": ["951", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "938": {"inputs": {"images": ["965", 0], "face": ["961", 0], "crop": ["955", 0], "mask": ["972", 0], "warp": ["949", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "940": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": true}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "942": {"inputs": {"text": ["918", 0], "clip": ["923", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "943": {"inputs": {"expand_LRU": 20, "expand_B": 20, "image": ["968", 0], "mask": ["909", 4]}, "class_type": "BoundedImageCropWithMask_v3_LR", "_meta": {"title": "Bounded Image Crop With Mask LR v3"}}, "944": {"inputs": {"segs": ["917", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "945": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["912", 0], "image": ["911", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "946": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["911", 0], "mask": ["913", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "947": {"inputs": {"samples": ["960", 0], "vae": ["959", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "949": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["961", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "951": {"inputs": {"amount": 1, "samples": ["973", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "952": {"inputs": {"noise_mask": true, "positive": ["871", 0], "negative": ["875", 0], "vae": ["870", 0], "pixels": ["882", 0], "mask": ["891", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "953": {"inputs": {"switch_1": "On", "controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "start_percent_1": 0, "end_percent_1": 0.2, "switch_2": "On", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_strength_2": 0.2, "start_percent_2": 0.2, "end_percent_2": 0.8, "switch_3": "On", "controlnet_3": "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", "controlnet_strength_3": 0.4, "start_percent_3": 0, "end_percent_3": 0.6, "image_1": ["886", 0], "image_2": ["886", 0], "image_3": ["888", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}}, "954": {"inputs": {"samples": ["937", 0], "vae": ["870", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "955": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["949", 0], "image_target": ["954", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "957": {"inputs": {"scheduler": "sgm_uniform", "steps": 8, "denoise": 0.1, "model": ["923", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "958": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "resize", "rescale_factor": 1, "resize_width": ["979", 0], "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["885", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "959": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "960": {"inputs": {"seed": 42029499340256, "steps": 4, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "denoise": 0.5, "model": ["930", 0], "positive": ["935", 0], "negative": ["935", 1], "latent_image": ["935", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "961": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["932", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "963": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["940", 0], "image": ["949", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "964": {"inputs": {"expand": -10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 15, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["943", 2]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "965": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["902", 0], "image": ["943", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "968": {"inputs": {"upscale_method": "bicubic", "width": ["979", 0], "height": ["979", 1], "crop": "disabled", "image": ["958", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "969": {"inputs": {"image": "pasted/image (1885).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "970": {"inputs": {"image": "pasted/image (1887).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "971": {"inputs": {"image": "pasted/image (1886).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "972": {"inputs": {"expand": 40, "incremental_expandrate": 0, "tapered_corners": false, "flip_input": false, "blur_radius": 0, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["974", 0]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "973": {"inputs": {"noise_mask": false, "positive": ["904", 0], "negative": ["905", 0], "vae": ["870", 0], "pixels": ["947", 0], "mask": ["975", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "974": {"inputs": {"invert_mask": false, "grow": 5, "blur": 4, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["987", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "975": {"inputs": {"invert_mask": true, "grow": 20, "blur": 10, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["974", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "976": {"inputs": {"image": ["978", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "978": {"inputs": {"width": 1536, "height": 1536, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "downscale if bigger", "multiple_of": 0, "image": ["463", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "979": {"inputs": {"image": ["463", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "981": {"inputs": {"text": "product/20250605/100752/329395", "text_b": "product_2214957", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存换头结果图"}}, "982": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 0.9, "model": ["923", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "985": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["968", 0], "target_mask": ["964", 0], "target_bounds": ["943", 1], "source": ["938", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "986": {"inputs": {"output_path": ["981", 0], "filename_prefix": ["981", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["985", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "987": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["949", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 988, "last_link_id": 1787, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3706.53466796875, 1812.228759765625], "size": [261.10693359375, 178], "flags": {}, "order": 251, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 820}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2848.5341796875, 1879.2283935546875], "size": [264.5999755859375, 26], "flags": {}, "order": 255, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2852.5341796875, 2061.2265625], "size": [264.5999755859375, 26], "flags": {}, "order": 242, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 592}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 56, "type": "ImageToMask", "pos": [-1986.5281982421875, 1933.196533203125], "size": [210, 59.905555725097656], "flags": {}, "order": 262, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1434], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 60, "type": "Get Image Size", "pos": [-3917.53466796875, 2266.224853515625], "size": [298.42425537109375, 46], "flags": {}, "order": 252, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 821}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 61, "type": "Get Image Size", "pos": [-3203.9482421875, 3095.456787109375], "size": [210, 46], "flags": {}, "order": 241, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 113, "type": "Reroute", "pos": [-4042.759765625, 1668.781982421875], "size": [75, 26], "flags": {}, "order": 238, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 819}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 159, "type": "Text String", "pos": [-2210, 3360], "size": [228.5572052001953, 190], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [873], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [874], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存tryon结果", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250719/100014/235799", "product_1011153", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 167, "type": "Reroute", "pos": [-4409.53564453125, 1786.22900390625], "size": [75, 26], "flags": {}, "order": 250, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 441}], "outputs": [{"name": "", "type": "IMAGE", "links": [820, 821, 822, 823], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-7087.40234375, 1276.54248046875], "size": [248.14456176757812, 150], "flags": {}, "order": 203, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292, 1448], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5796.40234375, 1658.54248046875], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 221, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [544, 1423, 1424], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "type": "MaskToImage", "pos": [-5828.779296875, 1989.6298828125], "size": [264.5999755859375, 26], "flags": {}, "order": 204, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1429], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 208, "type": "Reroute", "pos": [-4313.53564453125, 1468.2301025390625], "size": [75, 26], "flags": {}, "order": 234, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1428}], "outputs": [{"name": "", "type": "IMAGE", "links": [819], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-5005.779296875, 2112.63134765625], "size": [210, 84.43663024902344], "flags": {}, "order": 235, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1432}], "outputs": [{"name": "MASK", "type": "MASK", "links": [591], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-6855.95703125, 2875.468994140625], "size": [243.56057739257812, 150], "flags": {}, "order": 232, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 1465}, {"name": "padding_left", "type": "INT", "link": 1456, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1457, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1458, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1459, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5033.61572265625, 2818.682373046875], "size": [210, 138], "flags": {}, "order": 248, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 420, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 421, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [441], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 245, "type": "EmptyImage", "pos": [-5743.5244140625, 2618.468994140625], "size": [243.3533935546875, 102], "flags": {}, "order": 224, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 759, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 246, "type": "CR Seed", "pos": [-7381.40234375, 1781.54248046875], "size": [281.7162780761719, 102], "flags": {}, "order": 209, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1446, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1152, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 247, "type": "CR Seed", "pos": [-7359.40234375, 2077.543701171875], "size": [278.3121032714844, 102], "flags": {}, "order": 210, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 1451, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1536, "fixed"], "color": "#222", "bgcolor": "#000"}, {"id": 249, "type": "SimpleMath+", "pos": [-5383.61572265625, 2823.682373046875], "size": [220.35072326660156, 98], "flags": {}, "order": 247, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5383.61572265625, 2624.68310546875], "size": [210, 112.43743896484375], "flags": {}, "order": 246, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6444.5244140625, 2623.468994140625], "size": [282.12066650390625, 195.71939086914062], "flags": {}, "order": 240, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "type": "ImageScaleBy", "pos": [-6100.5244140625, 2879.468994140625], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 243, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6189.40234375, 1598.54248046875], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 218, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "type": "JWIntegerMin", "pos": [-6928.779296875, 1696.63037109375], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 211, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 259, "type": "JWIntegerMin", "pos": [-6934.779296875, 2046.6298828125], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 212, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6575.779296875, 1672.630126953125], "size": [210, 71.68185424804688], "flags": {}, "order": 213, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755, 1426, 1430], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 265, "type": "JWIntegerMax", "pos": [-6617.779296875, 1963.6302490234375], "size": [210, 71.68185424804688], "flags": {}, "order": 214, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 756, 1427, 1431], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-6926.40234375, 1832.54248046875], "size": [210, 67.93143463134766], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 269, "type": "Note", "pos": [-6562.779296875, 1830.6304931640625], "size": [210, 67.93143463134766], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2872.509765625, 2777.515869140625], "size": [234.29580688476562, 262], "flags": {}, "order": 268, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 850, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [661045452417164, "fixed", 20, 1, "euler", "simple", 1]}, {"id": 276, "type": "VAEDecode", "pos": [-2649.509765625, 2683.51611328125], "size": [210, 46], "flags": {}, "order": 269, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1501], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4101.509765625, 2744.51611328125], "size": [269.3892822265625, 89.79380798339844], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500, 513], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "], "color": "#222", "bgcolor": "#000"}, {"id": 278, "type": "FluxGuidance", "pos": [-3845.345703125, 2850.************], "size": [210, 58], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 279, "type": "UNETLoader", "pos": [-4014.509765625, 2607.************], "size": [326.5174865722656, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "type": "VAELoader", "pos": [-4432.3720703125, 2883.************], "size": [300, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4459.341796875, 2688.78076171875], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3237.509765625, 2790.515869140625], "size": [210, 138], "flags": {}, "order": 266, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 1437, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1438, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [848], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3235.97265625, 2670.276123046875], "size": [184.8000030517578, 26], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4452.9453125, 3088.456787109375], "size": [370, 60], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3917.685302734375, 1980.57470703125], "size": [210, 78], "flags": {}, "order": 253, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 822, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 286, "type": "StyleModelApply", "pos": [-3552.980712890625, 1847.6009521484375], "size": [210, 122], "flags": {}, "order": 256, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "shape": 7, "label": "CLIP视觉输出"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 287, "type": "StyleModelLoader", "pos": [-4441.5869140625, 3005.603271484375], "size": [340, 60], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "type": "InpaintStitch", "pos": [-2357.509765625, 2665.************], "size": [256.60272216796875, 78], "flags": {}, "order": 271, "mode": 0, "inputs": [{"name": "stitch", "type": "STITCH", "link": 1500, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 1502, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1440], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 289, "type": "InpaintCrop", "pos": [-3653.509765625, 3144.515869140625], "size": [245.64613342285156, 386], "flags": {}, "order": 263, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1435, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 1436, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "shape": 7, "label": "上下文遮罩(可选)"}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [1498], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [1437], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [1438], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3801.5908203125, 2779.913330078125], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 68, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 513, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 291, "type": "ImageCrop", "pos": [-2462.509765625, 2949.51611328125], "size": [210, 118], "flags": {}, "order": 272, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1440}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3561.509765625, 2635.************], "size": [271.6474304199219, 86.10514068603516], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 297, "type": "ImageToMask", "pos": [-2040.5343017578125, 1606.2298583984375], "size": [210, 59.905555725097656], "flags": {}, "order": 261, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 300, "type": "MaskToImage", "pos": [-6903.95703125, 3363.468994140625], "size": [176.39999389648438, 26], "flags": {}, "order": 233, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1466}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2941.52734375, 3169.814697265625], "size": [75, 26], "flags": {}, "order": 184, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 305, "type": "Reroute", "pos": [-2594.52734375, 3165.814697265625], "size": [75, 26], "flags": {}, "order": 226, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2224.509765625, 3169.515869140625], "size": [239.650634765625, 122], "flags": {}, "order": 273, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555, 879], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 314, "type": "Get Image Size", "pos": [-7429.40234375, 1354.54248046875], "size": [210, 46], "flags": {}, "order": 183, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 315, "type": "GrowMask", "pos": [-7412, 1491], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 200, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1599}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "title": "替换区域扩张大小", "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [5, false], "color": "#232", "bgcolor": "#353"}, {"id": 316, "type": "Get Image Size", "pos": [-6768.779296875, 1300.628173828125], "size": [210, 46], "flags": {}, "order": 205, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 319, "type": "GetImageSize+", "pos": [-6613.5244140625, 2695.468994140625], "size": [144.6750030517578, 71.8825912475586], "flags": {}, "order": 236, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 320, "type": "GetImageSize+", "pos": [-5700.61572265625, 2958.350830078125], "size": [214.20001220703125, 66], "flags": {}, "order": 245, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 321, "type": "easy imageConcat", "pos": [-3773.3818359375, 1401.21484375], "size": [315, 102], "flags": {}, "order": 254, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 823}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [589, 1433, 1477], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 322, "type": "GrowMask", "pos": [-3303.79052734375, 1979.870361328125], "size": [315, 82], "flags": {}, "order": 239, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 591}], "outputs": [{"name": "MASK", "type": "MASK", "links": [592], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, true]}, {"id": 323, "type": "easy imageConcat", "pos": [-2367.5341796875, 1607.2298583984375], "size": [315, 102], "flags": {}, "order": 259, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 324, "type": "easy imageConcat", "pos": [-2452.4833984375, 1927.94970703125], "size": [315, 102], "flags": {"collapsed": false}, "order": 260, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 328, "type": "Reroute", "pos": [-8927.5751953125, 1416.828857421875], "size": [75, 26], "flags": {}, "order": 182, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 807}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 610, 1425, 1598], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-7856.5244140625, 2596.468994140625], "size": [75, 26], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1512}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7427.95703125, 2875.468994140625], "size": [243.56057739257812, 150], "flags": {}, "order": 222, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1444}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 347, "type": "SimpleMath+", "pos": [-7790.95703125, 2903.468994140625], "size": [210, 98], "flags": {"collapsed": false}, "order": 215, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "link": 1468, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 348, "type": "SimpleMath+", "pos": [-7779.5244140625, 3126.468994140625], "size": [210, 98], "flags": {"collapsed": false}, "order": 219, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "link": 1469, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+b"]}, {"id": 349, "type": "MaskToImage", "pos": [-7767.95703125, 3305.468994140625], "size": [176.39999389648438, 26], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7416.95703125, 3183.468994140625], "size": [243.56057739257812, 150], "flags": {}, "order": 223, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 351, "type": "ImageToMask", "pos": [-7165.5244140625, 3250.468994140625], "size": [210, 83.63514709472656], "flags": {}, "order": 229, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1465, 1466, 1467], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 362, "type": "SimpleMath+", "pos": [-4387.9453125, 3188.456787109375], "size": [210, 98], "flags": {"collapsed": false}, "order": 216, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 384, "type": "ImageScaleBy", "pos": [-6074.5244140625, 3323.468994140625], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 244, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5057.61572265625, 3241.682373046875], "size": [210, 138], "flags": {}, "order": 249, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 771, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 770, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 386, "type": "EmptyImage", "pos": [-5703.61572265625, 3151.682373046875], "size": [243.3533935546875, 102], "flags": {}, "order": 225, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 762, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 390, "type": "Reroute", "pos": [-6052.61572265625, 2591.683349609375], "size": [75, 26], "flags": {}, "order": 217, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 391, "type": "Reroute", "pos": [-6035.61572265625, 2681.682861328125], "size": [75, 26], "flags": {}, "order": 220, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-6666.95703125, 3247.468994140625], "size": [243.56057739257812, 150], "flags": {}, "order": 237, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 1467}, {"name": "padding_left", "type": "INT", "link": 1461, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 1462, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 1463, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 1464, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 397, "type": "ImageScaleBy", "pos": [-9361, 1422], "size": [217.8218994140625, 125.52959442138672], "flags": {}, "order": 181, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 806}, {"name": "scale_by", "type": "FLOAT", "link": 798, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [807], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 398, "type": "UpscaleSizeCalculator", "pos": [-9718, 1570], "size": [220, 118], "flags": {}, "order": 180, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 805}, {"name": "target_size", "type": "INT", "link": 797, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [798], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 399, "type": "ConrainPythonExecutor", "pos": [-10318, 1679], "size": [365.79345703125, 195.28152465820312], "flags": {}, "order": 179, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 800, "shape": 7}, {"name": "any_b", "type": "*", "link": 801, "shape": 7}, {"name": "any_c", "type": "*", "link": 802, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [797], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "type": "Get Image Size", "pos": [-10659, 1689], "size": [210, 46], "flags": {}, "order": 178, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 804}], "outputs": [{"name": "width", "type": "INT", "links": [800], "slot_index": 0}, {"name": "height", "type": "INT", "links": [801], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 401, "type": "CR Seed", "pos": [-11164, 1650], "size": [270.7088317871094, 109.29169464111328], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [802, 811], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "最大支持尺寸", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 402, "type": "Reroute", "pos": [-11040, 1492], "size": [75, 26], "flags": {}, "order": 177, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1497}], "outputs": [{"name": "", "type": "*", "links": [804, 805, 806], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 404, "type": "Get Image Size", "pos": [-11103, 2232], "size": [210, 46], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 827}], "outputs": [{"name": "width", "type": "INT", "links": [809, 895], "slot_index": 0}, {"name": "height", "type": "INT", "links": [810, 896], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 405, "type": "ConrainPythonExecutor", "pos": [-10762, 2181], "size": [353.50982666015625, 168.5362548828125], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 809, "shape": 7}, {"name": "any_b", "type": "*", "link": 810, "shape": 7}, {"name": "any_c", "type": "*", "link": 811, "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [812], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "type": "UpscaleSizeCalculator", "pos": [-10343, 2431], "size": [220, 102.22442626953125], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 828}, {"name": "target_size", "type": "INT", "link": 812, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [817, 897], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 407, "type": "Reroute", "pos": [-11179, 2613], "size": [75, 26], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1257}], "outputs": [{"name": "", "type": "IMAGE", "links": [827, 828, 829], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageScaleBy", "pos": [-9932, 2298], "size": [210, 78], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 829}, {"name": "scale_by", "type": "FLOAT", "link": 817, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1507, 1515], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 434, "type": "ConrainPythonExecutor", "pos": [-4152.9453125, 3298.456298828125], "size": [255.5079803466797, 218.5600128173828], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 846, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [847], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 436, "type": "VHS_DuplicateLatents", "pos": [-3283.509765625, 2981.51611328125], "size": [260.3999938964844, 58.512535095214844], "flags": {}, "order": 267, "mode": 0, "inputs": [{"name": "latents", "type": "LATENT", "link": 848}, {"name": "multiply_by", "type": "INT", "link": 849, "widget": {"name": "multiply_by"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [850], "slot_index": 0}, {"name": "count", "type": "INT", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "widgets_values": {"multiply_by": 1}}, {"id": 457, "type": "Reroute", "pos": [-11131.5751953125, 2025.311767578125], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 880}], "outputs": [{"name": "", "type": "IMAGE", "links": [885], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 459, "type": "ImageResize+", "pos": [-10330, 2011], "size": [315, 218], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 885}, {"name": "width", "type": "INT", "link": 895, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 896, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [889], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "type": "ImageScaleBy", "pos": [-9906, 2038], "size": [214.26881408691406, 94.98396301269531], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 889}, {"name": "scale_by", "type": "FLOAT", "link": 897, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 461, "type": "ImageToMask", "pos": [-9131, 2034], "size": [226.7721405029297, 83.94685363769531], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 890}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1506, 1512], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 757, "type": "Reroute", "pos": [-12237, 1497], "size": [75, 26], "flags": {}, "order": 174, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1776}], "outputs": [{"name": "", "type": "IMAGE", "links": [1416], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 759, "type": "easy compare", "pos": [-11988, 1537], "size": [216.3546905517578, 95.56697845458984], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 1405}, {"name": "b", "type": "*", "link": 1406}], "outputs": [{"name": "boolean", "type": "BOOLEAN", "links": [1409], "slot_index": 0}], "properties": {"Node name for S&R": "easy compare"}, "widgets_values": ["a == b"]}, {"id": 760, "type": "CR Text", "pos": [-12330, 1888], "size": [210, 110.8138198852539], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1406], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["0"]}, {"id": 761, "type": "easy ifElse", "pos": [-11695, 1370], "size": [210, 80.45691680908203], "flags": {}, "order": 176, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1407}, {"name": "on_false", "type": "*", "link": 1418}, {"name": "boolean", "type": "BOOLEAN", "link": 1409, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1497], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 763, "type": "ConrainPythonExecutor", "pos": [-12126, 1445], "size": [327.3860778808594, 248.97320556640625], "flags": {"collapsed": true}, "order": 175, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1416, "shape": 7}, {"name": "any_b", "type": "*", "link": 1417, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1418], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 764, "type": "ImageCrop", "pos": [-5033.40234375, 1428.54248046875], "size": [210, 114], "flags": {}, "order": 230, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1425}, {"name": "width", "type": "INT", "link": 1426, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1427, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1419, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1420, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1428], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 765, "type": "ConrainPythonExecutor", "pos": [-5407.40234375, 1728.54248046875], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 228, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1424, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1419, 1421], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "type": "ConrainPythonExecutor", "pos": [-5399.40234375, 1615.54248046875], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 227, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1423, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1420, 1422], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "type": "ImageCrop", "pos": [-5071.40234375, 1869.54248046875], "size": [210, 114], "flags": {}, "order": 231, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1429}, {"name": "width", "type": "INT", "link": 1430, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1431, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 1421, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 1422, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1432], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 768, "type": "Reroute", "pos": [-3559.637939453125, 2989.5966796875], "size": [75, 26], "flags": {}, "order": 257, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1433}], "outputs": [{"name": "", "type": "IMAGE", "links": [1435], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 769, "type": "Reroute", "pos": [-3627.509765625, 3008.51611328125], "size": [75, 26], "flags": {}, "order": 264, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1434}], "outputs": [{"name": "", "type": "MASK", "links": [1436], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 773, "type": "Reroute", "pos": [-8257, 2543], "size": [75, 26], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1513}], "outputs": [{"name": "", "type": "IMAGE", "links": [1444], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 774, "type": "ConrainPythonExecutor", "pos": [-7831.40234375, 1775.54248046875], "size": [400, 200], "flags": {}, "order": 207, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1449, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1446], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "type": "ConrainPythonExecutor", "pos": [-7833.40234375, 2109.544677734375], "size": [400, 200], "flags": {}, "order": 208, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1450, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1451], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "type": "Reroute", "pos": [-7788.40234375, 1303.54248046875], "size": [75, 26], "flags": {}, "order": 206, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1448}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1449, 1450], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 779, "type": "CR Seed", "pos": [-7515.95703125, 2642.468994140625], "size": [315, 102], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [1455, 1460, 1468, 1469], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [0, "fixed"]}, {"id": 780, "type": "Reroute", "pos": [-7038.95703125, 2964.468994140625], "size": [75, 26], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1455, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1456, 1457, 1458, 1459], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 781, "type": "Reroute", "pos": [-7004.5244140625, 3164.468994140625], "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1460, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [1461, 1462, 1463, 1464], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 784, "type": "Note", "pos": [-7668.40234375, 1522.54248046875], "size": [210, 60], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"], "color": "#432", "bgcolor": "#653"}, {"id": 788, "type": "PreviewImage", "pos": [-3258.529296875, 1360.197509765625], "size": [210, 246], "flags": {}, "order": 258, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1477}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 789, "type": "Note", "pos": [-13243, 1368], "size": [210, 60], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["参考图"], "color": "#432", "bgcolor": "#653"}, {"id": 790, "type": "Note", "pos": [-13237, 1698], "size": [210, 60], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask"], "color": "#432", "bgcolor": "#653"}, {"id": 791, "type": "Note", "pos": [-13241, 2198], "size": [210, 60], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装原图"], "color": "#432", "bgcolor": "#653"}, {"id": 792, "type": "ConrainPythonExecutor", "pos": [-3010.509765625, 3271.515869140625], "size": [368.42327880859375, 199.41488647460938], "flags": {}, "order": 265, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1498, "shape": 7}, {"name": "any_b", "type": "*", "link": 1499, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1500], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 793, "type": "ImpactImageBatchToImageList", "pos": [-2592.509765625, 2813.51611328125], "size": [223.97938537597656, 66.6196060180664], "flags": {}, "order": 270, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1501}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1502], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "widgets_values": []}, {"id": 797, "type": "ImageCompositeMasked", "pos": [-8701, 2258], "size": [315, 146], "flags": {}, "order": 115, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 1510}, {"name": "source", "type": "IMAGE", "link": 1515}, {"name": "mask", "type": "MASK", "link": 1506, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1513], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 799, "type": "Image Blank", "pos": [-9106, 2315], "size": [210, 140.97183227539062], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1508, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1509, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1510], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 800, "type": "Get Image Size", "pos": [-9441, 2317], "size": [210, 46], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1507}], "outputs": [{"name": "width", "type": "INT", "links": [1508], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1509], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 833, "type": "Reroute", "pos": [-8597.283203125, 845.2554321289062], "size": [75, 26], "flags": {}, "order": 185, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1598}], "outputs": [{"name": "", "type": "IMAGE", "links": [1559, 1563, 1567]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 834, "type": "Conrain_SAMModelLoader", "pos": [-8861.5, 411.11846923828125], "size": [269.19927978515625, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [1557, 1561], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "widgets_values": ["sam_vit_h_cloth"]}, {"id": 835, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-8266.5009765625, 129.11795043945312], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 186, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 1557, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 1558, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1559, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1560, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1579], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1575], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 836, "type": "Conrain_GroundingDinoModelLoader", "pos": [-8872.76171875, 271.8019104003906], "size": [285.80181884765625, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [1558, 1562], "slot_index": 0}], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "widgets_values": ["groundingdino_cloth"]}, {"id": 837, "type": "CR Text", "pos": [-9858.5, 179.11776733398438], "size": [217.36741638183594, 128.27645874023438], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1565, 1570, 1571, 1584], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "抠图词", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["upper garment"], "color": "#232", "bgcolor": "#353"}, {"id": 838, "type": "Conrain_GroundingDinoSAMSegment", "pos": [-7207.49560546875, 436.1183776855469], "size": [260.3999938964844, 151.95924377441406], "flags": {}, "order": 187, "mode": 0, "inputs": [{"name": "sam_model", "type": "SAM_MODEL", "link": 1561, "label": "sam_model"}, {"name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 1562, "label": "grounding_dino_model"}, {"name": "image", "type": "IMAGE", "link": 1563, "label": "image"}, {"name": "prompt", "type": "STRING", "link": 1564, "widget": {"name": "prompt"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [1587], "slot_index": 1, "shape": 3, "label": "MASK"}, {"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 2}], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "widgets_values": ["jacket", "white", 0.3], "color": "#474747", "bgcolor": "#333333"}, {"id": 839, "type": "ConrainPythonExecutor", "pos": [-9462.5, 322.1181640625], "size": [296.9554443359375, 286.73455810546875], "flags": {"collapsed": true}, "order": 51, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1565, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1566], "slot_index": 0}], "title": "需要替换的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 840, "type": "ShowText|pysssss", "pos": [-9110.5, 223.1180419921875], "size": [210, 276], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1566, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1560], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 841, "type": "Note", "pos": [-9435.5, 365.1184387207031], "size": [210, 111.45333862304688], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "title": "抠图词", "properties": {}, "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"], "color": "#432", "bgcolor": "#653"}, {"id": 842, "type": "DensePosePreprocessor", "pos": [-8737.681640625, -77.98147583007812], "size": [315, 106], "flags": {}, "order": 188, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1567}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1568], "slot_index": 0}], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "widgets_values": ["densepose_r50_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 843, "type": "ConrainMaskFromColors", "pos": [-8246.5556640625, -91.07675170898438], "size": [228.88619995117188, 124.55016326904297], "flags": {}, "order": 191, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1568}, {"name": "color_list", "type": "STRING", "link": 1569, "widget": {"name": "color_list"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1572], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 844, "type": "ConrainPythonExecutor", "pos": [-9160.283203125, 22.255279541015625], "size": [372.4948425292969, 392.6221008300781], "flags": {"collapsed": true}, "order": 52, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1570, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1569], "slot_index": 0}], "title": "densepose提取mask区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 845, "type": "ConrainPythonExecutor", "pos": [-9455.5, 560.1185302734375], "size": [300.1857604980469, 284.6385192871094], "flags": {"collapsed": true}, "order": 53, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1571, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1583], "slot_index": 0}], "title": "需要排除的mask", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 846, "type": "GrowMask", "pos": [-7872.55419921875, -84.07614135742188], "size": [210, 95.39423370361328], "flags": {}, "order": 193, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1572}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1576], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 847, "type": "Masks Add", "pos": [-6844.49560546875, 95.11788940429688], "size": [210, 46], "flags": {}, "order": 197, "mode": 0, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1573}, {"name": "masks_b", "type": "MASK", "link": 1574}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1585], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Add"}, "widgets_values": []}, {"id": 848, "type": "GrowMask", "pos": [-7882.50048828125, 242.11807250976562], "size": [210, 93.73921966552734], "flags": {}, "order": 190, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1575}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1574], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [0, false]}, {"id": 849, "type": "JWMaskResize", "pos": [-7576.49658203125, -67.88180541992188], "size": [259.6524353027344, 135.95858764648438], "flags": {}, "order": 195, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1576}, {"name": "height", "type": "INT", "link": 1577, "widget": {"name": "height"}}, {"name": "width", "type": "INT", "link": 1578, "widget": {"name": "width"}}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1573], "slot_index": 0}], "properties": {"Node name for S&R": "JWMaskResize"}, "widgets_values": [512, 512, "bilinear"]}, {"id": 850, "type": "GetImageSize+", "pos": [-7894.55517578125, 94.22164916992188], "size": [214.20001220703125, 66], "flags": {}, "order": 189, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1579}], "outputs": [{"name": "width", "type": "INT", "links": [1578, 1580], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1577, 1581], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 851, "type": "Image Blank", "pos": [-7228.49560546875, -82.88174438476562], "size": [315, 154], "flags": {}, "order": 192, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 1580, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1581, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1582], "slot_index": 0}], "properties": {"Node name for S&R": "Image Blank"}, "widgets_values": [512, 512, 255, 255, 255]}, {"id": 852, "type": "ImageToMask", "pos": [-6853.49560546875, -76.88180541992188], "size": [210, 68.49308776855469], "flags": {}, "order": 194, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1582}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1588], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 853, "type": "ShowText|pysssss", "pos": [-9109.5, 561.1185913085938], "size": [210, 276], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 1583, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [1564], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["trousers"], "trousers", "trousers"]}, {"id": 854, "type": "ConrainPythonExecutor", "pos": [-7226.49560546875, 133.11788940429688], "size": [304.83380126953125, 204.48739624023438], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1584, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1589], "slot_index": 0}], "title": "需要排除的区域", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 855, "type": "Masks Subtract", "pos": [-6557.28271484375, 106.25527954101562], "size": [210, 46], "flags": {}, "order": 198, "mode": 4, "inputs": [{"name": "masks_a", "type": "MASK", "link": 1585}, {"name": "masks_b", "type": "MASK", "link": 1586}], "outputs": [{"name": "MASKS", "type": "MASK", "links": [1596, 1599], "slot_index": 0}], "properties": {"Node name for S&R": "Masks Subtract"}, "widgets_values": []}, {"id": 856, "type": "easy ifElse", "pos": [-6546.49560546875, -81.88174438476562], "size": [210, 82.75738525390625], "flags": {}, "order": 196, "mode": 0, "inputs": [{"name": "on_true", "type": "*", "link": 1587}, {"name": "on_false", "type": "*", "link": 1588}, {"name": "boolean", "type": "BOOLEAN", "link": 1589, "widget": {"name": "boolean"}}], "outputs": [{"name": "*", "type": "*", "links": [1586], "slot_index": 0}], "properties": {"Node name for S&R": "easy ifElse"}, "widgets_values": [false]}, {"id": 862, "type": "MaskToImage", "pos": [-6398.1064453125, 202.65988159179688], "size": [188.457763671875, 48.499874114990234], "flags": {}, "order": 199, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1596}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1597, 1602], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 869, "type": "Note", "pos": [-9315.283203125, -28.744720458984375], "size": [210, 60], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"], "color": "#432", "bgcolor": "#653"}, {"id": 870, "type": "VAELoader", "pos": [-10420, -5170], "size": [315, 58], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [1664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 871, "type": "FluxGuidance", "pos": [-9500, -4130], "size": [211.60000610351562, 58], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1607}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1717], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 872, "type": "CR Apply Multi-ControlNet", "pos": [-8340, -4320], "size": [274.56842041015625, 98], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "base_positive", "type": "CONDITIONING", "link": 1608}, {"name": "base_negative", "type": "CONDITIONING", "link": 1609}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": 1610}], "outputs": [{"name": "base_pos", "type": "CONDITIONING", "links": [1616], "slot_index": 0, "shape": 3}, {"name": "base_neg", "type": "CONDITIONING", "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "widgets_values": ["On"]}, {"id": 873, "type": "DualCLIPLoader", "pos": [-10420, -5360], "size": [315, 106], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [1667], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 874, "type": "UNETLoader", "pos": [-10420, -5530], "size": [315, 82], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1666], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 875, "type": "CLIPTextEncode", "pos": [-9490, -4030], "size": [210, 125.79536437988281], "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1611}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1609, 1718], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 876, "type": "CLIPTextEncode", "pos": [-9800, -4080], "size": [210, 125.79536437988281], "flags": {"collapsed": false}, "order": 84, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1612}, {"name": "text", "type": "STRING", "link": 1613, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1607], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 877, "type": "DifferentialDiffusion", "pos": [-9790, -4150], "size": [210, 26], "flags": {"collapsed": true}, "order": 80, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1614}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 878, "type": "BasicGuider", "pos": [-7720, -4180], "size": [263.1893615722656, 46], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1615, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1616, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1618], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 879, "type": "SamplerCustomAdvanced", "pos": [-7310, -4310], "size": [314.************, 106], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1617, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1618, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1619, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1620, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1621, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [1628], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 880, "type": "ConrainPythonExecutor", "pos": [-9800, -4750], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 103, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1622, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1626], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 881, "type": "ConrainPythonExecutor", "pos": [-9800, -4830], "size": [354.8526611328125, 193.51210021972656], "flags": {"collapsed": true}, "order": 102, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1623, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1625], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 882, "type": "ImageCrop+", "pos": [-9550, -5020], "size": [316.8935546875, 239.64944458007812], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1624}, {"name": "width", "type": "INT", "link": 1625, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1626, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1729], "slot_index": 0}, {"name": "x", "type": "INT"}, {"name": "y", "type": "INT"}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 883, "type": "Reroute", "pos": [-9100, -5540], "size": [75, 26], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1627}], "outputs": [{"name": "", "type": "IMAGE", "links": [1637]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 884, "type": "Sapiens<PERSON><PERSON>der", "pos": [-9060, -5400], "size": [659.2952270507812, 298], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1636], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 885, "type": "VAEDecode", "pos": [-6900, -4270], "size": [190.54541015625, 46], "flags": {}, "order": 137, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1628}, {"name": "vae", "type": "VAE", "link": 1629}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1731], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 886, "type": "Zoe-DepthMapPreprocessor", "pos": [-9240, -3840], "size": [210, 58], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1630}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1634, 1722, 1723], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "widgets_values": [1024]}, {"id": 887, "type": "Reroute", "pos": [-9460, -3950], "size": [75, 26], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1631}], "outputs": [{"name": "", "type": "IMAGE", "links": [1630, 1632, 1720]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 888, "type": "<PERSON><PERSON>", "pos": [-9240, -3680], "size": [212.60569763183594, 82], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1632}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1724, 1760], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [0.2, 0.8]}, {"id": 889, "type": "RandomNoise", "pos": [-7720, -4320], "size": [255.33419799804688, 82], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1617], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [960145982391288, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 890, "type": "KSamplerSelect", "pos": [-7720, -4050], "size": [314.************, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1619], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 891, "type": "ConrainGrowMaskWithBlur", "pos": [-8980, -4890], "size": [340.20001220703125, 246], "flags": {}, "order": 125, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1633}], "outputs": [{"name": "mask", "type": "MASK", "links": [1715, 1721], "slot_index": 0}, {"name": "mask_inverted", "type": "MASK"}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 892, "type": "PreviewImage", "pos": [-8900, -4020], "size": [210, 246], "flags": {}, "order": 126, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1634}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 893, "type": "Reroute", "pos": [-10000, -5040], "size": [75, 26], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1635}], "outputs": [{"name": "", "type": "IMAGE", "links": [1624, 1756]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 895, "type": "CR Image Input Switch", "pos": [-10090, -2690], "size": [210, 74], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1638, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 1639, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1659], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 896, "type": "CR Image Input Switch", "pos": [-10090, -2540], "size": [210, 74], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1640, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 1641, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1660], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 897, "type": "JWStringToInteger", "pos": [-10430, -2680], "size": [210, 58], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1639], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 898, "type": "JWStringToInteger", "pos": [-10450, -2530], "size": [213.92233276367188, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1641], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 899, "type": "Reroute", "pos": [-6940, -2080], "size": [75, 26], "flags": {}, "order": 149, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1642}], "outputs": [{"name": "", "type": "MASK", "links": [1765]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 900, "type": "Reroute", "pos": [-6960, -2160], "size": [75, 26], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1643}], "outputs": [{"name": "", "type": "IMAGE", "links": [1764]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 901, "type": "Reroute", "pos": [-6940, -2010], "size": [75, 26], "flags": {}, "order": 146, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1644}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1766]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 902, "type": "UpscaleSizeCalculator", "pos": [-9400, -1810], "size": [210, 136.1963348388672], "flags": {}, "order": 145, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1645}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [1743], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [1024]}, {"id": 903, "type": "Reroute", "pos": [-7970, -2140], "size": [75, 26], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1646}], "outputs": [{"name": "", "type": "MODEL", "links": [1665, 1730]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 904, "type": "FluxGuidance", "pos": [-7880, -1650], "size": [211.60000610351562, 58], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1647, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1749], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [2]}, {"id": 905, "type": "ConditioningZeroOut", "pos": [-7880, -1790], "size": [222.26809692382812, 26], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1648}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1750], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 906, "type": "RandomNoise", "pos": [-7330, -1870], "size": [221.2050323486328, 83.35130310058594], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1690], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [0, "fixed"]}, {"id": 907, "type": "BasicGuider", "pos": [-7310, -2110], "size": [161.1999969482422, 46], "flags": {}, "order": 165, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1649, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1650}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1691], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 908, "type": "KSamplerSelect", "pos": [-7340, -1990], "size": [210, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1692], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpm_2"]}, {"id": 909, "type": "SapiensSampler", "pos": [-10080, -1960], "size": [247.4663848876953, 259.6097412109375], "flags": {}, "order": 143, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1651}, {"name": "image", "type": "IMAGE", "link": 1652}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [1705], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 910, "type": "CLIPTextEncode", "pos": [-8030, -2960], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": false}, "order": 58, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1653}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1678], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}, {"id": 911, "type": "ImageResize+", "pos": [-9440, -2730], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1654}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1708, 1709], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 912, "type": "UltralyticsDetectorProvider", "pos": [-9810, -2520], "size": [315, 78], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1707], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 913, "type": "MaskFastGrow", "pos": [-8840, -3140], "size": [210, 178], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1655}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1710], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 914, "type": "Reroute", "pos": [-10590, -2380], "size": [75, 26], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1656, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1738]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 915, "type": "Reroute", "pos": [-10620, -1580], "size": [75, 26], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1657}], "outputs": [{"name": "", "type": "CLIP", "links": [1701]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 916, "type": "ImpactMakeImageList", "pos": [-9770, -2720], "size": [231.68582153320312, 104.573486328125], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1658}, {"name": "image2", "type": "IMAGE", "link": 1659}, {"name": "image3", "type": "IMAGE", "link": 1660}, {"name": "image4", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1654], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "widgets_values": []}, {"id": 917, "type": "ImpactSEGSOrderedFilter", "pos": [-9110, -3140], "size": [210, 158.96408081054688], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1661}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1706], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 918, "type": "ConrainRandomPrompts", "pos": [-10460, -3960], "size": [602.1065673828125, 245.61325073242188], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [1613, 1656], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 Golden alabaster European female model,20-year-old,  long wavy auburn hair, ", 991, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 919, "type": "Reroute", "pos": [-10220, -4160], "size": [75, 26], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1662}], "outputs": [{"name": "", "type": "CLIP", "links": [1611, 1612, 1657]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#432", "bgcolor": "#653"}, {"id": 920, "type": "Reroute", "pos": [-8250, -2130], "size": [75, 26], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1663}], "outputs": [{"name": "", "type": "VAE", "links": [1726, 1751]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 921, "type": "Reroute", "pos": [-9990, -5170], "size": [75, 26], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1664}], "outputs": [{"name": "", "type": "VAE", "links": [1629, 1663, 1719]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#322", "bgcolor": "#533"}, {"id": 922, "type": "DifferentialDiffusion", "pos": [-7890, -1920], "size": [210, 26], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1649], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 923, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-10010, -5530], "size": [516.7993774414062, 126], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1666}, {"name": "clip", "type": "CLIP", "link": 1667}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1668], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [1662], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/Katriina-小头2_copy_copy_10352_20250326_143128/<PERSON><PERSON><PERSON>-小头2_copy_copy_10352_20250326_143128-flux/Katriina-小头2_copy_copy_10352_20250326_143128-flux.safetensors", "1", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 924, "type": "Reroute", "pos": [-10230, -4290], "size": [75, 26], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1668}], "outputs": [{"name": "", "type": "MODEL", "links": [1614, 1646, 1761]}], "properties": {"showOutputText": false, "horizontal": false}, "color": "#223", "bgcolor": "#335"}, {"id": 925, "type": "CLIPTextEncode", "pos": [-7950, -3120], "size": [210, 96], "flags": {"collapsed": false}, "order": 95, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1669}, {"name": "text", "type": "STRING", "link": 1670, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1677], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 926, "type": "ControlNetLoader", "pos": [-7710, -2920], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1674], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 927, "type": "InstantIDFaceAnalysis", "pos": [-7710, -3040], "size": [315, 58], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [1673], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 928, "type": "InstantIDModelLoader", "pos": [-7710, -3140], "size": [315, 58], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [1672], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 929, "type": "DetectFaces", "pos": [-9070, -2600], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1671}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [1682], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 930, "type": "ApplyInstantID", "pos": [-7290, -3170], "size": [210, 266], "flags": {}, "order": 157, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 1672, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 1673, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 1674, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 1675, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1676, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 1677, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 1678, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 1679, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "link": null, "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1733], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [1684], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [1685], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 931, "type": "easy imageListToImageBatch", "pos": [-8830, -2850], "size": [222.84095764160156, 26], "flags": {}, "order": 124, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1680}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1671], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 932, "type": "DetectFaces", "pos": [-8330, -3020], "size": [210, 126], "flags": {}, "order": 151, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1681}, {"name": "mask", "type": "MASK", "link": null, "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [1737], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 933, "type": "CropFaces", "pos": [-8830, -2600], "size": [221.15121459960938, 146], "flags": {}, "order": 132, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1682}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [1683], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 934, "type": "Reroute", "pos": [-8490, -3170], "size": [75, 26], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1683}], "outputs": [{"name": "", "type": "IMAGE", "links": [1675]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 935, "type": "InpaintModelConditioning", "pos": [-7040, -2980], "size": [210, 138], "flags": {}, "order": 159, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1684, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 1685}, {"name": "vae", "type": "VAE", "link": 1686}, {"name": "pixels", "type": "IMAGE", "link": 1687}, {"name": "mask", "type": "MASK", "link": 1688}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1734], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [1735], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1736], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 936, "type": "Reroute", "pos": [-8490, -3090], "size": [75, 26], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1689, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1670]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 937, "type": "SamplerCustomAdvanced", "pos": [-7010, -1910], "size": [236.8000030517578, 109.************], "flags": {}, "order": 167, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1690, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1691, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1692, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1693, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1694}], "outputs": [{"name": "output", "type": "LATENT", "links": [1725], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 938, "type": "WarpFacesBack", "pos": [-6470, -1780], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 170, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1695}, {"name": "face", "type": "FACE", "link": 1696}, {"name": "crop", "type": "IMAGE", "link": 1697}, {"name": "mask", "type": "MASK", "link": 1698}, {"name": "warp", "type": "WARP", "link": 1699}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1767], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 939, "type": "Reroute", "pos": [-10370, -2140], "size": [75, 26], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1700}], "outputs": [{"name": "", "type": "IMAGE", "links": [1643, 1652, 1704]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 940, "type": "Sapiens<PERSON><PERSON>der", "pos": [-10420, -1960], "size": [283.3903503417969, 298], "flags": {}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1651, 1739], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 941, "type": "Reroute", "pos": [-8690, -1560], "size": [75, 26], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1701}], "outputs": [{"name": "", "type": "CLIP", "links": [1702]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 942, "type": "CLIPTextEncode", "pos": [-8340, -1640], "size": [296.32208251953125, 77.2895278930664], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1702}, {"name": "text", "type": "STRING", "link": 1703, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1647, 1648], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 943, "type": "BoundedImageCropWithMask_v3_LR", "pos": [-9710, -2100], "size": [285.6000061035156, 149.66432189941406], "flags": {}, "order": 144, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1704}, {"name": "mask", "type": "MASK", "link": 1705}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1645, 1742], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1644], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [1741], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [20, 20]}, {"id": 944, "type": "SegsToCombinedMask", "pos": [-8950, -3190], "size": [289.79998779296875, 26], "flags": {"collapsed": true}, "order": 110, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1706}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1655], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 945, "type": "ImpactSimpleDetectorSEGS", "pos": [-9440, -3150], "size": [277.6741943359375, 319.97015380859375], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1707}, {"name": "image", "type": "IMAGE", "link": 1708}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1661], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 946, "type": "Bounded Image Crop with Mask", "pos": [-9110, -2840], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1709}, {"name": "mask", "type": "MASK", "link": 1710}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1680], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 947, "type": "VAEDecode", "pos": [-6700, -2640], "size": [140, 46], "flags": {"collapsed": false}, "order": 163, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1711}, {"name": "vae", "type": "VAE", "link": 1712}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1752], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 948, "type": "Reroute", "pos": [-7510, -2860], "size": [75, 26], "flags": {}, "order": 154, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1713}], "outputs": [{"name": "", "type": "IMAGE", "links": [1679, 1687]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 949, "type": "CropFaces", "pos": [-8020, -2800], "size": [221.15121459960938, 146], "flags": {}, "order": 153, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1714}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [1713, 1727, 1740, 1771], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [1699], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 950, "type": "MaskPreview+", "pos": [-8310, -4890], "size": [210, 246], "flags": {}, "order": 130, "mode": 4, "inputs": [{"name": "mask", "type": "MASK", "link": 1715}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 951, "type": "RepeatLatentBatch", "pos": [-7320, -1670], "size": [210, 80.70018768310547], "flags": {}, "order": 166, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1716}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1694], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [1]}, {"id": 952, "type": "InpaintModelConditioning", "pos": [-8900, -4320], "size": [216.59999084472656, 138], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1717}, {"name": "negative", "type": "CONDITIONING", "link": 1718}, {"name": "vae", "type": "VAE", "link": 1719}, {"name": "pixels", "type": "IMAGE", "link": 1720}, {"name": "mask", "type": "MASK", "link": 1721}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1608], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1621], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 953, "type": "CR Multi-ControlNet Stack", "pos": [-8570, -3850], "size": [563.9595947265625, 454], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "image_1", "type": "IMAGE", "link": 1722, "shape": 7}, {"name": "image_2", "type": "IMAGE", "link": 1723, "shape": 7}, {"name": "image_3", "type": "IMAGE", "link": 1724, "shape": 7}, {"name": "controlnet_stack", "type": "CONTROL_NET_STACK", "link": null, "shape": 7}], "outputs": [{"name": "CONTROLNET_STACK", "type": "CONTROL_NET_STACK", "links": [1610], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.2, 0.2, 0.8, "On", "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", 0.4, 0, 0.6]}, {"id": 954, "type": "VAEDecode", "pos": [-7020, -1670], "size": [210, 46], "flags": {"collapsed": false}, "order": 168, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1725}, {"name": "vae", "type": "VAE", "link": 1726}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1728], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 956, "type": "Reroute", "pos": [-9390, -4540], "size": [75, 26], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1729}], "outputs": [{"name": "", "type": "IMAGE", "links": [1627, 1631]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 957, "type": "BasicScheduler", "pos": [-7590, -1710], "size": [210, 106], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1730}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1693], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", 8, 0.1]}, {"id": 958, "type": "CR Upscale Image", "pos": [-6950, -4130], "size": [315, 222], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1731}, {"name": "resize_width", "type": "INT", "link": 1732, "widget": {"name": "resize_width"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1745], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, 1024, "lanc<PERSON>s", "true", 8]}, {"id": 959, "type": "CheckpointLoaderSimple", "pos": [-8330, -3190], "size": [314.34735107421875, 128.43458557128906], "flags": {}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1676], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [1653, 1669], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [1686, 1712], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 960, "type": "K<PERSON><PERSON><PERSON>", "pos": [-6780, -3000], "size": [261.8017578125, 262], "flags": {}, "order": 162, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1733}, {"name": "positive", "type": "CONDITIONING", "link": 1734}, {"name": "negative", "type": "CONDITIONING", "link": 1735}, {"name": "latent_image", "type": "LATENT", "link": 1736}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1711], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42029499340256, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.5]}, {"id": 961, "type": "OrderedFaceFilter", "pos": [-8330, -2840], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 152, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1737}], "outputs": [{"name": "filtered", "type": "FACE", "links": [1696, 1714], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 962, "type": "Reroute", "pos": [-8670, -2380], "size": [75, 26], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1738, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [1689, 1703]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 963, "type": "SapiensSampler", "pos": [-8320, -2620], "size": [265.8616027832031, 258], "flags": {}, "order": 155, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1739}, {"name": "image", "type": "IMAGE", "link": 1740}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "23,24,25,26,27", false, 255, 255, 255]}, {"id": 964, "type": "ConrainGrowMaskWithBlur", "pos": [-9170, -2110], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 147, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1741}], "outputs": [{"name": "mask", "type": "MASK", "links": [1642], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 965, "type": "ImageScaleBy", "pos": [-9060, -1770], "size": [250.58731079101562, 109.74829864501953], "flags": {}, "order": 148, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1742, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 1743, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1744], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 966, "type": "Reroute", "pos": [-8730, -1770], "size": [75, 26], "flags": {}, "order": 150, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1744}], "outputs": [{"name": "", "type": "IMAGE", "links": [1681, 1695]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 972, "type": "ConrainGrowMaskWithBlur", "pos": [-7060, -2730], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 160, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1748}], "outputs": [{"name": "mask", "type": "MASK", "links": [1698], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [40, 0, false, false, 0, 1, 1, false]}, {"id": 973, "type": "InpaintModelConditioning", "pos": [-7610, -2110], "size": [216.59999084472656, 138], "flags": {}, "order": 164, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1749}, {"name": "negative", "type": "CONDITIONING", "link": 1750, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 1751}, {"name": "pixels", "type": "IMAGE", "link": 1752}, {"name": "mask", "type": "MASK", "link": 1753, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1650], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1716], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [false]}, {"id": 974, "type": "MaskFastGrow", "pos": [-7390, -2790], "size": [210, 178], "flags": {}, "order": 158, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1754}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1688, 1748, 1755], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 5, 4, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 975, "type": "MaskFastGrow", "pos": [-7390, -2550], "size": [212.8609161376953, 178], "flags": {}, "order": 161, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1755}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1753], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [true, 20, 10, 0, 1, true]}, {"id": 976, "type": "GetImageSize+", "pos": [-10060, -4850], "size": [214.20001220703125, 66], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1756}], "outputs": [{"name": "width", "type": "INT", "links": [1623], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1622], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 978, "type": "ImageResize+", "pos": [-10220, -4590], "size": [315, 218], "flags": {"collapsed": true}, "order": 77, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1758}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1635], "slot_index": 0}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 980, "type": "PreviewImage", "pos": [-8900, -3650], "size": [210, 246], "flags": {}, "order": 128, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1760}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 982, "type": "BasicScheduler", "pos": [-7710, -3910], "size": [309.76611328125, 106], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1761, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1620], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 0.9], "color": "#232", "bgcolor": "#353"}, {"id": 987, "type": "easy humanSegmentation", "pos": [-7730, -2820], "size": [300, 500], "flags": {"collapsed": false}, "order": 156, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1771}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [1754], "slot_index": 1}, {"name": "bbox", "type": "BBOX", "slot_index": 2}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 977, "type": "Reroute", "pos": [-10390, -4590], "size": [75, 26], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1772}], "outputs": [{"name": "", "type": "IMAGE", "links": [1758, 1759]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 979, "type": "GetImageSize+", "pos": [-7270, -4130], "size": [214.20001220703125, 66], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1759}], "outputs": [{"name": "width", "type": "INT", "links": [1732, 1746], "slot_index": 0}, {"name": "height", "type": "INT", "links": [1747], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 968, "type": "ImageScale", "pos": [-7340, -3890], "size": [315, 130], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1745}, {"name": "width", "type": "INT", "link": 1746, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 1747, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1700, 1763], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 981, "type": "Text String", "pos": [-7360, -3660], "size": [315, 190], "flags": {}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1769], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1770], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存换头结果图", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250605/100752/329395", "product_2214957", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 985, "type": "Bounded Image Blend with Mask", "pos": [-6540, -2110], "size": [243.60000610351562, 172.84213256835938], "flags": {}, "order": 171, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1764}, {"name": "target_mask", "type": "MASK", "link": 1765}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1766}, {"name": "source", "type": "IMAGE", "link": 1767}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1762, 1768, 1776], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 462, "type": "LoadImage", "pos": [-12920, 2096], "size": [315, 314], "flags": {}, "order": 38, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1257], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["14d1fa373786449da51799a0a251dd5d.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 456, "type": "LoadImage", "pos": [-12934, 1702], "size": [315, 314], "flags": {}, "order": 39, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [880], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product_2971815_DPvfb.png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 433, "type": "CR Text", "pos": [-4473.509765625, 3366.515869140625], "size": [211.76846313476562, 168.80604553222656], "flags": {}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [846], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "tryon生成图片张数", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["4"], "color": "#232", "bgcolor": "#353"}, {"id": 435, "type": "JWStringToInteger", "pos": [-3942.9482421875, 3091.456787109375], "size": [210, 56.551239013671875], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 847, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [849, 1499], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 894, "type": "SapiensSampler", "pos": [-8330, -5490], "size": [315, 258], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1636}, {"name": "image", "type": "IMAGE", "link": 1637}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [1633], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 463, "type": "LoadImage", "pos": [-12939, 1306], "size": [315, 314], "flags": {}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1407, 1417, 1772], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1995).png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 955, "type": "easy imageColorMatch", "pos": [-6750, -1730], "size": [210, 102], "flags": {"collapsed": false}, "order": 169, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 1727}, {"name": "image_target", "type": "IMAGE", "link": 1728}], "outputs": [{"name": "image", "type": "IMAGE", "links": [1697], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 863, "type": "PreviewImage", "pos": [-6175.1064453125, -17.339996337890625], "size": [210, 246], "flags": {}, "order": 201, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1597}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 451, "type": "PreviewImage", "pos": [-1945.509765625, 2759.515869140625], "size": [210, 246], "flags": {}, "order": 275, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 879}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 308, "type": "ConrainImageSave", "pos": [-1860, 3280], "size": [231.75296020507812, 266], "flags": {}, "order": 274, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 873, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 874, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 986, "type": "ConrainImageSave", "pos": [-6970, -3680], "size": [320, 266], "flags": {}, "order": 173, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1768, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1769, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1770, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 758, "type": "CR Text", "pos": [-12326, 1677], "size": [210, 106.75990295410156], "flags": {}, "order": 42, "mode": 0, "inputs": [], "outputs": [{"name": "text", "type": "*", "links": [1405], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "输入图是否要换头", "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["1"], "color": "#232", "bgcolor": "#353"}, {"id": 983, "type": "PreviewImage", "pos": [-5947.119140625, -2106.30126953125], "size": [393.20245361328125, 409.597412109375], "flags": {}, "order": 172, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1762}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 984, "type": "PreviewImage", "pos": [-6560, -3890], "size": [236.9750518798828, 273.07080078125], "flags": {}, "order": 141, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 1763}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 865, "type": "ConrainImageSave", "pos": [-6209.28271484375, 457.2551574707031], "size": [231.75296020507812, 266], "flags": {}, "order": 202, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1602, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 1600, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 1601, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 864, "type": "Text String", "pos": [-6784.81591796875, 473.802734375], "size": [300.3023681640625, 216.65859985351562], "flags": {}, "order": 43, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [1600], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [1601], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存参看图的mask", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250719/100014/235799", "mask_product_1011153", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 969, "type": "LoadImage", "pos": [-10450, -3150], "size": [235.8109893798828, 314], "flags": {}, "order": 44, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1658], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1885).png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 970, "type": "LoadImage", "pos": [-10110, -3150], "size": [249.60922241210938, 314], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1638], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1887).png", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 971, "type": "LoadImage", "pos": [-9770, -3150], "size": [234.48504638671875, 314], "flags": {}, "order": 46, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1640], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["pasted/image (1886).png", "image"], "color": "#232", "bgcolor": "#353"}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [513, 277, 0, 290, 0, "CONDITIONING"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1257, 462, 0, 407, 0, "*"], [1405, 758, 0, 759, 0, "*"], [1406, 760, 0, 759, 1, "*"], [1407, 463, 0, 761, 0, "*"], [1409, 759, 0, 761, 2, "BOOLEAN"], [1416, 757, 0, 763, 0, "*"], [1417, 463, 0, 763, 1, "*"], [1418, 763, 0, 761, 1, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1477, 321, 0, 788, 0, "IMAGE"], [1497, 761, 0, 402, 0, "*"], [1498, 289, 0, 792, 0, "*"], [1499, 435, 0, 792, 1, "*"], [1500, 792, 0, 288, 0, "STITCH"], [1501, 276, 0, 793, 0, "IMAGE"], [1502, 793, 0, 288, 1, "IMAGE"], [1506, 461, 0, 797, 2, "MASK"], [1507, 408, 0, 800, 0, "IMAGE"], [1508, 800, 0, 799, 0, "INT"], [1509, 800, 1, 799, 1, "INT"], [1510, 799, 0, 797, 0, "IMAGE"], [1512, 461, 0, 331, 0, "*"], [1513, 797, 0, 773, 0, "*"], [1515, 408, 0, 797, 1, "IMAGE"], [1557, 834, 0, 835, 0, "SAM_MODEL"], [1558, 836, 0, 835, 1, "GROUNDING_DINO_MODEL"], [1559, 833, 0, 835, 2, "IMAGE"], [1560, 840, 0, 835, 3, "STRING"], [1561, 834, 0, 838, 0, "SAM_MODEL"], [1562, 836, 0, 838, 1, "GROUNDING_DINO_MODEL"], [1563, 833, 0, 838, 2, "IMAGE"], [1564, 853, 0, 838, 3, "STRING"], [1565, 837, 0, 839, 0, "*"], [1566, 839, 0, 840, 0, "STRING"], [1567, 833, 0, 842, 0, "IMAGE"], [1568, 842, 0, 843, 0, "IMAGE"], [1569, 844, 0, 843, 1, "STRING"], [1570, 837, 0, 844, 0, "*"], [1571, 837, 0, 845, 0, "*"], [1572, 843, 0, 846, 0, "MASK"], [1573, 849, 0, 847, 0, "MASK"], [1574, 848, 0, 847, 1, "MASK"], [1575, 835, 1, 848, 0, "MASK"], [1576, 846, 0, 849, 0, "MASK"], [1577, 850, 1, 849, 1, "INT"], [1578, 850, 0, 849, 2, "INT"], [1579, 835, 0, 850, 0, "IMAGE"], [1580, 850, 0, 851, 0, "INT"], [1581, 850, 1, 851, 1, "INT"], [1582, 851, 0, 852, 0, "IMAGE"], [1583, 845, 0, 853, 0, "STRING"], [1584, 837, 0, 854, 0, "*"], [1585, 847, 0, 855, 0, "MASK"], [1586, 856, 0, 855, 1, "MASK"], [1587, 838, 1, 856, 0, "*"], [1588, 852, 0, 856, 1, "*"], [1589, 854, 0, 856, 2, "BOOLEAN"], [1596, 855, 0, 862, 0, "MASK"], [1597, 862, 0, 863, 0, "IMAGE"], [1598, 328, 0, 833, 0, "*"], [1599, 855, 0, 315, 0, "MASK"], [1600, 864, 0, 865, 1, "STRING"], [1601, 864, 1, 865, 2, "STRING"], [1602, 862, 0, 865, 0, "IMAGE"], [1607, 876, 0, 871, 0, "CONDITIONING"], [1608, 952, 0, 872, 0, "CONDITIONING"], [1609, 875, 0, 872, 1, "CONDITIONING"], [1610, 953, 0, 872, 2, "CONTROL_NET_STACK"], [1611, 919, 0, 875, 0, "CLIP"], [1612, 919, 0, 876, 0, "CLIP"], [1613, 918, 0, 876, 1, "STRING"], [1614, 924, 0, 877, 0, "MODEL"], [1615, 877, 0, 878, 0, "MODEL"], [1616, 872, 0, 878, 1, "CONDITIONING"], [1617, 889, 0, 879, 0, "NOISE"], [1618, 878, 0, 879, 1, "GUIDER"], [1619, 890, 0, 879, 2, "SAMPLER"], [1620, 982, 0, 879, 3, "SIGMAS"], [1621, 952, 2, 879, 4, "LATENT"], [1622, 976, 1, 880, 0, "*"], [1623, 976, 0, 881, 0, "*"], [1624, 893, 0, 882, 0, "IMAGE"], [1625, 881, 0, 882, 1, "INT"], [1626, 880, 0, 882, 2, "INT"], [1627, 956, 0, 883, 0, "*"], [1628, 879, 0, 885, 0, "LATENT"], [1629, 921, 0, 885, 1, "VAE"], [1630, 887, 0, 886, 0, "IMAGE"], [1631, 956, 0, 887, 0, "*"], [1632, 887, 0, 888, 0, "IMAGE"], [1633, 894, 4, 891, 0, "MASK"], [1634, 886, 0, 892, 0, "IMAGE"], [1635, 978, 0, 893, 0, "*"], [1636, 884, 0, 894, 0, "MODEL_SAPIEN"], [1637, 883, 0, 894, 1, "IMAGE"], [1638, 970, 0, 895, 0, "IMAGE"], [1639, 897, 0, 895, 2, "INT"], [1640, 971, 0, 896, 0, "IMAGE"], [1641, 898, 0, 896, 2, "INT"], [1642, 964, 0, 899, 0, "*"], [1643, 939, 0, 900, 0, "*"], [1644, 943, 1, 901, 0, "*"], [1645, 943, 0, 902, 0, "IMAGE"], [1646, 924, 0, 903, 0, "*"], [1647, 942, 0, 904, 0, "CONDITIONING"], [1648, 942, 0, 905, 0, "CONDITIONING"], [1649, 922, 0, 907, 0, "MODEL"], [1650, 973, 0, 907, 1, "CONDITIONING"], [1651, 940, 0, 909, 0, "MODEL_SAPIEN"], [1652, 939, 0, 909, 1, "IMAGE"], [1653, 959, 1, 910, 0, "CLIP"], [1654, 916, 0, 911, 0, "IMAGE"], [1655, 944, 0, 913, 0, "MASK"], [1656, 918, 0, 914, 0, "*"], [1657, 919, 0, 915, 0, "*"], [1658, 969, 0, 916, 0, "IMAGE"], [1659, 895, 0, 916, 1, "IMAGE"], [1660, 896, 0, 916, 2, "IMAGE"], [1661, 945, 0, 917, 0, "SEGS"], [1662, 923, 1, 919, 0, "*"], [1663, 921, 0, 920, 0, "*"], [1664, 870, 0, 921, 0, "*"], [1665, 903, 0, 922, 0, "MODEL"], [1666, 874, 0, 923, 0, "MODEL"], [1667, 873, 0, 923, 1, "CLIP"], [1668, 923, 0, 924, 0, "*"], [1669, 959, 1, 925, 0, "CLIP"], [1670, 936, 0, 925, 1, "STRING"], [1671, 931, 0, 929, 0, "IMAGE"], [1672, 928, 0, 930, 0, "INSTANTID"], [1673, 927, 0, 930, 1, "FACEANALYSIS"], [1674, 926, 0, 930, 2, "CONTROL_NET"], [1675, 934, 0, 930, 3, "IMAGE"], [1676, 959, 0, 930, 4, "MODEL"], [1677, 925, 0, 930, 5, "CONDITIONING"], [1678, 910, 0, 930, 6, "CONDITIONING"], [1679, 948, 0, 930, 7, "IMAGE"], [1680, 946, 0, 931, 0, "IMAGE"], [1681, 966, 0, 932, 0, "IMAGE"], [1682, 929, 0, 933, 0, "FACE"], [1683, 933, 0, 934, 0, "*"], [1684, 930, 1, 935, 0, "CONDITIONING"], [1685, 930, 2, 935, 1, "CONDITIONING"], [1686, 959, 2, 935, 2, "VAE"], [1687, 948, 0, 935, 3, "IMAGE"], [1688, 974, 0, 935, 4, "MASK"], [1689, 962, 0, 936, 0, "*"], [1690, 906, 0, 937, 0, "NOISE"], [1691, 907, 0, 937, 1, "GUIDER"], [1692, 908, 0, 937, 2, "SAMPLER"], [1693, 957, 0, 937, 3, "SIGMAS"], [1694, 951, 0, 937, 4, "LATENT"], [1695, 966, 0, 938, 0, "IMAGE"], [1696, 961, 0, 938, 1, "FACE"], [1697, 955, 0, 938, 2, "IMAGE"], [1698, 972, 0, 938, 3, "MASK"], [1699, 949, 2, 938, 4, "WARP"], [1700, 968, 0, 939, 0, "*"], [1701, 915, 0, 941, 0, "*"], [1702, 941, 0, 942, 0, "CLIP"], [1703, 962, 0, 942, 1, "STRING"], [1704, 939, 0, 943, 0, "IMAGE"], [1705, 909, 4, 943, 1, "MASK"], [1706, 917, 0, 944, 0, "SEGS"], [1707, 912, 0, 945, 0, "BBOX_DETECTOR"], [1708, 911, 0, 945, 1, "IMAGE"], [1709, 911, 0, 946, 0, "IMAGE"], [1710, 913, 0, 946, 1, "MASK"], [1711, 960, 0, 947, 0, "LATENT"], [1712, 959, 2, 947, 1, "VAE"], [1713, 949, 0, 948, 0, "*"], [1714, 961, 0, 949, 0, "FACE"], [1715, 891, 0, 950, 0, "MASK"], [1716, 973, 2, 951, 0, "LATENT"], [1717, 871, 0, 952, 0, "CONDITIONING"], [1718, 875, 0, 952, 1, "CONDITIONING"], [1719, 921, 0, 952, 2, "VAE"], [1720, 887, 0, 952, 3, "IMAGE"], [1721, 891, 0, 952, 4, "MASK"], [1722, 886, 0, 953, 0, "IMAGE"], [1723, 886, 0, 953, 1, "IMAGE"], [1724, 888, 0, 953, 2, "IMAGE"], [1725, 937, 0, 954, 0, "LATENT"], [1726, 920, 0, 954, 1, "VAE"], [1727, 949, 0, 955, 0, "IMAGE"], [1728, 954, 0, 955, 1, "IMAGE"], [1729, 882, 0, 956, 0, "*"], [1730, 903, 0, 957, 0, "MODEL"], [1731, 885, 0, 958, 0, "IMAGE"], [1732, 979, 0, 958, 1, "INT"], [1733, 930, 0, 960, 0, "MODEL"], [1734, 935, 0, 960, 1, "CONDITIONING"], [1735, 935, 1, 960, 2, "CONDITIONING"], [1736, 935, 2, 960, 3, "LATENT"], [1737, 932, 0, 961, 0, "FACE"], [1738, 914, 0, 962, 0, "*"], [1739, 940, 0, 963, 0, "MODEL_SAPIEN"], [1740, 949, 0, 963, 1, "IMAGE"], [1741, 943, 2, 964, 0, "MASK"], [1742, 943, 0, 965, 0, "IMAGE"], [1743, 902, 0, 965, 1, "FLOAT"], [1744, 965, 0, 966, 0, "*"], [1745, 958, 0, 968, 0, "IMAGE"], [1746, 979, 0, 968, 1, "INT"], [1747, 979, 1, 968, 2, "INT"], [1748, 974, 0, 972, 0, "MASK"], [1749, 904, 0, 973, 0, "CONDITIONING"], [1750, 905, 0, 973, 1, "CONDITIONING"], [1751, 920, 0, 973, 2, "VAE"], [1752, 947, 0, 973, 3, "IMAGE"], [1753, 975, 0, 973, 4, "MASK"], [1754, 987, 1, 974, 0, "MASK"], [1755, 974, 0, 975, 0, "MASK"], [1756, 893, 0, 976, 0, "IMAGE"], [1758, 977, 0, 978, 0, "IMAGE"], [1759, 977, 0, 979, 0, "IMAGE"], [1760, 888, 0, 980, 0, "IMAGE"], [1761, 924, 0, 982, 0, "MODEL"], [1762, 985, 0, 983, 0, "IMAGE"], [1763, 968, 0, 984, 0, "IMAGE"], [1764, 900, 0, 985, 0, "IMAGE"], [1765, 899, 0, 985, 1, "MASK"], [1766, 901, 0, 985, 2, "IMAGE_BOUNDS"], [1767, 938, 0, 985, 3, "IMAGE"], [1768, 985, 0, 986, 0, "IMAGE"], [1769, 981, 0, 986, 1, "STRING"], [1770, 981, 1, 986, 2, "STRING"], [1771, 949, 0, 987, 0, "IMAGE"], [1772, 463, 0, 977, 0, "*"], [1776, 985, 0, 757, 0, "*"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4528.39404296875, 1173.5872802734375, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "处理服装图mask", "bounding": [-7910.73583984375, 2524.262939453125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "处理参考图mask", "bounding": [-7866.99169921875, 1201.8798828125, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4531.9833984375, 2540.28515625, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "图片尺寸最大2048", "bounding": [-11264.9794921875, 1178.438232421875, 3091.599609375, 1692.7294921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 26, "title": "Group", "bounding": [-13011.990234375, 1207.5235595703125, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 27, "title": "处理参考图的mask", "bounding": [-9953.6708984375, -198.49551391601562, 4049.959228515625, 1155.1162109375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 28, "title": "TEXT | GENERATION", "bounding": [-10490, -4400, 3862.244384765625, 1043.714111328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 29, "title": "", "bounding": [-10490, -5600, 1295.358154296875, 1176.14306640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 30, "title": "Group", "bounding": [-9140, -5600, 1559.69091796875, 1167.5008544921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 31, "title": "人脸原图", "bounding": [-10490, -3230, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 32, "title": "Group", "bounding": [-10490, -2240, 1893.4822998046875, 743.8132934570312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 33, "title": "Group", "bounding": [-8360, -2210, 2118.918701171875, 722.1393432617188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 34, "title": "换脸", "bounding": [-8360, -3240, 1894.3173828125, 895.3728637695312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.2593742460100003, "offset": [14585.905881093453, 445.2481576112688]}}, "version": 0.4, "seed_widgets": {"275": 0, "401": 0, "779": 0, "889": 0, "906": 0, "918": 1, "960": 0}}}}}
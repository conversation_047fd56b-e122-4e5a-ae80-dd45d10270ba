{"client_id": "${clientId}", "prompt": {"159": {"_meta": {"title": "保存tryon结果"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "168": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["397", 0], "mask": ["315", 0], "padding_bottom": 0, "padding_left": 0, "padding_right": 0, "padding_top": 0}}, "176": {"_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"}}, "194": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["315", 0]}}, "211": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["767", 0]}}, "242": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["346", 0], "mask": ["351", 0], "padding_bottom": ["779", 0], "padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0]}}, "243": {"_meta": {"title": "ImageCompositeMasked"}, "class_type": "ImageCompositeMasked", "inputs": {"destination": ["245", 0], "resize_source": false, "source": ["254", 0], "x": ["250", 0], "y": ["249", 0]}}, "245": {"_meta": {"title": "设置底图颜色"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 16777215, "height": ["265", 0], "width": ["264", 0]}}, "246": {"_meta": {"title": "默认宽"}, "class_type": "CR Seed", "inputs": {"seed": ["774", 0]}}, "247": {"_meta": {"title": "默认高"}, "class_type": "CR Seed", "inputs": {"seed": ["775", 0]}}, "249": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["320", 1], "b": ["265", 0], "value": "b/2-a/2"}}, "250": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["320", 0], "b": ["264", 0], "value": "b/2-a/2"}}, "253": {"_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"}}, "254": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["242", 0], "scale_by": ["253", 0], "upscale_method": "area"}}, "256": {"_meta": {"title": "concat的图片大小"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["264", 0], "any_b": ["265", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"}}, "258": {"_meta": {"title": "Minimum宽"}, "class_type": "JWIntegerMin", "inputs": {"a": ["314", 0], "b": ["246", 0]}}, "259": {"_meta": {"title": "Minimum高"}, "class_type": "JWIntegerMin", "inputs": {"a": ["314", 1], "b": ["247", 0]}}, "264": {"_meta": {"title": "Maximum宽"}, "class_type": "JWIntegerMax", "inputs": {"a": ["316", 0], "b": ["258", 0]}}, "265": {"_meta": {"title": "Maximum高"}, "class_type": "JWIntegerMax", "inputs": {"a": ["316", 1], "b": ["259", 0]}}, "275": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 1, "latent_image": ["436", 0], "model": ["283", 0], "negative": ["282", 1], "positive": ["282", 0], "sampler_name": "euler", "scheduler": "simple", "seed": "${seed}", "steps": 20}}, "276": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["275", 0], "vae": ["280", 0]}}, "277": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["281", 0], "text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["277", 0], "guidance": 30}}, "279": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux-fill-tryon-20250308.safetensors", "weight_dtype": "default"}}, "280": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "281": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "device": "default", "type": "flux"}}, "282": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["289", 2], "negative": ["290", 0], "noise_mask": true, "pixels": ["289", 1], "positive": ["286", 0], "vae": ["280", 0]}}, "283": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["293", 0]}}, "284": {"_meta": {"title": "Load CLIP Vision"}, "class_type": "CLIPVisionLoader", "inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}}, "285": {"_meta": {"title": "CLIP Vision Encode"}, "class_type": "CLIPVisionEncode", "inputs": {"clip_vision": ["284", 0], "crop": "center", "image": ["243", 0]}}, "286": {"_meta": {"title": "Apply Style Model"}, "class_type": "StyleModelApply", "inputs": {"clip_vision_output": ["285", 0], "conditioning": ["278", 0], "strength": 1, "strength_type": "multiply", "style_model": ["287", 0]}}, "287": {"_meta": {"title": "Load Style Model"}, "class_type": "StyleModelLoader", "inputs": {"style_model_name": "flux1-redux-dev.safetensors"}}, "288": {"_meta": {"title": "✂️ Inpaint <PERSON>itch"}, "class_type": "InpaintStitch", "inputs": {"inpainted_image": ["793", 0], "rescale_algorithm": "bislerp", "stitch": ["792", 0]}}, "289": {"_meta": {"title": "✂️ Inpaint Crop"}, "class_type": "InpaintCrop", "inputs": {"blend_pixels": 16, "blur_mask_pixels": 0, "context_expand_factor": 1, "context_expand_pixels": 10, "fill_mask_holes": true, "force_height": 1024, "force_width": 1024, "image": ["321", 0], "invert_mask": false, "mask": ["56", 0], "max_height": 1785, "max_width": 1536, "min_height": 512, "min_width": 512, "mode": "ranged size", "optional_context_mask": ["297", 0], "padding": 32, "rescale_algorithm": "bicubic", "rescale_factor": 1}}, "290": {"_meta": {"title": "ConditioningZeroOut"}, "class_type": "ConditioningZeroOut", "inputs": {"conditioning": ["277", 0]}}, "291": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["61", 1], "image": ["288", 0], "width": ["61", 0], "x": ["60", 0], "y": 0}}, "293": {"_meta": {"title": "LoraLoaderModelOnly"}, "class_type": "LoraLoaderModelOnly", "inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "model": ["279", 0], "strength_model": 1}}, "297": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["323", 0]}}, "300": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["351", 0]}}, "306": {"_meta": {"title": "Bounded Image Blend LR"}, "class_type": "Bounded_Image_Blend_LR", "inputs": {"blend_factor": 1, "feathering": 5, "source": ["291", 0], "target": ["397", 0], "target_bounds": ["176", 0]}}, "308": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["159", 1], "images": ["306", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["159", 0], "quality": 100, "use_time_str": "true"}}, "314": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["397", 0]}}, "315": {"_meta": {"title": "替换区域扩张大小"}, "class_type": "GrowMask", "inputs": {"expand": 5, "mask": ["847", 0], "tapered_corners": false}}, "316": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["168", 0]}}, "319": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["242", 0]}}, "320": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["254", 0]}}, "321": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["243", 0], "image2": ["764", 0], "match_image_size": false}}, "322": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["211", 0], "tapered_corners": true}}, "323": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["385", 0], "image2": ["53", 0], "match_image_size": false}}, "324": {"_meta": {"title": "imageConcat"}, "class_type": "easy imageConcat", "inputs": {"direction": "right", "image1": ["53", 0], "image2": ["54", 0], "match_image_size": false}}, "346": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["797", 0], "mask": ["461", 0], "padding_bottom": ["348", 0], "padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0]}}, "347": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["264", 0], "b": ["779", 0], "value": "a/2+b"}}, "348": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["265", 0], "b": ["779", 0], "value": "a/2+b"}}, "349": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["461", 0]}}, "350": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["349", 0], "mask": ["461", 0], "padding_bottom": ["348", 0], "padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0]}}, "351": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["350", 0]}}, "362": {"_meta": {"title": "🔧 Simple Math"}, "class_type": "SimpleMath+", "inputs": {"a": ["264", 0], "value": "a*2*2"}}, "384": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["392", 0], "scale_by": ["253", 0], "upscale_method": "area"}}, "385": {"_meta": {"title": "ImageCompositeMasked"}, "class_type": "ImageCompositeMasked", "inputs": {"destination": ["386", 0], "resize_source": false, "source": ["384", 0], "x": ["250", 0], "y": ["249", 0]}}, "386": {"_meta": {"title": "设置底图颜色"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": 0, "height": ["265", 0], "width": ["264", 0]}}, "392": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["300", 0], "mask": ["351", 0], "padding_bottom": ["779", 0], "padding_left": ["779", 0], "padding_right": ["779", 0], "padding_top": ["779", 0]}}, "397": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["761", 0], "scale_by": ["398", 0], "upscale_method": "area"}}, "398": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["761", 0], "target_size": ["399", 0]}}, "399": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["400", 0], "any_b": ["400", 1], "any_c": ["401", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"}}, "400": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["761", 0]}}, "401": {"_meta": {"title": "最大支持尺寸"}, "class_type": "CR Seed", "inputs": {"seed": 1785}}, "404": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["462", 0]}}, "405": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["404", 0], "any_b": ["404", 1], "any_c": ["401", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"}}, "406": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["462", 0], "target_size": ["405", 0]}}, "408": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["462", 0], "scale_by": ["406", 0], "upscale_method": "area"}}, "433": {"_meta": {"title": "tryon生成图片张数"}, "class_type": "CR Text", "inputs": {"text": "${imageNum}"}}, "434": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["433", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"}}, "435": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["434", 0]}}, "436": {"_meta": {"title": "Duplicate Latent Batch 🎥🅥🅗🅢"}, "class_type": "VHS_DuplicateLatents", "inputs": {"latents": ["282", 2], "multiply_by": ["435", 0]}}, "456": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${maskImage}", "upload": "image"}}, "459": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "always", "height": ["404", 1], "image": ["456", 0], "interpolation": "nearest", "method": "keep proportion", "multiple_of": 0, "width": ["404", 0]}}, "460": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["459", 0], "scale_by": ["406", 0], "upscale_method": "area"}}, "461": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["460", 0]}}, "462": {"_meta": {"title": "模特图"}, "class_type": "LoadImage", "inputs": {"image": "${clotheImage}", "upload": "image"}}, "463": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${referenceImage}", "upload": "image"}}, "52": {"_meta": {"title": "A Person Mask Generator"}, "class_type": "APersonMaskGenerator", "inputs": {"background_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "face_mask": false, "hair_mask": false, "images": ["243", 0]}}, "53": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["52", 0]}}, "54": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["322", 0]}}, "56": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["324", 0]}}, "60": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["243", 0]}}, "61": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["764", 0]}}, "758": {"_meta": {"title": "输入图是否要换头"}, "class_type": "CR Text", "inputs": {"text": "${isNeedReplaceFace?then('1','0')}"}}, "759": {"_meta": {"title": "Compare"}, "class_type": "easy compare", "inputs": {"a": ["758", 0], "b": ["760", 0], "comparison": "a == b"}}, "760": {"_meta": {"title": "🔤 CR Text"}, "class_type": "CR Text", "inputs": {"text": "0"}}, "761": {"_meta": {"title": "If else"}, "class_type": "easy ifElse", "inputs": {"boolean": ["759", 0], "on_false": ["763", 0], "on_true": ["463", 0]}}, "763": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["${isNeedReplaceFace?then('985','463')}", 0], "any_b": ["463", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"}}, "764": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["265", 0], "image": ["397", 0], "width": ["264", 0], "x": ["765", 0], "y": ["766", 0]}}, "765": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["176", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"}}, "766": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["176", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"}}, "767": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["265", 0], "image": ["194", 0], "width": ["264", 0], "x": ["765", 0], "y": ["766", 0]}}, "774": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["168", 1], "call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"}}, "775": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["168", 1], "call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"}}, "779": {"_meta": {"title": "🌱 CR Seed"}, "class_type": "CR Seed", "inputs": {"seed": 0}}, "792": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["289", 0], "any_b": ["435", 0], "call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"}}, "793": {"_meta": {"title": "Image Batch to Image List"}, "class_type": "ImpactImageBatchToImageList", "inputs": {"image": ["276", 0]}}, "797": {"_meta": {"title": "ImageCompositeMasked"}, "class_type": "ImageCompositeMasked", "inputs": {"destination": ["799", 0], "mask": ["461", 0], "resize_source": false, "source": ["408", 0], "x": 0, "y": 0}}, "799": {"_meta": {"title": "Image Blank"}, "class_type": "Image Blank", "inputs": {"blue": 255, "green": 255, "height": ["800", 1], "red": 255, "width": ["800", 0]}}, "800": {"_meta": {"title": "Get Image Size"}, "class_type": "Get Image Size", "inputs": {"image": ["408", 0]}}, "834": {"_meta": {"title": "Conrain SAMModelLoader"}, "class_type": "Conrain_SAMModelLoader", "inputs": {"model_name": "sam_vit_h_cloth"}}, "835": {"_meta": {"title": "Conrain GroundingDinoSAMSegment"}, "class_type": "Conrain_GroundingDinoSAMSegment", "inputs": {"background": "white", "grounding_dino_model": ["836", 0], "image": ["397", 0], "prompt": ["840", 0], "sam_model": ["834", 0], "threshold": 0.6}}, "836": {"_meta": {"title": "Conrain GroundingDinoModelLoader"}, "class_type": "Conrain_GroundingDinoModelLoader", "inputs": {"model_name": "groundingdino_cloth"}}, "837": {"_meta": {"title": "抠图词"}, "class_type": "CR Text", "inputs": {"text": "${clotheType}"}}, "838": {"_meta": {"title": "Conrain GroundingDinoSAMSegment"}, "class_type": "Conrain_GroundingDinoSAMSegment", "inputs": {"background": "white", "grounding_dino_model": ["836", 0], "image": ["397", 0], "prompt": ["853", 0], "sam_model": ["834", 0], "threshold": 0.3}}, "839": {"_meta": {"title": "需要替换的区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["837", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"}}, "840": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["839", 0]}}, "842": {"_meta": {"title": "DensePose Estimator"}, "class_type": "DensePosePreprocessor", "inputs": {"cmap": "<PERSON><PERSON><PERSON> (MagicAnimate)", "image": ["397", 0], "model": "densepose_r101_fpn_dl.torchscript", "resolution": 512}}, "843": {"_meta": {"title": "Conrain Mask From Colors"}, "class_type": "ConrainMaskFromColors", "inputs": {"color_list": ["844", 0], "image": ["842", 0], "threshold": 2}}, "844": {"_meta": {"title": "densepose提取mask区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["837", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"}}, "845": {"_meta": {"title": "需要排除的mask"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["837", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"}}, "846": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["843", 0], "tapered_corners": false}}, "847": {"_meta": {"title": "Masks Add"}, "class_type": "Masks Add", "inputs": {"masks_a": ["849", 0], "masks_b": ["848", 0]}}, "848": {"_meta": {"title": "GrowMask"}, "class_type": "GrowMask", "inputs": {"expand": 0, "mask": ["835", 1], "tapered_corners": false}}, "849": {"_meta": {"title": "Mask Resize"}, "class_type": "JWMaskResize", "inputs": {"height": ["850", 1], "interpolation_mode": "bilinear", "mask": ["846", 0], "width": ["850", 0]}}, "850": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["835", 0]}}, "851": {"_meta": {"title": "Image Blank"}, "class_type": "Image Blank", "inputs": {"blue": 255, "green": 255, "height": ["850", 1], "red": 255, "width": ["850", 0]}}, "852": {"_meta": {"title": "Convert Image to Mask"}, "class_type": "ImageToMask", "inputs": {"channel": "red", "image": ["851", 0]}}, "853": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["845", 0]}}, "854": {"_meta": {"title": "需要排除的区域"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["837", 0], "call_code": "# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"}}, "856": {"_meta": {"title": "If else"}, "class_type": "easy ifElse", "inputs": {"boolean": ["854", 0], "on_false": ["852", 0], "on_true": ["838", 1]}}, "862": {"_meta": {"title": "Convert Mask to Image"}, "class_type": "MaskToImage", "inputs": {"mask": ["847", 0]}}, "864": {"_meta": {"title": "保存参看图的mask"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "mask_${fileNamePrefix}", "text_c": "", "text_d": ""}}, "865": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["864", 1], "images": ["862", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["864", 0], "quality": 100, "use_time_str": "true"}}, "870": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "871": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["876", 0], "guidance": 3.5}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "872": {"_meta": {"title": "🕹️ CR Apply Multi-ControlNet"}, "class_type": "CR Apply Multi-ControlNet", "inputs": {"base_negative": ["875", 0], "base_positive": ["952", 0], "controlnet_stack": ["953", 0], "switch": "On"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "873": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "874": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "875": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["923", 1], "text": ""}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "876": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["923", 1], "text": ["918", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "877": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["923", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "878": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["872", 0], "model": ["877", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "879": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["878", 0], "latent_image": ["952", 2], "noise": ["889", 0], "sampler": ["890", 0], "sigmas": ["982", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "880": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["976", 1], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "881": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["976", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "882": {"_meta": {"title": "🔧 Image Crop"}, "class_type": "ImageCrop+", "inputs": {"height": ["880", 0], "image": ["978", 0], "position": "top-left", "width": ["881", 0], "x_offset": 0, "y_offset": 0}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "884": {"_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "class_type": "Sapiens<PERSON><PERSON>der", "inputs": {"convert_torchscript_to_bf16": false, "depth_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "normal_ckpt": "none", "pose_ckpt": "none", "remove_background": true, "seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "seg_pellete": true, "show_pose_object": false, "use_yolo": false}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "885": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["879", 0], "vae": ["870", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "886": {"_meta": {"title": "<PERSON>"}, "class_type": "Zoe-DepthMapPreprocessor", "inputs": {"image": ["882", 0], "resolution": 1024}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "888": {"_meta": {"title": "<PERSON><PERSON>"}, "class_type": "<PERSON><PERSON>", "inputs": {"high_threshold": 0.8, "image": ["882", 0], "low_threshold": 0.2}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "889": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "890": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "euler"}}, "891": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 10, "decay_factor": 1, "expand": 2, "fill_holes": true, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["894", 4], "tapered_corners": true}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "894": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "3,23,24,25,26,27", "image": ["882", 0], "model": ["884", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "895": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["897", 0], "image1": ["970", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "896": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["898", 0], "image1": ["971", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "897": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "1"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "898": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "1"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "902": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["943", 0], "target_size": 1024}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "904": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["942", 0], "guidance": 2}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "905": {"_meta": {"title": "ConditioningZeroOut"}, "class_type": "ConditioningZeroOut", "inputs": {"conditioning": ["942", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "906": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": 0}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "907": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["973", 0], "model": ["922", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "908": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "dpm_2"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "909": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "3,23,24,25,26,27", "image": ["968", 0], "model": ["940", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "910": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["959", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "911": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "upscale if smaller", "height": 2048, "image": ["916", 0], "interpolation": "lanc<PERSON>s", "method": "keep proportion", "multiple_of": 0, "width": 2048}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "912": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "913": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 256, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["944", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "916": {"_meta": {"title": "Make Image List"}, "class_type": "ImpactMakeImageList", "inputs": {"image1": ["969", 0], "image2": ["895", 0], "image3": ["896", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "917": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["945", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "918": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 991}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "922": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["923", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "923": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["873", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["874", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "925": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["959", 1], "text": ["918", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "926": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "927": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "928": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "929": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["931", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "930": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["926", 0], "end_at": 1, "image": ["933", 0], "image_kps": ["949", 0], "insightface": ["927", 0], "instantid": ["928", 0], "model": ["959", 0], "negative": ["910", 0], "positive": ["925", 0], "start_at": 0, "weight": 1}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "931": {"_meta": {"title": "Image List To Image Batch"}, "class_type": "easy imageListToImageBatch", "inputs": {"images": ["946", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "932": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["965", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "933": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["929", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "935": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["974", 0], "negative": ["930", 2], "noise_mask": true, "pixels": ["949", 0], "positive": ["930", 1], "vae": ["959", 2]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "937": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["907", 0], "latent_image": ["951", 0], "noise": ["906", 0], "sampler": ["908", 0], "sigmas": ["957", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "938": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["955", 0], "face": ["961", 0], "images": ["965", 0], "mask": ["972", 0], "warp": ["949", 2]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "940": {"_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "class_type": "Sapiens<PERSON><PERSON>der", "inputs": {"convert_torchscript_to_bf16": true, "depth_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "normal_ckpt": "none", "pose_ckpt": "none", "remove_background": true, "seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "seg_pellete": true, "show_pose_object": false, "use_yolo": false}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "942": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["923", 1], "text": ["918", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "943": {"_meta": {"title": "Bounded Image Crop With Mask LR v3"}, "class_type": "BoundedImageCropWithMask_v3_LR", "inputs": {"expand_B": 20, "expand_LRU": 20, "image": ["968", 0], "mask": ["909", 4]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "944": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["917", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "945": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["912", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["911", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "946": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["911", 0], "mask": ["913", 0], "padding_bottom": 64, "padding_left": 64, "padding_right": 64, "padding_top": 64}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "947": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["960", 0], "vae": ["959", 2]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "949": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["961", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "951": {"_meta": {"title": "Repeat Latent Batch"}, "class_type": "RepeatLatentBatch", "inputs": {"amount": 1, "samples": ["973", 2]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "952": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["891", 0], "negative": ["875", 0], "noise_mask": true, "pixels": ["882", 0], "positive": ["871", 0], "vae": ["870", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "953": {"_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}, "class_type": "CR Multi-ControlNet Stack", "inputs": {"controlnet_1": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_2": "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", "controlnet_3": "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", "controlnet_strength_1": 0.6, "controlnet_strength_2": 0.2, "controlnet_strength_3": 0.4, "end_percent_1": 0.2, "end_percent_2": 0.8, "end_percent_3": 0.6, "image_1": ["886", 0], "image_2": ["886", 0], "image_3": ["888", 0], "start_percent_1": 0, "start_percent_2": 0.2, "start_percent_3": 0, "switch_1": "On", "switch_2": "On", "switch_3": "On"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "954": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["937", 0], "vae": ["870", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "955": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["949", 0], "image_target": ["954", 0], "method": "adain", "save_prefix": "ComfyUI"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "957": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 0.1, "model": ["923", 0], "scheduler": "sgm_uniform", "steps": 8}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "958": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["885", 0], "mode": "resize", "resampling_method": "lanc<PERSON>s", "rescale_factor": 1, "resize_width": ["979", 0], "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}}, "959": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "960": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.5, "latent_image": ["935", 2], "model": ["930", 0], "negative": ["935", 1], "positive": ["935", 0], "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "seed": 42029499340256, "steps": 4}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "961": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["932", 0], "order": "descending", "take_count": 1, "take_start": 0}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "963": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "23,24,25,26,27", "image": ["949", 0], "model": ["940", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "964": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 15, "decay_factor": 1, "expand": -10, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["943", 2], "tapered_corners": true}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "965": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["943", 0], "scale_by": ["902", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "968": {"_meta": {"title": "Upscale Image"}, "class_type": "ImageScale", "inputs": {"crop": "disabled", "height": ["979", 1], "image": ["958", 0], "upscale_method": "bicubic", "width": ["979", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "969": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "970": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "971": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "972": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 0, "decay_factor": 1, "expand": 40, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["974", 0], "tapered_corners": false}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "973": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["975", 0], "negative": ["905", 0], "noise_mask": false, "pixels": ["947", 0], "positive": ["904", 0], "vae": ["870", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "974": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 4, "enabled": true, "grow": 5, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["987", 1]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "975": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 10, "enabled": true, "grow": 20, "high_limit": 1, "invert_mask": true, "low_limit": 0, "mask": ["974", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "976": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["978", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "978": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "downscale if bigger", "height": 1536, "image": ["463", 0], "interpolation": "lanc<PERSON>s", "method": "keep proportion", "multiple_of": 0, "width": 1536}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "979": {"_meta": {"title": "🔧 Get Image Size"}, "class_type": "GetImageSize+", "inputs": {"image": ["463", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "981": {"_meta": {"title": "保存换头结果图"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "face_${fileNamePrefix}", "text_c": "", "text_d": ""}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "982": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 0.9, "model": ["923", 0], "scheduler": "simple", "steps": 20}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "985": {"_meta": {"title": "Bounded Image Blend with Mask"}, "class_type": "Bounded Image Blend with Mask", "inputs": {"blend_factor": 1, "feathering": 0, "source": ["938", 0], "target": ["968", 0], "target_bounds": ["943", 1], "target_mask": ["964", 0]}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "986": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["981", 1], "images": ["985", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["981", 0], "quality": 100, "use_time_str": "true"}, "disable": "${isNeedReplaceFace?then('false','true')}"}, "987": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["949", 0], "mask_components": "13", "method": "human_parsing_lip"}, "disable": "${isNeedReplaceFace?then('false','true')}"}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [14585.905881093453, 445.2481576112688], "scale": 0.2593742460100003}}, "groups": [{"id": 2, "bounding": [-4528.39404296875, 1173.5872802734375, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "cat图片准备"}, {"id": 5, "bounding": [-7910.73583984375, 2524.262939453125, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理服装图mask"}, {"id": 6, "bounding": [-7866.99169921875, 1201.8798828125, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理参考图mask"}, {"id": 7, "bounding": [-4531.9833984375, 2540.28515625, 3080.71728515625, 1101.5528564453125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "重绘"}, {"id": 8, "bounding": [-11264.9794921875, 1178.438232421875, 3091.599609375, 1692.7294921875], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "图片尺寸最大2048"}, {"id": 26, "bounding": [-13011.990234375, 1207.5235595703125, 1616.3167724609375, 1283.9755859375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 27, "bounding": [-9953.6708984375, -198.49551391601562, 4049.959228515625, 1155.1162109375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "处理参考图的mask"}, {"id": 28, "bounding": [-10490, -4400, 3862.244384765625, 1043.714111328125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "TEXT | GENERATION"}, {"id": 29, "bounding": [-10490, -5600, 1295.358154296875, 1176.14306640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": ""}, {"id": 30, "bounding": [-9140, -5600, 1559.69091796875, 1167.5008544921875], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 31, "bounding": [-10490, -3230, 1902.8575439453125, 803.4837036132812], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 32, "bounding": [-10490, -2240, 1893.4822998046875, 743.8132934570312], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 33, "bounding": [-8360, -2210, 2118.918701171875, 722.1393432617188], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 34, "bounding": [-8360, -3240, 1894.3173828125, 895.3728637695312], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}], "last_link_id": 1787, "last_node_id": 988, "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [441, 243, 0, 167, 0, "*"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [513, 277, 0, 290, 0, "CONDITIONING"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [591, 211, 0, 322, 0, "MASK"], [592, 322, 0, 54, 0, "MASK"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [797, 399, 0, 398, 1, "INT"], [798, 398, 0, 397, 1, "FLOAT"], [800, 400, 0, 399, 0, "*"], [801, 400, 1, 399, 1, "*"], [802, 401, 0, 399, 2, "*"], [804, 402, 0, 400, 0, "IMAGE"], [805, 402, 0, 398, 0, "IMAGE"], [806, 402, 0, 397, 0, "IMAGE"], [807, 397, 0, 328, 0, "*"], [809, 404, 0, 405, 0, "*"], [810, 404, 1, 405, 1, "*"], [811, 401, 0, 405, 2, "*"], [812, 405, 0, 406, 1, "INT"], [817, 406, 0, 408, 1, "FLOAT"], [819, 208, 0, 113, 0, "*"], [820, 167, 0, 52, 0, "IMAGE"], [821, 167, 0, 60, 0, "IMAGE"], [822, 167, 0, 285, 1, "IMAGE"], [823, 167, 0, 321, 0, "IMAGE"], [827, 407, 0, 404, 0, "IMAGE"], [828, 407, 0, 406, 0, "IMAGE"], [829, 407, 0, 408, 0, "IMAGE"], [846, 433, 0, 434, 0, "*"], [847, 434, 0, 435, 0, "STRING"], [848, 282, 2, 436, 0, "LATENT"], [849, 435, 0, 436, 1, "INT"], [850, 436, 0, 275, 3, "LATENT"], [873, 159, 0, 308, 1, "STRING"], [874, 159, 1, 308, 2, "STRING"], [879, 306, 0, 451, 0, "IMAGE"], [880, 456, 0, 457, 0, "*"], [885, 457, 0, 459, 0, "IMAGE"], [889, 459, 0, 460, 0, "IMAGE"], [890, 460, 0, 461, 0, "IMAGE"], [895, 404, 0, 459, 1, "INT"], [896, 404, 1, 459, 2, "INT"], [897, 406, 0, 460, 1, "FLOAT"], [1257, 462, 0, 407, 0, "*"], [1405, 758, 0, 759, 0, "*"], [1406, 760, 0, 759, 1, "*"], [1407, 463, 0, 761, 0, "*"], [1409, 759, 0, 761, 2, "BOOLEAN"], [1416, 757, 0, 763, 0, "*"], [1417, 463, 0, 763, 1, "*"], [1418, 763, 0, 761, 1, "*"], [1419, 765, 0, 764, 3, "INT"], [1420, 766, 0, 764, 4, "INT"], [1421, 765, 0, 767, 3, "INT"], [1422, 766, 0, 767, 4, "INT"], [1423, 176, 0, 766, 0, "*"], [1424, 176, 0, 765, 0, "*"], [1425, 328, 0, 764, 0, "IMAGE"], [1426, 264, 0, 764, 1, "INT"], [1427, 265, 0, 764, 2, "INT"], [1428, 764, 0, 208, 0, "*"], [1429, 194, 0, 767, 0, "IMAGE"], [1430, 264, 0, 767, 1, "INT"], [1431, 265, 0, 767, 2, "INT"], [1432, 767, 0, 211, 0, "IMAGE"], [1433, 321, 0, 768, 0, "*"], [1434, 56, 0, 769, 0, "*"], [1435, 768, 0, 289, 0, "IMAGE"], [1436, 769, 0, 289, 1, "MASK"], [1437, 289, 1, 282, 3, "IMAGE"], [1438, 289, 2, 282, 4, "MASK"], [1440, 288, 0, 291, 0, "IMAGE"], [1444, 773, 0, 346, 0, "IMAGE"], [1446, 774, 0, 246, 0, "INT"], [1448, 168, 1, 776, 0, "*"], [1449, 776, 0, 774, 0, "*"], [1450, 776, 0, 775, 0, "*"], [1451, 775, 0, 247, 0, "INT"], [1455, 779, 0, 780, 0, "*"], [1456, 780, 0, 242, 2, "INT"], [1457, 780, 0, 242, 3, "INT"], [1458, 780, 0, 242, 4, "INT"], [1459, 780, 0, 242, 5, "INT"], [1460, 779, 0, 781, 0, "*"], [1461, 781, 0, 392, 2, "INT"], [1462, 781, 0, 392, 3, "INT"], [1463, 781, 0, 392, 4, "INT"], [1464, 781, 0, 392, 5, "INT"], [1465, 351, 0, 242, 1, "MASK"], [1466, 351, 0, 300, 0, "MASK"], [1467, 351, 0, 392, 1, "MASK"], [1468, 779, 0, 347, 1, "*"], [1469, 779, 0, 348, 1, "*"], [1477, 321, 0, 788, 0, "IMAGE"], [1497, 761, 0, 402, 0, "*"], [1498, 289, 0, 792, 0, "*"], [1499, 435, 0, 792, 1, "*"], [1500, 792, 0, 288, 0, "STITCH"], [1501, 276, 0, 793, 0, "IMAGE"], [1502, 793, 0, 288, 1, "IMAGE"], [1506, 461, 0, 797, 2, "MASK"], [1507, 408, 0, 800, 0, "IMAGE"], [1508, 800, 0, 799, 0, "INT"], [1509, 800, 1, 799, 1, "INT"], [1510, 799, 0, 797, 0, "IMAGE"], [1512, 461, 0, 331, 0, "*"], [1513, 797, 0, 773, 0, "*"], [1515, 408, 0, 797, 1, "IMAGE"], [1557, 834, 0, 835, 0, "SAM_MODEL"], [1558, 836, 0, 835, 1, "GROUNDING_DINO_MODEL"], [1559, 833, 0, 835, 2, "IMAGE"], [1560, 840, 0, 835, 3, "STRING"], [1561, 834, 0, 838, 0, "SAM_MODEL"], [1562, 836, 0, 838, 1, "GROUNDING_DINO_MODEL"], [1563, 833, 0, 838, 2, "IMAGE"], [1564, 853, 0, 838, 3, "STRING"], [1565, 837, 0, 839, 0, "*"], [1566, 839, 0, 840, 0, "STRING"], [1567, 833, 0, 842, 0, "IMAGE"], [1568, 842, 0, 843, 0, "IMAGE"], [1569, 844, 0, 843, 1, "STRING"], [1570, 837, 0, 844, 0, "*"], [1571, 837, 0, 845, 0, "*"], [1572, 843, 0, 846, 0, "MASK"], [1573, 849, 0, 847, 0, "MASK"], [1574, 848, 0, 847, 1, "MASK"], [1575, 835, 1, 848, 0, "MASK"], [1576, 846, 0, 849, 0, "MASK"], [1577, 850, 1, 849, 1, "INT"], [1578, 850, 0, 849, 2, "INT"], [1579, 835, 0, 850, 0, "IMAGE"], [1580, 850, 0, 851, 0, "INT"], [1581, 850, 1, 851, 1, "INT"], [1582, 851, 0, 852, 0, "IMAGE"], [1583, 845, 0, 853, 0, "STRING"], [1584, 837, 0, 854, 0, "*"], [1585, 847, 0, 855, 0, "MASK"], [1586, 856, 0, 855, 1, "MASK"], [1587, 838, 1, 856, 0, "*"], [1588, 852, 0, 856, 1, "*"], [1589, 854, 0, 856, 2, "BOOLEAN"], [1596, 855, 0, 862, 0, "MASK"], [1597, 862, 0, 863, 0, "IMAGE"], [1598, 328, 0, 833, 0, "*"], [1599, 855, 0, 315, 0, "MASK"], [1600, 864, 0, 865, 1, "STRING"], [1601, 864, 1, 865, 2, "STRING"], [1602, 862, 0, 865, 0, "IMAGE"], [1607, 876, 0, 871, 0, "CONDITIONING"], [1608, 952, 0, 872, 0, "CONDITIONING"], [1609, 875, 0, 872, 1, "CONDITIONING"], [1610, 953, 0, 872, 2, "CONTROL_NET_STACK"], [1611, 919, 0, 875, 0, "CLIP"], [1612, 919, 0, 876, 0, "CLIP"], [1613, 918, 0, 876, 1, "STRING"], [1614, 924, 0, 877, 0, "MODEL"], [1615, 877, 0, 878, 0, "MODEL"], [1616, 872, 0, 878, 1, "CONDITIONING"], [1617, 889, 0, 879, 0, "NOISE"], [1618, 878, 0, 879, 1, "GUIDER"], [1619, 890, 0, 879, 2, "SAMPLER"], [1620, 982, 0, 879, 3, "SIGMAS"], [1621, 952, 2, 879, 4, "LATENT"], [1622, 976, 1, 880, 0, "*"], [1623, 976, 0, 881, 0, "*"], [1624, 893, 0, 882, 0, "IMAGE"], [1625, 881, 0, 882, 1, "INT"], [1626, 880, 0, 882, 2, "INT"], [1627, 956, 0, 883, 0, "*"], [1628, 879, 0, 885, 0, "LATENT"], [1629, 921, 0, 885, 1, "VAE"], [1630, 887, 0, 886, 0, "IMAGE"], [1631, 956, 0, 887, 0, "*"], [1632, 887, 0, 888, 0, "IMAGE"], [1633, 894, 4, 891, 0, "MASK"], [1634, 886, 0, 892, 0, "IMAGE"], [1635, 978, 0, 893, 0, "*"], [1636, 884, 0, 894, 0, "MODEL_SAPIEN"], [1637, 883, 0, 894, 1, "IMAGE"], [1638, 970, 0, 895, 0, "IMAGE"], [1639, 897, 0, 895, 2, "INT"], [1640, 971, 0, 896, 0, "IMAGE"], [1641, 898, 0, 896, 2, "INT"], [1642, 964, 0, 899, 0, "*"], [1643, 939, 0, 900, 0, "*"], [1644, 943, 1, 901, 0, "*"], [1645, 943, 0, 902, 0, "IMAGE"], [1646, 924, 0, 903, 0, "*"], [1647, 942, 0, 904, 0, "CONDITIONING"], [1648, 942, 0, 905, 0, "CONDITIONING"], [1649, 922, 0, 907, 0, "MODEL"], [1650, 973, 0, 907, 1, "CONDITIONING"], [1651, 940, 0, 909, 0, "MODEL_SAPIEN"], [1652, 939, 0, 909, 1, "IMAGE"], [1653, 959, 1, 910, 0, "CLIP"], [1654, 916, 0, 911, 0, "IMAGE"], [1655, 944, 0, 913, 0, "MASK"], [1656, 918, 0, 914, 0, "*"], [1657, 919, 0, 915, 0, "*"], [1658, 969, 0, 916, 0, "IMAGE"], [1659, 895, 0, 916, 1, "IMAGE"], [1660, 896, 0, 916, 2, "IMAGE"], [1661, 945, 0, 917, 0, "SEGS"], [1662, 923, 1, 919, 0, "*"], [1663, 921, 0, 920, 0, "*"], [1664, 870, 0, 921, 0, "*"], [1665, 903, 0, 922, 0, "MODEL"], [1666, 874, 0, 923, 0, "MODEL"], [1667, 873, 0, 923, 1, "CLIP"], [1668, 923, 0, 924, 0, "*"], [1669, 959, 1, 925, 0, "CLIP"], [1670, 936, 0, 925, 1, "STRING"], [1671, 931, 0, 929, 0, "IMAGE"], [1672, 928, 0, 930, 0, "INSTANTID"], [1673, 927, 0, 930, 1, "FACEANALYSIS"], [1674, 926, 0, 930, 2, "CONTROL_NET"], [1675, 934, 0, 930, 3, "IMAGE"], [1676, 959, 0, 930, 4, "MODEL"], [1677, 925, 0, 930, 5, "CONDITIONING"], [1678, 910, 0, 930, 6, "CONDITIONING"], [1679, 948, 0, 930, 7, "IMAGE"], [1680, 946, 0, 931, 0, "IMAGE"], [1681, 966, 0, 932, 0, "IMAGE"], [1682, 929, 0, 933, 0, "FACE"], [1683, 933, 0, 934, 0, "*"], [1684, 930, 1, 935, 0, "CONDITIONING"], [1685, 930, 2, 935, 1, "CONDITIONING"], [1686, 959, 2, 935, 2, "VAE"], [1687, 948, 0, 935, 3, "IMAGE"], [1688, 974, 0, 935, 4, "MASK"], [1689, 962, 0, 936, 0, "*"], [1690, 906, 0, 937, 0, "NOISE"], [1691, 907, 0, 937, 1, "GUIDER"], [1692, 908, 0, 937, 2, "SAMPLER"], [1693, 957, 0, 937, 3, "SIGMAS"], [1694, 951, 0, 937, 4, "LATENT"], [1695, 966, 0, 938, 0, "IMAGE"], [1696, 961, 0, 938, 1, "FACE"], [1697, 955, 0, 938, 2, "IMAGE"], [1698, 972, 0, 938, 3, "MASK"], [1699, 949, 2, 938, 4, "WARP"], [1700, 968, 0, 939, 0, "*"], [1701, 915, 0, 941, 0, "*"], [1702, 941, 0, 942, 0, "CLIP"], [1703, 962, 0, 942, 1, "STRING"], [1704, 939, 0, 943, 0, "IMAGE"], [1705, 909, 4, 943, 1, "MASK"], [1706, 917, 0, 944, 0, "SEGS"], [1707, 912, 0, 945, 0, "BBOX_DETECTOR"], [1708, 911, 0, 945, 1, "IMAGE"], [1709, 911, 0, 946, 0, "IMAGE"], [1710, 913, 0, 946, 1, "MASK"], [1711, 960, 0, 947, 0, "LATENT"], [1712, 959, 2, 947, 1, "VAE"], [1713, 949, 0, 948, 0, "*"], [1714, 961, 0, 949, 0, "FACE"], [1715, 891, 0, 950, 0, "MASK"], [1716, 973, 2, 951, 0, "LATENT"], [1717, 871, 0, 952, 0, "CONDITIONING"], [1718, 875, 0, 952, 1, "CONDITIONING"], [1719, 921, 0, 952, 2, "VAE"], [1720, 887, 0, 952, 3, "IMAGE"], [1721, 891, 0, 952, 4, "MASK"], [1722, 886, 0, 953, 0, "IMAGE"], [1723, 886, 0, 953, 1, "IMAGE"], [1724, 888, 0, 953, 2, "IMAGE"], [1725, 937, 0, 954, 0, "LATENT"], [1726, 920, 0, 954, 1, "VAE"], [1727, 949, 0, 955, 0, "IMAGE"], [1728, 954, 0, 955, 1, "IMAGE"], [1729, 882, 0, 956, 0, "*"], [1730, 903, 0, 957, 0, "MODEL"], [1731, 885, 0, 958, 0, "IMAGE"], [1732, 979, 0, 958, 1, "INT"], [1733, 930, 0, 960, 0, "MODEL"], [1734, 935, 0, 960, 1, "CONDITIONING"], [1735, 935, 1, 960, 2, "CONDITIONING"], [1736, 935, 2, 960, 3, "LATENT"], [1737, 932, 0, 961, 0, "FACE"], [1738, 914, 0, 962, 0, "*"], [1739, 940, 0, 963, 0, "MODEL_SAPIEN"], [1740, 949, 0, 963, 1, "IMAGE"], [1741, 943, 2, 964, 0, "MASK"], [1742, 943, 0, 965, 0, "IMAGE"], [1743, 902, 0, 965, 1, "FLOAT"], [1744, 965, 0, 966, 0, "*"], [1745, 958, 0, 968, 0, "IMAGE"], [1746, 979, 0, 968, 1, "INT"], [1747, 979, 1, 968, 2, "INT"], [1748, 974, 0, 972, 0, "MASK"], [1749, 904, 0, 973, 0, "CONDITIONING"], [1750, 905, 0, 973, 1, "CONDITIONING"], [1751, 920, 0, 973, 2, "VAE"], [1752, 947, 0, 973, 3, "IMAGE"], [1753, 975, 0, 973, 4, "MASK"], [1754, 987, 1, 974, 0, "MASK"], [1755, 974, 0, 975, 0, "MASK"], [1756, 893, 0, 976, 0, "IMAGE"], [1758, 977, 0, 978, 0, "IMAGE"], [1759, 977, 0, 979, 0, "IMAGE"], [1760, 888, 0, 980, 0, "IMAGE"], [1761, 924, 0, 982, 0, "MODEL"], [1762, 985, 0, 983, 0, "IMAGE"], [1763, 968, 0, 984, 0, "IMAGE"], [1764, 900, 0, 985, 0, "IMAGE"], [1765, 899, 0, 985, 1, "MASK"], [1766, 901, 0, 985, 2, "IMAGE_BOUNDS"], [1767, 938, 0, 985, 3, "IMAGE"], [1768, 985, 0, 986, 0, "IMAGE"], [1769, 981, 0, 986, 1, "STRING"], [1770, 981, 1, 986, 2, "STRING"], [1771, 949, 0, 987, 0, "IMAGE"], [1772, 463, 0, 977, 0, "*"], [1776, 985, 0, 757, 0, "*"]], "nodes": [{"id": 52, "flags": {}, "inputs": [{"link": 820, "name": "images", "type": "IMAGE"}], "mode": 0, "order": 251, "outputs": [{"links": [109], "name": "masks", "slot_index": 0, "type": "MASK"}], "pos": [-3706.53466796875, 1812.228759765625], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "size": [261.10693359375, 178], "type": "APersonMaskGenerator", "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "flags": {}, "inputs": [{"link": 109, "name": "mask", "type": "MASK"}], "mode": 0, "order": 255, "outputs": [{"links": [594, 596], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2848.5341796875, 1879.2283935546875], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 54, "flags": {}, "inputs": [{"link": 592, "name": "mask", "type": "MASK"}], "mode": 0, "order": 242, "outputs": [{"links": [597], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2852.5341796875, 2061.2265625], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 56, "flags": {}, "inputs": [{"link": 598, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 262, "outputs": [{"links": [1434], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-1986.5281982421875, 1933.196533203125], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 59.905555725097656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 60, "flags": {}, "inputs": [{"link": 821, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 252, "outputs": [{"links": [522], "name": "width", "slot_index": 0, "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-3917.53466796875, 2266.224853515625], "properties": {"Node name for S&R": "Get Image Size"}, "size": [298.42425537109375, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 61, "flags": {}, "inputs": [{"link": 186, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 241, "outputs": [{"links": [520], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [521], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-3203.9482421875, 3095.456787109375], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 113, "flags": {}, "inputs": [{"link": 819, "name": "", "type": "*"}], "mode": 0, "order": 238, "outputs": [{"links": [186, 587], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4042.759765625, 1668.781982421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 159, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [873], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [874], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-2210, 3360], "properties": {"Node name for S&R": "Text String"}, "size": [228.5572052001953, 190], "title": "保存tryon结果", "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 167, "flags": {}, "inputs": [{"link": 441, "name": "", "type": "*"}], "mode": 0, "order": 250, "outputs": [{"links": [820, 821, 822, 823], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4409.53564453125, 1786.22900390625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 168, "flags": {}, "inputs": [{"link": 606, "name": "image", "type": "IMAGE"}, {"link": 569, "name": "mask", "type": "MASK"}], "mode": 0, "order": 203, "outputs": [{"links": [571], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [292, 1448], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-7087.40234375, 1276.54248046875], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [248.14456176757812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 176, "flags": {"collapsed": true}, "inputs": [{"link": 566, "name": "any_a", "shape": 7, "type": "*"}, {"link": 567, "name": "any_b", "shape": 7, "type": "*"}, {"link": 292, "name": "any_c", "shape": 7, "type": "*"}, {"link": 451, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 221, "outputs": [{"links": [544, 1423, 1424], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5796.40234375, 1658.54248046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [423.4119567871094, 402.7642517089844], "title": "以涂抹区域的为中心点画一个矩形框", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = center_h - rect_height / 2.0\n    new_h_end   = new_h_start + rect_height\n    new_w_start = center_w - rect_width / 2.0\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 194, "flags": {}, "inputs": [{"link": 570, "name": "mask", "type": "MASK"}], "mode": 0, "order": 204, "outputs": [{"links": [1429], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5828.779296875, 1989.6298828125], "properties": {"Node name for S&R": "MaskToImage"}, "size": [264.5999755859375, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 208, "flags": {}, "inputs": [{"link": 1428, "name": "", "type": "*"}], "mode": 0, "order": 234, "outputs": [{"links": [819], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-4313.53564453125, 1468.2301025390625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 211, "flags": {}, "inputs": [{"link": 1432, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 235, "outputs": [{"links": [591], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-5005.779296875, 2112.63134765625], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 84.43663024902344], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 242, "flags": {}, "inputs": [{"link": 655, "name": "image", "type": "IMAGE"}, {"link": 1465, "name": "mask", "type": "MASK"}, {"link": 1456, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 1457, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 1458, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 1459, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 232, "outputs": [{"links": [439, 580], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-6855.95703125, 2875.468994140625], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [10, 10, 10, 10]}, {"id": 243, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "destination", "link": 418, "name": "destination", "type": "IMAGE"}, {"label": "source", "link": 440, "name": "source", "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "x", "link": 420, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"label": "y", "link": 421, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 248, "outputs": [{"label": "IMAGE", "links": [441], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5033.61572265625, 2818.682373046875], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "size": [210, 138], "type": "ImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 245, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "width", "link": 757, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 759, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 224, "outputs": [{"label": "IMAGE", "links": [418], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5743.5244140625, 2618.468994140625], "properties": {"Node name for S&R": "EmptyImage"}, "size": [243.3533935546875, 102], "title": "设置底图颜色", "type": "EmptyImage", "widgets_values": [512, 512, 1, 16777215]}, {"id": 246, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1446, "name": "seed", "type": "INT", "widget": {"name": "seed"}}], "mode": 0, "order": 209, "outputs": [{"links": [453], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7381.40234375, 1781.54248046875], "properties": {"Node name for S&R": "CR Seed"}, "size": [281.7162780761719, 102], "title": "默认宽", "type": "CR Seed", "widgets_values": [1152, "fixed"]}, {"id": 247, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 1451, "name": "seed", "type": "INT", "widget": {"name": "seed"}}], "mode": 0, "order": 210, "outputs": [{"links": [455], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7359.40234375, 2077.543701171875], "properties": {"Node name for S&R": "CR Seed"}, "size": [278.3121032714844, 102], "title": "默认高", "type": "CR Seed", "widgets_values": [1536, "fixed"]}, {"id": 249, "flags": {}, "inputs": [{"link": 585, "name": "a", "shape": 7, "type": "*"}, {"link": 760, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 247, "outputs": [{"links": [421, 770], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-5383.61572265625, 2823.682373046875], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [220.35072326660156, 98], "type": "SimpleMath+", "widgets_values": ["b/2-a/2"]}, {"id": 250, "flags": {}, "inputs": [{"link": 584, "name": "a", "shape": 7, "type": "*"}, {"link": 758, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 246, "outputs": [{"links": [420, 771], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-5383.61572265625, 2624.68310546875], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 112.43743896484375], "type": "SimpleMath+", "widgets_values": ["b/2-a/2"]}, {"id": 253, "flags": {}, "inputs": [{"link": 481, "name": "any_a", "shape": 7, "type": "*"}, {"link": 478, "name": "any_b", "shape": 7, "type": "*"}, {"link": 581, "name": "any_c", "shape": 7, "type": "*"}, {"link": 582, "name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 240, "outputs": [{"links": [486, 748], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-6444.5244140625, 2623.468994140625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [282.12066650390625, 195.71939086914062], "title": "原logo比目标尺寸大时缩小到目标尺寸", "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 254, "flags": {}, "inputs": [{"link": 439, "name": "image", "type": "IMAGE"}, {"link": 486, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 243, "outputs": [{"links": [440, 583], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6100.5244140625, 2879.468994140625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [261.7075500488281, 81.54931640625], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 256, "flags": {}, "inputs": [{"link": 474, "name": "any_a", "shape": 7, "type": "*"}, {"link": 477, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 218, "outputs": [{"links": [451], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-6189.40234375, 1598.54248046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [270.53582763671875, 200.77845764160156], "title": "concat的图片大小", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 258, "flags": {}, "inputs": [{"link": 564, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 453, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 211, "outputs": [{"links": [473], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6928.779296875, 1696.63037109375], "properties": {"Node name for S&R": "JWIntegerMin"}, "size": [213.4287567138672, 79.92222595214844], "title": "Minimum宽", "type": "JWIntegerMin", "widgets_values": [0, 0]}, {"id": 259, "flags": {}, "inputs": [{"link": 565, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 455, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 212, "outputs": [{"links": [476], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6934.779296875, 2046.6298828125], "properties": {"Node name for S&R": "JWIntegerMin"}, "size": [213.4287567138672, 79.92222595214844], "title": "Minimum高", "type": "JWIntegerMin", "widgets_values": [0, 0]}, {"id": 264, "flags": {}, "inputs": [{"link": 572, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 473, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 213, "outputs": [{"links": [474, 481, 648, 675, 755, 1426, 1430], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6575.779296875, 1672.630126953125], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 71.68185424804688], "title": "Maximum宽", "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 265, "flags": {}, "inputs": [{"link": 573, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 476, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 214, "outputs": [{"links": [477, 478, 652, 756, 1427, 1431], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-6617.779296875, 1963.6302490234375], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 71.68185424804688], "title": "Maximum高", "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 268, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 1, "outputs": [], "pos": [-6926.40234375, 1832.54248046875], "properties": {}, "size": [210, 67.93143463134766], "type": "Note", "widgets_values": ["模特图较小时，以模特图的大小为准"]}, {"id": 269, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 2, "outputs": [], "pos": [-6562.779296875, 1830.6304931640625], "properties": {}, "size": [210, 67.93143463134766], "type": "Note", "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"]}, {"id": 275, "flags": {}, "inputs": [{"label": "模型", "link": 493, "name": "model", "type": "MODEL"}, {"label": "正面条件", "link": 494, "name": "positive", "type": "CONDITIONING"}, {"label": "负面条件", "link": 495, "name": "negative", "type": "CONDITIONING"}, {"label": "Latent", "link": 850, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 268, "outputs": [{"label": "Latent", "links": [497], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [-2872.509765625, 2777.515869140625], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [234.29580688476562, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "fixed", 20, 1, "euler", "simple", 1]}, {"id": 276, "flags": {}, "inputs": [{"label": "Latent", "link": 497, "name": "samples", "type": "LATENT"}, {"label": "VAE", "link": 498, "name": "vae", "type": "VAE"}], "mode": 0, "order": 269, "outputs": [{"label": "图像", "links": [1501], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2649.509765625, 2683.51611328125], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 277, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "CLIP", "link": 499, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 48, "outputs": [{"label": "条件", "links": [500, 513], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-4101.509765625, 2744.51611328125], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [269.3892822265625, 89.79380798339844], "type": "CLIPTextEncode", "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting. "]}, {"id": 278, "flags": {}, "inputs": [{"label": "条件", "link": 500, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 67, "outputs": [{"label": "条件", "links": [508], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3845.345703125, 2850.************], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [210, 58], "type": "FluxGuidance", "widgets_values": [30]}, {"id": 279, "flags": {}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "模型", "links": [516], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-4014.509765625, 2607.************], "properties": {"Node name for S&R": "UNETLoader"}, "size": [326.5174865722656, 82], "type": "UNETLoader", "widgets_values": ["flux-fill-tryon-20250308.safetensors", "default"]}, {"id": 280, "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [{"links": [498, 503], "name": "VAE", "slot_index": 0, "type": "VAE"}], "pos": [-4432.3720703125, 2883.************], "properties": {"Node name for S&R": "VAELoader"}, "size": [300, 60], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 281, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"label": "CLIP", "links": [499], "name": "CLIP", "slot_index": 0, "type": "CLIP"}], "pos": [-4459.341796875, 2688.78076171875], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 282, "flags": {}, "inputs": [{"label": "正面条件", "link": 501, "name": "positive", "type": "CONDITIONING"}, {"label": "负面条件", "link": 502, "name": "negative", "type": "CONDITIONING"}, {"label": "VAE", "link": 503, "name": "vae", "type": "VAE"}, {"label": "图像", "link": 1437, "name": "pixels", "type": "IMAGE"}, {"label": "遮罩", "link": 1438, "name": "mask", "type": "MASK"}], "mode": 0, "order": 266, "outputs": [{"label": "正面条件", "links": [494], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"label": "负面条件", "links": [495], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}, {"label": "Latent", "links": [848], "name": "latent", "slot_index": 2, "type": "LATENT"}], "pos": [-3237.509765625, 2790.515869140625], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 283, "flags": {}, "inputs": [{"label": "模型", "link": 506, "name": "model", "type": "MODEL"}], "mode": 0, "order": 66, "outputs": [{"label": "模型", "links": [493], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-3235.97265625, 2670.276123046875], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [184.8000030517578, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"label": "CLIP视觉", "links": [507], "name": "CLIP_VISION", "slot_index": 0, "type": "CLIP_VISION"}], "pos": [-4452.9453125, 3088.456787109375], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "size": [370, 60], "type": "CLIPVisionLoader", "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 285, "flags": {}, "inputs": [{"label": "CLIP视觉", "link": 507, "name": "clip_vision", "type": "CLIP_VISION"}, {"label": "图像", "link": 822, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 253, "outputs": [{"label": "CLIP视觉输出", "links": [510], "name": "CLIP_VISION_OUTPUT", "slot_index": 0, "type": "CLIP_VISION_OUTPUT"}], "pos": [-3917.685302734375, 1980.57470703125], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "size": [210, 78], "type": "CLIPVisionEncode", "widgets_values": ["center"]}, {"id": 286, "flags": {}, "inputs": [{"label": "条件", "link": 508, "name": "conditioning", "type": "CONDITIONING"}, {"label": "风格模型", "link": 509, "name": "style_model", "type": "STYLE_MODEL"}, {"label": "CLIP视觉输出", "link": 510, "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT"}], "mode": 0, "order": 256, "outputs": [{"label": "条件", "links": [501], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3552.980712890625, 1847.6009521484375], "properties": {"Node name for S&R": "StyleModelApply"}, "size": [210, 122], "type": "StyleModelApply", "widgets_values": [1, "multiply"]}, {"id": 287, "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"label": "风格模型", "links": [509], "name": "STYLE_MODEL", "type": "STYLE_MODEL"}], "pos": [-4441.5869140625, 3005.603271484375], "properties": {"Node name for S&R": "StyleModelLoader"}, "size": [340, 60], "type": "StyleModelLoader", "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 288, "flags": {}, "inputs": [{"label": "接缝", "link": 1500, "name": "stitch", "type": "STITCH"}, {"label": "图像", "link": 1502, "name": "inpainted_image", "type": "IMAGE"}], "mode": 0, "order": 271, "outputs": [{"label": "图像", "links": [1440], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [-2357.509765625, 2665.************], "properties": {"Node name for S&R": "InpaintStitch"}, "size": [256.60272216796875, 78], "type": "InpaintStitch", "widgets_values": ["bislerp"]}, {"id": 289, "flags": {}, "inputs": [{"label": "图像", "link": 1435, "name": "image", "type": "IMAGE"}, {"label": "遮罩", "link": 1436, "name": "mask", "type": "MASK"}, {"label": "上下文遮罩(可选)", "link": 530, "name": "optional_context_mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 263, "outputs": [{"label": "接缝", "links": [1498], "name": "stitch", "slot_index": 0, "type": "STITCH"}, {"links": [1437], "name": "cropped_image", "slot_index": 1, "type": "IMAGE"}, {"links": [1438], "name": "cropped_mask", "slot_index": 2, "type": "MASK"}], "pos": [-3653.509765625, 3144.515869140625], "properties": {"Node name for S&R": "InpaintCrop"}, "size": [245.64613342285156, 386], "type": "InpaintCrop", "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1785, 32]}, {"id": 290, "flags": {"collapsed": true}, "inputs": [{"label": "条件", "link": 513, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 68, "outputs": [{"label": "条件", "links": [502], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-3801.5908203125, 2779.913330078125], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "size": [317.4000244140625, 26], "type": "ConditioningZeroOut", "widgets_values": []}, {"id": 291, "flags": {}, "inputs": [{"link": 1440, "name": "image", "type": "IMAGE"}, {"link": 520, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 521, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 522, "name": "x", "type": "INT", "widget": {"name": "x"}}], "mode": 0, "order": 272, "outputs": [{"links": [551], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2462.509765625, 2949.51611328125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 118], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 293, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 516, "name": "model", "type": "MODEL"}], "mode": 0, "order": 47, "outputs": [{"links": [506], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-3561.509765625, 2635.************], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "size": [271.6474304199219, 86.10514068603516], "type": "LoraLoaderModelOnly", "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1]}, {"id": 297, "flags": {}, "inputs": [{"link": 595, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 261, "outputs": [{"links": [530], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-2040.5343017578125, 1606.2298583984375], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 59.905555725097656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 300, "flags": {}, "inputs": [{"link": 1466, "name": "mask", "type": "MASK"}], "mode": 0, "order": 233, "outputs": [{"links": [765], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6903.95703125, 3363.468994140625], "properties": {"Node name for S&R": "MaskToImage"}, "size": [176.39999389648438, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 304, "flags": {}, "inputs": [{"link": 610, "name": "", "type": "*"}], "mode": 0, "order": 184, "outputs": [{"links": [547], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-2941.52734375, 3169.814697265625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 305, "flags": {}, "inputs": [{"link": 544, "name": "", "type": "*"}], "mode": 0, "order": 226, "outputs": [{"links": [550], "name": "", "slot_index": 0, "type": "*"}], "pos": [-2594.52734375, 3165.814697265625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 306, "flags": {}, "inputs": [{"link": 547, "name": "target", "type": "IMAGE"}, {"link": 550, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 551, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 273, "outputs": [{"links": [555, 879], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2224.509765625, 3169.515869140625], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "size": [239.650634765625, 122], "type": "Bounded_Image_Blend_LR", "widgets_values": [1, 5]}, {"id": 308, "flags": {}, "inputs": [{"label": "images", "link": 555, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 873, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 874, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 274, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-1860, 3280], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [231.75296020507812, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 314, "flags": {}, "inputs": [{"link": 608, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 183, "outputs": [{"links": [564, 566], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [565, 567], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-7429.40234375, 1354.54248046875], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 315, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1599, "name": "mask", "type": "MASK"}], "mode": 0, "order": 200, "outputs": [{"links": [569, 570], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7412, 1491], "properties": {"Node name for S&R": "GrowMask"}, "size": [232.74205017089844, 84.21175384521484], "title": "替换区域扩张大小", "type": "GrowMask", "widgets_values": [5, false]}, {"id": 316, "flags": {}, "inputs": [{"link": 571, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 205, "outputs": [{"links": [572], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [573], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-6768.779296875, 1300.628173828125], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 319, "flags": {}, "inputs": [{"link": 580, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 236, "outputs": [{"links": [581], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [582], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-6613.5244140625, 2695.468994140625], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [144.6750030517578, 71.8825912475586], "type": "GetImageSize+", "widgets_values": []}, {"id": 320, "flags": {}, "inputs": [{"link": 583, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 245, "outputs": [{"links": [584], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [585], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-5700.61572265625, 2958.350830078125], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 321, "flags": {}, "inputs": [{"link": 823, "name": "image1", "type": "IMAGE"}, {"link": 587, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 254, "outputs": [{"links": [589, 1433, 1477], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-3773.3818359375, 1401.21484375], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 322, "flags": {}, "inputs": [{"link": 591, "name": "mask", "type": "MASK"}], "mode": 0, "order": 239, "outputs": [{"links": [592], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-3303.79052734375, 1979.870361328125], "properties": {"Node name for S&R": "GrowMask"}, "size": [315, 82], "type": "GrowMask", "widgets_values": [0, true]}, {"id": 323, "flags": {}, "inputs": [{"link": 773, "name": "image1", "type": "IMAGE"}, {"link": 594, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 259, "outputs": [{"links": [595], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2367.5341796875, 1607.2298583984375], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 324, "flags": {"collapsed": false}, "inputs": [{"link": 596, "name": "image1", "type": "IMAGE"}, {"link": 597, "name": "image2", "type": "IMAGE"}], "mode": 0, "order": 260, "outputs": [{"links": [598], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-2452.4833984375, 1927.94970703125], "properties": {"Node name for S&R": "easy imageConcat"}, "size": [315, 102], "type": "easy imageConcat", "widgets_values": ["right", false]}, {"id": 328, "flags": {}, "inputs": [{"link": 807, "name": "", "type": "*"}], "mode": 0, "order": 182, "outputs": [{"links": [606, 608, 610, 1425, 1598], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-8927.5751953125, 1416.828857421875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 331, "flags": {}, "inputs": [{"link": 1512, "name": "", "type": "*"}], "mode": 0, "order": 112, "outputs": [{"links": [647, 657, 670], "name": "", "slot_index": 0, "type": "MASK"}], "pos": [-7856.5244140625, 2596.468994140625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 346, "flags": {}, "inputs": [{"link": 1444, "name": "image", "type": "IMAGE"}, {"link": 647, "name": "mask", "type": "MASK"}, {"link": 651, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 650, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 653, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 654, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 222, "outputs": [{"links": [655], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-7427.95703125, 2875.468994140625], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 347, "flags": {"collapsed": false}, "inputs": [{"link": 648, "name": "a", "shape": 7, "type": "*"}, {"link": 1468, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 215, "outputs": [{"links": [650, 651, 659, 660], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-7790.95703125, 2903.468994140625], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a/2+b"]}, {"id": 348, "flags": {"collapsed": false}, "inputs": [{"link": 652, "name": "a", "shape": 7, "type": "*"}, {"link": 1469, "name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 219, "outputs": [{"links": [653, 654, 661, 662], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-7779.5244140625, 3126.468994140625], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a/2+b"]}, {"id": 349, "flags": {}, "inputs": [{"link": 670, "name": "mask", "type": "MASK"}], "mode": 0, "order": 116, "outputs": [{"links": [658], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-7767.95703125, 3305.468994140625], "properties": {"Node name for S&R": "MaskToImage"}, "size": [176.39999389648438, 26], "type": "MaskToImage", "widgets_values": []}, {"id": 350, "flags": {}, "inputs": [{"link": 658, "name": "image", "type": "IMAGE"}, {"link": 657, "name": "mask", "type": "MASK"}, {"link": 659, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 660, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 661, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 662, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 223, "outputs": [{"links": [663], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-7416.95703125, 3183.468994140625], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [0, 0, 0, 0]}, {"id": 351, "flags": {}, "inputs": [{"link": 663, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 229, "outputs": [{"links": [1465, 1466, 1467], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7165.5244140625, 3250.468994140625], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 83.63514709472656], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 362, "flags": {"collapsed": false}, "inputs": [{"link": 675, "name": "a", "shape": 7, "type": "*"}, {"name": "b", "shape": 7, "type": "*"}, {"name": "c", "shape": 7, "type": "*"}], "mode": 0, "order": 216, "outputs": [{"links": [], "name": "INT", "slot_index": 0, "type": "INT"}, {"name": "FLOAT", "type": "FLOAT"}], "pos": [-4387.9453125, 3188.456787109375], "properties": {"Node name for S&R": "SimpleMath+"}, "size": [210, 98], "type": "SimpleMath+", "widgets_values": ["a*2*2"]}, {"id": 384, "flags": {}, "inputs": [{"link": 766, "name": "image", "type": "IMAGE"}, {"link": 748, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 244, "outputs": [{"links": [768], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6074.5244140625, 3323.468994140625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [261.7075500488281, 81.54931640625], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 385, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "destination", "link": 749, "name": "destination", "type": "IMAGE"}, {"label": "source", "link": 768, "name": "source", "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "x", "link": 771, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"label": "y", "link": 770, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 249, "outputs": [{"label": "IMAGE", "links": [773], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5057.61572265625, 3241.682373046875], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "size": [210, 138], "type": "ImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 386, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"label": "width", "link": 761, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 762, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 225, "outputs": [{"label": "IMAGE", "links": [749], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-5703.61572265625, 3151.682373046875], "properties": {"Node name for S&R": "EmptyImage"}, "size": [243.3533935546875, 102], "title": "设置底图颜色", "type": "EmptyImage", "widgets_values": [512, 512, 1, 0]}, {"id": 390, "flags": {}, "inputs": [{"link": 755, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 217, "outputs": [{"links": [757, 758, 761], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-6052.61572265625, 2591.683349609375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 391, "flags": {}, "inputs": [{"link": 756, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 220, "outputs": [{"links": [759, 760, 762], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-6035.61572265625, 2681.682861328125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 392, "flags": {}, "inputs": [{"link": 765, "name": "image", "type": "IMAGE"}, {"link": 1467, "name": "mask", "type": "MASK"}, {"link": 1461, "name": "padding_left", "type": "INT", "widget": {"name": "padding_left"}}, {"link": 1462, "name": "padding_right", "type": "INT", "widget": {"name": "padding_right"}}, {"link": 1463, "name": "padding_top", "type": "INT", "widget": {"name": "padding_top"}}, {"link": 1464, "name": "padding_bottom", "type": "INT", "widget": {"name": "padding_bottom"}}], "mode": 0, "order": 237, "outputs": [{"links": [766], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "IMAGE_BOUNDS", "slot_index": 1, "type": "IMAGE_BOUNDS"}], "pos": [-6666.95703125, 3247.468994140625], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [243.56057739257812, 150], "type": "Bounded Image Crop with Mask", "widgets_values": [10, 10, 10, 10]}, {"id": 397, "flags": {}, "inputs": [{"link": 806, "name": "image", "type": "IMAGE"}, {"link": 798, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 181, "outputs": [{"links": [807], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9361, 1422], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [217.8218994140625, 125.52959442138672], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 398, "flags": {}, "inputs": [{"link": 805, "name": "image", "type": "IMAGE"}, {"link": 797, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 180, "outputs": [{"links": [798], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-9718, 1570], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": [512]}, {"id": 399, "flags": {}, "inputs": [{"link": 800, "name": "any_a", "shape": 7, "type": "*"}, {"link": 801, "name": "any_b", "shape": 7, "type": "*"}, {"link": 802, "name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 179, "outputs": [{"links": [797], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-10318, 1679], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [365.79345703125, 195.28152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 400, "flags": {}, "inputs": [{"link": 804, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 178, "outputs": [{"links": [800], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [801], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-10659, 1689], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 401, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [802, 811], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-11164, 1650], "properties": {"Node name for S&R": "CR Seed"}, "size": [270.7088317871094, 109.29169464111328], "title": "最大支持尺寸", "type": "CR Seed", "widgets_values": [1785, "fixed"]}, {"id": 402, "flags": {}, "inputs": [{"link": 1497, "name": "", "type": "*"}], "mode": 0, "order": 177, "outputs": [{"links": [804, 805, 806], "name": "", "slot_index": 0, "type": "*"}], "pos": [-11040, 1492], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 404, "flags": {}, "inputs": [{"link": 827, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 75, "outputs": [{"links": [809, 895], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [810, 896], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-11103, 2232], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 405, "flags": {}, "inputs": [{"link": 809, "name": "any_a", "shape": 7, "type": "*"}, {"link": 810, "name": "any_b", "shape": 7, "type": "*"}, {"link": 811, "name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 87, "outputs": [{"links": [812], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-10762, 2181], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [353.50982666015625, 168.5362548828125], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(any_c,max(any_a, any_b))]"]}, {"id": 406, "flags": {}, "inputs": [{"link": 828, "name": "image", "type": "IMAGE"}, {"link": 812, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": 0, "order": 96, "outputs": [{"links": [817, 897], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-10343, 2431], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 102.22442626953125], "type": "UpscaleSizeCalculator", "widgets_values": [512]}, {"id": 407, "flags": {}, "inputs": [{"link": 1257, "name": "", "type": "*"}], "mode": 0, "order": 59, "outputs": [{"links": [827, 828, 829], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-11179, 2613], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 408, "flags": {}, "inputs": [{"link": 829, "name": "image", "type": "IMAGE"}, {"link": 817, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 100, "outputs": [{"links": [1507, 1515], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9932, 2298], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [210, 78], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 433, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 40, "outputs": [{"links": [846], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-4473.509765625, 3366.515869140625], "properties": {"Node name for S&R": "CR Text"}, "size": [211.76846313476562, 168.80604553222656], "title": "tryon生成图片张数", "type": "CR Text", "widgets_values": ["${imageNum}"]}, {"id": 434, "flags": {}, "inputs": [{"link": 846, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 61, "outputs": [{"links": [847], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-4152.9453125, 3298.456298828125], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [255.5079803466797, 218.5600128173828], "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n\ttry:\n\t\tcount = int(any_a)\n\t\treturn [str(count)]\n\texcept Exception as e:\n\t\treturn [\"1\"]"]}, {"id": 435, "flags": {}, "inputs": [{"link": 847, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 76, "outputs": [{"links": [849, 1499], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-3942.9482421875, 3091.456787109375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 56.551239013671875], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 436, "flags": {}, "inputs": [{"link": 848, "name": "latents", "type": "LATENT"}, {"link": 849, "name": "multiply_by", "type": "INT", "widget": {"name": "multiply_by"}}], "mode": 0, "order": 267, "outputs": [{"links": [850], "name": "LATENT", "slot_index": 0, "type": "LATENT"}, {"links": [], "name": "count", "slot_index": 1, "type": "INT"}], "pos": [-3283.509765625, 2981.51611328125], "properties": {"Node name for S&R": "VHS_DuplicateLatents"}, "size": [260.3999938964844, 58.512535095214844], "type": "VHS_DuplicateLatents", "widgets_values": {"multiply_by": 1}}, {"id": 451, "flags": {}, "inputs": [{"link": 879, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 275, "outputs": [], "pos": [-1945.509765625, 2759.515869140625], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 456, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 39, "outputs": [{"links": [880], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12934, 1702], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${maskImage}", "image"]}, {"id": 457, "flags": {}, "inputs": [{"link": 880, "name": "", "type": "*"}], "mode": 0, "order": 60, "outputs": [{"links": [885], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-11131.5751953125, 2025.311767578125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 459, "flags": {}, "inputs": [{"link": 885, "name": "image", "type": "IMAGE"}, {"link": 895, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 896, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 88, "outputs": [{"links": [889], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-10330, 2011], "properties": {"Node name for S&R": "ImageResize+"}, "size": [315, 218], "type": "ImageResize+", "widgets_values": [512, 512, "nearest", "keep proportion", "always", 0]}, {"id": 460, "flags": {}, "inputs": [{"link": 889, "name": "image", "type": "IMAGE"}, {"link": 897, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 101, "outputs": [{"links": [890], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9906, 2038], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [214.26881408691406, 94.98396301269531], "type": "ImageScaleBy", "widgets_values": ["area", 1]}, {"id": 461, "flags": {}, "inputs": [{"link": 890, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 108, "outputs": [{"links": [1506, 1512], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-9131, 2034], "properties": {"Node name for S&R": "ImageToMask"}, "size": [226.7721405029297, 83.94685363769531], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 462, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 38, "outputs": [{"links": [1257], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12920, 2096], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "title": "模特图", "type": "LoadImage", "widgets_values": ["${clotheImage}", "image"]}, {"id": 463, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 41, "outputs": [{"links": [1407, 1417, 1772], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-12939, 1306], "properties": {"Node name for S&R": "LoadImage"}, "size": [315, 314], "type": "LoadImage", "widgets_values": ["${referenceImage}", "image"]}, {"id": 757, "flags": {}, "inputs": [{"link": 1776, "name": "", "type": "*"}], "mode": 0, "order": 174, "outputs": [{"links": [1416], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-12237, 1497], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 758, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 42, "outputs": [{"links": [1405], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-12326, 1677], "properties": {"Node name for S&R": "CR Text"}, "size": [210, 106.75990295410156], "title": "输入图是否要换头", "type": "CR Text", "widgets_values": ["${isNeedReplaceFace?then('1','0')}"]}, {"id": 759, "flags": {}, "inputs": [{"link": 1405, "name": "a", "type": "*"}, {"link": 1406, "name": "b", "type": "*"}], "mode": 0, "order": 63, "outputs": [{"links": [1409], "name": "boolean", "slot_index": 0, "type": "BOOLEAN"}], "pos": [-11988, 1537], "properties": {"Node name for S&R": "easy compare"}, "size": [216.3546905517578, 95.56697845458984], "type": "easy compare", "widgets_values": ["a == b"]}, {"id": 760, "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [1406], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-12330, 1888], "properties": {"Node name for S&R": "CR Text"}, "size": [210, 110.8138198852539], "type": "CR Text", "widgets_values": ["0"]}, {"id": 761, "flags": {}, "inputs": [{"link": 1407, "name": "on_true", "type": "*"}, {"link": 1418, "name": "on_false", "type": "*"}, {"link": 1409, "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}}], "mode": 0, "order": 176, "outputs": [{"links": [1497], "name": "*", "slot_index": 0, "type": "*"}], "pos": [-11695, 1370], "properties": {"Node name for S&R": "easy ifElse"}, "size": [210, 80.45691680908203], "type": "easy ifElse", "widgets_values": [false]}, {"id": 763, "flags": {"collapsed": true}, "inputs": [{"link": 1416, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1417, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 175, "outputs": [{"links": [1418], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-12126, 1445], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [327.3860778808594, 248.97320556640625], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n   if any_a is None:\n      return [any_b]\n   else:\n      return [any_a]\n"]}, {"id": 764, "flags": {}, "inputs": [{"link": 1425, "name": "image", "type": "IMAGE"}, {"link": 1426, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1427, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1419, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1420, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 230, "outputs": [{"links": [1428], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5033.40234375, 1428.54248046875], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 114], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 765, "flags": {"collapsed": true}, "inputs": [{"link": 1424, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 228, "outputs": [{"links": [1419, 1421], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5407.40234375, 1728.54248046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [385.54595947265625, 187.53152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 766, "flags": {"collapsed": true}, "inputs": [{"link": 1423, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 227, "outputs": [{"links": [1420, 1422], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-5399.40234375, 1615.54248046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [385.54595947265625, 187.53152465820312], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 767, "flags": {}, "inputs": [{"link": 1429, "name": "image", "type": "IMAGE"}, {"link": 1430, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1431, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1421, "name": "x", "type": "INT", "widget": {"name": "x"}}, {"link": 1422, "name": "y", "type": "INT", "widget": {"name": "y"}}], "mode": 0, "order": 231, "outputs": [{"links": [1432], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-5071.40234375, 1869.54248046875], "properties": {"Node name for S&R": "ImageCrop"}, "size": [210, 114], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 768, "flags": {}, "inputs": [{"link": 1433, "name": "", "type": "*"}], "mode": 0, "order": 257, "outputs": [{"links": [1435], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-3559.637939453125, 2989.5966796875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 769, "flags": {}, "inputs": [{"link": 1434, "name": "", "type": "*"}], "mode": 0, "order": 264, "outputs": [{"links": [1436], "name": "", "slot_index": 0, "type": "MASK"}], "pos": [-3627.509765625, 3008.51611328125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 773, "flags": {}, "inputs": [{"link": 1513, "name": "", "type": "*"}], "mode": 0, "order": 120, "outputs": [{"links": [1444], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-8257, 2543], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 774, "flags": {}, "inputs": [{"link": 1449, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 207, "outputs": [{"links": [1446], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7831.40234375, 1775.54248046875], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = w_end-w_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 775, "flags": {}, "inputs": [{"link": 1450, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 208, "outputs": [{"links": [1451], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7833.40234375, 2109.544677734375], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [400, 200], "type": "ConrainPythonExecutor", "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = h_end-h_start\n\n   width = max_size + int(math.sqrt(max_size))\n\n   return [width.item()]"]}, {"id": 776, "flags": {}, "inputs": [{"link": 1448, "name": "", "type": "*"}], "mode": 0, "order": 206, "outputs": [{"links": [1449, 1450], "name": "", "slot_index": 0, "type": "IMAGE_BOUNDS"}], "pos": [-7788.40234375, 1303.54248046875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 779, "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [1455, 1460, 1468, 1469], "name": "seed", "slot_index": 0, "type": "INT"}, {"name": "show_help", "type": "STRING"}], "pos": [-7515.95703125, 2642.468994140625], "properties": {"Node name for S&R": "CR Seed"}, "size": [315, 102], "type": "CR Seed", "widgets_values": [0, "fixed"]}, {"id": 780, "flags": {}, "inputs": [{"link": 1455, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 49, "outputs": [{"links": [1456, 1457, 1458, 1459], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-7038.95703125, 2964.468994140625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 781, "flags": {}, "inputs": [{"link": 1460, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 50, "outputs": [{"links": [1461, 1462, 1463, 1464], "name": "", "slot_index": 0, "type": "INT"}], "pos": [-7004.5244140625, 3164.468994140625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 784, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [], "pos": [-7668.40234375, 1522.54248046875], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["扩散区域的大小，\n输入5,10,20,30"]}, {"id": 788, "flags": {}, "inputs": [{"link": 1477, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 258, "outputs": [], "pos": [-3258.529296875, 1360.197509765625], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 789, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [], "pos": [-13243, 1368], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["参考图"]}, {"id": 790, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [], "pos": [-13237, 1698], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["服装mask"]}, {"id": 791, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [], "pos": [-13241, 2198], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["服装原图"]}, {"id": 792, "flags": {}, "inputs": [{"link": 1498, "name": "any_a", "shape": 7, "type": "*"}, {"link": 1499, "name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 265, "outputs": [{"links": [1500], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-3010.509765625, 3271.515869140625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.42327880859375, 199.41488647460938], "type": "ConrainPythonExecutor", "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a]*any_b"]}, {"id": 793, "flags": {}, "inputs": [{"link": 1501, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 270, "outputs": [{"links": [1502], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [-2592.509765625, 2813.51611328125], "properties": {"Node name for S&R": "ImpactImageBatchToImageList"}, "size": [223.97938537597656, 66.6196060180664], "type": "ImpactImageBatchToImageList", "widgets_values": []}, {"id": 797, "flags": {}, "inputs": [{"link": 1510, "name": "destination", "type": "IMAGE"}, {"link": 1515, "name": "source", "type": "IMAGE"}, {"link": 1506, "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 115, "outputs": [{"links": [1513], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-8701, 2258], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "size": [315, 146], "type": "ImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 799, "flags": {}, "inputs": [{"link": 1508, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1509, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 111, "outputs": [{"links": [1510], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9106, 2315], "properties": {"Node name for S&R": "Image Blank"}, "size": [210, 140.97183227539062], "type": "Image Blank", "widgets_values": [512, 512, 255, 255, 255]}, {"id": 800, "flags": {}, "inputs": [{"link": 1507, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 107, "outputs": [{"links": [1508], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1509], "name": "height", "slot_index": 1, "type": "INT"}], "pos": [-9441, 2317], "properties": {"Node name for S&R": "Get Image Size"}, "size": [210, 46], "type": "Get Image Size", "widgets_values": []}, {"id": 833, "flags": {}, "inputs": [{"link": 1598, "name": "", "type": "*"}], "mode": 0, "order": 185, "outputs": [{"links": [1559, 1563, 1567], "name": "", "type": "IMAGE"}], "pos": [-8597.283203125, 845.2554321289062], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 834, "flags": {}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"label": "SAM_MODEL", "links": [1557, 1561], "name": "SAM_MODEL", "shape": 3, "slot_index": 0, "type": "SAM_MODEL"}], "pos": [-8861.5, 411.11846923828125], "properties": {"Node name for S&R": "Conrain_SAMModelLoader"}, "size": [269.19927978515625, 58], "type": "Conrain_SAMModelLoader", "widgets_values": ["sam_vit_h_cloth"]}, {"id": 835, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "sam_model", "link": 1557, "name": "sam_model", "type": "SAM_MODEL"}, {"label": "grounding_dino_model", "link": 1558, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"label": "image", "link": 1559, "name": "image", "type": "IMAGE"}, {"link": 1560, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": 0, "order": 186, "outputs": [{"label": "IMAGE", "links": [1579], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [1575], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "IMAGE", "slot_index": 2, "type": "IMAGE"}], "pos": [-8266.5009765625, 129.11795043945312], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "size": [260.3999938964844, 151.95924377441406], "type": "Conrain_GroundingDinoSAMSegment", "widgets_values": ["jacket", "white", 0.6]}, {"id": 836, "flags": {}, "inputs": [], "mode": 0, "order": 16, "outputs": [{"links": [1558, 1562], "name": "GROUNDING_DINO_MODEL", "slot_index": 0, "type": "GROUNDING_DINO_MODEL"}], "pos": [-8872.76171875, 271.8019104003906], "properties": {"Node name for S&R": "Conrain_GroundingDinoModelLoader"}, "size": [285.80181884765625, 58], "type": "Conrain_GroundingDinoModelLoader", "widgets_values": ["groundingdino_cloth"]}, {"id": 837, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"links": [1565, 1570, 1571, 1584], "name": "text", "slot_index": 0, "type": "*"}, {"name": "show_help", "type": "STRING"}], "pos": [-9858.5, 179.11776733398438], "properties": {"Node name for S&R": "CR Text"}, "size": [217.36741638183594, 128.27645874023438], "title": "抠图词", "type": "CR Text", "widgets_values": ["${clotheType}"]}, {"id": 838, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "sam_model", "link": 1561, "name": "sam_model", "type": "SAM_MODEL"}, {"label": "grounding_dino_model", "link": 1562, "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL"}, {"label": "image", "link": 1563, "name": "image", "type": "IMAGE"}, {"link": 1564, "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}}], "mode": 0, "order": 187, "outputs": [{"label": "IMAGE", "links": [], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [1587], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "IMAGE", "slot_index": 2, "type": "IMAGE"}], "pos": [-7207.49560546875, 436.1183776855469], "properties": {"Node name for S&R": "Conrain_GroundingDinoSAMSegment"}, "size": [260.3999938964844, 151.95924377441406], "type": "Conrain_GroundingDinoSAMSegment", "widgets_values": ["jacket", "white", 0.3]}, {"id": 839, "flags": {"collapsed": true}, "inputs": [{"link": 1565, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 51, "outputs": [{"links": [1566], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9462.5, 322.1181640625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [296.9554443359375, 286.73455810546875], "title": "需要替换的区域", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"upper garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"lower garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 840, "flags": {}, "inputs": [{"link": 1566, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 69, "outputs": [{"links": [1560], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [-9110.5, 223.1180419921875], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [210, 276], "type": "ShowText|pysssss", "widgets_values": [["coat. shirt. jacket"], "coat. shirt. jacket", "coat. shirt. jacket", "coat. shirt. jacket"]}, {"id": 841, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [], "pos": [-9435.5, 365.1184387207031], "properties": {}, "size": [210, 111.45333862304688], "title": "抠图词", "type": "Note", "widgets_values": ["上装传入：coat. shirt. jacket\n下装：trousers\n套装：clothing\n"]}, {"id": 842, "flags": {}, "inputs": [{"link": 1567, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 188, "outputs": [{"links": [1568], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-8737.681640625, -77.98147583007812], "properties": {"Node name for S&R": "DensePosePreprocessor"}, "size": [315, 106], "type": "DensePosePreprocessor", "widgets_values": ["densepose_r101_fpn_dl.torchscript", "<PERSON><PERSON><PERSON> (MagicAnimate)", 512]}, {"id": 843, "flags": {}, "inputs": [{"link": 1568, "name": "image", "type": "IMAGE"}, {"link": 1569, "name": "color_list", "type": "STRING", "widget": {"name": "color_list"}}], "mode": 0, "order": 191, "outputs": [{"links": [1572], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-8246.5556640625, -91.7675170898438], "properties": {"Node name for S&R": "ConrainMaskFromColors"}, "size": [228.88619995117188, 124.55016326904297], "type": "ConrainMaskFromColors", "widgets_values": ["128,128,128\n255,128,0", 2]}, {"id": 844, "flags": {"collapsed": true}, "inputs": [{"link": 1570, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 52, "outputs": [{"links": [1569], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9160.283203125, 22.255279541015625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [372.4948425292969, 392.6221008300781], "title": "densepose提取mask区域", "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    body_hand = '''73,31,112'''\n\n    leg = '''44,113,142\n40,124,142\n31,154,138\n32,163,133'''\n\n    if any_a == \"upper garment\":\n        mask = body_hand\n    elif any_a == \"lower garment\":\n        mask = leg\n    else:\n        mask = body_hand +\"\\n\"+ leg\n    return [mask]\n"]}, {"id": 845, "flags": {"collapsed": true}, "inputs": [{"link": 1571, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 53, "outputs": [{"links": [1583], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-9455.5, 560.1185302734375], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [300.1857604980469, 284.6385192871094], "title": "需要排除的mask", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a == \"lower garment\":\n        mask = 'coat. shirt. jacket'\n    elif any_a == \"upper garment\":\n        mask = 'trousers'\n    else:\n        mask = 'clothing'\n\n    return [mask]"]}, {"id": 846, "flags": {}, "inputs": [{"link": 1572, "name": "mask", "type": "MASK"}], "mode": 0, "order": 193, "outputs": [{"links": [1576], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7872.55419921875, -84.7614135742188], "properties": {"Node name for S&R": "GrowMask"}, "size": [210, 95.39423370361328], "type": "GrowMask", "widgets_values": [0, false]}, {"id": 847, "flags": {}, "inputs": [{"link": 1573, "name": "masks_a", "type": "MASK"}, {"link": 1574, "name": "masks_b", "type": "MASK"}], "mode": 0, "order": 197, "outputs": [{"links": [1585], "name": "MASKS", "slot_index": 0, "type": "MASK"}], "pos": [-6844.49560546875, 95.11788940429688], "properties": {"Node name for S&R": "Masks Add"}, "size": [210, 46], "type": "Masks Add", "widgets_values": []}, {"id": 848, "flags": {}, "inputs": [{"link": 1575, "name": "mask", "type": "MASK"}], "mode": 0, "order": 190, "outputs": [{"links": [1574], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7882.50048828125, 242.11807250976562], "properties": {"Node name for S&R": "GrowMask"}, "size": [210, 93.73921966552734], "type": "GrowMask", "widgets_values": [0, false]}, {"id": 849, "flags": {}, "inputs": [{"link": 1576, "name": "mask", "type": "MASK"}, {"link": 1577, "name": "height", "type": "INT", "widget": {"name": "height"}}, {"link": 1578, "name": "width", "type": "INT", "widget": {"name": "width"}}], "mode": 0, "order": 195, "outputs": [{"links": [1573], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7576.49658203125, -67.88180541992188], "properties": {"Node name for S&R": "JWMaskResize"}, "size": [259.6524353027344, 135.95858764648438], "type": "JWMaskResize", "widgets_values": [512, 512, "bilinear"]}, {"id": 850, "flags": {}, "inputs": [{"link": 1579, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 189, "outputs": [{"links": [1578, 1580], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1577, 1581], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-7894.55517578125, 94.22164916992188], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 851, "flags": {}, "inputs": [{"link": 1580, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1581, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 192, "outputs": [{"links": [1582], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-7228.49560546875, -82.88174438476562], "properties": {"Node name for S&R": "Image Blank"}, "size": [315, 154], "type": "Image Blank", "widgets_values": [512, 512, 255, 255, 255]}, {"id": 852, "flags": {}, "inputs": [{"link": 1582, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 194, "outputs": [{"links": [1588], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-6853.49560546875, -76.88180541992188], "properties": {"Node name for S&R": "ImageToMask"}, "size": [210, 68.49308776855469], "type": "ImageToMask", "widgets_values": ["red"]}, {"id": 853, "flags": {}, "inputs": [{"link": 1583, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 70, "outputs": [{"links": [1564], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [-9109.5, 561.1185913085938], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [210, 276], "type": "ShowText|pysssss", "widgets_values": [["trousers"], "trousers", "trousers", "trousers"]}, {"id": 854, "flags": {}, "inputs": [{"link": 1584, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 54, "outputs": [{"links": [1589], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-7226.49560546875, 133.11788940429688], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [304.83380126953125, 204.48739624023438], "title": "需要排除的区域", "type": "ConrainPythonExecutor", "widgets_values": ["# 优先取用户的输入\ndef call(any_a, any_b, any_c, any_d):\n    if any_a in (\"upper garment\",\"lower garment\"):\n        return [True]\n\n    return [False]"]}, {"id": 855, "flags": {}, "inputs": [{"link": 1585, "name": "masks_a", "type": "MASK"}, {"link": 1586, "name": "masks_b", "type": "MASK"}], "mode": 4, "order": 198, "outputs": [{"links": [1596, 1599], "name": "MASKS", "slot_index": 0, "type": "MASK"}], "pos": [-6557.28271484375, 106.25527954101562], "properties": {"Node name for S&R": "Masks Subtract"}, "size": [210, 46], "type": "Masks Subtract", "widgets_values": []}, {"id": 856, "flags": {}, "inputs": [{"link": 1587, "name": "on_true", "type": "*"}, {"link": 1588, "name": "on_false", "type": "*"}, {"link": 1589, "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}}], "mode": 0, "order": 196, "outputs": [{"links": [1586], "name": "*", "slot_index": 0, "type": "*"}], "pos": [-6546.49560546875, -81.88174438476562], "properties": {"Node name for S&R": "easy ifElse"}, "size": [210, 82.75738525390625], "type": "easy ifElse", "widgets_values": [false]}, {"id": 862, "flags": {}, "inputs": [{"link": 1596, "name": "mask", "type": "MASK"}], "mode": 0, "order": 199, "outputs": [{"links": [1597, 1602], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6398.1064453125, 202.65988159179688], "properties": {"Node name for S&R": "MaskToImage"}, "size": [188.457763671875, 48.499874114990234], "type": "MaskToImage", "widgets_values": []}, {"id": 863, "flags": {}, "inputs": [{"link": 1597, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 201, "outputs": [], "pos": [-6175.1064453125, -17.339996337890625], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 864, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 43, "outputs": [{"label": "STRING", "links": [1600], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [1601], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-6784.81591796875, 473.802734375], "properties": {"Node name for S&R": "Text String"}, "size": [300.3023681640625, 216.65859985351562], "title": "保存参看图的mask", "type": "Text String", "widgets_values": ["${outputPath}", "mask_${fileNamePrefix}", "", ""]}, {"id": 865, "flags": {}, "inputs": [{"label": "images", "link": 1602, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 1600, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 1601, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 202, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-6209.28271484375, 457.2551574707031], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [231.75296020507812, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 869, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [], "pos": [-9315.283203125, -28.744720458984375], "properties": {}, "size": [210, 60], "type": "Note", "widgets_values": ["躯干\n73,31,112\n\n手臂\n38,173,129\n70,192,110\n170,220,50\n114,208,86\n53,183,121\n92,200,99\n197,224,33\n142,214,68\n\n腿\n44,113,142\n40,124,142\n31,154,138\n32,163,133"]}, {"id": 870, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 20, "outputs": [{"links": [1664], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [-10420, -5170], "properties": {"Node name for S&R": "VAELoader"}, "size": [315, 58], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 871, "flags": {}, "inputs": [{"link": 1607, "name": "conditioning", "type": "CONDITIONING"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 93, "outputs": [{"links": [1717], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9500, -4130], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": [3.5]}, {"id": 872, "flags": {}, "inputs": [{"link": 1608, "name": "base_positive", "type": "CONDITIONING"}, {"link": 1609, "name": "base_negative", "type": "CONDITIONING"}, {"link": 1610, "name": "controlnet_stack", "type": "CONTROL_NET_STACK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 133, "outputs": [{"links": [1616], "name": "base_pos", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"name": "base_neg", "shape": 3, "type": "CONDITIONING"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [-8340, -4320], "properties": {"Node name for S&R": "CR Apply Multi-ControlNet"}, "size": [274.56842041015625, 98], "type": "CR Apply Multi-ControlNet", "widgets_values": ["On"]}, {"id": 873, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 21, "outputs": [{"links": [1667], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-10420, -5360], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 874, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 22, "outputs": [{"links": [1666], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-10420, -5530], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "default"]}, {"id": 875, "flags": {"collapsed": true}, "inputs": [{"link": 1611, "name": "clip", "type": "CLIP"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 83, "outputs": [{"links": [1609, 1718], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9490, -4030], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 125.79536437988281], "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 876, "flags": {"collapsed": false}, "inputs": [{"link": 1612, "name": "clip", "type": "CLIP"}, {"link": 1613, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 84, "outputs": [{"links": [1607], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-9800, -4080], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 125.79536437988281], "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 877, "flags": {"collapsed": true}, "inputs": [{"link": 1614, "name": "model", "type": "MODEL"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 80, "outputs": [{"links": [1615], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-9790, -4150], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [210, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 878, "flags": {}, "inputs": [{"link": 1615, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1616, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 135, "outputs": [{"links": [1618], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [-7720, -4180], "properties": {"Node name for S&R": "BasicGuider"}, "size": [263.1893615722656, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 879, "flags": {}, "inputs": [{"link": 1617, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 1618, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 1619, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 1620, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 1621, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 136, "outputs": [{"links": [1628], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [-7310, -4310], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [314.************, 106], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 880, "flags": {"collapsed": true}, "inputs": [{"link": 1622, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 103, "outputs": [{"links": [1626], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [-9800, -4750], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [354.8526611328125, 193.51210021972656], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 881, "flags": {"collapsed": true}, "inputs": [{"link": 1623, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 102, "outputs": [{"links": [1625], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [-9800, -4830], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [354.8526611328125, 193.51210021972656], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\treturn [any_a//16*16]"]}, {"id": 882, "flags": {}, "inputs": [{"link": 1624, "name": "image", "type": "IMAGE"}, {"link": 1625, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1626, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 109, "outputs": [{"links": [1729], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "x", "type": "INT"}, {"name": "y", "type": "INT"}], "pos": [-9550, -5020], "properties": {"Node name for S&R": "ImageCrop+"}, "size": [316.8935546875, 239.64944458007812], "type": "ImageCrop+", "widgets_values": [1024, 1024, "top-left", 0, 0]}, {"id": 883, "flags": {}, "inputs": [{"link": 1627, "name": "", "type": "*"}], "mode": 0, "order": 117, "outputs": [{"links": [1637], "name": "", "type": "IMAGE"}], "pos": [-9100, -5540], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 884, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 23, "outputs": [{"links": [1636], "name": "model", "slot_index": 0, "type": "MODEL_SAPIEN"}], "pos": [-9060, -5400], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "size": [659.2952270507812, 298], "type": "Sapiens<PERSON><PERSON>der", "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, false]}, {"id": 885, "flags": {}, "inputs": [{"link": 1628, "name": "samples", "type": "LATENT"}, {"link": 1629, "name": "vae", "type": "VAE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 137, "outputs": [{"links": [1731], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6900, -4270], "properties": {"Node name for S&R": "VAEDecode"}, "size": [190.54541015625, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 886, "flags": {}, "inputs": [{"link": 1630, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 122, "outputs": [{"links": [1634, 1722, 1723], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-9240, -3840], "properties": {"Node name for S&R": "Zoe-DepthMapPreprocessor"}, "size": [210, 58], "type": "Zoe-DepthMapPreprocessor", "widgets_values": [1024]}, {"id": 887, "flags": {}, "inputs": [{"link": 1631, "name": "", "type": "*"}], "mode": 0, "order": 118, "outputs": [{"links": [1630, 1632, 1720], "name": "", "type": "IMAGE"}], "pos": [-9460, -3950], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 888, "flags": {}, "inputs": [{"link": 1632, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 123, "outputs": [{"links": [1724, 1760], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-9240, -3680], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "size": [212.60569763183594, 82], "type": "<PERSON><PERSON>", "widgets_values": [0.2, 0.8]}, {"id": 889, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 24, "outputs": [{"links": [1617], "name": "NOISE", "shape": 3, "slot_index": 0, "type": "NOISE"}], "pos": [-7720, -4320], "properties": {"Node name for S&R": "RandomNoise"}, "size": [255.33419799804688, 82], "type": "RandomNoise", "widgets_values": ["${seed}", "randomize"]}, {"id": 890, "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"links": [1619], "name": "SAMPLER", "shape": 3, "slot_index": 0, "type": "SAMPLER"}], "pos": [-7720, -4050], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [314.************, 58], "type": "KSamplerSelect", "widgets_values": ["euler"]}, {"id": 891, "flags": {}, "inputs": [{"link": 1633, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 125, "outputs": [{"links": [1715, 1721], "name": "mask", "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "type": "MASK"}], "pos": [-8980, -4890], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [340.20001220703125, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [2, 0, true, false, 10, 1, 1, true]}, {"id": 892, "flags": {}, "inputs": [{"link": 1634, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 126, "outputs": [], "pos": [-8900, -4020], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 893, "flags": {}, "inputs": [{"link": 1635, "name": "", "type": "*"}], "mode": 0, "order": 89, "outputs": [{"links": [1624, 1756], "name": "", "type": "IMAGE"}], "pos": [-10000, -5040], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 894, "flags": {}, "inputs": [{"link": 1636, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1637, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 121, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [1633], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [-8330, -5490], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [315, 258], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 895, "flags": {}, "inputs": [{"link": 1638, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1639, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 64, "outputs": [{"links": [1659], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [-10090, -2690], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 896, "flags": {}, "inputs": [{"link": 1640, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1641, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 65, "outputs": [{"links": [1660], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [-10090, -2540], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 897, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 26, "outputs": [{"links": [1639], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-10430, -2680], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 898, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 27, "outputs": [{"links": [1641], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-10450, -2530], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [213.92233276367188, 58], "type": "JWStringToInteger", "widgets_values": ["1"]}, {"id": 899, "flags": {}, "inputs": [{"link": 1642, "name": "", "type": "*"}], "mode": 0, "order": 149, "outputs": [{"links": [1765], "name": "", "type": "MASK"}], "pos": [-6940, -2080], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 900, "flags": {}, "inputs": [{"link": 1643, "name": "", "type": "*"}], "mode": 0, "order": 142, "outputs": [{"links": [1764], "name": "", "type": "IMAGE"}], "pos": [-6960, -2160], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 901, "flags": {}, "inputs": [{"link": 1644, "name": "", "type": "*"}], "mode": 0, "order": 146, "outputs": [{"links": [1766], "name": "", "type": "IMAGE_BOUNDS"}], "pos": [-6940, -2010], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 902, "flags": {}, "inputs": [{"link": 1645, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 145, "outputs": [{"links": [1743], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-9400, -1810], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [210, 136.1963348388672], "type": "UpscaleSizeCalculator", "widgets_values": [1024]}, {"id": 903, "flags": {}, "inputs": [{"link": 1646, "name": "", "type": "*"}], "mode": 0, "order": 81, "outputs": [{"links": [1665, 1730], "name": "", "type": "MODEL"}], "pos": [-7970, -2140], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 904, "flags": {}, "inputs": [{"link": 1647, "name": "conditioning", "slot_index": 0, "type": "CONDITIONING"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 105, "outputs": [{"links": [1749], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [-7880, -1650], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": [2]}, {"id": 905, "flags": {}, "inputs": [{"link": 1648, "name": "conditioning", "type": "CONDITIONING"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 106, "outputs": [{"links": [1750], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-7880, -1790], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "size": [222.26809692382812, 26], "type": "ConditioningZeroOut", "widgets_values": []}, {"id": 906, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 28, "outputs": [{"links": [1690], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [-7330, -1870], "properties": {"Node name for S&R": "RandomNoise"}, "size": [221.2050323486328, 83.35130310058594], "type": "RandomNoise", "widgets_values": [0, "fixed"]}, {"id": 907, "flags": {}, "inputs": [{"link": 1649, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1650, "name": "conditioning", "type": "CONDITIONING"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 165, "outputs": [{"links": [1691], "name": "GUIDER", "shape": 3, "type": "GUIDER"}], "pos": [-7310, -2110], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 908, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 29, "outputs": [{"links": [1692], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [-7340, -1990], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["dpm_2"]}, {"id": 909, "flags": {}, "inputs": [{"link": 1651, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1652, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 143, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [1705], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [-10080, -1960], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [247.4663848876953, 259.6097412109375], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 910, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": false}, "inputs": [{"link": 1653, "name": "clip", "type": "CLIP"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 58, "outputs": [{"links": [1678], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-8030, -2960], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 911, "flags": {}, "inputs": [{"link": 1654, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 90, "outputs": [{"links": [1708, 1709], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-9440, -2730], "properties": {"Node name for S&R": "ImageResize+"}, "size": [261.8576965332031, 224.5092315673828], "type": "ImageResize+", "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 912, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 30, "outputs": [{"label": "BBOX_DETECTOR", "links": [1707], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [-9810, -2520], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 913, "flags": {}, "inputs": [{"link": 1655, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 114, "outputs": [{"links": [1710], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-8840, -3140], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 914, "flags": {}, "inputs": [{"link": 1656, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 57, "outputs": [{"links": [1738], "name": "", "type": "STRING"}], "pos": [-10590, -2380], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 915, "flags": {}, "inputs": [{"link": 1657, "name": "", "type": "*"}], "mode": 0, "order": 85, "outputs": [{"links": [1701], "name": "", "type": "CLIP"}], "pos": [-10620, -1580], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 916, "flags": {}, "inputs": [{"link": 1658, "name": "image1", "type": "IMAGE"}, {"link": 1659, "name": "image2", "type": "IMAGE"}, {"link": 1660, "name": "image3", "type": "IMAGE"}, {"name": "image4", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 79, "outputs": [{"links": [1654], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [-9770, -2720], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "size": [231.68582153320312, 104.573486328125], "type": "ImpactMakeImageList", "widgets_values": []}, {"id": 917, "flags": {}, "inputs": [{"link": 1661, "name": "segs", "type": "SEGS"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 104, "outputs": [{"links": [1706], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"links": [], "name": "remained_SEGS", "slot_index": 1, "type": "SEGS"}], "pos": [-9110, -3140], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [210, 158.96408081054688], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 918, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 31, "outputs": [{"links": [1613, 1656], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [-10460, -3960], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [602.1065673828125, 245.61325073242188], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 991, "fixed"]}, {"id": 919, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1662, "name": "", "type": "*"}], "mode": 0, "order": 73, "outputs": [{"links": [1611, 1612, 1657], "name": "", "type": "CLIP"}], "pos": [-10220, -4160], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 920, "flags": {}, "inputs": [{"link": 1663, "name": "", "type": "*"}], "mode": 0, "order": 71, "outputs": [{"links": [1726, 1751], "name": "", "type": "VAE"}], "pos": [-8250, -2130], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 921, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [{"link": 1664, "name": "", "type": "*"}], "mode": 0, "order": 55, "outputs": [{"links": [1629, 1663, 1719], "name": "", "type": "VAE"}], "pos": [-9990, -5170], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 922, "flags": {}, "inputs": [{"link": 1665, "name": "model", "type": "MODEL"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 91, "outputs": [{"links": [1649], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-7890, -1920], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [210, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 923, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1666, "name": "model", "type": "MODEL"}, {"link": 1667, "name": "clip", "type": "CLIP"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 56, "outputs": [{"links": [1668], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [1662], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [-10010, -5530], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [516.7993774414062, 126], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 924, "bgcolor": "#335", "color": "#223", "flags": {}, "inputs": [{"link": 1668, "name": "", "type": "*"}], "mode": 0, "order": 72, "outputs": [{"links": [1614, 1646, 1761], "name": "", "type": "MODEL"}], "pos": [-10230, -4290], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 925, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 1669, "name": "clip", "type": "CLIP"}, {"link": 1670, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 95, "outputs": [{"links": [1677], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-7950, -3120], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 926, "flags": {"collapsed": true}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 32, "outputs": [{"label": "CONTROL_NET", "links": [1674], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [-7710, -2920], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 927, "flags": {"collapsed": true}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 33, "outputs": [{"label": "FACEANALYSIS", "links": [1673], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [-7710, -3040], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 928, "flags": {"collapsed": true}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 34, "outputs": [{"label": "INSTANTID", "links": [1672], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [-7710, -3140], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 929, "flags": {}, "inputs": [{"link": 1671, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 129, "outputs": [{"links": [1682], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [-9070, -2600], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 930, "flags": {}, "inputs": [{"label": "instantid", "link": 1672, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 1673, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 1674, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 1675, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 1676, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 1677, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 1678, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 1679, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 157, "outputs": [{"label": "MODEL", "links": [1733], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [1684], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [1685], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [-7290, -3170], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 931, "flags": {}, "inputs": [{"link": 1680, "name": "images", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 124, "outputs": [{"links": [1671], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-8830, -2850], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "size": [222.84095764160156, 26], "type": "easy imageListToImageBatch", "widgets_values": []}, {"id": 932, "flags": {}, "inputs": [{"link": 1681, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 151, "outputs": [{"links": [1737], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [-8330, -3020], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 933, "flags": {}, "inputs": [{"link": 1682, "name": "faces", "type": "FACE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 132, "outputs": [{"links": [1683], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [-8830, -2600], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 934, "flags": {}, "inputs": [{"link": 1683, "name": "", "type": "*"}], "mode": 0, "order": 134, "outputs": [{"links": [1675], "name": "", "type": "IMAGE"}], "pos": [-8490, -3170], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 935, "flags": {}, "inputs": [{"link": 1684, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 1685, "name": "negative", "type": "CONDITIONING"}, {"link": 1686, "name": "vae", "type": "VAE"}, {"link": 1687, "name": "pixels", "type": "IMAGE"}, {"link": 1688, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 159, "outputs": [{"links": [1734], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [1735], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [1736], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [-7040, -2980], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 936, "flags": {}, "inputs": [{"link": 1689, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 86, "outputs": [{"links": [1670], "name": "", "type": "STRING"}], "pos": [-8490, -3090], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 937, "flags": {}, "inputs": [{"link": 1690, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 1691, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 1692, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 1693, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 1694, "name": "latent_image", "type": "LATENT"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 167, "outputs": [{"links": [1725], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [-7010, -1910], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 109.************], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 938, "flags": {}, "inputs": [{"link": 1695, "name": "images", "type": "IMAGE"}, {"link": 1696, "name": "face", "type": "FACE"}, {"link": 1697, "name": "crop", "type": "IMAGE"}, {"link": 1698, "name": "mask", "type": "MASK"}, {"link": 1699, "name": "warp", "type": "WARP"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 170, "outputs": [{"links": [1767], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-6470, -1780], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 939, "flags": {}, "inputs": [{"link": 1700, "name": "", "type": "*"}], "mode": 0, "order": 140, "outputs": [{"links": [1643, 1652, 1704], "name": "", "type": "IMAGE"}], "pos": [-10370, -2140], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 940, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 35, "outputs": [{"links": [1651, 1739], "name": "model", "slot_index": 0, "type": "MODEL_SAPIEN"}], "pos": [-10420, -1960], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "size": [283.3903503417969, 298], "type": "Sapiens<PERSON><PERSON>der", "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 941, "flags": {}, "inputs": [{"link": 1701, "name": "", "type": "*"}], "mode": 0, "order": 94, "outputs": [{"links": [1702], "name": "", "type": "CLIP"}], "pos": [-8690, -1560], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 942, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 1702, "name": "clip", "type": "CLIP"}, {"link": 1703, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 99, "outputs": [{"links": [1647, 1648], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [-8340, -1640], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [296.32208251953125, 77.2895278930664], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 943, "flags": {}, "inputs": [{"link": 1704, "name": "image", "type": "IMAGE"}, {"link": 1705, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 144, "outputs": [{"links": [1645, 1742], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [1644], "name": "IMAGE_BOUNDS", "shape": 3, "slot_index": 1, "type": "IMAGE_BOUNDS"}, {"links": [1741], "name": "MASK", "shape": 3, "slot_index": 2, "type": "MASK"}, {"links": [], "name": "SCALE_BY", "shape": 3, "slot_index": 3, "type": "FLOAT"}], "pos": [-9710, -2100], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "size": [285.6000061035156, 149.66432189941406], "type": "BoundedImageCropWithMask_v3_LR", "widgets_values": [20, 20]}, {"id": 944, "flags": {"collapsed": true}, "inputs": [{"link": 1706, "name": "segs", "type": "SEGS"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 110, "outputs": [{"links": [1655], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-8950, -3190], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [289.79998779296875, 26], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 945, "flags": {}, "inputs": [{"link": 1707, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1708, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 98, "outputs": [{"links": [1661], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [-9440, -3150], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [277.6741943359375, 319.97015380859375], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 946, "flags": {}, "inputs": [{"link": 1709, "name": "image", "type": "IMAGE"}, {"link": 1710, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 119, "outputs": [{"links": [1680], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [-9110, -2840], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [235.1999969482422, 150.37045288085938], "type": "Bounded Image Crop with Mask", "widgets_values": [64, 64, 64, 64]}, {"id": 947, "flags": {"collapsed": false}, "inputs": [{"link": 1711, "name": "samples", "type": "LATENT"}, {"link": 1712, "name": "vae", "type": "VAE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 163, "outputs": [{"links": [1752], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-6700, -2640], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 948, "flags": {}, "inputs": [{"link": 1713, "name": "", "type": "*"}], "mode": 0, "order": 154, "outputs": [{"links": [1679, 1687], "name": "", "type": "IMAGE"}], "pos": [-7510, -2860], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 949, "flags": {}, "inputs": [{"link": 1714, "name": "faces", "type": "FACE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 153, "outputs": [{"links": [1713, 1727, 1740, 1771], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [1699], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [-8020, -2800], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 950, "flags": {}, "inputs": [{"link": 1715, "name": "mask", "type": "MASK"}], "mode": 4, "order": 130, "outputs": [], "pos": [-8310, -4890], "properties": {"Node name for S&R": "MaskPreview+"}, "size": [210, 246], "type": "MaskPreview+", "widgets_values": []}, {"id": 951, "flags": {}, "inputs": [{"link": 1716, "name": "samples", "type": "LATENT"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 166, "outputs": [{"links": [1694], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [-7320, -1670], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "size": [210, 80.70018768310547], "type": "RepeatLatentBatch", "widgets_values": [1]}, {"id": 952, "flags": {}, "inputs": [{"link": 1717, "name": "positive", "type": "CONDITIONING"}, {"link": 1718, "name": "negative", "type": "CONDITIONING"}, {"link": 1719, "name": "vae", "type": "VAE"}, {"link": 1720, "name": "pixels", "type": "IMAGE"}, {"link": 1721, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 131, "outputs": [{"links": [1608], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "shape": 3, "type": "CONDITIONING"}, {"links": [1621], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [-8900, -4320], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [216.59999084472656, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 953, "flags": {}, "inputs": [{"link": 1722, "name": "image_1", "shape": 7, "type": "IMAGE"}, {"link": 1723, "name": "image_2", "shape": 7, "type": "IMAGE"}, {"link": 1724, "name": "image_3", "shape": 7, "type": "IMAGE"}, {"name": "controlnet_stack", "shape": 7, "type": "CONTROL_NET_STACK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 127, "outputs": [{"links": [1610], "name": "CONTROLNET_STACK", "shape": 3, "slot_index": 0, "type": "CONTROL_NET_STACK"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [-8570, -3850], "properties": {"Node name for S&R": "CR Multi-ControlNet Stack"}, "size": [563.9595947265625, 454], "type": "CR Multi-ControlNet Stack", "widgets_values": ["On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.6, 0, 0.2, "On", "xlab_flux_controlnet/flux-depth-controlnet-v3.safetensors", 0.2, 0.2, 0.8, "On", "xlab_flux_controlnet/flux-canny-controlnet-v3.safetensors", 0.4, 0, 0.6]}, {"id": 954, "flags": {"collapsed": false}, "inputs": [{"link": 1725, "name": "samples", "type": "LATENT"}, {"link": 1726, "name": "vae", "type": "VAE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 168, "outputs": [{"links": [1728], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-7020, -1670], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 955, "flags": {"collapsed": false}, "inputs": [{"link": 1727, "name": "image_ref", "type": "IMAGE"}, {"link": 1728, "name": "image_target", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 169, "outputs": [{"links": [1697], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [-6750, -1730], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 956, "flags": {}, "inputs": [{"link": 1729, "name": "", "type": "*"}], "mode": 0, "order": 113, "outputs": [{"links": [1627, 1631], "name": "", "type": "IMAGE"}], "pos": [-9390, -4540], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 957, "flags": {}, "inputs": [{"link": 1730, "name": "model", "type": "MODEL"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 92, "outputs": [{"links": [1693], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [-7590, -1710], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["sgm_uniform", 8, 0.1]}, {"id": 958, "flags": {}, "inputs": [{"link": 1731, "name": "image", "type": "IMAGE"}, {"link": 1732, "name": "resize_width", "type": "INT", "widget": {"name": "resize_width"}}], "mode": 0, "order": 138, "outputs": [{"links": [1745], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [-6950, -4130], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 1, 1024, "lanc<PERSON>s", "true", 8]}, {"id": 959, "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 36, "outputs": [{"links": [1676], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [1653, 1669], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [1686, 1712], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [-8330, -3190], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [314.34735107421875, 128.43458557128906], "type": "CheckpointLoaderSimple", "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 960, "flags": {}, "inputs": [{"link": 1733, "name": "model", "type": "MODEL"}, {"link": 1734, "name": "positive", "type": "CONDITIONING"}, {"link": 1735, "name": "negative", "type": "CONDITIONING"}, {"link": 1736, "name": "latent_image", "type": "LATENT"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 162, "outputs": [{"links": [1711], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [-6780, -3000], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": [42029499340256, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.5]}, {"id": 961, "flags": {}, "inputs": [{"link": 1737, "name": "faces", "type": "FACE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 152, "outputs": [{"links": [1696, 1714], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [-8330, -2840], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 962, "flags": {}, "inputs": [{"link": 1738, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 74, "outputs": [{"links": [1689, 1703], "name": "", "type": "STRING"}], "pos": [-8670, -2380], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 963, "flags": {}, "inputs": [{"link": 1739, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1740, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 155, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [-8320, -2620], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [265.8616027832031, 258], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "23,24,25,26,27", false, 255, 255, 255]}, {"id": 964, "flags": {"collapsed": false}, "inputs": [{"link": 1741, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 147, "outputs": [{"links": [1642], "name": "mask", "shape": 3, "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "shape": 3, "type": "MASK"}], "pos": [-9170, -2110], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [236.14947509765625, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 965, "flags": {}, "inputs": [{"label": "image", "link": 1742, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 1743, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 148, "outputs": [{"label": "IMAGE", "links": [1744], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-9060, -1770], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [250.58731079101562, 109.74829864501953], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 966, "flags": {}, "inputs": [{"link": 1744, "name": "", "type": "*"}], "mode": 0, "order": 150, "outputs": [{"links": [1681, 1695], "name": "", "type": "IMAGE"}], "pos": [-8730, -1770], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 968, "flags": {}, "inputs": [{"link": 1745, "name": "image", "type": "IMAGE"}, {"link": 1746, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 1747, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 139, "outputs": [{"links": [1700, 1763], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [-7340, -3890], "properties": {"Node name for S&R": "ImageScale"}, "size": [315, 130], "type": "ImageScale", "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 969, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 44, "outputs": [{"links": [1658], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-10450, -3150], "properties": {"Node name for S&R": "LoadImage"}, "size": [235.8109893798828, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 970, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 45, "outputs": [{"links": [1638], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-10110, -3150], "properties": {"Node name for S&R": "LoadImage"}, "size": [249.60922241210938, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 971, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 46, "outputs": [{"links": [1640], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [-9770, -3150], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 972, "flags": {"collapsed": false}, "inputs": [{"link": 1748, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 160, "outputs": [{"links": [1698], "name": "mask", "shape": 3, "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "shape": 3, "type": "MASK"}], "pos": [-7060, -2730], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [236.14947509765625, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [40, 0, false, false, 0, 1, 1, false]}, {"id": 973, "flags": {}, "inputs": [{"link": 1749, "name": "positive", "type": "CONDITIONING"}, {"link": 1750, "name": "negative", "slot_index": 1, "type": "CONDITIONING"}, {"link": 1751, "name": "vae", "type": "VAE"}, {"link": 1752, "name": "pixels", "type": "IMAGE"}, {"link": 1753, "name": "mask", "slot_index": 4, "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 164, "outputs": [{"links": [1650], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "shape": 3, "type": "CONDITIONING"}, {"links": [1716], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [-7610, -2110], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [216.59999084472656, 138], "type": "InpaintModelConditioning", "widgets_values": [false]}, {"id": 974, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1754, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 158, "outputs": [{"links": [1688, 1748, 1755], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [-7390, -2790], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 5, 4, 0, 1, true]}, {"id": 975, "flags": {}, "inputs": [{"link": 1755, "name": "mask", "type": "MASK"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 161, "outputs": [{"links": [1753], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [-7390, -2550], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [212.8609161376953, 178], "type": "MaskFastGrow", "widgets_values": [true, 20, 10, 0, 1, true]}, {"id": 976, "flags": {}, "inputs": [{"link": 1756, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 97, "outputs": [{"links": [1623], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1622], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-10060, -4850], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 977, "flags": {}, "inputs": [{"link": 1772, "name": "", "type": "*"}], "mode": 0, "order": 62, "outputs": [{"links": [1758, 1759], "name": "", "type": "IMAGE"}], "pos": [-10390, -4590], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 978, "flags": {"collapsed": true}, "inputs": [{"link": 1758, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 77, "outputs": [{"links": [1635], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [-10220, -4590], "properties": {"Node name for S&R": "ImageResize+"}, "size": [315, 218], "type": "ImageResize+", "widgets_values": [1536, 1536, "lanc<PERSON>s", "keep proportion", "downscale if bigger", 0]}, {"id": 979, "flags": {}, "inputs": [{"link": 1759, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 78, "outputs": [{"links": [1732, 1746], "name": "width", "slot_index": 0, "type": "INT"}, {"links": [1747], "name": "height", "slot_index": 1, "type": "INT"}, {"name": "count", "type": "INT"}], "pos": [-7270, -4130], "properties": {"Node name for S&R": "GetImageSize+"}, "size": [214.20001220703125, 66], "type": "GetImageSize+", "widgets_values": []}, {"id": 980, "flags": {}, "inputs": [{"link": 1760, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 128, "outputs": [], "pos": [-8900, -3650], "properties": {"Node name for S&R": "PreviewImage"}, "size": [210, 246], "type": "PreviewImage", "widgets_values": []}, {"id": 981, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 37, "outputs": [{"label": "STRING", "links": [1769], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [1770], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [-7360, -3660], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "title": "保存换头结果图", "type": "Text String", "widgets_values": ["${outputPath}", "face_${fileNamePrefix}", "", ""]}, {"id": 982, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1761, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 82, "outputs": [{"links": [1620], "name": "SIGMAS", "shape": 3, "slot_index": 0, "type": "SIGMAS"}], "pos": [-7710, -3910], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [309.76611328125, 106], "type": "BasicScheduler", "widgets_values": ["simple", 20, 0.9]}, {"id": 983, "flags": {}, "inputs": [{"link": 1762, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 172, "outputs": [], "pos": [-5947.119140625, -2106.30126953125], "properties": {"Node name for S&R": "PreviewImage"}, "size": [393.20245361328125, 409.597412109375], "type": "PreviewImage", "widgets_values": []}, {"id": 984, "flags": {}, "inputs": [{"link": 1763, "name": "images", "type": "IMAGE"}], "mode": 4, "order": 141, "outputs": [], "pos": [-6560, -3890], "properties": {"Node name for S&R": "PreviewImage"}, "size": [236.9750518798828, 273.7080078125], "type": "PreviewImage", "widgets_values": []}, {"id": 985, "flags": {}, "inputs": [{"link": 1764, "name": "target", "type": "IMAGE"}, {"link": 1765, "name": "target_mask", "type": "MASK"}, {"link": 1766, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 1767, "name": "source", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 171, "outputs": [{"links": [1762, 1768, 1776], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [-6540, -2110], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "size": [243.60000610351562, 172.84213256835938], "type": "Bounded Image Blend with Mask", "widgets_values": [1, 0]}, {"id": 986, "flags": {}, "inputs": [{"label": "images", "link": 1768, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 1769, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 1770, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 173, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [-6970, -3680], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 987, "flags": {"collapsed": false}, "inputs": [{"link": 1771, "name": "image", "type": "IMAGE"}], "mode": "${isNeedReplaceFace?then(0,4)}", "order": 156, "outputs": [{"links": [], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"links": [1754], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "slot_index": 2, "type": "BBOX"}], "pos": [-7730, -2820], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "size": [300, 500], "type": "easy humanSegmentation", "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}], "seed_widgets": {"275": 0, "401": 0, "779": 0, "889": 0, "906": 0, "918": 1, "960": 0}, "version": 0.4}}}}
{"client_id": "${client_id}", "prompt": {"1": {"inputs": {"noise": ["4", 0], "guider": ["5", 0], "sampler": ["7", 0], "sigmas": ["8", 0], "latent_image": ["9", 2]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "2": {"inputs": {"samples": ["1", 0], "vae": ["144", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "4": {"inputs": {"noise_seed": "${seed}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "5": {"inputs": {"model": ["6", 0], "conditioning": ["9", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "6": {"inputs": {"model": ["146", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "7": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "8": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["146", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "9": {"inputs": {"noise_mask": true, "positive": ["10", 0], "negative": ["11", 0], "vae": ["144", 0], "pixels": ["72", 0], "mask": ["73", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "10": {"inputs": {"guidance": 3.5, "conditioning": ["16", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "11": {"inputs": {"text": "", "clip": ["145", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "16": {"inputs": {"text": "${transRedrawDesc}", "clip": ["145", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "72": {"inputs": {"image": "${redrawImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "73": {"inputs": {"expand": 5, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["72", 1]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "144": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "145": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "146": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "164": {"inputs": {"output_path": ["165", 0], "filename_prefix": ["165", 1], "extension": "png", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["169", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "165": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "167": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "resize", "rescale_factor": 2, "resize_width": ["168", 0], "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["2", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "168": {"inputs": {"image": ["72", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "169": {"inputs": {"upscale_method": "bicubic", "width": ["168", 0], "height": ["168", 1], "crop": "disabled", "image": ["167", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 169, "last_link_id": 347, "nodes": [{"id": 5, "type": "BasicGuider", "pos": [390.81829833984375, 174.06961059570312], "size": [161.1999969482422, 46], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 9}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [4], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 23, "type": "Reroute", "pos": [-53.18169021606445, 52.069610595703125], "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 179}], "outputs": [{"name": "", "type": "MODEL", "links": [26, 27], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 90, "type": "Reroute", "pos": [-866.1282348632812, 72.44730377197266], "size": [75, 26], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 291}], "outputs": [{"name": "", "type": "CLIP", "links": [183, 184], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 89, "type": "Reroute", "pos": [-859, 25], "size": [75, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 290}], "outputs": [{"name": "", "type": "MODEL", "links": [179], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 4, "type": "RandomNoise", "pos": [340.81829833984375, 24.069612503051758], "size": [277.9598693847656, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [3], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed}", "randomize"]}, {"id": 10, "type": "FluxGuidance", "pos": [30, 230], "size": [211.60000610351562, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 15, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 145, "type": "DualCLIPLoader", "pos": [-1261.************, 162.11502075195312], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [291], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 146, "type": "UNETLoader", "pos": [-1259, 25], "size": [315, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [290], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 1, "type": "SamplerCustomAdvanced", "pos": [691, 124], "size": [355.20001220703125, 106], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 3, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 4, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 6, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 7, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 190}], "outputs": [{"name": "output", "type": "LATENT", "links": [1], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 9, "type": "InpaintModelConditioning", "pos": [30, 350], "size": [216.59999084472656, 138], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 10}, {"name": "negative", "type": "CONDITIONING", "link": 11, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 323}, {"name": "pixels", "type": "IMAGE", "link": 135}, {"name": "mask", "type": "MASK", "link": 193, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [9], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [189], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 8, "type": "BasicScheduler", "pos": [340.81829833984375, 374.0696105957031], "size": [210, 106], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 27}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 11, "type": "CLIPTextEncode", "pos": [-569, 383], "size": [266.98651123046875, 76], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 184}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 7, "type": "KSamplerSelect", "pos": [341, 274], "size": [210, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [6], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 144, "type": "VAELoader", "pos": [-1235, 370], "size": [210, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [321, 323], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 16, "type": "CLIPTextEncode", "pos": [-569, 49], "size": [394.06396484375, 153.04241943359375], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 183, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [15], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["${transRedrawDesc}"]}, {"id": 94, "type": "RepeatLatentBatch", "pos": [696, 401], "size": [315, 58], "flags": {}, "order": 22, "mode": 4, "inputs": [{"name": "samples", "type": "LATENT", "link": 189}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [190], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2]}, {"id": 73, "type": "ConrainGrowMaskWithBlur", "pos": [-474, 635], "size": [315, 246], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 131}], "outputs": [{"name": "mask", "type": "MASK", "links": [193], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [5, 0, true, false, 10, 1, 1, false]}, {"id": 56, "type": "Reroute", "pos": [-716, 640], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 168}], "outputs": [{"name": "", "type": "IMAGE", "links": [135], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 62, "type": "Reroute", "pos": [-722, 715], "size": [75, 26], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 130}], "outputs": [{"name": "", "type": "MASK", "links": [131], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 6, "type": "DifferentialDiffusion", "pos": [40, 150], "size": [184.8000030517578, 26], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 26}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [5], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 2, "type": "VAEDecode", "pos": [1121, 124], "size": [210, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1}, {"name": "vae", "type": "VAE", "link": 321}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [344], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 169, "type": "ImageScale", "pos": [752.1070556640625, 607.1694946289062], "size": [315, 130], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 343}, {"name": "width", "type": "INT", "link": 341, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 342, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [345], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["bicubic", 512, 512, "disabled"]}, {"id": 164, "type": "ConrainImageSave", "pos": [741.7228393554688, 817.4902954101562], "size": [315, 274], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 345}, {"name": "output_path", "type": "STRING", "link": 337, "widget": {"name": "output_path"}}, {"name": "filename_prefix", "type": "STRING", "link": 338, "widget": {"name": "filename_prefix"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 167, "type": "CR Upscale Image", "pos": [284.8659362792969, 617.8369750976562], "size": [315, 222], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 344}, {"name": "resize_width", "type": "INT", "link": 346, "widget": {"name": "resize_width"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [343], "slot_index": 0}, {"name": "show_help", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "resize", 2, 1024, "lanc<PERSON>s", "true", 8]}, {"id": 165, "type": "Text String", "pos": [288.06011962890625, 899.0267333984375], "size": [315, 190], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [337], "slot_index": 0}, {"name": "STRING", "type": "STRING", "links": [338], "slot_index": 1}, {"name": "STRING", "type": "STRING", "links": null}, {"name": "STRING", "type": "STRING", "links": null}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 168, "type": "GetImageSize+", "pos": [26.164735794067383, 629.5550537109375], "size": [214.20001220703125, 66], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 347}], "outputs": [{"name": "width", "type": "INT", "links": [341, 346], "slot_index": 0}, {"name": "height", "type": "INT", "links": [342], "slot_index": 1}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}}, {"id": 166, "type": "Reroute", "pos": [-79.51700592041016, 631.63818359375], "size": [75, 26], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 340}], "outputs": [{"name": "", "type": "IMAGE", "links": [347], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 72, "type": "LoadImage", "pos": [-1274, 610], "size": [496.0507507324219, 444.35638427734375], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [168, 340], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [130], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${redrawImage}", "image"], "color": "#232", "bgcolor": "#353"}], "links": [[1, 1, 0, 2, 0, "LATENT"], [3, 4, 0, 1, 0, "NOISE"], [4, 5, 0, 1, 1, "GUIDER"], [5, 6, 0, 5, 0, "MODEL"], [6, 7, 0, 1, 2, "SAMPLER"], [7, 8, 0, 1, 3, "SIGMAS"], [9, 9, 0, 5, 1, "CONDITIONING"], [10, 10, 0, 9, 0, "CONDITIONING"], [11, 11, 0, 9, 1, "CONDITIONING"], [15, 16, 0, 10, 0, "CONDITIONING"], [26, 23, 0, 6, 0, "MODEL"], [27, 23, 0, 8, 0, "MODEL"], [83, 19, 0, 2, 1, "VAE"], [84, 19, 0, 9, 2, "VAE"], [85, 19, 0, 2, 1, "VAE"], [86, 19, 0, 9, 2, "VAE"], [90, 19, 0, 2, 1, "VAE"], [91, 19, 0, 9, 2, "VAE"], [130, 72, 1, 62, 0, "*"], [131, 62, 0, 73, 0, "MASK"], [135, 56, 0, 9, 3, "IMAGE"], [168, 72, 0, 56, 0, "*"], [179, 89, 0, 23, 0, "*"], [183, 90, 0, 16, 0, "CLIP"], [184, 90, 0, 11, 0, "CLIP"], [189, 9, 2, 94, 0, "LATENT"], [190, 94, 0, 1, 4, "LATENT"], [193, 73, 0, 9, 4, "MASK"], [283, 19, 0, 9, 2, "VAE"], [284, 19, 0, 125, 1, "VAE"], [285, 19, 0, 2, 1, "VAE"], [286, 19, 0, 123, 2, "VAE"], [290, 146, 0, 89, 0, "*"], [291, 145, 0, 90, 0, "*"], [316, 144, 0, 125, 1, "VAE"], [317, 144, 0, 123, 2, "VAE"], [318, 144, 0, 9, 2, "VAE"], [319, 144, 0, 2, 1, "VAE"], [321, 144, 0, 2, 1, "VAE"], [323, 144, 0, 9, 2, "VAE"], [337, 165, 0, 164, 1, "STRING"], [338, 165, 1, 164, 2, "STRING"], [340, 72, 0, 166, 0, "*"], [341, 168, 0, 169, 1, "INT"], [342, 168, 1, 169, 2, "INT"], [343, 167, 0, 169, 0, "IMAGE"], [344, 2, 0, 167, 0, "IMAGE"], [345, 169, 0, 164, 0, "IMAGE"], [346, 168, 0, 167, 1, "INT"], [347, 166, 0, 168, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Group", "bounding": [-71, -50, 1456, 552], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [-1285, -51, 1200, 552], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [-1284, 526, 1199, 596], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-66.03231811523438, 527.8765869140625, 1261.9051513671875, 811.9479370117188], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.611590904484149, "offset": [1072.0479630715145, 298.7248198206287]}}, "version": 0.4, "seed_widgets": {"4": 0}}}}}
{"client_id": "8bcc34c39a4a4af3ab6b9717e444a56e", "prompt": {"152": {"inputs": {"image": "000 (1).jpg", "upload": "image"}, "class_type": "LoadImage"}, "177": {"inputs": {"text": "product/20241121/100018/106228", "text_b": "product_103231", "text_c": "", "text_d": ""}, "class_type": "Text String"}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate"}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text"}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text"}, "209": {"inputs": {"swap_model": "inswapper_128.onnx", "facedetection_model": "retinaface_resnet50", "face_restore_model": "GFPGANv1.4.pth", "parse_model": "parsenet"}, "class_type": "LoadConrainReactorModels"}, "210": {"inputs": {"enabled": true, "face_restore_visibility": "0.7", "codeformer_weight": "0.7", "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "keep_largest": "yes", "input_image": ["299", 0], "swap_model": ["209", 0], "facedetection": ["209", 1], "face_restore_model": ["209", 2], "faceparse_model": ["209", 3], "source_image": ["152", 0]}, "class_type": "ConrainReActorFaceSwap"}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave"}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate"}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text"}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg"}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1785", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["210", 0]}, "class_type": "CR Upscale Image"}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": "16777215"}, "class_type": "EmptyImage"}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB"}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number"}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy"}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator"}, "268": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode"}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode"}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader"}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader"}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader"}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced"}, "274": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect"}, "275": {"inputs": {"scheduler": "beta", "steps": 20, "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler"}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider"}, "277": {"inputs": {"noise_seed": 155799800851505}, "class_type": "RandomNoise"}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["268", 0]}, "class_type": "FluxGuidance"}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage"}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["296", 0]}, "class_type": "ModelSamplingFlux"}, "282": {"inputs": {"prompts": "(linrun2111:1.3), {front view|front view|front view|front view|side view}, The model is wearing A striped beige shirt and brown pants, The shirt features a brown collar and brown cuffs. ,buttoned.The model is wearing heels. The heels are made of leather. \n\n\n{A female model standing with arms crossed, looking confidently at the camera with a slight smile, legs shoulder-width apart.|A female model standing straight with hands in pockets, looking off to the side with a relaxed expression, body slightly turned.|A female model standing with one hand on his hip, the other hand hanging loosely by his side, gazing directly at the camera with a neutral expression.|A female model standing with one leg slightly forward, both hands clasped behind his back, looking down with a thoughtful expression.}, standing against a light gray background, with high contrast and sharp edges, clear facial features.,(full body:1.1),long shot,focus on body,\n\n\nchinese fashion model,real skin details,(Relieved,A gentle and comforting smile, often accompanied by a slight exhale, showing relief from stress or worry),", "seed": 1421}, "class_type": "ConrainRandomPrompts"}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss"}, "284": {"inputs": {"seed": 1785}, "class_type": "CR Seed"}, "285": {"inputs": {"seed": 1340}, "class_type": "CR Seed"}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts"}, "296": {"inputs": {"lora_name": "dev/几里与麻1536-1024_copy_100362_20241116_004814/几里与麻1536-1024_copy_100362_20241116_004814-flux/几里与麻1536-1024_copy_100362_20241116_004814-flux.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "297": {"inputs": {"lora_name": "", "strength_model": "1", "strength_clip": "1", "model": ["272", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "298": {"inputs": {"lora_name": "open_lora/FLUX-dev-lora-AntiBlur.safetensors", "strength_model": "1.5", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "299": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 658284675316747, "steps": 10, "cfg": "3", "sampler_name": "euler", "scheduler": "beta", "denoise": 0.4, "feather": 3, "noise_mask": true, "force_inpaint": false, "bbox_threshold": 0.5, "bbox_dilation": 2, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": 0, "noise_mask_feather": false, "image": ["328", 0], "detailer_pipe": ["300", 0]}, "class_type": "FaceDetailerPipe"}, "300": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["301", 0], "bbox_detector": ["303", 0]}, "class_type": "BasicPipeToDetailerPipe"}, "301": {"inputs": {"model": ["296", 0], "clip": ["296", 1], "vae": ["270", 0], "positive": ["302", 0], "negative": ["304", 0]}, "class_type": "ToBasicPipe"}, "302": {"inputs": {"text": ["331", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode"}, "303": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider"}, "304": {"inputs": {}, "class_type": "ImpactNegativeConditioningPlaceholder"}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked"}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave"}, "324": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider"}, "325": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "Prefer GPU"}, "class_type": "SAMLoader"}, "326": {"inputs": {"text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["329", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced"}, "327": {"inputs": {"text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["329", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced"}, "328": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 512, "seed": 960238209640492, "steps": 8, "cfg": "3", "sampler_name": "euler", "scheduler": "normal", "denoise": 0.4, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 500, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "wildcard": "", "cycle": 1, "inpaint_model": 1, "noise_mask_feather": 0, "image": ["269", 0], "model": ["329", 0], "clip": ["329", 1], "vae": ["329", 2], "positive": ["326", 0], "negative": ["327", 0], "bbox_detector": ["324", 0], "sam_model_opt": ["325", 0]}, "class_type": "FaceDetailer"}, "329": {"inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "331": {"inputs": {"prompts": "a asian 22 years-old fashion model,(Relieved,A gentle and comforting smile, often accompanied by a slight exhale, showing relief from stress or worry),", "seed": 1553}, "class_type": "ConrainRandomPrompts"}, "334": {"inputs": {"text": "", "clip": ["342", 1]}, "class_type": "CLIPTextEncode"}, "335": {"inputs": {"pixels": ["269", 0], "vae": ["341", 2]}, "class_type": "VAEEncode"}, "336": {"inputs": {"samples": ["335", 0], "mask": ["340", 0]}, "class_type": "SetLatentNoiseMask"}, "337": {"inputs": {"samples": ["338", 0], "vae": ["341", 2]}, "class_type": "VAEDecode"}, "338": {"inputs": {"seed": 229328310894190, "steps": 10, "cfg": 7.5, "sampler_name": "dpmpp_sde", "scheduler": "karras", "denoise": 0.2, "model": ["342", 0], "positive": ["343", 0], "negative": ["334", 0], "latent_image": ["336", 0]}, "class_type": "K<PERSON><PERSON><PERSON>"}, "340": {"inputs": {"face_mask": true, "background_mask": false, "hair_mask": true, "body_mask": true, "clothes_mask": false, "confidence": 0.4, "images": ["269", 0]}, "class_type": "APersonMaskGenerator"}, "341": {"inputs": {"ckpt_name": "epicrealism_naturalSinRC1VAE.safetensors"}, "class_type": "CheckpointLoaderSimple"}, "342": {"inputs": {"lora_name": "open_lora/more_details.safetensors", "strength_model": 0.5, "strength_clip": 1, "model": ["341", 0], "clip": ["341", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "343": {"inputs": {"text": ["331", 0], "clip": ["342", 1]}, "class_type": "CLIPTextEncode"}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor"}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor"}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings"}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger"}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings"}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger"}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop"}, "365": {"inputs": {"image": ["210", 0]}, "class_type": "Image Size to Number"}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax"}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 367, "last_link_id": 615, "nodes": [{"id": 177, "type": "Text String", "pos": {"0": 6357.84765625, "1": -745.0751953125}, "size": {"0": 315, "1": 190}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20241121/100018/106228", "product_103231", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 200, "type": "String to Text", "pos": {"0": 5984.080078125, "1": -532.56689453125}, "size": {"0": 315, "1": 58}, "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": {"0": 5962.080078125, "1": -726.566650390625}, "size": {"0": 315, "1": 58}, "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 209, "type": "LoadConrainReactorModels", "pos": {"0": 4621.01953125, "1": -1323.437744140625}, "size": {"0": 324.5391845703125, "1": 190}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [342], "slot_index": 0, "shape": 3, "label": "faceswapper_model"}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [343], "slot_index": 1, "shape": 3, "label": "facedetection_model"}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [344], "slot_index": 2, "shape": 3, "label": "facerestore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [345], "slot_index": 3, "shape": 3, "label": "faceparse_model"}], "title": "导入换脸模型", "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"], "color": "#494949", "bgcolor": "#353535"}, {"id": 232, "type": "Text Concatenate", "pos": {"0": 6839.0927734375, "1": -778.6949462890625}, "size": {"0": 250, "1": 142}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": {"0": 6350.84765625, "1": -874.0751953125}, "size": {"0": 315, "1": 58}, "flags": {"collapsed": false}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 235, "type": "InspyrenetRembg", "pos": {"0": 6358.943359375, "1": -1297.677978515625}, "size": {"0": 230, "1": 90}, "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 236, "type": "CR Upscale Image", "pos": {"0": 5864.943359375, "1": -1424.677978515625}, "size": {"0": 315, "1": 222}, "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 516, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 240, "type": "Note", "pos": {"0": 6788.943359375, "1": -1618.677978515625}, "size": {"0": 260, "1": 110}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": {"0": 6799.943359375, "1": -1219.677978515625}, "size": {"0": 252, "1": 26}, "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 268, "type": "CLIPTextEncode", "pos": {"0": -2378.759033203125, "1": -1245.************}, "size": {"0": 285.6000061035156, "1": 54}, "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 513}, {"name": "text", "type": "STRING", "link": 453, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [464], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 270, "type": "VAELoader", "pos": {"0": -1454, "1": -1276}, "size": {"0": 247.6494903564453, "1": 64.26640319824219}, "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 271, "type": "DualCLIPLoader", "pos": {"0": -3052.************, "1": -1443.************}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [512], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 272, "type": "UNETLoader", "pos": {"0": -3039.7587890625, "1": -1645.************}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [511], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 274, "type": "KSamplerSelect", "pos": {"0": -1742.8243408203125, "1": -1365.************}, "size": {"0": 210, "1": 58}, "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 275, "type": "BasicScheduler", "pos": {"0": -1720.7586669921875, "1": -1153.3388671875}, "size": {"0": 210, "1": 106}, "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", 20, 1]}, {"id": 276, "type": "BasicGuider", "pos": {"0": -1704.82421875, "1": -1485.************}, "size": {"0": 161.1999969482422, "1": 46}, "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 277, "type": "RandomNoise", "pos": {"0": -1809, "1": -1653}, "size": {"0": 317.5343933105469, "1": 84.33126831054688}, "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [155799800851505, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 278, "type": "FluxGuidance", "pos": {"0": -2036.************, "1": -1323.************}, "size": {"0": 211.60000610351562, "1": 58}, "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 464}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": {"0": -2032.************, "1": -1200.3387451171875}, "size": {"0": 210, "1": 86.50716400146484}, "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": {"0": -2021.************, "1": -1012.3389892578125}, "size": {"0": 210, "1": 122}, "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 514, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 282, "type": "ConrainRandomPrompts", "pos": {"0": -2922.759033203125, "1": -1198.3387451171875}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [472, 473], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), {front view|front view|front view|front view|side view}, The model is wearing A striped beige shirt and brown pants, The shirt features a brown collar and brown cuffs. ,buttoned.The model is wearing heels. The heels are made of leather. \n\n\n{A female model standing with arms crossed, looking confidently at the camera with a slight smile, legs shoulder-width apart.|A female model standing straight with hands in pockets, looking off to the side with a relaxed expression, body slightly turned.|A female model standing with one hand on his hip, the other hand hanging loosely by his side, gazing directly at the camera with a neutral expression.|A female model standing with one leg slightly forward, both hands clasped behind his back, looking down with a thoughtful expression.}, standing against a light gray background, with high contrast and sharp edges, clear facial features.,(full body:1.1),long shot,focus on body,\n\n\nchinese fashion model,real skin details,(Relieved,A gentle and comforting smile, often accompanied by a slight exhale, showing relief from stress or worry),", 1421, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 283, "type": "ShowText|pysssss", "pos": {"0": -2374.759033203125, "1": -1072.338623046875}, "size": {"0": 256.63372802734375, "1": 226}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 472, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [453], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), side view, The model is wearing A striped beige shirt and brown pants, The shirt features a brown collar and brown cuffs. ,buttoned.The model is wearing heels. The heels are made of leather. \n\n\nA female model standing with arms crossed, looking confidently at the camera with a slight smile, legs shoulder-width apart., standing against a light gray background, with high contrast and sharp edges, clear facial features.,(full body:1.1),long shot,focus on body,\n\n\nchinese fashion model,real skin details,(Relieved,A gentle and comforting smile, often accompanied by a slight exhale, showing relief from stress or worry),"]}, {"id": 284, "type": "CR Seed", "pos": {"0": -2377.759033203125, "1": -631.3389892578125}, "size": {"0": 243.4204864501953, "1": 102}, "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 285, "type": "CR Seed", "pos": {"0": -2391.759033203125, "1": -817.3389282226562}, "size": {"0": 243.4204864501953, "1": 102}, "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1340, "fixed"]}, {"id": 286, "type": "ConrainRandomPrompts", "pos": {"0": -2906.1630859375, "1": -876.3988647460938}, "size": {"0": 411.6590881347656, "1": 124}, "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 287, "type": "Reroute", "pos": {"0": -1187.8585205078125, "1": -966.1553955078125}, "size": [75, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 473, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": {"0": -1222.858642578125, "1": -835.1553344726562}, "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 299, "type": "FaceDetailerPipe", "pos": {"0": 3954.708984375, "1": -1634.05810546875}, "size": {"0": 346, "1": 782}, "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 557}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 503}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [517], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, 658284675316747, "randomize", 10, "3", "euler", "beta", 0.4, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 300, "type": "BasicPipeToDetailerPipe", "pos": {"0": 3630.27734375, "1": -1581.**********}, "size": {"0": 262, "1": 204.4281768798828}, "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 504}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 505}, {"name": "sam_model_opt", "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR"}, {"name": "detailer_hook", "type": "DETAILER_HOOK"}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 301, "type": "ToBasicPipe", "pos": {"0": 3336.27734375, "1": -1575.**********}, "size": {"0": 241.79998779296875, "1": 106}, "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506}, {"name": "clip", "type": "CLIP", "link": 507}, {"name": "vae", "type": "VAE", "link": 520}, {"name": "positive", "type": "CONDITIONING", "link": 508}, {"name": "negative", "type": "CONDITIONING", "link": 509}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [504], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 302, "type": "CLIPTextEncode", "pos": {"0": 3063.27734375, "1": -1580.**********}, "size": {"0": 210, "1": 116.85224914550781}, "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 510}, {"name": "text", "type": "STRING", "link": 554, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a asian 22 years-old fashion model,"], "color": "#232", "bgcolor": "#353"}, {"id": 303, "type": "UltralyticsDetectorProvider", "pos": {"0": 3033.27734375, "1": -1149.406005859375}, "size": {"0": 226.8000030517578, "1": 78}, "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [505], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 304, "type": "ImpactNegativeConditioningPlaceholder", "pos": {"0": 3051.27734375, "1": -1303.**********}, "size": {"0": 210, "1": 26}, "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [509], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 306, "type": "Note", "pos": {"0": -2669.759033203125, "1": -1746.************}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 307, "type": "Note", "pos": {"0": -2348.759033203125, "1": -1758.************}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["去模糊lora节点\n纯色背景流程：关闭\n其他流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 310, "type": "Note", "pos": {"0": 5897.943359375, "1": -1608.677978515625}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 319, "type": "Note", "pos": {"0": 4660.6328125, "1": -1077.2584228515625}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 320, "type": "ConrainTextSave", "pos": {"0": 8055.84765625, "1": -527.0752563476562}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 324, "type": "UltralyticsDetectorProvider", "pos": {"0": 3060.822265625, "1": 98.61608123779297}, "size": {"0": 315, "1": 78}, "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [552], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 325, "type": "SAMLoader", "pos": {"0": 3093.822265625, "1": 252.61593627929688}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [553], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 326, "type": "BNK_CLIPTextEncodeAdvanced", "pos": {"0": 3038.822265625, "1": -427.3840637207031}, "size": {"0": 389.95330810546875, "1": 157.71157836914062}, "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 545, "label": "clip"}, {"name": "text", "type": "STRING", "link": 555, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [550], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 327, "type": "BNK_CLIPTextEncodeAdvanced", "pos": {"0": 3060.822265625, "1": -177.3840789794922}, "size": {"0": 400, "1": 200}, "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 546, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [551], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 329, "type": "CheckpointLoaderSimple", "pos": {"0": 3032.625, "1": -614.596923828125}, "size": {"0": 315, "1": 98}, "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [547], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [545, 546, 548], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [549], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 333, "type": "Note", "pos": {"0": 3524, "1": 40}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 334, "type": "CLIPTextEncode", "pos": {"0": 512.3257446289062, "1": -1272.296142578125}, "size": {"0": 301.23193359375, "1": 108.12081909179688}, "flags": {"collapsed": true}, "order": 45, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 558, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [567], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 335, "type": "VAEEncode", "pos": {"0": 156.32608032226562, "1": -817.29638671875}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 559, "label": "pixels"}, {"name": "vae", "type": "VAE", "link": 560, "label": "vae"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [561], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "VAEEncode"}, "widgets_values": []}, {"id": 336, "type": "SetLatentNoiseMask", "pos": {"0": 508.32574462890625, "1": -863.2962646484375}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 561, "label": "samples"}, {"name": "mask", "type": "MASK", "link": 562, "label": "mask"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [568], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "SetLatentNoiseMask"}, "widgets_values": []}, {"id": 337, "type": "VAEDecode", "pos": {"0": 1903.6512451171875, "1": -1472.183837890625}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 563, "label": "samples"}, {"name": "vae", "type": "VAE", "link": 564, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 338, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 1481.3271484375, "1": -1345.296142578125}, "size": {"0": 312.9380798339844, "1": 269.4892883300781}, "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 565, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 566, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 567, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 568, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [563], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [229328310894190, "randomize", 10, 7.5, "dpmpp_sde", "karras", 0.2]}, {"id": 339, "type": "Reroute", "pos": {"0": -238.6738739013672, "1": -1097.2965087890625}, "size": [75, 26], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 601, "label": ""}], "outputs": [{"name": "", "type": "IMAGE", "links": [559, 569]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 340, "type": "APersonMaskGenerator", "pos": {"0": 139.32608032226562, "1": -1166.2962646484375}, "size": {"0": 315, "1": 178}, "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 569}], "outputs": [{"name": "masks", "type": "MASK", "links": [562], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [true, false, true, true, false, 0.4]}, {"id": 341, "type": "CheckpointLoaderSimple", "pos": {"0": -414.6739807128906, "1": -1475.2960205078125}, "size": {"0": 310.720947265625, "1": 98}, "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [570], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [571], "slot_index": 1, "shape": 3}, {"name": "VAE", "type": "VAE", "links": [560, 564], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["epicrealism_naturalSinRC1VAE.safetensors"]}, {"id": 342, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": 175.32611083984375, "1": -1499.296142578125}, "size": {"0": 315, "1": 126}, "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 570}, {"name": "clip", "type": "CLIP", "link": 571}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [565], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [558, 572], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["open_lora/more_details.safetensors", 0.5, 1]}, {"id": 343, "type": "CLIPTextEncode", "pos": {"0": 1060.1024169921875, "1": -978.25634765625}, "size": {"0": 210, "1": 116.85224914550781}, "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 572}, {"name": "text", "type": "STRING", "link": 575, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [566], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["happy expression, a 20 years-old Chinese fashion girl, Very fair skin, the model has Radiant Smile"], "color": "#232", "bgcolor": "#353"}, {"id": 349, "type": "ConrainPythonExecutor", "pos": {"0": -2045, "1": -741}, "size": {"0": 368.1804504394531, "1": 203.2705841064453}, "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591}, {"name": "any_b", "type": "*"}, {"name": "any_c", "type": "*"}, {"name": "any_d", "type": "*"}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": {"0": -2022, "1": -640}, "size": {"0": 368.1804504394531, "1": 203.2705841064453}, "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587}, {"name": "any_b", "type": "*"}, {"name": "any_c", "type": "*"}, {"name": "any_d", "type": "*"}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": {"0": -1813, "1": -763}, "size": {"0": 184.8000030517578, "1": 27.56488609313965}, "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": {"0": -1563, "1": -767}, "size": {"0": 210, "1": 34}, "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": {"0": -1742, "1": -633}, "size": {"0": 184.8000030517578, "1": 39.813907623291016}, "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": {"0": -1509, "1": -612}, "size": {"0": 210, "1": 34}, "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 216, "type": "ConrainImageSave", "pos": {"0": 8041.84765625, "1": -905.0751953125}, "size": {"0": 320, "1": 266}, "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 603, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 185, "type": "Text Concatenate", "pos": {"0": 7484.84765625, "1": -599.0751953125}, "size": {"0": 315, "1": 178}, "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 308, "type": "Note", "pos": {"0": 4653.01953125, "1": -1504.437744140625}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 152, "type": "LoadImage", "pos": {"0": 4633.01953125, "1": -873.4379272460938}, "size": {"0": 320, "1": 314}, "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [346], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "shape": 3, "label": "MASK"}], "title": "导入人脸图片", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["000 (1).jpg", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 355, "type": "ImageCrop", "pos": {"0": 7526.84765625, "1": -902.0751953125}, "size": {"0": 225.3616943359375, "1": 122.95598602294922}, "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [603], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": {"0": -1408, "1": -1634}, "size": {"0": 236.8000030517578, "1": 112.51068878173828}, "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 269, "type": "VAEDecode", "pos": {"0": -1097, "1": -1511}, "size": {"0": 210, "1": 46}, "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [601, 611], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 331, "type": "ConrainRandomPrompts", "pos": {"0": -1196, "1": -1151}, "size": {"0": 319.1407165527344, "1": 134.37188720703125}, "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [554, 555, 575], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a asian 22 years-old fashion model,(Relieved,A gentle and comforting smile, often accompanied by a slight exhale, showing relief from stress or worry),", 1553, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 309, "type": "Note", "pos": {"0": 5220.01953125, "1": -1548.437744140625}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 210, "type": "ConrainReActorFaceSwap", "pos": {"0": 5138.01953125, "1": -1360.437744140625}, "size": {"0": 367.79998779296875, "1": 370}, "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 517, "label": "input_image"}, {"name": "swap_model", "type": "FACE_MODEL", "link": 342, "label": "swap_model"}, {"name": "facedetection", "type": "FACE_MODEL", "link": 343, "label": "facedetection"}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 344, "label": "face_restore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 345, "label": "faceparse_model"}, {"name": "source_image", "type": "IMAGE", "link": 346, "label": "source_image"}, {"name": "face_model", "type": "FACE_MODEL", "label": "face_model"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [516, 612], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "shape": 3, "label": "FACE_MODEL"}], "title": "换脸", "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, "0.7", "0.7", "no", "no", "0", "0", 1, "yes"], "color": "#494949", "bgcolor": "#353535"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": {"0": 7231.943359375, "1": -1343.677978515625}, "size": {"0": 252, "1": 146}, "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 365, "type": "Image Size to Number", "pos": {"0": 7246.943359375, "1": -1582.677978515625}, "size": {"0": 229.20001220703125, "1": 126}, "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 612, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": {"0": 7540.943359375, "1": -1507.677978515625}, "size": {"0": 210, "1": 67.1211166381836}, "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": {"0": 7809.943359375, "1": -1430.677978515625}, "size": {"0": 220, "1": 118}, "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 263, "type": "ImageScaleBy", "pos": {"0": 8123.943359375, "1": -1541.677978515625}, "size": {"0": 228.9691162109375, "1": 78}, "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}, {"id": 328, "type": "FaceDetailer", "pos": {"0": 3796, "1": -597}, "size": {"0": 350.5302734375, "1": 902.3991088867188}, "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 611, "label": "image"}, {"name": "model", "type": "MODEL", "link": 547, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 548, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 549, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 550, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 551, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 552, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 553, "slot_index": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "slot_index": 8, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [557], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, 960238209640492, "randomize", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 248, "type": "EmptyImage", "pos": {"0": 6813.943359375, "1": -1436.677978515625}, "size": {"0": 231.5089111328125, "1": 120.12616729736328}, "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, "16777215"], "color": "#474747", "bgcolor": "#333333"}, {"id": 261, "type": "Image Size to Number", "pos": {"0": 6364.943359375, "1": -1522.677978515625}, "size": {"0": 229.20001220703125, "1": 126}, "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 332, "type": "Note", "pos": {"0": 3481, "1": -1179}, "size": {"0": 210, "1": 91.33761596679688}, "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"], "color": "#322", "bgcolor": "#533"}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -2374, "1": -1625}, "size": {"0": 273.12445068359375, "1": 126}, "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 501}, {"name": "clip", "type": "CLIP", "link": 502}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "去模糊lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["open_lora/FLUX-dev-lora-AntiBlur.safetensors", "1.5", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -2386, "1": -1450}, "size": {"0": 279.70758056640625, "1": 126}, "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506, 514], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [507, 510, 513], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["dev/几里与麻1536-1024_copy_100362_20241116_004814/几里与麻1536-1024_copy_100362_20241116_004814-flux/几里与麻1536-1024_copy_100362_20241116_004814-flux.safetensors", 1, 1], "color": "#232", "bgcolor": "#353"}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": {"0": -2676, "1": -1580}, "size": {"0": 228.4159698486328, "1": 126}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 511}, {"name": "clip", "type": "CLIP", "link": 512}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [501], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [502], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["", "1", "1"], "color": "#232", "bgcolor": "#353"}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [342, 209, 0, 210, 1, "FACE_MODEL"], [343, 209, 1, 210, 2, "FACE_MODEL"], [344, 209, 2, 210, 3, "FACE_MODEL"], [345, 209, 3, 210, 4, "FACE_MODEL"], [346, 152, 0, 210, 5, "IMAGE"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [501, 297, 0, 298, 0, "MODEL"], [502, 297, 1, 298, 1, "CLIP"], [503, 300, 0, 299, 1, "DETAILER_PIPE"], [504, 301, 0, 300, 0, "BASIC_PIPE"], [505, 303, 0, 300, 1, "BBOX_DETECTOR"], [506, 296, 0, 301, 0, "MODEL"], [507, 296, 1, 301, 1, "CLIP"], [508, 302, 0, 301, 3, "CONDITIONING"], [509, 304, 0, 301, 4, "CONDITIONING"], [510, 296, 1, 302, 0, "CLIP"], [511, 272, 0, 297, 0, "MODEL"], [512, 271, 0, 297, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [514, 296, 0, 280, 0, "MODEL"], [516, 210, 0, 236, 0, "IMAGE"], [517, 299, 0, 210, 0, "IMAGE"], [520, 270, 0, 301, 2, "VAE"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [545, 329, 1, 326, 0, "CLIP"], [546, 329, 1, 327, 0, "CLIP"], [547, 329, 0, 328, 1, "MODEL"], [548, 329, 1, 328, 2, "CLIP"], [549, 329, 2, 328, 3, "VAE"], [550, 326, 0, 328, 4, "CONDITIONING"], [551, 327, 0, 328, 5, "CONDITIONING"], [552, 324, 0, 328, 6, "BBOX_DETECTOR"], [553, 325, 0, 328, 7, "SAM_MODEL"], [554, 331, 0, 302, 1, "STRING"], [555, 331, 0, 326, 1, "STRING"], [557, 328, 0, 299, 0, "IMAGE"], [558, 342, 1, 334, 0, "CLIP"], [559, 339, 0, 335, 0, "IMAGE"], [560, 341, 2, 335, 1, "VAE"], [561, 335, 0, 336, 0, "LATENT"], [562, 340, 0, 336, 1, "MASK"], [563, 338, 0, 337, 0, "LATENT"], [564, 341, 2, 337, 1, "VAE"], [565, 342, 0, 338, 0, "MODEL"], [566, 343, 0, 338, 1, "CONDITIONING"], [567, 334, 0, 338, 2, "CONDITIONING"], [568, 336, 0, 338, 3, "LATENT"], [569, 339, 0, 340, 0, "IMAGE"], [570, 341, 0, 342, 0, "MODEL"], [571, 341, 1, 342, 1, "CLIP"], [572, 342, 1, 343, 0, "CLIP"], [575, 331, 0, 343, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [601, 269, 0, 339, 0, "*"], [602, 263, 0, 355, 0, "IMAGE"], [603, 355, 0, 216, 0, "IMAGE"], [611, 269, 0, 328, 0, "IMAGE"], [612, 210, 0, 365, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"]], "groups": [{"title": "换脸", "bounding": [4570, -1716, 1069, 1391], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "1.5修脸", "bounding": [2992, -734, 1347, 1200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "换背景", "bounding": [5767, -1714, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "修脸换脸", "bounding": [2984, -1753, 1350, 956], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "出图", "bounding": [-3083, -1817, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "保存图片", "bounding": [5767, -1008, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "表情控制", "bounding": [-449, -1713, 2615, 1044], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.17715610000000037, "offset": [2116.8747524784435, 2539.9338802130064]}}, "version": 0.4, "widget_idx_map": {"274": {"sampler_name": 0}, "275": {"scheduler": 0}, "277": {"noise_seed": 0}, "282": {"seed": 1}, "284": {"seed": 0}, "285": {"seed": 0}, "286": {"seed": 1}, "299": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "328": {"seed": 3, "sampler_name": 7, "scheduler": 8}, "331": {"seed": 1}, "338": {"seed": 0, "sampler_name": 4, "scheduler": 5}}, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "299": 3, "328": 3, "331": 1, "338": 0}}}}}
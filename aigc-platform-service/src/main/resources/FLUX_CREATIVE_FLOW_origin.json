{"client_id": "${clientId}", "prompt": {"152": {"class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo.faceImage}", "upload": "image"}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "177": {"class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "209": {"class_type": "LoadConrainReactorModels", "inputs": {"face_restore_model": "GFPGANv1.4.pth", "facedetection_model": "retinaface_resnet50", "parse_model": "parsenet", "swap_model": "inswapper_128.onnx"}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "210": {"class_type": "ConrainReActorFaceSwap", "inputs": {"codeformer_weight": "0.7", "console_log_level": 1, "detect_gender_input": "no", "detect_gender_source": "no", "enabled": true, "face_restore_model": ["209", 2], "face_restore_visibility": "${FACE.extInfo.faceRestoreVisibility}", "facedetection": ["209", 1], "faceparse_model": ["209", 3], "input_faces_index": "0", "input_image": ["${isFluxRepair?then(299,328)}", 0], "keep_largest": "yes", "source_faces_index": "0", "source_image": ["152", 0], "swap_model": ["209", 0]}, "disable": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then('true','false')}"}, "216": {"class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"class_type": "CR Upscale Image", "inputs": {"image": ["${(isLoraFace&&!isLoraSwapFace)?then(isFluxRepair?then(299,328),isBackView?then(isFluxRepair?then(299,328),210))}", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": "${height}"}, "disable": "${isPureBg?then('false','true')}"}, "268": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "269": {"class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}}, "272": {"class_type": "UNETLoader", "inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}}, "273": {"class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"class_type": "KSamplerSelect", "inputs": {"sampler_name": "euler"}}, "275": {"class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "beta", "steps": 20}}, "276": {"class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"class_type": "FluxGuidance", "inputs": {"conditioning": ["268", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["296", 0], "width": ["352", 0]}}, "282": {"class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"class_type": "ShowText|pysssss", "inputs": {"text": ["282", 0]}}, "284": {"class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${(isLoraFace||!(isPureBg||!isAntiBlueLora))?then(1,0)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,272),298)}", 0], "strength_clip": 1, "strength_model": 1}}, "297": {"class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["272", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,272)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "299": {"class_type": "FaceDetailerPipe", "inputs": {"bbox_crop_factor": 3, "bbox_dilation": 2, "bbox_threshold": 0.5, "cfg": "${faceCfg}", "cycle": 1, "denoise": 0.4, "detailer_pipe": ["300", 0], "drop_size": 40, "feather": 3, "force_inpaint": false, "guide_size": 512, "guide_size_for": true, "image": ["${(true||isLoraFace&&!isLoraSwapFace)?then(269,337)}", 0], "inpaint_model": 0, "max_size": 1024, "noise_mask": true, "noise_mask_feather": false, "refiner_ratio": 0.2, "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "beta", "seed": "${faceSeed}", "steps": 10}, "disable": "${isFluxRepair?then('false','true')}"}, "300": {"class_type": "BasicPipeToDetailerPipe", "inputs": {"Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["301", 0], "bbox_detector": ["303", 0], "wildcard": ""}, "disable": "${isFluxRepair?then('false','true')}"}, "301": {"class_type": "ToBasicPipe", "inputs": {"clip": ["296", 1], "model": ["296", 0], "negative": ["304", 0], "positive": ["302", 0], "vae": ["270", 0]}, "disable": "${isFluxRepair?then('false','true')}"}, "302": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["331", 0]}, "disable": "${isFluxRepair?then('false','true')}"}, "303": {"class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${isFluxRepair?then('false','true')}"}, "304": {"class_type": "ImpactNegativeConditioningPlaceholder", "inputs": {}, "disable": "${isFluxRepair?then('false','true')}"}, "316": {"class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "324": {"class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}, "disable": "${isFluxRepair?then('true','false')}"}, "325": {"class_type": "SAMLoader", "inputs": {"device_mode": "Prefer GPU", "model_name": "sam_vit_b_01ec64.pth"}, "disable": "${isFluxRepair?then('true','false')}"}, "326": {"class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["329", 1], "text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${isFluxRepair?then('true','false')}"}, "327": {"class_type": "BNK_CLIPTextEncodeAdvanced", "inputs": {"clip": ["329", 1], "text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111"}, "disable": "${isFluxRepair?then('true','false')}"}, "328": {"class_type": "FaceDetailer", "inputs": {"bbox_crop_factor": 3, "bbox_detector": ["324", 0], "bbox_dilation": 500, "bbox_threshold": 0.5, "cfg": "${faceCfg}", "clip": ["329", 1], "cycle": 1, "denoise": 0.4, "drop_size": 40, "feather": 5, "force_inpaint": true, "guide_size": 384, "guide_size_for": true, "image": ["${(true||isLoraFace&&!isLoraSwapFace)?then(269,337)}", 0], "inpaint_model": 1, "max_size": 512, "model": ["329", 0], "negative": ["327", 0], "noise_mask": true, "noise_mask_feather": 0, "positive": ["326", 0], "sam_bbox_expansion": 0, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "sam_model_opt": ["325", 0], "sam_threshold": 0.93, "sampler_name": "euler", "scheduler": "normal", "seed": "${faceSeed}", "steps": 8, "vae": ["329", 2], "wildcard": ""}, "disable": "${isFluxRepair?then('true','false')}"}, "329": {"class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "disable": "${isFluxRepair?then('true','false')}"}, "331": {"class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.extInfo.expression}", "seed": 1620}}, "334": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["342", 1], "text": ""}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "335": {"class_type": "VAEEncode", "inputs": {"pixels": ["269", 0], "vae": ["341", 2]}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "336": {"class_type": "SetLatentNoiseMask", "inputs": {"mask": ["340", 0], "samples": ["335", 0]}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "337": {"class_type": "VAEDecode", "inputs": {"samples": ["338", 0], "vae": ["341", 2]}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "338": {"class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 7.5, "denoise": 0.2, "latent_image": ["336", 0], "model": ["342", 0], "negative": ["334", 0], "positive": ["343", 0], "sampler_name": "dpmpp_sde", "scheduler": "karras", "seed": "${seed}", "steps": 10}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "340": {"class_type": "APersonMaskGenerator", "inputs": {"background_mask": false, "body_mask": true, "clothes_mask": false, "confidence": 0.4, "face_mask": true, "hair_mask": true, "images": ["269", 0]}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "341": {"class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "epicrealism_naturalSinRC1VAE.safetensors"}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "342": {"class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["341", 1], "lora_name": "open_lora/more_details.safetensors", "model": ["341", 0], "strength_clip": 1, "strength_model": 0.5}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "343": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["342", 1], "text": ["331", 0]}, "disable": "${(true||isLoraFace&&!isLoraSwapFace)?then('true','false')}"}, "349": {"class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,(isLoraFace&&!isLoraSwapFace)?then(isFluxRepair?then(299,328),isBackView?then(isFluxRepair?then(299,328),210)))}", 0], "width": ["285", 0], "x": 0, "y": 0}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [5248.282070098242, 2514.2120461928644], "scale": 0.1610510000000002}}, "groups": [{"bounding": [4630, -1630, 1069, 1391], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"bounding": [2992, -734, 1347, 1200], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "1.5修脸"}, {"bounding": [5727, -1629, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"bounding": [2988, -1734, 1350, 956], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "修脸换脸"}, {"bounding": [-3083, -1817, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"bounding": [5752, -937, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"bounding": [-449, -1713, 2615, 1044], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "表情控制"}], "last_link_id": 604, "last_node_id": 358, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [342, 209, 0, 210, 1, "FACE_MODEL"], [343, 209, 1, 210, 2, "FACE_MODEL"], [344, 209, 2, 210, 3, "FACE_MODEL"], [345, 209, 3, 210, 4, "FACE_MODEL"], [346, 152, 0, 210, 5, "IMAGE"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [501, 297, 0, 298, 0, "MODEL"], [502, 297, 1, 298, 1, "CLIP"], [503, 300, 0, 299, 1, "DETAILER_PIPE"], [504, 301, 0, 300, 0, "BASIC_PIPE"], [505, 303, 0, 300, 1, "BBOX_DETECTOR"], [506, 296, 0, 301, 0, "MODEL"], [507, 296, 1, 301, 1, "CLIP"], [508, 302, 0, 301, 3, "CONDITIONING"], [509, 304, 0, 301, 4, "CONDITIONING"], [510, 296, 1, 302, 0, "CLIP"], [511, 272, 0, 297, 0, "MODEL"], [512, 271, 0, 297, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [514, 296, 0, 280, 0, "MODEL"], [516, 210, 0, 236, 0, "IMAGE"], [517, 299, 0, 210, 0, "IMAGE"], [520, 270, 0, 301, 2, "VAE"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [545, 329, 1, 326, 0, "CLIP"], [546, 329, 1, 327, 0, "CLIP"], [547, 329, 0, 328, 1, "MODEL"], [548, 329, 1, 328, 2, "CLIP"], [549, 329, 2, 328, 3, "VAE"], [550, 326, 0, 328, 4, "CONDITIONING"], [551, 327, 0, 328, 5, "CONDITIONING"], [552, 324, 0, 328, 6, "BBOX_DETECTOR"], [553, 325, 0, 328, 7, "SAM_MODEL"], [554, 331, 0, 302, 1, "STRING"], [555, 331, 0, 326, 1, "STRING"], [557, 328, 0, 299, 0, "IMAGE"], [558, 342, 1, 334, 0, "CLIP"], [559, 339, 0, 335, 0, "IMAGE"], [560, 341, 2, 335, 1, "VAE"], [561, 335, 0, 336, 0, "LATENT"], [562, 340, 0, 336, 1, "MASK"], [563, 338, 0, 337, 0, "LATENT"], [564, 341, 2, 337, 1, "VAE"], [565, 342, 0, 338, 0, "MODEL"], [566, 343, 0, 338, 1, "CONDITIONING"], [567, 334, 0, 338, 2, "CONDITIONING"], [568, 336, 0, 338, 3, "LATENT"], [569, 339, 0, 340, 0, "IMAGE"], [570, 341, 0, 342, 0, "MODEL"], [571, 341, 1, 342, 1, "CLIP"], [572, 342, 1, 343, 0, "CLIP"], [574, 337, 0, 328, 0, "IMAGE"], [575, 331, 0, 343, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [601, 269, 0, 339, 0, "*"], [602, 263, 0, 355, 0, "IMAGE"], [603, 355, 0, 216, 0, "IMAGE"]], "nodes": [{"id": 152, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 0, "outputs": [{"label": "IMAGE", "links": [346], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "name": "MASK", "shape": 3, "type": "MASK"}], "pos": {"0": 4692, "1": -831}, "properties": {"Node name for S&R": "LoadImage"}, "size": {"0": 320, "1": 314}, "title": "导入人脸图片", "type": "LoadImage", "widgets_values": ["${FACE.extInfo.faceImage}", "image"]}, {"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": {"0": 6343, "1": -674}, "properties": {"Node name for S&R": "Text String"}, "size": {"0": 315, "1": 190}, "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 44, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": 7463, "1": -449}, "properties": {"Node name for S&R": "Text Concatenate"}, "size": {"0": 315, "1": 178}, "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": 5969.232421875, "1": -461.4916076660156}, "properties": {"Node name for S&R": "String to Text"}, "size": {"0": 315, "1": 58}, "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": 5947.232421875, "1": -655.491455078125}, "properties": {"Node name for S&R": "String to Text"}, "size": {"0": 315, "1": 58}, "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 209, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 4, "outputs": [{"label": "faceswapper_model", "links": [342], "name": "faceswapper_model", "shape": 3, "slot_index": 0, "type": "FACE_MODEL"}, {"label": "facedetection_model", "links": [343], "name": "facedetection_model", "shape": 3, "slot_index": 1, "type": "FACE_MODEL"}, {"label": "facerestore_model", "links": [344], "name": "facerestore_model", "shape": 3, "slot_index": 2, "type": "FACE_MODEL"}, {"label": "faceparse_model", "links": [345], "name": "faceparse_model", "shape": 3, "slot_index": 3, "type": "FACE_MODEL"}], "pos": {"0": 4681, "1": -1237}, "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "size": {"0": 324.5391845703125, "1": 190}, "title": "导入换脸模型", "type": "LoadConrainReactorModels", "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"]}, {"id": 210, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "input_image", "link": 517, "name": "input_image", "type": "IMAGE"}, {"label": "swap_model", "link": 342, "name": "swap_model", "type": "FACE_MODEL"}, {"label": "facedetection", "link": 343, "name": "facedetection", "type": "FACE_MODEL"}, {"label": "face_restore_model", "link": 344, "name": "face_restore_model", "type": "FACE_MODEL"}, {"label": "faceparse_model", "link": 345, "name": "faceparse_model", "type": "FACE_MODEL"}, {"label": "source_image", "link": 346, "name": "source_image", "type": "IMAGE"}, {"label": "face_model", "name": "face_model", "type": "FACE_MODEL"}], "mode": "${((isLoraFace&&!isLoraSwapFace)||isBackView)?then(4,0)}", "order": 70, "outputs": [{"label": "IMAGE", "links": [516], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "FACE_MODEL", "name": "FACE_MODEL", "shape": 3, "type": "FACE_MODEL"}], "pos": {"0": 5190, "1": -1219}, "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "size": {"0": 367.79998779296875, "1": 370}, "title": "换脸", "type": "ConrainReActorFaceSwap", "widgets_values": [true, "${FACE.extInfo.faceRestoreVisibility}", "0.7", "no", "no", "0", "0", 1, "yes"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 603, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 74, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": {"0": 8027, "1": -799}, "properties": {"Node name for S&R": "ConrainImageSave"}, "size": {"0": 320, "1": 266}, "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 31, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": 6824.2451171875, "1": -707.6197509765625}, "properties": {"Node name for S&R": "Text Concatenate"}, "size": {"0": 250, "1": 142}, "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": 6336, "1": -803}, "properties": {"Node name for S&R": "String to Text"}, "size": {"0": 315, "1": 58}, "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 72, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": {"0": 6319, "1": -1213}, "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": {"0": 230, "1": 90}, "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 516, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 71, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": {"0": 5825, "1": -1340}, "properties": {"Node name for S&R": "CR Upscale Image"}, "size": {"0": 315, "1": 222}, "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [], "pos": {"0": 6749, "1": -1534}, "properties": {"text": ""}, "size": {"0": 260, "1": 110}, "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 75, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 6768, "1": -1368}, "properties": {"Node name for S&R": "EmptyImage"}, "size": {"0": 231.5089111328125, "1": 120.12616729736328}, "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 73, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 6760, "1": -1135}, "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": {"0": 252, "1": 26}, "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 76, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": {"0": 6321, "1": -1470}, "properties": {"Node name for S&R": "Image Size to Number"}, "size": {"0": 229.20001220703125, "1": 126}, "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 77, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 7934, "1": -1188}, "properties": {"Node name for S&R": "ImageScaleBy"}, "size": {"0": 320, "1": 80}, "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 78, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": {"0": 7939, "1": -1450}, "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": {"0": 315, "1": 118}, "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 268, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 513, "name": "clip", "type": "CLIP"}, {"link": 453, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 52, "outputs": [{"links": [464], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": -2378.759033203125, "1": -1245.************}, "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 285.6000061035156, "1": 54}, "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 61, "outputs": [{"links": [601], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": {"0": -1099, "1": -1353}, "properties": {"Node name for S&R": "VAEDecode"}, "size": {"0": 210, "1": 46}, "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [455, 520], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": {"0": -1454, "1": -1276}, "properties": {"Node name for S&R": "VAELoader"}, "size": {"0": 247.6494903564453, "1": 64.26640319824219}, "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [512], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": {"0": -3052.************, "1": -1443.************}, "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": {"0": 315, "1": 106}, "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [511], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": {"0": -3039.7587890625, "1": -1645.************}, "properties": {"Node name for S&R": "UNETLoader"}, "size": {"0": 315, "1": 82}, "type": "UNETLoader", "widgets_values": ["flux1-dev.safetensors", "default"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 60, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": {"0": -1414, "1": -1496}, "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": {"0": 236.8000030517578, "1": 112.51068878173828}, "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 9, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": {"0": -1742.8243408203125, "1": -1365.************}, "properties": {"Node name for S&R": "KSamplerSelect"}, "size": {"0": 210, "1": 58}, "type": "KSamplerSelect", "widgets_values": ["euler"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 57, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": {"0": -1720.7586669921875, "1": -1153.3388671875}, "properties": {"Node name for S&R": "BasicScheduler"}, "size": {"0": 210, "1": 106}, "type": "BasicScheduler", "widgets_values": ["beta", 20, 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 59, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": {"0": -1704.82421875, "1": -1485.************}, "properties": {"Node name for S&R": "BasicGuider"}, "size": {"0": 161.1999969482422, "1": 46}, "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": {"0": -1809, "1": -1653}, "properties": {"Node name for S&R": "RandomNoise"}, "size": {"0": 317.5343933105469, "1": 84.33126831054688}, "type": "RandomNoise", "widgets_values": ["${seed}", "randomize"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 464, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 56, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": -2036.************, "1": -1323.************}, "properties": {"Node name for S&R": "FluxGuidance"}, "size": {"0": 211.60000610351562, "1": 58}, "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 54, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": {"0": -2032.************, "1": -1200.3387451171875}, "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": {"0": 210, "1": 86.50716400146484}, "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 514, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 53, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": {"0": -2021.************, "1": -1012.3389892578125}, "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": {"0": 210, "1": 122}, "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [472, 473], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": -2922.759033203125, "1": -1198.3387451171875}, "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": {"0": 400, "1": 200}, "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "randomize"]}, {"id": 283, "flags": {}, "inputs": [{"link": 472, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 33, "outputs": [{"links": [453], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": {"0": -2374.759033203125, "1": -1072.338623046875}, "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": {"0": 256.63372802734375, "1": 226}, "type": "ShowText|pysssss", "widgets_values": [""]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": {"0": -2377.759033203125, "1": -631.3389892578125}, "properties": {"Node name for S&R": "CR Seed"}, "size": {"0": 243.4204864501953, "1": 102}, "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": {"0": -2391.759033203125, "1": -817.3389282226562}, "properties": {"Node name for S&R": "CR Seed"}, "size": {"0": 243.4204864501953, "1": 102}, "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": -2906.1630859375, "1": -876.3988647460938}, "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": {"0": 411.6590881347656, "1": 124}, "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 473, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 34, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": {"0": -1187.8585205078125, "1": -966.1553955078125}, "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 37, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": {"0": -1222.858642578125, "1": -835.1553344726562}, "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 47, "outputs": [{"links": [506, 514], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [507, 510, 513], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": {"0": -2387, "1": -1450}, "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": {"0": 279.70758056640625, "1": 126}, "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", 1, 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 511, "name": "model", "type": "MODEL"}, {"link": 512, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 32, "outputs": [{"links": [501], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [502], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": {"0": -2676, "1": -1580}, "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": {"0": 228.4159698486328, "1": 126}, "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 501, "name": "model", "type": "MODEL"}, {"link": 502, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 41, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": {"0": -2373.759033203125, "1": -1625.************}, "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": {"0": 273.12445068359375, "1": 126}, "title": "去模糊lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 299, "flags": {}, "inputs": [{"link": 557, "name": "image", "type": "IMAGE"}, {"link": 503, "name": "detailer_pipe", "type": "DETAILER_PIPE"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC"}], "mode": "${isFluxRepair?then(0,4)}", "order": 69, "outputs": [{"links": [517], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"name": "cropped_enhanced_alpha", "shape": 6, "type": "IMAGE"}, {"links": [], "name": "mask", "shape": 3, "slot_index": 3, "type": "MASK"}, {"name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": {"0": 3959, "1": -1615}, "properties": {"Node name for S&R": "FaceDetailerPipe"}, "size": {"0": 346, "1": 782}, "type": "FaceDetailerPipe", "widgets_values": [512, true, 1024, "${faceSeed}", "randomize", 10, "${faceCfg}", "euler", "beta", 0.4, 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 300, "flags": {}, "inputs": [{"link": 504, "name": "basic_pipe", "type": "BASIC_PIPE"}, {"link": 505, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"name": "sam_model_opt", "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR"}, {"name": "detailer_hook", "type": "DETAILER_HOOK"}], "mode": "${isFluxRepair?then(0,4)}", "order": 58, "outputs": [{"links": [503], "name": "detailer_pipe", "shape": 3, "slot_index": 0, "type": "DETAILER_PIPE"}], "pos": {"0": 3634.568359375, "1": -1562.34912109375}, "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "size": {"0": 262, "1": 204.*************}, "type": "BasicPipeToDetailerPipe", "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 301, "flags": {}, "inputs": [{"link": 506, "name": "model", "type": "MODEL"}, {"link": 507, "name": "clip", "type": "CLIP"}, {"link": 520, "name": "vae", "type": "VAE"}, {"link": 508, "name": "positive", "type": "CONDITIONING"}, {"link": 509, "name": "negative", "type": "CONDITIONING"}], "mode": "${isFluxRepair?then(0,4)}", "order": 55, "outputs": [{"links": [504], "name": "basic_pipe", "shape": 3, "slot_index": 0, "type": "BASIC_PIPE"}], "pos": {"0": 3340.568359375, "1": -1556.34912109375}, "properties": {"Node name for S&R": "ToBasicPipe"}, "size": {"0": 241.79998779296875, "1": 106}, "type": "ToBasicPipe", "widgets_values": []}, {"id": 302, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 510, "name": "clip", "type": "CLIP"}, {"link": 554, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isFluxRepair?then(0,4)}", "order": 51, "outputs": [{"links": [508], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 3067.568359375, "1": -1561.34912109375}, "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 210, "1": 116.85224914550781}, "title": "修脸prompt", "type": "CLIPTextEncode", "widgets_values": ["${FACE.extTags}"]}, {"id": 303, "flags": {}, "inputs": [], "mode": "${isFluxRepair?then(0,4)}", "order": 15, "outputs": [{"links": [505], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "shape": 3, "type": "SEGM_DETECTOR"}], "pos": {"0": 3037.568359375, "1": -1130.347900390625}, "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": {"0": 226.8000030517578, "1": 78}, "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 304, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${isFluxRepair?then(0,4)}", "order": 16, "outputs": [{"links": [509], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 3055.568359375, "1": -1284.34912109375}, "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "size": {"0": 210, "1": 26}, "type": "ImpactNegativeConditioningPlaceholder", "widgets_values": []}, {"id": 306, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 17, "outputs": [], "pos": {"0": -2669.759033203125, "1": -1746.************}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"]}, {"id": 307, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [], "pos": {"0": -2348.759033203125, "1": -1758.************}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["去模糊lora节点\n纯色背景流程：关闭\n其他流程：打开"]}, {"id": 308, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [], "pos": {"0": 4713, "1": -1386}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 309, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 20, "outputs": [], "pos": {"0": 5103.61328125, "1": -1392.820556640625}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 310, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [], "pos": {"0": 5858, "1": -1524}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 79, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 7402, "1": -1349}, "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": {"0": 378, "1": 146}, "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 319, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 21, "outputs": [], "pos": {"0": 4720.61328125, "1": -990.8206176757812}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 50, "outputs": [], "pos": {"0": 8041, "1": -456}, "properties": {"Node name for S&R": "ConrainTextSave"}, "size": {"0": 315, "1": 106}, "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 324, "flags": {}, "inputs": [], "mode": "${isFluxRepair?then(4,0)}", "order": 22, "outputs": [{"label": "BBOX_DETECTOR", "links": [552], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": {"0": 3060.822265625, "1": 98.61608123779297}, "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": {"0": 315, "1": 78}, "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 325, "flags": {}, "inputs": [], "mode": "${isFluxRepair?then(4,0)}", "order": 23, "outputs": [{"label": "SAM_MODEL", "links": [553], "name": "SAM_MODEL", "shape": 3, "slot_index": 0, "type": "SAM_MODEL"}], "pos": {"0": 3093.822265625, "1": 252.61593627929688}, "properties": {"Node name for S&R": "SAMLoader"}, "size": {"0": 315, "1": 82}, "type": "SAMLoader", "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 326, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"label": "clip", "link": 545, "name": "clip", "type": "CLIP"}, {"link": 555, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${isFluxRepair?then(4,0)}", "order": 40, "outputs": [{"label": "CONDITIONING", "links": [550], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 3038.822265625, "1": -427.3840637207031}, "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": {"0": 389.95330810546875, "1": 157.71157836914062}, "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["", "none", "A1111"]}, {"id": 327, "flags": {}, "inputs": [{"label": "clip", "link": 546, "name": "clip", "type": "CLIP"}], "mode": "${isFluxRepair?then(4,0)}", "order": 38, "outputs": [{"label": "CONDITIONING", "links": [551], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 3060.822265625, "1": -177.3840789794922}, "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "size": {"0": 400, "1": 200}, "type": "BNK_CLIPTextEncodeAdvanced", "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 328, "flags": {}, "inputs": [{"label": "image", "link": 574, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 547, "name": "model", "type": "MODEL"}, {"label": "clip", "link": 548, "name": "clip", "type": "CLIP"}, {"label": "vae", "link": 549, "name": "vae", "type": "VAE"}, {"label": "positive", "link": 550, "name": "positive", "type": "CONDITIONING"}, {"label": "negative", "link": 551, "name": "negative", "slot_index": 5, "type": "CONDITIONING"}, {"label": "bbox_detector", "link": 552, "name": "bbox_detector", "slot_index": 6, "type": "BBOX_DETECTOR"}, {"label": "sam_model_opt", "link": 553, "name": "sam_model_opt", "slot_index": 7, "type": "SAM_MODEL"}, {"label": "segm_detector_opt", "name": "segm_detector_opt", "slot_index": 8, "type": "SEGM_DETECTOR"}, {"label": "detailer_hook", "name": "detailer_hook", "type": "DETAILER_HOOK"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC"}], "mode": "${isFluxRepair?then(4,0)}", "order": 68, "outputs": [{"label": "image", "links": [557], "name": "image", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "cropped_refined", "links": [], "name": "cropped_refined", "shape": 6, "slot_index": 1, "type": "IMAGE"}, {"label": "cropped_enhanced_alpha", "links": [], "name": "cropped_enhanced_alpha", "shape": 6, "slot_index": 2, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 3, "type": "MASK"}, {"label": "detailer_pipe", "name": "detailer_pipe", "shape": 3, "type": "DETAILER_PIPE"}, {"label": "cnet_images", "name": "cnet_images", "shape": 6, "type": "IMAGE"}], "pos": {"0": 3925, "1": -506}, "properties": {"Node name for S&R": "FaceDetailer"}, "size": {"0": 350.5302734375, "1": 902.3991088867188}, "type": "FaceDetailer", "widgets_values": [384, true, 512, "${faceSeed}", "randomize", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, "", 1, 1, 0]}, {"id": 329, "flags": {}, "inputs": [], "mode": "${isFluxRepair?then(4,0)}", "order": 24, "outputs": [{"label": "MODEL", "links": [547], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "CLIP", "links": [545, 546, 548], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}, {"label": "VAE", "links": [549], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE"}], "pos": {"0": 3032.625, "1": -614.596923828125}, "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": {"0": 315, "1": 98}, "type": "CheckpointLoaderSimple", "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [554, 555, 575], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": -1208, "1": -1157}, "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": {"0": 319.1407165527344, "1": 134.37188720703125}, "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.extInfo.expression}", 1620, "randomize"]}, {"id": 332, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [], "pos": {"0": 3485.568359375, "1": -1160.34912109375}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"]}, {"id": 333, "bgcolor": "#533", "color": "#322", "flags": {}, "inputs": [], "mode": 0, "order": 30, "outputs": [], "pos": {"0": 3524, "1": 40}, "properties": {}, "size": {"0": 210, "1": 91.33761596679688}, "type": "Note", "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"]}, {"id": 334, "bgcolor": "#533", "color": "#322", "flags": {"collapsed": true}, "inputs": [{"label": "clip", "link": 558, "name": "clip", "type": "CLIP"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 45, "outputs": [{"label": "CONDITIONING", "links": [567], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 512.3257446289062, "1": -1272.296142578125}, "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 301.23193359375, "1": 108.12081909179688}, "type": "CLIPTextEncode", "widgets_values": [""]}, {"id": 335, "flags": {}, "inputs": [{"label": "pixels", "link": 559, "name": "pixels", "type": "IMAGE"}, {"label": "vae", "link": 560, "name": "vae", "type": "VAE"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 63, "outputs": [{"label": "LATENT", "links": [561], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": {"0": 156.32608032226562, "1": -817.29638671875}, "properties": {"Node name for S&R": "VAEEncode"}, "size": {"0": 210, "1": 46}, "type": "VAEEncode", "widgets_values": []}, {"id": 336, "flags": {}, "inputs": [{"label": "samples", "link": 561, "name": "samples", "type": "LATENT"}, {"label": "mask", "link": 562, "name": "mask", "type": "MASK"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 65, "outputs": [{"label": "LATENT", "links": [568], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": {"0": 508.32574462890625, "1": -863.2962646484375}, "properties": {"Node name for S&R": "SetLatentNoiseMask"}, "size": {"0": 210, "1": 46}, "type": "SetLatentNoiseMask", "widgets_values": []}, {"id": 337, "flags": {}, "inputs": [{"label": "samples", "link": 563, "name": "samples", "type": "LATENT"}, {"label": "vae", "link": 564, "name": "vae", "type": "VAE"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 67, "outputs": [{"label": "IMAGE", "links": [574], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 1903.6512451171875, "1": -1472.183837890625}, "properties": {"Node name for S&R": "VAEDecode"}, "size": {"0": 210, "1": 46}, "type": "VAEDecode", "widgets_values": []}, {"id": 338, "flags": {}, "inputs": [{"label": "model", "link": 565, "name": "model", "type": "MODEL"}, {"label": "positive", "link": 566, "name": "positive", "type": "CONDITIONING"}, {"label": "negative", "link": 567, "name": "negative", "type": "CONDITIONING"}, {"label": "latent_image", "link": 568, "name": "latent_image", "type": "LATENT"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 66, "outputs": [{"label": "LATENT", "links": [563], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": {"0": 1481.3271484375, "1": -1345.296142578125}, "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": {"0": 312.9380798339844, "1": 269.4892883300781}, "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "randomize", 10, 7.5, "dpmpp_sde", "karras", 0.2]}, {"id": 339, "flags": {}, "inputs": [{"label": "", "link": 601, "name": "", "type": "*"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 62, "outputs": [{"links": [559, 569], "name": "", "type": "IMAGE"}], "pos": {"0": -238.6738739013672, "1": -1097.2965087890625}, "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 340, "flags": {}, "inputs": [{"link": 569, "name": "images", "type": "IMAGE"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 64, "outputs": [{"links": [562], "name": "masks", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": {"0": 139.32608032226562, "1": -1166.2962646484375}, "properties": {"Node name for S&R": "APersonMaskGenerator"}, "size": {"0": 315, "1": 178}, "type": "APersonMaskGenerator", "widgets_values": [true, false, true, true, false, 0.4]}, {"id": 341, "flags": {}, "inputs": [], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 26, "outputs": [{"links": [570], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [571], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}, {"links": [560, 564], "name": "VAE", "shape": 3, "slot_index": 2, "type": "VAE"}], "pos": {"0": -414.6739807128906, "1": -1475.2960205078125}, "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": {"0": 310.720947265625, "1": 98}, "type": "CheckpointLoaderSimple", "widgets_values": ["epicrealism_naturalSinRC1VAE.safetensors"]}, {"id": 342, "flags": {}, "inputs": [{"link": 570, "name": "model", "type": "MODEL"}, {"link": 571, "name": "clip", "type": "CLIP"}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 39, "outputs": [{"links": [565], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [558, 572], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": {"0": 175.32611083984375, "1": -1499.296142578125}, "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": {"0": 315, "1": 126}, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["open_lora/more_details.safetensors", 0.5, 1]}, {"id": 343, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 572, "name": "clip", "type": "CLIP"}, {"link": 575, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": "${(true||isLoraFace&&!isLoraSwapFace)?then(4,0)}", "order": 46, "outputs": [{"links": [566], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": {"0": 1060.1024169921875, "1": -978.25634765625}, "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": {"0": 210, "1": 116.85224914550781}, "title": "修脸prompt", "type": "CLIPTextEncode", "widgets_values": ["happy expression, a 20 years-old Chinese fashion girl, Very fair skin, the model has Radiant Smile"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "type": "*"}, {"name": "any_b", "type": "*"}, {"name": "any_c", "type": "*"}, {"name": "any_d", "type": "*"}], "mode": 0, "order": 36, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": {"0": -2045, "1": -741}, "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": {"0": 368.1804504394531, "1": 203.2705841064453}, "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "type": "*"}, {"name": "any_b", "type": "*"}, {"name": "any_c", "type": "*"}, {"name": "any_d", "type": "*"}], "mode": 0, "order": 35, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": {"0": -2022, "1": -640}, "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": {"0": 368.1804504394531, "1": 203.2705841064453}, "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 43, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": -1813, "1": -763}, "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": {"0": 184.8000030517578, "1": 27.56488609313965}, "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 49, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": {"0": -1563, "1": -767}, "properties": {"Node name for S&R": "JWStringToInteger"}, "size": {"0": 210, "1": 34}, "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 42, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": {"0": -1742, "1": -633}, "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": {"0": 184.8000030517578, "1": 39.813907623291016}, "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 48, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": {"0": -1509, "1": -612}, "properties": {"Node name for S&R": "JWStringToInteger"}, "size": {"0": 210, "1": 34}, "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 80, "outputs": [{"links": [603], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": {"0": 7622, "1": -839}, "properties": {"Node name for S&R": "ImageCrop"}, "size": {"0": 225.3616943359375, "1": 122.95598602294922}, "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "299": 3, "328": 3, "331": 1, "338": 0}, "version": 0.4, "widget_idx_map": {"274": {"sampler_name": 0}, "275": {"scheduler": 0}, "277": {"noise_seed": 0}, "282": {"seed": 1}, "284": {"seed": 0}, "285": {"seed": 0}, "286": {"seed": 1}, "299": {"sampler_name": 7, "scheduler": 8, "seed": 3}, "328": {"sampler_name": 7, "scheduler": 8, "seed": 3}, "331": {"seed": 1}, "338": {"sampler_name": 4, "scheduler": 5, "seed": 0}}}}}}
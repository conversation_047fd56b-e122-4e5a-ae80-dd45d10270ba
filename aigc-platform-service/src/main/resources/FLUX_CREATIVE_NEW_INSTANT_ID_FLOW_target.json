{"client_id": "54f7869e4501480189afc9523d2f2956", "prompt": {"177": {"inputs": {"text": "product/20250527/101161/303403", "text_b": "product_2080950", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1785", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["725", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": ""}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB", "_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "268": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "beta", "steps": "30", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "277": {"inputs": {"noise_seed": 42029499340386}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["268", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["378", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), front view, (full-body portrait:1.2), full body, The model is wearing A navy blue jacket top and the inner layer of the jacket is black and beige wide-leg pants set, The jacket is hip-length with a round neckline, long sleeves, and large silver buttons down the front without any visible logos or patterns. The fabric of this jacket appears to be lightweight and slightly sheer, with a subtle sheen that suggests it may be made from a silk blend or a fine synthetic material.  The pants are loose-fitting, with a wide leg cut and an ankle-length hem, made from a light, textured fabric. ,buttoned.20-year-old European female model, is wearing {black sleeveless top} inside. 20-year-old European female model, is wearing loafers. The  loafers are made of leather. \n\n\nA softly lit studio with neutral, diffused lighting that creates gentle shadows and a balanced, open atmosphere. The background is a soft, light beige or pale grey, with clean lines and minimalistic walls that subtly reflect the light. The light is directional but softly filtered, coming from one side and casting natural shadows on the walls and floor, enhancing the depth of the space. The overall effect is calm, elegant, and spacious, with a focus on creating a sense of openness and subtle contrast between light and shadow without overwhelming warmth.{20-year-old European female model stands casually with hands in his pockets and his legs bent.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model is walking with one hand in pocket and one hand naturally perpendicular to side.|20-year-old European female model is walking with hands in his pockets.|20-year-old European female model is walking with hands hanging down naturally.},\n\n\na mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", "seed": 2048}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": 1785}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": 1340}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "online_product/女士中式大龄开衫_19429_20250521_103121/女士中式大龄开衫_19429_20250521_103121-flux/女士中式大龄开衫_19429_20250521_103121-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "online_product/Nalis_4567_20241215_204429/Nalis_4567_20241215_204429-flux/Nalis_4567_20241215_204429-flux.safetensors", "strength_model": 0.3, "strength_clip": 0.3, "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "298": {"inputs": {"lora_name": "online_product/如缤场景测试_17775_20250514_171805/如缤场景测试_17775_20250514_171805-flux/如缤场景测试_17775_20250514_171805-flux.safetensors", "strength_model": 0.8, "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "331": {"inputs": {"prompts": "a mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["725", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "373": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "378": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 0, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["296", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "389": {"inputs": {"instantid_file": "ip-adapter.bin"}, "class_type": "InstantIDModelLoader", "_meta": {"title": "Load InstantID Model"}}, "390": {"inputs": {"provider": "CUDA"}, "class_type": "InstantIDFaceAnalysis", "_meta": {"title": "InstantID Face Analysis"}}, "391": {"inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "392": {"inputs": {"noise_mask": true, "positive": ["618", 0], "negative": ["618", 1], "vae": ["416", 2], "pixels": ["414", 0], "mask": ["402", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "397": {"inputs": {"text": ["331", 0], "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "398": {"inputs": {"text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye", "clip": ["416", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "399": {"inputs": {"criteria": "area", "order": "descending", "take_start": 0, "take_count": 1, "faces": ["436", 0]}, "class_type": "OrderedFaceFilter", "_meta": {"title": "Ordered Face Filter"}}, "402": {"inputs": {"invert_mask": false, "grow": 16, "blur": 8, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["624", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "409": {"inputs": {"method": "adain", "image_output": "<PERSON>de", "save_prefix": "ComfyUI", "image_ref": ["414", 0], "image_target": ["413", 0]}, "class_type": "easy imageColorMatch", "_meta": {"title": "Image Color Match"}}, "410": {"inputs": {"images": ["697", 0], "face": ["399", 0], "crop": ["409", 0], "mask": ["754", 0], "warp": ["414", 2]}, "class_type": "WarpFacesBack", "_meta": {"title": "<PERSON>p Faces Back"}}, "411": {"inputs": {"seed": 42029499340386, "steps": 4, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "denoise": 0.4, "model": ["412", 0], "positive": ["392", 0], "negative": ["392", 1], "latent_image": ["392", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "412": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "instantid": ["389", 0], "insightface": ["390", 0], "control_net": ["391", 0], "image": ["446", 0], "model": ["416", 0], "positive": ["397", 0], "negative": ["398", 0], "image_kps": ["414", 0]}, "class_type": "ApplyInstantID", "_meta": {"title": "Apply InstantID"}}, "413": {"inputs": {"samples": ["411", 0], "vae": ["416", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "414": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["399", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "416": {"inputs": {"ckpt_name": "sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "436": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["697", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "446": {"inputs": {"crop_size": 1024, "crop_factor": 2, "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "faces": ["447", 0]}, "class_type": "CropFaces", "_meta": {"title": "CropFaces"}}, "447": {"inputs": {"threshold": 0.1, "min_size": 64, "max_size": 1024, "image": ["592", 0]}, "class_type": "DetectFaces", "_meta": {"title": "DetectFaces"}}, "462": {"inputs": {"image": "1ed0c076607c45ada29d19e5f53a74b7.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "474": {"inputs": {"Input": ["771", 0], "image1": ["475", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "475": {"inputs": {"image": "080c38bdd5404804a84577ca004d24ea.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "476": {"inputs": {"image": "594a79670a204dfe897520c682bb6511.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "477": {"inputs": {"Input": ["770", 0], "image1": ["476", 0]}, "class_type": "CR Image Input Switch", "_meta": {"title": "🔀 CR Image Input Switch"}}, "559": {"inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}, "class_type": "ImpactMakeImageList", "_meta": {"title": "Make Image List"}}, "561": {"inputs": {"width": 2048, "height": 2048, "interpolation": "lanc<PERSON>s", "method": "keep proportion", "condition": "upscale if smaller", "multiple_of": 0, "image": ["559", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "570": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["610", 0], "image": ["561", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "574": {"inputs": {"segs": ["597", 0]}, "class_type": "SegsToCombinedMask", "_meta": {"title": "SEGS to MASK (combined)"}}, "576": {"inputs": {"padding_left": 64, "padding_right": 64, "padding_top": 64, "padding_bottom": 64, "image": ["561", 0], "mask": ["580", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "580": {"inputs": {"invert_mask": false, "grow": 256, "blur": 0, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["574", 0]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "592": {"inputs": {"images": ["576", 0]}, "class_type": "easy imageListToImageBatch", "_meta": {"title": "Image List To Image Batch"}}, "597": {"inputs": {"target": "area(=w*h)", "order": true, "take_start": 0, "take_count": 1, "segs": ["570", 0]}, "class_type": "ImpactSEGSOrderedFilter", "_meta": {"title": "SEGS Filter (ordered)"}}, "610": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "618": {"inputs": {"strength": 0.1, "start_percent": 0, "end_percent": 0.5, "positive": ["412", 1], "negative": ["412", 2], "control_net": ["619", 0], "image": ["414", 0], "vae": ["416", 2]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "619": {"inputs": {"control_net_name": "TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "624": {"inputs": {"method": "human_parsing_lip", "confidence": 0.4, "crop_multi": 0, "mask_components": "13", "image": ["414", 0]}, "class_type": "easy humanSegmentation", "_meta": {"title": "Human Segmentation"}}, "670": {"inputs": {"model": ["683", 0], "conditioning": ["672", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "671": {"inputs": {"sampler_name": "euler_ancestral"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "672": {"inputs": {"noise_mask": true, "positive": ["673", 0], "negative": ["698", 1], "vae": ["270", 0], "pixels": ["684", 0], "mask": ["701", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "673": {"inputs": {"guidance": 2, "conditioning": ["698", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "674": {"inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "676": {"inputs": {"expand": -10, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 15, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["693", 2]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "678": {"inputs": {"seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "depth_ckpt": "none", "normal_ckpt": "none", "pose_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "remove_background": true, "use_yolo": false, "show_pose_object": false, "seg_pellete": true, "convert_torchscript_to_bf16": true}, "class_type": "Sapiens<PERSON><PERSON>der", "_meta": {"title": "Sapiens<PERSON><PERSON>der"}}, "679": {"inputs": {"scheduler": "sgm_uniform", "steps": 20, "denoise": 0.65, "model": ["753", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "681": {"inputs": {"noise": ["682", 0], "guider": ["670", 0], "sampler": ["671", 0], "sigmas": ["679", 0], "latent_image": ["705", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "682": {"inputs": {"noise_seed": 745052156325993}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "683": {"inputs": {"model": ["753", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "684": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["692", 0], "image": ["693", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "687": {"inputs": {"conditioning": ["688", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "688": {"inputs": {"text": ["331", 0], "clip": ["271", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "692": {"inputs": {"target_size": 1024, "image": ["693", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "693": {"inputs": {"expand_LRU": 20, "expand_B": 20, "image": ["269", 0], "mask": ["752", 0]}, "class_type": "BoundedImageCropWithMask_v3_LR", "_meta": {"title": "Bounded Image Crop With Mask LR v3"}}, "694": {"inputs": {"type": "canny/lineart/anime_lineart/mlsd", "control_net": ["674", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "695": {"inputs": {"low_threshold": 0.1, "high_threshold": 0.5, "image": ["684", 0]}, "class_type": "<PERSON><PERSON>", "_meta": {"title": "<PERSON><PERSON>"}}, "697": {"inputs": {"samples": ["681", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "698": {"inputs": {"strength": 0.6, "start_percent": 0, "end_percent": 0.5, "positive": ["688", 0], "negative": ["687", 0], "control_net": ["694", 0], "image": ["695", 0], "vae": ["270", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "701": {"inputs": {"scale_by": ["692", 0], "mask": ["676", 0]}, "class_type": "MaskUpscale_LR", "_meta": {"title": "Mask Upscale LR"}}, "703": {"inputs": {"seg_select": "2.<PERSON>_<PERSON>", "add_seg_index": "3,23,24,25,26,27", "save_pose": false, "BG_R": 255, "BG_G": 255, "BG_B": 255, "model": ["678", 0], "image": ["269", 0]}, "class_type": "SapiensSampler", "_meta": {"title": "SapiensSampler"}}, "705": {"inputs": {"amount": 1, "samples": ["672", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "725": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["269", 0], "target_mask": ["676", 0], "target_bounds": ["693", 1], "source": ["410", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "752": {"inputs": {"call_code": "import cv2\nimport numpy as np\nimport torch\n\ndef filter_mask(mask):\n    \"\"\"\n    输入torch tensor格式的mask，形状为【通道，高度，宽度】或【高度，宽度】\n    计算连通域，返回只有最大连通域的mask，其他区域设置为0\n    \"\"\"\n    # 保存原始设备和数据类型\n    original_device = mask.device\n    original_dtype = mask.dtype\n    \n    # 转换为numpy处理\n    if mask.dim() == 3:  # 通道处理 [channel, height, width]\n        num_channels = mask.shape[0]\n        new_masks = []\n        \n        for i in range(num_channels):\n            single_mask = mask[i].cpu().numpy()\n            processed_mask = _process_single_mask(single_mask)\n            new_masks.append(torch.from_numpy(processed_mask))\n        \n        result = torch.stack(new_masks)\n    elif mask.dim() == 2:  # 单个mask [height, width]\n        single_mask = mask.cpu().numpy()\n        processed_mask = _process_single_mask(single_mask)\n        result = torch.from_numpy(processed_mask)\n    else:\n        raise ValueError(\"mask维度必须是2D或3D\")\n    \n    # 转换回原始设备和数据类型\n    return result.to(device=original_device, dtype=original_dtype)\n\ndef _process_single_mask(mask):\n    \"\"\"处理单个2D mask\"\"\"\n    # 数据预处理：确保正确的二值化\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        # 如果是浮点数，假设范围是0-1，转换为0-255\n        mask_normalized = (mask * 255).astype(np.uint8)\n    else:\n        mask_normalized = mask.astype(np.uint8)\n    \n    # 二值化处理，确保只有0和255\n    _, binary_mask = cv2.threshold(mask_normalized, 127, 255, cv2.THRESH_BINARY)\n    \n    # 形态学开运算，去除小噪声\n    kernel = np.ones((3,3), np.uint8)\n    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)\n    \n    # 计算连通域\n    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_mask)\n    \n    if num_labels <= 1:  # 只有背景，没有前景\n        return np.zeros_like(mask)\n    \n    # 获取最大的连通域（排除背景）\n    max_area_index = np.argmax(stats[1:, cv2.CC_STAT_AREA]) + 1\n    \n    # 创建新的mask，只有最大的连通域为1，其他为0\n    new_mask = (labels == max_area_index).astype(mask.dtype)\n    \n    # 如果原始数据是浮点型，将结果转换回0-1范围\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        new_mask = new_mask.astype(mask.dtype)\n    \n    return new_mask\n\ndef call(any_a, any_b, any_c, any_d):\n    new_mask = filter_mask(any_a)\n    if torch.all(new_mask == 0):\n        blank_size = 4\n        # 设置左上角2x2区域为1\n        if new_mask.dim() == 2:\n            new_mask[0:blank_size, 0:blank_size] = 1\n        else:  # 3D\n            new_mask[0, 0:blank_size, 0:blank_size] = 1\n    return [new_mask]", "any_a": ["703", 4]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "753": {"inputs": {"lora_name": "online_product/Nalis_4567_20241215_204429/Nalis_4567_20241215_204429-flux/Nalis_4567_20241215_204429-flux.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "754": {"inputs": {"invert_mask": false, "grow": 64, "blur": 32, "low_limit": 0, "high_limit": 1, "enabled": true, "mask": ["414", 1]}, "class_type": "MaskFastGrow", "_meta": {"title": "<PERSON> Grow Fast"}}, "770": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "771": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 772, "last_link_id": 1426, "nodes": [{"id": 185, "type": "Text Concatenate", "pos": [7636.95849609375, -1253.41162109375], "size": [315, 178], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 216, "type": "ConrainImageSave", "pos": [8201.634765625, -1526.3419189453125], "size": [320, 266], "flags": {}, "order": 136, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 856, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "type": "Text Concatenate", "pos": [6991.95849609375, -1432.4114990234375], "size": [250, 142], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [6502.95849609375, -1528.411376953125], "size": [315, 58], "flags": {"collapsed": false}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 268, "type": "CLIPTextEncode", "pos": [1311.6290283203125, -1404.5489501953125], "size": [285.6000061035156, 54], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 513}, {"name": "text", "type": "STRING", "link": 453, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [464], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 271, "type": "DualCLIPLoader", "pos": [-1186.1917724609375, -930.2503662109375], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 664, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 274, "type": "KSamplerSelect", "pos": [1982.990966796875, -1166.6705322265625], "size": [210, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [2003.67236328125, -999.4038696289062], "size": [210, 106], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["beta", "30", 1]}, {"id": 276, "type": "BasicGuider", "pos": [1966.************, -1408.01806640625], "size": [161.1999969482422, 46], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 278, "type": "FluxGuidance", "pos": [1672.114501953125, -1396.1895751953125], "size": [211.60000610351562, 58], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 464}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1691.************, -1161.************], "size": [210, 86.50716400146484], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 284, "type": "CR Seed", "pos": [1318.2945556640625, -531.36474609375], "size": [243.4204864501953, 102], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1785, "fixed"]}, {"id": 285, "type": "CR Seed", "pos": [1304.2945556640625, -717.3659057617188], "size": [243.4204864501953, 102], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [1340, "fixed"]}, {"id": 287, "type": "Reroute", "pos": [2508.18603515625, -866.1821899414062], "size": [75, 26], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 473, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2473.18603515625, -735.1822509765625], "size": [75, 26], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 320, "type": "ConrainTextSave", "pos": [8207.953125, -1181.4114990234375], "size": [315, 106], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1651.5965576171875, -641.0267944335938], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1674.5916748046875, -540.0256958007812], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [1883.5928955078125, -663.0265502929688], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2133.54541015625, -667.0265502929688], "size": [210, 34], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [1954.5938720703125, -533.0257568359375], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2187.53271484375, -512.0257568359375], "size": [210, 34], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 365, "type": "Image Size to Number", "pos": [7447.1708984375, -2335.321533203125], "size": [229.20001220703125, 126], "flags": {}, "order": 132, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1416, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [7741.1708984375, -2260.321533203125], "size": [210, 67.1211166381836], "flags": {}, "order": 134, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 477, "type": "CR Image Input Switch", "pos": [4193.0537109375, -1612.7928466796875], "size": [210, 74], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1089, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 1424, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1044, 1060, 1070], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 559, "type": "ImpactMakeImageList", "pos": [4528.984375, -1783.8060302734375], "size": [140, 86], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 1001}, {"name": "image2", "type": "IMAGE", "link": 1069}, {"name": "image3", "type": "IMAGE", "link": 1070}, {"name": "image4", "type": "IMAGE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1009], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "widgets_values": []}, {"id": 592, "type": "easy imageListToImageBatch", "pos": [4832.5732421875, -1937.627197265625], "size": [315, 26], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1072}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1073], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "widgets_values": []}, {"id": 579, "type": "Reroute", "pos": [4805.14599609375, -1606.9412841796875], "size": [75, 26], "flags": {}, "order": 61, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1092}], "outputs": [{"name": "", "type": "IMAGE", "links": [1037], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 447, "type": "DetectFaces", "pos": [5064.96484375, -2149.68798828125], "size": [216.65777587890625, 143.53131103515625], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1073}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [1071], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 561, "type": "ImageResize+", "pos": [4475.1279296875, -1605.0535888671875], "size": [261.8576965332031, 224.5092315673828], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1009}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1092, 1093], "slot_index": 0}, {"name": "width", "type": "INT", "links": null}, {"name": "height", "type": "INT", "links": null}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 597, "type": "ImpactSEGSOrderedFilter", "pos": [4494.18310546875, -1262.16796875], "size": [210, 158.96408081054688], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1084}], "outputs": [{"name": "filtered_SEGS", "type": "SEGS", "links": [1086], "slot_index": 0}, {"name": "remained_SEGS", "type": "SEGS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 574, "type": "SegsToCombinedMask", "pos": [4766.97509765625, -1133.2860107421875], "size": [289.79998779296875, 26], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "segs", "type": "SEGS", "link": 1086}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1039], "slot_index": 0}], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "widgets_values": []}, {"id": 457, "type": "Reroute", "pos": [3770.6259765625, -681.1192016601562], "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 786, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [788], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 446, "type": "CropFaces", "pos": [5317.95458984375, -2141.50634765625], "size": [221.15121459960938, 146], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 1071}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [713, 762], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 550, "type": "Reroute", "pos": [4180.76416015625, -906.014404296875], "size": [75, 26], "flags": {}, "order": 117, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1335}], "outputs": [{"name": "", "type": "IMAGE", "links": [985], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 549, "type": "Reroute", "pos": [4441.5126953125, -554.5872192382812], "size": [75, 26], "flags": {}, "order": 119, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 985}], "outputs": [{"name": "", "type": "IMAGE", "links": [979, 980, 1132], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 355, "type": "ImageCrop", "pos": [7678.95849609375, -1556.411376953125], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 142, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [856], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 674, "type": "ControlNetLoader", "pos": [1177.1427001953125, 97.07112121582031], "size": [518.1205444335938, 58.483619689941406], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1258], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 675, "type": "Reroute", "pos": [197.6379852294922, 213.60537719726562], "size": [75, 26], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1234, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "FLOAT", "links": [1245, 1271]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 681, "type": "SamplerCustomAdvanced", "pos": [4482.06591796875, 329.81170654296875], "size": [355.20001220703125, 106], "flags": {}, "order": 108, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 1238, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 1239, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 1240, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 1241, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 1242}], "outputs": [{"name": "output", "type": "LATENT", "links": [1261], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 270, "type": "VAELoader", "pos": [2301.************, -1185.7738037109375], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520, 1277], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 689, "type": "Reroute", "pos": [1222.99072265625, 280.************], "size": [75, 26], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1285}], "outputs": [{"name": "", "type": "CLIP", "links": [1249]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 576, "type": "Bounded Image Crop with Mask", "pos": [4769.27783203125, -2157.122314453125], "size": [235.1999969482422, 150.37045288085938], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1037}, {"name": "mask", "type": "MASK", "link": 1040}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1072], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": null}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [64, 64, 64, 64]}, {"id": 398, "type": "CLIPTextEncode", "pos": [3991.1611328125, -612.0104370117188], "size": [239.4051971435547, 91.89370727539062], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 956}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [716], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"], "color": "#232", "bgcolor": "#353"}, {"id": 693, "type": "BoundedImageCropWithMask_v3_LR", "pos": [-289.6543884277344, 130.72567749023438], "size": [285.6000061035156, 142], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1256}, {"name": "mask", "type": "MASK", "link": 1394}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1244, 1255], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [1324], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [1235], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [20, 20]}, {"id": 436, "type": "DetectFaces", "pos": [3731.463623046875, -531.0082397460938], "size": [210, 126], "flags": {}, "order": 111, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1336}, {"name": "mask", "type": "MASK", "shape": 7}], "outputs": [{"name": "faces", "type": "FACE", "links": [684], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DetectFaces"}, "widgets_values": [0.1, 64, 1024]}, {"id": 616, "type": "Reroute", "pos": [3913.857666015625, -2227.386474609375], "size": [75, 26], "flags": {}, "order": 114, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1121}], "outputs": [{"name": "", "type": "IMAGE", "links": [1335], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 676, "type": "ConrainGrowMaskWithBlur", "pos": [150.67288208007812, 164.52459716796875], "size": [236.14947509765625, 246], "flags": {"collapsed": false}, "order": 94, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1235}], "outputs": [{"name": "mask", "type": "MASK", "links": [1270, 1321], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 692, "type": "UpscaleSizeCalculator", "pos": [-68.13079833984375, 555.9083251953125], "size": [259.6224060058594, 129.99847412109375], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1255}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [1234], "slot_index": 0}, {"name": "rescale_width", "type": "INT", "links": null}, {"name": "recover_factor", "type": "FLOAT", "links": null}, {"name": "recover_width", "type": "INT", "links": null}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [1024]}, {"id": 580, "type": "MaskFastGrow", "pos": [4883.5205078125, -1450.7889404296875], "size": [210, 178], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1039}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1040], "slot_index": 0}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 684, "type": "ImageScaleBy", "pos": [502.68756103515625, 179.6353759765625], "size": [250.58731079101562, 109.74829864501953], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1244, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 1245, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1246], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2276.************, -1484.35302734375], "size": [236.8000030517578, 127.00992584228516], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-695.4215087890625, -691.8218994140625], "size": [315, 154], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 385, "type": "Reroute", "pos": [-552.3848876953125, -1389.24609375], "size": [75, 26], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 283, "type": "ShowText|pysssss", "pos": [1292.5960693359375, -1219.8851318359375], "size": [256.63372802734375, 226], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 472, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [453], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": [["(linrun2111:1.3), back view, angled slightly to the left,whole body,The model is wearing A black blouse featuring large yellow polka dots and yellow floral patterns evenly distributed across the fabric, with a loose fit, three-quarter length sleeves, and a round neckline. 20-year-old Chinese female  model, is wearing  mgs2222 dark blue straight-leg jeans underneath. 20-year-old Chinese female  model, is wearing  gold hoop earrings. 20-year-old Chinese female  model, is wearing mgs2222 white slingback heels with black straps. The  mgs2222 white slingback heels with black straps are made of leather. \n\n\nmgs2222,an urban street in front of a historic brick building with large glass windows, beige stone trim, and reflected cityscape; stone pavement and distant pedestrians. The natural daylight casts soft shadows on the ground extending to the left.walking down the street.walking away from the camera with left foot forward and right foot slightly lifted, left hand relaxed by side while right arm hangs loosely. head slightly turned to the right.\n\n\na mgm3004 cold white skin chinese female model,20-year-old,  long straight black hair,"], "(linrun2111:1.3), back view, angled slightly to the left,whole body,The model is wearing A black blouse featuring large yellow polka dots and yellow floral patterns evenly distributed across the fabric, with a loose fit, three-quarter length sleeves, and a round neckline. 20-year-old Chinese female  model, is wearing  mgs2222 dark blue straight-leg jeans underneath. 20-year-old Chinese female  model, is wearing  gold hoop earrings. 20-year-old Chinese female  model, is wearing mgs2222 white slingback heels with black straps. The  mgs2222 white slingback heels with black straps are made of leather. \n\n\nmgs2222,an urban street in front of a historic brick building with large glass windows, beige stone trim, and reflected cityscape; stone pavement and distant pedestrians. The natural daylight casts soft shadows on the ground extending to the left.walking down the street.walking away from the camera with left foot forward and right foot slightly lifted, left hand relaxed by side while right arm hangs loosely. head slightly turned to the right.\n\n\na mgm3004 cold white skin chinese female model,20-year-old,  long straight black hair,", "(linrun2111:1.3), back view, angled slightly to the left,whole body,The model is wearing A black blouse featuring large yellow polka dots and yellow floral patterns evenly distributed across the fabric, with a loose fit, three-quarter length sleeves, and a round neckline. 20-year-old Chinese female  model, is wearing  mgs2222 dark blue straight-leg jeans underneath. 20-year-old Chinese female  model, is wearing  gold hoop earrings. 20-year-old Chinese female  model, is wearing mgs2222 white slingback heels with black straps. The  mgs2222 white slingback heels with black straps are made of leather. \n\n\nmgs2222,an urban street in front of a historic brick building with large glass windows, beige stone trim, and reflected cityscape; stone pavement and distant pedestrians. The natural daylight casts soft shadows on the ground extending to the left.walking down the street.walking away from the camera with left foot forward and right foot slightly lifted, left hand relaxed by side while right arm hangs loosely. head slightly turned to the right.\n\n\na mgm3004 cold white skin chinese female model,20-year-old,  long straight black hair,", "(linrun2111:1.3), back view, angled slightly to the left,whole body,The model is wearing A black blouse featuring large yellow polka dots and yellow floral patterns evenly distributed across the fabric, with a loose fit, three-quarter length sleeves, and a round neckline. 20-year-old Chinese female  model, is wearing  mgs2222 dark blue straight-leg jeans underneath. 20-year-old Chinese female  model, is wearing  gold hoop earrings. 20-year-old Chinese female  model, is wearing mgs2222 white slingback heels with black straps. The  mgs2222 white slingback heels with black straps are made of leather. \n\n\nmgs2222,an urban street in front of a historic brick building with large glass windows, beige stone trim, and reflected cityscape; stone pavement and distant pedestrians. The natural daylight casts soft shadows on the ground extending to the left.walking down the street.walking away from the camera with left foot forward and right foot slightly lifted, left hand relaxed by side while right arm hangs loosely. head slightly turned to the right.\n\n\na mgm3004 cold white skin chinese female model,20-year-old,  long straight black hair,", "(linrun2111:1.3), front view, (full-body portrait:1.2), full body, The model is wearing A navy blue jacket top and the inner layer of the jacket is black and beige wide-leg pants set, The jacket is hip-length with a round neckline, long sleeves, and large silver buttons down the front without any visible logos or patterns. The fabric of this jacket appears to be lightweight and slightly sheer, with a subtle sheen that suggests it may be made from a silk blend or a fine synthetic material.  The pants are loose-fitting, with a wide leg cut and an ankle-length hem, made from a light, textured fabric. ,buttoned.20-year-old European female model, is wearing black sleeveless top inside. 20-year-old European female model, is wearing loafers. The  loafers are made of leather. \n\n\nA softly lit studio with neutral, diffused lighting that creates gentle shadows and a balanced, open atmosphere. The background is a soft, light beige or pale grey, with clean lines and minimalistic walls that subtly reflect the light. The light is directional but softly filtered, coming from one side and casting natural shadows on the walls and floor, enhancing the depth of the space. The overall effect is calm, elegant, and spacious, with a focus on creating a sense of openness and subtle contrast between light and shadow without overwhelming warmth.20-year-old European female model is walking with one hand in pocket and one hand naturally perpendicular to side.,\n\n\na mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,"]}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1565.364990234375, -882.087890625], "size": [210, 122], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 705, "type": "RepeatLatentBatch", "pos": [4417.4013671875, 607.6464233398438], "size": [210, 80.70018768310547], "flags": {}, "order": 107, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1276}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1242], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [1]}, {"id": 701, "type": "MaskUpscale_LR", "pos": [475.3071594238281, 486.9634094238281], "size": [320, 60], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1270, "label": "mask"}, {"name": "scale_by", "type": "FLOAT", "link": 1271, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1268], "slot_index": 0, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "widgets_values": [4]}, {"id": 699, "type": "Reroute", "pos": [838.0829467773438, 488.2565002441406], "size": [75, 26], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1268}], "outputs": [{"name": "", "type": "MASK", "links": [1232]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 706, "type": "Reroute", "pos": [2273.233642578125, 108.75343322753906], "size": [75, 26], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1277}], "outputs": [{"name": "", "type": "VAE", "links": [1230, 1262, 1267]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 687, "type": "ConditioningZeroOut", "pos": [1924.6767578125, 463.5101623535156], "size": [222.26809692382812, 26], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1248}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1264], "slot_index": 0}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 688, "type": "CLIPTextEncode", "pos": [1526.1217041015625, 431.4842834472656], "size": [285.6000061035156, 65.87068176269531], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 1249}, {"name": "text", "type": "STRING", "link": 1282, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1248, 1263], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 682, "type": "RandomNoise", "pos": [4116.41748046875, 270.2794494628906], "size": [277.9598693847656, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [1238], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [745052156325993, "fixed"]}, {"id": 683, "type": "DifferentialDiffusion", "pos": [3836.329345703125, 311.916015625], "size": [184.8000030517578, 26], "flags": {"collapsed": false}, "order": 67, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1397}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1226], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 677, "type": "Reroute", "pos": [-1125.8050537109375, 132.85696411132812], "size": [75, 26], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1392}], "outputs": [{"name": "", "type": "*", "links": [1256, 1274, 1319]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 455, "type": "Reroute", "pos": [-174.99717712402344, -462.8396301269531], "size": [75, 26], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1357}], "outputs": [{"name": "", "type": "IMAGE", "links": [789, 1392], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 680, "type": "Reroute", "pos": [1991.021484375, 338.81671142578125], "size": [75, 26], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1237}], "outputs": [{"name": "", "type": "IMAGE", "links": [1266]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 671, "type": "KSamplerSelect", "pos": [4114.82470703125, 494.41168212890625], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [1240], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler_ancestral"]}, {"id": 670, "type": "BasicGuider", "pos": [4160.22509765625, 399.0107116699219], "size": [161.1999969482422, 46], "flags": {}, "order": 106, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1226, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 1227}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [1239], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 673, "type": "FluxGuidance", "pos": [3823.9736328125, 438.120849609375], "size": [211.60000610351562, 58], "flags": {}, "order": 104, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 1233, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [1228], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [2]}, {"id": 728, "type": "Reroute", "pos": [5065.74462890625, 253.8061065673828], "size": [75, 26], "flags": {}, "order": 93, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1324}], "outputs": [{"name": "", "type": "IMAGE_BOUNDS", "links": [1326], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 727, "type": "Reroute", "pos": [5065.23486328125, 205.93362426757812], "size": [75, 26], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1321}], "outputs": [{"name": "", "type": "MASK", "links": [1323], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 726, "type": "Reroute", "pos": [5066.88232421875, 153.15419006347656], "size": [75, 26], "flags": {}, "order": 89, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1319}], "outputs": [{"name": "", "type": "*", "links": [1320], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 379, "type": "UNETLoader", "pos": [-1171.4366455078125, -1402.59912109375], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [662, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 272, "type": "UNETLoader", "pos": [-1189.7391357421875, -1156.2449951171875], "size": [315, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 647, 647, 944], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 373, "type": "JWStringToInteger", "pos": [-648.970703125, -1132.3956298828125], "size": [210, 58], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644, 644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 377, "type": "CR Model Input Switch", "pos": [-357.1766052246094, -1110.5040283203125], "size": [257.191650390625, 78.78076171875], "flags": {"collapsed": false}, "order": 52, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 944, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [946, 1395], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["1"]}, {"id": 386, "type": "Reroute", "pos": [-244.0059356689453, -919.148193359375], "size": [75, 26], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665, 1285, 1396], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [739.959716796875, -684.6459350585938], "size": [421.542236328125, 163.53213500976562], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 397, "type": "CLIPTextEncode", "pos": [4105.27880859375, -804.9226684570312], "size": [210, 96], "flags": {"collapsed": false}, "order": 55, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 955}, {"name": "text", "type": "STRING", "link": 788, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [715], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a 5 year girl"], "color": "#222", "bgcolor": "#000"}, {"id": 416, "type": "CheckpointLoaderSimple", "pos": [3702.845458984375, -854.5330810546875], "size": [314.34735107421875, 128.43458557128906], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1412], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [955, 956], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [674, 719, 1133], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sdxl/epicrealismXL_VXIAbeast4SLightning.safetensors"]}, {"id": 679, "type": "BasicScheduler", "pos": [4118.50439453125, 600.8508911132812], "size": [210, 106], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1398}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [1241], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", 20, 0.65]}, {"id": 475, "type": "LoadImage", "pos": [4102.89697265625, -2189.171630859375], "size": [268.51116943359375, 314], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [813], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["080c38bdd5404804a84577ca004d24ea.png", "image"]}, {"id": 476, "type": "LoadImage", "pos": [4437.705078125, -2194.097900390625], "size": [234.48504638671875, 314], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1003, 1007, 1051, 1064, 1089], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["594a79670a204dfe897520c682bb6511.png", "image"]}, {"id": 409, "type": "easy imageColorMatch", "pos": [5319.93359375, -383.676025390625], "size": [210, 102], "flags": {"collapsed": false}, "order": 125, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 989}, {"name": "image_target", "type": "IMAGE", "link": 1142}], "outputs": [{"name": "image", "type": "IMAGE", "links": [703], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageColorMatch"}, "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 752, "type": "ConrainPythonExecutor", "pos": [-748.3713989257812, 154.49908447265625], "size": [294.2134704589844, 566.1136474609375], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 1393, "shape": 7}, {"name": "any_b", "type": "*", "link": null, "shape": 7}, {"name": "any_c", "type": "*", "link": null, "shape": 7}, {"name": "any_d", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [1394], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["import cv2\nimport numpy as np\nimport torch\n\ndef filter_mask(mask):\n    \"\"\"\n    输入torch tensor格式的mask，形状为【通道，高度，宽度】或【高度，宽度】\n    计算连通域，返回只有最大连通域的mask，其他区域设置为0\n    \"\"\"\n    # 保存原始设备和数据类型\n    original_device = mask.device\n    original_dtype = mask.dtype\n    \n    # 转换为numpy处理\n    if mask.dim() == 3:  # 通道处理 [channel, height, width]\n        num_channels = mask.shape[0]\n        new_masks = []\n        \n        for i in range(num_channels):\n            single_mask = mask[i].cpu().numpy()\n            processed_mask = _process_single_mask(single_mask)\n            new_masks.append(torch.from_numpy(processed_mask))\n        \n        result = torch.stack(new_masks)\n    elif mask.dim() == 2:  # 单个mask [height, width]\n        single_mask = mask.cpu().numpy()\n        processed_mask = _process_single_mask(single_mask)\n        result = torch.from_numpy(processed_mask)\n    else:\n        raise ValueError(\"mask维度必须是2D或3D\")\n    \n    # 转换回原始设备和数据类型\n    return result.to(device=original_device, dtype=original_dtype)\n\ndef _process_single_mask(mask):\n    \"\"\"处理单个2D mask\"\"\"\n    # 数据预处理：确保正确的二值化\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        # 如果是浮点数，假设范围是0-1，转换为0-255\n        mask_normalized = (mask * 255).astype(np.uint8)\n    else:\n        mask_normalized = mask.astype(np.uint8)\n    \n    # 二值化处理，确保只有0和255\n    _, binary_mask = cv2.threshold(mask_normalized, 127, 255, cv2.THRESH_BINARY)\n    \n    # 形态学开运算，去除小噪声\n    kernel = np.ones((3,3), np.uint8)\n    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)\n    \n    # 计算连通域\n    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_mask)\n    \n    if num_labels <= 1:  # 只有背景，没有前景\n        return np.zeros_like(mask)\n    \n    # 获取最大的连通域（排除背景）\n    max_area_index = np.argmax(stats[1:, cv2.CC_STAT_AREA]) + 1\n    \n    # 创建新的mask，只有最大的连通域为1，其他为0\n    new_mask = (labels == max_area_index).astype(mask.dtype)\n    \n    # 如果原始数据是浮点型，将结果转换回0-1范围\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        new_mask = new_mask.astype(mask.dtype)\n    \n    return new_mask\n\ndef call(any_a, any_b, any_c, any_d):\n    new_mask = filter_mask(any_a)\n    if torch.all(new_mask == 0):\n        blank_size = 4\n        # 设置左上角2x2区域为1\n        if new_mask.dim() == 2:\n            new_mask[0:blank_size, 0:blank_size] = 1\n        else:  # 3D\n            new_mask[0, 0:blank_size, 0:blank_size] = 1\n    return [new_mask]"]}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [709.6905517578125, -1195.457763671875], "size": [499.25970458984375, 126], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 654, 1341], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [513, 510, 513, 630, 631], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/女士中式大龄开衫_19429_20250521_103121/女士中式大龄开衫_19429_20250521_103121-flux/女士中式大龄开衫_19429_20250521_103121-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [720.578369140625, -1607.893310546875], "size": [477.3377990722656, 128.31455993652344], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 946}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/Nalis_4567_20241215_204429/Nalis_4567_20241215_204429-flux/Nalis_4567_20241215_204429-flux.safetensors", 0.3, 0.3], "color": "#232", "bgcolor": "#353"}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [740.9517822265625, -978.5265502929688], "size": [400, 200], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [472, 473], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), front view, (full-body portrait:1.2), full body, The model is wearing A navy blue jacket top and the inner layer of the jacket is black and beige wide-leg pants set, The jacket is hip-length with a round neckline, long sleeves, and large silver buttons down the front without any visible logos or patterns. The fabric of this jacket appears to be lightweight and slightly sheer, with a subtle sheen that suggests it may be made from a silk blend or a fine synthetic material.  The pants are loose-fitting, with a wide leg cut and an ankle-length hem, made from a light, textured fabric. ,buttoned.20-year-old European female model, is wearing {black sleeveless top} inside. 20-year-old European female model, is wearing loafers. The  loafers are made of leather. \n\n\nA softly lit studio with neutral, diffused lighting that creates gentle shadows and a balanced, open atmosphere. The background is a soft, light beige or pale grey, with clean lines and minimalistic walls that subtly reflect the light. The light is directional but softly filtered, coming from one side and casting natural shadows on the walls and floor, enhancing the depth of the space. The overall effect is calm, elegant, and spacious, with a focus on creating a sense of openness and subtle contrast between light and shadow without overwhelming warmth.{20-year-old European female model stands casually with hands in his pockets and his legs bent.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model stood casually with hands hanging naturally at his sides.|20-year-old European female model is walking with one hand in pocket and one hand naturally perpendicular to side.|20-year-old European female model is walking with hands in his pockets.|20-year-old European female model is walking with hands hanging down naturally.},\n\n\na mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", 2048, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2354.7509765625, -1010.1593627929688], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [575, 786, 575, 1282], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["a mgm3004 creamy peach skin european female model,20-year-old,  long light brown wavy hair,", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 277, "type": "RandomNoise", "pos": [1829.1529541015625, -1579.895751953125], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": [42029499340386, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 399, "type": "OrderedFaceFilter", "pos": [3728.188232421875, -311.67803955078125], "size": [227.9144744873047, 169.93338012695312], "flags": {}, "order": 112, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 684}], "outputs": [{"name": "filtered", "type": "FACE", "links": [702, 720], "slot_index": 0}, {"name": "rest", "type": "FACE"}], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "widgets_values": ["area", "descending", 0, 1]}, {"id": 694, "type": "SetUnionControlNetType", "pos": [1843.809814453125, 148.7852020263672], "size": [336.6855163574219, 106.00154113769531], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "control_net", "type": "CONTROL_NET", "link": 1258}], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1265], "slot_index": 0}], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "widgets_values": ["canny/lineart/anime_lineart/mlsd"]}, {"id": 695, "type": "<PERSON><PERSON>", "pos": [1506.9503173828125, 275.8267822265625], "size": [222.01300048828125, 82], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1259}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1237], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [0.1, 0.5]}, {"id": 697, "type": "VAEDecode", "pos": [4894.6767578125, 366.17950439453125], "size": [210, 46], "flags": {}, "order": 109, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1261}, {"name": "vae", "type": "VAE", "link": 1262}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1338], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 698, "type": "ControlNetApplyAdvanced", "pos": [2378.658203125, 408.31219482421875], "size": [218.0561065673828, 186], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1263}, {"name": "negative", "type": "CONDITIONING", "link": 1264}, {"name": "control_net", "type": "CONTROL_NET", "link": 1265}, {"name": "image", "type": "IMAGE", "link": 1266}, {"name": "vae", "type": "VAE", "link": 1267, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1233], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1229], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.6, 0, 0.5]}, {"id": 672, "type": "InpaintModelConditioning", "pos": [3812.064453125, 589.8123168945312], "size": [216.59999084472656, 138], "flags": {}, "order": 105, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1228}, {"name": "negative", "type": "CONDITIONING", "link": 1229, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 1230}, {"name": "pixels", "type": "IMAGE", "link": 1231}, {"name": "mask", "type": "MASK", "link": 1232, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1227], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [1276], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 685, "type": "Reroute", "pos": [815.7013549804688, 195.89584350585938], "size": [75, 26], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1246}], "outputs": [{"name": "", "type": "IMAGE", "links": [1231, 1259], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 610, "type": "UltralyticsDetectorProvider", "pos": [3720.58740234375, -1361.011962890625], "size": [315, 78], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [1343], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 703, "type": "SapiensSampler", "pos": [-1135.94677734375, 325.4853210449219], "size": [315, 258], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "model", "type": "MODEL_SAPIEN", "link": 1273}, {"name": "image", "type": "IMAGE", "link": 1274}], "outputs": [{"name": "seg_img", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "depth_img", "type": "IMAGE", "links": [], "slot_index": 1}, {"name": "normal_img", "type": "IMAGE", "links": [], "slot_index": 2}, {"name": "pose_img", "type": "IMAGE", "links": [], "slot_index": 3}, {"name": "mask", "type": "MASK", "links": [1393], "slot_index": 4}], "properties": {"Node name for S&R": "SapiensSampler"}, "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 725, "type": "Bounded Image Blend with Mask", "pos": [5306.212890625, -197.30076599121094], "size": [247.87564086914062, 164.6544647216797], "flags": {}, "order": 128, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 1320}, {"name": "target_mask", "type": "MASK", "link": 1323}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 1326}, {"name": "source", "type": "IMAGE", "link": 1328}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1417], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 410, "type": "WarpFacesBack", "pos": [4934.3994140625, -200.19781494140625], "size": [182.46627807617188, 157.38844299316406], "flags": {}, "order": 126, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1337}, {"name": "face", "type": "FACE", "link": 702}, {"name": "crop", "type": "IMAGE", "link": 703}, {"name": "mask", "type": "MASK", "link": 1400}, {"name": "warp", "type": "WARP", "link": 705}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1300], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "WarpFacesBack"}, "widgets_values": []}, {"id": 456, "type": "Reroute", "pos": [5169.2236328125, -180.05690002441406], "size": [75, 26], "flags": {}, "order": 127, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1300}], "outputs": [{"name": "", "type": "IMAGE", "links": [1328], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 764, "type": "Reroute", "pos": [6341.591796875, -1057.5367431640625], "size": [75, 26], "flags": {}, "order": 130, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1425}], "outputs": [{"name": "", "type": "IMAGE", "links": [1415, 1416], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 201, "type": "String to Text", "pos": [6107.16748046875, -1419.36865234375], "size": [315, 58], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 200, "type": "String to Text", "pos": [6110.244140625, -1267.6646728515625], "size": [315, 58], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 391, "type": "ControlNetLoader", "pos": [4440.48388671875, -596.2993774414062], "size": [378.708740234375, 58], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [712], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 390, "type": "InstantIDFaceAnalysis", "pos": [4440.83056640625, -684.0112915039062], "size": [315, 58], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"name": "FACEANALYSIS", "type": "FACEANALYSIS", "links": [711], "slot_index": 0, "shape": 3, "label": "FACEANALYSIS"}], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["CUDA"]}, {"id": 389, "type": "InstantIDModelLoader", "pos": [4439.45947265625, -759.6692504882812], "size": [315, 58], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "INSTANTID", "type": "INSTANTID", "links": [710], "slot_index": 0, "shape": 3, "label": "INSTANTID"}], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": ["ip-adapter.bin"]}, {"id": 619, "type": "ControlNetLoader", "pos": [4360.033203125, -877.9781494140625], "size": [315, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [1126], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"]}, {"id": 412, "type": "ApplyInstantID", "pos": [4731.416015625, -842.5057373046875], "size": [210, 266], "flags": {}, "order": 120, "mode": 0, "inputs": [{"name": "instantid", "type": "INSTANTID", "link": 710, "slot_index": 0, "label": "instantid"}, {"name": "insightface", "type": "FACEANALYSIS", "link": 711, "slot_index": 1, "label": "insightface"}, {"name": "control_net", "type": "CONTROL_NET", "link": 712, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 713, "label": "image"}, {"name": "model", "type": "MODEL", "link": 1412, "slot_index": 4, "label": "model"}, {"name": "positive", "type": "CONDITIONING", "link": 715, "slot_index": 5, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 716, "slot_index": 6, "label": "negative"}, {"name": "image_kps", "type": "IMAGE", "link": 980, "shape": 7, "label": "image_kps"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [706], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "positive", "type": "CONDITIONING", "links": [1127], "slot_index": 1, "shape": 3, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "links": [1128], "slot_index": 2, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"groupcolor": "#444", "color": "rgba(0,0,0,.8)"}}, "widgets_values": [1, 0, 1]}, {"id": 411, "type": "K<PERSON><PERSON><PERSON>", "pos": [5275.3818359375, -849.4486694335938], "size": [261.8017578125, 262], "flags": {}, "order": 123, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 706}, {"name": "positive", "type": "CONDITIONING", "link": 1134}, {"name": "negative", "type": "CONDITIONING", "link": 1135}, {"name": "latent_image", "type": "LATENT", "link": 709}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [718], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42029499340386, "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.4]}, {"id": 413, "type": "VAEDecode", "pos": [5342.8876953125, -515.1910400390625], "size": [140, 46], "flags": {"collapsed": false}, "order": 124, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 718}, {"name": "vae", "type": "VAE", "link": 719}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1142], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 402, "type": "MaskFastGrow", "pos": [4642.54443359375, -196.25387573242188], "size": [210, 178], "flags": {}, "order": 118, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1388}], "outputs": [{"name": "MASK", "type": "MASK", "links": [676], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 16, 8, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 754, "type": "MaskFastGrow", "pos": [4546.5107421875, -470.543212890625], "size": [210, 178], "flags": {}, "order": 116, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 1399}], "outputs": [{"name": "MASK", "type": "MASK", "links": [1400], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFastGrow"}, "widgets_values": [false, 64, 32, 0, 1, true], "color": "#432", "bgcolor": "#653"}, {"id": 392, "type": "InpaintModelConditioning", "pos": [4979.54736328125, -550.9163208007812], "size": [210, 138], "flags": {}, "order": 122, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1136, "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "link": 1137}, {"name": "vae", "type": "VAE", "link": 674}, {"name": "pixels", "type": "IMAGE", "link": 979}, {"name": "mask", "type": "MASK", "link": 676}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1134], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": [1135], "slot_index": 1, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [709], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 618, "type": "ControlNetApplyAdvanced", "pos": [4981.6240234375, -833.2544555664062], "size": [243.52598571777344, 186], "flags": {}, "order": 121, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 1127}, {"name": "negative", "type": "CONDITIONING", "link": 1128}, {"name": "control_net", "type": "CONTROL_NET", "link": 1126}, {"name": "image", "type": "IMAGE", "link": 1132}, {"name": "vae", "type": "VAE", "link": 1133, "shape": 7}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [1136], "slot_index": 0}, {"name": "negative", "type": "CONDITIONING", "links": [1137], "slot_index": 1}], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "widgets_values": [0.1, 0, 0.5]}, {"id": 414, "type": "CropFaces", "pos": [4003.9951171875, -443.82769775390625], "size": [221.15121459960938, 146], "flags": {}, "order": 113, "mode": 0, "inputs": [{"name": "faces", "type": "FACE", "link": 720}], "outputs": [{"name": "crops", "type": "IMAGE", "links": [989, 1121, 1145], "slot_index": 0, "shape": 3}, {"name": "masks", "type": "MASK", "links": [1399], "slot_index": 1, "shape": 3}, {"name": "warps", "type": "WARP", "links": [705], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "CropFaces"}, "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 624, "type": "easy humanSegmentation", "pos": [3306.924560546875, -459.94635009765625], "size": [300, 500], "flags": {"collapsed": false}, "order": 115, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1145}], "outputs": [{"name": "image", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "mask", "type": "MASK", "links": [1388], "slot_index": 1}, {"name": "bbox", "type": "BBOX", "links": null, "slot_index": 2}], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 604, "type": "Reroute", "pos": [3485.7802734375, 114.76781463623047], "size": [75, 26], "flags": {}, "order": 110, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1338}], "outputs": [{"name": "", "type": "IMAGE", "links": [1336, 1337], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 570, "type": "ImpactSimpleDetectorSEGS", "pos": [4112.90771484375, -1405.44482421875], "size": [277.6741943359375, 319.97015380859375], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 1343}, {"name": "image", "type": "IMAGE", "link": 1093}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": null, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}], "outputs": [{"name": "SEGS", "type": "SEGS", "links": [1084], "slot_index": 0}], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 240, "type": "Note", "pos": [6988.8974609375, -2370.87353515625], "size": [260, 110], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 474, "type": "CR Image Input Switch", "pos": [4197.35693359375, -1760.59130859375], "size": [210, 74], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 813, "shape": 7}, {"name": "image2", "type": "IMAGE", "link": null, "shape": 7}, {"name": "Input", "type": "INT", "link": 1423, "widget": {"name": "Input"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [998, 1069], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Image Input Switch"}, "widgets_values": [2]}, {"id": 770, "type": "JWStringToInteger", "pos": [3746.0947265625, -1582.428466796875], "size": [210, 72.16617584228516], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1424], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 771, "type": "JWStringToInteger", "pos": [3744.010986328125, -1727.185791015625], "size": [210, 72.16617584228516], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [1423], "slot_index": 0}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 753, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2587.705810546875, -205.0255584716797], "size": [477.3377990722656, 128.31455993652344], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1395}, {"name": "clip", "type": "CLIP", "link": 1396}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1397, 1398], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/Nalis_4567_20241215_204429/Nalis_4567_20241215_204429-flux/Nalis_4567_20241215_204429-flux.safetensors", 1, 1], "color": "#232", "bgcolor": "#353"}, {"id": 462, "type": "LoadImage", "pos": [3709.57421875, -2161.04541015625], "size": [233.97132873535156, 314], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1001], "slot_index": 0}, {"name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["1ed0c076607c45ada29d19e5f53a74b7.png", "image"]}, {"id": 678, "type": "Sapiens<PERSON><PERSON>der", "pos": [4212.32373046875, -252.35496520996094], "size": [283.3903503417969, 298], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "MODEL_SAPIEN", "links": [1273], "slot_index": 0}], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 765, "type": "Reroute", "pos": [5718.21533203125, -167.27053833007812], "size": [75, 26], "flags": {}, "order": 129, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1417}], "outputs": [{"name": "", "type": "IMAGE", "links": [1425], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 768, "type": "Note", "pos": [5863.87744140625, -285.8473815917969], "size": [260, 110], "flags": {}, "order": 31, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["背面图不做换脸，只需要直出图"], "color": "#432", "bgcolor": "#653"}, {"id": 177, "type": "Text String", "pos": [6503.51904296875, -1381.0140380859375], "size": [315, 190], "flags": {}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250527/101161/303403", "product_2080950", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [713.581298828125, -1405.7806396484375], "size": [491.7470703125, 126], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["online_product/如缤场景测试_17775_20250514_171805/如缤场景测试_17775_20250514_171805-flux/如缤场景测试_17775_20250514_171805-flux.safetensors", 0.8, 1], "color": "#232", "bgcolor": "#353"}, {"id": 269, "type": "VAEDecode", "pos": [2576.850341796875, -1327.6995849609375], "size": [210, 46], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 1357, 1426], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 772, "type": "Reroute", "pos": [5718.18310546875, -337.20770263671875], "size": [75, 26], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 1426}], "outputs": [{"name": "", "type": "IMAGE", "links": null}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 378, "type": "ModelMergeFlux1", "pos": [-125.35467529296875, -630.95068359375], "size": [315, 1566], "flags": {"collapsed": true}, "order": 72, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 1341}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 236, "type": "CR Upscale Image", "pos": [6102.8974609375, -2119.87353515625], "size": [315, 222], "flags": {}, "order": 131, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1415, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 261, "type": "Image Size to Number", "pos": [6564.8974609375, -2276.87353515625], "size": [229.20001220703125, 126], "flags": {}, "order": 138, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 235, "type": "InspyrenetRembg", "pos": [6558.8974609375, -2050.87353515625], "size": [230, 90], "flags": {}, "order": 133, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 248, "type": "EmptyImage", "pos": [7013.8974609375, -2189.87353515625], "size": [231.5089111328125, 120.12616729736328], "flags": {"collapsed": false}, "order": 137, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [6999.8974609375, -1972.87353515625], "size": [252, 26], "flags": {}, "order": 135, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [7431.8974609375, -2096.87353515625], "size": [252, 146], "flags": {}, "order": 141, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [8009.8974609375, -2182.87353515625], "size": [220, 118], "flags": {}, "order": 140, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 263, "type": "ImageScaleBy", "pos": [8323.8857421875, -2294.87353515625], "size": [228.9691162109375, 78], "flags": {}, "order": 139, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [674, 416, 2, 392, 2, "VAE"], [676, 402, 0, 392, 4, "MASK"], [684, 436, 0, 399, 0, "FACE"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [813, 475, 0, 474, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [979, 549, 0, 392, 3, "IMAGE"], [980, 549, 0, 412, 7, "IMAGE"], [985, 550, 0, 549, 0, "*"], [989, 414, 0, 409, 0, "IMAGE"], [998, 474, 0, 559, 0, "IMAGE"], [1001, 462, 0, 559, 0, "IMAGE"], [1003, 476, 0, 559, 1, "IMAGE"], [1007, 476, 0, 559, 1, "IMAGE"], [1009, 559, 0, 561, 0, "IMAGE"], [1037, 579, 0, 576, 0, "IMAGE"], [1039, 574, 0, 580, 0, "MASK"], [1040, 580, 0, 576, 1, "MASK"], [1044, 477, 0, 559, 1, "IMAGE"], [1051, 476, 0, 559, 1, "IMAGE"], [1060, 477, 0, 559, 1, "IMAGE"], [1064, 476, 0, 559, 1, "IMAGE"], [1069, 474, 0, 559, 1, "IMAGE"], [1070, 477, 0, 559, 2, "IMAGE"], [1071, 447, 0, 446, 0, "FACE"], [1072, 576, 0, 592, 0, "IMAGE"], [1073, 592, 0, 447, 0, "IMAGE"], [1084, 570, 0, 597, 0, "SEGS"], [1086, 597, 0, 574, 0, "SEGS"], [1089, 476, 0, 477, 0, "IMAGE"], [1092, 561, 0, 579, 0, "*"], [1093, 561, 0, 570, 1, "IMAGE"], [1121, 414, 0, 616, 0, "*"], [1126, 619, 0, 618, 2, "CONTROL_NET"], [1127, 412, 1, 618, 0, "CONDITIONING"], [1128, 412, 2, 618, 1, "CONDITIONING"], [1132, 549, 0, 618, 3, "IMAGE"], [1133, 416, 2, 618, 4, "VAE"], [1134, 392, 0, 411, 1, "CONDITIONING"], [1135, 392, 1, 411, 2, "CONDITIONING"], [1136, 618, 0, 392, 0, "CONDITIONING"], [1137, 618, 1, 392, 1, "CONDITIONING"], [1142, 413, 0, 409, 1, "IMAGE"], [1145, 414, 0, 624, 0, "IMAGE"], [1226, 683, 0, 670, 0, "MODEL"], [1227, 672, 0, 670, 1, "CONDITIONING"], [1228, 673, 0, 672, 0, "CONDITIONING"], [1229, 698, 1, 672, 1, "CONDITIONING"], [1230, 706, 0, 672, 2, "VAE"], [1231, 685, 0, 672, 3, "IMAGE"], [1232, 699, 0, 672, 4, "MASK"], [1233, 698, 0, 673, 0, "CONDITIONING"], [1234, 692, 0, 675, 0, "*"], [1235, 693, 2, 676, 0, "MASK"], [1237, 695, 0, 680, 0, "*"], [1238, 682, 0, 681, 0, "NOISE"], [1239, 670, 0, 681, 1, "GUIDER"], [1240, 671, 0, 681, 2, "SAMPLER"], [1241, 679, 0, 681, 3, "SIGMAS"], [1242, 705, 0, 681, 4, "LATENT"], [1244, 693, 0, 684, 0, "IMAGE"], [1245, 675, 0, 684, 1, "FLOAT"], [1246, 684, 0, 685, 0, "*"], [1248, 688, 0, 687, 0, "CONDITIONING"], [1249, 689, 0, 688, 0, "CLIP"], [1255, 693, 0, 692, 0, "IMAGE"], [1256, 677, 0, 693, 0, "IMAGE"], [1258, 674, 0, 694, 0, "CONTROL_NET"], [1259, 685, 0, 695, 0, "IMAGE"], [1261, 681, 0, 697, 0, "LATENT"], [1262, 706, 0, 697, 1, "VAE"], [1263, 688, 0, 698, 0, "CONDITIONING"], [1264, 687, 0, 698, 1, "CONDITIONING"], [1265, 694, 0, 698, 2, "CONTROL_NET"], [1266, 680, 0, 698, 3, "IMAGE"], [1267, 706, 0, 698, 4, "VAE"], [1268, 701, 0, 699, 0, "*"], [1270, 676, 0, 701, 0, "MASK"], [1271, 675, 0, 701, 1, "FLOAT"], [1273, 678, 0, 703, 0, "MODEL_SAPIEN"], [1274, 677, 0, 703, 1, "IMAGE"], [1276, 672, 2, 705, 0, "LATENT"], [1277, 270, 0, 706, 0, "*"], [1282, 331, 0, 688, 1, "STRING"], [1285, 386, 0, 689, 0, "*"], [1300, 410, 0, 456, 0, "*"], [1319, 677, 0, 726, 0, "*"], [1320, 726, 0, 725, 0, "IMAGE"], [1321, 676, 0, 727, 0, "*"], [1323, 727, 0, 725, 1, "MASK"], [1324, 693, 1, 728, 0, "*"], [1326, 728, 0, 725, 2, "IMAGE_BOUNDS"], [1328, 456, 0, 725, 3, "IMAGE"], [1335, 616, 0, 550, 0, "*"], [1336, 604, 0, 436, 0, "IMAGE"], [1337, 604, 0, 410, 0, "IMAGE"], [1338, 697, 0, 604, 0, "*"], [1341, 296, 0, 378, 0, "MODEL"], [1343, 610, 0, 570, 0, "BBOX_DETECTOR"], [1357, 269, 0, 455, 0, "*"], [1388, 624, 1, 402, 0, "MASK"], [1392, 455, 0, 677, 0, "*"], [1393, 703, 4, 752, 0, "*"], [1394, 752, 0, 693, 1, "MASK"], [1395, 377, 0, 753, 0, "MODEL"], [1396, 386, 0, 753, 1, "CLIP"], [1397, 753, 0, 683, 0, "MODEL"], [1398, 753, 0, 679, 0, "MODEL"], [1399, 414, 1, 754, 0, "MASK"], [1400, 754, 0, 410, 3, "MASK"], [1412, 416, 0, 412, 4, "MODEL"], [1415, 764, 0, 236, 0, "IMAGE"], [1416, 764, 0, 365, 0, "IMAGE"], [1417, 725, 0, 765, 0, "*"], [1423, 771, 0, 474, 2, "INT"], [1424, 770, 0, 477, 2, "INT"], [1425, 765, 0, 764, 0, "*"], [1426, 269, 0, 772, 0, "*"]], "groups": [{"id": 3, "title": "换背景", "bounding": [5985.7255859375, -2473.57763671875, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [613.629638671875, -1717.02734375, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [5919.30859375, -1662.13818359375, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-1243.5155029296875, -1717.0443115234375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "换脸", "bounding": [3680.31640625, -960.367431640625, 1901.2747802734375, 953.2968139648438], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "人脸原图", "bounding": [3692.35400390625, -2275.424072265625, 1887.6405029296875, 1281.4771728515625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 17, "title": "Group", "bounding": [1157.142822265625, 17.07085609436035, 1472.12255859375, 704.690673828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 18, "title": "Group", "bounding": [-1236.62939453125, 5.20417594909668, 2190.035888671875, 714.1162109375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 19, "title": "Group", "bounding": [3657.97802734375, 92.7929916381836, 1952.30078125, 716.3968505859375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.15772225476662696, "offset": [2178.4748502650095, 3018.233424432685]}}, "version": 0.4, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "682": 0}}}}}
{"client_id": "432be54683be47c2ab8d2bbfde47401b", "prompt": {"152": {"inputs": {"image": "prod_fix_8a53848920cb4d239d8c6ddf17952c75.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "导入人脸图片"}}, "177": {"inputs": {"text": "product/20250412/100774/187921", "text_b": "product_1508044", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "185": {"inputs": {"delimiter": " ", "clean_whitespace": "true", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "200": {"inputs": {"string": "【negative】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "201": {"inputs": {"string": "【positive】:"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "209": {"inputs": {"swap_model": "inswapper_128.onnx", "facedetection_model": "retinaface_resnet50", "face_restore_model": "GFPGANv1.4.pth", "parse_model": "parsenet"}, "class_type": "LoadConrainReactorModels", "_meta": {"title": "导入换脸模型"}}, "210": {"inputs": {"enabled": true, "face_restore_visibility": "0.7", "codeformer_weight": "0.7", "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "keep_largest": "yes", "input_image": ["299", 0], "swap_model": ["209", 0], "facedetection": ["209", 1], "face_restore_model": ["209", 2], "faceparse_model": ["209", 3], "source_image": ["152", 0]}, "class_type": "ConrainReActorFaceSwap", "_meta": {"title": "换脸"}}, "216": {"inputs": {"output_path": ["177", 0], "filename_prefix": ["177", 1], "extension": "png", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["355", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "232": {"inputs": {"delimiter": "/", "clean_whitespace": "false", "text_a": ["233", 0], "text_b": ["177", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "233": {"inputs": {"string": "output"}, "class_type": "String to Text", "_meta": {"title": "String to Text"}}, "235": {"inputs": {"torchscript_jit": "default", "image": ["236", 0]}, "class_type": "InspyrenetRembg", "_meta": {"title": "Inspyrenet Rembg"}}, "236": {"inputs": {"upscale_model": "4xUltrasharp_4xUltrasharpV10.pt", "mode": "rescale", "rescale_factor": 2, "resize_width": "1785", "resampling_method": "lanc<PERSON>s", "supersample": "true", "rounding_modulus": 8, "image": ["210", 0]}, "class_type": "CR Upscale Image", "_meta": {"title": "🔍 CR Upscale Image"}}, "248": {"inputs": {"width": ["261", 4], "height": ["261", 5], "batch_size": 1, "color": ""}, "class_type": "EmptyImage", "_meta": {"title": "EmptyImage"}}, "258": {"inputs": {"image": ["235", 0]}, "class_type": "ImageRGBA2RGB", "_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}}, "261": {"inputs": {"image": ["236", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "263": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["266", 0], "image": ["316", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "266": {"inputs": {"target_size": ["367", 0], "image": ["316", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "268": {"inputs": {"text": ["283", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "269": {"inputs": {"samples": ["273", 0], "vae": ["270", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "270": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "271": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "272": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "273": {"inputs": {"noise": ["277", 0], "guider": ["276", 0], "sampler": ["274", 0], "sigmas": ["275", 0], "latent_image": ["279", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "274": {"inputs": {"sampler_name": "dpmpp_2m"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "275": {"inputs": {"scheduler": "sgm_uniform", "steps": "20", "denoise": 1, "model": ["280", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "276": {"inputs": {"model": ["280", 0], "conditioning": ["278", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "277": {"inputs": {"noise_seed": "847705127442094"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "278": {"inputs": {"guidance": "3.5", "conditioning": ["268", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"width": ["352", 0], "height": ["354", 0], "batch_size": "1"}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "280": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["352", 0], "height": ["354", 0], "model": ["378", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "282": {"inputs": {"prompts": "(linrun2111:1.3), front view,upper body,A half-body photo of a model wearing A white t-shirt, The t-shirt has a logo patch on the left sleeve.A portrait of a person's upper body, no lower body visible,The model is wearing {oversized loose high-waisted ash gray multi-pocket denim wide-leg flared pants,uniform style in each picture} underneath. The model is wearing {none}. The model is  holding {none}. \n\n\nmgs2222,a plain white background.standing casually.standing with one hand touching the side of the face and the other hand in the pocket.\n\n\nA 23-year-old young European male model,The model had short, handsome brown hair,", "seed": "474270042766012"}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "正向提示词"}}, "283": {"inputs": {"text": ["282", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "284": {"inputs": {"seed": "1785"}, "class_type": "CR Seed", "_meta": {"title": "height"}}, "285": {"inputs": {"seed": "1340"}, "class_type": "CR Seed", "_meta": {"title": "width"}}, "286": {"inputs": {"prompts": "(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", "seed": 1033}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "负向提示词"}}, "296": {"inputs": {"lora_name": "product/短袖款1-白-黑-卡其_12160_20250410_160909/短袖款1-白-黑-卡其_12160_20250410_160909-flux/短袖款1-白-黑-卡其_12160_20250410_160909-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["298", 0], "clip": ["298", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "服装lora"}}, "297": {"inputs": {"lora_name": "product/润辰模特定制_新_11087_20250331_180902/润辰模特定制_新_11087_20250331_180902-flux/润辰模特定制_新_11087_20250331_180902-flux.safetensors", "strength_model": "0.6", "strength_clip": "0.6", "model": ["377", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "298": {"inputs": {"lora_name": "product/润辰上衣场景_10898_20250329_120231/润辰上衣场景_10898_20250329_120231-flux/润辰上衣场景_10898_20250329_120231-flux.safetensors", "strength_model": "0.8", "strength_clip": 1, "model": ["297", 0], "clip": ["297", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "风格lora"}}, "299": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": "946862898905107", "steps": 10, "cfg": "1.5", "sampler_name": "euler", "scheduler": "beta", "denoise": "0.55", "feather": 3, "noise_mask": true, "force_inpaint": false, "bbox_threshold": 0.5, "bbox_dilation": 2, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "refiner_ratio": 0.2, "cycle": 1, "inpaint_model": 0, "noise_mask_feather": false, "image": ["328", 0], "detailer_pipe": ["300", 0]}, "class_type": "FaceDetailerPipe", "_meta": {"title": "FaceDetailer (pipe)"}}, "300": {"inputs": {"wildcard": "", "Select to add LoRA": "Select the LoRA to add to the text", "Select to add Wildcard": "Select the Wildcard to add to the text", "basic_pipe": ["301", 0], "bbox_detector": ["303", 0]}, "class_type": "BasicPipeToDetailerPipe", "_meta": {"title": "BasicPipe -> DetailerPipe"}}, "301": {"inputs": {"model": ["380", 0], "clip": ["296", 1], "vae": ["270", 0], "positive": ["302", 0], "negative": ["304", 0]}, "class_type": "ToBasicPipe", "_meta": {"title": "ToBasicPipe"}}, "302": {"inputs": {"text": ["331", 0], "clip": ["296", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "修脸prompt"}}, "303": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "304": {"inputs": {}, "class_type": "ImpactNegativeConditioningPlaceholder", "_meta": {"title": "Negative Cond Placeholder"}}, "316": {"inputs": {"x": 0, "y": 0, "resize_source": false, "source": ["258", 0], "destination": ["248", 0], "mask": ["235", 1]}, "class_type": "ConrainImageCompositeMasked", "_meta": {"title": "conrain image composite masked"}}, "320": {"inputs": {"text": ["185", 0], "path": ["232", 0], "filename": ["177", 1]}, "class_type": "ConrainTextSave", "_meta": {"title": "conrain save text"}}, "324": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "325": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "Prefer GPU"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "326": {"inputs": {"text": ["331", 0], "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["329", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "327": {"inputs": {"text": "EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "token_normalization": "none", "weight_interpretation": "A1111", "clip": ["329", 1]}, "class_type": "BNK_CLIPTextEncodeAdvanced", "_meta": {"title": "CLIP Text Encode (Advanced)"}}, "328": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 512, "seed": "946862898905107", "steps": 8, "cfg": "3", "sampler_name": "euler", "scheduler": "normal", "denoise": 0.4, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 500, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 40, "wildcard": "", "cycle": 1, "inpaint_model": 1, "noise_mask_feather": 0, "image": ["269", 0], "model": ["329", 0], "clip": ["329", 1], "vae": ["329", 2], "positive": ["326", 0], "negative": ["327", 0], "bbox_detector": ["324", 0], "sam_model_opt": ["325", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "329": {"inputs": {"ckpt_name": "真实系：majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "331": {"inputs": {"prompts": "A 23-year-old young European male model,A 23-year-old young European male model,The model had short, handsome brown hair,", "seed": 1335}, "class_type": "ConrainRandomPrompts", "_meta": {"title": "修脸提示词"}}, "349": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["285", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "350": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]", "any_a": ["284", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "351": {"inputs": {"any_a": ["349", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "352": {"inputs": {"text": ["351", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "353": {"inputs": {"any_a": ["350", 0]}, "class_type": "ConrainAnyToStrings", "_meta": {"title": "conrain any to strings"}}, "354": {"inputs": {"text": ["353", 0]}, "class_type": "JWStringToInteger", "_meta": {"title": "String to Integer"}}, "355": {"inputs": {"width": ["285", 0], "height": ["284", 0], "x": 0, "y": 0, "image": ["263", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "365": {"inputs": {"image": ["210", 0]}, "class_type": "Image Size to Number", "_meta": {"title": "Image Size to Number"}}, "367": {"inputs": {"a": ["365", 4], "b": ["365", 5]}, "class_type": "JWIntegerMax", "_meta": {"title": "Integer Maximum"}}, "369": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["374", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "371": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["369", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "372": {"inputs": {"Input": 2, "model1": ["371", 0], "model2": ["369", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "373": {"inputs": {"text": "1"}, "class_type": "JWStringToInteger", "_meta": {"title": "推理加速开关"}}, "374": {"inputs": {"lora_name": "product/润辰模特定制_新_11087_20250331_180902/润辰模特定制_新_11087_20250331_180902-flux/润辰模特定制_新_11087_20250331_180902-flux.safetensors", "strength_model": "1", "strength_clip": 1, "model": ["272", 0], "clip": ["271", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "人脸lora"}}, "375": {"inputs": {"object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2, "end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0]}, "class_type": "ApplyFBCacheOnModel", "_meta": {"title": "Apply First Block Cache"}}, "377": {"inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "378": {"inputs": {"img_in.": 1, "time_in.": 1, "guidance_in": 1, "vector_in.": 1, "txt_in.": 1, "double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.2.": 1, "single_blocks.3.": 1, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "final_layer.": 1, "model1": ["296", 0], "model2": ["379", 0]}, "class_type": "ModelMergeFlux1", "_meta": {"title": "合并PW和flux模型"}}, "379": {"inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "PW模型"}}, "380": {"inputs": {"Input": ["381", 0], "model1": ["372", 0], "model2": ["296", 0]}, "class_type": "CR Model Input Switch", "_meta": {"title": "🔀 CR Model Input Switch"}}, "381": {"inputs": {"text": "2"}, "class_type": "JWStringToInteger", "_meta": {"title": "模特后置开关"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 386, "last_link_id": 665, "nodes": [{"id": 152, "type": "LoadImage", "pos": [4503, -965], "size": [320, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [346], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "shape": 3, "label": "MASK"}], "title": "导入人脸图片", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["prod_fix_8a53848920cb4d239d8c6ddf17952c75.jpg", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 177, "type": "Text String", "pos": [6215.19775390625, -780.4785766601562], "size": [315, 190], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [392, 394], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [386, 539], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["product/20250412/100774/187921", "product_1508044", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 185, "type": "Text Concatenate", "pos": [7342.19775390625, -634.4783935546875], "size": [315, 178], "flags": {}, "order": 49, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 319, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 475, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "link": 318, "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "link": 476, "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [540], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "type": "String to Text", "pos": [5842.15087890625, -567.2930297851562], "size": [315, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [318], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【negative】:"]}, {"id": 201, "type": "String to Text", "pos": [5820.15087890625, -761.2932739257812], "size": [315, 58], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [319], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["【positive】:"]}, {"id": 209, "type": "LoadConrainReactorModels", "pos": [4492, -1416], "size": [324.5391845703125, 190], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "faceswapper_model", "type": "FACE_MODEL", "links": [342], "slot_index": 0, "shape": 3, "label": "faceswapper_model"}, {"name": "facedetection_model", "type": "FACE_MODEL", "links": [343], "slot_index": 1, "shape": 3, "label": "facedetection_model"}, {"name": "facerestore_model", "type": "FACE_MODEL", "links": [344], "slot_index": 2, "shape": 3, "label": "facerestore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "links": [345], "slot_index": 3, "shape": 3, "label": "faceparse_model"}], "title": "导入换脸模型", "properties": {"Node name for S&R": "LoadConrainReactorModels"}, "widgets_values": ["inswapper_128.onnx", "retinaface_resnet50", "GFPGANv1.4.pth", "parsenet"], "color": "#494949", "bgcolor": "#353535"}, {"id": 210, "type": "ConrainReActorFaceSwap", "pos": [5031, -1425], "size": [367.79998779296875, 370], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "input_image", "type": "IMAGE", "link": 517, "label": "input_image"}, {"name": "swap_model", "type": "FACE_MODEL", "link": 342, "label": "swap_model"}, {"name": "facedetection", "type": "FACE_MODEL", "link": 343, "label": "facedetection"}, {"name": "face_restore_model", "type": "FACE_MODEL", "link": 344, "label": "face_restore_model"}, {"name": "faceparse_model", "type": "FACE_MODEL", "link": 345, "label": "faceparse_model"}, {"name": "source_image", "type": "IMAGE", "link": 346, "shape": 7, "label": "source_image"}, {"name": "face_model", "type": "FACE_MODEL", "shape": 7, "label": "face_model"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [516, 612], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "FACE_MODEL", "type": "FACE_MODEL", "shape": 3, "label": "FACE_MODEL"}], "title": "换脸", "properties": {"Node name for S&R": "ConrainReActorFaceSwap"}, "widgets_values": [true, "0.7", "0.7", "no", "no", "0", "0", 1, "yes"], "color": "#494949", "bgcolor": "#353535"}, {"id": 216, "type": "ConrainImageSave", "pos": [7899, -940], "size": [320, 266], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 603, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 392, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 386, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "png", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "type": "Text Concatenate", "pos": [6697.27783203125, -813.4215087890625], "size": [250, 142], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "text_a", "type": "STRING", "link": 395, "widget": {"name": "text_a"}, "label": "text_a"}, {"name": "text_b", "type": "STRING", "link": 394, "widget": {"name": "text_b"}, "label": "text_b"}, {"name": "text_c", "type": "STRING", "widget": {"name": "text_c"}, "label": "text_c"}, {"name": "text_d", "type": "STRING", "widget": {"name": "text_d"}, "label": "text_d"}], "outputs": [{"name": "STRING", "type": "STRING", "links": [541], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "Text Concatenate"}, "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "type": "String to Text", "pos": [6208.19775390625, -909.4784545898438], "size": [315, 58], "flags": {"collapsed": false}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [395], "slot_index": 0, "shape": 3, "label": "STRING"}], "properties": {"Node name for S&R": "String to Text"}, "widgets_values": ["output"]}, {"id": 235, "type": "InspyrenetRembg", "pos": [6200, -1323], "size": [230, 90], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 396, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [432], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [531], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InspyrenetRembg"}, "widgets_values": ["default"], "color": "#474747", "bgcolor": "#333333"}, {"id": 236, "type": "CR Upscale Image", "pos": [5706, -1450], "size": [315, 222], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 516, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [396, 402], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "show_help", "type": "STRING", "shape": 3, "label": "show_help"}], "properties": {"Node name for S&R": "CR Upscale Image"}, "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "1785", "lanc<PERSON>s", "true", 8], "color": "#474747", "bgcolor": "#333333"}, {"id": 240, "type": "Note", "pos": [6630, -1643], "size": [260, 110], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"], "color": "#432", "bgcolor": "#653"}, {"id": 248, "type": "EmptyImage", "pos": [6655, -1462], "size": [231.5089111328125, 120.12616729736328], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 442, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 443, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [529], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, ""], "color": "#474747", "bgcolor": "#333333"}, {"id": 258, "type": "ImageRGBA2RGB", "pos": [6641, -1245], "size": [252, 26], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 432, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [530], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 268, "type": "CLIPTextEncode", "pos": [1356.314453125, -1442.6668701171875], "size": [285.6000061035156, 54], "flags": {}, "order": 62, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 513}, {"name": "text", "type": "STRING", "link": 453, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [464], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["linrun2111, white background, full body, front view,"], "color": "#494949", "bgcolor": "#353535"}, {"id": 269, "type": "VAEDecode", "pos": [2618, -1436], "size": [210, 46], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 596}, {"name": "vae", "type": "VAE", "link": 455}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [611, 611], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 270, "type": "VAELoader", "pos": [2319.339111328125, -1210.8780517578125], "size": [247.6494903564453, 64.26640319824219], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [455, 520], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 271, "type": "DualCLIPLoader", "pos": [-974.3479614257812, -943.7987060546875], "size": [315, 106], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [622, 639, 664], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "type": "UNETLoader", "pos": [-969.5924682617188, -1139.4793701171875], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [621, 638, 645, 647], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 273, "type": "SamplerCustomAdvanced", "pos": [2306.54931640625, -1559.1309814453125], "size": [236.8000030517578, 112.51068878173828], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 456, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 457, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 458, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 459, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 460, "slot_index": 4}], "outputs": [{"name": "output", "type": "LATENT", "links": [596], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 274, "type": "KSamplerSelect", "pos": [2001, -1191.7747802734375], "size": [210, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [458], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["dpmpp_2m"]}, {"id": 275, "type": "BasicScheduler", "pos": [1992.************, -1071.4217529296875], "size": [210, 106], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 461, "slot_index": 0}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [459], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["sgm_uniform", "20", 1]}, {"id": 276, "type": "BasicGuider", "pos": [1984.70556640625, -1433.122314453125], "size": [161.1999969482422, 46], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 462, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 463, "slot_index": 1}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [457], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 277, "type": "RandomNoise", "pos": [1854.52099609375, -1600.************], "size": [317.5343933105469, 84.33126831054688], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [456], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["847705127442094", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 278, "type": "FluxGuidance", "pos": [1690.1234130859375, -1421.2938232421875], "size": [211.60000610351562, 58], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 464}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [463], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": ["3.5"], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "EmptySD3LatentImage", "pos": [1709.2464599609375, -1186.37353515625], "size": [210, 86.50716400146484], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 595, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 594, "widget": {"name": "height"}}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [460], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": ["1340", "1785", "1"], "color": "#232", "bgcolor": "#353"}, {"id": 280, "type": "ModelSamplingFlux", "pos": [1692.************, -937.4694213867188], "size": [210, 122], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 651, "slot_index": 0}, {"name": "width", "type": "INT", "link": 593, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 592, "widget": {"name": "height"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [461, 462], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "widgets_values": [1.15, 0.5, "1340", "1785"]}, {"id": 282, "type": "ConrainRandomPrompts", "pos": [779.6383056640625, -1020.9454956054688], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [472, 473], "slot_index": 0, "shape": 3}], "title": "正向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(linrun2111:1.3), front view,upper body,A half-body photo of a model wearing A white t-shirt, The t-shirt has a logo patch on the left sleeve.A portrait of a person's upper body, no lower body visible,The model is wearing {oversized loose high-waisted ash gray multi-pocket denim wide-leg flared pants,uniform style in each picture} underneath. The model is wearing {none}. The model is  holding {none}. \n\n\nmgs2222,a plain white background.standing casually.standing with one hand touching the side of the face and the other hand in the pocket.\n\n\nA 23-year-old young European male model,The model had short, handsome brown hair,", "474270042766012", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 283, "type": "ShowText|pysssss", "pos": [1304.6009521484375, -1079.596923828125], "size": [256.63372802734375, 226], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 472, "widget": {"name": "text"}}], "outputs": [{"name": "STRING", "type": "STRING", "links": [453], "slot_index": 0, "shape": 6}], "properties": {"Node name for S&R": "ShowText|pysssss"}, "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A deep grey t-shirt, The t-shirt has a round neckline and short sleeves.The model is wearing  black wide-leg pants underneath. The model is wearing black chunky shoes. The black chunky shoes have a fashionable style. \n\n\nlinrun2010,a model stands against a plain white background with even lighting.standing still.standing with legs slightly apart, left hand in pocket, right hand relaxed by side.\n\n\nThis is a 23 year old European female model,The model wore lipstick the color of bean paste, sweet and cute face,smile,The model has natural orange-brown long wavy hair, voluminous and layered."]}, {"id": 284, "type": "CR Seed", "pos": [1336.303466796875, -556.4685668945312], "size": [243.4204864501953, 102], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [587, 589], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "height", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": ["1785", "fixed"]}, {"id": 285, "type": "CR Seed", "pos": [1322.303466796875, -742.4697875976562], "size": [243.4204864501953, 102], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [590, 591], "slot_index": 0, "shape": 3}, {"name": "show_help", "type": "STRING", "shape": 3}], "title": "width", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": ["1340", "fixed"]}, {"id": 286, "type": "ConrainRandomPrompts", "pos": [789.3619995117188, -736.4147338867188], "size": [411.6590881347656, 124], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [474], "slot_index": 0, "shape": 3}], "title": "负向提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 287, "type": "Reroute", "pos": [2526.195556640625, -891.2860107421875], "size": [75, 26], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 473, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [475], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 288, "type": "Reroute", "pos": [2491.195556640625, -760.2861328125], "size": [75, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 474, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "STRING", "links": [476], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 296, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [749.9860229492188, -1247.851806640625], "size": [499.25970458984375, 126], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 499}, {"name": "clip", "type": "CLIP", "link": 500}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [514, 650, 654], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [513, 510, 513, 630, 631], "slot_index": 1, "shape": 3}], "title": "服装lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/短袖款1-白-黑-卡其_12160_20250410_160909/短袖款1-白-黑-卡其_12160_20250410_160909-flux/短袖款1-白-黑-卡其_12160_20250410_160909-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 297, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [754.6295166015625, -1641.997314453125], "size": [489.4413757324219, 126], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 646}, {"name": "clip", "type": "CLIP", "link": 665}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [627], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [628, 625], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/润辰模特定制_新_11087_20250331_180902/润辰模特定制_新_11087_20250331_180902-flux/润辰模特定制_新_11087_20250331_180902-flux.safetensors", "0.6", "0.6"], "color": "#232", "bgcolor": "#353"}, {"id": 298, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [754.8997192382812, -1445.91943359375], "size": [491.7470703125, 126], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 627}, {"name": "clip", "type": "CLIP", "link": 628}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [499], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [500], "slot_index": 1, "shape": 3}], "title": "风格lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/润辰上衣场景_10898_20250329_120231/润辰上衣场景_10898_20250329_120231-flux/润辰上衣场景_10898_20250329_120231-flux.safetensors", "0.8", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 299, "type": "FaceDetailerPipe", "pos": [3954.708984375, -1634.5810546875], "size": [346, 782], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 557}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "link": 503}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [517], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "shape": 6}, {"name": "mask", "type": "MASK", "links": [], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "shape": 6}], "properties": {"Node name for S&R": "FaceDetailerPipe"}, "widgets_values": [512, true, 1024, "946862898905107", "fixed", 10, "1.5", "euler", "beta", "0.55", 3, true, false, 0.5, 2, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, 0.2, 1, 0, false]}, {"id": 300, "type": "BasicPipeToDetailerPipe", "pos": [3642, -1629], "size": [262, 204.4281768798828], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "link": 504}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 505}, {"name": "sam_model_opt", "type": "SAM_MODEL", "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7}], "outputs": [{"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": [503], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "BasicPipeToDetailerPipe"}, "widgets_values": ["", "Select the LoRA to add to the text", "Select the Wildcard to add to the text"]}, {"id": 301, "type": "ToBasicPipe", "pos": [3341, -1629], "size": [241.79998779296875, 106], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 661}, {"name": "clip", "type": "CLIP", "link": 630}, {"name": "vae", "type": "VAE", "link": 520}, {"name": "positive", "type": "CONDITIONING", "link": 508}, {"name": "negative", "type": "CONDITIONING", "link": 509}], "outputs": [{"name": "basic_pipe", "type": "BASIC_PIPE", "links": [504], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ToBasicPipe"}, "widgets_values": []}, {"id": 302, "type": "CLIPTextEncode", "pos": [3033, -1410], "size": [210, 116.85224914550781], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 631}, {"name": "text", "type": "STRING", "link": 554, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0}], "title": "修脸prompt", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A 23-year-old young European male model,"], "color": "#232", "bgcolor": "#353"}, {"id": 303, "type": "UltralyticsDetectorProvider", "pos": [3316, -1018], "size": [226.8000030517578, 78], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [505], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 304, "type": "ImpactNegativeConditioningPlaceholder", "pos": [3014, -1119], "size": [210, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [509], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImpactNegativeConditioningPlaceholder"}, "widgets_values": [], "color": "#494949", "bgcolor": "#353535"}, {"id": 306, "type": "Note", "pos": [3314, -1446], "size": [210, 91.33761596679688], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["人脸lora节点\n单张图人脸流程：关闭\nlora人脸流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 307, "type": "Note", "pos": [1326.650146484375, -1662.**********], "size": [210, 91.33761596679688], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["去模糊lora节点\n纯色背景流程：关闭\n其他流程：打开"], "color": "#322", "bgcolor": "#533"}, {"id": 308, "type": "Note", "pos": [4546.26318359375, -1586.704345703125], "size": [210, 91.33761596679688], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入换脸节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 309, "type": "Note", "pos": [5000.97509765625, -1611.552001953125], "size": [210, 91.33761596679688], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换脸\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 310, "type": "Note", "pos": [5739.2734375, -1633.447998046875], "size": [210, 91.33761596679688], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["换背景所有节点\n纯色背景流程：打开\n其他流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 316, "type": "ConrainImageCompositeMasked", "pos": [7073, -1369], "size": [252, 146], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "source", "type": "IMAGE", "link": 530}, {"name": "destination", "type": "IMAGE", "link": 529}, {"name": "mask", "type": "MASK", "link": 531, "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [532, 533], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "widgets_values": [0, 0, false]}, {"id": 319, "type": "Note", "pos": [4536.26318359375, -1155.704345703125], "size": [210, 91.33761596679688], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["导入人脸图片节点\n单张图人脸且非背面流程：打开\nlora人脸或背面流程：关闭"], "color": "#322", "bgcolor": "#533"}, {"id": 320, "type": "ConrainTextSave", "pos": [7913.19775390625, -562.4788208007812], "size": [315, 106], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 540, "widget": {"name": "text"}}, {"name": "path", "type": "STRING", "link": 541, "widget": {"name": "path"}}, {"name": "filename", "type": "STRING", "link": 539, "widget": {"name": "filename"}}], "outputs": [], "properties": {"Node name for S&R": "ConrainTextSave"}, "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 324, "type": "UltralyticsDetectorProvider", "pos": [3061, 99], "size": [315, 78], "flags": {}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [552], "slot_index": 0, "shape": 3, "label": "BBOX_DETECTOR"}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": [], "slot_index": 1, "shape": 3, "label": "SEGM_DETECTOR"}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 325, "type": "SAMLoader", "pos": [3094, 253], "size": [315, 82], "flags": {}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [553], "slot_index": 0, "shape": 3, "label": "SAM_MODEL"}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "Prefer GPU"]}, {"id": 326, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [3039, -427], "size": [389.95330810546875, 157.71157836914062], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 545, "label": "clip"}, {"name": "text", "type": "STRING", "link": 555, "widget": {"name": "text"}}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [550], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["", "none", "A1111"], "color": "#232", "bgcolor": "#353"}, {"id": 327, "type": "BNK_CLIPTextEncodeAdvanced", "pos": [3061, -177], "size": [400, 200], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 546, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [551], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BNK_CLIPTextEncodeAdvanced"}, "widgets_values": ["EasyNegative,badhandv4,(worst quality, low quality:1.4),normal quality,bad anatomy,BadNegAnatomyV1-neg,bad face, low quality lowres bad face, low quality lowres fused face, low quality lowres poorly drawn face, low quality lowres cloned face, low quality lowres big face, low quality lowres long face, low quality lowres bad eyes, low quality lowres fused eyes poorly drawn eyes, low quality lowres extra eyes,bad hands,3hands,4hands,2humans,no humans,3humans, bad feet,", "none", "A1111"]}, {"id": 328, "type": "FaceDetailer", "pos": [3800, -600], "size": [350.5302734375, 902.3991088867188], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 611, "label": "image"}, {"name": "model", "type": "MODEL", "link": 547, "label": "model"}, {"name": "clip", "type": "CLIP", "link": 548, "label": "clip"}, {"name": "vae", "type": "VAE", "link": 549, "label": "vae"}, {"name": "positive", "type": "CONDITIONING", "link": 550, "label": "positive"}, {"name": "negative", "type": "CONDITIONING", "link": 551, "slot_index": 5, "label": "negative"}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 552, "slot_index": 6, "label": "bbox_detector"}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 553, "slot_index": 7, "shape": 7, "label": "sam_model_opt"}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "slot_index": 8, "shape": 7, "label": "segm_detector_opt"}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "shape": 7, "label": "detailer_hook"}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [557], "slot_index": 0, "shape": 3, "label": "image"}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6, "label": "cropped_refined"}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [], "slot_index": 2, "shape": 6, "label": "cropped_enhanced_alpha"}, {"name": "mask", "type": "MASK", "shape": 3, "label": "mask"}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "shape": 3, "label": "detailer_pipe"}, {"name": "cnet_images", "type": "IMAGE", "shape": 6, "label": "cnet_images"}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [384, true, 512, "946862898905107", "fixed", 8, "3", "euler", "normal", 0.4, 5, true, true, 0.5, 500, 3, "center-1", 0, 0.93, 0, 0.7, "False", 40, "", 1, 1, 0]}, {"id": 329, "type": "CheckpointLoaderSimple", "pos": [3033, -615], "size": [315, 98], "flags": {}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [547], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "CLIP", "links": [545, 546, 548], "slot_index": 1, "shape": 3, "label": "CLIP"}, {"name": "VAE", "type": "VAE", "links": [549], "slot_index": 2, "shape": 3, "label": "VAE"}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["真实系：majicmixRealistic_v7.safetensors"]}, {"id": 331, "type": "ConrainRandomPrompts", "pos": [2518.53955078125, -1076.130859375], "size": [319.1407165527344, 134.37188720703125], "flags": {}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"name": "prompt", "type": "STRING", "links": [554, 555, 575], "slot_index": 0, "shape": 3}], "title": "修脸提示词", "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "widgets_values": ["A 23-year-old young European male model,A 23-year-old young European male model,The model had short, handsome brown hair,", 1335, "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 332, "type": "Note", "pos": [3705, -1348], "size": [210, 91.33761596679688], "flags": {}, "order": 28, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["flux修脸\n单张图片换脸或者真实感需求流程：关闭\n人脸lora流程：打开\n"], "color": "#322", "bgcolor": "#533"}, {"id": 333, "type": "Note", "pos": [3524, 40], "size": [210, 91.33761596679688], "flags": {}, "order": 29, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["sd1.5 修脸\n单张图片换脸或者真实感需求流程：打开\n人脸lora流程：关闭\n"], "color": "#322", "bgcolor": "#533"}, {"id": 349, "type": "ConrainPythonExecutor", "pos": [1669.60546875, -666.1307373046875], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 591, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [581], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "type": "ConrainPythonExecutor", "pos": [1692.6005859375, -565.1295776367188], "size": [368.1804504394531, 203.2705841064453], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 587, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [583], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "type": "ConrainAnyToStrings", "pos": [1901.601806640625, -688.1304931640625], "size": [184.8000030517578, 27.56488609313965], "flags": {}, "order": 48, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 581}], "outputs": [{"name": "STRING", "type": "STRING", "links": [582], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 352, "type": "JWStringToInteger", "pos": [2151.55419921875, -692.1304931640625], "size": [210, 34], "flags": {}, "order": 53, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 582, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [593, 595], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 353, "type": "ConrainAnyToStrings", "pos": [1972.60302734375, -558.1295776367188], "size": [184.8000030517578, 39.813907623291016], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 583}], "outputs": [{"name": "STRING", "type": "STRING", "links": [584], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "widgets_values": []}, {"id": 354, "type": "JWStringToInteger", "pos": [2205.5419921875, -537.129638671875], "size": [210, 34], "flags": {}, "order": 52, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "link": 584, "widget": {"name": "text"}}], "outputs": [{"name": "INT", "type": "INT", "links": [592, 594], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["0"]}, {"id": 355, "type": "ImageCrop", "pos": [7384.19775390625, -937.478515625], "size": [225.3616943359375, 122.95598602294922], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 602}, {"name": "width", "type": "INT", "link": 590, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 589, "widget": {"name": "height"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [603], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 365, "type": "Image Size to Number", "pos": [7088.2734375, -1607.447998046875], "size": [229.20001220703125, 126], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 612, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [613], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [614], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 367, "type": "JWIntegerMax", "pos": [7382.2734375, -1532.447998046875], "size": [210, 67.1211166381836], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 613, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 614, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [615], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 375, "type": "ApplyFBCacheOnModel", "pos": [-483.5779724121094, -705.3702392578125], "size": [315, 154], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 647}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [642], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "type": "CR Model Input Switch", "pos": [170.24545288085938, -701.9577026367188], "size": [257.191650390625, 78.78076171875], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 642, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 645, "shape": 7}, {"name": "Input", "type": "INT", "link": 644, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [646], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["1"]}, {"id": 378, "type": "ModelMergeFlux1", "pos": [283.6439208984375, -1048.404052734375], "size": [315, 1566], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 650}, {"name": "model2", "type": "MODEL", "link": 663}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [651], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "type": "UNETLoader", "pos": [-971.5514526367188, -1367.3934326171875], "size": [315, 82], "flags": {}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [656, 662], "slot_index": 0, "shape": 3}], "title": "PW模型", "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"], "color": "#494949", "bgcolor": "#353535"}, {"id": 380, "type": "CR Model Input Switch", "pos": [2002.4271240234375, -1845.9454345703125], "size": [315, 78], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 659, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 654, "shape": 7}, {"name": "Input", "type": "INT", "link": 653, "widget": {"name": "Input"}}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [660], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": ["2"]}, {"id": 381, "type": "JWStringToInteger", "pos": [1568.322265625, -1807.3868408203125], "size": [210, 58], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [653], "slot_index": 0}], "title": "模特后置开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["2"]}, {"id": 382, "type": "Reroute", "pos": [-495.542724609375, -1627.7763671875], "size": [75, 26], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 656}], "outputs": [{"name": "", "type": "MODEL", "links": [657], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 383, "type": "Reroute", "pos": [612.795654296875, -1851.9267578125], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 658}], "outputs": [{"name": "", "type": "MODEL", "links": [659], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 384, "type": "Reroute", "pos": [3162.654541015625, -1830.18701171875], "size": [75, 26], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 660}], "outputs": [{"name": "", "type": "MODEL", "links": [661], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 385, "type": "Reroute", "pos": [-474.9272766113281, -1071.6295166015625], "size": [75, 26], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 662}], "outputs": [{"name": "", "type": "MODEL", "links": [663], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 386, "type": "Reroute", "pos": [415.603759765625, -950.938232421875], "size": [75, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 664}], "outputs": [{"name": "", "type": "CLIP", "links": [665], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 374, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [-504.43853759765625, -1522.910400390625], "size": [395.2306213378906, 126.47626495361328], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 638}, {"name": "clip", "type": "CLIP", "link": 639}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [641], "slot_index": 0, "shape": 3}, {"name": "CLIP", "type": "CLIP", "links": [], "slot_index": 1, "shape": 3}], "title": "人脸lora", "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["product/润辰模特定制_新_11087_20250331_180902/润辰模特定制_新_11087_20250331_180902-flux/润辰模特定制_新_11087_20250331_180902-flux.safetensors", "1", 1], "color": "#232", "bgcolor": "#353"}, {"id": 371, "type": "ApplyFBCacheOnModel", "pos": [-271.2709045410156, -1294.364501953125], "size": [315, 154], "flags": {}, "order": 55, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 633}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [632], "slot_index": 0}], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 369, "type": "ModelMergeFlux1", "pos": [-64.76937866210938, -1602.9716796875], "size": [315, 1566], "flags": {"collapsed": true}, "order": 50, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 641}, {"name": "model2", "type": "MODEL", "link": 657}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [619, 633, 643], "slot_index": 0}], "title": "合并PW和flux模型", "properties": {"Node name for S&R": "ModelMergeFlux1"}, "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 372, "type": "CR Model Input Switch", "pos": [150.6240997314453, -1296.3515625], "size": [242.97930908203125, 78], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "model1", "type": "MODEL", "link": 632, "shape": 7}, {"name": "model2", "type": "MODEL", "link": 643, "shape": 7}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [658, 635], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Model Input Switch"}, "widgets_values": [2]}, {"id": 373, "type": "JWStringToInteger", "pos": [-164.47792053222656, -939.6198120117188], "size": [210, 58], "flags": {"collapsed": false}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [644], "slot_index": 0}], "title": "推理加速开关", "properties": {"Node name for S&R": "JWStringToInteger"}, "widgets_values": ["1"]}, {"id": 261, "type": "Image Size to Number", "pos": [6206, -1548], "size": [229.20001220703125, 126], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 441, "label": "image"}], "outputs": [{"name": "width_num", "type": "NUMBER", "shape": 3, "label": "width_num"}, {"name": "height_num", "type": "NUMBER", "shape": 3, "label": "height_num"}, {"name": "width_float", "type": "FLOAT", "shape": 3, "label": "width_float"}, {"name": "height_float", "type": "FLOAT", "shape": 3, "label": "height_float"}, {"name": "width_int", "type": "INT", "links": [442], "slot_index": 4, "shape": 3, "label": "width_int"}, {"name": "height_int", "type": "INT", "links": [443], "slot_index": 5, "shape": 3, "label": "height_int"}], "properties": {"Node name for S&R": "Image Size to Number"}, "widgets_values": [], "color": "#474747", "bgcolor": "#333333"}, {"id": 266, "type": "UpscaleSizeCalculator", "pos": [7651, -1455], "size": [220, 118], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 532, "label": "image"}, {"name": "target_size", "type": "INT", "link": 615, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [448], "slot_index": 0, "shape": 3, "label": "rescale_factor"}, {"name": "rescale_width", "type": "INT", "shape": 3, "label": "rescale_width"}, {"name": "recover_factor", "type": "FLOAT", "shape": 3, "label": "recover_factor"}, {"name": "recover_width", "type": "INT", "shape": 3, "label": "recover_width"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": ["1785"], "color": "#494949", "bgcolor": "#353535"}, {"id": 263, "type": "ImageScaleBy", "pos": [7965, -1567], "size": [228.9691162109375, 78], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 533, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 448, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [602], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 0.4], "color": "#494949", "bgcolor": "#353535"}], "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [342, 209, 0, 210, 1, "FACE_MODEL"], [343, 209, 1, 210, 2, "FACE_MODEL"], [344, 209, 2, 210, 3, "FACE_MODEL"], [345, 209, 3, 210, 4, "FACE_MODEL"], [346, 152, 0, 210, 5, "IMAGE"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [503, 300, 0, 299, 1, "DETAILER_PIPE"], [504, 301, 0, 300, 0, "BASIC_PIPE"], [505, 303, 0, 300, 1, "BBOX_DETECTOR"], [508, 302, 0, 301, 3, "CONDITIONING"], [509, 304, 0, 301, 4, "CONDITIONING"], [513, 296, 1, 268, 0, "CLIP"], [516, 210, 0, 236, 0, "IMAGE"], [517, 299, 0, 210, 0, "IMAGE"], [520, 270, 0, 301, 2, "VAE"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [545, 329, 1, 326, 0, "CLIP"], [546, 329, 1, 327, 0, "CLIP"], [547, 329, 0, 328, 1, "MODEL"], [548, 329, 1, 328, 2, "CLIP"], [549, 329, 2, 328, 3, "VAE"], [550, 326, 0, 328, 4, "CONDITIONING"], [551, 327, 0, 328, 5, "CONDITIONING"], [552, 324, 0, 328, 6, "BBOX_DETECTOR"], [553, 325, 0, 328, 7, "SAM_MODEL"], [554, 331, 0, 302, 1, "STRING"], [555, 331, 0, 326, 1, "STRING"], [557, 328, 0, 299, 0, "IMAGE"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [603, 355, 0, 216, 0, "IMAGE"], [611, 269, 0, 328, 0, "IMAGE"], [612, 210, 0, 365, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [619, 369, 0, 301, 0, "MODEL"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [630, 296, 1, 301, 1, "CLIP"], [631, 296, 1, 302, 0, "CLIP"], [632, 371, 0, 372, 0, "MODEL"], [633, 369, 0, 371, 0, "MODEL"], [638, 272, 0, 374, 0, "MODEL"], [639, 271, 0, 374, 1, "CLIP"], [641, 374, 0, 369, 0, "MODEL"], [642, 375, 0, 377, 0, "MODEL"], [643, 369, 0, 372, 1, "MODEL"], [644, 373, 0, 377, 2, "INT"], [645, 272, 0, 377, 1, "MODEL"], [646, 377, 0, 297, 0, "MODEL"], [647, 272, 0, 375, 0, "MODEL"], [650, 296, 0, 378, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [653, 381, 0, 380, 2, "INT"], [654, 296, 0, 380, 1, "MODEL"], [656, 379, 0, 382, 0, "*"], [657, 382, 0, 369, 1, "MODEL"], [658, 372, 0, 383, 0, "*"], [659, 383, 0, 380, 0, "MODEL"], [660, 380, 0, 384, 0, "*"], [661, 384, 0, 301, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"]], "groups": [{"id": 1, "title": "换脸", "bounding": [4431.9775390625, -1752.8297119140625, 1069, 1391], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "1.5修脸", "bounding": [2992, -734, 1347, 1200], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "换背景", "bounding": [5626.828125, -1745.7041015625, 2682, 634], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "修脸换脸", "bounding": [2984, -1753, 1350, 956], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "出图", "bounding": [631.6390380859375, -1742.131591796875, 2253, 1344], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "保存图片", "bounding": [5624.35009765625, -1042.7269287109375, 2689, 694], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "模型加载", "bounding": [-1031.672119140625, -1730.5921630859375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.20992832109438067, "offset": [-2139.907780635149, 3547.5423110244756]}}, "version": 0.4, "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "299": 3, "328": 3, "331": 1}}}}}
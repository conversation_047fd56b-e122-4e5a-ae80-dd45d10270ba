{"client_id": "${clientId}", "prompt": {"52": {"inputs": {"face_mask": false, "background_mask": false, "hair_mask": false, "body_mask": false, "clothes_mask": false, "confidence": 0.4, "images": ["243", 0]}, "class_type": "APersonMaskGenerator", "_meta": {"title": "A Person Mask Generator"}}, "53": {"inputs": {"mask": ["52", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "54": {"inputs": {"mask": ["211", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "56": {"inputs": {"channel": "red", "image": ["324", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "60": {"inputs": {"image": ["243", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "61": {"inputs": {"image": ["399", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "159": {"inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}, "class_type": "Text String", "_meta": {"title": "保存目录和文件名前缀"}}, "168": {"inputs": {"padding_left": 0, "padding_right": 0, "padding_top": 0, "padding_bottom": 0, "image": ["413", 0], "mask": ["315", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "176": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = int(center_h - rect_height / 2.0)\n    new_h_end   = new_h_start + rect_height\n    new_w_start = int(center_w - rect_width / 2.0)\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]", "any_a": ["314", 0], "any_b": ["314", 1], "any_c": ["168", 1], "any_d": ["256", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "以涂抹区域的为中心点画一个矩形框"}}, "194": {"inputs": {"mask": ["315", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "211": {"inputs": {"channel": "red", "image": ["408", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "242": {"inputs": {"padding_left": ["426", 0], "padding_right": ["426", 0], "padding_top": ["426", 0], "padding_bottom": ["426", 0], "image": ["346", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "243": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["245", 0], "source": ["254", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "245": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 16777215}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "246": {"inputs": {"seed": ["428", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认宽"}}, "247": {"inputs": {"seed": ["428", 0]}, "class_type": "CR Seed", "_meta": {"title": "默认高"}}, "249": {"inputs": {"value": "b/2-a/2", "a": ["320", 1], "b": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "250": {"inputs": {"value": "b/2-a/2", "a": ["320", 0], "b": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "253": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]", "any_a": ["264", 0], "any_b": ["265", 0], "any_c": ["319", 0], "any_d": ["319", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "原logo比目标尺寸大时缩小到目标尺寸"}}, "254": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["242", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "256": {"inputs": {"call_code": "def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]", "any_a": ["264", 0], "any_b": ["265", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "concat的图片大小"}}, "258": {"inputs": {"a": ["314", 0], "b": ["246", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum宽"}}, "259": {"inputs": {"a": ["314", 1], "b": ["247", 0]}, "class_type": "JWIntegerMin", "_meta": {"title": "Minimum高"}}, "264": {"inputs": {"a": ["316", 0], "b": ["258", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum宽"}}, "265": {"inputs": {"a": ["316", 1], "b": ["259", 0]}, "class_type": "JWIntegerMax", "_meta": {"title": "Maximum高"}}, "275": {"inputs": {"seed": 695267675323688, "steps": 10, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["283", 0], "positive": ["282", 0], "negative": ["282", 1], "latent_image": ["282", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "276": {"inputs": {"samples": ["275", 0], "vae": ["280", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "277": {"inputs": {"text": "The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting.", "clip": ["281", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "278": {"inputs": {"guidance": 30, "conditioning": ["277", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "279": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "280": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "281": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "282": {"inputs": {"noise_mask": true, "positive": ["286", 0], "negative": ["290", 0], "vae": ["280", 0], "pixels": ["289", 1], "mask": ["289", 2]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "283": {"inputs": {"model": ["293", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "284": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "285": {"inputs": {"crop": "center", "clip_vision": ["284", 0], "image": ["243", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "286": {"inputs": {"strength": 1, "strength_type": "multiply", "conditioning": ["278", 0], "style_model": ["287", 0], "clip_vision_output": ["285", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "287": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "288": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["289", 0], "inpainted_image": ["276", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "✂️ Inpaint <PERSON>itch"}}, "289": {"inputs": {"context_expand_pixels": 10, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 0, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 512, "min_height": 512, "max_width": 1536, "max_height": 1536, "padding": 32, "image": ["321", 0], "mask": ["56", 0], "optional_context_mask": ["297", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "✂️ Inpaint Crop"}}, "290": {"inputs": {"conditioning": ["277", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "291": {"inputs": {"width": ["61", 0], "height": ["61", 1], "x": ["60", 0], "y": 0, "image": ["288", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "293": {"inputs": {"lora_name": "fill-lora/catvton-flux-lora-alpha.safetensors", "strength_model": 1, "model": ["279", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "297": {"inputs": {"channel": "red", "image": ["323", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "300": {"inputs": {"mask": ["351", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "301": {"inputs": {"image": ["300", 0], "image_bounds": ["242", 1]}, "class_type": "Bounded Image Crop", "_meta": {"title": "Bounded Image Crop"}}, "306": {"inputs": {"blend_factor": 1, "feathering": 5, "target": ["413", 0], "target_bounds": ["176", 0], "source": ["291", 0]}, "class_type": "Bounded_Image_Blend_LR", "_meta": {"title": "Bounded Image Blend LR"}}, "308": {"inputs": {"output_path": ["159", 0], "filename_prefix": ["159", 1], "extension": "jpg", "dpi": 100, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["306", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "314": {"inputs": {"image": ["413", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "315": {"inputs": {"expand": 2, "tapered_corners": true, "mask": ["420", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "316": {"inputs": {"image": ["168", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "319": {"inputs": {"image": ["242", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "320": {"inputs": {"image": ["254", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "321": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["243", 0], "image2": ["399", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "323": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["385", 0], "image2": ["53", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "324": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["53", 0], "image2": ["54", 0]}, "class_type": "easy imageConcat", "_meta": {"title": "imageConcat"}}, "326": {"inputs": {"text": "667", "anything": ["319", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "341": {"inputs": {"image": "${mergedImageComfyUI_origin}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "模特图"}}, "343": {"inputs": {"image": "${mergedImageComfyUI_cloth}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "服装图"}}, "346": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["343", 0], "mask": ["343", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "347": {"inputs": {"value": "a/2+450", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "348": {"inputs": {"value": "a/2+450", "a": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "349": {"inputs": {"mask": ["343", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "350": {"inputs": {"padding_left": ["347", 0], "padding_right": ["347", 0], "padding_top": ["348", 0], "padding_bottom": ["348", 0], "image": ["349", 0], "mask": ["343", 1]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "351": {"inputs": {"channel": "red", "image": ["350", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "362": {"inputs": {"value": "a*2*2", "a": ["264", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "363": {"inputs": {"value": "a*2", "a": ["265", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "384": {"inputs": {"upscale_method": "area", "scale_by": ["253", 0], "image": ["392", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "385": {"inputs": {"x": ["250", 0], "y": ["249", 0], "resize_source": false, "destination": ["386", 0], "source": ["384", 0]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "386": {"inputs": {"width": ["264", 0], "height": ["265", 0], "batch_size": 1, "color": 0}, "class_type": "EmptyImage", "_meta": {"title": "设置底图颜色"}}, "392": {"inputs": {"padding_left": ["426", 0], "padding_right": ["426", 0], "padding_top": ["426", 0], "padding_bottom": ["426", 0], "image": ["300", 0], "mask": ["351", 0]}, "class_type": "Bounded Image Crop with Mask", "_meta": {"title": "Bounded Image Crop with Mask"}}, "399": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["400", 0], "y": ["401", 0], "image": ["413", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "400": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "401": {"inputs": {"call_code": "# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]", "any_a": ["176", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "408": {"inputs": {"width": ["264", 0], "height": ["265", 0], "x": ["400", 0], "y": ["401", 0], "image": ["194", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "410": {"inputs": {"call_code": "# 如果图片尺寸大于1785，压缩到1785\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(1785,max(any_a,any_b))]", "any_a": ["411", 0], "any_b": ["411", 1]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "411": {"inputs": {"image": ["341", 0]}, "class_type": "Get Image Size", "_meta": {"title": "Get Image Size"}}, "413": {"inputs": {"upscale_method": "nearest-exact", "scale_by": ["414", 0], "image": ["341", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "414": {"inputs": {"target_size": ["410", 0], "image": ["341", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "417": {"inputs": {"upscale_method": "nearest-exact", "scale_by": ["418", 0], "image": ["419", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "418": {"inputs": {"target_size": ["410", 0], "image": ["419", 0]}, "class_type": "UpscaleSizeCalculator", "_meta": {"title": "Upscale Size Calculator"}}, "419": {"inputs": {"mask": ["341", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "420": {"inputs": {"channel": "red", "image": ["417", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "426": {"inputs": {"value": "a/15", "a": ["433", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "428": {"inputs": {"call_code": "# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = max(h_end-h_start,w_end-w_start)\n\n   width = max_size + int(math.sqrt(max_size)) + any_b\n\n   return [width.item()]", "any_a": ["168", 1], "any_b": ["433", 0]}, "class_type": "ConrainPythonExecutor", "_meta": {"title": "conrain python executor"}}, "429": {"inputs": {"text": "637", "anything": ["428", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "433": {"inputs": {"seed": "${growMask?number}"}, "class_type": "CR Seed", "_meta": {"title": "原图选区外扩大小"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 442, "last_link_id": 909, "nodes": [{"id": 52, "type": "APersonMaskGenerator", "pos": [-3768.278564453125, -1715.478759765625], "size": [261.10693359375, 178], "flags": {}, "order": 88, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 864}], "outputs": [{"name": "masks", "type": "MASK", "links": [109], "slot_index": 0}], "properties": {"Node name for S&R": "APersonMaskGenerator"}, "widgets_values": [false, false, false, false, false, 0.4]}, {"id": 53, "type": "MaskToImage", "pos": [-2910.278076171875, -1648.478759765625], "size": [264.5999755859375, 26], "flags": {}, "order": 90, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [594, 596], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 54, "type": "MaskToImage", "pos": [-2914.278076171875, -1466.478759765625], "size": [264.5999755859375, 26], "flags": {}, "order": 72, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 837}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [597], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 283, "type": "DifferentialDiffusion", "pos": [-3258.596923828125, -906.3131103515625], "size": [184.8000030517578, 26], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 506, "label": "模型"}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [493], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 290, "type": "ConditioningZeroOut", "pos": [-3824.216064453125, -796.3939208984375], "size": [317.4000244140625, 26], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 513, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [502], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "widgets_values": []}, {"id": 293, "type": "LoraLoaderModelOnly", "pos": [-3582.215576171875, -916.3945922851562], "size": [228.5093231201172, 82], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 516}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [506], "slot_index": 0}], "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "widgets_values": ["fill-lora/catvton-flux-lora-alpha.safetensors", 1], "color": "#494949", "bgcolor": "#353535"}, {"id": 281, "type": "DualCLIPLoader", "pos": [-4481.96875, -887.5264282226562], "size": [315, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [499], "slot_index": 0, "label": "CLIP"}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"]}, {"id": 277, "type": "CLIPTextEncode", "pos": [-4120.9716796875, -837.5263671875], "size": [210, 84.2347640991211], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 499, "label": "CLIP"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [500, 513], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["The pair of images highlights a cloth and its styling on a model, high resolution, 4K, 8K; [IMAGE1] Detailed product shot of a cloth; [IMAGE2] The same cloth is worn by a model in a lifestyle setting."], "color": "#222", "bgcolor": "#000"}, {"id": 279, "type": "UNETLoader", "pos": [-3847.************, -932.5264892578125], "size": [210, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [516], "slot_index": 0, "label": "模型"}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-fill-dev.safetensors", "default"]}, {"id": 278, "type": "FluxGuidance", "pos": [-3867.970703125, -725.5264892578125], "size": [210, 58], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 500, "label": "条件"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [508], "slot_index": 0, "shape": 3, "label": "条件"}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [30]}, {"id": 324, "type": "easy imageConcat", "pos": [-2513.************, -1599.7569580078125], "size": [315, 102], "flags": {"collapsed": false}, "order": 93, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 596}, {"name": "image2", "type": "IMAGE", "link": 597}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [598], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 280, "type": "VAELoader", "pos": [-4454.9990234375, -692.************], "size": [300, 60], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [498, 503], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 284, "type": "CLIPVisionLoader", "pos": [-4475.2138671875, -419.3942565917969], "size": [370, 60], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [507], "slot_index": 0, "label": "CLIP视觉"}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 287, "type": "StyleModelLoader", "pos": [-4464.2138671875, -570.7047729492188], "size": [340, 60], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "STYLE_MODEL", "type": "STYLE_MODEL", "links": [509], "label": "风格模型"}], "properties": {"Node name for S&R": "StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 319, "type": "GetImageSize+", "pos": [-7041, -910], "size": [214.20001220703125, 66], "flags": {}, "order": 74, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 580}], "outputs": [{"name": "width", "type": "INT", "links": [581, 603], "slot_index": 0}, {"name": "height", "type": "INT", "links": [582], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 349, "type": "MaskToImage", "pos": [-8063, -198], "size": [176.39999389648438, 26], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 670}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [658], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 358, "type": "Note", "pos": [-7442, -373], "size": [210, 67.93143463134766], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["先裁剪图片再grow mask提升速度"], "color": "#432", "bgcolor": "#653"}, {"id": 363, "type": "SimpleMath+", "pos": [-4335.67626953125, -161.4929962158203], "size": [210, 98], "flags": {"collapsed": false}, "order": 53, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 676, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2"]}, {"id": 362, "type": "SimpleMath+", "pos": [-4352.67626953125, -314.4930419921875], "size": [210, 98], "flags": {"collapsed": false}, "order": 49, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 675, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*2*2"]}, {"id": 286, "type": "StyleModelApply", "pos": [-3561.677001953125, -689.4927368164062], "size": [210, 122], "flags": {}, "order": 91, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 508, "label": "条件"}, {"name": "style_model", "type": "STYLE_MODEL", "link": 509, "label": "风格模型"}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 510, "shape": 7, "label": "CLIP视觉输出"}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [501], "slot_index": 0, "label": "条件"}], "properties": {"Node name for S&R": "StyleModelApply"}, "widgets_values": [1, "multiply"]}, {"id": 56, "type": "ImageToMask", "pos": [-2102.2783203125, -1624.478759765625], "size": [210, 59.905555725097656], "flags": {}, "order": 95, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 598}], "outputs": [{"name": "MASK", "type": "MASK", "links": [799], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 350, "type": "Bounded Image Crop with Mask", "pos": [-7739, -218], "size": [243.56057739257812, 150], "flags": {}, "order": 57, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 658}, {"name": "mask", "type": "MASK", "link": 657}, {"name": "padding_left", "type": "INT", "link": 659, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 660, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 661, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 662, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [663], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 243, "type": "ImageCompositeMasked", "pos": [-5220, -710], "size": [210, 138], "flags": {}, "order": 83, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 418, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 440, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 420, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 421, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [871], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 391, "type": "Reroute", "pos": [-6222, -847], "size": [75, 26], "flags": {}, "order": 54, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 756, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [759, 760, 762], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 300, "type": "MaskToImage", "pos": [-7361, -638], "size": [176.39999389648438, 26], "flags": {}, "order": 69, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 891}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [538, 765], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 249, "type": "SimpleMath+", "pos": [-5570, -705], "size": [220.35072326660156, 98], "flags": {}, "order": 82, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 585, "shape": 7}, {"name": "b", "type": "*", "link": 760, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [421, 770], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 250, "type": "SimpleMath+", "pos": [-5570, -904], "size": [210, 112.43743896484375], "flags": {}, "order": 81, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 584, "shape": 7}, {"name": "b", "type": "*", "link": 758, "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [420, 771], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["b/2-a/2"]}, {"id": 301, "type": "Bounded Image Crop", "pos": [-5570, -521], "size": [229.20001220703125, 46], "flags": {}, "order": 75, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 538}, {"name": "image_bounds", "type": "IMAGE_BOUNDS", "link": 746}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded Image Crop"}, "widgets_values": []}, {"id": 385, "type": "ImageCompositeMasked", "pos": [-5244, -287], "size": [210, 138], "flags": {}, "order": 84, "mode": 0, "inputs": [{"name": "destination", "type": "IMAGE", "link": 749, "label": "destination"}, {"name": "source", "type": "IMAGE", "link": 768, "label": "source"}, {"name": "mask", "type": "MASK", "shape": 7, "label": "mask"}, {"name": "x", "type": "INT", "link": 771, "widget": {"name": "x"}, "label": "x"}, {"name": "y", "type": "INT", "link": 770, "widget": {"name": "y"}, "label": "y"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [773], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageCompositeMasked"}, "widgets_values": [0, 0, false], "color": "#494949", "bgcolor": "#353535"}, {"id": 323, "type": "easy imageConcat", "pos": [-2429.2783203125, -1920.478759765625], "size": [315, 102], "flags": {}, "order": 92, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 773}, {"name": "image2", "type": "IMAGE", "link": 594}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [595], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 346, "type": "Bounded Image Crop with Mask", "pos": [-7737, -468], "size": [243.56057739257812, 150], "flags": {}, "order": 56, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 646}, {"name": "mask", "type": "MASK", "link": 647}, {"name": "padding_left", "type": "INT", "link": 651, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 650, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 653, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 654, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [655], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 253, "type": "ConrainPythonExecutor", "pos": [-6739, -924], "size": [397.1240234375, 180.2206268310547], "flags": {}, "order": 77, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 481, "shape": 7}, {"name": "any_b", "type": "*", "link": 478, "shape": 7}, {"name": "any_c", "type": "*", "link": 581, "shape": 7}, {"name": "any_d", "type": "*", "link": 582, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [486, 748], "slot_index": 0}], "title": "原logo比目标尺寸大时缩小到目标尺寸", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\n\treturn [min(any_a/any_c,any_b/any_d,1)]"]}, {"id": 386, "type": "EmptyImage", "pos": [-5890, -377], "size": [243.3533935546875, 102], "flags": {}, "order": 59, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 761, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 762, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [749], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 0], "color": "#222", "bgcolor": "#000"}, {"id": 282, "type": "InpaintModelConditioning", "pos": [-3272.677001953125, -793.4927368164062], "size": [210, 138], "flags": {}, "order": 97, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 501, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 502, "label": "负面条件"}, {"name": "vae", "type": "VAE", "link": 503, "label": "VAE"}, {"name": "pixels", "type": "IMAGE", "link": 800, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 801, "label": "遮罩"}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [494], "slot_index": 0, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "links": [495], "slot_index": 1, "label": "负面条件"}, {"name": "latent", "type": "LATENT", "links": [496], "slot_index": 2, "label": "Latent"}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 245, "type": "EmptyImage", "pos": [-5927, -883], "size": [243.3533935546875, 102], "flags": {}, "order": 58, "mode": 0, "inputs": [{"name": "width", "type": "INT", "link": 757, "widget": {"name": "width"}, "label": "width"}, {"name": "height", "type": "INT", "link": 759, "widget": {"name": "height"}, "label": "height"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [418], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "title": "设置底图颜色", "properties": {"Node name for S&R": "EmptyImage"}, "widgets_values": [512, 512, 1, 16777215], "color": "#222", "bgcolor": "#000"}, {"id": 384, "type": "ImageScaleBy", "pos": [-6274, -244], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 79, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 766}, {"name": "scale_by", "type": "FLOAT", "link": 748, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [768], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 326, "type": "easy showAnything", "pos": [-6691, -549], "size": [210, 76], "flags": {}, "order": 76, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 603, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["667"]}, {"id": 285, "type": "CLIPVisionEncode", "pos": [-3850.677001953125, -583.4932861328125], "size": [210, 78], "flags": {"collapsed": false}, "order": 89, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 507, "label": "CLIP视觉"}, {"name": "image", "type": "IMAGE", "link": 865, "label": "图像"}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [510], "slot_index": 0, "label": "CLIP视觉输出"}], "properties": {"Node name for S&R": "CLIPVisionEncode"}, "widgets_values": ["center"]}, {"id": 390, "type": "Reroute", "pos": [-6223, -913], "size": [75, 26], "flags": {}, "order": 50, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 755, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [757, 758, 761], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 211, "type": "ImageToMask", "pos": [-3252.278076171875, -1394.478759765625], "size": [210, 84.43663024902344], "flags": {}, "order": 68, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 832}], "outputs": [{"name": "MASK", "type": "MASK", "links": [837], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 411, "type": "Get Image Size", "pos": [-7955.52734375, -2943.770263671875], "size": [210, 46], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 839}], "outputs": [{"name": "width", "type": "INT", "links": [840], "slot_index": 0}, {"name": "height", "type": "INT", "links": [841], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 304, "type": "Reroute", "pos": [-2952.677001953125, -328.4930725097656], "size": [75, 26], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 610}], "outputs": [{"name": "", "type": "IMAGE", "links": [547], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 423, "type": "Reroute", "pos": [-8016.18115234375, -2216.91552734375], "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 867}], "outputs": [{"name": "", "type": "MASK", "links": [868], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 400, "type": "ConrainPythonExecutor", "pos": [-5377.41259765625, -1736.681884765625], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 804, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [824, 828], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][2]]"]}, {"id": 401, "type": "ConrainPythonExecutor", "pos": [-5384.41259765625, -1837.681640625], "size": [385.54595947265625, 187.53152465820312], "flags": {"collapsed": true}, "order": 62, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 806, "shape": 7}, {"name": "any_b", "type": "*", "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [823, 827], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 自定义方法，方法名称call和4个入参不能修改\n# 示例方法\ndef call(any_a, any_b, any_c, any_d):\n\treturn [any_a[0][0]]"]}, {"id": 399, "type": "ImageCrop", "pos": [-5181.41259765625, -2220.6796875], "size": [210, 114], "flags": {}, "order": 64, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 834}, {"name": "width", "type": "INT", "link": 812, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 813, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 824, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 823, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [872], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 194, "type": "MaskToImage", "pos": [-5909.41259765625, -1386.681884765625], "size": [264.5999755859375, 26], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 570}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [831], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 265, "type": "JWIntegerMax", "pos": [-6489.41259765625, -1410.681884765625], "size": [210, 71.68185424804688], "flags": {}, "order": 47, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 573, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 476, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [477, 478, 652, 676, 756, 813, 829], "slot_index": 0}], "title": "Maximum高", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 264, "type": "JWIntegerMax", "pos": [-6496.41259765625, -1818.681640625], "size": [210, 71.68185424804688], "flags": {}, "order": 46, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 572, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 473, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [474, 481, 648, 675, 755, 812, 830], "slot_index": 0}], "title": "Maximum宽", "properties": {"Node name for S&R": "JWIntegerMax"}, "widgets_values": [0, 0]}, {"id": 269, "type": "Note", "pos": [-6511.41259765625, -1711.681884765625], "size": [210, 67.93143463134766], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["服装mask的区域超过默认宽和高时，以图片区域为准"], "color": "#432", "bgcolor": "#653"}, {"id": 316, "type": "Get Image Size", "pos": [-6819.41259765625, -1891.680419921875], "size": [210, 46], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 571}], "outputs": [{"name": "width", "type": "INT", "links": [572], "slot_index": 0}, {"name": "height", "type": "INT", "links": [573], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 314, "type": "Get Image Size", "pos": [-7503.41259765625, -2114.6796875], "size": [210, 46], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 608}], "outputs": [{"name": "width", "type": "INT", "links": [564, 566], "slot_index": 0}, {"name": "height", "type": "INT", "links": [565, 567], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 328, "type": "Reroute", "pos": [-7682.41259765625, -2203.6796875], "size": [75, 26], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 845}], "outputs": [{"name": "", "type": "IMAGE", "links": [606, 608, 610, 834], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 412, "type": "Reroute", "pos": [-6997.3974609375, -2618.71630859375], "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 838}], "outputs": [{"name": "", "type": "IMAGE", "links": [839, 844, 846], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 329, "type": "Reroute", "pos": [-7718.41259765625, -2036.6796875], "size": [75, 26], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 869}], "outputs": [{"name": "", "type": "MASK", "links": [612], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 419, "type": "MaskToImage", "pos": [-7673.2978515625, -2735.9931640625], "size": [264.5999755859375, 26], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 868}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [855, 856], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": []}, {"id": 414, "type": "UpscaleSizeCalculator", "pos": [-7281.2978515625, -3029.9931640625], "size": [240.57949829101562, 106.9029541015625], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 846}, {"name": "target_size", "type": "INT", "link": 842, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [843], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 413, "type": "ImageScaleBy", "pos": [-6932.2978515625, -3033.9931640625], "size": [263.92108154296875, 106.1873550415039], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 844}, {"name": "scale_by", "type": "FLOAT", "link": 843, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [845], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["nearest-exact", 1]}, {"id": 420, "type": "ImageToMask", "pos": [-6602.2978515625, -2758.9931640625], "size": [210, 58], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 857}], "outputs": [{"name": "MASK", "type": "MASK", "links": [869], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 305, "type": "Reroute", "pos": [-2963, -433], "size": [75, 26], "flags": {}, "order": 60, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 544}], "outputs": [{"name": "", "type": "*", "links": [550], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 410, "type": "ConrainPythonExecutor", "pos": [-7677.2978515625, -3034.9931640625], "size": [310.3677978515625, 177.20755004882812], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 840, "shape": 7}, {"name": "any_b", "type": "*", "link": 841, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [842, 853], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 如果图片尺寸大于1785，压缩到1785\ndef call(any_a, any_b, any_c, any_d):\n\treturn [min(1785,max(any_a,any_b))]"]}, {"id": 297, "type": "ImageToMask", "pos": [-2102.2783203125, -1921.478759765625], "size": [210, 59.905555725097656], "flags": {}, "order": 94, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 595}], "outputs": [{"name": "MASK", "type": "MASK", "links": [530], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 247, "type": "CR Seed", "pos": [-7530.41259765625, -1575.681884765625], "size": [278.3121032714844, 102], "flags": {}, "order": 42, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 879, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [455], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认高", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [256, "fixed"]}, {"id": 259, "type": "JWIntegerMin", "pos": [-7105.41259765625, -1530.681884765625], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 45, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 565, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 455, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [476], "slot_index": 0}], "title": "Minimum高", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 268, "type": "Note", "pos": [-7087.41259765625, -1714.681884765625], "size": [210, 67.93143463134766], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["模特图较小时，以模特图的大小为准"], "color": "#432", "bgcolor": "#653"}, {"id": 258, "type": "JWIntegerMin", "pos": [-7085.41259765625, -1863.68115234375], "size": [213.4287567138672, 79.92222595214844], "flags": {}, "order": 44, "mode": 0, "inputs": [{"name": "a", "type": "INT", "link": 564, "widget": {"name": "a"}}, {"name": "b", "type": "INT", "link": 453, "widget": {"name": "b"}}], "outputs": [{"name": "INT", "type": "INT", "links": [473], "slot_index": 0}], "title": "Minimum宽", "properties": {"Node name for S&R": "JWIntegerMin"}, "widgets_values": [0, 0]}, {"id": 168, "type": "Bounded Image Crop with Mask", "pos": [-7094.71728515625, -2233.998046875], "size": [248.14456176757812, 150], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 606}, {"name": "mask", "type": "MASK", "link": 569}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [571], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [292, 877], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [0, 0, 0, 0]}, {"id": 256, "type": "ConrainPythonExecutor", "pos": [-6245.71728515625, -2070.998046875], "size": [270.53582763671875, 200.77845764160156], "flags": {}, "order": 51, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 474, "shape": 7}, {"name": "any_b", "type": "*", "link": 477, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [451], "slot_index": 0}], "title": "concat的图片大小", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n    return [[any_a, any_b]]"]}, {"id": 176, "type": "ConrainPythonExecutor", "pos": [-5845.71728515625, -2122.998046875], "size": [423.4119567871094, 402.7642517089844], "flags": {"collapsed": true}, "order": 55, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 566, "shape": 7}, {"name": "any_b", "type": "*", "link": 567, "shape": 7}, {"name": "any_c", "type": "*", "link": 292, "shape": 7}, {"name": "any_d", "type": "*", "link": 451, "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [544, 804, 806], "slot_index": 0}], "title": "以涂抹区域的为中心点画一个矩形框", "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\n    \"\"\"\n    根据图片尺寸和小矩形顶点，返回以小矩形中心为中心且尺寸固定的矩形顶点，\n    输出格式为 [h_start, h_end, w_start, w_end]，其中每个值都是 torch.tensor。\n\n    参数：\n      img_height: 图片高度\n      img_width: 图片宽度\n      small_rect: 小矩形顶点 [h_start, h_end, w_start, w_end]（支持 torch.tensor 或数字）\n      rect_width: 新矩形宽度，默认768\n      rect_height: 新矩形高度，默认1024\n\n    返回：\n      新矩形顶点 [h_start_new, h_end_new, w_start_new, w_end_new]，保证完全位于图片内\n    \"\"\"\n\n    img_height = any_b\n    img_width = any_a\n    small_rect = any_c[0]\n    rect_width=any_d[0]\n    rect_height=any_d[1]\n\n    # 提取小矩形各坐标，并转换为浮点数方便计算\n    h_start = small_rect[0]\n    h_end   = small_rect[1]\n    w_start = small_rect[2]\n    w_end   = small_rect[3]\n    # 计算小矩形的中心点\n    center_h = (h_start + h_end) / 2.0\n    center_w = (w_start + w_end) / 2.0\n\n    # 根据中心点计算新矩形的初始边界（不考虑边界情况）\n    new_h_start = int(center_h - rect_height / 2.0)\n    new_h_end   = new_h_start + rect_height\n    new_w_start = int(center_w - rect_width / 2.0)\n    new_w_end   = new_w_start + rect_width\n\n    tmp_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n\n    # 调整新矩形的垂直方向，确保不会超出图片上、下边界\n    if new_h_start < 0:\n        # 向下平移，使上边界刚好为0\n        shift = -new_h_start\n        new_h_start += shift\n        new_h_end += shift\n    if new_h_end > img_height:\n        # 向上平移，使下边界刚好为图片高度\n        shift = new_h_end - img_height\n        new_h_start -= shift\n        new_h_end -= shift\n\n    # 调整新矩形的水平方向，确保不会超出图片左、右边界\n    if new_w_start < 0:\n        shift = -new_w_start\n        new_w_start += shift\n        new_w_end += shift\n    if new_w_end > img_width:\n        shift = new_w_end - img_width\n        new_w_start -= shift\n        new_w_end -= shift\n\n    # 为了和输入格式一致，这里将数值转换为 torch.tensor 类型，\n    # 并取整（可根据需要保留小数）\n    new_rect = [int(new_h_start),\n                int(new_h_end),\n                int(new_w_start),\n                int(new_w_end)]\n    return [[new_rect]]"]}, {"id": 315, "type": "GrowMask", "pos": [-7532.41259765625, -1985.6796875], "size": [232.74205017089844, 84.21175384521484], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 612}], "outputs": [{"name": "MASK", "type": "MASK", "links": [569, 570], "slot_index": 0}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [2, true]}, {"id": 417, "type": "ImageScaleBy", "pos": [-6932.70263671875, -2853.34423828125], "size": [263.92108154296875, 106.1873550415039], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 856}, {"name": "scale_by", "type": "FLOAT", "link": 849, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [857], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["nearest-exact", 1]}, {"id": 418, "type": "UpscaleSizeCalculator", "pos": [-7288.70263671875, -2796.34423828125], "size": [240.57949829101562, 106.9029541015625], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 855}, {"name": "target_size", "type": "INT", "link": 853, "widget": {"name": "target_size"}}], "outputs": [{"name": "rescale_factor", "type": "FLOAT", "links": [849], "slot_index": 0}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "widgets_values": [512]}, {"id": 113, "type": "Reroute", "pos": [-4493, -2175], "size": [75, 26], "flags": {}, "order": 67, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 872}], "outputs": [{"name": "", "type": "IMAGE", "links": [186, 587], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 408, "type": "ImageCrop", "pos": [-5282.19482421875, -1648.326416015625], "size": [315, 130], "flags": {}, "order": 65, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 831}, {"name": "width", "type": "INT", "link": 830, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 829, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 828, "widget": {"name": "x"}}, {"name": "y", "type": "INT", "link": 827, "widget": {"name": "y"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [832], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 422, "type": "Reroute", "pos": [-4489, -1396], "size": [75, 26], "flags": {}, "order": 85, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 871}], "outputs": [{"name": "", "type": "IMAGE", "links": [862, 863, 864, 865], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 330, "type": "Reroute", "pos": [-8001, -693], "size": [75, 26], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 706}], "outputs": [{"name": "", "type": "IMAGE", "links": [646], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 430, "type": "Reroute", "pos": [-7522.99853515625, -838.9227294921875], "size": [75, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 881, "widget": {"name": "value"}}], "outputs": [{"name": "", "type": "INT", "links": [882, 883, 884, 885, 886, 887, 888, 889], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 351, "type": "ImageToMask", "pos": [-7429, -223], "size": [210, 83.63514709472656], "flags": {}, "order": 63, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 663}], "outputs": [{"name": "MASK", "type": "MASK", "links": [890], "slot_index": 0}], "properties": {"Node name for S&R": "ImageToMask"}, "widgets_values": ["red"]}, {"id": 431, "type": "Reroute", "pos": [-7583, -729], "size": [75, 26], "flags": {}, "order": 66, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 890}], "outputs": [{"name": "", "type": "MASK", "links": [891, 892, 893], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 331, "type": "Reroute", "pos": [-8021, -600], "size": [75, 26], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 796}], "outputs": [{"name": "", "type": "MASK", "links": [647, 657, 670], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 348, "type": "SimpleMath+", "pos": [-8053, -245], "size": [210, 98], "flags": {"collapsed": false}, "order": 52, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 652, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [653, 654, 661, 662], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+450"]}, {"id": 392, "type": "Bounded Image Crop with Mask", "pos": [-7154, -474], "size": [243.56057739257812, 150], "flags": {}, "order": 73, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 765}, {"name": "mask", "type": "MASK", "link": 892}, {"name": "padding_left", "type": "INT", "link": 886, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 887, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 888, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 889, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [766], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 288, "type": "InpaintStitch", "pos": [-2284.970703125, -915.5264282226562], "size": [256.60272216796875, 78], "flags": {}, "order": 100, "mode": 0, "inputs": [{"name": "stitch", "type": "STITCH", "link": 511, "label": "接缝"}, {"name": "inpainted_image", "type": "IMAGE", "link": 512, "label": "图像"}], "outputs": [{"name": "image", "type": "IMAGE", "links": [802], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "InpaintStitch"}, "widgets_values": ["bislerp"]}, {"id": 321, "type": "easy imageConcat", "pos": [-3835.125732421875, -2126.687744140625], "size": [315, 102], "flags": {}, "order": 87, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 863}, {"name": "image2", "type": "IMAGE", "link": 587}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [589, 798], "slot_index": 0}], "properties": {"Node name for S&R": "easy imageConcat"}, "widgets_values": ["right", false]}, {"id": 291, "type": "ImageCrop", "pos": [-2492.6767578125, -684.4927368164062], "size": [210, 118], "flags": {}, "order": 101, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 802}, {"name": "width", "type": "INT", "link": 520, "widget": {"name": "width"}}, {"name": "height", "type": "INT", "link": 521, "widget": {"name": "height"}}, {"name": "x", "type": "INT", "link": 522, "widget": {"name": "x"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [551], "slot_index": 0}], "properties": {"Node name for S&R": "ImageCrop"}, "widgets_values": [512, 512, 0, 0]}, {"id": 306, "type": "Bounded_Image_Blend_LR", "pos": [-2284.970703125, -481.5265808105469], "size": [239.650634765625, 122], "flags": {}, "order": 102, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 547}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 550}, {"name": "source", "type": "IMAGE", "link": 551}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [555], "slot_index": 0}], "properties": {"Node name for S&R": "Bounded_Image_Blend_LR"}, "widgets_values": [1, 5]}, {"id": 429, "type": "easy showAnything", "pos": [-7526, -1425], "size": [210, 76], "flags": {}, "order": 43, "mode": 0, "inputs": [{"name": "anything", "type": "*", "link": 880, "shape": 7}], "outputs": [], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["637"]}, {"id": 276, "type": "VAEDecode", "pos": [-2595.5966796875, -904.4205322265625], "size": [210, 46], "flags": {}, "order": 99, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 497, "label": "Latent"}, {"name": "vae", "type": "VAE", "link": 498, "label": "VAE"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [512], "slot_index": 0, "label": "图像"}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 308, "type": "ConrainImageSave", "pos": [-1898.9713134765625, -484.52655029296875], "size": [320, 266], "flags": {}, "order": 103, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 555, "label": "images"}, {"name": "output_path", "type": "STRING", "link": 556, "widget": {"name": "output_path"}, "label": "output_path"}, {"name": "filename_prefix", "type": "STRING", "link": 557, "widget": {"name": "filename_prefix"}, "label": "filename_prefix"}], "outputs": [{"name": "image_cnt", "type": "INT", "links": [], "slot_index": 0, "shape": 3, "label": "image_cnt"}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 426, "type": "SimpleMath+", "pos": [-7809, -847], "size": [210, 98], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 903, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [881], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/15"]}, {"id": 159, "type": "Text String", "pos": [-2740, -291], "size": [315, 190], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [556], "slot_index": 0, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "links": [557], "slot_index": 1, "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}, {"name": "STRING", "type": "STRING", "shape": 3, "label": "STRING"}], "title": "保存目录和文件名前缀", "properties": {"Node name for S&R": "Text String"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 428, "type": "ConrainPythonExecutor", "pos": [-7991, -1936], "size": [377.86767578125, 202.43890380859375], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "any_a", "type": "*", "link": 877, "shape": 7}, {"name": "any_b", "type": "*", "link": 906, "shape": 7}, {"name": "any_c", "type": "*", "shape": 7}, {"name": "any_d", "type": "*", "shape": 7}], "outputs": [{"name": "any", "type": "*", "links": [878, 879, 880], "slot_index": 0}], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "widgets_values": ["# 计算mask区域的大小，并调整放大一点\nimport math\ndef call(any_a, any_b, any_c, any_d):\n   react = any_a[0]\n\n   # 提取小矩形各坐标，并转换为浮点数方便计算\n   h_start = react[0]\n   h_end   = react[1]\n   w_start = react[2]\n   w_end   = react[3]\n\n   max_size = max(h_end-h_start,w_end-w_start)\n\n   width = max_size + int(math.sqrt(max_size)) + any_b\n\n   return [width.item()]"]}, {"id": 347, "type": "SimpleMath+", "pos": [-8052, -451], "size": [210, 98], "flags": {"collapsed": false}, "order": 48, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 648, "shape": 7}, {"name": "b", "type": "*", "shape": 7}, {"name": "c", "type": "*", "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [650, 651, 659, 660], "slot_index": 0}, {"name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a/2+450"]}, {"id": 242, "type": "Bounded Image Crop with Mask", "pos": [-7367, -878], "size": [243.56057739257812, 150], "flags": {}, "order": 70, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 655}, {"name": "mask", "type": "MASK", "link": 893}, {"name": "padding_left", "type": "INT", "link": 882, "widget": {"name": "padding_left"}}, {"name": "padding_right", "type": "INT", "link": 883, "widget": {"name": "padding_right"}}, {"name": "padding_top", "type": "INT", "link": 884, "widget": {"name": "padding_top"}}, {"name": "padding_bottom", "type": "INT", "link": 885, "widget": {"name": "padding_bottom"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [439, 580], "slot_index": 0}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [746], "slot_index": 1}], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "widgets_values": [10, 10, 10, 10]}, {"id": 60, "type": "Get Image Size", "pos": [-4398, -1839], "size": [298.42425537109375, 46], "flags": {}, "order": 86, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 862}], "outputs": [{"name": "width", "type": "INT", "links": [522], "slot_index": 0}, {"name": "height", "type": "INT"}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 246, "type": "CR Seed", "pos": [-7540, -1822], "size": [281.7162780761719, 102], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "seed", "type": "INT", "link": 878, "widget": {"name": "seed"}}], "outputs": [{"name": "seed", "type": "INT", "links": [453], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "默认宽", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": [256, "fixed"]}, {"id": 320, "type": "GetImageSize+", "pos": [-5889, -741], "size": [214.20001220703125, 66], "flags": {}, "order": 80, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 583}], "outputs": [{"name": "width", "type": "INT", "links": [584], "slot_index": 0}, {"name": "height", "type": "INT", "links": [585], "slot_index": 1}, {"name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 254, "type": "ImageScaleBy", "pos": [-6288, -621], "size": [261.7075500488281, 81.54931640625], "flags": {}, "order": 78, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 439}, {"name": "scale_by", "type": "FLOAT", "link": 486, "widget": {"name": "scale_by"}}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [440, 583], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["area", 1]}, {"id": 289, "type": "InpaintCrop", "pos": [-3592.677001953125, -494.4931945800781], "size": [245.64613342285156, 386], "flags": {}, "order": 96, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 798, "label": "图像"}, {"name": "mask", "type": "MASK", "link": 799, "label": "遮罩"}, {"name": "optional_context_mask", "type": "MASK", "link": 530, "shape": 7, "label": "上下文遮罩(可选)"}], "outputs": [{"name": "stitch", "type": "STITCH", "links": [511], "slot_index": 0, "label": "接缝"}, {"name": "cropped_image", "type": "IMAGE", "links": [800], "slot_index": 1}, {"name": "cropped_mask", "type": "MASK", "links": [801], "slot_index": 2}], "properties": {"Node name for S&R": "InpaintCrop"}, "widgets_values": [10, 1, true, 0, false, 16, "bicubic", "ranged size", 1024, 1024, 1, 512, 512, 1536, 1536, 32]}, {"id": 61, "type": "Get Image Size", "pos": [-3225, -544], "size": [210, 46], "flags": {}, "order": 71, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 186}], "outputs": [{"name": "width", "type": "INT", "links": [520], "slot_index": 0}, {"name": "height", "type": "INT", "links": [521], "slot_index": 1}], "properties": {"Node name for S&R": "Get Image Size"}, "widgets_values": []}, {"id": 275, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2983, -904], "size": [322.9063720703125, 262.88861083984375], "flags": {}, "order": 98, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 493, "label": "模型"}, {"name": "positive", "type": "CONDITIONING", "link": 494, "label": "正面条件"}, {"name": "negative", "type": "CONDITIONING", "link": 495, "label": "负面条件"}, {"name": "latent_image", "type": "LATENT", "link": 496, "label": "Latent"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [497], "slot_index": 0, "label": "Latent"}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [439635574454673, "randomize", 10, 1, "euler", "simple", 1]}, {"id": 433, "type": "CR Seed", "pos": [-8062, -921], "size": [232.8075714111328, 121.8284912109375], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "seed", "type": "INT", "links": [903, 906], "slot_index": 0}, {"name": "show_help", "type": "STRING"}], "title": "原图选区外扩大小", "properties": {"Node name for S&R": "CR Seed"}, "widgets_values": ["${growMask?number}", "fixed"], "color": "#232", "bgcolor": "#353"}, {"id": 341, "type": "LoadImage", "pos": [-8831, -2238], "size": [251.2660675048828, 331.4165954589844], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [838], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [867], "slot_index": 1}], "title": "模特图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${mergedImageComfyUI_origin}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 343, "type": "LoadImage", "pos": [-8812, -723], "size": [251.2660675048828, 331.4165954589844], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [706], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [796], "slot_index": 1}], "title": "服装图", "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${mergedImageComfyUI_cloth}", "image"], "color": "#232", "bgcolor": "#353"}], "links": [[109, 52, 0, 53, 0, "MASK"], [186, 113, 0, 61, 0, "IMAGE"], [292, 168, 1, 176, 2, "*"], [418, 245, 0, 243, 0, "IMAGE"], [420, 250, 0, 243, 3, "INT"], [421, 249, 0, 243, 4, "INT"], [439, 242, 0, 254, 0, "IMAGE"], [440, 254, 0, 243, 1, "IMAGE"], [451, 256, 0, 176, 3, "*"], [453, 246, 0, 258, 1, "INT"], [455, 247, 0, 259, 1, "INT"], [473, 258, 0, 264, 1, "INT"], [474, 264, 0, 256, 0, "*"], [476, 259, 0, 265, 1, "INT"], [477, 265, 0, 256, 1, "*"], [478, 265, 0, 253, 1, "*"], [481, 264, 0, 253, 0, "*"], [486, 253, 0, 254, 1, "FLOAT"], [493, 283, 0, 275, 0, "MODEL"], [494, 282, 0, 275, 1, "CONDITIONING"], [495, 282, 1, 275, 2, "CONDITIONING"], [496, 282, 2, 275, 3, "LATENT"], [497, 275, 0, 276, 0, "LATENT"], [498, 280, 0, 276, 1, "VAE"], [499, 281, 0, 277, 0, "CLIP"], [500, 277, 0, 278, 0, "CONDITIONING"], [501, 286, 0, 282, 0, "CONDITIONING"], [502, 290, 0, 282, 1, "CONDITIONING"], [503, 280, 0, 282, 2, "VAE"], [506, 293, 0, 283, 0, "MODEL"], [507, 284, 0, 285, 0, "CLIP_VISION"], [508, 278, 0, 286, 0, "CONDITIONING"], [509, 287, 0, 286, 1, "STYLE_MODEL"], [510, 285, 0, 286, 2, "CLIP_VISION_OUTPUT"], [511, 289, 0, 288, 0, "STITCH"], [512, 276, 0, 288, 1, "IMAGE"], [513, 277, 0, 290, 0, "CONDITIONING"], [516, 279, 0, 293, 0, "MODEL"], [520, 61, 0, 291, 1, "INT"], [521, 61, 1, 291, 2, "INT"], [522, 60, 0, 291, 3, "INT"], [530, 297, 0, 289, 2, "MASK"], [538, 300, 0, 301, 0, "IMAGE"], [544, 176, 0, 305, 0, "*"], [547, 304, 0, 306, 0, "IMAGE"], [550, 305, 0, 306, 1, "IMAGE_BOUNDS"], [551, 291, 0, 306, 2, "IMAGE"], [555, 306, 0, 308, 0, "IMAGE"], [556, 159, 0, 308, 1, "STRING"], [557, 159, 1, 308, 2, "STRING"], [564, 314, 0, 258, 0, "INT"], [565, 314, 1, 259, 0, "INT"], [566, 314, 0, 176, 0, "*"], [567, 314, 1, 176, 1, "*"], [569, 315, 0, 168, 1, "MASK"], [570, 315, 0, 194, 0, "MASK"], [571, 168, 0, 316, 0, "IMAGE"], [572, 316, 0, 264, 0, "INT"], [573, 316, 1, 265, 0, "INT"], [580, 242, 0, 319, 0, "IMAGE"], [581, 319, 0, 253, 2, "*"], [582, 319, 1, 253, 3, "*"], [583, 254, 0, 320, 0, "IMAGE"], [584, 320, 0, 250, 0, "*"], [585, 320, 1, 249, 0, "*"], [587, 113, 0, 321, 1, "IMAGE"], [589, 321, 0, 289, 0, "IMAGE"], [594, 53, 0, 323, 1, "IMAGE"], [595, 323, 0, 297, 0, "IMAGE"], [596, 53, 0, 324, 0, "IMAGE"], [597, 54, 0, 324, 1, "IMAGE"], [598, 324, 0, 56, 0, "IMAGE"], [603, 319, 0, 326, 0, "*"], [606, 328, 0, 168, 0, "IMAGE"], [608, 328, 0, 314, 0, "IMAGE"], [610, 328, 0, 304, 0, "*"], [612, 329, 0, 315, 0, "MASK"], [646, 330, 0, 346, 0, "IMAGE"], [647, 331, 0, 346, 1, "MASK"], [648, 264, 0, 347, 0, "*"], [650, 347, 0, 346, 3, "INT"], [651, 347, 0, 346, 2, "INT"], [652, 265, 0, 348, 0, "*"], [653, 348, 0, 346, 4, "INT"], [654, 348, 0, 346, 5, "INT"], [655, 346, 0, 242, 0, "IMAGE"], [657, 331, 0, 350, 1, "MASK"], [658, 349, 0, 350, 0, "IMAGE"], [659, 347, 0, 350, 2, "INT"], [660, 347, 0, 350, 3, "INT"], [661, 348, 0, 350, 4, "INT"], [662, 348, 0, 350, 5, "INT"], [663, 350, 0, 351, 0, "IMAGE"], [670, 331, 0, 349, 0, "MASK"], [675, 264, 0, 362, 0, "*"], [676, 265, 0, 363, 0, "*"], [706, 343, 0, 330, 0, "*"], [746, 242, 1, 301, 1, "IMAGE_BOUNDS"], [748, 253, 0, 384, 1, "FLOAT"], [749, 386, 0, 385, 0, "IMAGE"], [755, 264, 0, 390, 0, "*"], [756, 265, 0, 391, 0, "*"], [757, 390, 0, 245, 0, "INT"], [758, 390, 0, 250, 1, "*"], [759, 391, 0, 245, 1, "INT"], [760, 391, 0, 249, 1, "*"], [761, 390, 0, 386, 0, "INT"], [762, 391, 0, 386, 1, "INT"], [765, 300, 0, 392, 0, "IMAGE"], [766, 392, 0, 384, 0, "IMAGE"], [768, 384, 0, 385, 1, "IMAGE"], [770, 249, 0, 385, 4, "INT"], [771, 250, 0, 385, 3, "INT"], [773, 385, 0, 323, 0, "IMAGE"], [796, 343, 1, 331, 0, "*"], [798, 321, 0, 289, 0, "IMAGE"], [799, 56, 0, 289, 1, "MASK"], [800, 289, 1, 282, 3, "IMAGE"], [801, 289, 2, 282, 4, "MASK"], [802, 288, 0, 291, 0, "IMAGE"], [804, 176, 0, 400, 0, "*"], [806, 176, 0, 401, 0, "*"], [812, 264, 0, 399, 1, "INT"], [813, 265, 0, 399, 2, "INT"], [823, 401, 0, 399, 4, "INT"], [824, 400, 0, 399, 3, "INT"], [827, 401, 0, 408, 4, "INT"], [828, 400, 0, 408, 3, "INT"], [829, 265, 0, 408, 2, "INT"], [830, 264, 0, 408, 1, "INT"], [831, 194, 0, 408, 0, "IMAGE"], [832, 408, 0, 211, 0, "IMAGE"], [834, 328, 0, 399, 0, "IMAGE"], [837, 211, 0, 54, 0, "MASK"], [838, 341, 0, 412, 0, "*"], [839, 412, 0, 411, 0, "IMAGE"], [840, 411, 0, 410, 0, "*"], [841, 411, 1, 410, 1, "*"], [842, 410, 0, 414, 1, "INT"], [843, 414, 0, 413, 1, "FLOAT"], [844, 412, 0, 413, 0, "IMAGE"], [845, 413, 0, 328, 0, "*"], [846, 412, 0, 414, 0, "IMAGE"], [849, 418, 0, 417, 1, "FLOAT"], [853, 410, 0, 418, 1, "INT"], [855, 419, 0, 418, 0, "IMAGE"], [856, 419, 0, 417, 0, "IMAGE"], [857, 417, 0, 420, 0, "IMAGE"], [862, 422, 0, 60, 0, "IMAGE"], [863, 422, 0, 321, 0, "IMAGE"], [864, 422, 0, 52, 0, "IMAGE"], [865, 422, 0, 285, 1, "IMAGE"], [867, 341, 1, 423, 0, "*"], [868, 423, 0, 419, 0, "MASK"], [869, 420, 0, 329, 0, "*"], [871, 243, 0, 422, 0, "*"], [872, 399, 0, 113, 0, "*"], [877, 168, 1, 428, 0, "*"], [878, 428, 0, 246, 0, "INT"], [879, 428, 0, 247, 0, "INT"], [880, 428, 0, 429, 0, "*"], [881, 426, 0, 430, 0, "*"], [882, 430, 0, 242, 2, "INT"], [883, 430, 0, 242, 3, "INT"], [884, 430, 0, 242, 4, "INT"], [885, 430, 0, 242, 5, "INT"], [886, 430, 0, 392, 2, "INT"], [887, 430, 0, 392, 3, "INT"], [888, 430, 0, 392, 4, "INT"], [889, 430, 0, 392, 5, "INT"], [890, 351, 0, 431, 0, "*"], [891, 431, 0, 300, 0, "MASK"], [892, 431, 0, 392, 1, "MASK"], [893, 431, 0, 242, 1, "MASK"], [903, 433, 0, 426, 0, "*"], [906, 433, 0, 428, 1, "*"]], "groups": [{"id": 2, "title": "cat图片准备", "bounding": [-4590.1357421875, -2354.1220703125, 3106.5283203125, 1229.4473876953125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "涂抹logo原图", "bounding": [-8097.1201171875, -1004.4202270507812, 3181.731201171875, 983.1553955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "涂抹需要修复的logo区域", "bounding": [-8051.62451171875, -2415.42822265625, 3151.830322265625, 1206.65966796875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "重绘", "bounding": [-4554.61083984375, -1036.22705078125, 3081.62109375, 992.3799438476562], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 8, "title": "Group", "bounding": [-8016.513671875, -3155.791748046875, 1700.3160400390625, 623.9169311523438], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.3855432894295319, "offset": [10246.88703825071, 2757.0154633836]}}, "version": 0.4, "seed_widgets": {"275": 0, "433": 0}}}}}
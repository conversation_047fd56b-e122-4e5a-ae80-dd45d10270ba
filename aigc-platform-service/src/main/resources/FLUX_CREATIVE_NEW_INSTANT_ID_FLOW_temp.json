{"client_id": "${clientId}", "prompt": {"177": {"_meta": {"title": "Text String"}, "class_type": "Text String", "inputs": {"text": "${outputPath}", "text_b": "${fileNamePrefix}", "text_c": "", "text_d": ""}}, "185": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "true", "delimiter": " ", "text_a": ["201", 0], "text_b": ["282", 0], "text_c": ["200", 0], "text_d": ["286", 0]}}, "200": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【negative】:"}}, "201": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "【positive】:"}}, "216": {"_meta": {"title": "conrain save image"}, "class_type": "ConrainImageSave", "inputs": {"dpi": 100, "embed_workflow": "false", "extension": "jpg", "filename_prefix": ["177", 1], "images": ["355", 0], "lossless_webp": "false", "optimize_image": "true", "output_as_root": "true", "output_path": ["177", 0], "quality": 100, "use_time_str": "true"}}, "232": {"_meta": {"title": "Text Concatenate"}, "class_type": "Text Concatenate", "inputs": {"clean_whitespace": "false", "delimiter": "/", "text_a": ["233", 0], "text_b": ["177", 0]}}, "233": {"_meta": {"title": "String to Text"}, "class_type": "String to Text", "inputs": {"string": "output"}}, "235": {"_meta": {"title": "Inspyrenet Rembg"}, "class_type": "InspyrenetRembg", "inputs": {"image": ["236", 0], "torchscript_jit": "default"}, "disable": "${isPureBg?then('false','true')}"}, "236": {"_meta": {"title": "🔍 CR Upscale Image"}, "class_type": "CR Upscale Image", "inputs": {"image": ["725", 0], "mode": "rescale", "resampling_method": "lanc<PERSON>s", "rescale_factor": 2, "resize_width": "${height}", "rounding_modulus": 8, "supersample": "true", "upscale_model": "4xUltrasharp_4xUltrasharpV10.pt"}, "disable": "${isPureBg?then('false','true')}"}, "248": {"_meta": {"title": "EmptyImage"}, "class_type": "EmptyImage", "inputs": {"batch_size": 1, "color": "${pureRgb}", "height": ["261", 5], "width": ["261", 4]}, "disable": "${isPureBg?then('false','true')}"}, "258": {"_meta": {"title": "Convert RGBA to RGB 🌌 ReActor"}, "class_type": "ImageRGBA2RGB", "inputs": {"image": ["235", 0]}, "disable": "${isPureBg?then('false','true')}"}, "261": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["236", 0]}, "disable": "${isPureBg?then('false','true')}"}, "263": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["316", 0], "scale_by": ["266", 0], "upscale_method": "lanc<PERSON>s"}, "disable": "${isPureBg?then('false','true')}"}, "266": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["316", 0], "target_size": ["367", 0]}, "disable": "${isPureBg?then('false','true')}"}, "268": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["296", 1], "text": ["283", 0]}}, "269": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["273", 0], "vae": ["270", 0]}}, "270": {"_meta": {"title": "Load VAE"}, "class_type": "VAELoader", "inputs": {"vae_name": "ae.safetensors"}}, "271": {"_meta": {"title": "DualCLIPLoader"}, "class_type": "DualCLIPLoader", "inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "device": "default", "type": "flux"}}, "272": {"_meta": {"title": "Load Diffusion Model"}, "class_type": "UNETLoader", "inputs": {"unet_name": "${baseModelName!'flux1-dev.safetensors'}", "weight_dtype": "${baseModelDType!'default'}"}}, "273": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["276", 0], "latent_image": ["279", 0], "noise": ["277", 0], "sampler": ["274", 0], "sigmas": ["275", 0]}}, "274": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "${samplerName!'euler'}"}}, "275": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 1, "model": ["280", 0], "scheduler": "${scheduleName!'beta'}", "steps": "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}"}}, "276": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["278", 0], "model": ["280", 0]}}, "277": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": "${seed}"}}, "278": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["268", 0], "guidance": "${lora.extInfo.cfg}"}}, "279": {"_meta": {"title": "EmptySD3LatentImage"}, "class_type": "EmptySD3LatentImage", "inputs": {"batch_size": "${imageNum}", "height": ["354", 0], "width": ["352", 0]}}, "280": {"_meta": {"title": "ModelSamplingFlux"}, "class_type": "ModelSamplingFlux", "inputs": {"base_shift": 0.5, "height": ["354", 0], "max_shift": 1.15, "model": ["${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(378,296)}", 0], "width": ["352", 0]}}, "282": {"_meta": {"title": "正向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": "${promptSeed}"}}, "283": {"_meta": {"title": "Show Text 🐍"}, "class_type": "ShowText|pysssss", "inputs": {"text": ["282", 0]}}, "284": {"_meta": {"title": "height"}, "class_type": "CR Seed", "inputs": {"seed": "${height}"}}, "285": {"_meta": {"title": "width"}, "class_type": "CR Seed", "inputs": {"seed": "${width}"}}, "286": {"_meta": {"title": "负向提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "(blurry:1.3), (Breasts exposed:1.2), (But<PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ${lora.extInfo.negative}${FACE.extInfo.negative}${SCENE.extInfo.negative}", "seed": 1033}}, "296": {"_meta": {"title": "服装lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,271),298)}", "${((isPureBg||!isAntiBlueLora)&&!isLoraFace)?then(0,1)?number}"], "lora_name": "${lora.loraName}", "model": ["${(isPureBg||!isAntiBlueLora)?then(isLoraFace?then(297,377),298)}", 0], "strength_clip": 1, "strength_model": "${loraStrength}"}}, "297": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "${FACE.extInfo.faceLoraStrength}", "strength_model": "${FACE.extInfo.faceLoraStrength}"}, "disable": "${isLoraFace?then('false','true')}"}, "298": {"_meta": {"title": "风格lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["${isLoraFace?then(297,271)}", "${isLoraFace?then(1,0)?number}"], "lora_name": "${sceneLora}", "model": ["${isLoraFace?then(297,377)}", 0], "strength_clip": 1, "strength_model": "${sceneLoraStrength}"}, "disable": "${(isPureBg||!isAntiBlueLora)?then('true','false')}"}, "316": {"_meta": {"title": "conrain image composite masked"}, "class_type": "ConrainImageCompositeMasked", "inputs": {"destination": ["248", 0], "mask": ["235", 1], "resize_source": false, "source": ["258", 0], "x": 0, "y": 0}, "disable": "${isPureBg?then('false','true')}"}, "320": {"_meta": {"title": "conrain save text"}, "class_type": "ConrainTextSave", "inputs": {"filename": ["177", 1], "path": ["232", 0], "text": ["185", 0]}}, "331": {"_meta": {"title": "修脸提示词"}, "class_type": "ConrainRandomPrompts", "inputs": {"prompts": "${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "seed": 1335}}, "349": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["285", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "350": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["284", 0], "call_code": "def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"}}, "351": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["349", 0]}}, "352": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["351", 0]}}, "353": {"_meta": {"title": "conrain any to strings"}, "class_type": "ConrainAnyToStrings", "inputs": {"any_a": ["350", 0]}}, "354": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": ["353", 0]}}, "355": {"_meta": {"title": "Image Crop"}, "class_type": "ImageCrop", "inputs": {"height": ["284", 0], "image": ["${isPureBg?then(263,725)}", 0], "width": ["285", 0], "x": 0, "y": 0}}, "365": {"_meta": {"title": "Image Size to Number"}, "class_type": "Image Size to Number", "inputs": {"image": ["725", 0]}}, "367": {"_meta": {"title": "Integer Maximum"}, "class_type": "JWIntegerMax", "inputs": {"a": ["365", 4], "b": ["365", 5]}}, "373": {"_meta": {"title": "推理加速开关"}, "class_type": "JWStringToInteger", "inputs": {"text": "${speedUpSwitch?then(1,2)}"}}, "375": {"_meta": {"title": "Apply First Block Cache"}, "class_type": "ApplyFBCacheOnModel", "inputs": {"end": 0.8, "max_consecutive_cache_hits": 5, "model": ["272", 0], "object_to_patch": "diffusion_model", "residual_diff_threshold": 0.15, "start": 0.2}}, "377": {"_meta": {"title": "🔀 CR Model Input Switch"}, "class_type": "CR Model Input Switch", "inputs": {"Input": ["373", 0], "model1": ["375", 0], "model2": ["272", 0]}}, "378": {"_meta": {"title": "合并PW和flux模型"}, "class_type": "ModelMergeFlux1", "inputs": {"double_blocks.0.": 1, "double_blocks.1.": 1, "double_blocks.10.": 1, "double_blocks.11.": 1, "double_blocks.12.": 1, "double_blocks.13.": 1, "double_blocks.14.": 1, "double_blocks.15.": 1, "double_blocks.16.": 1, "double_blocks.17.": 1, "double_blocks.18.": 1, "double_blocks.2.": 1, "double_blocks.3.": 1, "double_blocks.4.": 1, "double_blocks.5.": 1, "double_blocks.6.": 1, "double_blocks.7.": 1, "double_blocks.8.": 1, "double_blocks.9.": 1, "final_layer.": 1, "guidance_in": 1, "img_in.": 1, "model1": ["296", 0], "model2": ["379", 0], "single_blocks.0.": 1, "single_blocks.1.": 1, "single_blocks.10.": 1, "single_blocks.11.": 1, "single_blocks.12.": 1, "single_blocks.13.": 1, "single_blocks.14.": 1, "single_blocks.15.": 1, "single_blocks.16.": 1, "single_blocks.17.": 1, "single_blocks.18.": 1, "single_blocks.19.": 0, "single_blocks.2.": 1, "single_blocks.20.": 0, "single_blocks.21.": 0, "single_blocks.22.": 0, "single_blocks.23.": 0, "single_blocks.24.": 0, "single_blocks.25.": 0, "single_blocks.26.": 0, "single_blocks.27.": 0, "single_blocks.28.": 0, "single_blocks.29.": 0, "single_blocks.3.": 1, "single_blocks.30.": 0, "single_blocks.31.": 0, "single_blocks.32.": 0, "single_blocks.33.": 0, "single_blocks.34.": 0, "single_blocks.35.": 0, "single_blocks.36.": 0, "single_blocks.37.": 0, "single_blocks.4.": 1, "single_blocks.5.": 1, "single_blocks.6.": 1, "single_blocks.7.": 1, "single_blocks.8.": 1, "single_blocks.9.": 1, "time_in.": 1, "txt_in.": 1, "vector_in.": 1}, "disable": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then('false','true')}"}, "379": {"_meta": {"title": "PW模型"}, "class_type": "UNETLoader", "inputs": {"unet_name": "pixelwave_flux1_dev_bf16_03.safetensors", "weight_dtype": "default"}, "disable": "${(isPWModel)?then('false','true')}"}, "389": {"_meta": {"title": "Load InstantID Model"}, "class_type": "InstantIDModelLoader", "inputs": {"instantid_file": "ip-adapter.bin"}}, "390": {"_meta": {"title": "InstantID Face Analysis"}, "class_type": "InstantIDFaceAnalysis", "inputs": {"provider": "CUDA"}}, "391": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "control_instant_id_sdxl.safetensors"}}, "392": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["402", 0], "negative": ["618", 1], "noise_mask": true, "pixels": ["414", 0], "positive": ["618", 0], "vae": ["416", 2]}}, "397": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": ["331", 0]}}, "398": {"_meta": {"title": "CLIP Text Encode (Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["416", 1], "text": "Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"}}, "399": {"_meta": {"title": "Ordered Face Filter"}, "class_type": "OrderedFaceFilter", "inputs": {"criteria": "area", "faces": ["436", 0], "order": "descending", "take_count": 1, "take_start": 0}}, "402": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 8, "enabled": true, "grow": 16, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["624", 1]}}, "409": {"_meta": {"title": "Image Color Match"}, "class_type": "easy imageColorMatch", "inputs": {"image_output": "<PERSON>de", "image_ref": ["414", 0], "image_target": ["413", 0], "method": "adain", "save_prefix": "ComfyUI"}}, "410": {"_meta": {"title": "<PERSON>p Faces Back"}, "class_type": "WarpFacesBack", "inputs": {"crop": ["409", 0], "face": ["399", 0], "images": ["697", 0], "mask": ["754", 0], "warp": ["414", 2]}}, "411": {"_meta": {"title": "K<PERSON><PERSON><PERSON>"}, "class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": 1, "denoise": 0.4, "latent_image": ["392", 2], "model": ["412", 0], "negative": ["392", 1], "positive": ["392", 0], "sampler_name": "euler_ancestral", "scheduler": "sgm_uniform", "seed": "${seed}", "steps": 4}}, "412": {"_meta": {"title": "Apply InstantID"}, "class_type": "ApplyInstantID", "inputs": {"control_net": ["391", 0], "end_at": 1, "image": ["446", 0], "image_kps": ["414", 0], "insightface": ["390", 0], "instantid": ["389", 0], "model": ["416", 0], "negative": ["398", 0], "positive": ["397", 0], "start_at": 0, "weight": 1}}, "413": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["411", 0], "vae": ["416", 2]}}, "414": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["399", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "416": {"_meta": {"title": "Load Checkpoint"}, "class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"}}, "436": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["697", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "446": {"_meta": {"title": "CropFaces"}, "class_type": "CropFaces", "inputs": {"crop_factor": 2, "crop_size": 1024, "faces": ["447", 0], "mask_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "447": {"_meta": {"title": "DetectFaces"}, "class_type": "DetectFaces", "inputs": {"image": ["592", 0], "max_size": 1024, "min_size": 64, "threshold": 0.1}}, "462": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "${FACE.extInfo['faceImage']}", "upload": "image"}}, "474": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["771", 0], "image1": ["475", 0]}}, "475": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "476": {"_meta": {"title": "Load Image"}, "class_type": "LoadImage", "inputs": {"image": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "upload": "image"}}, "477": {"_meta": {"title": "🔀 CR Image Input Switch"}, "class_type": "CR Image Input Switch", "inputs": {"Input": ["770", 0], "image1": ["476", 0]}}, "559": {"_meta": {"title": "Make Image List"}, "class_type": "ImpactMakeImageList", "inputs": {"image1": ["462", 0], "image2": ["474", 0], "image3": ["477", 0]}}, "561": {"_meta": {"title": "🔧 Image Resize"}, "class_type": "ImageResize+", "inputs": {"condition": "upscale if smaller", "height": 2048, "image": ["559", 0], "interpolation": "lanc<PERSON>s", "method": "keep proportion", "multiple_of": 0, "width": 2048}}, "570": {"_meta": {"title": "Simple Detector (SEGS)"}, "class_type": "ImpactSimpleDetectorSEGS", "inputs": {"bbox_detector": ["610", 0], "bbox_dilation": 0, "bbox_threshold": 0.5, "crop_factor": 3, "drop_size": 10, "image": ["561", 0], "post_dilation": 0, "sam_mask_hint_threshold": 0.7, "sub_bbox_expansion": 0, "sub_dilation": 0, "sub_threshold": 0.5}}, "574": {"_meta": {"title": "SEGS to MASK (combined)"}, "class_type": "SegsToCombinedMask", "inputs": {"segs": ["597", 0]}}, "576": {"_meta": {"title": "Bounded Image Crop with Mask"}, "class_type": "Bounded Image Crop with Mask", "inputs": {"image": ["561", 0], "mask": ["580", 0], "padding_bottom": 64, "padding_left": 64, "padding_right": 64, "padding_top": 64}}, "580": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 0, "enabled": true, "grow": 256, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["574", 0]}}, "592": {"_meta": {"title": "Image List To Image Batch"}, "class_type": "easy imageListToImageBatch", "inputs": {"images": ["576", 0]}}, "597": {"_meta": {"title": "SEGS Filter (ordered)"}, "class_type": "ImpactSEGSOrderedFilter", "inputs": {"order": true, "segs": ["570", 0], "take_count": 1, "take_start": 0, "target": "area(=w*h)"}}, "610": {"_meta": {"title": "UltralyticsDetectorProvider"}, "class_type": "UltralyticsDetectorProvider", "inputs": {"model_name": "bbox/face_yolov8m.pt"}}, "618": {"_meta": {"title": "Apply ControlNet"}, "class_type": "ControlNetApplyAdvanced", "inputs": {"control_net": ["619", 0], "end_percent": 0.5, "image": ["414", 0], "negative": ["412", 2], "positive": ["412", 1], "start_percent": 0, "strength": 0.1, "vae": ["416", 2]}}, "619": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"}}, "624": {"_meta": {"title": "Human Segmentation"}, "class_type": "easy humanSegmentation", "inputs": {"confidence": 0.4, "crop_multi": 0, "image": ["414", 0], "mask_components": "13", "method": "human_parsing_lip"}}, "670": {"_meta": {"title": "BasicGuider"}, "class_type": "BasicGuider", "inputs": {"conditioning": ["672", 0], "model": ["683", 0]}}, "671": {"_meta": {"title": "KSamplerSelect"}, "class_type": "KSamplerSelect", "inputs": {"sampler_name": "euler_ancestral"}}, "672": {"_meta": {"title": "InpaintModelConditioning"}, "class_type": "InpaintModelConditioning", "inputs": {"mask": ["701", 0], "negative": ["698", 1], "noise_mask": true, "pixels": ["684", 0], "positive": ["673", 0], "vae": ["270", 0]}}, "673": {"_meta": {"title": "FluxGuidance"}, "class_type": "FluxGuidance", "inputs": {"conditioning": ["698", 0], "guidance": 2}}, "674": {"_meta": {"title": "Load ControlNet Model"}, "class_type": "ControlNetLoader", "inputs": {"control_net_name": "FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"}}, "676": {"_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}, "class_type": "ConrainGrowMaskWithBlur", "inputs": {"blur_radius": 15, "decay_factor": 1, "expand": -10, "fill_holes": false, "flip_input": false, "incremental_expandrate": 0, "lerp_alpha": 1, "mask": ["693", 2], "tapered_corners": true}}, "678": {"_meta": {"title": "Sapiens<PERSON><PERSON>der"}, "class_type": "Sapiens<PERSON><PERSON>der", "inputs": {"convert_torchscript_to_bf16": true, "depth_ckpt": "none", "dtype": "float32_torch", "minimum_person_height": 0.5, "normal_ckpt": "none", "pose_ckpt": "none", "remove_background": true, "seg_ckpt": "seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "seg_pellete": true, "show_pose_object": false, "use_yolo": false}}, "679": {"_meta": {"title": "BasicScheduler"}, "class_type": "BasicScheduler", "inputs": {"denoise": 0.65, "model": ["753", 0], "scheduler": "sgm_uniform", "steps": 20}}, "681": {"_meta": {"title": "SamplerCustomAdvanced"}, "class_type": "SamplerCustomAdvanced", "inputs": {"guider": ["670", 0], "latent_image": ["705", 0], "noise": ["682", 0], "sampler": ["671", 0], "sigmas": ["679", 0]}}, "682": {"_meta": {"title": "RandomNoise"}, "class_type": "RandomNoise", "inputs": {"noise_seed": 745052156325993}}, "683": {"_meta": {"title": "Differential Diffusion"}, "class_type": "DifferentialDiffusion", "inputs": {"model": ["753", 0]}}, "684": {"_meta": {"title": "Upscale Image By"}, "class_type": "ImageScaleBy", "inputs": {"image": ["693", 0], "scale_by": ["692", 0], "upscale_method": "lanc<PERSON>s"}}, "687": {"_meta": {"title": "ConditioningZeroOut"}, "class_type": "ConditioningZeroOut", "inputs": {"conditioning": ["688", 0]}}, "688": {"_meta": {"title": "CLIP Text Encode (Positive Prompt)"}, "class_type": "CLIPTextEncode", "inputs": {"clip": ["271", 0], "text": ["331", 0]}}, "692": {"_meta": {"title": "Upscale Size Calculator"}, "class_type": "UpscaleSizeCalculator", "inputs": {"image": ["693", 0], "target_size": 1024}}, "693": {"_meta": {"title": "Bounded Image Crop With Mask LR v3"}, "class_type": "BoundedImageCropWithMask_v3_LR", "inputs": {"expand_B": 20, "expand_LRU": 20, "image": ["269", 0], "mask": ["752", 0]}}, "694": {"_meta": {"title": "SetUnionControlNetType"}, "class_type": "SetUnionControlNetType", "inputs": {"control_net": ["674", 0], "type": "canny/lineart/anime_lineart/mlsd"}}, "695": {"_meta": {"title": "<PERSON><PERSON>"}, "class_type": "<PERSON><PERSON>", "inputs": {"high_threshold": 0.5, "image": ["684", 0], "low_threshold": 0.1}}, "697": {"_meta": {"title": "VAE Decode"}, "class_type": "VAEDecode", "inputs": {"samples": ["681", 0], "vae": ["270", 0]}}, "698": {"_meta": {"title": "Apply ControlNet"}, "class_type": "ControlNetApplyAdvanced", "inputs": {"control_net": ["694", 0], "end_percent": 0.5, "image": ["695", 0], "negative": ["687", 0], "positive": ["688", 0], "start_percent": 0, "strength": 0.6, "vae": ["270", 0]}}, "701": {"_meta": {"title": "Mask Upscale LR"}, "class_type": "MaskUpscale_LR", "inputs": {"mask": ["676", 0], "scale_by": ["692", 0]}}, "703": {"_meta": {"title": "SapiensSampler"}, "class_type": "SapiensSampler", "inputs": {"BG_B": 255, "BG_G": 255, "BG_R": 255, "add_seg_index": "3,23,24,25,26,27", "image": ["269", 0], "model": ["678", 0], "save_pose": false, "seg_select": "2.<PERSON>_<PERSON>"}}, "705": {"_meta": {"title": "Repeat Latent Batch"}, "class_type": "RepeatLatentBatch", "inputs": {"amount": 1, "samples": ["672", 2]}}, "725": {"_meta": {"title": "Bounded Image Blend with Mask"}, "class_type": "Bounded Image Blend with Mask", "inputs": {"blend_factor": 1, "feathering": 0, "source": ["410", 0], "target": ["269", 0], "target_bounds": ["693", 1], "target_mask": ["676", 0]}}, "752": {"_meta": {"title": "conrain python executor"}, "class_type": "ConrainPythonExecutor", "inputs": {"any_a": ["703", 4], "call_code": "import cv2\nimport numpy as np\nimport torch\n\ndef filter_mask(mask):\n    \"\"\"\n    输入torch tensor格式的mask，形状为【通道，高度，宽度】或【高度，宽度】\n    计算连通域，返回只有最大连通域的mask，其他区域设置为0\n    \"\"\"\n    # 保存原始设备和数据类型\n    original_device = mask.device\n    original_dtype = mask.dtype\n    \n    # 转换为numpy处理\n    if mask.dim() == 3:  # 通道处理 [channel, height, width]\n        num_channels = mask.shape[0]\n        new_masks = []\n        \n        for i in range(num_channels):\n            single_mask = mask[i].cpu().numpy()\n            processed_mask = _process_single_mask(single_mask)\n            new_masks.append(torch.from_numpy(processed_mask))\n        \n        result = torch.stack(new_masks)\n    elif mask.dim() == 2:  # 单个mask [height, width]\n        single_mask = mask.cpu().numpy()\n        processed_mask = _process_single_mask(single_mask)\n        result = torch.from_numpy(processed_mask)\n    else:\n        raise ValueError(\"mask维度必须是2D或3D\")\n    \n    # 转换回原始设备和数据类型\n    return result.to(device=original_device, dtype=original_dtype)\n\ndef _process_single_mask(mask):\n    \"\"\"处理单个2D mask\"\"\"\n    # 数据预处理：确保正确的二值化\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        # 如果是浮点数，假设范围是0-1，转换为0-255\n        mask_normalized = (mask * 255).astype(np.uint8)\n    else:\n        mask_normalized = mask.astype(np.uint8)\n    \n    # 二值化处理，确保只有0和255\n    _, binary_mask = cv2.threshold(mask_normalized, 127, 255, cv2.THRESH_BINARY)\n    \n    # 形态学开运算，去除小噪声\n    kernel = np.ones((3,3), np.uint8)\n    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)\n    \n    # 计算连通域\n    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_mask)\n    \n    if num_labels <= 1:  # 只有背景，没有前景\n        return np.zeros_like(mask)\n    \n    # 获取最大的连通域（排除背景）\n    max_area_index = np.argmax(stats[1:, cv2.CC_STAT_AREA]) + 1\n    \n    # 创建新的mask，只有最大的连通域为1，其他为0\n    new_mask = (labels == max_area_index).astype(mask.dtype)\n    \n    # 如果原始数据是浮点型，将结果转换回0-1范围\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        new_mask = new_mask.astype(mask.dtype)\n    \n    return new_mask\n\ndef call(any_a, any_b, any_c, any_d):\n    new_mask = filter_mask(any_a)\n    if torch.all(new_mask == 0):\n        blank_size = 4\n        # 设置左上角2x2区域为1\n        if new_mask.dim() == 2:\n            new_mask[0:blank_size, 0:blank_size] = 1\n        else:  # 3D\n            new_mask[0, 0:blank_size, 0:blank_size] = 1\n    return [new_mask]"}}, "753": {"_meta": {"title": "人脸lora"}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": {"clip": ["271", 0], "lora_name": "${FACE.extInfo.faceLora}", "model": ["377", 0], "strength_clip": "1", "strength_model": "1"}, "disable": "${isLoraFace?then('false','true')}"}, "754": {"_meta": {"title": "<PERSON> Grow Fast"}, "class_type": "MaskFastGrow", "inputs": {"blur": 32, "enabled": true, "grow": 64, "high_limit": 1, "invert_mask": false, "low_limit": 0, "mask": ["414", 1]}}, "770": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"}}, "771": {"_meta": {"title": "String to Integer"}, "class_type": "JWStringToInteger", "inputs": {"text": "<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"config": {}, "extra": {"ds": {"offset": [2178.4748502650095, 3018.233424432685], "scale": 0.15772225476662696}}, "groups": [{"id": 3, "bounding": [5985.7255859375, -2473.57763671875, 2682, 634], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换背景"}, {"id": 5, "bounding": [613.629638671875, -1717.2734375, 2253, 1344], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "出图"}, {"id": 6, "bounding": [5919.30859375, -1662.13818359375, 2689, 694], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "保存图片"}, {"id": 7, "bounding": [-1243.5155029296875, -1717.443115234375, 1584.10302734375, 1333.3258056640625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "模型加载"}, {"id": 8, "bounding": [3680.31640625, -960.367431640625, 1901.2747802734375, 953.2968139648438], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "换脸"}, {"id": 10, "bounding": [3692.35400390625, -2275.424072265625, 1887.6405029296875, 1281.4771728515625], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "人脸原图"}, {"id": 17, "bounding": [1157.142822265625, 17.7085609436035, 1472.12255859375, 704.690673828125], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 18, "bounding": [-1236.62939453125, 5.20417594909668, 2190.35888671875, 714.1162109375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}, {"id": 19, "bounding": [3657.97802734375, 92.7929916381836, 1952.30078125, 716.3968505859375], "color": "#3f789e", "flags": {}, "font_size": 24, "title": "Group"}], "last_link_id": 1426, "last_node_id": 772, "links": [[318, 200, 0, 185, 2, "STRING"], [319, 201, 0, 185, 0, "STRING"], [386, 177, 1, 216, 2, "STRING"], [392, 177, 0, 216, 1, "STRING"], [394, 177, 0, 232, 1, "STRING"], [395, 233, 0, 232, 0, "STRING"], [396, 236, 0, 235, 0, "IMAGE"], [432, 235, 0, 258, 0, "IMAGE"], [441, 236, 0, 261, 0, "IMAGE"], [442, 261, 4, 248, 0, "INT"], [443, 261, 5, 248, 1, "INT"], [448, 266, 0, 263, 1, "FLOAT"], [453, 283, 0, 268, 1, "STRING"], [455, 270, 0, 269, 1, "VAE"], [456, 277, 0, 273, 0, "NOISE"], [457, 276, 0, 273, 1, "GUIDER"], [458, 274, 0, 273, 2, "SAMPLER"], [459, 275, 0, 273, 3, "SIGMAS"], [460, 279, 0, 273, 4, "LATENT"], [461, 280, 0, 275, 0, "MODEL"], [462, 280, 0, 276, 0, "MODEL"], [463, 278, 0, 276, 1, "CONDITIONING"], [464, 268, 0, 278, 0, "CONDITIONING"], [472, 282, 0, 283, 0, "STRING"], [473, 282, 0, 287, 0, "*"], [474, 286, 0, 288, 0, "*"], [475, 287, 0, 185, 1, "STRING"], [476, 288, 0, 185, 3, "STRING"], [499, 298, 0, 296, 0, "MODEL"], [500, 298, 1, 296, 1, "CLIP"], [513, 296, 1, 268, 0, "CLIP"], [529, 248, 0, 316, 1, "IMAGE"], [530, 258, 0, 316, 0, "IMAGE"], [531, 235, 1, 316, 2, "MASK"], [532, 316, 0, 266, 0, "IMAGE"], [533, 316, 0, 263, 0, "IMAGE"], [539, 177, 1, 320, 2, "STRING"], [540, 185, 0, 320, 0, "STRING"], [541, 232, 0, 320, 1, "STRING"], [581, 349, 0, 351, 0, "*"], [582, 351, 0, 352, 0, "STRING"], [583, 350, 0, 353, 0, "*"], [584, 353, 0, 354, 0, "STRING"], [587, 284, 0, 350, 0, "*"], [589, 284, 0, 355, 2, "INT"], [590, 285, 0, 355, 1, "INT"], [591, 285, 0, 349, 0, "*"], [592, 354, 0, 280, 2, "INT"], [593, 352, 0, 280, 1, "INT"], [594, 354, 0, 279, 1, "INT"], [595, 352, 0, 279, 0, "INT"], [596, 273, 0, 269, 0, "LATENT"], [602, 263, 0, 355, 0, "IMAGE"], [613, 365, 4, 367, 0, "INT"], [614, 365, 5, 367, 1, "INT"], [615, 367, 0, 266, 1, "INT"], [601, 269, 0, 339, 0, "*"], [627, 297, 0, 298, 0, "MODEL"], [628, 297, 1, 298, 1, "CLIP"], [642, 375, 0, 377, 0, "MODEL"], [644, 373, 0, 377, 2, "INT"], [647, 272, 0, 375, 0, "MODEL"], [651, 378, 0, 280, 0, "MODEL"], [662, 379, 0, 385, 0, "*"], [663, 385, 0, 378, 1, "MODEL"], [664, 271, 0, 386, 0, "*"], [665, 386, 0, 297, 1, "CLIP"], [674, 416, 2, 392, 2, "VAE"], [676, 402, 0, 392, 4, "MASK"], [684, 436, 0, 399, 0, "FACE"], [702, 399, 0, 410, 1, "FACE"], [703, 409, 0, 410, 2, "IMAGE"], [705, 414, 2, 410, 4, "WARP"], [706, 412, 0, 411, 0, "MODEL"], [709, 392, 2, 411, 3, "LATENT"], [710, 389, 0, 412, 0, "INSTANTID"], [711, 390, 0, 412, 1, "FACEANALYSIS"], [712, 391, 0, 412, 2, "CONTROL_NET"], [713, 446, 0, 412, 3, "IMAGE"], [715, 397, 0, 412, 5, "CONDITIONING"], [716, 398, 0, 412, 6, "CONDITIONING"], [718, 411, 0, 413, 0, "LATENT"], [719, 416, 2, 413, 1, "VAE"], [720, 399, 0, 414, 0, "FACE"], [786, 331, 0, 457, 0, "*"], [788, 457, 0, 397, 1, "STRING"], [813, 475, 0, 474, 0, "IMAGE"], [818, 480, 0, 462, 0, "COMBO"], [819, 481, 0, 475, 0, "COMBO"], [820, 482, 0, 476, 0, "COMBO"], [778, 152, 0, 423, 0, "IMAGE"], [779, 152, 0, 427, 0, "IMAGE"], [780, 152, 0, 428, 0, "IMAGE"], [856, 355, 0, 216, 0, "IMAGE"], [789, 455, 0, 458, 0, "*"], [790, 458, 0, 422, 0, "IMAGE"], [944, 272, 0, 377, 1, "MODEL"], [946, 377, 0, 297, 0, "MODEL"], [955, 416, 1, 397, 0, "CLIP"], [956, 416, 1, 398, 0, "CLIP"], [979, 549, 0, 392, 3, "IMAGE"], [980, 549, 0, 412, 7, "IMAGE"], [985, 550, 0, 549, 0, "*"], [989, 414, 0, 409, 0, "IMAGE"], [998, 474, 0, 559, 0, "IMAGE"], [1001, 462, 0, 559, 0, "IMAGE"], [1003, 476, 0, 559, 1, "IMAGE"], [1007, 476, 0, 559, 1, "IMAGE"], [1009, 559, 0, 561, 0, "IMAGE"], [1037, 579, 0, 576, 0, "IMAGE"], [1039, 574, 0, 580, 0, "MASK"], [1040, 580, 0, 576, 1, "MASK"], [1044, 477, 0, 559, 1, "IMAGE"], [1051, 476, 0, 559, 1, "IMAGE"], [1060, 477, 0, 559, 1, "IMAGE"], [1064, 476, 0, 559, 1, "IMAGE"], [1069, 474, 0, 559, 1, "IMAGE"], [1070, 477, 0, 559, 2, "IMAGE"], [1071, 447, 0, 446, 0, "FACE"], [1072, 576, 0, 592, 0, "IMAGE"], [1073, 592, 0, 447, 0, "IMAGE"], [1084, 570, 0, 597, 0, "SEGS"], [1086, 597, 0, 574, 0, "SEGS"], [1089, 476, 0, 477, 0, "IMAGE"], [1092, 561, 0, 579, 0, "*"], [1093, 561, 0, 570, 1, "IMAGE"], [1121, 414, 0, 616, 0, "*"], [1126, 619, 0, 618, 2, "CONTROL_NET"], [1127, 412, 1, 618, 0, "CONDITIONING"], [1128, 412, 2, 618, 1, "CONDITIONING"], [1132, 549, 0, 618, 3, "IMAGE"], [1133, 416, 2, 618, 4, "VAE"], [1134, 392, 0, 411, 1, "CONDITIONING"], [1135, 392, 1, 411, 2, "CONDITIONING"], [1136, 618, 0, 392, 0, "CONDITIONING"], [1137, 618, 1, 392, 1, "CONDITIONING"], [1142, 413, 0, 409, 1, "IMAGE"], [1145, 414, 0, 624, 0, "IMAGE"], [1226, 683, 0, 670, 0, "MODEL"], [1227, 672, 0, 670, 1, "CONDITIONING"], [1228, 673, 0, 672, 0, "CONDITIONING"], [1229, 698, 1, 672, 1, "CONDITIONING"], [1230, 706, 0, 672, 2, "VAE"], [1231, 685, 0, 672, 3, "IMAGE"], [1232, 699, 0, 672, 4, "MASK"], [1233, 698, 0, 673, 0, "CONDITIONING"], [1234, 692, 0, 675, 0, "*"], [1235, 693, 2, 676, 0, "MASK"], [1237, 695, 0, 680, 0, "*"], [1238, 682, 0, 681, 0, "NOISE"], [1239, 670, 0, 681, 1, "GUIDER"], [1240, 671, 0, 681, 2, "SAMPLER"], [1241, 679, 0, 681, 3, "SIGMAS"], [1242, 705, 0, 681, 4, "LATENT"], [1244, 693, 0, 684, 0, "IMAGE"], [1245, 675, 0, 684, 1, "FLOAT"], [1246, 684, 0, 685, 0, "*"], [1248, 688, 0, 687, 0, "CONDITIONING"], [1249, 689, 0, 688, 0, "CLIP"], [1255, 693, 0, 692, 0, "IMAGE"], [1256, 677, 0, 693, 0, "IMAGE"], [1258, 674, 0, 694, 0, "CONTROL_NET"], [1259, 685, 0, 695, 0, "IMAGE"], [1261, 681, 0, 697, 0, "LATENT"], [1262, 706, 0, 697, 1, "VAE"], [1263, 688, 0, 698, 0, "CONDITIONING"], [1264, 687, 0, 698, 1, "CONDITIONING"], [1265, 694, 0, 698, 2, "CONTROL_NET"], [1266, 680, 0, 698, 3, "IMAGE"], [1267, 706, 0, 698, 4, "VAE"], [1268, 701, 0, 699, 0, "*"], [1270, 676, 0, 701, 0, "MASK"], [1271, 675, 0, 701, 1, "FLOAT"], [1273, 678, 0, 703, 0, "MODEL_SAPIEN"], [1274, 677, 0, 703, 1, "IMAGE"], [1276, 672, 2, 705, 0, "LATENT"], [1277, 270, 0, 706, 0, "*"], [1282, 331, 0, 688, 1, "STRING"], [1285, 386, 0, 689, 0, "*"], [1300, 410, 0, 456, 0, "*"], [1319, 677, 0, 726, 0, "*"], [1320, 726, 0, 725, 0, "IMAGE"], [1321, 676, 0, 727, 0, "*"], [1323, 727, 0, 725, 1, "MASK"], [1324, 693, 1, 728, 0, "*"], [1326, 728, 0, 725, 2, "IMAGE_BOUNDS"], [1328, 456, 0, 725, 3, "IMAGE"], [1335, 616, 0, 550, 0, "*"], [1336, 604, 0, 436, 0, "IMAGE"], [1337, 604, 0, 410, 0, "IMAGE"], [1338, 697, 0, 604, 0, "*"], [1341, 296, 0, 378, 0, "MODEL"], [1343, 610, 0, 570, 0, "BBOX_DETECTOR"], [1357, 269, 0, 455, 0, "*"], [1388, 624, 1, 402, 0, "MASK"], [1392, 455, 0, 677, 0, "*"], [1393, 703, 4, 752, 0, "*"], [1394, 752, 0, 693, 1, "MASK"], [1395, 377, 0, 753, 0, "MODEL"], [1396, 386, 0, 753, 1, "CLIP"], [1397, 753, 0, 683, 0, "MODEL"], [1398, 753, 0, 679, 0, "MODEL"], [1399, 414, 1, 754, 0, "MASK"], [1400, 754, 0, 410, 3, "MASK"], [1412, 416, 0, 412, 4, "MODEL"], [1415, 764, 0, 236, 0, "IMAGE"], [1416, 764, 0, 365, 0, "IMAGE"], [1417, 725, 0, 765, 0, "*"], [1423, 771, 0, 474, 2, "INT"], [1424, 770, 0, 477, 2, "INT"], [1425, 765, 0, 764, 0, "*"], [1426, 269, 0, 772, 0, "*"]], "nodes": [{"id": 177, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 32, "outputs": [{"label": "STRING", "links": [392, 394], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}, {"label": "STRING", "links": [386, 539], "name": "STRING", "shape": 3, "slot_index": 1, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}, {"label": "STRING", "name": "STRING", "shape": 3, "type": "STRING"}], "pos": [6503.51904296875, -1381.140380859375], "properties": {"Node name for S&R": "Text String"}, "size": [315, 190], "type": "Text String", "widgets_values": ["${outputPath}", "${fileNamePrefix}", "", ""]}, {"id": 185, "flags": {}, "inputs": [{"label": "text_a", "link": 319, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 475, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "link": 318, "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "link": 476, "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 54, "outputs": [{"label": "STRING", "links": [540], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [7636.95849609375, -1253.41162109375], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [315, 178], "type": "Text Concatenate", "widgets_values": [" ", "true", "", "", "", ""]}, {"id": 200, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 21, "outputs": [{"label": "STRING", "links": [318], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6110.244140625, -1267.6646728515625], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【negative】:"]}, {"id": 201, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 20, "outputs": [{"label": "STRING", "links": [319], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6107.16748046875, -1419.36865234375], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["【positive】:"]}, {"id": 216, "flags": {}, "inputs": [{"label": "images", "link": 856, "name": "images", "type": "IMAGE"}, {"label": "output_path", "link": 392, "name": "output_path", "type": "STRING", "widget": {"name": "output_path"}}, {"label": "filename_prefix", "link": 386, "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}}], "mode": 0, "order": 136, "outputs": [{"label": "image_cnt", "links": [], "name": "image_cnt", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [8201.634765625, -1526.3419189453125], "properties": {"Node name for S&R": "ConrainImageSave"}, "size": [320, 266], "type": "ConrainImageSave", "widgets_values": ["[time(%Y-%m-%d)]", "ComfyUI", "jpg", 100, 100, "true", "false", "false", "true", "true"]}, {"id": 232, "flags": {}, "inputs": [{"label": "text_a", "link": 395, "name": "text_a", "type": "STRING", "widget": {"name": "text_a"}}, {"label": "text_b", "link": 394, "name": "text_b", "type": "STRING", "widget": {"name": "text_b"}}, {"label": "text_c", "name": "text_c", "type": "STRING", "widget": {"name": "text_c"}}, {"label": "text_d", "name": "text_d", "type": "STRING", "widget": {"name": "text_d"}}], "mode": 0, "order": 48, "outputs": [{"label": "STRING", "links": [541], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6991.95849609375, -1432.4114990234375], "properties": {"Node name for S&R": "Text Concatenate"}, "size": [250, 142], "type": "Text Concatenate", "widgets_values": ["/", "false", "", "", "", ""]}, {"id": 233, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 0, "outputs": [{"label": "STRING", "links": [395], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [6502.95849609375, -1528.411376953125], "properties": {"Node name for S&R": "String to Text"}, "size": [315, 58], "type": "String to Text", "widgets_values": ["output"]}, {"id": 235, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 396, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 133, "outputs": [{"label": "IMAGE", "links": [432], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "MASK", "links": [531], "name": "MASK", "shape": 3, "slot_index": 1, "type": "MASK"}], "pos": [6558.8974609375, -2050.87353515625], "properties": {"Node name for S&R": "InspyrenetRembg"}, "size": [230, 90], "type": "InspyrenetRembg", "widgets_values": ["default"]}, {"id": 236, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1415, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 131, "outputs": [{"label": "IMAGE", "links": [396, 402], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"label": "show_help", "name": "show_help", "shape": 3, "type": "STRING"}], "pos": [6102.8974609375, -2119.87353515625], "properties": {"Node name for S&R": "CR Upscale Image"}, "size": [315, 222], "type": "CR Upscale Image", "widgets_values": ["4xUltrasharp_4xUltrasharpV10.pt", "rescale", 2, "${height}", "lanc<PERSON>s", "true", 8]}, {"id": 240, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 26, "outputs": [], "pos": [6988.8974609375, -2370.87353515625], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["切换颜色，输入以下颜色的数值红色：16711680绿色：65280蓝色：255白色：16777215黑色：0"]}, {"id": 248, "bgcolor": "#333333", "color": "#474747", "flags": {"collapsed": false}, "inputs": [{"label": "width", "link": 442, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"label": "height", "link": 443, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": "${isPureBg?then(0,4)}", "order": 137, "outputs": [{"label": "IMAGE", "links": [529], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7013.8974609375, -2189.87353515625], "properties": {"Node name for S&R": "EmptyImage"}, "size": [231.5089111328125, 120.12616729736328], "type": "EmptyImage", "widgets_values": [512, 512, 1, "${pureRgb}"]}, {"id": 258, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 432, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 135, "outputs": [{"label": "IMAGE", "links": [530], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [6999.8974609375, -1972.87353515625], "properties": {"Node name for S&R": "ImageRGBA2RGB"}, "size": [252, 26], "type": "ImageRGBA2RGB", "widgets_values": []}, {"id": 261, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 441, "name": "image", "type": "IMAGE"}], "mode": "${isPureBg?then(0,4)}", "order": 138, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [442], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [443], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [6564.8974609375, -2276.87353515625], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 263, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 533, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 448, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": "${isPureBg?then(0,4)}", "order": 139, "outputs": [{"label": "IMAGE", "links": [602], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [8323.8857421875, -2294.87353515625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [228.9691162109375, 78], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 0.4]}, {"id": 266, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"label": "image", "link": 532, "name": "image", "type": "IMAGE"}, {"link": 615, "name": "target_size", "type": "INT", "widget": {"name": "target_size"}}], "mode": "${isPureBg?then(0,4)}", "order": 140, "outputs": [{"label": "rescale_factor", "links": [448], "name": "rescale_factor", "shape": 3, "slot_index": 0, "type": "FLOAT"}, {"label": "rescale_width", "name": "rescale_width", "shape": 3, "type": "INT"}, {"label": "recover_factor", "name": "recover_factor", "shape": 3, "type": "FLOAT"}, {"label": "recover_width", "name": "recover_width", "shape": 3, "type": "INT"}], "pos": [8009.8974609375, -2182.87353515625], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [220, 118], "type": "UpscaleSizeCalculator", "widgets_values": ["${height}"]}, {"id": 268, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 513, "name": "clip", "type": "CLIP"}, {"link": 453, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 73, "outputs": [{"links": [464], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1311.6290283203125, -1404.5489501953125], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 54], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 269, "flags": {}, "inputs": [{"link": 596, "name": "samples", "type": "LATENT"}, {"link": 455, "name": "vae", "type": "VAE"}], "mode": 0, "order": 83, "outputs": [{"links": [611, 1357, 1426], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [2576.850341796875, -1327.6995849609375], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 270, "flags": {}, "inputs": [], "mode": 0, "order": 6, "outputs": [{"links": [455, 520, 1277], "name": "VAE", "shape": 3, "slot_index": 0, "type": "VAE"}], "pos": [2301.329833984375, -1185.7738037109375], "properties": {"Node name for S&R": "VAELoader"}, "size": [247.6494903564453, 64.26640319824219], "type": "VAELoader", "widgets_values": ["ae.safetensors"]}, {"id": 271, "flags": {}, "inputs": [], "mode": 0, "order": 1, "outputs": [{"links": [622, 664, 664], "name": "CLIP", "shape": 3, "slot_index": 0, "type": "CLIP"}], "pos": [-1186.1917724609375, -930.2503662109375], "properties": {"Node name for S&R": "DualCLIPLoader"}, "size": [315, 106], "type": "DualCLIPLoader", "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 272, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": 0, "order": 10, "outputs": [{"links": [621, 647, 647, 944], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-1189.7391357421875, -1156.2449951171875], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "type": "UNETLoader", "widgets_values": ["${baseModelName!'flux1-dev.safetensors'}", "${baseModelDType!'default'}"]}, {"id": 273, "flags": {}, "inputs": [{"link": 456, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 457, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 458, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 459, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 460, "name": "latent_image", "slot_index": 4, "type": "LATENT"}], "mode": 0, "order": 81, "outputs": [{"links": [596], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [2276.************, -1484.35302734375], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [236.8000030517578, 127.************], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 274, "flags": {}, "inputs": [], "mode": 0, "order": 2, "outputs": [{"links": [458], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [1982.************, -1166.6705322265625], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["${samplerName!'euler'}"]}, {"id": 275, "flags": {}, "inputs": [{"link": 461, "name": "model", "slot_index": 0, "type": "MODEL"}], "mode": 0, "order": 78, "outputs": [{"links": [459], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [2003.67236328125, -999.4038696289062], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["${scheduleName!'beta'}", "${(lora.extInfo.increaseSteps)!isPWModel?then(30,20)}", 1]}, {"id": 276, "flags": {}, "inputs": [{"link": 462, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 463, "name": "conditioning", "slot_index": 1, "type": "CONDITIONING"}], "mode": 0, "order": 79, "outputs": [{"links": [457], "name": "GUIDER", "shape": 3, "slot_index": 0, "type": "GUIDER"}], "pos": [1966.************, -1408.1806640625], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 277, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 18, "outputs": [{"links": [456], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [1829.1529541015625, -1579.************], "properties": {"Node name for S&R": "RandomNoise"}, "size": [317.5343933105469, 84.33126831054688], "type": "RandomNoise", "widgets_values": ["${seed}", "fixed"]}, {"id": 278, "bgcolor": "#000", "color": "#222", "flags": {}, "inputs": [{"link": 464, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 76, "outputs": [{"links": [463], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [1672.114501953125, -1396.1************], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": ["${lora.extInfo.cfg}"]}, {"id": 279, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 595, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 594, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 65, "outputs": [{"links": [460], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [1691.************, -1161.************], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "size": [210, 86.50716400146484], "type": "EmptySD3LatentImage", "widgets_values": ["${width}", "${height}", "${imageNum}"]}, {"id": 280, "flags": {}, "inputs": [{"link": 651, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 593, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 592, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 75, "outputs": [{"links": [461, 462], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [1565.364990234375, -882.87890625], "properties": {"Node name for S&R": "ModelSamplingFlux"}, "size": [210, 122], "type": "ModelSamplingFlux", "widgets_values": [1.15, 0.5, "${width}", "${height}"]}, {"id": 282, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 16, "outputs": [{"links": [472, 473], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [740.9517822265625, -978.5265502929688], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [400, 200], "title": "正向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${lora.tags}${lora.extInfo.extTags}${lora.extInfo.features}\n\n\n${SCENE.extInfo.lens}${SCENE.tags}${SCENE.extInfo.posture}${SCENE.extInfo.style}\n\n\n${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", "${promptSeed}", "fixed"]}, {"id": 283, "flags": {}, "inputs": [{"link": 472, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 43, "outputs": [{"links": [453], "name": "STRING", "shape": 6, "slot_index": 0, "type": "STRING"}], "pos": [1292.5960693359375, -1219.8851318359375], "properties": {"Node name for S&R": "ShowText|pysssss"}, "size": [256.63372802734375, 226], "type": "ShowText|pysssss", "widgets_values": ["", "(linrun2111:1.3), front view,whole body,The model is wearing A black t-shirt, The t-shirt features white piping along the shoulders.White molding from neckline to cuff,The model is wearing  mgs2222 brown cargo pants underneath. The model is wearing  a gray cap.wearing outdoor sports sunglasses. The model is wearing mgs2222 hiking boots. The  mgs2222 hiking boots have thick soles. The model is holding trekking poles. \n\n\nnulla rocky outdoor terrain with scattered stones and a clear blue sky.hiking.standing with one leg slightly bent and the other straight, holding trekking poles in both hands.\n\n\na mgm3004 male model, short light brown hair styled with texture,"]}, {"id": 284, "flags": {}, "inputs": [], "mode": 0, "order": 3, "outputs": [{"links": [587, 589], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1318.2945556640625, -531.36474609375], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "height", "type": "CR Seed", "widgets_values": ["${height}", "fixed"]}, {"id": 285, "flags": {}, "inputs": [], "mode": 0, "order": 4, "outputs": [{"links": [590, 591], "name": "seed", "shape": 3, "slot_index": 0, "type": "INT"}, {"name": "show_help", "shape": 3, "type": "STRING"}], "pos": [1304.2945556640625, -717.3659057617188], "properties": {"Node name for S&R": "CR Seed"}, "size": [243.4204864501953, 102], "title": "width", "type": "CR Seed", "widgets_values": ["${width}", "fixed"]}, {"id": 286, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 12, "outputs": [{"links": [474], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [739.959716796875, -684.6459350585938], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [421.542236328125, 163.53213500976562], "title": "负向提示词", "type": "ConrainRandomPrompts", "widgets_values": ["(blurry:1.3), (Brea<PERSON> exposed:1.2), (<PERSON><PERSON> naked:1.2), (genital nudity:1.2), (porn:1.2), watermark, 3 hands, 3 legs, bad feet, 2 humans, 2 images, 2 pictures, no head, (worst quality:1.1), (mannequin:1.2), , (low quality:1.1), bad quality, NSFW, text, watermark, no human. ", 1033, "fixed"]}, {"id": 287, "flags": {}, "inputs": [{"link": 473, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 44, "outputs": [{"links": [475], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2508.18603515625, -866.1821899414062], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 288, "flags": {}, "inputs": [{"link": 474, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 40, "outputs": [{"links": [476], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [2473.18603515625, -735.1822509765625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 296, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 499, "name": "model", "type": "MODEL"}, {"link": 500, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 70, "outputs": [{"links": [514, 654, 1341], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [513, 510, 513, 630, 631], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [709.6905517578125, -1195.457763671875], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [499.25970458984375, 126], "title": "服装lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${lora.loraName}", "${loraStrength}", 1]}, {"id": 297, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 946, "name": "model", "type": "MODEL"}, {"link": 665, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 59, "outputs": [{"links": [627], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [628, 625], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [720.578369140625, -1607.893310546875], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [477.3377990722656, 128.31455993652344], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "${FACE.extInfo.faceLoraStrength}", "${FACE.extInfo.faceLoraStrength}"]}, {"id": 298, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 627, "name": "model", "type": "MODEL"}, {"link": 628, "name": "clip", "type": "CLIP"}], "mode": "${(isPureBg||!isAntiBlueLora)?then(4,0)}", "order": 66, "outputs": [{"links": [499], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [500], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [713.581298828125, -1405.7806396484375], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [491.7470703125, 126], "title": "风格lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${sceneLora}", "${sceneLoraStrength}", 1]}, {"id": 316, "flags": {}, "inputs": [{"link": 530, "name": "source", "type": "IMAGE"}, {"link": 529, "name": "destination", "type": "IMAGE"}, {"link": 531, "name": "mask", "shape": 7, "type": "MASK"}], "mode": "${isPureBg?then(0,4)}", "order": 141, "outputs": [{"links": [532, 533], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7431.8974609375, -2096.87353515625], "properties": {"Node name for S&R": "ConrainImageCompositeMasked"}, "size": [252, 146], "type": "ConrainImageCompositeMasked", "widgets_values": [0, 0, false]}, {"id": 320, "flags": {}, "inputs": [{"link": 540, "name": "text", "type": "STRING", "widget": {"name": "text"}}, {"link": 541, "name": "path", "type": "STRING", "widget": {"name": "path"}}, {"link": 539, "name": "filename", "type": "STRING", "widget": {"name": "filename"}}], "mode": 0, "order": 63, "outputs": [], "pos": [8207.953125, -1181.4114990234375], "properties": {"Node name for S&R": "ConrainTextSave"}, "size": [315, 106], "type": "ConrainTextSave", "widgets_values": ["", "./ComfyUI/output/", ""]}, {"id": 331, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [], "mode": 0, "order": 17, "outputs": [{"links": [575, 786, 575, 1282], "name": "prompt", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [2354.7509765625, -1010.1593627929688], "properties": {"Node name for S&R": "ConrainRandomPrompts"}, "size": [319.1407165527344, 134.37188720703125], "title": "修脸提示词", "type": "ConrainRandomPrompts", "widgets_values": ["${FACE.extTags}${FACE.tags}${FACE.extInfo.expression}${FACE.extInfo.hairstyle}", 1335, "fixed"]}, {"id": 349, "flags": {"collapsed": true}, "inputs": [{"link": 591, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 35, "outputs": [{"links": [581], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1651.5965576171875, -641.267944335938], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 350, "flags": {"collapsed": true}, "inputs": [{"link": 587, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 34, "outputs": [{"links": [583], "name": "any", "shape": 3, "slot_index": 0, "type": "*"}], "pos": [1674.5916748046875, -540.256958007812], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [368.1804504394531, 203.2705841064453], "type": "ConrainPythonExecutor", "widgets_values": ["def call(any_a, any_b, any_c, any_d):\n\tif any_a/8 == float(int(any_a/8)):\n\t\tresult = any_a\n\telse:\n\t\tresult = (int(any_a/8)+1)*8\n\treturn [str(result)]"]}, {"id": 351, "flags": {}, "inputs": [{"link": 581, "name": "any_a", "type": "*"}], "mode": 0, "order": 51, "outputs": [{"links": [582], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1883.5928955078125, -663.265502929688], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 27.56488609313965], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 352, "flags": {}, "inputs": [{"link": 582, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 58, "outputs": [{"links": [593, 595], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2133.54541015625, -667.265502929688], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 353, "flags": {}, "inputs": [{"link": 583, "name": "any_a", "type": "*"}], "mode": 0, "order": 50, "outputs": [{"links": [584], "name": "STRING", "shape": 3, "slot_index": 0, "type": "STRING"}], "pos": [1954.5938720703125, -533.257568359375], "properties": {"Node name for S&R": "ConrainAnyToStrings"}, "size": [184.8000030517578, 39.813907623291016], "type": "ConrainAnyToStrings", "widgets_values": []}, {"id": 354, "flags": {}, "inputs": [{"link": 584, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 57, "outputs": [{"links": [592, 594], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [2187.53271484375, -512.257568359375], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 34], "type": "JWStringToInteger", "widgets_values": ["0"]}, {"id": 355, "flags": {}, "inputs": [{"link": 602, "name": "image", "type": "IMAGE"}, {"link": 590, "name": "width", "type": "INT", "widget": {"name": "width"}}, {"link": 589, "name": "height", "type": "INT", "widget": {"name": "height"}}], "mode": 0, "order": 142, "outputs": [{"links": [856], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [7678.95849609375, -1556.411376953125], "properties": {"Node name for S&R": "ImageCrop"}, "size": [225.3616943359375, 122.95598602294922], "type": "ImageCrop", "widgets_values": [512, 512, 0, 0]}, {"id": 365, "bgcolor": "#333333", "color": "#474747", "flags": {}, "inputs": [{"label": "image", "link": 1416, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 132, "outputs": [{"label": "width_num", "name": "width_num", "shape": 3, "type": "NUMBER"}, {"label": "height_num", "name": "height_num", "shape": 3, "type": "NUMBER"}, {"label": "width_float", "name": "width_float", "shape": 3, "type": "FLOAT"}, {"label": "height_float", "name": "height_float", "shape": 3, "type": "FLOAT"}, {"label": "width_int", "links": [613], "name": "width_int", "shape": 3, "slot_index": 4, "type": "INT"}, {"label": "height_int", "links": [614], "name": "height_int", "shape": 3, "slot_index": 5, "type": "INT"}], "pos": [7447.1708984375, -2335.321533203125], "properties": {"Node name for S&R": "Image Size to Number"}, "size": [229.20001220703125, 126], "type": "Image Size to Number", "widgets_values": []}, {"id": 367, "flags": {}, "inputs": [{"link": 613, "name": "a", "type": "INT", "widget": {"name": "a"}}, {"link": 614, "name": "b", "type": "INT", "widget": {"name": "b"}}], "mode": 0, "order": 134, "outputs": [{"links": [615], "name": "INT", "shape": 3, "slot_index": 0, "type": "INT"}], "pos": [7741.1708984375, -2260.321533203125], "properties": {"Node name for S&R": "JWIntegerMax"}, "size": [210, 67.1211166381836], "type": "JWIntegerMax", "widgets_values": [0, 0]}, {"id": 373, "flags": {"collapsed": false}, "inputs": [], "mode": 0, "order": 11, "outputs": [{"links": [644, 644], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [-648.970703125, -1132.3956298828125], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 58], "title": "推理加速开关", "type": "JWStringToInteger", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 375, "flags": {}, "inputs": [{"link": 647, "name": "model", "type": "MODEL"}], "mode": 0, "order": 39, "outputs": [{"links": [642], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-695.4215087890625, -691.8218994140625], "properties": {"Node name for S&R": "ApplyFBCacheOnModel"}, "size": [315, 154], "type": "ApplyFBCacheOnModel", "widgets_values": ["diffusion_model", 0.15, 0.2, 0.8, 5]}, {"id": 377, "flags": {"collapsed": false}, "inputs": [{"link": 642, "name": "model1", "shape": 7, "type": "MODEL"}, {"link": 944, "name": "model2", "shape": 7, "type": "MODEL"}, {"link": 644, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 52, "outputs": [{"links": [946, 1395], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"name": "show_help", "type": "STRING"}], "pos": [-357.1766052246094, -1110.5040283203125], "properties": {"Node name for S&R": "CR Model Input Switch"}, "size": [257.191650390625, 78.78076171875], "type": "CR Model Input Switch", "widgets_values": ["${speedUpSwitch?then(1,2)}"]}, {"id": 378, "flags": {"collapsed": true}, "inputs": [{"link": 1341, "name": "model1", "type": "MODEL"}, {"link": 663, "name": "model2", "type": "MODEL"}], "mode": "${((!isFaceAfter&&isPWModel)||isPulidAndInstantId)?then(0,4)}", "order": 72, "outputs": [{"links": [651], "name": "MODEL", "slot_index": 0, "type": "MODEL"}], "pos": [-125.35467529296875, -630.95068359375], "properties": {"Node name for S&R": "ModelMergeFlux1"}, "size": [315, 1566], "title": "合并PW和flux模型", "type": "ModelMergeFlux1", "widgets_values": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, {"id": 379, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [], "mode": "${(isPWModel)?then(0,4)}", "order": 9, "outputs": [{"links": [662, 662], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [-1171.4366455078125, -1402.59912109375], "properties": {"Node name for S&R": "UNETLoader"}, "size": [315, 82], "title": "PW模型", "type": "UNETLoader", "widgets_values": ["pixelwave_flux1_dev_bf16_03.safetensors", "default"]}, {"id": 385, "flags": {}, "inputs": [{"link": 662, "name": "", "type": "*"}], "mode": 0, "order": 38, "outputs": [{"links": [663], "name": "", "slot_index": 0, "type": "MODEL"}], "pos": [-552.3848876953125, -1389.24609375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 386, "flags": {}, "inputs": [{"link": 664, "name": "", "type": "*"}], "mode": 0, "order": 33, "outputs": [{"links": [665, 1285, 1396], "name": "", "slot_index": 0, "type": "CLIP"}], "pos": [-244.59356689453, -919.148193359375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 389, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 24, "outputs": [{"label": "INSTANTID", "links": [710], "name": "INSTANTID", "shape": 3, "slot_index": 0, "type": "INSTANTID"}], "pos": [4439.45947265625, -759.6692504882812], "properties": {"Node name for S&R": "InstantIDModelLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDModelLoader", "widgets_values": ["ip-adapter.bin"]}, {"id": 390, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 23, "outputs": [{"label": "FACEANALYSIS", "links": [711], "name": "FACEANALYSIS", "shape": 3, "slot_index": 0, "type": "FACEANALYSIS"}], "pos": [4440.83056640625, -684.112915039062], "properties": {"Node name for S&R": "InstantIDFaceAnalysis", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [315, 58], "type": "InstantIDFaceAnalysis", "widgets_values": ["CUDA"]}, {"id": 391, "flags": {"collapsed": true}, "inputs": [], "mode": 0, "order": 22, "outputs": [{"label": "CONTROL_NET", "links": [712], "name": "CONTROL_NET", "shape": 3, "slot_index": 0, "type": "CONTROL_NET"}], "pos": [4440.48388671875, -596.2993774414062], "properties": {"Node name for S&R": "ControlNetLoader", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [378.708740234375, 58], "type": "ControlNetLoader", "widgets_values": ["control_instant_id_sdxl.safetensors"]}, {"id": 392, "flags": {}, "inputs": [{"link": 1136, "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"link": 1137, "name": "negative", "type": "CONDITIONING"}, {"link": 674, "name": "vae", "type": "VAE"}, {"link": 979, "name": "pixels", "type": "IMAGE"}, {"link": 676, "name": "mask", "type": "MASK"}], "mode": 0, "order": 122, "outputs": [{"links": [1134], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"links": [1135], "name": "negative", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"links": [709], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [4979.54736328125, -550.9163208007812], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [210, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 397, "bgcolor": "#000", "color": "#222", "flags": {"collapsed": false}, "inputs": [{"link": 955, "name": "clip", "type": "CLIP"}, {"link": 788, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 55, "outputs": [{"links": [715], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [4105.27880859375, -804.9226684570312], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [210, 96], "type": "CLIPTextEncode", "widgets_values": ["a 5 year girl"]}, {"id": 398, "bgcolor": "#353", "color": "#232", "flags": {"collapsed": false}, "inputs": [{"link": 956, "name": "clip", "type": "CLIP"}], "mode": 0, "order": 41, "outputs": [{"links": [716], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [3991.1611328125, -612.104370117188], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [239.4051971435547, 91.89370727539062], "type": "CLIPTextEncode", "widgets_values": ["Drawing, comics, anime, cartoons, 3D，filthy，dirty，Deformed, ugly，Low quality, cross-eye"]}, {"id": 399, "flags": {}, "inputs": [{"link": 684, "name": "faces", "type": "FACE"}], "mode": 0, "order": 112, "outputs": [{"links": [702, 720], "name": "filtered", "slot_index": 0, "type": "FACE"}, {"name": "rest", "type": "FACE"}], "pos": [3728.188232421875, -311.67803955078125], "properties": {"Node name for S&R": "OrderedFaceFilter"}, "size": [227.9144744873047, 169.93338012695312], "type": "OrderedFaceFilter", "widgets_values": ["area", "descending", 0, 1]}, {"id": 402, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1388, "name": "mask", "type": "MASK"}], "mode": 0, "order": 118, "outputs": [{"links": [676, 687, 690], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4642.54443359375, -196.25387573242188], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 16, 8, 0, 1, true]}, {"id": 409, "flags": {"collapsed": false}, "inputs": [{"link": 989, "name": "image_ref", "type": "IMAGE"}, {"link": 1142, "name": "image_target", "type": "IMAGE"}], "mode": 0, "order": 125, "outputs": [{"links": [703], "name": "image", "slot_index": 0, "type": "IMAGE"}], "pos": [5319.93359375, -383.676025390625], "properties": {"Node name for S&R": "easy imageColorMatch"}, "size": [210, 102], "type": "easy imageColorMatch", "widgets_values": ["adain", "<PERSON>de", "ComfyUI"]}, {"id": 410, "flags": {}, "inputs": [{"link": 1337, "name": "images", "type": "IMAGE"}, {"link": 702, "name": "face", "type": "FACE"}, {"link": 703, "name": "crop", "type": "IMAGE"}, {"link": 1400, "name": "mask", "type": "MASK"}, {"link": 705, "name": "warp", "type": "WARP"}], "mode": 0, "order": 126, "outputs": [{"links": [1300], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [4934.3994140625, -200.19781494140625], "properties": {"Node name for S&R": "WarpFacesBack"}, "size": [182.46627807617188, 157.38844299316406], "type": "WarpFacesBack", "widgets_values": []}, {"id": 411, "flags": {}, "inputs": [{"link": 706, "name": "model", "type": "MODEL"}, {"link": 1134, "name": "positive", "type": "CONDITIONING"}, {"link": 1135, "name": "negative", "type": "CONDITIONING"}, {"link": 709, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 123, "outputs": [{"links": [718], "name": "LATENT", "slot_index": 0, "type": "LATENT"}], "pos": [5275.3818359375, -849.4486694335938], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "size": [261.8017578125, 262], "type": "K<PERSON><PERSON><PERSON>", "widgets_values": ["${seed}", "fixed", 4, 1, "euler_ancestral", "sgm_uniform", 0.4]}, {"id": 412, "flags": {}, "inputs": [{"label": "instantid", "link": 710, "name": "instantid", "slot_index": 0, "type": "INSTANTID"}, {"label": "insightface", "link": 711, "name": "insightface", "slot_index": 1, "type": "FACEANALYSIS"}, {"label": "control_net", "link": 712, "name": "control_net", "slot_index": 2, "type": "CONTROL_NET"}, {"label": "image", "link": 713, "name": "image", "type": "IMAGE"}, {"label": "model", "link": 1412, "name": "model", "slot_index": 4, "type": "MODEL"}, {"label": "positive", "link": 715, "name": "positive", "slot_index": 5, "type": "CONDITIONING"}, {"label": "negative", "link": 716, "name": "negative", "slot_index": 6, "type": "CONDITIONING"}, {"label": "image_kps", "link": 980, "name": "image_kps", "shape": 7, "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 120, "outputs": [{"label": "MODEL", "links": [706], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"label": "positive", "links": [1127], "name": "positive", "shape": 3, "slot_index": 1, "type": "CONDITIONING"}, {"label": "negative", "links": [1128], "name": "negative", "shape": 3, "slot_index": 2, "type": "CONDITIONING"}], "pos": [4731.416015625, -842.5057373046875], "properties": {"Node name for S&R": "ApplyInstantID", "ttNbgOverride": {"color": "rgba(0,0,0,.8)", "groupcolor": "#444"}}, "size": [210, 266], "type": "ApplyInstantID", "widgets_values": [1, 0, 1]}, {"id": 413, "flags": {"collapsed": false}, "inputs": [{"link": 718, "name": "samples", "type": "LATENT"}, {"link": 719, "name": "vae", "type": "VAE"}], "mode": 0, "order": 124, "outputs": [{"links": [1142], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [5342.8876953125, -515.1910400390625], "properties": {"Node name for S&R": "VAEDecode"}, "size": [140, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 414, "flags": {}, "inputs": [{"link": 720, "name": "faces", "type": "FACE"}], "mode": 0, "order": 113, "outputs": [{"links": [989, 1121, 1145], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [1399, 689], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [705], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [4003.9951171875, -443.82769775390625], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 416, "flags": {}, "inputs": [], "mode": 0, "order": 13, "outputs": [{"links": [1412], "name": "MODEL", "slot_index": 0, "type": "MODEL"}, {"links": [955, 956], "name": "CLIP", "slot_index": 1, "type": "CLIP"}, {"links": [674, 719, 1133], "name": "VAE", "slot_index": 2, "type": "VAE"}], "pos": [3702.845458984375, -854.5330810546875], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "size": [314.34735107421875, 128.43458557128906], "type": "CheckpointLoaderSimple", "widgets_values": ["${instantIdModel!'sdxl/Epicrealismxl_Hades.safetensors'}"]}, {"id": 436, "flags": {}, "inputs": [{"link": 1336, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 111, "outputs": [{"links": [684], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [3731.463623046875, -531.82397460938], "properties": {"Node name for S&R": "DetectFaces"}, "size": [210, 126], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 446, "flags": {}, "inputs": [{"link": 1071, "name": "faces", "type": "FACE"}], "mode": 0, "order": 84, "outputs": [{"links": [713, 762], "name": "crops", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "masks", "shape": 3, "slot_index": 1, "type": "MASK"}, {"links": [], "name": "warps", "shape": 3, "slot_index": 2, "type": "WARP"}], "pos": [5317.95458984375, -2141.50634765625], "properties": {"Node name for S&R": "CropFaces"}, "size": [221.15121459960938, 146], "type": "CropFaces", "widgets_values": [1024, 2, "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 447, "flags": {}, "inputs": [{"link": 1073, "name": "image", "type": "IMAGE"}, {"name": "mask", "shape": 7, "type": "MASK"}], "mode": 0, "order": 82, "outputs": [{"links": [1071], "name": "faces", "shape": 3, "slot_index": 0, "type": "FACE"}], "pos": [5064.96484375, -2149.68798828125], "properties": {"Node name for S&R": "DetectFaces"}, "size": [216.65777587890625, 143.53131103515625], "type": "DetectFaces", "widgets_values": [0.1, 64, 1024]}, {"id": 455, "flags": {}, "inputs": [{"link": 1357, "name": "", "type": "*"}], "mode": 0, "order": 85, "outputs": [{"links": [789, 1392, 789], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [-174.99717712402344, -462.8396301269531], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 456, "flags": {}, "inputs": [{"link": 1300, "name": "", "type": "*"}], "mode": 0, "order": 127, "outputs": [{"links": [1328, 783], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [5169.2236328125, -180.5690002441406], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 457, "flags": {}, "inputs": [{"link": 786, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 45, "outputs": [{"links": [788, 788], "name": "", "slot_index": 0, "type": "STRING"}], "pos": [3770.6259765625, -681.1192016601562], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 462, "flags": {}, "inputs": [], "mode": 0, "order": 29, "outputs": [{"links": [1001, 859, 899, 957], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [3709.57421875, -2161.4541015625], "properties": {"Node name for S&R": "LoadImage"}, "size": [233.97132873535156, 314], "type": "LoadImage", "widgets_values": ["${FACE.extInfo['faceImage']}", "image"]}, {"id": 474, "flags": {}, "inputs": [{"link": 813, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1423, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 47, "outputs": [{"links": [998, 1069], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4197.35693359375, -1760.59130859375], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 475, "flags": {}, "inputs": [], "mode": 0, "order": 14, "outputs": [{"links": [813], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4102.89697265625, -2189.171630859375], "properties": {"Node name for S&R": "LoadImage"}, "size": [268.51116943359375, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>${FACE.extInfo['faceImageMore'][0]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 476, "flags": {}, "inputs": [], "mode": 0, "order": 15, "outputs": [{"links": [1003, 1007, 1051, 1064, 1089], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "MASK", "type": "MASK"}], "pos": [4437.705078125, -2194.97900390625], "properties": {"Node name for S&R": "LoadImage"}, "size": [234.48504638671875, 314], "type": "LoadImage", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>${FACE.extInfo['faceImageMore'][1]}<#else>${FACE.extInfo['faceImage']}</#if><#else>${FACE.extInfo['faceImage']}</#if>", "image"]}, {"id": 477, "flags": {}, "inputs": [{"link": 1089, "name": "image1", "shape": 7, "type": "IMAGE"}, {"name": "image2", "shape": 7, "type": "IMAGE"}, {"link": 1424, "name": "Input", "type": "INT", "widget": {"name": "Input"}}], "mode": 0, "order": 46, "outputs": [{"links": [1044, 1060, 1070], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "show_help", "type": "STRING"}], "pos": [4193.537109375, -1612.7928466796875], "properties": {"Node name for S&R": "CR Image Input Switch"}, "size": [210, 74], "type": "CR Image Input Switch", "widgets_values": [2]}, {"id": 549, "flags": {}, "inputs": [{"link": 985, "name": "", "type": "*"}], "mode": 0, "order": 119, "outputs": [{"links": [979, 980, 1132], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4441.5126953125, -554.5872192382812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 550, "flags": {}, "inputs": [{"link": 1335, "name": "", "type": "*"}], "mode": 0, "order": 117, "outputs": [{"links": [985], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4180.76416015625, -906.14404296875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 559, "flags": {}, "inputs": [{"link": 1001, "name": "image1", "type": "IMAGE"}, {"link": 1069, "name": "image2", "type": "IMAGE"}, {"link": 1070, "name": "image3", "type": "IMAGE"}, {"name": "image4", "type": "IMAGE"}], "mode": 0, "order": 42, "outputs": [{"links": [1009], "name": "IMAGE", "shape": 6, "slot_index": 0, "type": "IMAGE"}], "pos": [4528.984375, -1783.8060302734375], "properties": {"Node name for S&R": "ImpactMakeImageList"}, "size": [140, 86], "type": "ImpactMakeImageList", "widgets_values": []}, {"id": 561, "flags": {}, "inputs": [{"link": 1009, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 53, "outputs": [{"links": [1092, 1093], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "width", "type": "INT"}, {"name": "height", "type": "INT"}], "pos": [4475.1279296875, -1605.535888671875], "properties": {"Node name for S&R": "ImageResize+"}, "size": [261.8576965332031, 224.5092315673828], "type": "ImageResize+", "widgets_values": [2048, 2048, "lanc<PERSON>s", "keep proportion", "upscale if smaller", 0]}, {"id": 570, "flags": {}, "inputs": [{"link": 1343, "name": "bbox_detector", "type": "BBOX_DETECTOR"}, {"link": 1093, "name": "image", "type": "IMAGE"}, {"name": "sam_model_opt", "shape": 7, "type": "SAM_MODEL"}, {"name": "segm_detector_opt", "shape": 7, "type": "SEGM_DETECTOR"}], "mode": 0, "order": 62, "outputs": [{"links": [1084], "name": "SEGS", "slot_index": 0, "type": "SEGS"}], "pos": [4112.90771484375, -1405.44482421875], "properties": {"Node name for S&R": "ImpactSimpleDetectorSEGS"}, "size": [277.6741943359375, 319.97015380859375], "type": "ImpactSimpleDetectorSEGS", "widgets_values": [0.5, 0, 3, 10, 0.5, 0, 0, 0.7, 0]}, {"id": 574, "flags": {}, "inputs": [{"link": 1086, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 71, "outputs": [{"links": [1039], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4766.97509765625, -1133.2860107421875], "properties": {"Node name for S&R": "SegsToCombinedMask"}, "size": [289.79998779296875, 26], "type": "SegsToCombinedMask", "widgets_values": []}, {"id": 576, "flags": {}, "inputs": [{"link": 1037, "name": "image", "type": "IMAGE"}, {"link": 1040, "name": "mask", "type": "MASK"}], "mode": 0, "order": 77, "outputs": [{"links": [1072], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS"}], "pos": [4769.27783203125, -2157.122314453125], "properties": {"Node name for S&R": "Bounded Image Crop with Mask"}, "size": [235.1999969482422, 150.37045288085938], "type": "Bounded Image Crop with Mask", "widgets_values": [64, 64, 64, 64]}, {"id": 579, "flags": {}, "inputs": [{"link": 1092, "name": "", "type": "*"}], "mode": 0, "order": 61, "outputs": [{"links": [1037], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [4805.14599609375, -1606.9412841796875], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 580, "flags": {}, "inputs": [{"link": 1039, "name": "mask", "type": "MASK"}], "mode": 0, "order": 74, "outputs": [{"links": [1040], "name": "MASK", "slot_index": 0, "type": "MASK"}], "pos": [4883.5205078125, -1450.7889404296875], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 256, 0, 0, 1, true]}, {"id": 592, "flags": {}, "inputs": [{"link": 1072, "name": "images", "type": "IMAGE"}], "mode": 0, "order": 80, "outputs": [{"links": [1073], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [4832.5732421875, -1937.627197265625], "properties": {"Node name for S&R": "easy imageListToImageBatch"}, "size": [315, 26], "type": "easy imageListToImageBatch", "widgets_values": []}, {"id": 597, "flags": {}, "inputs": [{"link": 1084, "name": "segs", "type": "SEGS"}], "mode": 0, "order": 69, "outputs": [{"links": [1086], "name": "filtered_SEGS", "slot_index": 0, "type": "SEGS"}, {"links": [], "name": "remained_SEGS", "slot_index": 1, "type": "SEGS"}], "pos": [4494.18310546875, -1262.16796875], "properties": {"Node name for S&R": "ImpactSEGSOrderedFilter"}, "size": [210, 158.96408081054688], "type": "ImpactSEGSOrderedFilter", "widgets_values": ["area(=w*h)", true, 0, 1]}, {"id": 604, "flags": {}, "inputs": [{"link": 1338, "name": "", "type": "*"}], "mode": 0, "order": 110, "outputs": [{"links": [1336, 1337], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [3485.7802734375, 114.76781463623047], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 610, "flags": {}, "inputs": [], "mode": 0, "order": 19, "outputs": [{"label": "BBOX_DETECTOR", "links": [1343], "name": "BBOX_DETECTOR", "shape": 3, "slot_index": 0, "type": "BBOX_DETECTOR"}, {"label": "SEGM_DETECTOR", "links": [], "name": "SEGM_DETECTOR", "shape": 3, "slot_index": 1, "type": "SEGM_DETECTOR"}], "pos": [3720.58740234375, -1361.11962890625], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "size": [315, 78], "type": "UltralyticsDetectorProvider", "widgets_values": ["bbox/face_yolov8m.pt"]}, {"id": 616, "flags": {}, "inputs": [{"link": 1121, "name": "", "type": "*"}], "mode": 0, "order": 114, "outputs": [{"links": [1335], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [3913.857666015625, -2227.386474609375], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 618, "flags": {}, "inputs": [{"link": 1127, "name": "positive", "type": "CONDITIONING"}, {"link": 1128, "name": "negative", "type": "CONDITIONING"}, {"link": 1126, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1132, "name": "image", "type": "IMAGE"}, {"link": 1133, "name": "vae", "shape": 7, "type": "VAE"}], "mode": 0, "order": 121, "outputs": [{"links": [1136], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1137], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [4981.**********, -833.2544555664062], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "size": [243.52598571777344, 186], "type": "ControlNetApplyAdvanced", "widgets_values": [0.1, 0, 0.5]}, {"id": 619, "flags": {}, "inputs": [], "mode": 0, "order": 25, "outputs": [{"links": [1126], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [4360.33203125, -877.9781494140625], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [315, 58], "type": "ControlNetLoader", "widgets_values": ["TTPLanet_SDXL_Controlnet_Tile_Realistic_V1_fp16.safetensors"]}, {"id": 624, "flags": {"collapsed": false}, "inputs": [{"link": 1145, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 115, "outputs": [{"links": [], "name": "image", "slot_index": 0, "type": "IMAGE"}, {"links": [1388], "name": "mask", "slot_index": 1, "type": "MASK"}, {"name": "bbox", "slot_index": 2, "type": "BBOX"}], "pos": [3306.************, -459.94635009765625], "properties": {"Node name for S&R": "easy humanSegmentation", "values": [13]}, "size": [300, 500], "type": "easy humanSegmentation", "widgets_values": ["human_parsing_lip", 0.4, 0, "13"]}, {"id": 670, "flags": {}, "inputs": [{"link": 1226, "name": "model", "slot_index": 0, "type": "MODEL"}, {"link": 1227, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 106, "outputs": [{"links": [1239], "name": "GUIDER", "shape": 3, "type": "GUIDER"}], "pos": [4160.22509765625, 399.107116699219], "properties": {"Node name for S&R": "BasicGuider"}, "size": [161.1999969482422, 46], "type": "BasicGuider", "widgets_values": []}, {"id": 671, "flags": {}, "inputs": [], "mode": 0, "order": 8, "outputs": [{"links": [1240], "name": "SAMPLER", "shape": 3, "type": "SAMPLER"}], "pos": [4114.82470703125, 494.41168212890625], "properties": {"Node name for S&R": "KSamplerSelect"}, "size": [210, 58], "type": "KSamplerSelect", "widgets_values": ["euler_ancestral"]}, {"id": 672, "flags": {}, "inputs": [{"link": 1228, "name": "positive", "type": "CONDITIONING"}, {"link": 1229, "name": "negative", "slot_index": 1, "type": "CONDITIONING"}, {"link": 1230, "name": "vae", "type": "VAE"}, {"link": 1231, "name": "pixels", "type": "IMAGE"}, {"link": 1232, "name": "mask", "slot_index": 4, "type": "MASK"}], "mode": 0, "order": 105, "outputs": [{"links": [1227], "name": "positive", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}, {"name": "negative", "shape": 3, "type": "CONDITIONING"}, {"links": [1276], "name": "latent", "shape": 3, "slot_index": 2, "type": "LATENT"}], "pos": [3812.64453125, 589.8123168945312], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "size": [216.59999084472656, 138], "type": "InpaintModelConditioning", "widgets_values": [true]}, {"id": 673, "flags": {}, "inputs": [{"link": 1233, "name": "conditioning", "slot_index": 0, "type": "CONDITIONING"}], "mode": 0, "order": 104, "outputs": [{"links": [1228], "name": "CONDITIONING", "shape": 3, "slot_index": 0, "type": "CONDITIONING"}], "pos": [3823.9736328125, 438.120849609375], "properties": {"Node name for S&R": "FluxGuidance"}, "size": [211.60000610351562, 58], "type": "FluxGuidance", "widgets_values": [2]}, {"id": 674, "flags": {}, "inputs": [], "mode": 0, "order": 5, "outputs": [{"links": [1258], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [1177.1427001953125, 97.7112121582031], "properties": {"Node name for S&R": "ControlNetLoader"}, "size": [518.1205444335938, 58.483619689941406], "type": "ControlNetLoader", "widgets_values": ["FLUX.1-dev-ControlNet-Union-Pro-2.0-Shakker-Labs.safetensors"]}, {"id": 675, "flags": {}, "inputs": [{"link": 1234, "name": "", "type": "*", "widget": {"name": "value"}}], "mode": 0, "order": 95, "outputs": [{"links": [1245, 1271], "name": "", "type": "FLOAT"}], "pos": [197.6379852294922, 213.60537719726562], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 676, "flags": {"collapsed": false}, "inputs": [{"link": 1235, "name": "mask", "type": "MASK"}], "mode": 0, "order": 94, "outputs": [{"links": [1270, 1321], "name": "mask", "shape": 3, "slot_index": 0, "type": "MASK"}, {"name": "mask_inverted", "shape": 3, "type": "MASK"}], "pos": [150.67288208007812, 164.52459716796875], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "size": [236.14947509765625, 246], "type": "ConrainGrowMaskWithBlur", "widgets_values": [-10, 0, true, false, 15, 1, 1, false]}, {"id": 677, "flags": {}, "inputs": [{"link": 1392, "name": "", "type": "*"}], "mode": 0, "order": 87, "outputs": [{"links": [1256, 1274, 1319], "name": "", "type": "*"}], "pos": [-1125.8050537109375, 132.85696411132812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 678, "flags": {}, "inputs": [], "mode": 0, "order": 30, "outputs": [{"links": [1273], "name": "model", "slot_index": 0, "type": "MODEL_SAPIEN"}], "pos": [4212.32373046875, -252.35496520996094], "properties": {"Node name for S&R": "Sapiens<PERSON><PERSON>der"}, "size": [283.3903503417969, 298], "type": "Sapiens<PERSON><PERSON>der", "widgets_values": ["seg/sapiens_1b_goliath_best_goliath_mIoU_7994_epoch_151_torchscript.pt2", "none", "none", "none", "float32_torch", 0.5, true, false, false, true, true]}, {"id": 679, "flags": {}, "inputs": [{"link": 1398, "name": "model", "type": "MODEL"}], "mode": 0, "order": 68, "outputs": [{"links": [1241], "name": "SIGMAS", "shape": 3, "type": "SIGMAS"}], "pos": [4118.50439453125, 600.8508911132812], "properties": {"Node name for S&R": "BasicScheduler"}, "size": [210, 106], "type": "BasicScheduler", "widgets_values": ["sgm_uniform", 20, 0.65]}, {"id": 680, "flags": {}, "inputs": [{"link": 1237, "name": "", "type": "*"}], "mode": 0, "order": 102, "outputs": [{"links": [1266], "name": "", "type": "IMAGE"}], "pos": [1991.21484375, 338.81671142578125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 681, "flags": {}, "inputs": [{"link": 1238, "name": "noise", "slot_index": 0, "type": "NOISE"}, {"link": 1239, "name": "guider", "slot_index": 1, "type": "GUIDER"}, {"link": 1240, "name": "sampler", "slot_index": 2, "type": "SAMPLER"}, {"link": 1241, "name": "sigmas", "slot_index": 3, "type": "SIGMAS"}, {"link": 1242, "name": "latent_image", "type": "LATENT"}], "mode": 0, "order": 108, "outputs": [{"links": [1261], "name": "output", "shape": 3, "slot_index": 0, "type": "LATENT"}, {"name": "denoised_output", "shape": 3, "type": "LATENT"}], "pos": [4482.6591796875, 329.81170654296875], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "size": [355.20001220703125, 106], "type": "SamplerCustomAdvanced", "widgets_values": []}, {"id": 682, "flags": {}, "inputs": [], "mode": 0, "order": 7, "outputs": [{"links": [1238], "name": "NOISE", "shape": 3, "type": "NOISE"}], "pos": [4116.41748046875, 270.2794494628906], "properties": {"Node name for S&R": "RandomNoise"}, "size": [277.9598693847656, 82], "type": "RandomNoise", "widgets_values": [745052156325993, "fixed"]}, {"id": 683, "flags": {"collapsed": false}, "inputs": [{"link": 1397, "name": "model", "type": "MODEL"}], "mode": 0, "order": 67, "outputs": [{"links": [1226], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}], "pos": [3836.329345703125, 311.916015625], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "size": [184.8000030517578, 26], "type": "DifferentialDiffusion", "widgets_values": []}, {"id": 684, "flags": {}, "inputs": [{"label": "image", "link": 1244, "name": "image", "type": "IMAGE"}, {"label": "scale_by", "link": 1245, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 97, "outputs": [{"label": "IMAGE", "links": [1246], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [502.68756103515625, 179.6353759765625], "properties": {"Node name for S&R": "ImageScaleBy"}, "size": [250.58731079101562, 109.74829864501953], "type": "ImageScaleBy", "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 685, "flags": {}, "inputs": [{"link": 1246, "name": "", "type": "*"}], "mode": 0, "order": 99, "outputs": [{"links": [1231, 1259], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [815.7013549804688, 195.89584350585938], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 687, "flags": {}, "inputs": [{"link": 1248, "name": "conditioning", "type": "CONDITIONING"}], "mode": 0, "order": 64, "outputs": [{"links": [1264], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1924.6767578125, 463.5101623535156], "properties": {"Node name for S&R": "ConditioningZeroOut"}, "size": [222.26809692382812, 26], "type": "ConditioningZeroOut", "widgets_values": []}, {"id": 688, "bgcolor": "#353535", "color": "#494949", "flags": {}, "inputs": [{"link": 1249, "name": "clip", "type": "CLIP"}, {"link": 1282, "name": "text", "type": "STRING", "widget": {"name": "text"}}], "mode": 0, "order": 56, "outputs": [{"links": [1248, 1263], "name": "CONDITIONING", "slot_index": 0, "type": "CONDITIONING"}], "pos": [1526.1217041015625, 431.4842834472656], "properties": {"Node name for S&R": "CLIPTextEncode"}, "size": [285.6000061035156, 65.87068176269531], "title": "CLIP Text Encode (Positive Prompt)", "type": "CLIPTextEncode", "widgets_values": ["linrun2111, white background, full body, front view,"]}, {"id": 689, "flags": {}, "inputs": [{"link": 1285, "name": "", "type": "*"}], "mode": 0, "order": 49, "outputs": [{"links": [1249], "name": "", "type": "CLIP"}], "pos": [1222.99072265625, 280.567626953125], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 692, "flags": {}, "inputs": [{"link": 1255, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 92, "outputs": [{"links": [1234], "name": "rescale_factor", "slot_index": 0, "type": "FLOAT"}, {"name": "rescale_width", "type": "INT"}, {"name": "recover_factor", "type": "FLOAT"}, {"name": "recover_width", "type": "INT"}], "pos": [-68.13079833984375, 555.9083251953125], "properties": {"Node name for S&R": "UpscaleSizeCalculator"}, "size": [259.6224060058594, 129.99847412109375], "type": "UpscaleSizeCalculator", "widgets_values": [1024]}, {"id": 693, "flags": {}, "inputs": [{"link": 1256, "name": "image", "type": "IMAGE"}, {"link": 1394, "name": "mask", "type": "MASK"}], "mode": 0, "order": 91, "outputs": [{"links": [1244, 1255], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}, {"links": [1324], "name": "IMAGE_BOUNDS", "shape": 3, "slot_index": 1, "type": "IMAGE_BOUNDS"}, {"links": [1235], "name": "MASK", "shape": 3, "slot_index": 2, "type": "MASK"}, {"links": [], "name": "SCALE_BY", "shape": 3, "slot_index": 3, "type": "FLOAT"}], "pos": [-289.6543884277344, 130.72567749023438], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "size": [285.6000061035156, 142], "type": "BoundedImageCropWithMask_v3_LR", "widgets_values": [20, 20]}, {"id": 694, "flags": {}, "inputs": [{"link": 1258, "name": "control_net", "type": "CONTROL_NET"}], "mode": 0, "order": 36, "outputs": [{"links": [1265], "name": "CONTROL_NET", "slot_index": 0, "type": "CONTROL_NET"}], "pos": [1843.809814453125, 148.7852020263672], "properties": {"Node name for S&R": "SetUnionControlNetType"}, "size": [336.6855163574219, 106.154113769531], "type": "SetUnionControlNetType", "widgets_values": ["canny/lineart/anime_lineart/mlsd"]}, {"id": 695, "flags": {}, "inputs": [{"link": 1259, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 101, "outputs": [{"links": [1237], "name": "IMAGE", "slot_index": 0, "type": "IMAGE"}], "pos": [1506.9503173828125, 275.8267822265625], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "size": [222.1300048828125, 82], "type": "<PERSON><PERSON>", "widgets_values": [0.1, 0.5]}, {"id": 697, "flags": {}, "inputs": [{"link": 1261, "name": "samples", "type": "LATENT"}, {"link": 1262, "name": "vae", "type": "VAE"}], "mode": 0, "order": 109, "outputs": [{"links": [1338], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [4894.6767578125, 366.17950439453125], "properties": {"Node name for S&R": "VAEDecode"}, "size": [210, 46], "type": "VAEDecode", "widgets_values": []}, {"id": 698, "flags": {}, "inputs": [{"link": 1263, "name": "positive", "type": "CONDITIONING"}, {"link": 1264, "name": "negative", "type": "CONDITIONING"}, {"link": 1265, "name": "control_net", "type": "CONTROL_NET"}, {"link": 1266, "name": "image", "type": "IMAGE"}, {"link": 1267, "name": "vae", "shape": 7, "type": "VAE"}], "mode": 0, "order": 103, "outputs": [{"links": [1233], "name": "positive", "slot_index": 0, "type": "CONDITIONING"}, {"links": [1229], "name": "negative", "slot_index": 1, "type": "CONDITIONING"}], "pos": [2378.658203125, 408.31219482421875], "properties": {"Node name for S&R": "ControlNetApplyAdvanced"}, "size": [218.561065673828, 186], "type": "ControlNetApplyAdvanced", "widgets_values": [0.6, 0, 0.5]}, {"id": 699, "flags": {}, "inputs": [{"link": 1268, "name": "", "type": "*"}], "mode": 0, "order": 100, "outputs": [{"links": [1232], "name": "", "type": "MASK"}], "pos": [838.829467773438, 488.2565002441406], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 701, "flags": {}, "inputs": [{"label": "mask", "link": 1270, "name": "mask", "type": "MASK"}, {"label": "scale_by", "link": 1271, "name": "scale_by", "type": "FLOAT", "widget": {"name": "scale_by"}}], "mode": 0, "order": 98, "outputs": [{"label": "MASK", "links": [1268], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [475.3071594238281, 486.9634094238281], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "size": [320, 60], "type": "MaskUpscale_LR", "widgets_values": [4]}, {"id": 703, "flags": {}, "inputs": [{"link": 1273, "name": "model", "type": "MODEL_SAPIEN"}, {"link": 1274, "name": "image", "type": "IMAGE"}], "mode": 0, "order": 88, "outputs": [{"links": [], "name": "seg_img", "slot_index": 0, "type": "IMAGE"}, {"links": [], "name": "depth_img", "slot_index": 1, "type": "IMAGE"}, {"links": [], "name": "normal_img", "slot_index": 2, "type": "IMAGE"}, {"links": [], "name": "pose_img", "slot_index": 3, "type": "IMAGE"}, {"links": [1393], "name": "mask", "slot_index": 4, "type": "MASK"}], "pos": [-1135.94677734375, 325.4853210449219], "properties": {"Node name for S&R": "SapiensSampler"}, "size": [315, 258], "type": "SapiensSampler", "widgets_values": ["2.<PERSON>_<PERSON>", "3,23,24,25,26,27", false, 255, 255, 255]}, {"id": 705, "flags": {}, "inputs": [{"link": 1276, "name": "samples", "type": "LATENT"}], "mode": 0, "order": 107, "outputs": [{"links": [1242], "name": "LATENT", "shape": 3, "slot_index": 0, "type": "LATENT"}], "pos": [4417.4013671875, 607.6464233398438], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "size": [210, 80.70018768310547], "type": "RepeatLatentBatch", "widgets_values": [1]}, {"id": 706, "flags": {}, "inputs": [{"link": 1277, "name": "", "type": "*"}], "mode": 0, "order": 37, "outputs": [{"links": [1230, 1262, 1267], "name": "", "type": "VAE"}], "pos": [2273.233642578125, 108.75343322753906], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 725, "flags": {}, "inputs": [{"link": 1320, "name": "target", "type": "IMAGE"}, {"link": 1323, "name": "target_mask", "type": "MASK"}, {"link": 1326, "name": "target_bounds", "type": "IMAGE_BOUNDS"}, {"link": 1328, "name": "source", "type": "IMAGE"}], "mode": 0, "order": 128, "outputs": [{"links": [1417], "name": "IMAGE", "shape": 3, "slot_index": 0, "type": "IMAGE"}], "pos": [5306.212890625, -197.30076599121094], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "size": [247.87564086914062, 164.6544647216797], "type": "Bounded Image Blend with Mask", "widgets_values": [1, 0]}, {"id": 726, "flags": {}, "inputs": [{"link": 1319, "name": "", "type": "*"}], "mode": 0, "order": 89, "outputs": [{"links": [1320], "name": "", "slot_index": 0, "type": "*"}], "pos": [5066.88232421875, 153.15419006347656], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 727, "flags": {}, "inputs": [{"link": 1321, "name": "", "type": "*"}], "mode": 0, "order": 96, "outputs": [{"links": [1323], "name": "", "slot_index": 0, "type": "MASK"}], "pos": [5065.23486328125, 205.93362426757812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 728, "flags": {}, "inputs": [{"link": 1324, "name": "", "type": "*"}], "mode": 0, "order": 93, "outputs": [{"links": [1326], "name": "", "slot_index": 0, "type": "IMAGE_BOUNDS"}], "pos": [5065.74462890625, 253.8061065673828], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 752, "flags": {}, "inputs": [{"link": 1393, "name": "any_a", "shape": 7, "type": "*"}, {"name": "any_b", "shape": 7, "type": "*"}, {"name": "any_c", "shape": 7, "type": "*"}, {"name": "any_d", "shape": 7, "type": "*"}], "mode": 0, "order": 90, "outputs": [{"links": [1394], "name": "any", "slot_index": 0, "type": "*"}], "pos": [-748.3713989257812, 154.49908447265625], "properties": {"Node name for S&R": "ConrainPythonExecutor"}, "size": [294.2134704589844, 566.1136474609375], "type": "ConrainPythonExecutor", "widgets_values": ["import cv2\nimport numpy as np\nimport torch\n\ndef filter_mask(mask):\n    \"\"\"\n    输入torch tensor格式的mask，形状为【通道，高度，宽度】或【高度，宽度】\n    计算连通域，返回只有最大连通域的mask，其他区域设置为0\n    \"\"\"\n    # 保存原始设备和数据类型\n    original_device = mask.device\n    original_dtype = mask.dtype\n    \n    # 转换为numpy处理\n    if mask.dim() == 3:  # 通道处理 [channel, height, width]\n        num_channels = mask.shape[0]\n        new_masks = []\n        \n        for i in range(num_channels):\n            single_mask = mask[i].cpu().numpy()\n            processed_mask = _process_single_mask(single_mask)\n            new_masks.append(torch.from_numpy(processed_mask))\n        \n        result = torch.stack(new_masks)\n    elif mask.dim() == 2:  # 单个mask [height, width]\n        single_mask = mask.cpu().numpy()\n        processed_mask = _process_single_mask(single_mask)\n        result = torch.from_numpy(processed_mask)\n    else:\n        raise ValueError(\"mask维度必须是2D或3D\")\n    \n    # 转换回原始设备和数据类型\n    return result.to(device=original_device, dtype=original_dtype)\n\ndef _process_single_mask(mask):\n    \"\"\"处理单个2D mask\"\"\"\n    # 数据预处理：确保正确的二值化\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        # 如果是浮点数，假设范围是0-1，转换为0-255\n        mask_normalized = (mask * 255).astype(np.uint8)\n    else:\n        mask_normalized = mask.astype(np.uint8)\n    \n    # 二值化处理，确保只有0和255\n    _, binary_mask = cv2.threshold(mask_normalized, 127, 255, cv2.THRESH_BINARY)\n    \n    # 形态学开运算，去除小噪声\n    kernel = np.ones((3,3), np.uint8)\n    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)\n    \n    # 计算连通域\n    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_mask)\n    \n    if num_labels <= 1:  # 只有背景，没有前景\n        return np.zeros_like(mask)\n    \n    # 获取最大的连通域（排除背景）\n    max_area_index = np.argmax(stats[1:, cv2.CC_STAT_AREA]) + 1\n    \n    # 创建新的mask，只有最大的连通域为1，其他为0\n    new_mask = (labels == max_area_index).astype(mask.dtype)\n    \n    # 如果原始数据是浮点型，将结果转换回0-1范围\n    if mask.dtype == np.float32 or mask.dtype == np.float64:\n        new_mask = new_mask.astype(mask.dtype)\n    \n    return new_mask\n\ndef call(any_a, any_b, any_c, any_d):\n    new_mask = filter_mask(any_a)\n    if torch.all(new_mask == 0):\n        blank_size = 4\n        # 设置左上角2x2区域为1\n        if new_mask.dim() == 2:\n            new_mask[0:blank_size, 0:blank_size] = 1\n        else:  # 3D\n            new_mask[0, 0:blank_size, 0:blank_size] = 1\n    return [new_mask]"]}, {"id": 753, "bgcolor": "#353", "color": "#232", "flags": {}, "inputs": [{"link": 1395, "name": "model", "type": "MODEL"}, {"link": 1396, "name": "clip", "type": "CLIP"}], "mode": "${isLoraFace?then(0,4)}", "order": 60, "outputs": [{"links": [1397, 1398], "name": "MODEL", "shape": 3, "slot_index": 0, "type": "MODEL"}, {"links": [], "name": "CLIP", "shape": 3, "slot_index": 1, "type": "CLIP"}], "pos": [2587.705810546875, -205.255584716797], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "size": [477.3377990722656, 128.31455993652344], "title": "人脸lora", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "widgets_values": ["${FACE.extInfo.faceLora}", "1", "1"]}, {"id": 754, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [{"link": 1399, "name": "mask", "type": "MASK"}], "mode": 0, "order": 116, "outputs": [{"links": [1400], "name": "MASK", "shape": 3, "slot_index": 0, "type": "MASK"}], "pos": [4546.5107421875, -470.543212890625], "properties": {"Node name for S&R": "MaskFastGrow"}, "size": [210, 178], "type": "MaskFastGrow", "widgets_values": [false, 64, 32, 0, 1, true]}, {"id": 764, "flags": {}, "inputs": [{"link": 1425, "name": "", "type": "*"}], "mode": 0, "order": 130, "outputs": [{"links": [1415, 1416], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [6341.591796875, -1057.5367431640625], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 765, "flags": {}, "inputs": [{"link": 1417, "name": "", "type": "*"}], "mode": 0, "order": 129, "outputs": [{"links": [1425], "name": "", "slot_index": 0, "type": "IMAGE"}], "pos": [5718.21533203125, -167.27053833007812], "properties": {"horizontal": false, "showOutputText": false}, "size": [75, 26], "type": "Reroute"}, {"id": 768, "bgcolor": "#653", "color": "#432", "flags": {}, "inputs": [], "mode": 0, "order": 31, "outputs": [], "pos": [5863.87744140625, -285.8473815917969], "properties": {"text": ""}, "size": [260, 110], "type": "Note", "widgets_values": ["背面图不做换脸，只需要直出图"]}, {"id": 770, "flags": {}, "inputs": [], "mode": 0, "order": 27, "outputs": [{"links": [1424], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3746.947265625, -1582.428466796875], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 72.16617584228516], "type": "JWStringToInteger", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 0>1<#else>2</#if><#else>2</#if>"]}, {"id": 771, "flags": {}, "inputs": [], "mode": 0, "order": 28, "outputs": [{"links": [1423], "name": "INT", "slot_index": 0, "type": "INT"}], "pos": [3744.10986328125, -1727.185791015625], "properties": {"Node name for S&R": "JWStringToInteger"}, "size": [210, 72.16617584228516], "type": "JWStringToInteger", "widgets_values": ["<#if FACE.extInfo['faceImageMore'] && FACE.extInfo['faceImageMore']?is_sequence><#if FACE.extInfo['faceImageMore']?size gt 1>1<#else>2</#if><#else>2</#if>"]}], "seed_widgets": {"277": 0, "282": 1, "284": 0, "285": 0, "286": 1, "331": 1, "411": 0, "682": 0}, "version": 0.4}}}}
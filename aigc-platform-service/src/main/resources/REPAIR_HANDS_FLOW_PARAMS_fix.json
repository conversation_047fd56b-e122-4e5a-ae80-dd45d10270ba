{"client_id": "${clientId}", "prompt": {"1": {"inputs": {"noise": ["4", 0], "guider": ["5", 0], "sampler": ["7", 0], "sigmas": ["8", 0], "latent_image": ["94", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "2": {"inputs": {"samples": ["1", 0], "vae": ["144", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "4": {"inputs": {"noise_seed": "${seed}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "5": {"inputs": {"model": ["6", 0], "conditioning": ["9", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "6": {"inputs": {"model": ["146", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "7": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "8": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["146", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "9": {"inputs": {"noise_mask": true, "positive": ["10", 0], "negative": ["11", 0], "vae": ["144", 0], "pixels": ["72", 0], "mask": ["73", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "10": {"inputs": {"guidance": 3.5, "conditioning": ["16", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "11": {"inputs": {"text": "", "clip": ["145", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "16": {"inputs": {"text": "hand of young fashion model, clean nails, jewelry-free, high quality, high detail, realistic, no tattoos, no nail polish, no jewelry", "clip": ["145", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "72": {"inputs": {"image": "${repairImage}", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "73": {"inputs": {"expand": 5, "incremental_expandrate": 0, "tapered_corners": true, "flip_input": false, "blur_radius": 10, "lerp_alpha": 1, "decay_factor": 1, "fill_holes": false, "mask": ["72", 1]}, "class_type": "ConrainGrowMaskWithBlur", "_meta": {"title": "<PERSON><PERSON> Grow Mask With Blur"}}, "94": {"inputs": {"amount": 2, "samples": ["9", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "95": {"inputs": {"expand_LRU": 40, "expand_B": 60, "image": ["72", 0], "mask": ["73", 0]}, "class_type": "BoundedImageCropWithMask_v3_LR", "_meta": {"title": "Bounded Image Crop With Mask LR v3"}}, "104": {"inputs": {"upscale_method": "lanc<PERSON>s", "scale_by": ["95", 3], "image": ["95", 0]}, "class_type": "ImageScaleBy", "_meta": {"title": "Upscale Image By"}}, "105": {"inputs": {"scale_by": ["95", 3], "mask": ["95", 2]}, "class_type": "MaskUpscale_LR", "_meta": {"title": "Mask Upscale LR"}}, "118": {"inputs": {"noise": ["119", 0], "guider": ["120", 0], "sampler": ["121", 0], "sigmas": ["122", 0], "latent_image": ["127", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "119": {"inputs": {"noise_seed": "${seed2}"}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "120": {"inputs": {"model": ["6", 0], "conditioning": ["123", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "121": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "122": {"inputs": {"scheduler": "simple", "steps": 20, "denoise": 1, "model": ["146", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "123": {"inputs": {"noise_mask": true, "positive": ["10", 0], "negative": ["11", 0], "vae": ["144", 0], "pixels": ["104", 0], "mask": ["105", 0]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "125": {"inputs": {"samples": ["118", 0], "vae": ["144", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "127": {"inputs": {"amount": 2, "samples": ["123", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "130": {"inputs": {"amount": 2, "image": ["72", 0]}, "class_type": "RepeatImageBatch", "_meta": {"title": "RepeatImageBatch"}}, "138": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["130", 0], "target_mask": ["140", 0], "target_bounds": ["95", 1], "source": ["125", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "140": {"inputs": {"expand": 15, "tapered_corners": false, "mask": ["72", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "141": {"inputs": {"image1": ["148", 0], "image2": ["138", 0]}, "class_type": "ImageBatch", "_meta": {"title": "Batch Images"}}, "142": {"inputs": {"output_path": "${outputPath}", "filename_prefix": "${fileNamePrefix}", "extension": "jpg", "dpi": 300, "quality": 100, "optimize_image": "true", "lossless_webp": "false", "embed_workflow": "false", "use_time_str": "true", "output_as_root": "true", "images": ["141", 0]}, "class_type": "ConrainImageSave", "_meta": {"title": "conrain save image"}}, "144": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "145": {"inputs": {"clip_name1": "t5xxl_fp16.safetensors", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "146": {"inputs": {"unet_name": "flux1-dev.safetensors", "weight_dtype": "default"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "148": {"inputs": {"blend_factor": 1, "feathering": 0, "target": ["130", 0], "target_mask": ["140", 0], "target_bounds": ["150", 0], "source": ["2", 0]}, "class_type": "Bounded Image Blend with Mask", "_meta": {"title": "Bounded Image Blend with Mask"}}, "150": {"inputs": {"image": ["72", 0]}, "class_type": "Image Bounds", "_meta": {"title": "Image Bounds"}}}, "extra_data": {"extra_pnginfo": {"workflow": {"last_node_id": 162, "last_link_id": 324, "nodes": [{"id": 5, "type": "BasicGuider", "pos": [390.81829833984375, 174.06961059570312], "size": [161.1999969482422, 46], "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 5, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 9}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [4], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 23, "type": "Reroute", "pos": [-53.18169021606445, 52.069610595703125], "size": [75, 26], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 179}], "outputs": [{"name": "", "type": "MODEL", "links": [26, 27], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 90, "type": "Reroute", "pos": [-866.1282348632812, 72.44730377197266], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 291}], "outputs": [{"name": "", "type": "CLIP", "links": [183, 184], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 6, "type": "DifferentialDiffusion", "pos": [28.818309783935547, 170.06961059570312], "size": [184.8000030517578, 26], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 26}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [5, 227], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DifferentialDiffusion"}, "widgets_values": []}, {"id": 89, "type": "Reroute", "pos": [-859, 25], "size": [75, 26], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 290}], "outputs": [{"name": "", "type": "MODEL", "links": [179, 234], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 120, "type": "BasicGuider", "pos": [370, 780], "size": [161.1999969482422, 46], "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 227, "slot_index": 0}, {"name": "conditioning", "type": "CONDITIONING", "link": 228}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [223], "shape": 3}], "properties": {"Node name for S&R": "BasicGuider"}, "widgets_values": []}, {"id": 121, "type": "KSamplerSelect", "pos": [320, 880], "size": [210, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [224], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 124, "type": "Reroute", "pos": [-63.704559326171875, 621.5770874023438], "size": [75, 26], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 234}], "outputs": [{"name": "", "type": "MODEL", "links": [229]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 62, "type": "Reroute", "pos": [-769.93896484375, 748.3746948242188], "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 130}], "outputs": [{"name": "", "type": "MASK", "links": [131, 272], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 119, "type": "RandomNoise", "pos": [315, 626], "size": [292.23980712890625, 82.74410247802734], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [222], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed2}", "randomize"]}, {"id": 4, "type": "RandomNoise", "pos": [340.81829833984375, 24.069612503051758], "size": [277.9598693847656, 82], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [3], "shape": 3}], "properties": {"Node name for S&R": "RandomNoise"}, "widgets_values": ["${seed}", "randomize"]}, {"id": 10, "type": "FluxGuidance", "pos": [28.818309783935547, 261.0696105957031], "size": [211.60000610351562, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 15, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10, 230], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "FluxGuidance"}, "widgets_values": [3.5]}, {"id": 94, "type": "RepeatLatentBatch", "pos": [695.8182983398438, 401.0696105957031], "size": [315, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 189}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [190], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2]}, {"id": 104, "type": "ImageScaleBy", "pos": [-890, 1220], "size": [320, 80], "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 207, "label": "image"}, {"name": "scale_by", "type": "FLOAT", "link": 208, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [238], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "ImageScaleBy"}, "widgets_values": ["lanc<PERSON>s", 4]}, {"id": 105, "type": "MaskUpscale_LR", "pos": [-899, 1477], "size": [320, 60], "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 209, "label": "mask"}, {"name": "scale_by", "type": "FLOAT", "link": 210, "widget": {"name": "scale_by"}, "label": "scale_by"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [239], "slot_index": 0, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "MaskUpscale_LR"}, "widgets_values": [4]}, {"id": 145, "type": "DualCLIPLoader", "pos": [-1261.913330078125, 162.11502075195312], "size": [315, 106], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [291], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 146, "type": "UNETLoader", "pos": [-1259, 25], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [290], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["flux1-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 1, "type": "SamplerCustomAdvanced", "pos": [691, 124], "size": [355.20001220703125, 106], "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 3, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 4, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 6, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 7, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 190}], "outputs": [{"name": "output", "type": "LATENT", "links": [1], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 127, "type": "RepeatLatentBatch", "pos": [670, 970], "size": [315, 58], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 237}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [226], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "RepeatLatentBatch"}, "widgets_values": [2]}, {"id": 2, "type": "VAEDecode", "pos": [1121, 124], "size": [210, 46], "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1}, {"name": "vae", "type": "VAE", "link": 321}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [297], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 95, "type": "BoundedImageCropWithMask_v3_LR", "pos": [-1274, 1283], "size": [285.6000061035156, 142], "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 195}, {"name": "mask", "type": "MASK", "link": 240}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [207], "slot_index": 0, "shape": 3}, {"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [267], "slot_index": 1, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [209], "slot_index": 2, "shape": 3}, {"name": "SCALE_BY", "type": "FLOAT", "links": [208, 210], "slot_index": 3, "shape": 3}], "properties": {"Node name for S&R": "BoundedImageCropWithMask_v3_LR"}, "widgets_values": [40, 60]}, {"id": 130, "type": "RepeatImageBatch", "pos": [-435, 1369], "size": [254.88865661621094, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 243, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [294, 295], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "RepeatImageBatch"}, "widgets_values": [2]}, {"id": 150, "type": "Image Bounds", "pos": [-431, 1230], "size": [229.20001220703125, 26], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 300}], "outputs": [{"name": "IMAGE_BOUNDS", "type": "IMAGE_BOUNDS", "links": [299], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Image Bounds"}, "widgets_values": []}, {"id": 140, "type": "GrowMask", "pos": [-436, 1534], "size": [275.2359313964844, 82.3905258178711], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 272}], "outputs": [{"name": "MASK", "type": "MASK", "links": [273, 296], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "GrowMask"}, "widgets_values": [15, false]}, {"id": 56, "type": "Reroute", "pos": [-771.93896484375, 668.3746948242188], "size": [75, 26], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 168}], "outputs": [{"name": "", "type": "IMAGE", "links": [135, 195, 243, 300], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 148, "type": "Bounded Image Blend with Mask", "pos": [29, 1237], "size": [315, 142], "flags": {}, "order": 37, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 295}, {"name": "target_mask", "type": "MASK", "link": 296}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 299}, {"name": "source", "type": "IMAGE", "link": 297}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [302], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 138, "type": "Bounded Image Blend with Mask", "pos": [31, 1491], "size": [315, 142], "flags": {}, "order": 39, "mode": 0, "inputs": [{"name": "target", "type": "IMAGE", "link": 294}, {"name": "target_mask", "type": "MASK", "link": 273}, {"name": "target_bounds", "type": "IMAGE_BOUNDS", "link": 267}, {"name": "source", "type": "IMAGE", "link": 268}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [281], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "Bounded Image Blend with Mask"}, "widgets_values": [1, 0]}, {"id": 141, "type": "ImageBatch", "pos": [612, 1290], "size": [210, 46], "flags": {}, "order": 40, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 302}, {"name": "image2", "type": "IMAGE", "link": 281}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [289], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageBatch"}, "widgets_values": []}, {"id": 8, "type": "BasicScheduler", "pos": [340.81829833984375, 374.0696105957031], "size": [210, 106], "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 27}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [7], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 122, "type": "BasicScheduler", "pos": [320, 990], "size": [210, 106], "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 229}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [225], "shape": 3}], "properties": {"Node name for S&R": "BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 118, "type": "SamplerCustomAdvanced", "pos": [670, 740], "size": [355.20001220703125, 106], "flags": {}, "order": 36, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 222, "slot_index": 0}, {"name": "guider", "type": "GUIDER", "link": 223, "slot_index": 1}, {"name": "sampler", "type": "SAMPLER", "link": 224, "slot_index": 2}, {"name": "sigmas", "type": "SIGMAS", "link": 225, "slot_index": 3}, {"name": "latent_image", "type": "LATENT", "link": 226}], "outputs": [{"name": "output", "type": "LATENT", "links": [235], "slot_index": 0, "shape": 3}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 125, "type": "VAEDecode", "pos": [1120, 740], "size": [210, 46], "flags": {}, "order": 38, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 235}, {"name": "vae", "type": "VAE", "link": 322}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [268], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 11, "type": "CLIPTextEncode", "pos": [-569, 383], "size": [266.98651123046875, 76], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 184}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11, 231], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": [""]}, {"id": 123, "type": "InpaintModelConditioning", "pos": [13, 943], "size": [216.59999084472656, 138], "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 230}, {"name": "negative", "type": "CONDITIONING", "link": 231, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 324}, {"name": "pixels", "type": "IMAGE", "link": 238}, {"name": "mask", "type": "MASK", "link": 239, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [228], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [237], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 73, "type": "ConrainGrowMaskWithBlur", "pos": [-530, 596], "size": [315, 246], "flags": {"collapsed": false}, "order": 19, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 131}], "outputs": [{"name": "mask", "type": "MASK", "links": [193, 240], "slot_index": 0, "shape": 3}, {"name": "mask_inverted", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainGrowMaskWithBlur"}, "widgets_values": [5, 0, true, false, 10, 1, 1, false]}, {"id": 7, "type": "KSamplerSelect", "pos": [341, 274], "size": [210, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [6], "shape": 3}], "properties": {"Node name for S&R": "KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 144, "type": "VAELoader", "pos": [-1235, 370], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [321, 322, 323, 324], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["ae.safetensors"]}, {"id": 72, "type": "LoadImage", "pos": [-1274, 610], "size": [496.0507507324219, 444.35638427734375], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [168], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": [130], "slot_index": 1, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["${repairImage}", "image"], "color": "#232", "bgcolor": "#353"}, {"id": 16, "type": "CLIPTextEncode", "pos": [-569, 49], "size": [394.***********, 153.04241943359375], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 183, "slot_index": 0}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [15], "shape": 3}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["hand of young fashion model, clean nails, jewelry-free, high quality, high detail, realistic, no tattoos, no nail polish, no jewelry"]}, {"id": 9, "type": "InpaintModelConditioning", "pos": [22, 383], "size": [216.59999084472656, 138], "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "positive", "type": "CONDITIONING", "link": 10}, {"name": "negative", "type": "CONDITIONING", "link": 11, "slot_index": 1}, {"name": "vae", "type": "VAE", "link": 323}, {"name": "pixels", "type": "IMAGE", "link": 135}, {"name": "mask", "type": "MASK", "link": 193, "slot_index": 4}], "outputs": [{"name": "positive", "type": "CONDITIONING", "links": [9], "slot_index": 0, "shape": 3}, {"name": "negative", "type": "CONDITIONING", "links": null, "shape": 3}, {"name": "latent", "type": "LATENT", "links": [189], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "InpaintModelConditioning"}, "widgets_values": [true]}, {"id": 143, "type": "PrimitiveNode", "pos": [621, 1448], "size": [210, 58], "flags": {"collapsed": false}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [288], "slot_index": 0, "widget": {"name": "output_path"}}], "properties": {"Run widget replace on values": false}, "widgets_values": ["${outputPath}"], "color": "#232", "bgcolor": "#353"}, {"id": 142, "type": "ConrainImageSave", "pos": [1012, 1292], "size": [315, 274], "flags": {}, "order": 41, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 289}, {"name": "output_path", "type": "STRING", "link": 288, "widget": {"name": "output_path"}}], "outputs": [{"name": "image_cnt", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ConrainImageSave"}, "widgets_values": ["${outputPath}", "${fileNamePrefix}", "jpg", 300, 100, "true", "false", "false", "true", "true"]}], "links": [[1, 1, 0, 2, 0, "LATENT"], [3, 4, 0, 1, 0, "NOISE"], [4, 5, 0, 1, 1, "GUIDER"], [5, 6, 0, 5, 0, "MODEL"], [6, 7, 0, 1, 2, "SAMPLER"], [7, 8, 0, 1, 3, "SIGMAS"], [9, 9, 0, 5, 1, "CONDITIONING"], [10, 10, 0, 9, 0, "CONDITIONING"], [11, 11, 0, 9, 1, "CONDITIONING"], [15, 16, 0, 10, 0, "CONDITIONING"], [26, 23, 0, 6, 0, "MODEL"], [27, 23, 0, 8, 0, "MODEL"], [83, 19, 0, 2, 1, "VAE"], [84, 19, 0, 9, 2, "VAE"], [85, 19, 0, 2, 1, "VAE"], [86, 19, 0, 9, 2, "VAE"], [90, 19, 0, 2, 1, "VAE"], [91, 19, 0, 9, 2, "VAE"], [130, 72, 1, 62, 0, "*"], [131, 62, 0, 73, 0, "MASK"], [135, 56, 0, 9, 3, "IMAGE"], [168, 72, 0, 56, 0, "*"], [179, 89, 0, 23, 0, "*"], [183, 90, 0, 16, 0, "CLIP"], [184, 90, 0, 11, 0, "CLIP"], [189, 9, 2, 94, 0, "LATENT"], [190, 94, 0, 1, 4, "LATENT"], [193, 73, 0, 9, 4, "MASK"], [195, 56, 0, 95, 0, "IMAGE"], [207, 95, 0, 104, 0, "IMAGE"], [208, 95, 3, 104, 1, "FLOAT"], [209, 95, 2, 105, 0, "MASK"], [210, 95, 3, 105, 1, "FLOAT"], [222, 119, 0, 118, 0, "NOISE"], [223, 120, 0, 118, 1, "GUIDER"], [224, 121, 0, 118, 2, "SAMPLER"], [225, 122, 0, 118, 3, "SIGMAS"], [226, 127, 0, 118, 4, "LATENT"], [227, 6, 0, 120, 0, "MODEL"], [228, 123, 0, 120, 1, "CONDITIONING"], [229, 124, 0, 122, 0, "MODEL"], [230, 10, 0, 123, 0, "CONDITIONING"], [231, 11, 0, 123, 1, "CONDITIONING"], [234, 89, 0, 124, 0, "*"], [235, 118, 0, 125, 0, "LATENT"], [237, 123, 2, 127, 0, "LATENT"], [238, 104, 0, 123, 3, "IMAGE"], [239, 105, 0, 123, 4, "MASK"], [240, 73, 0, 95, 1, "MASK"], [243, 56, 0, 130, 0, "IMAGE"], [267, 95, 1, 138, 2, "IMAGE_BOUNDS"], [268, 125, 0, 138, 3, "IMAGE"], [272, 62, 0, 140, 0, "MASK"], [273, 140, 0, 138, 1, "MASK"], [281, 138, 0, 141, 1, "IMAGE"], [283, 19, 0, 9, 2, "VAE"], [284, 19, 0, 125, 1, "VAE"], [285, 19, 0, 2, 1, "VAE"], [286, 19, 0, 123, 2, "VAE"], [288, 143, 0, 142, 1, "STRING"], [289, 141, 0, 142, 0, "IMAGE"], [290, 146, 0, 89, 0, "*"], [291, 145, 0, 90, 0, "*"], [294, 130, 0, 138, 0, "IMAGE"], [295, 130, 0, 148, 0, "IMAGE"], [296, 140, 0, 148, 1, "MASK"], [297, 2, 0, 148, 3, "IMAGE"], [299, 150, 0, 148, 2, "IMAGE_BOUNDS"], [300, 56, 0, 150, 0, "IMAGE"], [302, 148, 0, 141, 0, "IMAGE"], [316, 144, 0, 125, 1, "VAE"], [317, 144, 0, 123, 2, "VAE"], [318, 144, 0, 9, 2, "VAE"], [319, 144, 0, 2, 1, "VAE"], [321, 144, 0, 2, 1, "VAE"], [322, 144, 0, 125, 1, "VAE"], [323, 144, 0, 9, 2, "VAE"], [324, 144, 0, 123, 2, "VAE"]], "groups": [{"id": 1, "title": "Group", "bounding": [-71, -50, 1456, 552], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Group", "bounding": [-1285, -51, 1200, 552], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [-1284, 526, 1199, 596], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "Group", "bounding": [-70, 529, 1456, 593], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Group", "bounding": [-1284, 1143, 782, 528], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Group", "bounding": [-486, 1143, 1871, 528], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4090909090909099, "offset": [2706.0296125418413, 96.7955337649278]}}, "version": 0.4, "widget_idx_map": {"4": {"noise_seed": 0}, "7": {"sampler_name": 0}, "8": {"scheduler": 0}, "119": {"noise_seed": 0}, "121": {"sampler_name": 0}, "122": {"scheduler": 0}}, "seed_widgets": {"4": 0, "119": 0}}}}}
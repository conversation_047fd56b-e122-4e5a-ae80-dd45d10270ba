package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ImageGroupCaptionUserQuery
 *
 * @version ImageGroupCaptionUserService.java v 0.1 2025-07-30 08:19:30
 */
@Data
public class ImageGroupCaptionUserQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Integer id;

    /** 图片组ID */
    private Integer imageGroupId;

    /** 用户ID */
    private Integer userId;

    /** 标注 */
    private String caption;

    /** 标注版本 */
    private String captionVersion;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CreativeTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * 创作任务 Service定义
 */
public interface CreativeTaskService {

    /**
     * 查询创作任务对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeTaskVO selectById(Integer id);

    /**
     * 获取全量信息
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeTaskVO selectFullById(Integer id);

    /**
     * 删除创作任务对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 删除创作任务对象
     *
     * @param ids 主键
     */
    void batchDeleteByIds(List<Integer> ids);

    /**
     * 添加创作任务对象
     *
     * @param creativeTask 对象参数
     * @return 返回结果
     */
    CreativeTaskVO insert(CreativeTaskVO creativeTask);

    /**
     * 修改创作任务对象
     *
     * @param creativeTask 对象参数
     */
    void updateByIdSelective(CreativeTaskVO creativeTask);

    /**
     * 带条件批量查询创作任务列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<CreativeTaskVO> queryCreativeTaskList(CreativeTaskQuery query);

    /**
     * 带条件分页查询创作任务
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<CreativeTaskVO> queryCreativeTaskByPage(CreativeTaskQuery query);

    /**
     * 初始化创作任务
     *
     * @param batch 批次
     */
    List<CreativeTaskVO> initTask(CreativeBatchVO batch);

    /**
     * 根据批次id查询创作任务是否存在
     *
     * @param batchId 批次id
     * @return 存在返回true
     */
    boolean isExistsTaskByBatchId(Integer batchId);

    /**
     * 根据批次id查询创作任务
     *
     * @param batchId 批次id
     * @return 进行中的创作任务
     */
    List<CreativeTaskVO> queryTaskByBatchId(Integer batchId);

    /**
     * 批量同步创作任务状态
     *
     * @param list      创作任务列表
     * @param serverUrl
     * @param serverId
     */
    void batchSyncStatus(List<CreativeTaskVO> list, String serverUrl, Integer serverId);

    /**
     * 根据promptId调用WS同步创作状态
     *
     * @param promptId     promptId
     * @param status       状态
     * @param doneNodes    完成的节点
     * @param nodeMax      节点总数
     * @param nodeSchadule 节点进度
     */
    void syncStatusByWS(String promptId, QueueCodeEnum status, List<String> doneNodes, Integer nodeMax,
                        Integer nodeSchadule);

    /**
     * 批量查询创建任务
     *
     * @param batchIds 批次 ids
     * @return 结果
     */
    Map<Integer, List<CreativeTaskVO>> batchQueryCreativeTask(List<Integer> batchIds);

    /**
     * 根据元素批量查询图片
     *
     * @param query 查询条件
     * @return 图片列表
     */
    PageInfo<String> queryImagesByElementWithPage(CreativeTaskQuery query);

    /**
     * 二次过滤掉前置任务未完成的多阶段任务
     *
     * @param unfinishedTasks 未完成的任务列表
     * @param isNeedSyncData  是否需要同步数据
     */
    void filterMultiProcess(List<CreativeTaskVO> unfinishedTasks, Boolean isNeedSyncData, CreativeTypeEnum type);

    /**
     * 批量插入任务
     *
     * @param tasks 任务列表
     */
    void batchInsert(List<CreativeTaskVO> tasks);

    /**
     * 根据任务获取工作流
     *
     * @param task 任务
     * @return 工作流
     */
    JSONObject fetchWorkflowByTask(CreativeTaskVO task);

    /**
     * 根据任务获取工作流
     *
     * @param taskId 任务id
     * @return 工作流
     */
    JSONObject fetchWorkflowByTask(Integer taskId);

}
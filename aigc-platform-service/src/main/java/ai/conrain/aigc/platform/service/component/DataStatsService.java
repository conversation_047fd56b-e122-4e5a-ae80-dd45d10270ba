/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component;

import java.util.List;

import ai.conrain.aigc.platform.service.model.stats.DeliveryStatsVO;

/**
 * 数据统计服务
 *
 * <AUTHOR>
 * @version : DataStatsService.java, v 0.1 2024/9/14 11:58 renxiao.wu Exp $
 */
public interface DataStatsService {
    /**
     * 统计时间区间内的交付数据
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 数据
     */
    List<DeliveryStatsVO> statsDelivery(String startDate, String endDate);
}

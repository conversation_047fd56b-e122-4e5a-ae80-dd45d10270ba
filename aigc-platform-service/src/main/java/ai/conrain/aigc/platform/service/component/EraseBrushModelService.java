package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.request.EraseBrushV2Request;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;

public interface EraseBrushModelService {

    /**
     * 消除笔任务执行
     * @param request 请求
     * @return 结果
     */
    CreativeBatchVO eraseBrushOperate(EraseBrushV2Request request);

    /**
     * 关闭消除笔任务,
     * - 如果没有涂抹, 直接删除这个 batch
     * - 如果已经有涂抹了, 则置为 FINISHED, 不再继续涂抹
     * @param batchId
     */
    void closeBatch(Integer batchId);
}

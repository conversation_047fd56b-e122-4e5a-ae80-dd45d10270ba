package ai.conrain.aigc.platform.service.model.converter;

import java.util.List;

import ai.conrain.aigc.platform.dal.entity.PermissionDO;
import ai.conrain.aigc.platform.service.model.query.PermissionQuery;
import ai.conrain.aigc.platform.service.model.vo.PermissionVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;

/**
 * PermissionConverter
 *
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
public class PermissionConverter {

    /**
     * DO -> VO
     */
    public static PermissionVO do2VO(PermissionDO from) {
        PermissionVO to = new PermissionVO();
        to.setId(from.getId());
        to.setAction(from.getAction());
        to.setName(from.getName());
        to.setConfig(from.getConfig());
        to.setAllowedSub(from.getAllowedSub() != null ? from.getAllowedSub() : false);
        to.setMemo(from.getMemo());
        to.setVersion(from.getVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static PermissionDO vo2DO(PermissionVO from) {
        PermissionDO to = new PermissionDO();
        to.setId(from.getId());
        to.setAction(from.getAction());
        to.setName(from.getName());
        to.setConfig(from.getConfig());
        to.setAllowedSub(from.isAllowedSub());
        to.setMemo(from.getMemo());
        to.setVersion(from.getVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static PermissionQuery do2Query(PermissionDO from) {
        PermissionQuery to = new PermissionQuery();
        to.setId(from.getId());
        to.setAction(from.getAction());
        to.setName(from.getName());
        to.setConfig(from.getConfig());
        to.setAllowedSub(from.getAllowedSub());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static PermissionDO query2DO(PermissionQuery from) {
        PermissionDO to = new PermissionDO();
        to.setId(from.getId());
        to.setAction(from.getAction());
        to.setName(from.getName());
        to.setConfig(from.getConfig());
        to.setAllowedSub(from.getAllowedSub());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<PermissionVO> doList2VOList(List<PermissionDO> list) {
        return CommonUtil.listConverter(list, PermissionConverter::do2VO);
    }
}
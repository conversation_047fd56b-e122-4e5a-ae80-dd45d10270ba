package ai.conrain.aigc.platform.service.model.common;

import lombok.Getter;

@Getter
public class BizException extends RuntimeException {

    private static final long serialVersionUID = -5624010505486892657L;

    private ResultCode code;

    private Object data;

    public BizException(String message) {
        super(message);
        this.code = ResultCode.SYS_ERROR;
    }

    public BizException(ResultCode code) {
        super(code.getDesc());
        this.code = code;
    }

    public BizException(ResultCode code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(ResultCode code, String message, Object data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public BizException(ResultCode code, Throwable cause, String message) {
        super(message, cause);
        this.code = code;
    }

    public BizException(ResultCode code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public BizException(ResultCode code, Object data) {
        this.code = code;
        this.data = data;
    }
}

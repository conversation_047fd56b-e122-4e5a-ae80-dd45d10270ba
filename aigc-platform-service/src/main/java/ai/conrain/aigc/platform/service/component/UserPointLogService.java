package ai.conrain.aigc.platform.service.component;

import java.util.List;

import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.UserPointLogQuery;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointUsageInfoVO;

/**
 * 用户算力流水 Service定义
 *
 * <AUTHOR>
 * @version UserPointLogService.java v 0.1 2024-06-21 04:14:12
 */
public interface UserPointLogService {

    /**
     * 查询用户算力流水对象
     *
     * @param id 主键
     * @return 返回结果
     */
    UserPointLogVO selectById(Integer id);

    /**
     * 删除用户算力流水对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加用户算力流水对象
     *
     * @param userPointLog 对象参数
     * @return 返回结果
     */
    UserPointLogVO insert(UserPointLogVO userPointLog);

    /**
     * 修改用户算力流水对象
     *
     * @param userPointLog 对象参数
     */
    void updateByIdSelective(UserPointLogVO userPointLog);

    /**
     * 带条件批量查询用户算力流水列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<UserPointLogVO> queryUserPointLogList(UserPointLogQuery query);

    /**
     * 带条件查询用户算力流水数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryUserPointLogCount(UserPointLogQuery query);

    /**
     * 带条件分页查询用户算力流水
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<UserPointLogVO> queryUserPointLogByPage(UserPointLogQuery query);

    /**
     * 带条件分页查询用户算力流水
     *
     * @param query
     * @return
     */
    PageInfo<UserPointUsageInfoVO> queryPointUsageInfoByPage(UserPointLogQuery query);

    /**
     * 根据业务id 和 业务类型 锁定用户算力流水
     *
     * @param bizId        业务id
     * @param userId       用户id
     * @param pointLogType 积分流水类型
     * @return 用户算力流水
     */
    UserPointLogVO lockByBizId(Integer bizId, Integer userId, PointLogTypeEnum pointLogType);

    /**
     * 根据用户id和日期范围获取用户消耗缪斯点
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 用户 id 列表
     * @return 消耗缪斯点数
     */
    Integer getCustomerConsumptionPoints(String startDate, String endDate, List<Integer> userIdList);

    /**
     * 根据日期范围获取付费用户数
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param customUserList 用户id列表
     * @return 用户数量
     */
    Integer getPayUserCountByDate(String startDate, String endDate, List<Integer> customUserList);

    /**
     * 根据日期范围获取付费用户列表
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param customUserList 用户id列表
     * @return 用户列表
     */
    List<Integer> getPayUserByDate(String startDate, String endDate, List<Integer> customUserList);
}
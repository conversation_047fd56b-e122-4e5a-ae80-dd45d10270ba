/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;
import java.math.BigDecimal;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CATTY_DECIMAL_FORMAT;

/**
 * 斤BigDecimal序列化器
 *
 * <AUTHOR>
 * @version : CattyDecimalSerializer.java, v 0.1 2023/10/19 17:03 renxiao.wu Exp $
 */
public class CattyDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
        if (null == bigDecimal) {
            return;
        }

        // 格式化保留两位小数
        jsonGenerator.writeString(CATTY_DECIMAL_FORMAT.format(bigDecimal));
    }
}

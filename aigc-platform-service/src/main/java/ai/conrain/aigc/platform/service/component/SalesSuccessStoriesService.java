package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.SalesSuccessStoriesQuery;
import ai.conrain.aigc.platform.service.model.vo.SalesSuccessStoriesVO;
import java.util.List;

/**
 * 销售成功案例 Service定义
 *
 * <AUTHOR>
 * @version SalesSuccessStoriesService.java v 0.1 2025-06-26 05:49:50
 */
public interface SalesSuccessStoriesService {
	
	/**
	 * 查询销售成功案例对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	SalesSuccessStoriesVO selectById(Integer id);

	/**
	 * 删除销售成功案例对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加销售成功案例对象
	 * @param salesSuccessStories 对象参数
	 * @return 返回结果
	 */
	SalesSuccessStoriesVO insert(SalesSuccessStoriesVO salesSuccessStories);

	/**
	 * 修改销售成功案例对象
	 * @param salesSuccessStories 对象参数
	 */
	void updateByIdSelective(SalesSuccessStoriesVO salesSuccessStories);

	/**
	 * 带条件批量查询销售成功案例列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<SalesSuccessStoriesVO> querySalesSuccessStoriesList(SalesSuccessStoriesQuery query);

	/**
	 * 带条件查询销售成功案例数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long querySalesSuccessStoriesCount(SalesSuccessStoriesQuery query);

	/**
	 * 带条件分页查询销售成功案例
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<SalesSuccessStoriesVO> querySalesSuccessStoriesByPage(SalesSuccessStoriesQuery query);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * 重置排序请求
 *
 * <AUTHOR>
 * @version : ResetOrderRequest.java, v 0.1 2024/5/24 21:46 renxiao.wu Exp $
 */
@Data
public class ResetOrderRequest implements Serializable {
    private static final long serialVersionUID = 5840083880534615741L;

    /** 排序项列表 */
    @NotEmpty
    private List<ResetOrderItem> items;

    @Data
    public static class ResetOrderItem implements Serializable {
        private static final long serialVersionUID = 5859259404758690081L;
        /** id号 */
        @NotNull
        private Integer id;
        /** 排序 */
        @NotNull
        private Integer order;
    }
}

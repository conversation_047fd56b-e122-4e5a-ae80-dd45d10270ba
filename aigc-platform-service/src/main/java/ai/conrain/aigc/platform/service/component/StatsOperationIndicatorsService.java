package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsOperationIndicatorsDO;
import ai.conrain.aigc.platform.service.model.query.StatsOperationIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsExcelVO;
import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-16 11:38:50
 */
public interface StatsOperationIndicatorsService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	StatsOperationIndicatorsVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param statsOperationIndicators 对象参数
	 * @return 返回结果
	 */
	StatsOperationIndicatorsVO insert(StatsOperationIndicatorsVO statsOperationIndicators);

	/**
	 * 修改对象
	 * @param statsOperationIndicators 对象参数
	 */
	void updateByIdSelective(StatsOperationIndicatorsVO statsOperationIndicators);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<StatsOperationIndicatorsVO> queryStatsOperationIndicatorsList(StatsOperationIndicatorsQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryStatsOperationIndicatorsCount(StatsOperationIndicatorsQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<StatsOperationIndicatorsVO> queryStatsOperationIndicatorsByPage(StatsOperationIndicatorsQuery query);

	/**
	 * 批量插入或更新
	 *
	 * @param statsList 批量数据
	 * @return 记录条数
	 */
	int batchInsertOrUpdate(List<StatsOperationIndicatorsVO> statsList);

	/**
	 * 根据日期和统计周期查询统计数据
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param statsType 统计类型
	 * @return 统计数据列表
	 */
	List<StatsOperationIndicatorsVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType);

	/**
	 * 下载统计数据
	 * @param query 查询条件
	 * @return 封装之后的数据
	 */
    List<StatsOperationIndicatorsExcelVO> doDownload(StatsOperationIndicatorsQuery query);
}
package ai.conrain.aigc.platform.service.component.impl;


import ai.conrain.aigc.platform.dal.dao.UserDAO;
import ai.conrain.aigc.platform.dal.entity.UserDO;
import ai.conrain.aigc.platform.dal.example.UserExample;
import ai.conrain.aigc.platform.service.component.SubUserService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserConverter;
import ai.conrain.aigc.platform.service.model.request.SubUserUpdate;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户子账号服务实现
 *
 * <AUTHOR>
 * @version : SubUserServiceImpl.java, v 0.1 2024/1/20 20:28 renxiao.wu Exp $
 */
@Slf4j
@Service
public class SubUserServiceImpl implements SubUserService {
    /** 用户DAO */
    @Autowired
    private UserDAO userDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public List<UserVO> queryList() {
        OperationContext context = OperationContextHolder.getContext();
        AssertUtil.assertTrue(context.isMasterUser(), "查询子账号列表失败,当前账号非主账号");
        Integer userId = context.getMasterUser();

        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();
        criteria.andMasterIdEqualTo(userId);
        criteria.andUserTypeEqualTo(UserTypeEnum.SUB.getCode());
        example.setOrderByClause("id desc");

        List<UserDO> list = userDAO.selectByExample(example);

        return UserConverter.doList2VOList(list);
    }

    @Override
    public UserVO create(UserRegister user) {
        OperationContext context = OperationContextHolder.getContext();
        if (user.isOnlyMasterCanCreate()) {
            AssertUtil.assertTrue(context.isMasterUser(), ResultCode.ILLEGAL_PERMISSION, "新建子账户失败,当前账号非主账号");
        }
        Integer userId = context.getMasterUser();

        String mobile = user.getMobile();

        //1.根据手机号码查询用户是否已存在
        userService.checkMobileExists(mobile);

        //2.检查子账号数是否超过n
        checkSubUserCount(userId);

        //3.初始化db数据
        UserDO target = new UserDO();

        target.setNickName(user.getNickName());
        target.setLoginId(mobile);
        target.setMobile(mobile);
        if (StringUtils.isNotBlank(user.getRoleType())) {
            AssertUtil.assertTrue(RoleTypeEnum.getByCode(user.getRoleType()) != null, ResultCode.PARAM_INVALID,
                    "新建子账户失败,角色类型非法");
            target.setRoleType(user.getRoleType());
        } else {
            target.setRoleType(RoleTypeEnum.MERCHANT.getCode());
        }
        target.setOperatorId(userId);
        target.setDeleted(false);
        target.setLoginFailCount(0);
        //设置子账号属性
        target.setUserType(UserTypeEnum.SUB.getCode());
        target.setMasterId(userId);
        target.setStatus(UserStatusEnum.ENABLED.getCode());
        target.setCustomRole(user.getCustomRole());

        //4.插入数据库
        int n = userDAO.insertSelective(target);
        AssertUtil.assertTrue(n > 0, "新建子账户失败");
        AssertUtil.assertNotNull(target.getId(), "新建子账户返回id为空");

        if (StringUtils.equals(user.getRoleType(), RoleTypeEnum.DISTRIBUTOR.getCode()) && user.isInitDistributorRelatedAccounts()) {
            userService.createRelatedAccounts4Distributor(target.getNickName(), userId, target.getId());
        }

        return UserConverter.do2VO(target);
    }

    @Override
    public void update(SubUserUpdate user) {
        Integer operator = OperationContextHolder.getOperatorUserId();
        Integer userId = user.getUserId();

        UserDO target = userDAO.selectByPrimaryKey(userId);
        AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "修改用户信息失败");

        //1.修改的权限控制，要么是自己账号，要么修改的是下属子账户
        if (!operator.equals(userId)) {
            if (!StringUtils.equals(target.getUserType(), UserTypeEnum.SUB.getCode()) || !operator.equals(
                    target.getMasterId())) {
                log.error("目标修改的用户非当前用户或所属子账号，operator={},userId={},masterId={}", operator, userId,
                        target.getMasterId());
                throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
            }
        }

        //2.如果修改了手机号，则检查手机号码是否已存在
        if (StringUtils.isNotBlank(user.getMobile()) && !StringUtils.equals(user.getMobile(), target.getMobile())) {
            userService.checkMobileExists(user.getMobile());
        }

        //3.更新
        UserDO data = new UserDO();
        data.setId(userId);
        data.setMobile(user.getMobile());
        data.setLoginId(user.getMobile());
        data.setNickName(user.getNickName());

        int cnt = userDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "修改用户信息失败");

    }

    /**
     * 停用/启用子账号
     *
     * @param userId 用户id
     * @param status 状态
     */
    @Override
    public void enableOrDisable(Integer userId, UserStatusEnum status) {
        Integer operator = OperationContextHolder.getOperatorUserId();

        UserDO target = userDAO.selectByPrimaryKey(userId);
        AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "停用/启用子账号失败");

        if (!StringUtils.equals(target.getUserType(), UserTypeEnum.SUB.getCode()) || !operator.equals(
                target.getMasterId())) {
            log.error("目标停用/启用子账号非当前用户的子账号，operator={},userId={},masterId={}", operator, userId,
                    target.getMasterId());
            throw new BizException(ResultCode.ILLEGAL_PERMISSION, "权限非法");
        }

        if (status.equals(UserStatusEnum.getByCode(target.getStatus()))) {
            log.warn("目标停用/启用子账号状态与当前状态一致，不需要更新数据库，userId={},status={}", userId, status);
            return;
        }

        //3.更新
        UserDO data = new UserDO();
        data.setId(userId);
        data.setStatus(status.getCode());

        int cnt = userDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "修改用户信息失败");
    }

    /**
     * 检查子账号数是否超过n
     *
     * @param userId 主账号id
     */
    private void checkSubUserCount(Integer userId) {
        UserExample example = new UserExample();
        UserExample.Criteria criteria = example.createCriteria();
        criteria.andMasterIdEqualTo(userId);
        criteria.andUserTypeEqualTo(UserTypeEnum.SUB.getCode());

        long count = userDAO.countByExample(example);

        int maxCount = systemConfigService.queryIntValue(SystemConstants.MAX_SUB_USER_COUNT,
                CommonConstants.MAX_SUB_USER_COUNT);

        AssertUtil.assertTrue(count <= maxCount, ResultCode.OVER_MAX_SUB_COUNT,
                "超出最大子账号数量" + maxCount + "，userId=" + userId);
    }
}
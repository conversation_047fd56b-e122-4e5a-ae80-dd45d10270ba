package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupCaptionUserDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionUserDO;
import ai.conrain.aigc.platform.service.component.CaptionUserService;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupCaptionUserConverter;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.util.MapUtil;
import com.alibaba.fastjson2.JSONObject;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO;
import ai.conrain.aigc.platform.dal.example.ImageCaptionUserExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.converter.ImageCaptionUserConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionUserDAO;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import org.springframework.transaction.annotation.Transactional;

/**   
 * ImageCaptionUserService实现
 *
 * <AUTHOR>
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
@Slf4j
@Service
public class ImageCaptionUserServiceImpl implements ImageCaptionUserService {

	/** DAO */
	@Autowired
	private ImageCaptionUserDAO imageCaptionUserDAO;
    @Autowired
    private ImageGroupCaptionUserDAO imageGroupCaptionUserDAO;
    @Autowired
    private CaptionUserService captionUserService;

	@Override
	public ImageCaptionUserVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageCaptionUserDO data = imageCaptionUserDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return ImageCaptionUserConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageCaptionUserDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCaptionUser失败");
	}

	@Override
	public ImageCaptionUserVO insert(ImageCaptionUserVO imageCaptionUser) {
		AssertUtil.assertNotNull(imageCaptionUser, ResultCode.PARAM_INVALID, "imageCaptionUser is null");
		AssertUtil.assertTrue(imageCaptionUser.getId() == null, ResultCode.PARAM_INVALID, "imageCaptionUser.id is present");

		//创建时间、修改时间兜底
		if (imageCaptionUser.getCreateTime() == null) {
			imageCaptionUser.setCreateTime(new Date());
		}

		if (imageCaptionUser.getModifyTime() == null) {
			imageCaptionUser.setModifyTime(new Date());
		}

		ImageCaptionUserDO data = ImageCaptionUserConverter.vo2DO(imageCaptionUser);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = imageCaptionUserDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCaptionUser失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageCaptionUser返回id为空");
		imageCaptionUser.setId(data.getId());
		return imageCaptionUser;
	}

    @Override
    @Transactional
    public ImageCaptionUserVO save(ImageCaptionUserVO imageCaptionUser) {
        ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
        imageCaptionUserQuery.setImageId(imageCaptionUser.getImageId());
        imageCaptionUserQuery.setUserId(imageCaptionUser.getUserId());
        imageCaptionUserQuery.setType(imageCaptionUser.getType());
        imageCaptionUserQuery.setOriginalId(imageCaptionUser.getOriginalId());
        List<ImageCaptionUserVO> existingCaptions = queryImageCaptionUserList(imageCaptionUserQuery);
        if (CollectionUtils.isEmpty(existingCaptions)) {
            return insert(imageCaptionUser);
        } else {
            imageCaptionUser.setId(existingCaptions.getFirst().getId());
            updateByIdSelective(imageCaptionUser);
            return imageCaptionUser;
        }
    }

    @Override
	public void updateByIdSelective(ImageCaptionUserVO imageCaptionUser) {
		AssertUtil.assertNotNull(imageCaptionUser, ResultCode.PARAM_INVALID, "imageCaptionUser is null");
    	AssertUtil.assertTrue(imageCaptionUser.getId() != null, ResultCode.PARAM_INVALID, "imageCaptionUser.id is null");

		//修改时间必须更新
		imageCaptionUser.setModifyTime(new Date());
		ImageCaptionUserDO data = ImageCaptionUserConverter.vo2DO(imageCaptionUser);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = imageCaptionUserDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCaptionUser失败，影响行数:" + n);
	}

	@Override
	public List<ImageCaptionUserVO> queryImageCaptionUserList(ImageCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        if ("style".equals(query.getType())) {
            ImageGroupCaptionUserQuery imageGroupCaptionUserQuery = new ImageGroupCaptionUserQuery();
            imageGroupCaptionUserQuery.setImageGroupId(query.getImageId());
            imageGroupCaptionUserQuery.setUserId(query.getUserId());
            List<ImageGroupCaptionUserDO> list = imageGroupCaptionUserDAO.selectByExample(
                ImageGroupCaptionUserConverter.query2Example(imageGroupCaptionUserQuery));
            return list
                .stream()
                .map(captionUser -> {
                    ImageCaptionUserDO imageCaptionUser = new ImageCaptionUserDO();
                    imageCaptionUser.setId(captionUser.getId());
                    imageCaptionUser.setImageId(captionUser.getImageGroupId());
                    imageCaptionUser.setUserId(captionUser.getUserId());
                    imageCaptionUser.setCaption(captionUser.getCaption());
                    return imageCaptionUser;
                })
                .map(ImageCaptionUserConverter::do2VO)
                .collect(Collectors.toList());
        }
        List<ImageCaptionUserDO> list = imageCaptionUserDAO.selectByExample(ImageCaptionUserConverter.query2Example(query));
		return ImageCaptionUserConverter.doList2VOList(list);
	}

	@Override
	public Long queryImageCaptionUserCount(ImageCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageCaptionUserExample example = ImageCaptionUserConverter.query2Example(query);
		return imageCaptionUserDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询图像标注表用户标注数据，存储用户和大模型打标的数据
	 */
	@Override
	public PageInfo<ImageCaptionUserVO> queryImageCaptionUserByPage(ImageCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ImageCaptionUserVO> page = new PageInfo<>();

		ImageCaptionUserExample example = ImageCaptionUserConverter.query2Example(query);
		long totalCount = imageCaptionUserDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ImageCaptionUserDO> list = imageCaptionUserDAO.selectByExample(example);
		page.setList(ImageCaptionUserConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    public void saveFromImage(Integer imageId, String captionType, JSONObject data) {
        if (data == null) {
            return;
        }
        AssertUtil.assertNotBlank(captionType, ResultCode.PARAM_INVALID, "captionType is blank");
        String source = StringUtils.defaultIfBlank(data.getString("source"), "gemini");
        CaptionUserVO user = captionUserService.getOrCreateByUsername(source);
        Map<String, Object> flattenedData = MapUtil.flatten(data);
        ImageCaptionUserVO imageCaptionUserVO = new ImageCaptionUserVO();
        imageCaptionUserVO.setUserId(user.getId());
        imageCaptionUserVO.setImageId(imageId);
        imageCaptionUserVO.setType(captionType);
        imageCaptionUserVO.setCaption(JSONObject.from(flattenedData));
        save(imageCaptionUserVO);
    }

}
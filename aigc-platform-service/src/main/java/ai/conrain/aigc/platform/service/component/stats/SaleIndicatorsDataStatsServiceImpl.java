package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.query.*;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.DateUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.PROFILE_RELATED_MERCHANT_ACCOUNT;

@Slf4j
@Service("saleIndicatorsDataStatsServiceImpl")
public class SaleIndicatorsDataStatsServiceImpl extends AbstractDataStatsServiceImpl {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderInfoService orderInfoService;
    @Autowired
    private UserPointLogService userPointLogService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private DistributorCustomerService distributorCustomerService;
    @Autowired
    private StatsSaleIndicatorsService statsSaleIndicatorsService;
    @Autowired
    private UserProfileService userProfileService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.SALE_INDICATORS;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {

        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case DAILY:
                return executeDailyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case WEEKLY:
                return executeWeeklyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case MONTHLY:
                return executeMonthlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case QUARTERLY:
                return executeQuarterlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case TOTAL:
                return executeTotalBusinessStats(startDate, endDate, isTotal);
            default:
                return 0;
        }

    }

    /**
     * 执行日统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计数据
     */
    private int executeDailyBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum periodEnum) {
        // 1、获取主账户以及子账号用户信息
        List<UserVO> userInfoList = queryUserWithChildren();

        // 2、填充数据
        List<StatsSaleIndicatorsVO> rseultDataList = buildStatsIndicatorsData(storageDate, startDate, endDate,
                periodEnum, userInfoList);

        // 3、保存统计数据
        return saveStatsData(rseultDataList);
    }

    /**
     * 执行周统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计数据
     */
    private int executeWeeklyBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行月统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计数据
     */
    private int executeMonthlyBusinessStats(String storageDate, String startDate, String endDate,
                                            StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行季度统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计数据
     */
    private int executeQuarterlyBusinessStats(String storageDate, String startDate, String endDate,
                                              StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行周期性业务统计的通用方法
     *
     * @param storageDate      存储日期
     * @param startDate        开始日期
     * @param endDate          结束日期
     * @param targetPeriodEnum 目标统计周期
     * @return 返回处理的记录数
     */
    private int executePeriodBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum targetPeriodEnum) {
        // 查询某个时间段内的统计数据
        List<StatsSaleIndicatorsVO> sourceStatsList = statsSaleIndicatorsService.selectStatsInfoByDateAndPeriod(
                startDate, endDate, StatsPeriodEnum.DAILY.getCode());

        // 如果统计数据为空，则直接返回0
        if (CollectionUtils.isEmpty(sourceStatsList)) {
            log.info("执行{}周期统计时，时间范围{}至{}内未找到日统计数据",
                    targetPeriodEnum.getDesc(), startDate, endDate);
            return 0;
        }

        // 1、 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 根据用户ID进行分组
        Map<Integer, List<StatsSaleIndicatorsVO>> userIdStatsMap = sourceStatsList.stream()
                .collect(Collectors.groupingBy(StatsSaleIndicatorsVO::getUserId));

        // 汇总每个用户的统计数据
        List<StatsSaleIndicatorsVO> targetStatsList = new ArrayList<>();

        for (Map.Entry<Integer, List<StatsSaleIndicatorsVO>> entry : userIdStatsMap.entrySet()) {
            Integer userId = entry.getKey();
            List<StatsSaleIndicatorsVO> userStats = entry.getValue();

            // 确保有数据
            if (CollectionUtils.isEmpty(userStats)) {
                continue;
            }

            // 获取销售关联的所有客户
            SalesCustomerData customerData = getSalesCustomerData(userId);
            List<Integer> distinctCustomerIds = customerData.getDistinctCustomerIds();

            // 获取最新的一条记录，用于提取用户名称和父级ID等基本信息
            StatsSaleIndicatorsVO firstStat = userStats.stream()
                    .max(Comparator.comparing(StatsSaleIndicatorsVO::getCreateTime))
                    .orElse(userStats.get(0));

            // 进行累加以及比例计算
            StatsSaleIndicatorsVO targetStats = new StatsSaleIndicatorsVO();
            targetStats.setUserId(userId);
            targetStats.setStatsDate(storageDate);
            targetStats.setStatsType(targetPeriodEnum.getCode());
            targetStats.setName(firstStat.getName());
            targetStats.setParentId(firstStat.getParentId());
            targetStats.setCreateTime(new Date());
            targetStats.setModifyTime(new Date());

            // 初始化各项指标的默认值，避免空值问题
            targetStats.setClothesExpCount(0);
            targetStats.setCustomerConversionCount(0);
            targetStats.setCustomerConsumptionPoints(0);
            targetStats.setCustomerActivityRate("0.00");
            targetStats.setCustomerRepurchaseRate("0.00");
            targetStats.setCustomModelCustomers("0.00");
            targetStats.setCustomSceneCustomers("0.00");
            targetStats.setCustomerProtectionMetrics(0);
            targetStats.setCreateCount(0);

            // 累加各数值型指标
            int totalClothesExpCount = userStats.stream()
                    .filter(data -> data.getClothesExpCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getClothesExpCount)
                    .sum();

            int totalCustomerConversionCount = userStats.stream()
                    .filter(data -> data.getCustomerConversionCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCustomerConversionCount)
                    .sum();

            int totalCustomerConsumptionPoints = userStats.stream()
                    .filter(data -> data.getCustomerConsumptionPoints() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCustomerConsumptionPoints)
                    .sum();

            int totalCreateCount = userStats.stream()
                    .filter(data -> data.getCreateCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCreateCount)
                    .sum();

            // 设置累加的数值型指标
            targetStats.setClothesExpCount(totalClothesExpCount);
            targetStats.setCustomerConversionCount(totalCustomerConversionCount);
            targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
            targetStats.setCreateCount(totalCreateCount);


            // 若为企业则取最后一条记录的客户保护指标数据
            if (firstStat.getParentId() == 0) {
                targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());
            } else {
                // 重新计算客户保护指标
                int customerProtectionMetrics = getCustomerProtectionMetrics(startDate, endDate, userId, distinctCustomerIds);

                targetStats.setCustomerProtectionMetrics(customerProtectionMetrics);
            }


            // 计算加权平均比率指标 - 使用我们优化后的calculateWeightedAverageRatios方法
            calculateWeightedAverageRatios(userStats, targetStats);

            // 获取销售的准确客户总数
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            Long customerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

            // 重新计算模型比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, targetStats);
            // 重新计算场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, targetStats);


            // 将汇总的统计数据添加到结果列表中
            targetStatsList.add(targetStats);

            log.debug("完成用户ID:{}的{}周期统计，共汇总{}条日统计数据",
                    userId, targetPeriodEnum.getDesc(), userStats.size());
        }

        // 保存所有统计数据到数据库
        int insertCount = saveStatsData(targetStatsList);

        log.info("执行{}周期统计完成，时间范围{}至{}，共处理{}条记录",
                targetPeriodEnum.getDesc(), startDate, endDate, insertCount);

        return insertCount;
    }

    /**
     * 执行总统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param isTotal   是否汇总
     * @return 统计数据
     */
    private int executeTotalBusinessStats(String startDate, String endDate, boolean isTotal) {
        return 0;
    }

    /**
     * 构建统计数据
     *
     * @param storageDate  存储日期
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @param periodEnum   统计周期
     * @param userInfoList 用户信息列表
     * @return 统计数据列表
     */
    protected List<StatsSaleIndicatorsVO> buildStatsIndicatorsData(String storageDate, String startDate, String endDate,
                                                                   StatsPeriodEnum periodEnum, List<UserVO> userInfoList) {
        List<StatsSaleIndicatorsVO> resultDataList = new ArrayList<>();

        userInfoList.forEach(masterUser -> {
            // 取出主账号部分信息（用于最终汇总的记录）
            String corpName = masterUser.getCorpName();
            Integer corpOrgId = masterUser.getCorpOrgId();
            // 若corpOrgId为空，则跳过本次循环
            if (corpOrgId == null) {
                return;
            }

            // 2、取出子账号信息
            List<UserVO> subUserVoList = masterUser.getChildren();
            if (CollectionUtils.isEmpty(subUserVoList)) {
                subUserVoList = new ArrayList<>();
            }

            // 3、 合并为一个列表
            subUserVoList.add(masterUser);

            // 4、 构建统计数据
            List<StatsSaleIndicatorsVO> subResultDataList = doBuildStatsIndicatorsData(storageDate, startDate, endDate,
                    periodEnum, subUserVoList, corpOrgId);

            // 5、 构建总统计数据
            doBuildTotalStatsIndicatorsData(storageDate, periodEnum, subResultDataList, corpName, corpOrgId);

            // 6、 添加到结果列表中
            resultDataList.addAll(subResultDataList);
        });

        // 返回处理完成的结果
        return resultDataList;
    }

    /**
     * 构建统计数据
     *
     * @param storageDate   存储日期
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param periodEnum    统计周期
     * @param subUserVoList 子账号列表
     * @param parentId      父级ID
     * @return 统计数据列表
     */
    private List<StatsSaleIndicatorsVO> doBuildStatsIndicatorsData(String storageDate, String startDate, String endDate,
                                                                   StatsPeriodEnum periodEnum, List<UserVO> subUserVoList, Integer parentId) {
        // 初始化构建统计数据
        List<StatsSaleIndicatorsVO> resultDataList = new ArrayList<>();

        // 1、 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 遍历用户列表构建统计数据
        subUserVoList.forEach(userInfo -> {
            // 参数提取
            Integer userId = userInfo.getId();

            // 2、 获取销售关联的所有客户及虚拟账户客户(单个销售的客户数据)
            SalesCustomerData customerData = getSalesCustomerData(userId);
            List<Integer> distinctCustomerIds = customerData.getDistinctCustomerIds();
            List<Integer> virtualUserIds = customerData.getVirtualUserIds();

            // 获取销售的准确客户总数
            long customerCount = distinctCustomerIds.size();


            // 初始化统计数据
            StatsSaleIndicatorsVO statsSaleIndicatorsVO = new StatsSaleIndicatorsVO();
            statsSaleIndicatorsVO.setParentId(parentId);
            statsSaleIndicatorsVO.setStatsDate(storageDate);
            statsSaleIndicatorsVO.setStatsType(periodEnum.getCode());
            statsSaleIndicatorsVO.setUserId(userInfo.getId());
            statsSaleIndicatorsVO.setName(userInfo.getNickName());
            statsSaleIndicatorsVO.setCreateTime(new Date());
            statsSaleIndicatorsVO.setModifyTime(new Date());

            // 初始化各项指标的默认值，避免空值问题
            statsSaleIndicatorsVO.setClothesExpCount(0);
            statsSaleIndicatorsVO.setCustomerConversionCount(0);
            statsSaleIndicatorsVO.setCustomerConsumptionPoints(0);
            statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
            statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
            statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
            statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
            statsSaleIndicatorsVO.setCustomerProtectionMetrics(0);
            statsSaleIndicatorsVO.setCreateCount(0);

            // 1、服装体验量
            statsSaleIndicatorsVO.setClothesExpCount(getClothesExpCount(startDate, endDate, userId));

            // 2、客户转换量（新签3999以上）
            statsSaleIndicatorsVO.setCustomerConversionCount(
                    getCustomerConversionCount(startDate, endDate, userId, distinctCustomerIds));

            // 3、客户消耗点数
            statsSaleIndicatorsVO.setCustomerConsumptionPoints(
                    getCustomerConsumptionPoints(startDate, endDate, userId, distinctCustomerIds,
                            virtualUserIds));

            // 4、客户活跃率
            getCustomerActivityRate(startDate, endDate, userId, distinctCustomerIds, customerCount,
                    statsSaleIndicatorsVO);

            // 5、客户复购率（季度/半年）
            getCustomerRepurchaseRate(startDate, endDate, userId, distinctCustomerIds, customerCount,
                    statsSaleIndicatorsVO);

            // 6、定制模特比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, statsSaleIndicatorsVO);

            // 7、定制场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, statsSaleIndicatorsVO);

            // 8、客户保护指标
            statsSaleIndicatorsVO.setCustomerProtectionMetrics(
                    getCustomerProtectionMetrics(startDate, endDate, userId, distinctCustomerIds));

            // 9、销售出图数量
            statsSaleIndicatorsVO.setCreateCount(
                    getSalesDrawCount(startDate, endDate, userId, virtualUserIds));

            // 添加至结果列表中
            resultDataList.add(statsSaleIndicatorsVO);
        });

        // 返回构建完成的结果
        return resultDataList;
    }

    /**
     * 构建总统计数据
     *
     * @param storageDate       存储日期
     * @param periodEnum        统计周期
     * @param subResultDataList 子账号统计数据列表
     * @param corpName          公司名称
     * @param corpOrgId         公司ID
     */
    private void doBuildTotalStatsIndicatorsData(String storageDate, StatsPeriodEnum periodEnum,
                                                 List<StatsSaleIndicatorsVO> subResultDataList,
                                                 String corpName, Integer corpOrgId) {
        // 数据校验，如果没有子账号数据，则无需汇总
        if (CollectionUtils.isEmpty(subResultDataList)) {
            log.warn("构建总统计数据时子账号数据列表为空，无需汇总 corpOrgId:{}", corpOrgId);
            return;
        }

        // 创建一条用于汇总的记录
        StatsSaleIndicatorsVO totalIndicators = new StatsSaleIndicatorsVO();

        // 设置基础信息
        totalIndicators.setStatsDate(storageDate);
        totalIndicators.setStatsType(periodEnum.getCode());
        totalIndicators.setUserId(corpOrgId);
        totalIndicators.setName(corpName);
        // 总记录的父级ID为设置0
        totalIndicators.setParentId(0);
        totalIndicators.setCreateTime(new Date());
        totalIndicators.setModifyTime(new Date());

        // 初始化各项指标值，避免空指针
        totalIndicators.setClothesExpCount(0);
        totalIndicators.setCustomerConversionCount(0);
        totalIndicators.setCustomerConsumptionPoints(0);
        totalIndicators.setCreateCount(0);
        totalIndicators.setCustomerProtectionMetrics(0);
        totalIndicators.setCustomerActivityRate("0.00");
        totalIndicators.setCustomerRepurchaseRate("0.00");
        totalIndicators.setCustomModelCustomers("0.00");
        totalIndicators.setCustomSceneCustomers("0.00");

        // 汇总所有子账号的数据
        int totalClothesExpCount = 0;
        int totalCustomerConversionCount = 0;
        int totalCustomerConsumptionPoints = 0;
        int totalCreateCount = 0;
        int totalCustomerProtectionMetrics = 0;

        // 使用Stream API汇总数值型指标
        totalClothesExpCount = subResultDataList.stream()
                .filter(data -> data.getClothesExpCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getClothesExpCount)
                .sum();

        totalCustomerConversionCount = subResultDataList.stream()
                .filter(data -> data.getCustomerConversionCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerConversionCount)
                .sum();

        totalCustomerConsumptionPoints = subResultDataList.stream()
                .filter(data -> data.getCustomerConsumptionPoints() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerConsumptionPoints)
                .sum();

        totalCreateCount = subResultDataList.stream()
                .filter(data -> data.getCreateCount() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCreateCount)
                .sum();

        totalCustomerProtectionMetrics = subResultDataList.stream()
                .filter(data -> data.getCustomerProtectionMetrics() != null)
                .mapToInt(StatsSaleIndicatorsVO::getCustomerProtectionMetrics)
                .sum();

        // 设置汇总值
        totalIndicators.setClothesExpCount(totalClothesExpCount);
        totalIndicators.setCustomerConversionCount(totalCustomerConversionCount);
        totalIndicators.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
        totalIndicators.setCreateCount(totalCreateCount);
        totalIndicators.setCustomerProtectionMetrics(totalCustomerProtectionMetrics);

        // 计算加权平均比率指标
        calculateWeightedAverageRatios(subResultDataList, totalIndicators);


        for (StatsSaleIndicatorsVO data : subResultDataList) {
            int totalCustomModelRatioMolecular = 0;   // 定制模特比例分子（使用定制模特的客户数）
            int totalCustomModelRatioDenominator = 0; // 定制模特比例分母（总客户数）

            int totalCustomSceneRatioMolecular = 0;   // 定制场景比例分子（使用定制场景的客户数）
            int totalCustomSceneRatioDenominator = 0; // 定制场景比例分母（总客户数）


            // 定制模特比例
            Integer customModelRatioMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR);
            Integer customModelRatioDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR);
            if (customModelRatioMolecular != null && customModelRatioDenominator != null) {
                totalCustomModelRatioMolecular += customModelRatioMolecular;
                totalCustomModelRatioDenominator += customModelRatioDenominator;
                log.debug("累加模特定制率数据 - 销售:{}, 使用定制模特客户数:{}, 总客户数:{}",
                        data.getName(), customModelRatioMolecular, customModelRatioDenominator);
            }

            // 定制场景比例
            Integer customSceneRatioMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR);
            Integer customSceneRatioDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR);
            if (customSceneRatioMolecular != null && customSceneRatioDenominator != null) {
                totalCustomSceneRatioMolecular += customSceneRatioMolecular;
                totalCustomSceneRatioDenominator += customSceneRatioDenominator;
                log.debug("累加场景定制率数据 - 销售:{}, 使用定制场景客户数:{}, 总客户数:{}",
                        data.getName(), customSceneRatioMolecular, customSceneRatioDenominator);
            }


            // 计算定制模特比例
            calculateRatio(totalCustomModelRatioMolecular, totalCustomModelRatioDenominator,
                    "模特定制率", "使用定制模特客户总数", "客户总数",
                    totalIndicators,
                    totalIndicators::setCustomModelCustomers,
                    CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR);

            // 计算定制场景比例
            calculateRatio(totalCustomSceneRatioMolecular, totalCustomSceneRatioDenominator,
                    "场景定制率", "使用定制场景客户总数", "客户总数",
                    totalIndicators,
                    totalIndicators::setCustomSceneCustomers,
                    CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR);
        }

        log.info("构建总统计数据完成 - 公司名称:{}, 子账号数量:{}, 汇总体验服装数:{}, 汇总客户转换数:{}, 汇总点数消耗:{}",
                corpName, subResultDataList.size(), totalClothesExpCount, totalCustomerConversionCount,
                totalCustomerConsumptionPoints);

        // 将汇总记录添加到子账号数据列表中
        subResultDataList.add(totalIndicators);
    }

    /**
     * 计算加权平均比率指标
     *
     * @param subResultDataList 子账号统计数据列表
     * @param totalIndicators   汇总统计数据
     */
    private void calculateWeightedAverageRatios(List<StatsSaleIndicatorsVO> subResultDataList,
                                                StatsSaleIndicatorsVO totalIndicators) {
        // 累计所有指标的分子和分母
        int totalActivityRateMolecular = 0;   // 活跃率分子（活跃客户数）
        int totalActivityRateDenominator = 0; // 活跃率分母（总客户数）

        int totalRepurchaseRateMolecular = 0;   // 复购率分子（复购客户数）
        int totalRepurchaseRateDenominator = 0; // 复购率分母（有首次大额付款的客户数）

        // 从每个子账号的extInfo中获取分子和分母信息，分别累加
        for (StatsSaleIndicatorsVO data : subResultDataList) {
            if (data.getExtInfo() == null) {
                continue;
            }

            // 客户活跃率
            Integer activityRateMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR);
            Integer activityRateDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);
            if (activityRateMolecular != null && activityRateDenominator != null) {
                totalActivityRateMolecular += activityRateMolecular;
                totalActivityRateDenominator += activityRateDenominator;
                log.debug("累加活跃率数据 - 销售:{}, 活跃客户数:{}, 总客户数:{}",
                        data.getName(), activityRateMolecular, activityRateDenominator);
            }

            // 客户复购率
            Integer repurchaseRateMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR);
            Integer repurchaseRateDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);
            if (repurchaseRateMolecular != null && repurchaseRateDenominator != null) {
                totalRepurchaseRateMolecular += repurchaseRateMolecular;
                totalRepurchaseRateDenominator += repurchaseRateDenominator;
                log.debug("累加复购率数据 - 销售:{}, 复购客户数:{}, 有首付客户数:{}",
                        data.getName(), repurchaseRateMolecular, repurchaseRateDenominator);
            }
        }

        // 计算各项指标的比率并设置到结果对象中

        // 1. 计算客户活跃率
        calculateRatio(totalActivityRateMolecular, totalActivityRateDenominator,
                "客户活跃率", "活跃客户总数", "客户总数",
                totalIndicators,
                totalIndicators::setCustomerActivityRate,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);

        // 2. 计算客户复购率
        calculateRatio(totalRepurchaseRateMolecular, totalRepurchaseRateDenominator,
                "客户复购率", "复购客户总数", "有首付客户总数",
                totalIndicators,
                totalIndicators::setCustomerRepurchaseRate,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);
    }

    /**
     * 计算比率并设置到结果对象中
     *
     * @param molecular       分子值
     * @param denominator     分母值
     * @param indicatorName   指标名称
     * @param molecularDesc   分子描述
     * @param denominatorDesc 分母描述
     * @param target          目标对象
     * @param setter          设置方法
     * @param molecularKey    分子键名
     * @param denominatorKey  分母键名
     */
    private void calculateRatio(int molecular, int denominator,
                                String indicatorName, String molecularDesc, String denominatorDesc,
                                StatsSaleIndicatorsVO target, Consumer<String> setter,
                                String molecularKey, String denominatorKey) {

        // 如果分母大于0，计算比率
        if (denominator > 0) {
            double ratio = (double) molecular / denominator * 100;
            String formattedRatio = String.format("%.2f", ratio);

            // 设置结果
            setter.accept(formattedRatio);

            // 保存分子分母到target的extInfo中
            target.addExtInfo(molecularKey, molecular);
            target.addExtInfo(denominatorKey, denominator);

            log.info("计算总{} - {}:{}, {}:{}, 计算结果:{}%",
                    indicatorName, molecularDesc, molecular, denominatorDesc, denominator, formattedRatio);
        } else {
            // 设置默认值
            setter.accept("0.00");

            // 保存默认分子分母
            target.addExtInfo(molecularKey, 0);
            target.addExtInfo(denominatorKey, 0);

            log.info("计算总{} - 无有效数据，设置为0.00", indicatorName);
        }
    }

    /**
     * 获取客户消费点数
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @param virtualUserIds      虚拟账户ID列表
     * @return 客户消费点数
     */
    private Integer getCustomerConsumptionPoints(String startDate, String endDate, Integer userId,
                                                 List<Integer> distinctCustomerIds, List<Integer> virtualUserIds) {
        try {
            int totalPoints = 0;

            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 查询客户在指定时间范围内的点数消耗总量
            Integer customerPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate,
                    distinctCustomerIds);

            // 将点数消耗转换为正值（假设消耗是负值）
            totalPoints += (customerPoints != null ? Math.abs(customerPoints) : 0);

            // 如果销售自身也有点数消耗，也应该计入
            List<Integer> salesUserIds = new ArrayList<>();
            salesUserIds.add(userId);
            Integer salesOwnPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate, salesUserIds);
            totalPoints += (salesOwnPoints != null ? Math.abs(salesOwnPoints) : 0);

            // 加上虚拟账户的点数消耗
            if (!CollectionUtils.isEmpty(virtualUserIds)) {
                Integer virtualPoints = userPointLogService.getCustomerConsumptionPoints(startDate, endDate,
                        virtualUserIds);
                totalPoints += (virtualPoints != null ? Math.abs(virtualPoints) : 0);
            }

            log.info("客户消费点数统计 - 销售ID:{}, 客户数:{}, 虚拟账户数:{}, 总消耗点数:{}",
                    userId, distinctCustomerIds.size(),
                    virtualUserIds != null ? virtualUserIds.size() : 0,
                    totalPoints);

            return totalPoints;

        } catch (Exception e) {
            log.error("计算客户消费点数异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取客户转换量
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @return 客户转换量
     */
    private Integer getCustomerConversionCount(String startDate, String endDate, Integer userId,
                                               List<Integer> distinctCustomerIds) {
        try {
            int conversionCount = 0;

            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 在指定日期范围内查询首次大额充值（≥3999）的客户
            List<Integer> newConvertedCustomers = new ArrayList<>();
            for (Integer customerId : distinctCustomerIds) {
                // 查询该客户是否在统计期前已有大额充值
                OrderInfoQuery beforeQuery = new OrderInfoQuery();
                beforeQuery.setMasterUserId(customerId);
                beforeQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> beforeOrders = orderInfoService.queryOrderInfoListWithSalesInfo(beforeQuery);

                boolean hadBigPaymentBefore = false;
                if (!CollectionUtils.isEmpty(beforeOrders)) {
                    BigDecimal minAmount = new BigDecimal("3999");
                    // 筛选充值时间在开始日期之前的大额订单
                    for (OrderInfoVO order : beforeOrders) {
                        if (order.getPayAmount() != null &&
                                order.getPayAmount().compareTo(minAmount) >= 0 &&
                                order.getCreateTime() != null &&
                                order.getCreateTime().before(DateUtils.parseSimpleDate(startDate))) {
                            hadBigPaymentBefore = true;
                            break;
                        }
                    }
                }

                // 如果之前没有大额充值，检查统计期内是否有大额充值
                if (!hadBigPaymentBefore) {
                    OrderInfoQuery currentQuery = new OrderInfoQuery();
                    currentQuery.setMasterUserId(customerId);
                    currentQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                    // 仅查询统计期内的订单
                    currentQuery.setStartDate(startDate);
                    currentQuery.setEndDate(endDate);
                    List<OrderInfoVO> currentOrders = orderInfoService.queryOrderInfoListWithSalesInfo(currentQuery);

                    if (!CollectionUtils.isEmpty(currentOrders)) {
                        BigDecimal minAmount = new BigDecimal("3999");
                        // 检查是否有大额订单
                        boolean hasBigPayment = currentOrders.stream()
                                .anyMatch(order -> order.getPayAmount() != null &&
                                        order.getPayAmount().compareTo(minAmount) >= 0);

                        if (hasBigPayment) {
                            newConvertedCustomers.add(customerId);
                        }
                    }
                }
            }

            // 返回新签大额客户数量
            conversionCount = newConvertedCustomers.size();

            log.info("客户转换量统计 - 销售ID:{}, 总客户数:{}, 新签大额客户数:{}",
                    userId, distinctCustomerIds.size(), conversionCount);

            return conversionCount;

        } catch (Exception e) {
            log.error("计算客户转换量异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取服装体验量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userId    用户ID
     * @return 服装体验数量
     */
    private Integer getClothesExpCount(String startDate, String endDate, Integer userId) {
        try {
            // 销售自己账号上传的服装数量
            int salesOwnClothesCount = 0;
            // 虚拟账户上传的服装数量
            int virtualAccountClothesCount = 0;
            // 客户充值前上传的服装数量
            int customerBetaClothesCount = 0;

            // 1. 统计销售自己账号上传的服装
            salesOwnClothesCount = countSalesOwnClothes(startDate, endDate, userId);

            // 2. 统计销售关联的虚拟账户上传的服装
            List<Integer> virtualUserIds = fetchVirtualUserIds(userId);
            if (!CollectionUtils.isEmpty(virtualUserIds)) {
                virtualAccountClothesCount = countVirtualAccountClothes(startDate, endDate, virtualUserIds);
            }

            // 3. 统计销售客户充值前(< 3999元)上传的服装（体验服装）
            customerBetaClothesCount = countCustomerBetaClothes(startDate, endDate, userId);

            // 4. 计算总数并返回
            int totalClothesCount = salesOwnClothesCount + virtualAccountClothesCount + customerBetaClothesCount;

            log.info("服装体验量统计 - 用户ID:{}, 自己账号:{}, 虚拟户:{}, 客户充值前:{}, 总计:{}",
                    userId, salesOwnClothesCount, virtualAccountClothesCount, customerBetaClothesCount,
                    totalClothesCount);

            return totalClothesCount;

        } catch (Exception e) {
            log.error("计算服装体验量异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 统计销售自己账号上传的服装数量
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param userId    用户ID
     * @return 服装数量
     */
    private int countSalesOwnClothes(String startDate, String endDate, Integer userId) {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserId(userId);
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
        query.setMainTypes(Arrays.asList(MainTypeEnum.MAIN.getCode(), MainTypeEnum.NORMAL.getCode()));

        List<MaterialModelVO> clothesList = materialModelService.queryMaterialModelList(query);
        return CollectionUtils.isEmpty(clothesList) ? 0 : clothesList.size();
    }

    /**
     * 统计虚拟账户上传的服装数量
     *
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @param virtualUserIds 虚拟账户ID列表
     * @return 服装数量
     */
    private int countVirtualAccountClothes(String startDate, String endDate, List<Integer> virtualUserIds) {
        MaterialModelQuery query = new MaterialModelQuery();
        query.setUserIds(virtualUserIds);
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

        List<MaterialModelVO> clothesList = materialModelService.queryMaterialModelList(query);
        return CollectionUtils.isEmpty(clothesList) ? 0 : clothesList.size();
    }

    /**
     * 统计客户在充值3999元前上传的服装数量(体验服装)
     *
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @param salesUserId 销售用户ID
     * @return 体验服装数量
     */
    private int countCustomerBetaClothes(String startDate, String endDate, Integer salesUserId) {
        int totalBetaClothesCount = 0;

        // 1. 获取该销售关联的所有客户
        DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
        customerQuery.setDistributorSalesUserId(salesUserId);

        List<DistributorCustomerVO> customerList = distributorCustomerService
                .queryDistributorCustomerList(customerQuery);

        if (CollectionUtils.isEmpty(customerList)) {
            return 0;
        }

        // 2. 获取客户ID列表
        List<Integer> customerIds = customerList.stream()
                .map(DistributorCustomerVO::getCustomerMasterUserId)
                .collect(Collectors.toList());

        // 3.二次过滤客户信息
        UserQuery userQuery = new UserQuery();
        userQuery.setIds(customerIds);
        userQuery.setStatus(UserStatusEnum.ENABLED.getCode());
        List<UserVO> userVOS = userService.queryUsers(userQuery);

        // 获取客户ID列表
        customerIds = userVOS.stream()
                .map(UserVO::getId)
                .collect(Collectors.toList());

        // 3. 处理每个客户的体验服装
        for (Integer customerId : customerIds) {
            totalBetaClothesCount += countSingleCustomerBetaClothes(customerId, startDate, endDate);
        }

        return totalBetaClothesCount;
    }

    /**
     * 统计单个客户的体验服装数量
     *
     * @param customerId 客户ID
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @return 体验服装数量
     */
    private int countSingleCustomerBetaClothes(Integer customerId, String startDate, String endDate) {
        try {
            // 1. 查询客户的首次充值3999+的订单
            OrderInfoQuery orderQuery = new OrderInfoQuery();
            orderQuery.setMasterUserId(customerId);
            orderQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
            List<OrderInfoVO> orders = orderInfoService.queryOrderInfoListWithSalesInfo(orderQuery);

            // 2. 过滤出大额订单(>=3999元)并找出最早的一笔
            BigDecimal thresholdAmount = new BigDecimal("3999");
            Optional<OrderInfoVO> earliestBigOrder = orders.stream()
                    .filter(order -> order.getPayAmount() != null
                            && order.getPayAmount().compareTo(thresholdAmount) >= 0)
                    .min(Comparator.comparing(OrderInfoVO::getCreateTime));

            // 3. 根据是否有大额订单和订单时间，查询体验服装
            if (earliestBigOrder.isPresent()) {
                // 客户有大额充值，查询充值前的服装
                Date earliestOrderTime = earliestBigOrder.get().getCreateTime();

                // 查询在首次大额充值前上传的服装
                MaterialModelQuery betaQuery = new MaterialModelQuery();
                betaQuery.setUserId(customerId);
                betaQuery.setStartDate(startDate);
                betaQuery.setEndDate(DateUtils.formatSimpleDate(earliestOrderTime));
                betaQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
                betaQuery.setMainTypes(Arrays.asList(MainTypeEnum.MAIN.getCode(), MainTypeEnum.NORMAL.getCode()));

                List<MaterialModelVO> betaClothes = materialModelService.queryMaterialModelList(betaQuery);
                return CollectionUtils.isEmpty(betaClothes) ? 0 : betaClothes.size();
            } else {
                // 客户没有大额充值，所有服装都算体验服装
                MaterialModelQuery betaQuery = new MaterialModelQuery();
                betaQuery.setUserId(customerId);
                betaQuery.setStartDate(startDate);
                betaQuery.setEndDate(endDate);
                betaQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
                betaQuery.setMainTypes(Arrays.asList(MainTypeEnum.MAIN.getCode(), MainTypeEnum.NORMAL.getCode()));

                List<MaterialModelVO> betaClothes = materialModelService.queryMaterialModelList(betaQuery);
                return CollectionUtils.isEmpty(betaClothes) ? 0 : betaClothes.size();
            }
        } catch (Exception e) {
            log.error("统计客户体验服装异常，客户ID:{}, 异常:{}", customerId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取销售出图数量
     *
     * @param startDate      开始时间
     * @param endDate        结束时间
     * @param userId         用户ID
     * @param virtualUserIds 虚拟账户ID列表，可为null
     * @return 销售出图数量
     */
    private Integer getSalesDrawCount(String startDate, String endDate, Integer userId, List<Integer> virtualUserIds) {
        // 1. 获取销售自己账号的出图量
        CreativeBatchQuery creativeBatchQuery = new CreativeBatchQuery();
        creativeBatchQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());
        creativeBatchQuery.setOperatorId(userId);
        creativeBatchQuery.setStartTime(startDate);
        creativeBatchQuery.setEndTime(endDate);
        List<CreativeBatchVO> creativeBatchVOS = creativeBatchService.queryCreativeBatchList(creativeBatchQuery);

        int salesDrawCount = 0;
        if (!CollectionUtils.isEmpty(creativeBatchVOS)) {
            salesDrawCount = creativeBatchVOS.stream().map(CreativeBatchVO::getBatchCnt).reduce(0, Integer::sum);
        }

        // 2. 获取销售关联的虚拟户的出图量
        if (!CollectionUtils.isEmpty(virtualUserIds)) {
            for (Integer virtualUserId : virtualUserIds) {
                CreativeBatchQuery virtualUserQuery = new CreativeBatchQuery();
                virtualUserQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());
                virtualUserQuery.setUserId(virtualUserId);
                virtualUserQuery.setStartTime(startDate);
                virtualUserQuery.setEndTime(endDate);
                List<CreativeBatchVO> virtualUserBatchVOS = creativeBatchService
                        .queryCreativeBatchList(virtualUserQuery);

                if (!CollectionUtils.isEmpty(virtualUserBatchVOS)) {
                    salesDrawCount += virtualUserBatchVOS.stream().map(CreativeBatchVO::getBatchCnt).reduce(0,
                            Integer::sum);
                }
            }
        }

        return salesDrawCount;
    }

    /**
     * 获取销售关联的虚拟户用户ID列表
     *
     * @param salesUserId 销售用户ID
     * @return 虚拟户用户ID列表
     */
    private List<Integer> fetchVirtualUserIds(Integer salesUserId) {
        // 查询销售对应的虚拟商家
        UserProfileQuery userProfileQuery = new UserProfileQuery();
        userProfileQuery.setUid(salesUserId);
        userProfileQuery.setProfileKey(PROFILE_RELATED_MERCHANT_ACCOUNT);
        List<UserProfileVO> userProfileVOS = userProfileService.queryUserProfileList(userProfileQuery);
        if (CollectionUtils.isEmpty(userProfileVOS)) {
            return  new ArrayList<>();
        }

        // 提取id信息
        List<String> strIds = userProfileVOS.stream().map(UserProfileVO::getProfileVal).collect(Collectors.toList());
        // 转换为Integer类型的集合
        List<Integer> ids = strIds.stream().map(Integer::parseInt).collect(Collectors.toList());


        // 通过memo字段包含"虚拟商家"来查找虚拟户
        UserQuery query = new UserQuery();
        query.setIds(ids);

        List<UserVO> allVirtualUsers = userService.queryUsers(query);
        if (CollectionUtils.isEmpty(allVirtualUsers)) {
            return new ArrayList<>();
        }

        // 返回虚拟户ID列表
        return allVirtualUsers.stream().map(UserVO::getId).collect(Collectors.toList());
    }

    /**
     * 查询所有分销商角色的主账号及其子账号
     *
     * @return 主账号及其对应子账号的用户列表(子账号已经设置到对应主账号的children字段中)
     */
    protected List<UserVO> queryUserWithChildren() {
        // 使用渠道商角色类型
        String roleType = RoleTypeEnum.DISTRIBUTOR.getCode();

        // 1. 查询所有符合条件的主账号
        UserQuery masterQuery = new UserQuery();
        masterQuery.setRoleType(roleType);
        masterQuery.setUserType(UserTypeEnum.MASTER.getCode());
        masterQuery.setStatus(UserStatusEnum.ENABLED.getCode());
        List<UserVO> masterUsers = userService.queryUsers(masterQuery);
        if (CollectionUtils.isEmpty(masterUsers)) {
            return new ArrayList<>();
        }

        // 2. 获取主账号ID列表并查询所有相关子账号
        List<Integer> masterIds = masterUsers.stream()
                .map(UserVO::getId)
                .collect(Collectors.toList());

        // 3. 查询子账号并按masterId分组
        UserQuery subQuery = new UserQuery();
        subQuery.setRoleType(roleType);
        subQuery.setUserType(UserTypeEnum.SUB.getCode());
        subQuery.setStatus(UserStatusEnum.ENABLED.getCode());
        subQuery.setMasterIdList(masterIds);

        // 查询子账号并按masterId分组
        Map<Integer, List<UserVO>> subUsersByMasterId = userService.queryUsers(subQuery).stream()
                .collect(Collectors.groupingBy(UserVO::getMasterId));

        // 4. 将子账号设置到对应的主账号上并返回
        return masterUsers.stream()
                .peek(master -> master.setChildren(
                        subUsersByMasterId.getOrDefault(master.getId(), new ArrayList<>())))
                .collect(Collectors.toList());
    }

    /**
     * 获取销售关联的准确客户总数
     *
     * @param userId              销售用户ID
     * @param distinctCustomerIds 去重后的客户ID列表（作为备选数据源）
     * @return 客户总数
     */
    private long getAccurateCustomerCount(Integer userId, List<Integer> distinctCustomerIds) {
        // 获取销售的准确客户总数
        DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
        customerQuery.setDistributorSalesUserId(userId);
        Long totalCustomerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

        // 如果查询结果为空或为0，则使用传入的客户列表大小作为备选
        if (totalCustomerCount == null || totalCustomerCount == 0) {
            totalCustomerCount = (long) distinctCustomerIds.size();
        }

        return totalCustomerCount;
    }

    /**
     * 获取客户活跃率
     *
     * @param startDate             开始时间
     * @param endDate               结束时间
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomerActivityRate(String startDate, String endDate, Integer userId,
                                         List<Integer> distinctCustomerIds, long totalCustomerCount,
                                         StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
                return;
            }

            // 统计时间范围内有创作行为的客户数量
            int activeCustomerCount = 0;
            for (Integer customerId : distinctCustomerIds) {
                // 查询客户在时间范围内是否有创作行为（出图、出视频等）
                CreativeBatchQuery query = new CreativeBatchQuery();
                query.setUserId(customerId);
                query.setStartTime(startDate);
                query.setEndTime(endDate);
                query.setStatus(CreativeStatusEnum.FINISHED.getCode());
                // 只需要判断是否存在记录，设置pageSize为1即可
                query.setPageSize(1);
                query.setPageNum(1);

                List<CreativeBatchVO> batches = creativeBatchService.queryCreativeBatchList(query);
                if (!CollectionUtils.isEmpty(batches)) {
                    activeCustomerCount++;
                }
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
                return;
            }

            // 计算活跃率并格式化为两位小数的字符串
            double activityRate = (double) activeCustomerCount / totalCustomerCount * 100;
            String formattedRate = String.format("%.2f", activityRate);

            log.info("客户活跃率统计 - 销售ID:{}, 实际客户总数:{}, 活跃客户数:{}, 活跃率:{}%",
                    userId, totalCustomerCount, activeCustomerCount, formattedRate);

            // 设置活跃率
            statsSaleIndicatorsVO.setCustomerActivityRate(formattedRate);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, activeCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算客户活跃率异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomerActivityRate("0.00");
        }
    }

    /**
     * 获取客户复购率（季度/半年）
     *
     * @param startDate             开始时间
     * @param endDate               结束时间
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomerRepurchaseRate(String startDate, String endDate, Integer userId,
                                           List<Integer> distinctCustomerIds, long totalCustomerCount,
                                           StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            // 统计复购客户数
            int repurchaseCount = 0; // 复购客户数
            int customerCount = 0; // 客户总数（作为分母）

            // SQL中的计算方式：在指定时间范围内有复购行为（任何金额）的客户数量/付费客户数量
            for (Integer customerId : distinctCustomerIds) {
                // 先确认这个客户是否付费（VIP）用户
                boolean isVipUser = false;

                // 查找客户首次大额充值（>=3999元）的时间
                OrderInfoQuery allOrdersQuery = new OrderInfoQuery();
                allOrdersQuery.setMasterUserId(customerId);
                allOrdersQuery.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
                List<OrderInfoVO> allOrders = orderInfoService.queryOrderInfoListWithSalesInfo(allOrdersQuery);

                BigDecimal minAmount = new BigDecimal("3999");
                if (!CollectionUtils.isEmpty(allOrders)) {
                    // 检查是否有大额充值订单
                    isVipUser = allOrders.stream()
                            .anyMatch(order -> order.getPayAmount() != null &&
                                    order.getPayAmount().compareTo(minAmount) >= 0);
                }

                // 如果不是VIP用户，跳过
                if (!isVipUser) {
                    continue;
                }

                // 这是一个VIP客户，客户总数加1
                customerCount++;

                // 检查这个客户在统计期内是否有多次购买记录
                // 先计算该用户所有订单数量
                long totalOrderCount = allOrders.stream()
                        .filter(order -> order.getCreateTime() != null)
                        .count();

                // 计算统计期内的订单数量
                long orderCountInPeriod = allOrders.stream()
                        .filter(order -> order.getCreateTime() != null &&
                                isWithinPeriod(order.getCreateTime(), startDate, endDate))
                        .count();

                // 如果统计期内有订单且总订单数>1，认为有复购行为
                if (orderCountInPeriod > 0 && totalOrderCount > 1) {
                    repurchaseCount++;
                }
            }

            // 计算复购率 = 复购客户数 / 客户总数
            if (customerCount == 0) {
                statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
                return;
            }

            double repurchaseRate = (double) repurchaseCount / customerCount * 100;
            String formattedRate = String.format("%.2f", repurchaseRate);

            log.info("客户复购率统计 - 销售ID:{}, 实际客户总数:{}, VIP客户数:{}, 复购客户数:{}, 复购率:{}%",
                    userId, totalCustomerCount, customerCount, repurchaseCount, formattedRate);

            // 设置复购率
            statsSaleIndicatorsVO.setCustomerRepurchaseRate(formattedRate);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, repurchaseCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR, customerCount);

        } catch (Exception e) {
            log.error("计算客户复购率异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomerRepurchaseRate("0.00");
        }
    }

    /**
     * 判断日期是否在指定时间范围内
     *
     * @param date      要检查的日期
     * @param startDate 开始日期字符串
     * @param endDate   结束日期字符串
     * @return 是否在范围内
     */
    private boolean isWithinPeriod(Date date, String startDate, String endDate) {
        try {
            Date start = null;
            Date end = null;

            // 处理开始日期
            if (startDate.length() > 10 && startDate.contains(":")) {
                // 包含时间部分，使用完整格式解析
                start = DateUtils.parseSimple(startDate);
            } else {
                // 仅包含日期部分，解析为当天开始时间
                start = DateUtils.parseSimpleDate(startDate);
            }

            // 处理结束日期
            if (endDate.length() > 10 && endDate.contains(":")) {
                // 包含时间部分，使用完整格式解析
                end = DateUtils.parseSimple(endDate);
            } else {
                // 仅包含日期部分，解析为当天结束时间 (23:59:59)
                end = DateUtils.parseSimpleLastTime(endDate);
            }

            if (start == null || end == null) {
                log.error("日期解析失败，startDate: {}, endDate: {}", startDate, endDate);
                return false;
            }

            return date.compareTo(start) >= 0 && date.compareTo(end) <= 0;
        } catch (Exception e) {
            log.error("日期解析出错: {}, startDate={}, endDate={}", e.getMessage(), startDate, endDate, e);
            return false;
        }
    }

    /**
     * 获取定制模特比例
     *
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param userFaceSceneList     所有用户的定制元素信息
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomModelRatio(Integer userId,
                                     List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                     long totalCustomerCount, StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            // 统计使用定制模特的客户数
            int customModelCustomerCount = 0; // 使用了定制模特的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制模特
                boolean hasCustomModel = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getFaceCnt() != null && element.getFaceCnt() > 0);

                if (hasCustomModel) {
                    customModelCustomerCount++;
                }
            }

            // 计算定制模特比例 = 使用定制模特的客户数 / 总客户数
            double customModelRatio = (double) customModelCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customModelRatio);

            log.info("定制模特比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制模特客户数:{}, 定制模特比例:{}%",
                    userId, totalCustomerCount, customModelCustomerCount, formattedRatio);

            // 设置定制模特比例
            statsSaleIndicatorsVO.setCustomModelCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    customModelCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制模特比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
        }
    }

    /**
     * 获取定制场景比例
     *
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param userFaceSceneList     所有用户的定制元素信息
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomSceneRatio(Integer userId,
                                     List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                     long totalCustomerCount, StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds) || CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            // 统计使用定制场景的客户数
            int customSceneCustomerCount = 0; // 使用了定制场景的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制场景
                boolean hasCustomScene = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getSceneCnt() != null && element.getSceneCnt() > 0);

                if (hasCustomScene) {
                    customSceneCustomerCount++;
                }
            }

            // 计算定制场景比例 = 使用定制场景的客户数 / 总客户数
            double customSceneRatio = (double) customSceneCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customSceneRatio);

            log.info("定制场景比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制场景客户数:{}, 定制场景比例:{}%",
                    userId, totalCustomerCount, customSceneCustomerCount, formattedRatio);

            // 设置定制场景比例
            statsSaleIndicatorsVO.setCustomSceneCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    customSceneCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制场景比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
        }
    }

    /**
     * 获取客户保护指标（大于60天未充值的客户数量）
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @return 大于60天未充值的客户数量
     */
    private Integer getCustomerProtectionMetrics(String startDate, String endDate, Integer userId,
                                                 List<Integer> distinctCustomerIds) {
        try {
            if (CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 获取准确的客户总数
            long totalCustomerCount = getAccurateCustomerCount(userId, distinctCustomerIds);

            // 使用UserService中的queryBefore60Days方法获取所有超过60天创建且非VIP的用户
            List<SalesInfoVO> salesInfoList = userService.queryBefore60Days(endDate);
            if (CollectionUtils.isEmpty(salesInfoList)) {
                return 0;
            }

            // 筛选当前销售相关的记录并累加sleepCnt
            int sleepCustomerCount = salesInfoList.stream()
                    .filter(info -> userId.equals(info.getSalesId()))
                    .mapToInt(SalesInfoVO::getSleepCnt)
                    .sum();

            log.info("客户保护指标统计(超60天非VIP客户) - 销售ID:{}, 实际客户总数:{}, 超60天非VIP客户数:{}",
                    userId, totalCustomerCount, sleepCustomerCount);

            return sleepCustomerCount;
        } catch (Exception e) {
            log.error("计算客户保护指标异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    protected int saveStatsData(List<StatsSaleIndicatorsVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            try {
                return statsSaleIndicatorsService.batchInsertOrUpdate(statsList);
            } catch (Exception e) {
                log.error("保存统计数据异常: {}", e.getMessage(), e);
                return 0;
            }
        }
        return 0;
    }

    /**
     * 销售客户数据类，用于存储销售关联的客户信息
     */
    @Data
    private static class SalesCustomerData {
        // 直接关联的客户ID列表
        private List<Integer> directCustomerIds = new ArrayList<>();
        // 虚拟账户ID列表
        private List<Integer> virtualUserIds = new ArrayList<>();
        // 虚拟账户关联的客户ID列表
        private List<Integer> virtualCustomerIds = new ArrayList<>();
        // 合并去重后的所有客户ID列表
        private List<Integer> distinctCustomerIds = new ArrayList<>();
    }

    /**
     * 获取销售关联的所有客户数据
     *
     * @param userId 销售用户ID
     * @return 销售客户数据
     */
    private SalesCustomerData getSalesCustomerData(Integer userId) {
        SalesCustomerData result = new SalesCustomerData();

        try {
            // 获取销售关联的所有客户（直接客户）
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            List<DistributorCustomerVO> customerList = distributorCustomerService
                    .queryDistributorCustomerList(customerQuery);

            List<Integer> directCustomerIds = CollectionUtils.isEmpty(customerList) ? new ArrayList<>()
                    : customerList.stream()
                    .map(DistributorCustomerVO::getCustomerMasterUserId)
                    .distinct()
                    .collect(Collectors.toList());

            // 2.过滤出 3999 以上的用户
            List<Integer> more3999userIdList = userService.fetch3999UserIdList(userId);

            // 取directCustomerIds 和 more3999userIdList 两个集合的交集
            List<Integer> intersection = directCustomerIds.stream()
                    .filter(more3999userIdList::contains)
                    .distinct()
                    .collect(Collectors.toList());

            result.setDistinctCustomerIds(intersection);

        } catch (Exception e) {
            log.error("获取销售客户数据异常，销售ID:{}, 异常:{}", userId, e.getMessage(), e);
        }

        return result;
    }
}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.TrainingTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingTaskVO;
import ai.conrain.aigc.platform.service.model.vo.TaskProgressVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 训练任务主 Service定义
 *
 * <AUTHOR>
 * @version TrainingTaskService.java
 */
public interface TrainingTaskService {
	
	/**
	 * 查询训练任务主对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TrainingTaskVO selectById(Integer id);

	/**
	 * 删除训练任务主对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加训练任务主对象
	 * @param trainingTask 对象参数
	 * @return 返回结果
	 */
	TrainingTaskVO insert(TrainingTaskVO trainingTask);

	/**
	 * 修改训练任务主对象
	 * @param trainingTask 对象参数
	 */
	void updateByIdSelective(TrainingTaskVO trainingTask);

	/**
	 * 带条件批量查询训练任务主列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<TrainingTaskVO> queryTrainingTaskList(TrainingTaskQuery query);

	/**
	 * 带条件查询训练任务主数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryTrainingTaskCount(TrainingTaskQuery query);

	/**
	 * 带条件分页查询训练任务主
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<TrainingTaskVO> queryTrainingTaskByPage(TrainingTaskQuery query);
	/**
	 * 启动训练任务
	 * @param taskId 任务ID
	 */
	void startTask(Integer taskId);

	/**
	 * 停止训练任务
	 * @param taskId 任务ID
	 */
	void stopTask(Integer taskId);

	/**
	 * 获取任务进度
	 * @param taskId 任务ID
	 * @return 任务进度信息
	 */
	TaskProgressVO getTaskProgress(Integer taskId);
}
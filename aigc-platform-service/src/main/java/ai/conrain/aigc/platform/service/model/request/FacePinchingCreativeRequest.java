/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 模特捏脸创作请求
 *
 * <AUTHOR>
 * @version : FacePinchingCreativeRequest.java, v 0.1 2025/4/8 12:11 renxiao.wu Exp $
 */
@Data
public class FacePinchingCreativeRequest implements CreativeRequest {
    private static final long serialVersionUID = -4155812814876091520L;
    /** 原始图片列表 */
    @NotNull
    @Size(min = 1, max = 100)
    private List<String> originImages;
    /** 目标捏脸图片 */
    @NotNull
    @Size(min = 1, max = 100)
    private List<String> targetImages;

    /** 肤质类型，目前只有realistic/smooth */
    private String skinType;

    /** 性别类型 */
    @NotBlank
    private String genderType;

    /** 年龄范围 */
    private String ageRange;
}

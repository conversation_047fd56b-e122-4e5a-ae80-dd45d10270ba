package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.StatsMaterialOwnerDO;
import ai.conrain.aigc.platform.service.model.query.StatsMaterialOwnerQuery;
import ai.conrain.aigc.platform.dal.example.StatsMaterialOwnerExample;
import ai.conrain.aigc.platform.service.model.vo.StatsMaterialOwnerVO;

import org.springframework.util.ObjectUtils;

import com.alibaba.fastjson.JSON;

import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * StatsMaterialOwnerConverter
 *
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
public class StatsMaterialOwnerConverter {

    /**
     * DO -> VO
     */
    public static StatsMaterialOwnerVO do2VO(StatsMaterialOwnerDO from) {
        StatsMaterialOwnerVO to = new StatsMaterialOwnerVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setNickname(from.getNickname());
        to.setDeliveryCount(from.getDeliveryCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsMaterialOwnerDO vo2DO(StatsMaterialOwnerVO from) {
        StatsMaterialOwnerDO to = new StatsMaterialOwnerDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setNickname(from.getNickname());
        to.setDeliveryCount(from.getDeliveryCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSON.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsMaterialOwnerQuery do2Query(StatsMaterialOwnerDO from) {
        StatsMaterialOwnerQuery to = new StatsMaterialOwnerQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setNickname(from.getNickname());
        to.setDeliveryCount(from.getDeliveryCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsMaterialOwnerDO query2DO(StatsMaterialOwnerQuery from) {
        StatsMaterialOwnerDO to = new StatsMaterialOwnerDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setNickname(from.getNickname());
        to.setDeliveryCount(from.getDeliveryCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsMaterialOwnerExample query2Example(StatsMaterialOwnerQuery from) {
        StatsMaterialOwnerExample to = new StatsMaterialOwnerExample();
        StatsMaterialOwnerExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdLike("%" +from.getUserId() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getNickname())) {
            c.andNicknameLike("%" + from.getNickname() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getDeliveryCount())) {
            c.andDeliveryCountEqualTo(from.getDeliveryCount());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<StatsMaterialOwnerVO> doList2VOList(List<StatsMaterialOwnerDO> list) {
        return CommonUtil.listConverter(list, StatsMaterialOwnerConverter::do2VO);
    }
}
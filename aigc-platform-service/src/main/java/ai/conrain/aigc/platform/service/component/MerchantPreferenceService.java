package ai.conrain.aigc.platform.service.component;

import java.util.List;

import ai.conrain.aigc.platform.service.enums.PreferenceTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.MerchantPreferenceQuery;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;

/**
 * 商家偏好配置 Service定义
 *
 * <AUTHOR>
 * @version MerchantPreferenceService.java v 0.1 2024-11-12 08:18:52
 */
public interface MerchantPreferenceService {

    /**
     * 查询商家偏好配置对象
     *
     * @param id 主键
     * @return 返回结果
     */
    MerchantPreferenceVO selectById(Integer id);

    /**
     * 删除商家偏好配置对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加商家偏好配置对象
     *
     * @param merchantPreference 对象参数
     * @return 返回结果
     */
    MerchantPreferenceVO insert(MerchantPreferenceVO merchantPreference);

    /**
     * 修改商家偏好配置对象
     *
     * @param merchantPreference 对象参数
     */
    void updateByIdSelective(MerchantPreferenceVO merchantPreference);

    /**
     * 带条件批量查询商家偏好配置列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<MerchantPreferenceVO> queryMerchantPreferenceList(MerchantPreferenceQuery query);

    /**
     * 带条件批量查询商家偏好配置列表带扩展字段
     * @param query 查询条件
     * @return 结果
     */
    List<MerchantPreferenceVO> queryMerchantPreferenceListWithBlob(MerchantPreferenceQuery query);

    /**
     * 带条件查询商家偏好配置数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryMerchantPreferenceCount(MerchantPreferenceQuery query);

    /**
     * 带条件分页查询商家偏好配置
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<MerchantPreferenceVO> queryMerchantPreferenceByPage(MerchantPreferenceQuery query);

    /**
     * 基于用户id查询明细
     *
     * @param userId 用户id
     * @return 明细
     */
    MerchantPreferenceDetail queryDetailByUserId(Integer userId);

    /**
     * 查询已开启的创作偏好
     *
     * @param userId    用户id
     * @param clothType 衣服类型
     * @return 创作偏好
     */
    MerchantPreferenceVO queryEnableCreativePreference(Integer userId, String clothType);

    /**
     * 查询所有商家拍拿货
     *
     * @return 结果
     */
    List<MerchantPreferenceDetail> queryAllMerchantPreference();

    /**
     * 更新商家偏好信息
     *
     * @param preference 商家偏好
     */
    void updateMerchantPreference(MerchantPreferenceDetail preference);

    /**
     * 查询用户的创作偏好
     *
     * @param masterUserId 主用户id
     * @return 创作偏好列表
     */
    List<MerchantPreferenceVO> queryCreativePreference(Integer masterUserId);

    /**
     * 查询用户的交付偏好
     *
     * @param masterUserId 主用户id
     * @return 交付偏好列表
     */
    List<MerchantPreferenceVO> queryDeliveryPreference(Integer masterUserId);

    /**
     * 查询用户的服装搭配
     *
     * @param userId 主用户id
     * @return 服装搭配用户偏好
     */
    List<MerchantPreferenceVO> queryClothCollocation(Integer userId);

    /**
     * 检查数量限制
     *
     * @param typeEnum 类型
     * @param size     数量
     * @return 结果
     */
    Boolean checkQtyLimit(PreferenceTypeEnum typeEnum, int size);

    /**
     * 校验参数
     * @param query 查询条件
     */
    void validateParam(MerchantPreferenceQuery query);
}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.CodeDAO;
import ai.conrain.aigc.platform.dal.entity.CodeDO;
import ai.conrain.aigc.platform.dal.example.CodeExample;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.CodeService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.CodeStatus;
import ai.conrain.aigc.platform.service.enums.CodeType;
import ai.conrain.aigc.platform.service.model.biz.RegisterCodeInfo;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CodeConverter;
import ai.conrain.aigc.platform.service.model.query.CodeQuery;
import ai.conrain.aigc.platform.service.model.vo.CodeVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**   
 * CodeService实现
 *
 * <AUTHOR>
 * @version CodeService.java v 0.1 2025-05-19 02:24:41
 */
@Slf4j
@Service
public class CodeServiceImpl implements CodeService {

	/** DAO */
	@Autowired
	private CodeDAO codeDAO;

	@Autowired
	private UserService userService;

	@Override
	public CodeVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		CodeDO data = codeDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return CodeConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = codeDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Code失败");
	}

	@Override
	public CodeVO insert(CodeVO code) {
		AssertUtil.assertNotNull(code, ResultCode.PARAM_INVALID, "code is null");
		AssertUtil.assertTrue(code.getId() == null, ResultCode.PARAM_INVALID, "code.id is present");

		//创建时间、修改时间兜底
		if (code.getCreateTime() == null) {
			code.setCreateTime(new Date());
		}

		if (code.getModifyTime() == null) {
			code.setModifyTime(new Date());
		}

		CodeDO data = CodeConverter.vo2DO(code);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = codeDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Code失败");
		AssertUtil.assertNotNull(data.getId(), "新建Code返回id为空");
		code.setId(data.getId());
		return code;
	}

	@Override
	public CodeVO createRegisterPromotionCode(Integer relatedUserId, String externalNick) {

		CodeVO codeVO = new CodeVO();
		codeVO.setCode(CommonUtil.uuid().substring(0, 6));
		codeVO.setCodeType(CodeType.registerPromotion.name());
		codeVO.setCodeStatus(CodeStatus.valid.name());
		codeVO.setCreatorMasterId(OperationContextHolder.getMasterUserId());
		codeVO.setCreatorId(OperationContextHolder.getOperatorUserId());
		codeVO.setModifierId(OperationContextHolder.getOperatorUserId());

		//关联到用户id
		String nick = null;
		if (relatedUserId != null) {
			codeVO.setRelatedUserId(relatedUserId);
			UserVO userVO = userService.selectById(relatedUserId);
			AssertUtil.assertNotNull(userVO, "关联的用户不存在");
			nick = userVO.getNickName();
		} else if (StringUtils.isNotBlank(externalNick)) {
			nick = externalNick;
		}
		AssertUtil.assertNotBlank(nick, "关联的用户不存在");

		RegisterCodeInfo codeInfo = new RegisterCodeInfo();
		codeInfo.setNick(nick);
		codeInfo.setUrl(String.format("%s?/#/register?ic=%s", EnvUtil.getAppDomain(), codeVO.getCode()));
		if (OperationContextHolder.isDistributorRole()) {
			codeInfo.setDistributorCorpId(OperationContextHolder.getCorpOrgId());
		}
		codeInfo.setCode(codeVO.getCode());
		codeVO.setCodeInfo(CommonUtil.java2JSONObject(codeInfo));

		return this.insert(codeVO);
	}

	@Override
	public void updateByIdSelective(CodeVO code) {
		AssertUtil.assertNotNull(code, ResultCode.PARAM_INVALID, "code is null");
    	AssertUtil.assertTrue(code.getId() != null, ResultCode.PARAM_INVALID, "code.id is null");

		//修改时间必须更新
		code.setModifyTime(new Date());
		CodeDO data = CodeConverter.vo2DO(code);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = codeDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Code失败，影响行数:" + n);
	}

	@Override
	public List<CodeVO> queryCodeList(CodeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CodeExample example = CodeConverter.query2Example(query);

		List<CodeDO> list = codeDAO.selectByExample(example);
			return CodeConverter.doList2VOList(list);
	}

	@Override
	public Long queryCodeCount(CodeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CodeExample example = CodeConverter.query2Example(query);
		long c = codeDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询码
	 */
	@Override
	public PageInfo<CodeVO> queryCodeByPage(CodeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<CodeVO> page = new PageInfo<>();

		CodeExample example = CodeConverter.query2Example(query);
		long totalCount = codeDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<CodeDO> list = codeDAO.selectByExample(example);
		page.setList(CodeConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
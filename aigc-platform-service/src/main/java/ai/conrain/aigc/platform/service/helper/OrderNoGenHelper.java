package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

@Component
public class OrderNoGenHelper {

    @Autowired
    private TairService tairService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    private static final Random random = new Random();

    /**
     * 生成订单号（32位长度）
     * 17位时间戳 + 8位预留 + 7位随机数字
     */
    public String generateOrderNo() {

        // 17位毫秒时间戳
        String timestamp = sdf.format(new Date());

        // 8位自增序号
        long count = tairService.getNextOrderNoSeq();

        // 7位随机数
        String randomStr = String.format("%07d", random.nextInt(10000000));

        // 拼接成32位订单号
        return timestamp + String.format("%08d", count) + randomStr;
    }
}

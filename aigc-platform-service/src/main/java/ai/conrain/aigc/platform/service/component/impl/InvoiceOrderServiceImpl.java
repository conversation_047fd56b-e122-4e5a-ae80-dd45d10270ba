package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.InvoiceOrderDAO;
import ai.conrain.aigc.platform.dal.entity.InvoiceOrderDO;
import ai.conrain.aigc.platform.dal.example.InvoiceOrderExample;
import ai.conrain.aigc.platform.service.component.InvoiceInfoService;
import ai.conrain.aigc.platform.service.component.InvoiceOrderService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.InvoiceOrderConverter;
import ai.conrain.aigc.platform.service.model.query.InvoiceOrderQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceOrderVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**   
 * InvoiceOrderService实现
 *
 * <AUTHOR>
 * @version InvoiceOrderService.java v 0.1 2024-06-27 12:49:39
 */
@Slf4j
@Service
public class InvoiceOrderServiceImpl implements InvoiceOrderService {

	/** DAO */
	@Autowired
	private InvoiceOrderDAO invoiceOrderDAO;

	@Autowired
	private InvoiceInfoService invoiceInfoService;

	@Override
	public InvoiceOrderVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		InvoiceOrderDO data = invoiceOrderDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return InvoiceOrderConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = invoiceOrderDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除InvoiceOrder失败");
	}

	@Override
	public InvoiceOrderVO insert(InvoiceOrderVO invoiceOrder) {
		AssertUtil.assertNotNull(invoiceOrder, ResultCode.PARAM_INVALID, "invoiceOrder is null");
		AssertUtil.assertTrue(invoiceOrder.getId() == null, ResultCode.PARAM_INVALID, "invoiceOrder.id is present");

		//创建时间、修改时间兜底
		if (invoiceOrder.getCreateTime() == null) {
			invoiceOrder.setCreateTime(new Date());
		}

		if (invoiceOrder.getModifyTime() == null) {
			invoiceOrder.setModifyTime(new Date());
		}

		InvoiceOrderDO data = InvoiceOrderConverter.vo2DO(invoiceOrder);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = invoiceOrderDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建InvoiceOrder失败");
		AssertUtil.assertNotNull(data.getId(), "新建InvoiceOrder返回id为空");
		invoiceOrder.setId(data.getId());
		return invoiceOrder;
	}


	@Override
	public void updateByIdSelective(InvoiceOrderVO invoiceOrder) {
		AssertUtil.assertNotNull(invoiceOrder, ResultCode.PARAM_INVALID, "invoiceOrder is null");
    	AssertUtil.assertTrue(invoiceOrder.getId() != null, ResultCode.PARAM_INVALID, "invoiceOrder.id is null");

		//修改时间必须更新
		invoiceOrder.setModifyTime(new Date());
		InvoiceOrderDO data = InvoiceOrderConverter.vo2DO(invoiceOrder);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = invoiceOrderDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新InvoiceOrder失败，影响行数:" + n);
	}

	@Override
	public List<InvoiceOrderVO> queryInvoiceOrderList(InvoiceOrderQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceOrderExample example = InvoiceOrderConverter.query2Example(query);

		List<InvoiceOrderDO> list = invoiceOrderDAO.selectByExample(example);
			return InvoiceOrderConverter.doList2VOList(list);
	}

	@Override
	public Long queryInvoiceOrderCount(InvoiceOrderQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceOrderExample example = InvoiceOrderConverter.query2Example(query);
		long c = invoiceOrderDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询发票订单关联
	 */
	@Override
	public PageInfo<InvoiceOrderVO> queryInvoiceOrderByPage(InvoiceOrderQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<InvoiceOrderVO> page = new PageInfo<>();

		InvoiceOrderExample example = InvoiceOrderConverter.query2Example(query);
		long totalCount = invoiceOrderDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<InvoiceOrderDO> list = invoiceOrderDAO.selectByExample(example);
		page.setList(InvoiceOrderConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}
}
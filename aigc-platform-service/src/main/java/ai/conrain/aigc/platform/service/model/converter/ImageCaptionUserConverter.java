package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionUserDO;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.dal.example.ImageCaptionUserExample;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;

import com.alibaba.fastjson2.JSON;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ImageCaptionUserConverter
 *
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
public class ImageCaptionUserConverter {

    /**
     * DO -> VO
     */
    public static ImageCaptionUserVO do2VO(ImageCaptionUserDO from) {
        ImageCaptionUserVO to = new ImageCaptionUserVO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setImageId(from.getImageId());
        to.setUserId(from.getUserId());
        to.setOriginalId(from.getOriginalId());
        to.setCaption(JSON.parseObject(from.getCaption()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageCaptionUserDO vo2DO(ImageCaptionUserVO from) {
        ImageCaptionUserDO to = new ImageCaptionUserDO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setImageId(from.getImageId());
        to.setUserId(from.getUserId());
        to.setOriginalId(from.getOriginalId());
        to.setCaption(JSON.toJSONString(from.getCaption()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageCaptionUserExample query2Example(ImageCaptionUserQuery from) {
        ImageCaptionUserExample to = new ImageCaptionUserExample();
        ImageCaptionUserExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())){
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getImageId())) {
            c.andImageIdEqualTo(from.getImageId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOriginalId())) {
            c.andOriginalIdEqualTo(from.getOriginalId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getCaption())) {
            c.andCaptionEqualTo(from.getCaption());
        }
        if (!ObjectUtils.isEmpty(from.getCaptionVersion())) {
            c.andCaptionVersionEqualTo(from.getCaptionVersion());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (ImageCaptionUserExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageCaptionUserVO> doList2VOList(List<ImageCaptionUserDO> list) {
        return CommonUtil.listConverter(list, ImageCaptionUserConverter::do2VO);
    }
}
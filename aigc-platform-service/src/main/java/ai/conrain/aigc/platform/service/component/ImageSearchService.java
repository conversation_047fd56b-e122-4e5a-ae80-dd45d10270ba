package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageSearchResult;

/**
 * 图像检索服务
 */
public interface ImageSearchService {

    /**
     * 搜索并推荐
     * @param request
     * @return
     */
    StyleImageSearchResult searchAndRecommend(StyleImageSearchRequest request) throws Exception;

    /**
     * 根据服装图片搜索并建立配对，用于训练排序模型
     */
    void searchByClothImgAndBuildPairs(MaterialModelVO cloth) throws Exception;
}

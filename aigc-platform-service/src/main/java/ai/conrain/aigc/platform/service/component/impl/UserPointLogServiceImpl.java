package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.UserPointLogDAO;
import ai.conrain.aigc.platform.dal.entity.UserPointLogDO;
import ai.conrain.aigc.platform.dal.example.UserPointLogExample;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserPointLogService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserPointLogConverter;
import ai.conrain.aigc.platform.service.model.query.UserPointLogQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointUsageInfoVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * UserPointLogService实现
 *
 * <AUTHOR>
 * @version UserPointLogService.java v 0.1 2024-06-21 03:18:16
 */
@Slf4j
@Service
public class UserPointLogServiceImpl implements UserPointLogService {

    @Lazy
    @Autowired
    private UserService userService;

    /** DAO */
    @Autowired
    private UserPointLogDAO userPointLogDAO;

    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Lazy
    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public UserPointLogVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        UserPointLogDO data = userPointLogDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return UserPointLogConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = userPointLogDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserPointLog失败");
    }

    @Override
    public UserPointLogVO insert(UserPointLogVO userPointLog) {
        AssertUtil.assertNotNull(userPointLog, ResultCode.PARAM_INVALID, "userPointLog is null");
        AssertUtil.assertTrue(userPointLog.getId() == null, ResultCode.PARAM_INVALID, "userPointLog.id is present");

        // 创建时间、修改时间兜底
        if (userPointLog.getCreateTime() == null) {
            userPointLog.setCreateTime(new Date());
        }

        if (userPointLog.getModifyTime() == null) {
            userPointLog.setModifyTime(new Date());
        }

        if (userPointLog.getOriginPoint() != null) {
            userPointLog.addExtInfo("originPoint", userPointLog.getOriginPoint());
            userPointLog.addExtInfo("targetPoint", userPointLog.getTargetPoint());
        }

        if (userPointLog.getOriginGivePoint() != null) {
            userPointLog.addExtInfo("originGivePoint", userPointLog.getOriginGivePoint());
            userPointLog.addExtInfo("targetGivePoint", userPointLog.getTargetGivePoint());
        }

        if (userPointLog.getOriginExperiencePoint() != null) {
            userPointLog.addExtInfo("originExperiencePoint", userPointLog.getOriginExperiencePoint());
            userPointLog.addExtInfo("targetExperiencePoint", userPointLog.getTargetExperiencePoint());
        }

        if (userPointLog.getOriginModelPoint() != null) {
            userPointLog.addExtInfo("originModelPoint", userPointLog.getOriginModelPoint());
            userPointLog.addExtInfo("targetModelPoint", userPointLog.getTargetModelPoint());
        }

        if (userPointLog.getModelName() != null) {
            userPointLog.addExtInfo("modelName", userPointLog.getModelName());
        }

        UserPointLogDO data = UserPointLogConverter.vo2DO(userPointLog);
        int n = userPointLogDAO.insertSelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建UserPointLog失败");
        AssertUtil.assertNotNull(data.getId(), "新建UserPointLog返回id为空");
        userPointLog.setId(data.getId());
        return userPointLog;
    }

    @Override
    public void updateByIdSelective(UserPointLogVO userPointLog) {
        AssertUtil.assertNotNull(userPointLog, ResultCode.PARAM_INVALID, "userPointLog is null");
        AssertUtil.assertTrue(userPointLog.getId() != null, ResultCode.PARAM_INVALID, "userPointLog.id is null");

        // 修改时间必须更新
        userPointLog.setModifyTime(new Date());
        UserPointLogDO data = UserPointLogConverter.vo2DO(userPointLog);
        int n = userPointLogDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserPointLog失败，影响行数:" + n);
    }

    @Override
    public List<UserPointLogVO> queryUserPointLogList(UserPointLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserPointLogExample example = UserPointLogConverter.query2Example(query);

        List<UserPointLogDO> list = userPointLogDAO.selectByExample(example);
        return UserPointLogConverter.doList2VOList(list);
    }

    @Override
    public Long queryUserPointLogCount(UserPointLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserPointLogExample example = UserPointLogConverter.query2Example(query);
        return userPointLogDAO.countByExample(example);
    }

    /**
     * 带条件分页查询用户算力流水
     */
    @Override
    public PageInfo<UserPointLogVO> queryUserPointLogByPage(UserPointLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<UserPointLogVO> page = new PageInfo<>();

        UserPointLogExample example = UserPointLogConverter.query2Example(query);
        long totalCount = userPointLogDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<UserPointLogDO> list = userPointLogDAO.selectByExampleWithBLOBs(example);
        page.setList(UserPointLogConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 带条件分页查询用户算力流水
     *
     * @param query
     * @return
     */
    @Override
    public PageInfo<UserPointUsageInfoVO> queryPointUsageInfoByPage(UserPointLogQuery query) {
        PageInfo<UserPointUsageInfoVO> ret = new PageInfo<>();

        PageInfo<UserPointLogVO> page = this.queryUserPointLogByPage(query);

        ret.setHasNextPage(page.isHasNextPage());
        ret.setSize(page.getSize());
        ret.setTotalCount(page.getTotalCount());

        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<UserPointUsageInfoVO> list = convertToUsageInfoList(page.getList());
            ret.setList(list);
        }

        return ret;
    }

    @Override
    public UserPointLogVO lockByBizId(Integer bizId, Integer userId, PointLogTypeEnum pointLogType) {
        UserPointLogDO data = userPointLogDAO.lockByBizId(bizId, pointLogType.getCode());

        AssertUtil.assertNotNull(data, ResultCode.BIZ_FAIL, "未找到该bizId对应的记录");
        AssertUtil.assertTrue(userId.equals(data.getUserId()), ResultCode.ILLEGAL_PERMISSION,
            "该bizId对应的记录不属于该用户");

        return UserPointLogConverter.do2VO(data);
    }

    @Override
    public Integer getCustomerConsumptionPoints(String startDate, String endDate, List<Integer> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return 0;
        }
        return userPointLogDAO.getCustomerConsumptionPoints(startDate, endDate, userIdList);
    }

    @Override
    public Integer getPayUserCountByDate(String startDate, String endDate, List<Integer> customUserList) {
        if (CollectionUtils.isEmpty(customUserList)) {
            return 0;
        }
        return userPointLogDAO.getPayUserCountByDate(startDate, endDate, customUserList);
    }

    @Override
    public List<Integer> getPayUserByDate(String startDate, String endDate, List<Integer> customUserList) {
        if (CollectionUtils.isEmpty(customUserList)) {
            return new ArrayList<>();
        }
        return userPointLogDAO.getPayUserByDate(startDate, endDate, customUserList);
    }

    private List<UserPointUsageInfoVO> convertToUsageInfoList(List<UserPointLogVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 提取所有需要批量查询的ID
        Set<Integer> userIds = new HashSet<>();
        Set<Integer> creativeBatchIds = new HashSet<>();
        Set<Integer> loraModelIds = new HashSet<>(); // 新增：lora model IDs

        for (UserPointLogVO vo : list) {
            if (vo.getUserId() != null) {userIds.add(vo.getUserId());}
            if (vo.getOperatorId() != null) {userIds.add(vo.getOperatorId());}

            if (vo.getRelatedId() != null) {
                if (vo.getType().equals(PointLogTypeEnum.CREATIVE_CREATE)) {
                    creativeBatchIds.add(vo.getRelatedId());
                } else if (vo.getType().equals(PointLogTypeEnum.LORA_TRAIN) || vo.getType().equals(
                    PointLogTypeEnum.LORA_TRAIN_RETURN)) {
                    loraModelIds.add(vo.getRelatedId());
                }
            }
        }

        // 批量查询用户信息、创作批次、lora模型
        List<UserVO> userList = userService.batchQueryById(new ArrayList<>(userIds));
        List<CreativeBatchVO> creativeBatches = creativeBatchService.batchQueryByIds(new ArrayList<>(creativeBatchIds),
            false);
        List<MaterialModelVO> loraModels = materialModelService.batchQueryByIds(new ArrayList<>(loraModelIds));

        // 构建映射关系
        Map<Integer, UserVO> userMap = CommonUtil.toMap(userList, UserVO::getId);
        Map<Integer, CreativeBatchVO> creativeBatchMap = CommonUtil.toMap(creativeBatches, CreativeBatchVO::getId);
        Map<Integer, MaterialModelVO> loraModelMap = CommonUtil.toMap(loraModels, MaterialModelVO::getId);

        // 转换每一条记录
        List<UserPointUsageInfoVO> result = new ArrayList<>();
        for (UserPointLogVO vo : list) {
            UserPointUsageInfoVO infoVO = convUsageInfoInternal(vo, userMap, creativeBatchMap, loraModelMap);
            if (infoVO != null) {
                result.add(infoVO);
            }
        }

        return result;
    }

    private UserPointUsageInfoVO convUsageInfoInternal(UserPointLogVO v, Map<Integer, UserVO> userMap,
                                                       Map<Integer, CreativeBatchVO> creativeBatchMap,
                                                       Map<Integer, MaterialModelVO> loraModelMap) {

        UserPointUsageInfoVO ret = new UserPointUsageInfoVO();
        ret.setPointLogId(v.getId());
        ret.setUserId(v.getUserId());
        ret.setType(v.getType());
        ret.setUsedPoint(CommonUtil.point2MusePoint(v.getPoint()));
        ret.setUsedGivePoint(CommonUtil.point2MusePoint(v.getGivePoint()));
        ret.setUsedExperiencePoint(v.getExperiencePoint());
        ret.setUsedModelPoint(v.getModelPoint());
        ret.setOperatorUserId(v.getOperatorId());
        ret.setCreateTime(v.getCreateTime());

        ret.setMasterUser(v.getOperatorId() != null && v.getOperatorId().equals(v.getUserId()));

        // 查询用户信息
        if (v.getUserId() != null) {
            UserVO user = userMap.get(v.getUserId());
            if (user != null) {
                ret.setMasterLoginId(user.getLoginId());
                ret.setMasterUserNickName(user.getNickName());
                ret.setMasterRole(user.getRoleType());
            }
        }

        // 查询操作者信息
        if (v.getOperatorId() != null) {
            UserVO operator = userMap.get(v.getOperatorId());
            if (operator != null) {
                ret.setOperatorRole(operator.getRoleType());
                ret.setOperatorLoginId(operator.getLoginId());
                ret.setOperatorNickName(operator.getNickName());
            }
        }

        AssertUtil.assertNotNull(v.getType(), "UserPointLogVO.type为空");

        // 创作图
        if (v.getType().equals(PointLogTypeEnum.CREATIVE_CREATE)) {
            AssertUtil.assertNotNull(v.getRelatedId(), "UserPointLogVO.relatedId为空");
            Integer creativeBatchId = v.getRelatedId();
            CreativeBatchVO batch = creativeBatchMap.get(creativeBatchId);
            if (batch != null) {
                ret.setUsageScene(batch.getModelName());
            }
            ret.setUsageWay("出作品图");

        } else if (v.getType().equals(PointLogTypeEnum.LORA_TRAIN)) {

            if (initLoraInfo(v, ret, loraModelMap)) { // 使用映射
                return null;
            }

            ret.setUsageWay(getUsageWay4Train(v));
        } else if (v.getType().equals(PointLogTypeEnum.LORA_TRAIN_RETURN)) {

            if (initLoraInfo(v, ret, loraModelMap)) {
                return null;
            }

            ret.setUsageWay("服装退款");

        } else if (v.getType() == PointLogTypeEnum.EXPERIENCE_CREATE) {

            ret.setUsageScene(v.getType().getTitle());
            ret.setUsageWay("创作");

        } else if (CreativeTypeEnum.getShowPointLogTypeList().contains(v.getType().getCode())) {

            ret.setUsageScene(v.getType().getTitle());
            if (CreativeTypeEnum.isReturnLogType(v.getType())) {
                ret.setUsageWay(v.getType().getDesc());
            } else {
                ret.setUsageWay("创作");
            }

        } else if (v.getType() == PointLogTypeEnum.RECHARGE || v.getType() == PointLogTypeEnum.RECHARGE_MANUAL) {
            ret.setUsageScene("充值");

        } else if (StringUtils.isNotBlank(v.getMemo())) {
            ret.setUsageScene(v.getMemo());
        }

        return ret;
    }

    private boolean initLoraInfo(UserPointLogVO v, UserPointUsageInfoVO ret,
                                 Map<Integer, MaterialModelVO> loraModelMap) {
        if (StringUtils.isNotBlank(v.getExtInfo("modelName"))) {
            ret.setUsageScene(v.getExtInfo("modelName"));
        } else {
            AssertUtil.assertNotNull(v.getRelatedId(), "UserPointLogVO.relatedId为空");
            Integer loraModelId = v.getRelatedId();
            MaterialModelVO lora = loraModelMap.get(loraModelId); // 使用 map 替代 selectById
            if (lora != null) {
                ret.setUsageScene(lora.getName());
            } else {
                log.error("loraModelId对应的lora不存在，忽略这条记录（脏数据）UserPointLogVO.id:{}, modelId:{}", v.getId(),
                    loraModelId);
                return true;
            }
        }
        return false;
    }

    private String getUsageWay4Train(UserPointLogVO v) {
        if (v.getExtInfo() != null && v.getExtInfo().containsKey(CommonConstants.materialType)) {
            String materialType = v.getExtInfo(CommonConstants.materialType);
            if (StringUtils.equals(materialType, MaterialType.face.name())) {
                return "模特学习";
            } else if (StringUtils.equals(materialType, MaterialType.scene.name())) {
                return "场景学习";
            }
        }

        // 服装学习：
        boolean multiColors = v.getExtInfo() != null && v.getExtInfo().containsKey(CommonConstants.multiColors)
                              && "Y".equals(v.getExtInfo(CommonConstants.multiColors));
        boolean ironingCloth = v.getExtInfo() != null && v.getExtInfo().containsKey(CommonConstants.KEY_IRONING_CLOTH)
                               && "Y".equals(v.getExtInfo(CommonConstants.KEY_IRONING_CLOTH));
        boolean agentUpload = v.getExtInfo() != null && v.getExtInfo().containsKey(CommonConstants.uploadByAgentId)
                              && v.getExtInfo().getInteger(CommonConstants.uploadByAgentId) != null;

        String u = multiColors ? "多色" : "单色";
        u += "服装学习";

        if (agentUpload) {
            u += "-代拍";
        }

        if (ironingCloth) {
            u += "-代熨烫";
        }

        return u;
    }

}
package ai.conrain.aigc.platform.service.model.converter;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.MerchantPreferenceDO;
import ai.conrain.aigc.platform.dal.example.MerchantPreferenceExample;
import ai.conrain.aigc.platform.service.enums.PreferenceTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.model.query.MerchantPreferenceQuery;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import jakarta.validation.Valid;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CUSTOM_SCENE_DESC;

/**
 * MerchantPreferenceConverter
 *
 * @version MerchantPreferenceService.java v 0.1 2024-11-12 08:02:24
 */
public class MerchantPreferenceConverter {

    /**
     * DO -> VO
     */
    public static MerchantPreferenceVO do2VO(MerchantPreferenceDO from) {
        MerchantPreferenceVO to = new MerchantPreferenceVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(PreferenceTypeEnum.getByCode(from.getType()));
        if (StringUtils.isNotBlank(from.getTags())) {
            to.setTags(Arrays.asList(from.getTags().split(",")));
        }
        to.setMemo(from.getMemo());
        if (StringUtils.isNotBlank(from.getFaces())) {
            to.setFaces(JSONArray.parseArray(from.getFaces()).toJavaList(Integer.class));
        }
        if (StringUtils.isNotBlank(from.getScenes())) {
            to.setScenes(JSONArray.parseArray(from.getScenes()).toJavaList(Integer.class));
        }
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getClothCollocation())) {
            to.setClothCollocation(JSONObject.parseObject(from.getClothCollocation(), ClothCollocationModel.class));
        }
        to.setEnableAutoCreative(from.getEnableAutoCreative());
        to.setImageNum(from.getImageNum());
        to.setImageProportion(from.getImageProportion());
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static MerchantPreferenceDO vo2DO(MerchantPreferenceVO from) {
        MerchantPreferenceDO to = new MerchantPreferenceDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType().toString());
        to.setMemo(from.getMemo());
        if (CollectionUtils.isNotEmpty(from.getTags())) {
            to.setTags(String.join(",", from.getTags()));
        }
        if (CollectionUtils.isNotEmpty(from.getFaces())) {
            to.setFaces(JSONArray.toJSONString(from.getFaces()));
        }
        if (CollectionUtils.isNotEmpty(from.getScenes())) {
            to.setScenes(JSONArray.toJSONString(from.getScenes()));
        }
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (from.getClothCollocation() != null) {
            to.setClothCollocation(JSONObject.toJSONString(from.getClothCollocation()));
        }
        to.setEnableAutoCreative(from.getEnableAutoCreative());
        to.setImageNum(from.getImageNum());
        to.setImageProportion(from.getImageProportion());
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static MerchantPreferenceQuery do2Query(MerchantPreferenceDO from) {
        MerchantPreferenceQuery to = new MerchantPreferenceQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType());
        to.setMemo(from.getMemo());
        to.setFaces(from.getFaces());
        to.setScenes(from.getScenes());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothCollocation(from.getClothCollocation());
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSONObject.parseObject(from.getExtInfo(), JSONObject.class));
        }

        return to;
    }

    /**
     * Query -> DO
     */
    public static MerchantPreferenceDO query2DO(MerchantPreferenceQuery from) {
        MerchantPreferenceDO to = new MerchantPreferenceDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType());
        to.setMemo(from.getMemo());
        to.setFaces(from.getFaces());
        to.setScenes(from.getScenes());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothCollocation(from.getClothCollocation());
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * Query -> Example
     */
    public static MerchantPreferenceExample query2Example(MerchantPreferenceQuery from) {
        MerchantPreferenceExample to = new MerchantPreferenceExample();
        MerchantPreferenceExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getFaces())) {
            c.andFacesEqualTo(from.getFaces());
        }
        if (!ObjectUtils.isEmpty(from.getScenes())) {
            c.andScenesEqualTo(from.getScenes());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (MerchantPreferenceExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<MerchantPreferenceVO> doList2VOList(List<MerchantPreferenceDO> list) {
        return CommonUtil.listConverter(list, MerchantPreferenceConverter::do2VO);
    }

    public static MerchantPreferenceDetail convertDetail(List<MerchantPreferenceVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new MerchantPreferenceDetail();
        }

        MerchantPreferenceDetail detail = new MerchantPreferenceDetail();

        ArrayList<MerchantPreferenceVO> preferences = new ArrayList<>();
        ArrayList<MerchantPreferenceVO> creativePreferences = new ArrayList<>();

        list.forEach(e -> {
            if (e.getType().equals(PreferenceTypeEnum.AUTO_DELIVERY)) {
                preferences.add(e);
            } else {
                creativePreferences.add(e);
            }
        });

        detail.setPreferences(preferences);
        detail.setCreativePreferences(creativePreferences);
        detail.setUserId(list.get(0).getUserId());
        return detail;
    }

    public static List<MerchantPreferenceDetail> convertDetailList(List<MerchantPreferenceVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<Integer, List<MerchantPreferenceVO>> map = new HashMap<>();

        list.forEach(e -> {
            List<MerchantPreferenceVO> data = map.computeIfAbsent(e.getUserId(), k -> new ArrayList<>());
            data.add(e);
        });

        return map.values().stream().map(MerchantPreferenceConverter::convertDetail).sorted(
            Comparator.comparingInt(MerchantPreferenceDetail::getUserId)).collect(Collectors.toList());
    }

    public static MerchantPreferenceVO convertPreferenceVO(Integer userId, ClothCollocationDetail clothCollocation){
        MerchantPreferenceVO preferenceVO = new MerchantPreferenceVO();
        preferenceVO.setUserId(userId);
        preferenceVO.setOperatorId(userId);
        preferenceVO.setType(PreferenceTypeEnum.CLOTH_COLLOCATION);
        preferenceVO.setClothCollocation(clothCollocation.getCollocation());
        preferenceVO.setMemo(clothCollocation.getName());
        return preferenceVO;
    }

    public static List<ClothCollocationDetail> convertClothCollocationDetail(List<MerchantPreferenceVO> list){
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(e -> {
            ClothCollocationDetail clothCollocationDetail = new ClothCollocationDetail();
            clothCollocationDetail.setCollocation(e.getClothCollocation());
            clothCollocationDetail.setName(e.getMemo());
            clothCollocationDetail.setId(e.getId());
            return clothCollocationDetail;
        }).collect(Collectors.toList());
    }

    public static MerchantPreferenceVO query2VO(@Valid MerchantPreferenceQuery query) {
        return do2VO(query2DO(query));
    }
}
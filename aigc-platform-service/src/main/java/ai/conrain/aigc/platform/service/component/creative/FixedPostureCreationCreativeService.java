package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.helper.GrayscaleTestHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest.ReferenceInfo;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CAMERA_ANGLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_AFTER_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_LORA_STRENGTH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_IS_FIXED_POSE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LENS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MODEL_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NEGATIVE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_POSTURE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_STYLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.REFERENCE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.REFERENCE_INFO_LIST;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_INSTANT_ID_V2_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_NEW_FACE_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_NEW_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_NEW_INSTANT_ID_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_CREATIVE_STYLE_EXP_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW;

@Slf4j
@Service
public class FixedPostureCreationCreativeService extends CreateImageCreativeService {

    @Autowired
    private ComfyUIHelper comfyUIHelper;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private GrayscaleTestHelper grayscaleTestHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FIXED_POSTURE_CREATION;
    }

    @Override
    protected CreativeBatchVO buildData(AddCreativeRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO creativeBatchVO = super.buildData(request, modelVO);

        //  设置任务类型
        creativeBatchVO.setType(getCreativeType());
        // 添加固定姿势信息列表
        creativeBatchVO.addExtInfo(REFERENCE_INFO_LIST, request.getReferenceInfoList());

        // 处理服装搭配
        return creativeBatchVO;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        log.info(
            "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据...");

        // 反向解析参考图
        List<AddCreativeRequest.ReferenceInfo> referenceInfoList = objectMapper.convertValue(
            batch.getExtInfo().get(REFERENCE_INFO_LIST), new TypeReference<>() {});

        if (CollectionUtils.isEmpty(referenceInfoList)) {
            return null;
        }

        // 并行上传所有参考图片至 ComfyUI，提升上传效率
        List<CompletableFuture<?>> allUploadFutures = new ArrayList<>();
        referenceInfoList.forEach(referenceInfo -> {
            CompletableFuture<Void> referenceImageFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    String uploadImageUrl = referenceInfo.getOriginalImageUrl();
                    if (StringUtils.isBlank(uploadImageUrl) && referenceInfo.getReferenceConfig() != null) {
                        uploadImageUrl = referenceInfo.getReferenceConfig().getString(
                            CommonConstants.ORIGINAL_IMAGE_URL);
                    }
                    if (StringUtils.isBlank(uploadImageUrl)) {
                        log.error(
                            "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildData::原始参考图为空，终止流程");
                        throw new BizException(ResultCode.BIZ_FAIL);
                    }
                    return comfyUIHelper.upLoadImage(uploadImageUrl);
                } catch (IOException e) {
                    log.error("[ComfyUI流程][固定姿势创作]参考图片上传失败: {}", e.getMessage());
                    throw new RuntimeException("参考图片上传失败", e);
                }
            }).thenAccept(referenceInfo::setUploadComfyUIImageUrl);
            // 添加进入allUploadFutures
            allUploadFutures.add(referenceImageFuture);
        });

        try {
            // 等待所有图片上传完成
            CompletableFuture.allOf(allUploadFutures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error(
                "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::图片上传失败，终止流程: {}",
                e.getMessage());
            throw new BizException(ResultCode.BIZ_FAIL, "图片上传失败，请稍后重试");
        }

        List<CreativeTaskVO> result = new ArrayList<>();

        for (int i = 0; i < referenceInfoList.size(); i++) {
            ReferenceInfo referenceInfo = referenceInfoList.get(i);
            // 构建任务信息
            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);

            // 填充场景信息
            CreativeElementVO sceneElement = creativeElementService.selectById(referenceInfo.getLoraId());
            elements.add(sceneElement);

            // 填充扩展信息,i为referenceInfo的索引
            fillTaskExt(target, batch, elements, i);

            // 使用已上传完成的图片URL
            String referenceImageUrl = referenceInfo.getUploadComfyUIImageUrl();

            // 设置 loraId
            target.addExtInfo(KEY_MODEL_ID, referenceInfo.getLoraId());
            //  设置任务类型
            target.setType(getCreativeType());
            // 设置任务出图数量
            target.setBatchCnt(3);

            // 添加 参考图图片
            target.addExtInfo(CommonConstants.REFERENCE_IMAGE, referenceImageUrl);

            // 添加参考图原图片
            target.addExtInfo(CommonConstants.REFERENCE_ORIGINAL_IMAGE, referenceInfo.getImageUrl());
            // 添加风格lora配置
            if (Objects.nonNull(referenceInfo.getReferenceConfig())) {
                // 设置 lens
                target.addExtInfo(CommonConstants.KEY_LENS,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_LENS));
                // 设置 posture
                target.addExtInfo(CommonConstants.KEY_POSTURE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_POSTURE));
                // 设置 style
                target.addExtInfo(CommonConstants.KEY_STYLE,
                    referenceInfo.getReferenceConfig().get(CommonConstants.KEY_STYLE));
            }
            // 设置扩展标签
            target.addExtInfo(CommonConstants.BACK_TAGS, referenceInfo.getBackExtTags());
            // 设置 lora 地址
            target.addExtInfo(CommonConstants.KEY_LORA_PATH, referenceInfo.getLoraPath());

            // 设置图片比例
            target.setImageProportion(batch.getImageProportion());

            // 插入任务信息
            CreativeTaskVO data = creativeTaskService.insert(target);

            // 添加至集合中
            result.add(data);
        }

        log.info(
            "[ComfyUI流程][固定姿势创作]FixedPostureCreationCreativeService::buildTasks::开始构建CreativeTaskVO 任务数据构建完成,共：{}个任务",
            result.size());

        return result;
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        // 调用父类方法，传递固定姿势创作的日志前缀
        String flowKey = determineFlowKeyWithLogging(elements, modelVO, task, context, "【固定姿势创作】");

        // 将通用流程映射为固定姿势创作专用流程
        return mapToFixedPostureFlow(flowKey);
    }

    /**
     * 将通用创作流程映射为固定姿势创作专用流程
     *
     * @param flowKey 父类返回的流程key
     * @return 固定姿势创作专用的流程key
     */
    protected String mapToFixedPostureFlow(String flowKey) {
        if (flowKey == null) {
            return null;
        }

        switch (flowKey) {
            case FLUX_CREATIVE_INSTANT_ID_V2_FLOW:
            case FLUX_CREATIVE_NEW_INSTANT_ID_FLOW:
                //TODO 临时 修改
                //return FLUX_FIXED_POSTURE_CREATIVE_NEW_INSTANT_ID_FLOW;
            case FLUX_CREATIVE_NEW_FACE_FLOW:
                return FLUX_FIXED_POSTURE_CREATIVE_NEW_FACE_FLOW;

            case FLUX_CREATIVE_NEW_FLOW:
                return FLUX_FIXED_POSTURE_CREATIVE_NEW_FLOW;

            case FLUX_CREATIVE_STYLE_EXP_FLOW:
                return FLUX_FIXED_POSTURE_CREATIVE_STYLE_EXP_FLOW;

            default:
                return flowKey; // 其他流程（如纯色背景）直接返回
        }
    }

    /**
     * 数据修正
     *
     * @param elements   元素列表
     * @param modelVO    模型数据
     * @param context    配置map上下文
     * @param task       任务
     * @param promptSeed prompt的种子
     */
    @Override
    protected void dataCorrect(List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context,
                               CreativeTaskVO task, long promptSeed) {
        context.put(KEY_IS_FIXED_POSE, true);

        CreativeElementVO originFaceElement = elements.stream().filter(
            element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())).findFirst().orElse(null);
        String needReplaceModelKeys = systemConfigService.queryValueByKey(SystemConstants.NEED_REPLACE_MODEL_KEYS);

        Integer loraId = task.getIntegerFromExtInfo(KEY_MODEL_ID);
        CreativeElementVO sceneElement = creativeElementService.selectById(loraId);
        if (sceneElement == null) {
            // 初始化创建场景参数
            sceneElement = new CreativeElementVO();
            sceneElement.setConfigKey(ElementConfigKeyEnum.SCENE.name());

            sceneElement.addExtInfo(KEY_LORA_PATH, task.getStringFromExtInfo(CommonConstants.KEY_LORA_PATH));
            sceneElement.addExtInfo(KEY_LENS, task.getStringFromExtInfo(CommonConstants.KEY_LENS));
            sceneElement.setTags(task.getStringFromExtInfo(CommonConstants.BACK_TAGS));
            sceneElement.addExtInfo(KEY_POSTURE, task.getStringFromExtInfo(CommonConstants.KEY_POSTURE));
            sceneElement.addExtInfo(KEY_STYLE, task.getStringFromExtInfo(CommonConstants.KEY_STYLE));
            sceneElement.addExtInfo(KEY_NEGATIVE, StringUtils.EMPTY);
        }
        // 添加场景元素
        elements.add(sceneElement);

        CreativeElementVO faceElement = CommonUtil.deepCopy(originFaceElement);

        // 添加参考图片
        context.put(REFERENCE_IMAGE, task.getStringFromExtInfo(REFERENCE_IMAGE));

        // lora模特，默认修脸强度1
        if (ElementUtils.isLoraFace(faceElement) && null == faceElement.getExtInfo(KEY_FACE_AFTER_STRENGTH,
            Double.class)) {
            faceElement.addExtInfo(KEY_FACE_AFTER_STRENGTH, 1);
        }

        if (isNoshowFace(context, sceneElement) && faceElement != null && grayscaleTestHelper.isHit(
            "fixed_pose_noshow_face", task.getUserId(), "固定姿势-无头风")) {
            faceElement.addExtInfo(KEY_FACE_LORA_STRENGTH, 0.8);
        }

        // 重置任务视角信息
        try {
            List<String> cameraAngles = getCameraAngles(task);

            CameraAngleEnum styleOrientation = CameraAngleEnum.getOrientationByStr(sceneElement.getType());
            CameraAngleEnum.replaceOrientation(styleOrientation, cameraAngles);
            CameraAngleEnum styleBodyPosition = CameraAngleEnum.getBodyPositionByStr(sceneElement.getType());
            CameraAngleEnum.replaceBodyPosition(styleBodyPosition, cameraAngles);

            task.addExtInfo(KEY_CAMERA_ANGLE, cameraAngles);

            log.info("识别到风格lora场景，重置构图的角度,batchId={},cameraAngles={}", task.getBatchId(), cameraAngles);
        } catch (Exception e) {
            log.warn("解析视角参数失败，使用默认值", e);
        }

        // 如果不需要替换模型，直接返回原有的设置
        if (StringUtils.isBlank(needReplaceModelKeys) || !needReplaceFaceFeatures(faceElement, sceneElement, context)) {
            context.put("lora", modelVO);
            context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
            context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
            return;
        }

        String faceFeatures = StringUtils.EMPTY;
        if (Objects.nonNull(faceElement)) {
            faceFeatures = getFaceFeatures(faceElement);
        }

        String[] split = StringUtils.split(needReplaceModelKeys, ",");

        for (String key : split) {
            replaceFaceFeatures(modelVO, key, faceFeatures);
            replaceFaceFeatures(faceElement, key, faceFeatures);
            replaceFaceFeatures(sceneElement, key, faceFeatures);
        }

        context.put("lora", modelVO);
        context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
        context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
    }
}

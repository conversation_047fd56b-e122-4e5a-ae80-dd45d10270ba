/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;

/**
 * <AUTHOR>
 * @version : GrayscaleTestHelper.java, v 0.1 2025/7/13 16:54 renxiao.wu Exp $
 */
@Slf4j
@Component
public class GrayscaleTestHelper {
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 是否命中灰度测试
     *
     * @param key       key
     * @param sceneName 场景名称
     * @return true 命中灰度实验
     */
    public boolean isHit(String key, String sceneName) {
        return isHit(key, null, sceneName);
    }

    /**
     * 判断是否命中灰度实验
     *
     * @param key       key
     * @param userId    用户id
     * @param sceneName 场景名称
     * @return true 命中灰度实验
     */
    public boolean isHit(String key, Integer userId, String sceneName) {
        JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
        if (switchJson == null) {
            return false;
        }

        if (!switchJson.containsKey(key)) {
            return false;
        }

        if (userId == null) {
            float flag = switchJson.getFloatValue(key);
            return GrayscaleTestUtils.isHit(flag, sceneName);
        }

        JSONObject detailSwitchJson = switchJson.getJSONObject(key);
        if (detailSwitchJson == null) {
            return false;
        }

        if (StringUtils.equals("rate", detailSwitchJson.getString("type"))) {
            float flag = detailSwitchJson.getFloatValue("rate");
            return GrayscaleTestUtils.isHit(flag, sceneName);
        }

        JSONArray userArr = detailSwitchJson.getJSONArray("user");
        if (userArr != null) {
            if (userArr.contains("ALL")) {
                log.info("当前{}场景，命中灰度实验,ALL,userId={}", sceneName, userId);
                return true;
            }

            if (userArr.contains(userId)) {
                log.info("当前{}场景，命中灰度实验,userId={}", sceneName, userId);
                return true;
            }
        }

        log.info("当前{}场景，未命中灰度实验,userId={}", sceneName, userId);
        return false;
    }
}

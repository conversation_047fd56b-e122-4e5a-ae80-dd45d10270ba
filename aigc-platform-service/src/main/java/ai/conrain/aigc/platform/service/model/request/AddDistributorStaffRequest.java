package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.validation.EnumValid;
import ai.conrain.aigc.platform.service.validation.Mobile;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

@Data
public class AddDistributorStaffRequest implements Serializable {

    /** 手机号 */
    @Mobile
    private String mobile;

    /** 昵称 */
    @NotBlank(message = "昵称不能为空")
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50之间")
    private String nickName;

    /** 角色 */
    @EnumValid(value = CustomRoleEnum.class, nullable = true)
    private String customRole;

    /** 部门id */
    private Integer deptOrgId;
}

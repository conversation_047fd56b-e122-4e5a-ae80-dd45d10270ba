package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class AssignCustomer2DistributorRequest implements Serializable {

    private String userCorpName;

    @NotNull
    private Integer userId;

    @NotEmpty
    private List<Integer> distributorOrgIdPath;
}

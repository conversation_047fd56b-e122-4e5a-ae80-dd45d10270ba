package ai.conrain.aigc.platform.service.model.request;


import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddDeptRequest implements Serializable {

    /** 父级组织ID，根组织为0 */
    @NotNull
    private Integer parentId;

    /** 组织名称 */
    @NotBlank
    private String name;

    /** 销售类型 */
    private String salesType;

    /** 二级渠道管理员昵称 */
    private String nickName;

    /** 二级渠道管理员手机号 */
    private String mobile;

    private String customRole;
}

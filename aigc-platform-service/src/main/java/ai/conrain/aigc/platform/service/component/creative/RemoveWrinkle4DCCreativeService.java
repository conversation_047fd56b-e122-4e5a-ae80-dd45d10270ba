package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.meitu.MeituService;
import ai.conrain.aigc.platform.integration.meitu.model.MeituRemoveWrinkleRequest;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkle4DCRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class RemoveWrinkle4DCCreativeService extends AbstractCreativeService<RemoveWrinkle4DCRequest> {

    @Autowired
    private MeituService meituService;

    @Autowired
    private OssHelper ossHelper;

    @Override
    protected CreativeBatchVO buildData(RemoveWrinkle4DCRequest request, MaterialModelVO modelVO) throws IOException {
        MeituRemoveWrinkleRequest meituRequest = MeituRemoveWrinkleRequest.fromUrl(request.getOriginImage());
        List<String> resultImages = new ArrayList<>();
        for (Integer alpha : request.getAlphas()) {
            AssertUtil.assertNotNull(alpha, "alpha cannot be null");
            meituRequest.setAlpha(alpha);
            String resultBase64 = meituService.removeWrinkle(meituRequest);
            AssertUtil.assertNotBlank(resultBase64, ResultCode.BIZ_FAIL, "meitu remove wrinkle failed");

            // 将base64结果上传到OSS
            String resultImageUrl = ossHelper.uploadBase64(resultBase64, ".jpg", "");
            AssertUtil.assertNotBlank(resultImageUrl, ResultCode.BIZ_FAIL, "oss upload failed");
            resultImages.add(resultImageUrl);
        }
        AssertUtil.assertNotEmpty(resultImages, "resultImages cannot be empty");

        // imageSource 字段 和 taskId 双重验证
        CreativeBatchVO batch = new CreativeBatchVO();
        batch.setType(getCreativeType());
        batch.setUserId(OperationContextHolder.getMasterUserId());
        batch.setShowImage(resultImages.get(0));
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        batch.setBatchCnt(resultImages.size());
        batch.setImageProportion("NONE");
        batch.setStatus(CreativeStatusEnum.FINISHED);
        batch.setExtInfo(CommonUtil.java2JSONObject(request));
        batch.addExtInfo(KEY_ORIGIN_IMAGE, request.getOriginImage());
        batch.setResultImages(resultImages);
        
        if (StringUtils.equals(HISTORY, request.getImageSource())) {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        target.setStatus(CreativeStatusEnum.FINISHED);
        target.setResultImages(Collections.singletonList(batch.getResultImages().get(idx)));
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.REMOVE_WRINKLE_4_DC;
    }

}

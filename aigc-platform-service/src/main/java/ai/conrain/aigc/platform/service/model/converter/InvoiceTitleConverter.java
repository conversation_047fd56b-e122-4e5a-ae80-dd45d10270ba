package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO;
import ai.conrain.aigc.platform.service.model.query.InvoiceTitleQuery;
import ai.conrain.aigc.platform.dal.example.InvoiceTitleExample;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * InvoiceTitleConverter
 *
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
public class InvoiceTitleConverter {

    /**
     * DO -> VO
     */
    public static InvoiceTitleVO do2VO(InvoiceTitleDO from) {
        InvoiceTitleVO to = new InvoiceTitleVO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static InvoiceTitleDO vo2DO(InvoiceTitleVO from) {
        InvoiceTitleDO to = new InvoiceTitleDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static InvoiceTitleQuery do2Query(InvoiceTitleDO from) {
        InvoiceTitleQuery to = new InvoiceTitleQuery();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static InvoiceTitleDO query2DO(InvoiceTitleQuery from) {
        InvoiceTitleDO to = new InvoiceTitleDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static InvoiceTitleExample query2Example(InvoiceTitleQuery from) {
        InvoiceTitleExample to = new InvoiceTitleExample();
        InvoiceTitleExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserId())) {
            c.andMasterUserIdEqualTo(from.getMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserId())) {
            c.andOperatorUserIdEqualTo(from.getOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceType())) {
            c.andInvoiceTypeEqualTo(from.getInvoiceType());
        }
        if (!ObjectUtils.isEmpty(from.getSubjectType())) {
            c.andSubjectTypeEqualTo(from.getSubjectType());
        }
        if (!ObjectUtils.isEmpty(from.getSubjectName())) {
            c.andSubjectNameEqualTo(from.getSubjectName());
        }
        if (!ObjectUtils.isEmpty(from.getCreditCode())) {
            c.andCreditCodeEqualTo(from.getCreditCode());
        }
        if (!ObjectUtils.isEmpty(from.getBusinessAddress())) {
            c.andBusinessAddressEqualTo(from.getBusinessAddress());
        }
        if (!ObjectUtils.isEmpty(from.getBusinessPhone())) {
            c.andBusinessPhoneEqualTo(from.getBusinessPhone());
        }
        if (!ObjectUtils.isEmpty(from.getBankName())) {
            c.andBankNameEqualTo(from.getBankName());
        }
        if (!ObjectUtils.isEmpty(from.getBankAccount())) {
            c.andBankAccountEqualTo(from.getBankAccount());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (InvoiceTitleExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<InvoiceTitleVO> doList2VOList(List<InvoiceTitleDO> list) {
        return CommonUtil.listConverter(list, InvoiceTitleConverter::do2VO);
    }
}
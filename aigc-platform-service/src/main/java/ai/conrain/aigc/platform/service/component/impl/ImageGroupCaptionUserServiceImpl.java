package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionUserDO;
import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionUserExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupCaptionUserConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupCaptionUserDAO;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;

/**   
 * ImageGroupCaptionUserService实现
 *
 * <AUTHOR>
 * @version ImageGroupCaptionUserService.java v 0.1 2025-07-30 08:19:30
 */
@Slf4j
@Service
public class ImageGroupCaptionUserServiceImpl implements ImageGroupCaptionUserService {

	/** DAO */
	@Autowired
	private ImageGroupCaptionUserDAO imageGroupCaptionUserDAO;

	@Override
	public ImageGroupCaptionUserVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageGroupCaptionUserDO data = imageGroupCaptionUserDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return ImageGroupCaptionUserConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageGroupCaptionUserDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageGroupCaptionUser失败");
	}

	@Override
	public ImageGroupCaptionUserVO insert(ImageGroupCaptionUserVO imageGroupCaptionUser) {
		AssertUtil.assertNotNull(imageGroupCaptionUser, ResultCode.PARAM_INVALID, "imageGroupCaptionUser is null");
		AssertUtil.assertTrue(imageGroupCaptionUser.getId() == null, ResultCode.PARAM_INVALID, "imageGroupCaptionUser.id is present");

		//创建时间、修改时间兜底
		if (imageGroupCaptionUser.getCreateTime() == null) {
			imageGroupCaptionUser.setCreateTime(new Date());
		}

		if (imageGroupCaptionUser.getModifyTime() == null) {
			imageGroupCaptionUser.setModifyTime(new Date());
		}

		ImageGroupCaptionUserDO data = ImageGroupCaptionUserConverter.vo2DO(imageGroupCaptionUser);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = imageGroupCaptionUserDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageGroupCaptionUser失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageGroupCaptionUser返回id为空");
		imageGroupCaptionUser.setId(data.getId());
		return imageGroupCaptionUser;
	}

    @Override
    public ImageGroupCaptionUserVO save(ImageGroupCaptionUserVO imageGroupCaptionUser) {
        ImageGroupCaptionUserQuery imageCaptionUserQuery = new ImageGroupCaptionUserQuery();
        imageCaptionUserQuery.setImageGroupId(imageGroupCaptionUser.getImageGroupId());
        imageCaptionUserQuery.setUserId(imageGroupCaptionUser.getUserId());
        List<ImageGroupCaptionUserVO> existingCaptions = queryImageGroupCaptionUserList(imageCaptionUserQuery);
        if (CollectionUtils.isEmpty(existingCaptions)) {
            return insert(imageGroupCaptionUser);
        } else {
            imageGroupCaptionUser.setId(existingCaptions.get(0).getId());
            updateByIdSelective(imageGroupCaptionUser);
            return imageGroupCaptionUser;
        }
    }

    @Override
	public void updateByIdSelective(ImageGroupCaptionUserVO imageGroupCaptionUser) {
		AssertUtil.assertNotNull(imageGroupCaptionUser, ResultCode.PARAM_INVALID, "imageGroupCaptionUser is null");
    	AssertUtil.assertTrue(imageGroupCaptionUser.getId() != null, ResultCode.PARAM_INVALID, "imageGroupCaptionUser.id is null");

		//修改时间必须更新
		imageGroupCaptionUser.setModifyTime(new Date());
		ImageGroupCaptionUserDO data = ImageGroupCaptionUserConverter.vo2DO(imageGroupCaptionUser);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = imageGroupCaptionUserDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageGroupCaptionUser失败，影响行数:" + n);
	}

	@Override
	public List<ImageGroupCaptionUserVO> queryImageGroupCaptionUserList(ImageGroupCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupCaptionUserExample example = ImageGroupCaptionUserConverter.query2Example(query);

		List<ImageGroupCaptionUserDO> list = imageGroupCaptionUserDAO.selectByExample(example);
		return ImageGroupCaptionUserConverter.doList2VOList(list);
	}

	@Override
	public Long queryImageGroupCaptionUserCount(ImageGroupCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupCaptionUserExample example = ImageGroupCaptionUserConverter.query2Example(query);
		return imageGroupCaptionUserDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询图像组标注表用户标注数据，多人打标
	 */
	@Override
	public PageInfo<ImageGroupCaptionUserVO> queryImageGroupCaptionUserByPage(ImageGroupCaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ImageGroupCaptionUserVO> page = new PageInfo<>();

		ImageGroupCaptionUserExample example = ImageGroupCaptionUserConverter.query2Example(query);
		long totalCount = imageGroupCaptionUserDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ImageGroupCaptionUserDO> list = imageGroupCaptionUserDAO.selectByExample(example);
		page.setList(ImageGroupCaptionUserConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
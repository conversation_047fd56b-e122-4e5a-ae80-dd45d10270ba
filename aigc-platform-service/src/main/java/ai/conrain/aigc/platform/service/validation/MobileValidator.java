/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.validation;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.MOBILE_PATTERN;

/**
 * 手机号校验器
 *
 * <AUTHOR>
 * @version : MobileValidator.java, v 0.1 2023/9/7 14:10 renxiao.wu Exp $
 */
@Slf4j
public class MobileValidator implements ConstraintValidator<Mobile, String> {

    private boolean nullable = false;

    public void initialize(Mobile constraintAnnotation) {
        nullable = constraintAnnotation.nullable();
    }

    @Override
    public boolean isValid(String mobile, ConstraintValidatorContext context) {
        if (StringUtils.isBlank(mobile)) {
            //允许为空时，直接返回
            if (nullable) {
                return true;
            }
            log.warn("手机号码验证失败，mobile为空，mobile={}", mobile);
            return false;
        }
        boolean result = MOBILE_PATTERN.matcher(mobile).matches();
        if (!result) {
            log.warn("手机号码验证失败，mobile非法，mobile={}", mobile);
        }
        return result;
    }
}

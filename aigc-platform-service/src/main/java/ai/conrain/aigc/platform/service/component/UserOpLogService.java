package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.UserOpLogDO;
import ai.conrain.aigc.platform.service.model.query.UserOpLogQuery;
import ai.conrain.aigc.platform.service.model.vo.UserOpLogVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 用户操作记录 Service定义
 *
 * <AUTHOR>
 * @version UserOpLogService.java v 0.1 2024-01-25 09:31:00
 */
public interface UserOpLogService {
	
	/**
	 * 查询用户操作记录对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	UserOpLogVO selectById(Integer id);

	/**
	 * 删除用户操作记录对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加用户操作记录对象
	 * @param userOpLog 对象参数
	 * @return 返回结果
	 */
	UserOpLogVO insert(UserOpLogVO userOpLog);

	/**
	 * 修改用户操作记录对象
	 * @param userOpLog 对象参数
	 */
	void updateByIdSelective(UserOpLogVO userOpLog);

	/**
	 * 带条件批量查询用户操作记录列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<UserOpLogVO> queryUserOpLogList(UserOpLogQuery query);

	/**
	 * 带条件查询用户操作记录数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryUserOpLogCount(UserOpLogQuery query);

	/**
	 * 带条件分页查询用户操作记录
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<UserOpLogVO> queryUserOpLogByPage(UserOpLogQuery query);
}
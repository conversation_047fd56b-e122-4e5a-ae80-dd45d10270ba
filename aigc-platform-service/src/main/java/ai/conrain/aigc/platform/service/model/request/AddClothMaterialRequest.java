package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class AddClothMaterialRequest implements AddMaterialRequest, Serializable {
    private static final long serialVersionUID = -3016832721948728736L;

    @NotBlank
    private String name;

    @JsonIgnore
    private String materialType = MaterialType.cloth.name();

    /**
     * @see ai.conrain.aigc.platform.service.enums.ClothTypeEnum
     */
    @NotBlank
    private String clothType;

    // 年龄区段
    @NotBlank
    private String ageRange;

    //是否需要背面照
    private boolean backPhotoNeeded;

    //男装｜女装｜童装
    private String clothStyleType = CommonConstants.female;

    @NotNull
    private ClothMaterialDetail materialDetail;

    private Integer colorNumber = 1;

    private List<String> colorDescriptions;

    private String colorDescription;

    /** 服装主体id */
    //代拍（额外多收费10元）
    private Integer principalId;

    //是否代熨烫（额外多收费10元）
    private boolean ironingCloth = false;

    //是否多色上传（额外多收费10元）
    private boolean multiColors = false;

    //搭配偏好
    private String matchPrefer = "";

    //自动出图参数，前台用户选择的参数
    private boolean autoGen = false;
    private AutoGenImgParam autoGenImgParam;

    // 是否裁剪放大，用于抠图流程的实验参数，Y/N，缺省为N
    private String cut4ScaleUp;

    /** 抠图类型 */
    private String cutoutType;

    /** 保留搭配物品，Y/N */
    private String reservedItems;

    /** 打标prompt类型 */
    private String labelType = LabelTypeEnum.DEFAULT.getCode();

    // 训练参数
    private TrainParam trainParam;

    /** 展示图片 */
    private String showImage;

    /** 业务标记 */
    private String bizTag;

    /** 批次id */
    private String batchId;

    /** 主子类型 */
    @JsonIgnore
    private MainTypeEnum mainType;

    /** 主模型id */
    @JsonIgnore
    private Integer mainId;

    /** 用途类型 */
    private String usageType;

    /** 用途备注 */
    private String usageMemo;

    /** 背景多色打标 */
    private String bgMultiColor;

    @Override
    public String getMaterialSubType() {
        return clothType;
    }

    //服装的男女款式描述（man/woman/child，默认值是woman，解决历史数据为空的情况）
    @Override
    public String getClothStyleType4TrainParam() {
        if (StringUtils.equalsIgnoreCase(clothStyleType, CommonConstants.child)) {
            return CommonConstants.child;
        } else if (StringUtils.equalsIgnoreCase(clothStyleType, CommonConstants.male)) {
            return CommonConstants.man;
        } else if (StringUtils.equalsIgnoreCase(clothStyleType, CommonConstants.female)) {
            return CommonConstants.woman;
        } else if (StringUtils.equalsIgnoreCase(clothStyleType, CommonConstants.unisex)) {
            return CommonConstants.unisex;
        } else {
            throw new RuntimeException("clothStyleType is invalid:" + clothStyleType);
        }
    }

    @Data
    public static class TrainParam {
        // 训练分辨率
        private String resolution;
        // 学习内容
        private String contentOrStyle;
        // Rank
        private String rank;
        // Alpha
        private String alpha;
        // 训练步数
        private String maxTrainStep;
        // 学习率
        private String lr;
        // Dropout
        private String dropout;
    }

    public boolean applyAutoGen() {
        if (autoGen && autoGenImgParam != null) {
            return CollectionUtils.isNotEmpty(autoGenImgParam.getAutoGenFaceIds()) && CollectionUtils.isNotEmpty(
                autoGenImgParam.getAutoGenSceneIds()) && CollectionUtils.isNotEmpty(
                autoGenImgParam.getAutoGenImgProportions()) && autoGenImgParam.getAutoGenTotalImgNum() != null
                   && autoGenImgParam.getAutoGenTotalImgNum() > 0;
        }

        return false;
    }
}

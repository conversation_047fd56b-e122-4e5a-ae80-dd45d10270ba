/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component;

/**
 * 短信服务
 *
 * <AUTHOR>
 * @version : SmsService.java, v 0.1 2023/9/7 00:20 renxiao.wu Exp $
 */
public interface SmsService {
    /**
     * 发送验证码
     *
     * @param mobile 手机号
     * @return true成功, false失败
     */
    boolean sendCaptcha(String mobile);

    /**
     * 验证短信验证码
     *
     * @param mobile  手机号
     * @param captcha 验证码
     * @return true：验证成功，false：验证失败
     */
    boolean verifyCaptcha(String mobile, String captcha);
}

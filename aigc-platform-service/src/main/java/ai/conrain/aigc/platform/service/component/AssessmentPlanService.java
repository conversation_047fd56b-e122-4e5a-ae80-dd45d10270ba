package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.AssessStatusEnum;
import ai.conrain.aigc.platform.service.enums.SalesAssessTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorEnhancedVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.query.AssessmentPlanQuery;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 销售考核计划 Service定义
 *
 * <AUTHOR>
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
public interface AssessmentPlanService {
	
	/**
	 * 查询销售考核计划对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	AssessmentPlanVO selectById(Integer id);

	/**
	 * 删除销售考核计划对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加销售考核计划对象
	 * @param assessmentPlan 对象参数
	 * @return 返回结果
	 */
	AssessmentPlanVO insert(AssessmentPlanVO assessmentPlan);

	/**
	 * 修改销售考核计划对象
	 * @param assessmentPlan 对象参数
	 */
	void updateByIdSelective(AssessmentPlanVO assessmentPlan);

	/**
	 * 带条件批量查询销售考核计划列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<AssessmentPlanVO> queryAssessmentPlanList(AssessmentPlanQuery query);

	/**
	 * 带条件查询销售考核计划数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryAssessmentPlanCount(AssessmentPlanQuery query);

	/**
	 * 插入或更新
	 * @param planVO 数据
	 * return 插入结果
	 */
	int insertOrUpdate(AssessmentPlanVO planVO);

	/**
	 * 带条件分页查询销售考核计划
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<AssessmentPlanVO> queryAssessmentPlanByPage(AssessmentPlanQuery query);

	/**
	 * 根据用户id查询销售考核计划
	 * @param principal 考核主体
	 * @return 结果
	 */
	AssessmentPlanVO queryLastAssessmentPlanExclusive(PrincipalModel principal, AssessStatusEnum... exceptStatus);

	/**
	 * 查询进行中的销售考核计划
	 * @param principal 考核主体
	 * @return 考核计划
	 */
	AssessmentPlanVO queryActiveAssessmentPlanExclusive(PrincipalModel principal);

	/**
	 * 编辑销售考核计划
	 * @param assessmentPlan 考核计划
	 */
	void modifyAssessmentPlan(@Valid AssessmentPlanVO assessmentPlan);

	/**
	 * 审核销售考核结果
	 */
	void reviewAssessmentPlan(Integer planId, SettleConfigModel newSettleConfig);

	/**
	 * 查询所有渠道当前的考核计划
	 * @param showTest 是否显示测试数据
	 * @return 所有渠道当前的考核计划
	 */
    List<DistributorEnhancedVO> queryAllDistributorAssessment(Boolean showTest);

	/**
	 * 查询渠道当前的考核计划
	 * @param principal 考核主体
	 * @return 渠道当前的考核计划
	 */
	DistributorEnhancedVO queryDistributorEnhancedVO(PrincipalModel principal);

	/**
	 * 初始化销售考核计划
	 *
	 * @param userVO   用户
	 * @param orgVO 组织
	 */

	void initAssessmentPlan(PrincipalModel principal, UserVO userVO, OrganizationVO orgVO);

	/**
	 * 获取考核主体
	 *
	 * @param userVO   用户
	 * @param orgVO 组织
	 * @return 考核主体
	 */
	PrincipalModel getPrincipal(UserVO userVO, OrganizationVO orgVO);

	/**
	 * 处理销售考核计划
	 */
	void processAssessment();

	AssessmentPlanVO create(PrincipalModel principal, AssessmentPlanVO oldPlan, SalesAssessTypeEnum assessType);

	AssessmentPlanVO init(AssessmentPlanVO originalPlan);

	AssessmentPlanVO waiting(AssessmentPlanVO originalPlan);

	AssessmentPlanVO active(AssessmentPlanVO originalPlan);

	AssessmentPlanVO passed(AssessmentPlanVO originalPlan);

	AssessmentPlanVO failed(AssessmentPlanVO originalPlan);

	AssessmentPlanVO reviewed(AssessmentPlanVO originalPlan);

	AssessmentPlanVO finished(AssessmentPlanVO originalPlan);

	AssessmentPlanVO cancelled(AssessmentPlanVO originalPlan);

	AssessmentPlanVO processStatusFlow(AssessmentPlanVO plan);
}
package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.DistributorCustomerDO;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.dal.example.DistributorCustomerExample;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * DistributorCustomerConverter
 *
 * @version DistributorCustomerService.java v 0.1 2024-07-15 04:11:37
 */
public class DistributorCustomerConverter {

    /**
     * DO -> VO
     */
    public static DistributorCustomerVO do2VO(DistributorCustomerDO from) {
        DistributorCustomerVO to = new DistributorCustomerVO();
        to.setId(from.getId());
        to.setCustomerMasterUserId(from.getCustomerMasterUserId());
        to.setDistributorCorpName(StringUtils.defaultIfBlank(from.getRelatedDistributorCorpName(), from.getDistributorCorpName()));
        to.setDistributorCorpOrgId(from.getDistributorCorpOrgId());
        to.setDistributorMasterUserId(from.getDistributorMasterUserId());
        to.setDistributorOperatorUserId(from.getDistributorOperatorUserId());
        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setCreatorId(from.getCreatorId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setCustomerMasterNick(from.getCustomerMasterNick());
        to.setCustomerMasterCorpName(from.getCustomerMasterCorpName());
        to.setCustomerMusePoint(from.getCustomerMusePoint());

        return to;
    }

    /**
     * VO -> DO
     */
    public static DistributorCustomerDO vo2DO(DistributorCustomerVO from) {
        DistributorCustomerDO to = new DistributorCustomerDO();
        to.setId(from.getId());
        to.setCustomerMasterUserId(from.getCustomerMasterUserId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setDistributorCorpOrgId(from.getDistributorCorpOrgId());
        to.setDistributorMasterUserId(from.getDistributorMasterUserId());
        to.setDistributorOperatorUserId(from.getDistributorOperatorUserId());
        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setCreatorId(from.getCreatorId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCustomerMusePoint(from.getCustomerMusePoint());

        return to;
    }

    /**
     * DO -> Query
     */
    public static DistributorCustomerQuery do2Query(DistributorCustomerDO from) {
        DistributorCustomerQuery to = new DistributorCustomerQuery();
        to.setId(from.getId());
        to.setCustomerMasterUserId(from.getCustomerMasterUserId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setDistributorCorpOrgId(from.getDistributorCorpOrgId());
        to.setDistributorMasterUserId(from.getDistributorMasterUserId());
        to.setDistributorOperatorUserId(from.getDistributorOperatorUserId());
        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setCreatorId(from.getCreatorId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static DistributorCustomerDO query2DO(DistributorCustomerQuery from) {
        DistributorCustomerDO to = new DistributorCustomerDO();
        to.setId(from.getId());
        to.setCustomerMasterUserId(from.getCustomerMasterUserId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setDistributorCorpOrgId(from.getDistributorCorpOrgId());
        to.setDistributorMasterUserId(from.getDistributorMasterUserId());
        to.setDistributorOperatorUserId(from.getDistributorOperatorUserId());
        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setCreatorId(from.getCreatorId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static DistributorCustomerExample query2Example(DistributorCustomerQuery from) {
        DistributorCustomerExample to = new DistributorCustomerExample();
        DistributorCustomerExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerMasterUserId())) {
            c.andCustomerMasterUserIdEqualTo(from.getCustomerMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpName())) {
            c.andDistributorCorpNameEqualTo(from.getDistributorCorpName());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpOrgId())) {
            c.andDistributorCorpOrgIdEqualTo(from.getDistributorCorpOrgId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorMasterUserId())) {
            c.andDistributorMasterUserIdEqualTo(from.getDistributorMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorOperatorUserId())) {
            c.andDistributorOperatorUserIdEqualTo(from.getDistributorOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorSalesUserId())) {
            c.andDistributorSalesUserIdEqualTo(from.getDistributorSalesUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorId())) {
            c.andCreatorIdEqualTo(from.getCreatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorIds())) {
            c.andCreatorIdIn(from.getCreatorIds());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorOperatorUserIds())) {
            c.andDistributorSalesUserIdIn(from.getDistributorOperatorUserIds());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorSalesUserIds())) {
            c.andDistributorSalesUserIdIn(from.getDistributorSalesUserIds());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerMasterUserIds())) {
            c.andCustomerMasterUserIdIn(from.getCustomerMasterUserIds());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getCustomerLike())) {
            c.andCustomerLike("'%" + from.getCustomerLike() + "%'");
        }

        //逻辑删除过滤
        for (DistributorCustomerExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<DistributorCustomerVO> doList2VOList(List<DistributorCustomerDO> list) {
        return CommonUtil.listConverter(list, DistributorCustomerConverter::do2VO);
    }
}
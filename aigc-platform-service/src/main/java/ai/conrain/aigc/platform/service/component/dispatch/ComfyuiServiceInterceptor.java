package ai.conrain.aigc.platform.service.component.dispatch;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class ComfyuiServiceInterceptor {
    private final static List<String> TARGET_METHODS = Arrays.asList("prompt", "queryStatusByHistory",
        "queryStatusByQueue");
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private ServerService serverService;

    //*Service类才会被拦截
    @Around("execution(public * ai.conrain.aigc.platform.integration.ai.ComfyUIService.*(..))")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        try {
            return proceedingJoinPoint.proceed();
        } catch (Exception e) {

            Signature signature = proceedingJoinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature)signature;

            Method method = methodSignature.getMethod();
            String methodName = method.getName();
            Class<?>[] parameterTypes = method.getParameterTypes();

            if (isTargetMethod(methodName, parameterTypes)) {
                processFail(proceedingJoinPoint, e);
            }

            throw e;
        }
    }

    /**
     * 处理异常场景
     *
     * @param proceedingJoinPoint 拦截点
     * @param e                   异常
     */
    private void processFail(ProceedingJoinPoint proceedingJoinPoint, Exception e) {
        Object[] args = proceedingJoinPoint.getArgs();
        String url = (String)args[1];

        ServerVO server = serverService.parseByUrl(url);
        if (server == null) {
            log.error("未找到对应的服务url,url={}", url);
            return;
        }

        Integer serverId = server.getId();
        ServerCache cache = serverHelper.getFromTair(serverId);
        if (cache == null) {
            cache = new ServerCache();
        }
        cache.fail();
        log.warn("调用comfyui异常,更新服务缓存数据，serverId={},当前失败次数{},status={}", serverId,
            cache.getFailTimes(), cache.getStatus());
        serverHelper.putToTair(serverId, cache);
    }

    private boolean isTargetMethod(String methodName, Class<?>[] parameterTypes) {
        if (!CollectionUtils.containsAny(TARGET_METHODS, methodName)) {
            return false;
        }

        return parameterTypes.length == 2;
    }
}
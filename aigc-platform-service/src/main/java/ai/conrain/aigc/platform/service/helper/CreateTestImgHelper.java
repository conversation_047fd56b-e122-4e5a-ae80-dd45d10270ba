/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.event.CreateTestImgEvent;
import ai.conrain.aigc.platform.service.model.event.CreateTestImgEvent.CreateTestRequest;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_UNLIMITED;

/**
 * 创建测试图片帮助类
 *
 * <AUTHOR>
 * @version : CreateTestImgHelper.java, v 0.1 2025/4/17 00:38 renxiao.wu Exp $
 */
@Slf4j
@Component
public class CreateTestImgHelper {
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private UserService userService;

    /**
     * 根据消息事件批量创建测试图片
     *
     * @param event 消息事件
     */
    public void batchCreateByEvent(CreateTestImgEvent event) throws IOException {
        if (event.getUserId() != null) {
            UserVO user = userService.selectById(event.getUserId());
            OperationContext operationContext = new OperationContext();
            operationContext.setLoginUser(user);
            OperationContextHolder.setContext(operationContext);
        } else {
            OperationContextHolder.setContext(CommonUtil.mockAutoCreativeContext());
        }

        CreativeElementVO faceRoot = creativeElementService.queryRootKey(ElementConfigKeyEnum.FACE.name());
        CreativeElementVO sceneRoot = creativeElementService.queryRootKey(ElementConfigKeyEnum.SCENE.name());

        createImage(event, faceRoot, sceneRoot);
    }

    /**
     * 生成测试图
     */
    private void createImage(CreateTestImgEvent event, CreativeElementVO faceRoot, CreativeElementVO sceneRoot)
        throws IOException {

        if (CollectionUtils.isNotEmpty(event.getCreateTestRequestPairs())) {
            for (CreateTestRequest request : event.getCreateTestRequestPairs()) {
                createOneBatch(event, faceRoot, sceneRoot, request.getFaceId(), request.getSceneId(),
                    request.getImgProportions(), request.getNum(), request.getCameraAngle(), request.getBizTag(),
                    request.getColorIndex());
            }
            return;
        }

        int testBatchNum = event.getTestNum() != null ? event.getTestNum() : 1;

        List<String> testImgProportions = event.getTestImgProportions();
        if (CollectionUtils.isEmpty(testImgProportions)) {
            testImgProportions = Collections.singletonList(ProportionTypeEnum.THREE_FOUR.getCode());
        }
        for (String p : testImgProportions) {
            AssertUtil.assertNotNull(ProportionTypeEnum.getByCode(p), ResultCode.PARAM_INVALID,
                "测试图片比例参数错误:" + p);
        }

        List<Integer> testFaces = event.getTestFaces();
        List<Integer> testScenes = event.getTestScenes();
        Integer imgNumPerBatch = event.getImgNumPerGroup() != null ? event.getImgNumPerGroup() : 20;
        String createMode = StringUtils.defaultIfBlank(event.getTestImgMode(), CommonConstants.TEST_IMG_MODE_RANDOM);
        List<List<String>> cameraAnglePairs = event.getCameraAnglePairs();
        //默认正面全身
        if (CollectionUtils.isEmpty(cameraAnglePairs)) {
            cameraAnglePairs = Collections.singletonList(
                Arrays.asList(CameraAngleEnum.WHOLE_BODY.getCode(), CameraAngleEnum.FRONT_VIEW.getCode()));
        }

        //随机生成，用于服务管理后台页面确认训练
        if (StringUtils.equals(createMode, CommonConstants.TEST_IMG_MODE_RANDOM)) {
            for (int i = 0; i < testBatchNum; i++) {
                Collections.shuffle(testFaces, new Random());
                Collections.shuffle(testScenes, new Random());
                Collections.shuffle(testImgProportions, new Random());

                //随机一个模特，一个场景，一个分辨率（后台其实只能选一个分辨率）
                Integer face = testFaces.get(0);
                Integer scene = testScenes.get(0);
                String proportion = testImgProportions.get(0);

                createOneBatch(event, faceRoot, sceneRoot, face, scene, proportion, imgNumPerBatch,
                    cameraAnglePairs.get(0));
            }

            //笛卡尔积生成
        } else if (StringUtils.equals(createMode, CommonConstants.TEST_IMG_MODE_CARTESIAN_PRO)) {
            for (Integer face : testFaces) {
                for (Integer scene : testScenes) {
                    for (String proportion : testImgProportions) {
                        if (CollectionUtils.size(cameraAnglePairs) > 1) {
                            for (List<String> cameraAnglePair : cameraAnglePairs) {
                                createOneBatch(event, faceRoot, sceneRoot, face, scene, proportion, imgNumPerBatch,
                                    cameraAnglePair);
                            }
                        } else {
                            for (int i = 0; i < testBatchNum; i++) {
                                createOneBatch(event, faceRoot, sceneRoot, face, scene, proportion, imgNumPerBatch,
                                    cameraAnglePairs.get(0));
                            }
                        }
                    }
                }
            }

        } else {
            throw new RuntimeException("不支持的测试图生成模式:" + createMode);
        }

    }

    private void createOneBatch(CreateTestImgEvent event, CreativeElementVO faceRoot, CreativeElementVO sceneRoot,
                                Integer face, Integer scene, String proportion, Integer imgNum,
                                List<String> cameraAngles) throws IOException {
        createOneBatch(event, faceRoot, sceneRoot, face, scene, proportion, imgNum, cameraAngles, null, null);
    }

    private void createOneBatch(CreateTestImgEvent event, CreativeElementVO faceRoot, CreativeElementVO sceneRoot,
                                Integer face, Integer scene, String proportion, Integer imgNum,
                                List<String> cameraAngles, String bizTag, Integer colorIndex) throws IOException {
        Map<Integer, List<Integer>> configs = new HashMap<>();
        if (faceRoot != null) {
            configs.put(faceRoot.getId(), Collections.singletonList(face));
        }

        if (sceneRoot != null) {
            configs.put(sceneRoot.getId(), Collections.singletonList(scene));
        }

        AddCreativeRequest req = new AddCreativeRequest();
        req.setLoraId(event.getModelId());
        req.setImageNum(imgNum);
        req.setProportion(proportion);
        req.setConfigs(configs);
        if (StringUtils.isBlank(bizTag)) {
            req.setBizTag(BizConstants.SYSTEM_IMAGES);
        } else {
            req.setBizTag(bizTag);
        }
        if (colorIndex != null) {
            req.setColorIndex(colorIndex);
        }
        req.setTransClothCollocation(event.getTestClothCollocation());
        req.setWithoutDeduction(event.isWithoutDeduction());
        req.setBodyType(CollectionUtils.isNotEmpty(cameraAngles) && cameraAngles.contains(KEY_UNLIMITED) ? KEY_UNLIMITED
            : CameraAngleEnum.getBodyPositionByStr(cameraAngles).getCode());
        req.setPosition(CameraAngleEnum.getOrientationByStr(cameraAngles).getCode());
        req.setExpression(req.getExpression());
        CreativeBatchVO batch = creativeBatchService.create(CreativeTypeEnum.CREATE_IMAGE, req);
        log.info("【测试图生成事件】随机生成图片批次成功: {}", batch.getId());
    }

}

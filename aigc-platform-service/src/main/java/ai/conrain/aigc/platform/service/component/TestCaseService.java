package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 测试用例集 Service定义
 *
 * <AUTHOR>
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
public interface TestCaseService {

    /**
     * 查询测试用例集对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestCaseVO selectById(Integer id);

    /**
     * 删除测试用例集对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加测试用例集对象
     *
     * @param testCase 对象参数
     * @return 返回结果
     */
    TestCaseVO insert(TestCaseVO testCase);

    /**
     * 修改测试用例集对象
     *
     * @param testCase 对象参数
     */
    void updateByIdSelective(TestCaseVO testCase);

    /**
     * 带条件批量查询测试用例集列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestCaseVO> queryTestCaseList(TestCaseQuery query);

    /**
     * 带条件查询测试用例集数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestCaseCount(TestCaseQuery query);

    /**
     * 带条件分页查询测试用例集
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestCaseVO> queryTestCaseByPage(TestCaseQuery query);

    /**
     * 从Excel导入测试用例
     *
     * @param name 名称
     * @param type 类型
     * @param file 文件
     * @return 测试用例对象
     */
    TestCaseVO importFromExcel(String name, String type, MultipartFile file) throws IOException;
}
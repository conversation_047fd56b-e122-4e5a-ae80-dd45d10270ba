package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.CommonTaskDO;
import ai.conrain.aigc.platform.dal.example.CommonTaskExample;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * CommonTaskConverter
 *
 * @version CommonTaskService.java v 0.1 2024-12-31 09:04:22
 */
public class CommonTaskConverter {

    /**
     * DO -> VO
     */
    public static CommonTaskVO do2VO(CommonTaskDO from) {
        CommonTaskVO to = new CommonTaskVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setOutTaskPlatform(from.getOutTaskPlatform());
        to.setOutTaskId(from.getOutTaskId());
        to.setOutTaskStatus(from.getOutTaskStatus());
        to.setReqBizParams(from.getReqBizParams());
        to.setRelatedBizType(from.getRelatedBizType());
        to.setRelatedBizId(from.getRelatedBizId());
        if (CommonUtil.isValidJson(from.getExtInfo())) {
            to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        }

        to.setTaskStartTime(from.getTaskStartTime());
        to.setTaskEndTime(from.getTaskEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCompleteRequest(from.getCompleteRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CommonTaskDO vo2DO(CommonTaskVO from) {
        CommonTaskDO to = new CommonTaskDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setOutTaskPlatform(from.getOutTaskPlatform());
        to.setOutTaskId(from.getOutTaskId());
        to.setOutTaskStatus(from.getOutTaskStatus());
        to.setReqBizParams(from.getReqBizParams());
        to.setRelatedBizType(from.getRelatedBizType());
        to.setRelatedBizId(from.getRelatedBizId());
        if (from.getExtInfo() != null) {
            to.setExtInfo(from.getExtInfo().toJSONString());
        }
        to.setTaskStartTime(from.getTaskStartTime());
        to.setTaskEndTime(from.getTaskEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCompleteRequest(from.getCompleteRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * DO -> Query
     */
    public static CommonTaskQuery do2Query(CommonTaskDO from) {
        CommonTaskQuery to = new CommonTaskQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setOutTaskPlatform(from.getOutTaskPlatform());
        to.setOutTaskId(from.getOutTaskId());
        to.setOutTaskStatus(from.getOutTaskStatus());
        to.setReqBizParams(from.getReqBizParams());
        to.setRelatedBizType(from.getRelatedBizType());
        to.setRelatedBizId(from.getRelatedBizId());
        to.setExtInfo(from.getExtInfo());
        to.setTaskStartTime(from.getTaskStartTime());
        to.setTaskEndTime(from.getTaskEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCompleteRequest(from.getCompleteRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * Query -> DO
     */
    public static CommonTaskDO query2DO(CommonTaskQuery from) {
        CommonTaskDO to = new CommonTaskDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setOutTaskPlatform(from.getOutTaskPlatform());
        to.setOutTaskId(from.getOutTaskId());
        to.setOutTaskStatus(from.getOutTaskStatus());
        to.setReqBizParams(from.getReqBizParams());
        to.setRelatedBizType(from.getRelatedBizType());
        to.setRelatedBizId(from.getRelatedBizId());
        to.setExtInfo(from.getExtInfo());
        to.setTaskStartTime(from.getTaskStartTime());
        to.setTaskEndTime(from.getTaskEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCompleteRequest(from.getCompleteRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }


    /**
     * Query -> Example
     */
    public static CommonTaskExample query2Example(CommonTaskQuery from) {
        CommonTaskExample to = new CommonTaskExample();
        CommonTaskExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIdList())) {
            c.andIdIn(from.getIdList());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getTaskType())) {
            c.andTaskTypeEqualTo(from.getTaskType());
        }
        if (!ObjectUtils.isEmpty(from.getTaskTypeList())) {
            c.andTaskTypeIn(from.getTaskTypeList());
        }
        if (!ObjectUtils.isEmpty(from.getTaskStatus())) {
            c.andTaskStatusEqualTo(from.getTaskStatus());
        }
        if (!ObjectUtils.isEmpty(from.getTaskStatusList())) {
            c.andTaskStatusIn(from.getTaskStatusList());
        }
        if (!ObjectUtils.isEmpty(from.getOutTaskPlatform())) {
            c.andOutTaskPlatformEqualTo(from.getOutTaskPlatform());
        }
        if (!ObjectUtils.isEmpty(from.getOutTaskId())) {
            c.andOutTaskIdEqualTo(from.getOutTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getOutTaskStatus())) {
            c.andOutTaskStatusEqualTo(from.getOutTaskStatus());
        }
        if (!ObjectUtils.isEmpty(from.getReqBizParams())) {
            c.andReqBizParamsEqualTo(from.getReqBizParams());
        }
        if (!ObjectUtils.isEmpty(from.getRelatedBizType())) {
            c.andRelatedBizTypeEqualTo(from.getRelatedBizType());
        }
        if (!ObjectUtils.isEmpty(from.getRelatedBizId())) {
            c.andRelatedBizIdEqualTo(from.getRelatedBizId());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getTaskStartTime())) {
            c.andTaskStartTimeEqualTo(from.getTaskStartTime());
        }
        if (!ObjectUtils.isEmpty(from.getTaskEndTime())) {
            c.andTaskEndTimeEqualTo(from.getTaskEndTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (CommonTaskExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CommonTaskVO> doList2VOList(List<CommonTaskDO> list) {
        return CommonUtil.listConverter(list, CommonTaskConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import ai.conrain.aigc.platform.service.model.query.ImageCaseSyncRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
public interface ImageCaseSyncRecordService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageCaseSyncRecordVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param imageCaseSyncRecord 对象参数
	 * @return 返回结果
	 */
	ImageCaseSyncRecordVO insert(ImageCaseSyncRecordVO imageCaseSyncRecord);

	/**
	 * 修改对象
	 * @param imageCaseSyncRecord 对象参数
	 */
	void updateById(ImageCaseSyncRecordVO imageCaseSyncRecord);

	/**
	 * 全量查询
	 * return 结果
	 */
	List<ImageCaseSyncRecordVO> findAll();
}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.enums.CaptionUserRoleEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionUserDO;
import ai.conrain.aigc.platform.dal.example.CaptionUserExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.converter.CaptionUserConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.CaptionUserDAO;
import ai.conrain.aigc.platform.service.component.CaptionUserService;

/**   
 * CaptionUserService实现
 *
 * <AUTHOR>
 * @version CaptionUserService.java v 0.1 2025-08-12 12:55:30
 */
@Slf4j
@Service
public class CaptionUserServiceImpl implements CaptionUserService {

	/** DAO */
	@Autowired
	private CaptionUserDAO captionUserDAO;

	/** BCrypt密码编码器 */
	private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

	@Override
	public CaptionUserVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		CaptionUserDO data = captionUserDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return CaptionUserConverter.do2VO(data);
	}

    @Override
	public CaptionUserVO selectByUsername(String username) {
		AssertUtil.assertNotNull(username, ResultCode.PARAM_INVALID, "id is null");

        CaptionUserQuery captionUserQuery = new CaptionUserQuery();
        captionUserQuery.setUsername(username);
        List<CaptionUserVO> captionUserVOS = queryCaptionUserList(captionUserQuery);
        if (!captionUserVOS.isEmpty()) {
            return captionUserVOS.getFirst();
        }
        return null;
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = captionUserDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CaptionUser失败");
	}

    @Override
    public CaptionUserVO create(CaptionUserVO captionUser) {
        AssertUtil.assertNotNull(captionUser, ResultCode.PARAM_INVALID, "captionUser is null");
        AssertUtil.assertTrue(captionUser.getId() == null, ResultCode.PARAM_INVALID, "captionUser.id is present");
        CaptionUserQuery captionUserQuery = new CaptionUserQuery();
        captionUserQuery.setUsername(captionUser.getUsername());
        List<CaptionUserVO> captionUserVOS = queryCaptionUserList(captionUserQuery);
        if (!captionUserVOS.isEmpty()) {
            throw new BizException(ResultCode.USERNAME_ALREADY_EXISTS);
        }
        captionUser.setPasswordHash(passwordEncoder.encode(captionUser.getPassword()));
        return insert(captionUser);
    }

    @Override
	public CaptionUserVO insert(CaptionUserVO captionUser) {
		AssertUtil.assertNotNull(captionUser, ResultCode.PARAM_INVALID, "captionUser is null");
		AssertUtil.assertTrue(captionUser.getId() == null, ResultCode.PARAM_INVALID, "captionUser.id is present");

		//创建时间、修改时间兜底
		if (captionUser.getCreateTime() == null) {
			captionUser.setCreateTime(new Date());
		}

		if (captionUser.getModifyTime() == null) {
			captionUser.setModifyTime(new Date());
		}

		CaptionUserDO data = CaptionUserConverter.vo2DO(captionUser);
		Integer n = captionUserDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CaptionUser失败");
		AssertUtil.assertNotNull(data.getId(), "新建CaptionUser返回id为空");
		captionUser.setId(data.getId());
		return captionUser;
	}


	@Override
	public void updateByIdSelective(CaptionUserVO captionUser) {
		AssertUtil.assertNotNull(captionUser, ResultCode.PARAM_INVALID, "captionUser is null");
    	AssertUtil.assertTrue(captionUser.getId() != null, ResultCode.PARAM_INVALID, "captionUser.id is null");

		//修改时间必须更新
		captionUser.setModifyTime(new Date());
		CaptionUserDO data = CaptionUserConverter.vo2DO(captionUser);
		int n = captionUserDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CaptionUser失败，影响行数:" + n);
	}

	@Override
	public List<CaptionUserVO> queryCaptionUserList(CaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CaptionUserExample example = CaptionUserConverter.query2Example(query);

		List<CaptionUserDO> list = captionUserDAO.selectByExample(example);
		return CaptionUserConverter.doList2VOList(list);
	}

	@Override
	public Long queryCaptionUserCount(CaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CaptionUserExample example = CaptionUserConverter.query2Example(query);
		return captionUserDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询用户基础信息
	 */
	@Override
	public PageInfo<CaptionUserVO> queryCaptionUserByPage(CaptionUserQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<CaptionUserVO> page = new PageInfo<>();

		CaptionUserExample example = CaptionUserConverter.query2Example(query);
		long totalCount = captionUserDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<CaptionUserDO> list = captionUserDAO.selectByExample(example);
		page.setList(CaptionUserConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    public CaptionUserVO getOrCreateByUsername(String username) {
        CaptionUserQuery captionUserQuery = new CaptionUserQuery();
        captionUserQuery.setUsername(username);
        List<CaptionUserVO> captionUserVOS = queryCaptionUserList(captionUserQuery);
        if (!captionUserVOS.isEmpty()) {
            return captionUserVOS.getFirst();
        }

        // 创建新用户，使用 BCrypt 加密默认密码 'a1111111'
        String defaultPassword = "a1111111";
        String passwordHash = passwordEncoder.encode(defaultPassword);

        CaptionUserVO captionUserVO = new CaptionUserVO();
        captionUserVO.setUsername(username);
        captionUserVO.setNickname(username);
        captionUserVO.setRole(CaptionUserRoleEnum.LLM.getCode());
        captionUserVO.setPasswordHash(passwordHash);

        // 保存新用户到数据库
        return insert(captionUserVO);
    }

}
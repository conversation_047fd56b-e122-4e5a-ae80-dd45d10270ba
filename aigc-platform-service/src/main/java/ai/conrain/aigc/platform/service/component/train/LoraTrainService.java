/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.train;

import ai.conrain.aigc.platform.service.model.request.AddMaterialRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;

/**
 * lora训练服务
 *
 * <AUTHOR>
 * @version : LoraTrainService.java, v 0.1 2025/2/23 17:49 renxiao.wu Exp $
 */
public interface LoraTrainService<T extends AddMaterialRequest> {
    /**
     * 创建Lora训练任务
     *
     * @param request 请求
     * @return 模型
     */
    MaterialModelVO create(T request);
}

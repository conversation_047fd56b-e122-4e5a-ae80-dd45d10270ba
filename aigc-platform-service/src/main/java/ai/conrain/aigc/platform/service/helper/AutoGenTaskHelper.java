package ai.conrain.aigc.platform.service.helper;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.AutoGenImgRetDetail;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_DELIVERY_TIME;
import static ai.conrain.aigc.platform.service.helper.DingTalkNoticeWTCHelper.DingNoticeBizTypeEnum.AUTO_GEN_TASK_NONE_SCENE;

@Slf4j
@Service
public class AutoGenTaskHelper {

    @Autowired
    private CommonTaskService commonTaskService;

    @Autowired
    private CreativeBatchService creativeBatchService;

    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private DingTalkNoticeWTCHelper dingTalkNoticeWTCHelper;

    public static final String male = "male";
    public static final String female = "female";
    public static final String unisex = "unisex";

    @Transactional(rollbackFor = Exception.class)
    public void syncAutoGenTaskStatus(Integer taskId) {
        log.info("[autoGenImg]开始同步任务commonTaskId:{}自动出图状态", taskId);

        // 锁定任务，防止并发
        CommonTaskVO commonTaskVO = commonTaskService.lockById(taskId);

        // 还没有提交创作则提交创作，由init => running
        String taskStatus = commonTaskVO.getTaskStatus();

        if (StringUtils.equals(taskStatus, CommonTaskEnums.TaskStatus.INIT.name())) {

            JSONObject ext = commonTaskVO.getExtInfo();
            if (ext == null) {
                log.warn("[autoGenImg]当前任务extInfo不是json格式，可能是脏数据，忽略");
                return;
            }

            MaterialModelVO model = materialModelService.selectById(Integer.valueOf(commonTaskVO.getRelatedBizId()));

            if (StringUtils.equals(ext.getString(CommonConstants.KEY_CONFIRM_CAN_DELIVER), CommonConstants.YES)
                && StringUtils.equals(ext.getString(CommonConstants.KEY_LORA_UPLOAD_OSS_SUCCESS),
                CommonConstants.YES)) {

                if (!CommonUtil.isValidJson(commonTaskVO.getReqBizParams())) {
                    log.error(
                        "[autoGenImg]当前任务手工确认可交付，且lora已经完成同步，但reqBizParams不是json格式，无法继续");
                    return;
                }

                AutoGenImgParam autoGenImgParam = model.getClothLoraTrainDetail().getAutoGenImgParam();
                if (autoGenImgParam == null) {
                    log.error("[autoGenImg]当前任务手工确认可交付，且lora已经完成同步，但reqBizParams解析异常，无法继续");
                    return;
                }
                AutoGenImgParam taskGenParam = JSONObject.parseObject(commonTaskVO.getReqBizParams(),
                    AutoGenImgParam.class);
                if (taskGenParam == null) {
                    log.error("[autoGenImg]当前任务手工确认可交付，且lora已经完成同步，但reqBizParams解析异常，无法继续2");
                    return;
                }
                autoGenImgParam.setAutoGenTotalImgNum(taskGenParam.getAutoGenTotalImgNum());

                log.info("[autoGenImg]当前任务手工确认可交付，且lora已经完成同步，开始创建出图任务");
                createAutoGenImgBatches(commonTaskVO, autoGenImgParam);
                commonTaskService.updateByIdSelective(commonTaskVO);
            } else {
                if (model == null || model.getStatus().equals(MaterialModelStatusEnum.DISABLED.name())) {
                    log.info("[autoGenImg]当前任务对应服装模型已下架，任务失败");
                    commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.FAILED.name());
                    commonTaskService.updateByIdSelective(commonTaskVO);
                }
            }

            // 已经提交创作 running
        } else if (StringUtils.equals(taskStatus, CommonTaskEnums.TaskStatus.PENDING.name()) || StringUtils.equals(
            taskStatus, CommonTaskEnums.TaskStatus.RUNNING.name())) {

            log.info("[autoGenImg]当前任务状态为pending/running，开始更新出图任务状态");

            AutoGenImgRetDetail retDetail = JSONObject.parseObject(commonTaskVO.getRetDetail(),
                AutoGenImgRetDetail.class);
            AssertUtil.assertNotNull(retDetail, "当前任务状态为running，但retDetail不是json格式，无法继续");
            AssertUtil.assertNotEmpty(retDetail.getItems(), "当前任务状态为running，但retDetail.items为空，无法继续");

            boolean allEnd = true;

            for (AutoGenImgRetDetail.AutoGenSubBatch autoGenSubBatch : retDetail.getItems()) {
                if (!Objects.requireNonNull(CreativeStatusEnum.getByCode(autoGenSubBatch.getBatchStatus())).isEnd()) {
                    CreativeBatchVO creativeBatchVO = creativeBatchService.selectById(autoGenSubBatch.getBatchId());
                    if (creativeBatchVO != null) {
                        autoGenSubBatch.setBatchStatus(creativeBatchVO.getStatus().getCode());

                        if (!creativeBatchVO.getStatus().isEnd()) {
                            allEnd = false;
                        }
                    }
                }
            }

            if (allEnd) {
                commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.COMPLETED.name());

                // 更新对应batch的user为material model对应的商家
                // 更新material model状态为审核通过
                updateRelatedCreativeBatchesAndModel(commonTaskVO, retDetail);
            }

            commonTaskVO.setRetDetail(JSONObject.toJSONString(retDetail));
            commonTaskService.updateByIdSelective(commonTaskVO);

            // 任务已经完成
        } else if (StringUtils.equals(taskStatus, CommonTaskEnums.TaskStatus.COMPLETED.name())) {
            log.info("[autoGenImg]当前任务commonTaskId:{}已经完成，开始更新对应图片和服装的归属到商家", taskId);

            AutoGenImgRetDetail retDetail = JSONObject.parseObject(commonTaskVO.getRetDetail(),
                AutoGenImgRetDetail.class);
            AssertUtil.assertNotNull(retDetail, "当前任务状态为running，但retDetail不是json格式，无法继续");
            AssertUtil.assertNotEmpty(retDetail.getItems(), "当前任务状态为running，但retDetail.items为空，无法继续");

            // 更新对应batch的user为material model对应的商家
            // 更新material model状态为审核通过
            updateRelatedCreativeBatchesAndModel(commonTaskVO, retDetail);
        }

    }

    private void updateRelatedCreativeBatchesAndModel(CommonTaskVO commonTaskVO, AutoGenImgRetDetail retDetail) {
        log.info("[autoGenImg]当前自动出图子任务都已完成，开始更新关联的creativeBatch和materialModel");

        MaterialModelVO materialModelVO = materialModelService.lockById(
            Integer.valueOf(commonTaskVO.getRelatedBizId()));
        if (materialModelVO == null) {
            log.warn("[autoGenImg]当前自动出图子任务都已完成，但materialModel不存在，忽略，可能是对应模型被删除");
        } else {
            // 更新material model状态为审核通过
            if (StringUtils.equals(materialModelVO.getStatus(), MaterialModelStatusEnum.TESTING.getCode())
                || StringUtils.equals(materialModelVO.getStatus(), MaterialModelStatusEnum.ENABLED.getCode())) {

                materialModelVO.setStatus(MaterialModelStatusEnum.ENABLED.getCode());

                if (StringUtils.isBlank(materialModelVO.getExtInfo(KEY_DELIVERY_TIME, String.class))) {
                    materialModelVO.addExtInfo(KEY_DELIVERY_TIME, DateUtils.formatTime(new Date()));
                }
                materialModelService.innerUpdate(materialModelVO);

                for (AutoGenImgRetDetail.AutoGenSubBatch autoGenSubBatch : retDetail.getItems()) {
                    CreativeBatchVO creativeBatchVO = creativeBatchService.selectById(autoGenSubBatch.getBatchId());
                    if (creativeBatchVO != null) {
                        creativeBatchVO.setUserId(materialModelVO.getUserId());
                        creativeBatchVO.setOperatorId(materialModelVO.getOperatorId());
                        creativeBatchService.updateByIdSelective(creativeBatchVO);
                    } else {
                        log.warn(
                            "[autoGenImg]当前自动出图子任务都已完成，但creativeBatch不存在，忽略，可能是对应批次被删除");
                    }
                }
            } else {
                log.warn(
                    "[autoGenImg]当前自动出图子任务都已完成，但materialModel状态是{}，不更改对应模型状态，以人工为准，可能是手工审核不通过",
                    materialModelVO.getStatus());
            }
        }
    }

    private void createAutoGenImgBatches(CommonTaskVO commonTaskVO, AutoGenImgParam autoGenImgParam) {
        int totalTestImgNum = autoGenImgParam.getAutoGenTotalImgNum() != null ? autoGenImgParam.getAutoGenTotalImgNum()
            : 20;
        List<Integer> testFaces = autoGenImgParam.getAutoGenFaceIds();
        List<Integer> testScenes = autoGenImgParam.getAutoGenSceneIds();
        List<String> testProportions = autoGenImgParam.getAutoGenImgProportions();

        CreativeElementVO faceRoot = creativeElementService.queryRootKey(ElementConfigKeyEnum.FACE.name());
        CreativeElementVO sceneRoot = creativeElementService.queryRootKey(ElementConfigKeyEnum.SCENE.name());

        List<Integer> ids = Lists.newArrayList();
        ids.addAll(testFaces);
        ids.addAll(testScenes);

        List<CreativeElementVO> allElements = creativeElementService.batchQueryByIds(ids);
        Map<Integer, CreativeElementVO> elementsMap = allElements.stream().collect(
            Collectors.toMap(CreativeElementVO::getId, Function.identity()));

        // 按性别分类模特和场景
        Map<String, List<Integer>> genderToFaces = new HashMap<>();
        Map<String, List<Integer>> genderToScenes = new HashMap<>();

        // 分类模特
        for (Integer faceId : testFaces) {
            CreativeElementVO face = elementsMap.get(faceId);
            if (face == null) {
                log.warn("[autoGenImg]模特元素不存在，ID: {}", faceId);
                continue;
            }

            String gender = getElementGender(face);
            if (!genderToFaces.containsKey(gender)) {
                genderToFaces.put(gender, new ArrayList<>());
            }
            genderToFaces.get(gender).add(faceId);
        }

        // 分类场景
        for (Integer sceneId : testScenes) {
            CreativeElementVO scene = elementsMap.get(sceneId);
            if (scene == null) {
                log.warn("[autoGenImg]场景元素不存在，ID: {}", sceneId);
                continue;
            }

            String gender = getElementGender(scene);

            //性别通用，场景同时放在男女通道里，让两边性别都能用这个去出图
            if (StringUtils.equals(gender, unisex)) {
                if (!genderToScenes.containsKey(male)) {
                    genderToScenes.put(male, new ArrayList<>());
                }
                genderToScenes.get(male).add(sceneId);

                if (!genderToScenes.containsKey(female)) {
                    genderToScenes.put(female, new ArrayList<>());
                }
                genderToScenes.get(female).add(sceneId);

                //指定性别
            } else {
                if (!genderToScenes.containsKey(gender)) {
                    genderToScenes.put(gender, new ArrayList<>());
                }
                genderToScenes.get(gender).add(sceneId);
            }
        }

        // 计算有效的组合数量
        int validCombinations = 0;
        for (String gender : genderToFaces.keySet()) {
            if (genderToScenes.containsKey(gender)) {
                validCombinations += genderToFaces.get(gender).size() * genderToScenes.get(gender).size();
            } else {
                log.warn("[autoGenImg]性别为{}的模特没有对应性别的场景，这些模特将被忽略: {}", gender,
                    genderToFaces.get(gender));
            }
        }

        if (validCombinations == 0) {
            log.error("[autoGenImg]没有有效的模特-场景性别匹配组合，无法创建出图任务");
            dingTalkNoticeWTCHelper.sendMsg2DevGroup(AUTO_GEN_TASK_NONE_SCENE, commonTaskVO.getId(),
                String.format("[autoGenImg]没有有效的模特-场景性别匹配组合，无法创建出图任务,id=%s,loraId=%s",
                    commonTaskVO.getId(), commonTaskVO.getRelatedBizId()));
            return;
        }

        // 重新计算每个批次的平均图片数量
        int avgImgNumOfBatch = totalTestImgNum / (validCombinations * testProportions.size());
        // 如果平均值是0，则设置为1
        if (avgImgNumOfBatch == 0) {
            avgImgNumOfBatch = 1;
        }

        AutoGenImgRetDetail retDetail = new AutoGenImgRetDetail();
        List<AutoGenImgRetDetail.AutoGenSubBatch> items = new ArrayList<>();

        int totalImgNumLeft = totalTestImgNum;
        int totalBatches = 0;

        // 按性别匹配创建批次
        for (String gender : genderToFaces.keySet()) {
            if (!genderToScenes.containsKey(gender)) {
                log.warn("[autoGenImg]性别为{}的模特没有对应性别的场景，这些模特将被忽略: {}", gender,
                    genderToFaces.get(gender));
                continue; // 跳过没有对应性别场景的模特
            }

            List<Integer> facesOfGender = genderToFaces.get(gender);
            List<Integer> scenesOfGender = genderToScenes.get(gender);

            for (Integer face : facesOfGender) {
                for (Integer scene : scenesOfGender) {
                    for (String proportion : testProportions) {
                        totalBatches++;

                        // 如果当前是最后一批，则全部生成
                        boolean lastBatch = totalBatches == validCombinations * testProportions.size();

                        int imgNumOfThisBatch = lastBatch ? totalImgNumLeft : Math.min(totalImgNumLeft,
                            avgImgNumOfBatch);

                        if (imgNumOfThisBatch > 0) {
                            CreativeBatchVO batch = createOneBatch(Integer.valueOf(commonTaskVO.getRelatedBizId()),
                                faceRoot, sceneRoot, face, scene, proportion, imgNumOfThisBatch);

                            if (batch == null) {
                                log.error("[autoGenImg]创建出图任务失败，下次再试");
                            } else {
                                AutoGenImgRetDetail.AutoGenSubBatch item = new AutoGenImgRetDetail.AutoGenSubBatch();
                                {
                                    item.setBatchId(batch.getId());
                                    item.setBatchStatus(CreativeStatusEnum.INIT.name());
                                    item.setBatchCnt(batch.getBatchCnt());
                                }

                                items.add(item);
                                totalImgNumLeft -= imgNumOfThisBatch;
                            }
                        }
                    }
                }
            }
        }

        log.info("[autoGenImg]创建了{}个批次，剩余未分配的图片数量: {}", items.size(), totalImgNumLeft);

        retDetail.setItems(items);
        commonTaskVO.setRetDetail(JSONObject.toJSONString(retDetail));

        // 更新任务状态为RUNNING
        commonTaskVO.setTaskStatus(CommonTaskEnums.TaskStatus.RUNNING.name());
    }

    /**
     * 获取元素的性别属性
     *
     * @param element 元素
     * @return 性别标识，male / female / unisex
     */
    private String getElementGender(CreativeElementVO element) {

        if (element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name())) {
            if (element.getType().contains("male-model")) {
                return male;
            } else if (element.getType().contains("female-model")) {
                return female;
            }
        } else {
            if (element.getType().contains("Common") || (element.getType().contains("Male") && element.getType()
                .contains("Female"))) {
                return unisex;
            }
            if (element.getType().contains("Male")) {
                return male;
            } else if (element.getType().contains("Female")) {
                return female;
            }
        }

        log.error("当前配置无法区分男女:{},{}", element.getId(), element.getName());
        // 钉钉通知松然
        DingTalkNoticeHelper.sendMsg2DevGroup("当前配置无法区分男女:" + element.getName() + "," + element.getId(),
            Collections.singletonList("15906660486"));
        throw new RuntimeException("[autoGenImg]当前配置无法区分男女:" + element.getName());
    }

    private CreativeBatchVO createOneBatch(Integer loraId, CreativeElementVO faceRoot, CreativeElementVO sceneRoot,
                                           Integer face, Integer scene, String proportion, Integer imgNum) {

        Map<Integer, List<Integer>> configs = new HashMap<>();
        if (faceRoot != null) {
            configs.put(faceRoot.getId(), Collections.singletonList(face));
        }

        if (sceneRoot != null) {
            configs.put(sceneRoot.getId(), Collections.singletonList(scene));
        }

        AddCreativeRequest req = new AddCreativeRequest();
        req.setLoraId(loraId);
        req.setImageNum(imgNum);
        req.setProportion(proportion);
        req.setConfigs(configs);
        req.setBizTag(BizConstants.SYSTEM_IMAGES);

        try {
            return creativeBatchService.create(CreativeTypeEnum.CREATE_IMAGE, req);
        } catch (Exception e) {
            log.error("[autoGenImg]创建出图任务失败", e);
            return null;
        }
    }
}

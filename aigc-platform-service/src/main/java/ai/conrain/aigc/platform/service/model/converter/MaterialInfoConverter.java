package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.MaterialInfoDO;
import ai.conrain.aigc.platform.dal.example.MaterialInfoExample;
import ai.conrain.aigc.platform.service.model.query.MaterialInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * MaterialInfoConverter
 *
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
public class MaterialInfoConverter {

    /**
     * DO -> VO
     */
    public static MaterialInfoVO do2VO(MaterialInfoDO from) {
        MaterialInfoVO to = new MaterialInfoVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setSubType(from.getSubType());
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMaterialDetail(CommonUtil.parseObject(from.getMaterialDetail()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static MaterialInfoDO vo2DO(MaterialInfoVO from) {
        MaterialInfoDO to = new MaterialInfoDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setSubType(from.getSubType());
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMaterialDetail(CommonUtil.toJSONString(from.getMaterialDetail()));

        return to;
    }

    /**
     * DO -> Query
     */
    public static MaterialInfoQuery do2Query(MaterialInfoDO from) {
        MaterialInfoQuery to = new MaterialInfoQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setSubType(from.getSubType());
        to.setExtInfo(from.getExtInfo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMaterialDetail(from.getMaterialDetail());

        return to;
    }

    /**
     * Query -> DO
     */
    public static MaterialInfoDO query2DO(MaterialInfoQuery from) {
        MaterialInfoDO to = new MaterialInfoDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setSubType(from.getSubType());
        to.setExtInfo(from.getExtInfo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMaterialDetail(from.getMaterialDetail());

        return to;
    }


    /**
     * Query -> Example
     */
    public static MaterialInfoExample query2Example(MaterialInfoQuery from) {
        MaterialInfoExample to = new MaterialInfoExample();
        MaterialInfoExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getSubType())) {
            c.andSubTypeEqualTo(from.getSubType());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (MaterialInfoExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<MaterialInfoVO> doList2VOList(List<MaterialInfoDO> list) {
        return CommonUtil.listConverter(list, MaterialInfoConverter::do2VO);
    }
}
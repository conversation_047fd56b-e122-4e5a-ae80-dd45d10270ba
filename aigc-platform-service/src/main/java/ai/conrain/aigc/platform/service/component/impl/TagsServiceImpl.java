package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.TagsDAO;
import ai.conrain.aigc.platform.dal.entity.TagsDO;
import ai.conrain.aigc.platform.dal.example.TagsExample;
import ai.conrain.aigc.platform.service.component.TagsService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TagsConverter;
import ai.conrain.aigc.platform.service.model.query.TagsQuery;
import ai.conrain.aigc.platform.service.model.vo.TagsVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**   
 * TagsService实现
 *
 * <AUTHOR>
 * @version TagsService.java v 0.1 2024-05-22 08:28:41
 */
@Slf4j
@Service
public class TagsServiceImpl implements TagsService {

	/** DAO */
	@Autowired
	private TagsDAO tagsDAO;

	@Override
	public TagsVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		TagsDO data = tagsDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return TagsConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = tagsDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Tags失败");
	}

	@Override
	public TagsVO insert(TagsVO tags) {
		AssertUtil.assertNotNull(tags, ResultCode.PARAM_INVALID, "tags is null");
		AssertUtil.assertTrue(tags.getId() == null, ResultCode.PARAM_INVALID, "tags.id is present");

		//创建时间、修改时间兜底
		if (tags.getCreateTime() == null) {
			tags.setCreateTime(new Date());
		}

		if (tags.getModifyTime() == null) {
			tags.setModifyTime(new Date());
		}

		TagsDO data = TagsConverter.vo2DO(tags);
		Integer n = tagsDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Tags失败");
		AssertUtil.assertNotNull(data.getId(), "新建Tags返回id为空");
		tags.setId(data.getId());
		return tags;
	}


	@Override
	public void updateByIdSelective(TagsVO tags) {
		AssertUtil.assertNotNull(tags, ResultCode.PARAM_INVALID, "tags is null");
    	AssertUtil.assertTrue(tags.getId() != null, ResultCode.PARAM_INVALID, "tags.id is null");

		//修改时间必须更新
		tags.setModifyTime(new Date());
		TagsDO data = TagsConverter.vo2DO(tags);
		int n = tagsDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Tags失败，影响行数:" + n);
	}

	@Override
	public List<TagsVO> queryTagsList(TagsQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TagsExample example = TagsConverter.query2Example(query);

		List<TagsDO> list = tagsDAO.selectByExampleWithBLOBs(example);
			return TagsConverter.doList2VOList(list);
	}

	@Override
	public Long queryTagsCount(TagsQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TagsExample example = TagsConverter.query2Example(query);
		long c = tagsDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询标签配置
	 */
	@Override
	public PageInfo<TagsVO> queryTagsByPage(TagsQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<TagsVO> page = new PageInfo<>();

		TagsExample example = TagsConverter.query2Example(query);
		long totalCount = tagsDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<TagsDO> list = tagsDAO.selectByExample(example);
		page.setList(TagsConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
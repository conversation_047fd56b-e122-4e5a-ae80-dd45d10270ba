package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.PreferenceTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.MerchantPreferenceDetail;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.MerchantPreferenceDO;
import ai.conrain.aigc.platform.dal.example.MerchantPreferenceExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.MerchantPreferenceQuery;
import ai.conrain.aigc.platform.service.model.vo.MerchantPreferenceVO;
import ai.conrain.aigc.platform.service.model.converter.MerchantPreferenceConverter;
import ai.conrain.aigc.platform.dal.dao.MerchantPreferenceDAO;
import ai.conrain.aigc.platform.service.component.MerchantPreferenceService;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * MerchantPreferenceService实现
 *
 * <AUTHOR>
 * @version MerchantPreferenceService.java v 0.1 2024-11-12 08:18:52
 */
@Slf4j
@Service
public class MerchantPreferenceServiceImpl implements MerchantPreferenceService {

    /** DAO */
    @Autowired
    private MerchantPreferenceDAO merchantPreferenceDAO;
    @Autowired
    private UserService userService;

    @Override
    public MerchantPreferenceVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        MerchantPreferenceDO data = merchantPreferenceDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return MerchantPreferenceConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = merchantPreferenceDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除MerchantPreference失败");
    }

    @Override
    public MerchantPreferenceVO insert(MerchantPreferenceVO merchantPreference) {
        AssertUtil.assertNotNull(merchantPreference, ResultCode.PARAM_INVALID, "merchantPreference is null");
        AssertUtil.assertTrue(merchantPreference.getId() == null, ResultCode.PARAM_INVALID,
            "merchantPreference.id is present");

        //创建时间、修改时间兜底
        if (merchantPreference.getCreateTime() == null) {
            merchantPreference.setCreateTime(new Date());
        }

        if (merchantPreference.getModifyTime() == null) {
            merchantPreference.setModifyTime(new Date());
        }

        MerchantPreferenceDO data = MerchantPreferenceConverter.vo2DO(merchantPreference);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = merchantPreferenceDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建MerchantPreference失败");
        AssertUtil.assertNotNull(data.getId(), "新建MerchantPreference返回id为空");
        merchantPreference.setId(data.getId());
        return merchantPreference;
    }

    @Override
    public void updateByIdSelective(MerchantPreferenceVO merchantPreference) {
        AssertUtil.assertNotNull(merchantPreference, ResultCode.PARAM_INVALID, "merchantPreference is null");
        AssertUtil.assertTrue(merchantPreference.getId() != null, ResultCode.PARAM_INVALID,
            "merchantPreference.id is null");

        //修改时间必须更新
        merchantPreference.setModifyTime(new Date());
        MerchantPreferenceDO data = MerchantPreferenceConverter.vo2DO(merchantPreference);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = merchantPreferenceDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MerchantPreference失败，影响行数:" + n);
    }

    @Override
    public List<MerchantPreferenceVO> queryMerchantPreferenceList(MerchantPreferenceQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MerchantPreferenceExample example = MerchantPreferenceConverter.query2Example(query);

        List<MerchantPreferenceDO> list = merchantPreferenceDAO.selectByExample(example);
        return MerchantPreferenceConverter.doList2VOList(list);
    }

    @Override
    public List<MerchantPreferenceVO> queryMerchantPreferenceListWithBlob(MerchantPreferenceQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MerchantPreferenceExample example = MerchantPreferenceConverter.query2Example(query);

        List<MerchantPreferenceDO> list = merchantPreferenceDAO.selectByExampleWithBLOBs(example);
        return MerchantPreferenceConverter.doList2VOList(list);
    }

    @Override
    public Long queryMerchantPreferenceCount(MerchantPreferenceQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MerchantPreferenceExample example = MerchantPreferenceConverter.query2Example(query);
        long c = merchantPreferenceDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询商家偏好配置
     */
    @Override
    public PageInfo<MerchantPreferenceVO> queryMerchantPreferenceByPage(MerchantPreferenceQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<MerchantPreferenceVO> page = new PageInfo<>();

        MerchantPreferenceExample example = MerchantPreferenceConverter.query2Example(query);
        long totalCount = merchantPreferenceDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<MerchantPreferenceDO> list = merchantPreferenceDAO.selectByExample(example);
        page.setList(MerchantPreferenceConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public MerchantPreferenceDetail queryDetailByUserId(Integer userId) {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setUserId(userId);

        List<MerchantPreferenceVO> list = queryMerchantPreferenceList(query);
        return MerchantPreferenceConverter.convertDetail(list);
    }

    @Override
    public MerchantPreferenceVO queryEnableCreativePreference(Integer userId, String clothType) {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setUserId(userId);
        query.setType(PreferenceTypeEnum.CREATIVE.getCode());

        List<MerchantPreferenceVO> list = queryMerchantPreferenceList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.stream().filter(e -> e.getEnableAutoCreative() != null && e.getEnableAutoCreative()).filter(
            e -> e.getTags().contains(clothType)).findFirst().orElse(null);
    }

    @Override
    public List<MerchantPreferenceDetail> queryAllMerchantPreference() {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        List<MerchantPreferenceVO> list = queryMerchantPreferenceList(query);

        List<MerchantPreferenceDetail> result = MerchantPreferenceConverter.convertDetailList(list);

        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        List<UserVO> userList = userService.batchQueryById(
            result.stream().map(MerchantPreferenceDetail::getUserId).collect(Collectors.toList()));

        if (CollectionUtils.isNotEmpty(userList)) {
            result.forEach(item -> {
                userList.stream()
                    .filter(u -> u.getId().equals(item.getUserId()))
                    .findFirst().ifPresent(user -> item.setNickName(user.getNickName()));
            });

        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMerchantPreference(MerchantPreferenceDetail preference) {
        int cnt = merchantPreferenceDAO.deleteByUserId(preference.getUserId());
        AssertUtil.assertTrue(cnt >= 0, ResultCode.BIZ_FAIL, "删除MerchantPreference失败，影响行数:" + cnt);
        Date now = new Date();

        List<MerchantPreferenceDO> batchList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(preference.getPreferences())) {
            preference.getPreferences().forEach(item -> {
                MerchantPreferenceDO target = buildPreferenceDO(preference.getUserId(),
                    PreferenceTypeEnum.AUTO_DELIVERY, item, now);
                batchList.add(target);
            });
        }

        if (CollectionUtils.isNotEmpty(preference.getCreativePreferences())) {
            preference.getCreativePreferences().forEach(item -> {
                MerchantPreferenceDO target = buildPreferenceDO(preference.getUserId(), PreferenceTypeEnum.CREATIVE,
                    item, now);
                batchList.add(target);
            });
        }

        if (CollectionUtils.isNotEmpty(batchList)) {
            merchantPreferenceDAO.batchInsert(batchList);
        }
    }

    @Override
    public List<MerchantPreferenceVO> queryCreativePreference(Integer masterUserId) {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setType(PreferenceTypeEnum.CREATIVE.getCode());
        query.setUserId(masterUserId);
        return queryMerchantPreferenceList(query);
    }

    @Override
    public List<MerchantPreferenceVO> queryDeliveryPreference(Integer masterUserId) {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setType(PreferenceTypeEnum.AUTO_DELIVERY.getCode());
        query.setUserId(masterUserId);
        return queryMerchantPreferenceList(query);
    }

    @Override
    public List<MerchantPreferenceVO> queryClothCollocation(Integer userId) {
        MerchantPreferenceQuery query = new MerchantPreferenceQuery();
        query.setUserId(userId);
        query.setType(PreferenceTypeEnum.CLOTH_COLLOCATION.getCode());
        return queryMerchantPreferenceList(query);
    }

    @Override
    public Boolean checkQtyLimit(PreferenceTypeEnum typeEnum, int size) {
        switch (typeEnum) {
            case CLOTH_COLLOCATION:
                return size <= MAX_CLOTH_COLLOCATION_NUM;
            case CUSTOM_SCENE:
                return size <= MAX_CUSTOM_SCENE_NUM;
            default:
                return false;
        }
    }

    @Override
    public void validateParam(MerchantPreferenceQuery query) {PreferenceTypeEnum typeEnum = PreferenceTypeEnum.getByCode(query.getType());
        AssertUtil.assertNotNull(typeEnum, ResultCode.PARAM_INVALID, "类型错误");
        switch (Objects.requireNonNull(typeEnum)) {
            case CLOTH_COLLOCATION:
                AssertUtil.assertNotBlank(query.getClothCollocation(), ResultCode.PARAM_INVALID, "服装搭配不能为空");
                break;
            case CUSTOM_SCENE:
                AssertUtil.assertNotNull(query.getExtInfo(), ResultCode.PARAM_INVALID, "扩展信息不能为空");
                AssertUtil.assertTrue(query.getExtInfo().containsKey(KEY_CUSTOM_SCENE_DESC), ResultCode.PARAM_INVALID, "自定义场景不能为空");
                break;
            default:
                break;
        }
    }

    /**
     * 构建偏好DO
     *
     * @param userId 用户信息
     * @param type   类型
     * @param item   偏好信息
     * @param now    当前时间
     * @return 偏好DO
     */
    @NotNull
    private static MerchantPreferenceDO buildPreferenceDO(Integer userId, PreferenceTypeEnum type,
                                                          MerchantPreferenceVO item, Date now) {
        item.setType(type);
        item.setOperatorId(OperationContextHolder.getOperatorUserId());
        item.setCreateTime(now);
        item.setModifyTime(now);
        item.setUserId(userId);
        return MerchantPreferenceConverter.vo2DO(item);
    }

}
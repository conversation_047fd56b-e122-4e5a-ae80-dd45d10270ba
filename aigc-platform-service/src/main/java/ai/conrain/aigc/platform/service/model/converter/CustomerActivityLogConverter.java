package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.CustomerActivityLogDO;
import ai.conrain.aigc.platform.dal.example.CustomerActivityLogExample;
import ai.conrain.aigc.platform.service.model.query.CustomerActivityLogQuery;
import ai.conrain.aigc.platform.service.model.vo.CustomerActivityLogVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * CustomerActivityLogConverter
 *
 * @version CustomerActivityLogService.java
 */
public class CustomerActivityLogConverter {

    /**
     * DO -> VO
     */
    public static CustomerActivityLogVO do2VO(CustomerActivityLogDO from) {
        CustomerActivityLogVO to = new CustomerActivityLogVO();
        to.setId(from.getId());
        to.setDt(from.getDt());
        to.setTitle(from.getTitle());
        to.setUserId(from.getUserId());
        to.setCustomerName(from.getCustomerName());
        to.setCustomerCorp(from.getCustomerCorp());
        to.setUserGroup(from.getUserGroup());
        to.setSalesId(from.getSalesId());
        to.setSalesName(from.getSalesName());
        to.setSalesArea(from.getSalesArea());
        to.setFirstPayTime(from.getFirstPayTime());
        to.setPayAmount(from.getPayAmount());
        to.setConsumeAmount(from.getConsumeAmount());
        to.setPayTimes(from.getPayTimes());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CustomerActivityLogDO vo2DO(CustomerActivityLogVO from) {
        CustomerActivityLogDO to = new CustomerActivityLogDO();
        to.setId(from.getId());
        to.setDt(from.getDt());
        to.setTitle(from.getTitle());
        to.setUserId(from.getUserId());
        to.setCustomerName(from.getCustomerName());
        to.setCustomerCorp(from.getCustomerCorp());
        to.setUserGroup(from.getUserGroup());
        to.setSalesId(from.getSalesId());
        to.setSalesName(from.getSalesName());
        to.setSalesArea(from.getSalesArea());
        to.setFirstPayTime(from.getFirstPayTime());
        to.setPayAmount(from.getPayAmount());
        to.setConsumeAmount(from.getConsumeAmount());
        to.setPayTimes(from.getPayTimes());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CustomerActivityLogExample query2Example(CustomerActivityLogQuery from) {
        CustomerActivityLogExample to = new CustomerActivityLogExample();
        CustomerActivityLogExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getDt())) {
            c.andDtEqualTo(from.getDt());
        }
        if (!ObjectUtils.isEmpty(from.getTitle())) {
            c.andTitleEqualTo(from.getTitle());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerName())) {
            c.andCustomerNameEqualTo(from.getCustomerName());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerCorp())) {
            c.andCustomerCorpEqualTo(from.getCustomerCorp());
        }
        if (!ObjectUtils.isEmpty(from.getUserGroup())) {
            c.andUserGroupEqualTo(from.getUserGroup());
        }
        if (!ObjectUtils.isEmpty(from.getSalesId())) {
            c.andSalesIdEqualTo(from.getSalesId());
        }
        if (!ObjectUtils.isEmpty(from.getSalesName())) {
            c.andSalesNameEqualTo(from.getSalesName());
        }
        if (!ObjectUtils.isEmpty(from.getSalesArea())) {
            c.andSalesAreaEqualTo(from.getSalesArea());
        }
        if (!ObjectUtils.isEmpty(from.getFirstPayTime())) {
            c.andFirstPayTimeEqualTo(from.getFirstPayTime());
        }
        if (!ObjectUtils.isEmpty(from.getPayAmount())) {
            c.andPayAmountEqualTo(from.getPayAmount());
        }
        if (!ObjectUtils.isEmpty(from.getConsumeAmount())) {
            c.andConsumeAmountEqualTo(from.getConsumeAmount());
        }
        if (!ObjectUtils.isEmpty(from.getPayTimes())) {
            c.andPayTimesEqualTo(from.getPayTimes());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<CustomerActivityLogVO> doList2VOList(List<CustomerActivityLogDO> list) {
        return CommonUtil.listConverter(list, CustomerActivityLogConverter::do2VO);
    }
}
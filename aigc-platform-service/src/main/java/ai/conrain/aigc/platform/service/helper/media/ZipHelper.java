/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper.media;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * zip帮助类
 *
 * <AUTHOR>
 * @version : ZipHelper.java, v 0.1 2025/9/12 16:04 renxiao.wu Exp $
 */
@Slf4j
@Component
public class ZipHelper {
    @Autowired
    private OssService ossService;

    /**
     * 从ZIP文件中导入图片并上传到oss上
     *
     * @param file 文件
     * @return 图片URL列表
     * @throws IOException 异常
     */
    public List<String> importImagesFromZip(MultipartFile file) throws IOException {
        List<String> imageUrls = new ArrayList<>();

        try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
            ZipEntry zipEntry = zis.getNextEntry();

            while (zipEntry != null) {
                String entryName = zipEntry.getName();

                // 过滤掉各种系统文件和隐藏文件
                if (!isSystemOrHiddenFile(entryName) &&
                    !zipEntry.isDirectory() &&
                    isImageFile(entryName)) {
                    // 读取图片数据
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        baos.write(buffer, 0, len);
                    }

                    // 上传到OSS
                    String imageUrl = ossService.upload(formatFileName(entryName),
                        new ByteArrayInputStream(baos.toByteArray()));
                    imageUrls.add(imageUrl);
                }
                zipEntry = zis.getNextEntry();
            }
        }

        return imageUrls;
    }

    /**
     * 判断是否为系统或隐藏文件
     *
     * @param fileName 文件名
     * @return 是否为系统或隐藏文件
     */
    private boolean isSystemOrHiddenFile(String fileName) {
        // 转为小写进行比较
        String lowerFileName = fileName.toLowerCase();

        // 过滤 macOS 系统文件
        if (lowerFileName.contains("__macosx/") ||
            lowerFileName.contains(".ds_store") ||
            lowerFileName.contains("__macosx")) {
            return true;
        }

        // 过滤 Windows 系统文件
        if (lowerFileName.contains("thumbs.db") ||
            lowerFileName.contains("desktop.ini") ||
            fileName.startsWith("~$")) { // Word/Excel 临时文件
            return true;
        }

        // 过滤以点开头的隐藏文件
        if (fileName.contains("/.")) { // Unix/Linux 隐藏文件
            return true;
        }

        // 检查文件名是否以点开头（根目录下的隐藏文件）
        String[] pathParts = fileName.split("/");
        if (pathParts.length > 0 && pathParts[pathParts.length - 1].startsWith(".")) {
            return true;
        }

        return false;
    }


    /**
     * 是否图片文件
     *
     * @param fileName 文件名
     * @return 是否图片文件
     */
    private boolean isImageFile(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg") || lowerFileName.endsWith(".png")
               || lowerFileName.endsWith(".gif") || lowerFileName.endsWith(".bmp") || lowerFileName.endsWith(".webp");
    }

    /**
     * 格式化文件名
     *
     * @param fileName 文件名
     * @return 格式化后的文件名
     */
    private String formatFileName(String fileName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        // 先通过 ContentType 获取文件扩展名
        String ext = StringUtils.substringAfterLast(fileName, ".");
        if (StringUtils.isBlank(ext)) {
            ext = FilenameUtils.getExtension(fileName);// 判断 ext 是否包含问号
            if (ext != null && ext.contains("?")) {
                // 如果包含问号，则截取问号之前的部分
                ext = ext.substring(0, ext.indexOf("?"));
            }
        }
        //图片素材用于抠图需要强制小写后缀名
        ext = "." + StringUtils.lowerCase(ext);
        fileName = StringUtils.substringBeforeLast(fileName, ".");

        return sdf.format(new Date()) + OperationContextHolder.getMasterUserId() + "/" + fileName + "_"
               + CommonUtil.uuid() + ext;
    }
}

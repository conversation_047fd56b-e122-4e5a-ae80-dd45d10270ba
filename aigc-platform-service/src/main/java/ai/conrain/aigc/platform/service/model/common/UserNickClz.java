/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

/**
 * 主账号昵称接口
 *
 * <AUTHOR>
 * @version : UserNickClz.java, v 0.1 2024/6/27 16:50 renxiao.wu Exp $
 */
public interface UserNickClz extends UserIdClz {
    /**
     * 获取主账号昵称
     *
     * @return 主账号昵称
     */
    String getUserNick();

    /**
     * 设置主账号昵称
     *
     * @param userNick 主账号昵称
     */
    void setUserNick(String userNick);
}

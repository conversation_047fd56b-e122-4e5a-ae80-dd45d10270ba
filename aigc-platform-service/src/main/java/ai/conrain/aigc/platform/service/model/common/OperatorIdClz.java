/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

/**
 * 带operatorId的接口
 *
 * <AUTHOR>
 * @version : OperatorIdClz.java, v 0.1 2024/6/27 16:52 renxiao.wu Exp $
 */
public interface OperatorIdClz {
    /**
     * 获取操作员id
     *
     * @return 操作员id
     */
    Integer getOperatorId();

    /**
     * 设置操作员id
     *
     * @param operatorId 操作员id
     */
    void setOperatorId(Integer operatorId);
}

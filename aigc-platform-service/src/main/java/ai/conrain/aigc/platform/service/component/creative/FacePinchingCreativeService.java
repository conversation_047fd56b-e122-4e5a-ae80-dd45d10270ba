/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.enums.AgeRangeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeBizTypeEnum;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.request.FacePinchingCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_AGE_RANGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_GENDER_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_NEGATIVE_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RESULT_SIZE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SKIN_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TARGET_IMAGE_PATH;

/**
 * 模特捏脸创作服务
 *
 * <AUTHOR>
 * @version : FacePinchingCreativeService.java, v 0.1 2025/4/8 12:11 renxiao.wu Exp $
 */
@Service
public class FacePinchingCreativeService extends AbstractCreativeService<FacePinchingCreativeRequest> {
    @Autowired
    private BatchToAsyncExecutor batchToAsyncExecutor;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FACE_PINCHING;
    }

    @Override
    protected CreativeBatchVO buildData(FacePinchingCreativeRequest request, MaterialModelVO modelVO)
        throws IOException {
        int batchCnt = 3;
        CreativeBatchVO data = new CreativeBatchVO();
        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setBatchCnt(batchCnt);
        data.setShowImage(request.getTargetImages().get(0));
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setType(getCreativeType());
        data.setBizType(CreativeBizTypeEnum.NORMAL);
        data.setImageProportion(ProportionTypeEnum.THREE_FOUR.getCode());
        data.addExtInfo(KEY_RESULT_SIZE, batchCnt * request.getOriginImages().size());
        data.addExtInfo(KEY_SKIN_TYPE, request.getSkinType());
        data.addExtInfo(KEY_AGE_RANGE, request.getAgeRange());
        data.addExtInfo(KEY_GENDER_TYPE, request.getGenderType());

        batchToAsyncExecutor.storeSync(request, data);

        return data;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        super.fillTaskExt(target, batch, elements, idx);
        batchToAsyncExecutor.restoreTask(target, batch);
        Integer resultSize = batch.getExtValue(KEY_RESULT_SIZE, Integer.class);
        target.setBatchCnt(resultSize / batch.getBatchCnt());

        target.addExtInfo(KEY_SKIN_TYPE, batch.getExtValue(KEY_SKIN_TYPE, String.class));
        target.addExtInfo(KEY_AGE_RANGE, batch.getExtValue(KEY_AGE_RANGE, String.class));
        target.addExtInfo(KEY_GENDER_TYPE, batch.getExtValue(KEY_GENDER_TYPE, String.class));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        batchToAsyncExecutor.asyncExecAndStore(task, elements);

        context.put(KEY_ORIGIN_IMAGE_PATH, task.getExtInfo(KEY_ORIGIN_IMAGE_PATH, String.class));
        context.put(KEY_TARGET_IMAGE_PATH, task.getExtInfo(KEY_TARGET_IMAGE_PATH, String.class));
        String ageRangeStr = task.getExtInfo(KEY_AGE_RANGE, String.class);
        AgeRangeEnum ageRange = AgeRangeEnum.getByCode(ageRangeStr);
        ageRange = ageRange == null ? AgeRangeEnum.ADULT : ageRange;
        String genderType = task.getExtInfo(KEY_GENDER_TYPE, String.class);
        boolean isFemale = StringUtils.equals("female", genderType);
        String race = task.getExtInfo(KEY_RACE, String.class);

        String skinType = task.getExtInfo(KEY_SKIN_TYPE, String.class);
        //A eleven-year-old Chinese girl
        String prompt = String.format("A %s %s %s", ageRange.getAgeDesc(), race,
            ageRange == AgeRangeEnum.ADULT ? (isFemale ? "female model" : "male model") : (isFemale ? "girl" : "boy"));
        if (StringUtils.equals("smooth", skinType)) {
            prompt += ", Smooth skin, clean skin";
            context.put(KEY_NEGATIVE_PROMPT, "(freckles:1.2)");
            context.put("samplerName", "euler_ancestral");
            context.put("scheduler", "sgm_uniform");
        }

        context.put(KEY_PROMPT, prompt);
    }
}

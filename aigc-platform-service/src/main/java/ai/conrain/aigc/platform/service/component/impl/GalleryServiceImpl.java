package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.GalleryDAO;
import ai.conrain.aigc.platform.dal.entity.GalleryDO;
import ai.conrain.aigc.platform.dal.example.GalleryExample;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.GalleryService;
import ai.conrain.aigc.platform.service.enums.GallerySubTypeEnum;
import ai.conrain.aigc.platform.service.enums.GalleryTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.GalleryConverter;
import ai.conrain.aigc.platform.service.model.query.GalleryQuery;
import ai.conrain.aigc.platform.service.model.request.GalleryUploadRequest;
import ai.conrain.aigc.platform.service.model.vo.GalleryVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TAGS;

/**
 * GalleryService实现
 *
 * <AUTHOR>
 * @version GalleryService.java v 0.1 2025-08-20 02:49:20
 */
@Slf4j
@Service
public class GalleryServiceImpl implements GalleryService {

    /**
     * DAO
     */
    @Autowired
    private GalleryDAO galleryDAO;
    @Autowired
    private OssService ossService;

    @Override
    public GalleryVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        GalleryDO data = galleryDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return GalleryConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = galleryDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Gallery失败");
    }

    @Override
    public GalleryVO insert(GalleryVO gallery) {
        AssertUtil.assertNotNull(gallery, ResultCode.PARAM_INVALID, "gallery is null");
        AssertUtil.assertTrue(gallery.getId() == null, ResultCode.PARAM_INVALID, "gallery.id is present");

        //创建时间、修改时间兜底
        if (gallery.getCreateTime() == null) {
            gallery.setCreateTime(new Date());
        }

        if (gallery.getModifyTime() == null) {
            gallery.setModifyTime(new Date());
        }

        GalleryDO data = GalleryConverter.vo2DO(gallery);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = galleryDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Gallery失败");
        AssertUtil.assertNotNull(data.getId(), "新建Gallery返回id为空");
        gallery.setId(data.getId());
        return gallery;
    }


    @Override
    public void updateByIdSelective(GalleryVO gallery) {
        AssertUtil.assertNotNull(gallery, ResultCode.PARAM_INVALID, "gallery is null");
        AssertUtil.assertTrue(gallery.getId() != null, ResultCode.PARAM_INVALID, "gallery.id is null");

        // 更新 tags
        List<String> tags = gallery.getTags();
        if (CollectionUtils.isNotEmpty(tags)) {
            gallery.addExtInfo(KEY_TAGS, tags);
        }

        //修改时间必须更新
        gallery.setModifyTime(new Date());
        GalleryDO data = GalleryConverter.vo2DO(gallery);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = galleryDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Gallery失败，影响行数:" + n);
    }

    @Override
    public List<GalleryVO> queryGalleryList(GalleryQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        GalleryExample example = GalleryConverter.query2Example(query);

        List<GalleryDO> list = galleryDAO.selectByExample(example);
        return GalleryConverter.doList2VOList(list);
    }

    @Override
    public Long queryGalleryCount(GalleryQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        GalleryExample example = GalleryConverter.query2Example(query);
        return galleryDAO.countByExample(example);
    }

    /**
     * 带条件分页查询图库表: 保存系统配置的商品图、用户的上传历史等
     */
    @Override
    public PageInfo<GalleryVO> queryGalleryByPage(GalleryQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("gallery.modify_time desc");
        }

        PageInfo<GalleryVO> page = new PageInfo<>();

        GalleryExample example = GalleryConverter.query2Example(query);
        long totalCount = galleryDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<GalleryDO> list = galleryDAO.selectByExampleWithBLOBs(example);
        page.setList(GalleryConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public PageInfo<GalleryVO> queryFavoredGalleryByPage(GalleryQuery query) {

        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        AssertUtil.assertTrue(query.isFavored(), ResultCode.PARAM_INVALID, "favored is false");
        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("user_favor.modify_time desc");
        }

        PageInfo<GalleryVO> page = new PageInfo<>();

        GalleryExample example = GalleryConverter.query2Example(query);
        long totalCount = galleryDAO.countFavoredByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<GalleryDO> list = galleryDAO.selectFavoredByExampleWithBLOBs(example);
        page.setList(GalleryConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public PageInfo<GalleryVO> queryUsedGalleryByPage(GalleryQuery query) {

        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        AssertUtil.assertTrue(query.isUsed(), ResultCode.PARAM_INVALID, "used is invalid");

        PageInfo<GalleryVO> page = new PageInfo<>();

        GalleryExample example = GalleryConverter.query2Example(query);
        long totalCount = galleryDAO.countUsedByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<GalleryDO> list = galleryDAO.selectUsedByExampleWithBLOBs(example);
        page.setList(GalleryConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject galleryUpload(GalleryUploadRequest request) {

        List<MultipartFile> imageFiles = request.getImageFiles();
        GalleryTypeEnum type = GalleryTypeEnum.getByCode(request.getType());
        GallerySubTypeEnum subType = GallerySubTypeEnum.getByCode(request.getSubType());
        ModelTypeEnum belong = ModelTypeEnum.getByCode(request.getBelong());

        int success = 0;
        int duplicated = 0;
        int failed = 0;
        List<GalleryVO> data = new ArrayList<>();
        Integer userId = OperationContextHolder.getMasterUserId();
        Integer operatorId = OperationContextHolder.getOperatorUserId();

        for (MultipartFile file : imageFiles) {
            try {
                // 1. 计算 md5
                String md5 = CommonUtil.calculateMD5(file.getBytes());

                // 2. 检查重复
                // 如果图片已存在, 执行更新逻辑, 理论上只会有一个
                GalleryVO existed = hasExisted(userId, operatorId, md5, belong.getCode(), type.getCode());
                if (ObjectUtils.isNotEmpty(existed)) {
                    existed.addExtInfo(KEY_TAGS, request.getTags());
                    // 收集结果
                    data.add(existed);
                    this.updateByIdSelective(existed);
                    duplicated++;
                    continue;
                }

                // 3. 图片上传
                String imageUrl = uploadOss(file);
                GalleryVO gallery = GalleryConverter.buildVO(
                        userId, operatorId, type, subType, imageUrl, md5, belong, request.getTags());

                // 4. 插入数据
                data.add(this.insert(gallery));
                success++;

            } catch (IOException e) {
                failed++;
                log.error("图库上传失败");
                log.error(e.getMessage(), e);
            }
        }
        JSONObject result = new JSONObject();
        result.put("success", success);
        result.put("duplicated", duplicated);
        result.put("failed", failed);
        if (request.isWithResult()) {
            result.put("data", data);
        }
        return result;
    }

    /**
     * 查询当前用户(或系统)现有的图片
     * 用户上传在 同一个 type, 同一个 user 下不重复,
     * 系统上传在 同一个 type 下不重复
     */
    private GalleryVO hasExisted(Integer userId, Integer operatorId, String md5, String belong, String type) {
        GalleryQuery query = new GalleryQuery();
        if (!StringUtils.equals(ModelTypeEnum.SYSTEM.getCode(), belong)) {
            query.setUserId(userId);
            query.setOperatorId(operatorId);
        }
        query.setType(type);
        query.setMd5(md5);
        query.setBelong(belong);
        List<GalleryVO> list = queryGalleryList(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.getFirst();
    }

    private String uploadOss(MultipartFile file) throws IOException {
        AssertUtil.assertNotNull(file, ResultCode.BIZ_FAIL, "file is null");
        AssertUtil.assertTrue(StringUtils.startsWith(file.getContentType(), "image/"), ResultCode.BIZ_FAIL, "contentType is not valid");

        String ext = FileUtils.getExtension(file);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        String fileName = sdf.format(new Date()) + OperationContextHolder.getMasterUserId() + "/" + CommonUtil.uuid() + ext;
        String url = ossService.upload(fileName, file.getInputStream());
        AssertUtil.assertNotBlank(url, "文件上传失败，返回的url为空");
        return url;
    }

}
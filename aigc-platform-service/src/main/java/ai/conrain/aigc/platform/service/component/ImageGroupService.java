package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageGroupExportReq;
import ai.conrain.aigc.platform.service.model.query.ImageGroupExportResponse;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ClothInfoVO;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 图像组，由多种图组成的pair对 Service定义
 *
 * <AUTHOR>
 * @version ImageGroupService.java v 0.1 2025-07-30 08:19:30
 */
public interface ImageGroupService {

	/**
	 * 查询图像组，由多种图组成的pair对对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageGroupVO selectById(Integer id);

	/**
	 * 删除图像组，由多种图组成的pair对对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像组，由多种图组成的pair对对象
	 * @param imageGroup 对象参数
	 * @return 返回结果
	 */
	ImageGroupVO insert(ImageGroupVO imageGroup);

	/**
	 * 修改图像组，由多种图组成的pair对对象
	 * @param imageGroup 对象参数
	 */
	void updateByIdSelective(ImageGroupVO imageGroup);

	/**
	 * 带条件批量查询图像组，由多种图组成的pair对列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageGroupVO> queryImageGroupList(ImageGroupQuery query);

	/**
	 * 带条件查询图像组，由多种图组成的pair对数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageGroupCount(ImageGroupQuery query);

	/**
	 * 带条件分页查询图像组，由多种图组成的pair对
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageGroupVO> queryImageGroupByPage(ImageGroupQuery query);

    /**
     * 根据 MaterialModel id 更新服装
     * @param imageGroupId
     * @param clothId
     */
    void updateByClothId(@NotNull Integer imageGroupId, @NotNull Integer clothId);

    /**
     * 获取服装信息
     *
     * @param id
     * @return
     */
    ClothInfoVO getClothInfoById(Integer id);

    /**
     * 导出匹配对数据
     *
     * @param query
     * @return
     */
    List<ImageGroupExportResponse> export(ImageGroupExportReq query);

    /**
     * 分页查询tryon数据
     *
     * @param query
     * @return
     */
    PageInfo<ImageGroupVO> queryTryonByPage(ImageQuery query);
}
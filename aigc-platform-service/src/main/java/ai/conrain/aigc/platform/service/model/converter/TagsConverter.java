package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TagsDO;
import ai.conrain.aigc.platform.service.model.query.TagsQuery;
import ai.conrain.aigc.platform.dal.example.TagsExample;
import ai.conrain.aigc.platform.service.model.vo.TagsVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * TagsConverter
 *
 * @version TagsService.java v 0.1 2024-05-22 08:28:41
 */
public class TagsConverter {

    /**
     * DO -> VO
     */
    public static TagsVO do2VO(TagsDO from) {
        TagsVO to = new TagsVO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setName(from.getName());
        to.setDefChecked(from.getDefChecked());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetail(from.getDetail());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TagsDO vo2DO(TagsVO from) {
        TagsDO to = new TagsDO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setName(from.getName());
        to.setDefChecked(from.getDefChecked());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetail(from.getDetail());

        return to;
    }

    /**
     * DO -> Query
     */
    public static TagsQuery do2Query(TagsDO from) {
        TagsQuery to = new TagsQuery();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setName(from.getName());
        to.setDefChecked(from.getDefChecked());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetail(from.getDetail());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TagsDO query2DO(TagsQuery from) {
        TagsDO to = new TagsDO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setName(from.getName());
        to.setDefChecked(from.getDefChecked());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetail(from.getDetail());

        return to;
    }


    /**
     * Query -> Example
     */
    public static TagsExample query2Example(TagsQuery from) {
        TagsExample to = new TagsExample();
        TagsExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getDefChecked())) {
            c.andDefCheckedEqualTo(from.getDefChecked());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<TagsVO> doList2VOList(List<TagsDO> list) {
        return CommonUtil.listConverter(list, TagsConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.TagsDO;
import ai.conrain.aigc.platform.service.model.query.TagsQuery;
import ai.conrain.aigc.platform.service.model.vo.TagsVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 标签配置 Service定义
 *
 * <AUTHOR>
 * @version TagsService.java v 0.1 2024-05-22 08:28:41
 */
public interface TagsService {
	
	/**
	 * 查询标签配置对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TagsVO selectById(Integer id);

	/**
	 * 删除标签配置对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加标签配置对象
	 * @param tags 对象参数
	 * @return 返回结果
	 */
	TagsVO insert(TagsVO tags);

	/**
	 * 修改标签配置对象
	 * @param tags 对象参数
	 */
	void updateByIdSelective(TagsVO tags);

	/**
	 * 带条件批量查询标签配置列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<TagsVO> queryTagsList(TagsQuery query);

	/**
	 * 带条件查询标签配置数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryTagsCount(TagsQuery query);

	/**
	 * 带条件分页查询标签配置
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<TagsVO> queryTagsByPage(TagsQuery query);
}
package ai.conrain.aigc.platform.service.constants;

/** 埋点相关缓存 */
public interface TrackEventCachePrefix {

    // 基础前缀
    static final String BASE_PREFIX = "eventTrackingRecord:";

    // 查询埋点列表记录
    public static final String TRACK_EVENT_LIST = BASE_PREFIX + "trackEventList";

    // 等待缓存埋点列表记录
    public static final String WAIT_CACHE_TRACK_EVENT_LIST = BASE_PREFIX + "waitCacheTrackEventList";

    // 等待缓存埋点列表记录 缓存失效时间（1小时）
    public static final Integer WAIT_CACHE_TRACK_EVENT_LIST_TIMEOUT = 60 * 60;

}

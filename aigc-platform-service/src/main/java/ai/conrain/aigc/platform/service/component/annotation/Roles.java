package ai.conrain.aigc.platform.service.component.annotation;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 角色权限注解
 * <p>
 * 用于标记Controller接口，自动从@RequestMapping提取路径信息生成权限配置
 * </p>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Roles {
    /**
     * 允许访问的角色类型数组，默认管理员可访问
     */
    RoleTypeEnum[] value() default {RoleTypeEnum.ADMIN};

    /**
     * 版本号，格式为：YYYYMMDD.N，日期+自增号格式的版本号，用于控制权限的更新
     *
     * @return 版本号
     */
    String version() default "00000000.0";
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.stats;

import java.io.Serializable;

import lombok.Data;

/**
 * 交付统计项
 *
 * <AUTHOR>
 * @version : DeliveryStatsItem.java, v 0.1 2024/9/14 11:59 renxiao.wu Exp $
 */
@Data
public class DeliveryStatsItem implements Serializable {
    private static final long serialVersionUID = -5236542178372989737L;
    private int total;
    private int todo;
    private int in24;
    private int in48;
    private int other;
    private int unknown;

    public DeliveryStatsItem() {
    }

    public DeliveryStatsItem(int total, int in24, int in48, int other, int unknown) {
        this.total = total;
        this.in24 = in24;
        this.in48 = in48;
        this.other = other;
        this.unknown = unknown;
        this.todo = total - in24 - in48 - other - unknown;
    }
}

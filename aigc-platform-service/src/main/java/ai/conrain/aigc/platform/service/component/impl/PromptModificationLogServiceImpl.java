package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.PromptModificationLogDO;
import ai.conrain.aigc.platform.dal.example.PromptModificationLogExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.PromptModificationLogQuery;
import ai.conrain.aigc.platform.service.model.vo.PromptModificationLogVO;
import ai.conrain.aigc.platform.service.model.converter.PromptModificationLogConverter;
import ai.conrain.aigc.platform.dal.dao.PromptModificationLogDAO;
import ai.conrain.aigc.platform.service.component.PromptModificationLogService;

/**   
 * PromptModificationLogService实现
 *
 * <AUTHOR>
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
@Slf4j
@Service
public class PromptModificationLogServiceImpl implements PromptModificationLogService {

	/** DAO */
	@Autowired
	private PromptModificationLogDAO promptModificationLogDAO;

	@Override
	public PromptModificationLogVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		PromptModificationLogDO data = promptModificationLogDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return PromptModificationLogConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = promptModificationLogDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除PromptModificationLog失败");
	}

	@Override
	public PromptModificationLogVO insert(PromptModificationLogVO promptModificationLog) {
		AssertUtil.assertNotNull(promptModificationLog, ResultCode.PARAM_INVALID, "promptModificationLog is null");
		AssertUtil.assertTrue(promptModificationLog.getId() == null, ResultCode.PARAM_INVALID, "promptModificationLog.id is present");

		//创建时间、修改时间兜底
		if (promptModificationLog.getOperationTime() == null) {
			promptModificationLog.setOperationTime(new Date());
		}


		PromptModificationLogDO data = PromptModificationLogConverter.vo2DO(promptModificationLog);
		Integer n = promptModificationLogDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建PromptModificationLog失败");
		AssertUtil.assertNotNull(data.getId(), "新建PromptModificationLog返回id为空");
		promptModificationLog.setId(data.getId());
		return promptModificationLog;
	}


	@Override
	public void updateByIdSelective(PromptModificationLogVO promptModificationLog) {
		AssertUtil.assertNotNull(promptModificationLog, ResultCode.PARAM_INVALID, "promptModificationLog is null");
    	AssertUtil.assertTrue(promptModificationLog.getId() != null, ResultCode.PARAM_INVALID, "promptModificationLog.id is null");

		//修改时间必须更新
		promptModificationLog.setOperationTime(new Date());
		PromptModificationLogDO data = PromptModificationLogConverter.vo2DO(promptModificationLog);
		int n = promptModificationLogDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新PromptModificationLog失败，影响行数:" + n);
	}

	@Override
	public List<PromptModificationLogVO> queryPromptModificationLogList(PromptModificationLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PromptModificationLogExample example = PromptModificationLogConverter.query2Example(query);

		List<PromptModificationLogDO> list = promptModificationLogDAO.selectByExample(example);
			return PromptModificationLogConverter.doList2VOList(list);
	}

	@Override
	public Long queryPromptModificationLogCount(PromptModificationLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PromptModificationLogExample example = PromptModificationLogConverter.query2Example(query);
		long c = promptModificationLogDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询修改记录
	 */
	@Override
	public PageInfo<PromptModificationLogVO> queryPromptModificationLogByPage(PromptModificationLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<PromptModificationLogVO> page = new PageInfo<>();

		PromptModificationLogExample example = PromptModificationLogConverter.query2Example(query);
		long totalCount = promptModificationLogDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<PromptModificationLogDO> list = promptModificationLogDAO.selectByExample(example);
		page.setList(PromptModificationLogConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component;

/**
 * 密码服务
 *
 * <AUTHOR>
 * @version : PasswordService.java, v 0.1 2023/9/4 12:00 renxiao.wu Exp $
 */
public interface SecurityService {
    /**
     * 对密码进行加密
     *
     * @param password 原始密码
     * @return 加密后密码
     */
    String encodePassword(String password);

    /**
     * 验证密码
     *
     * @param password        原始密码
     * @param encodedPassword 加密后密码
     * @return 是否一致，true一致
     */
    boolean matchePassword(String password, String encodedPassword);

    /**
     * 生成随机key值
     *
     * @return
     */
    String genRandomKey();

    /**
     * 通过AES+盐值加密
     *
     * @param target 目标加密值
     * @param salt   盐值
     * @return 加密后字符串
     */
    String encrypt(String target, String salt);

    /**
     * 通过AES+盐值解密
     *
     * @param target 目标解密值
     * @param salt   盐值
     * @return 解密后字符串
     */
    String decrypt(String target, String salt);
}

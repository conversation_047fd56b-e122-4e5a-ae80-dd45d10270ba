package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.CustomerActivityStatsDO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CustomerActivityLogQuery;
import ai.conrain.aigc.platform.service.model.vo.CustomerActivityLogVO;
import java.util.List;

/**
 * 客户活跃记录 Service定义
 *
 * <AUTHOR>
 * @version CustomerActivityLogService.java
 */
public interface CustomerActivityLogService {

    /**
     * 查询客户活跃记录对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CustomerActivityLogVO selectById(Integer id);

    /**
     * 删除客户活跃记录对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加客户活跃记录对象
     *
     * @param customerActivityLog 对象参数
     * @return 返回结果
     */
    CustomerActivityLogVO insert(CustomerActivityLogVO customerActivityLog);

    /**
     * 修改客户活跃记录对象
     *
     * @param customerActivityLog 对象参数
     */
    void updateByIdSelective(CustomerActivityLogVO customerActivityLog);

    /**
     * 带条件批量查询客户活跃记录列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<CustomerActivityLogVO> queryCustomerActivityLogList(CustomerActivityLogQuery query);

    /**
     * 带条件查询客户活跃记录数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryCustomerActivityLogCount(CustomerActivityLogQuery query);

    /**
     * 带条件分页查询客户活跃记录
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<CustomerActivityLogVO> queryCustomerActivityLogByPage(CustomerActivityLogQuery query);

    /**
     * 初始化客户活跃记录
     */
    void initDaily();

    /**
     * 删除指定日期的记录
     *
     * @param dt 日期
     */
    void deleteByDt(String dt);

    /**
     * 统计销售区域活跃度数据
     *
     * @param groupType  分组类型
     * @param onlyDirect 是否只展示直营
     * @return 统计数据
     */
    List<CustomerActivityStatsDO> statsBySalesArea(String groupType, Boolean onlyDirect);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import javax.annotation.PostConstruct;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 创作服务工厂类
 *
 * <AUTHOR>
 * @version : CreativeServiceFactory.java, v 0.1 2024/7/18 15:57 renxiao.wu Exp $
 */
@Slf4j
@Service("creativeService")
public class CreativeServiceFactory {
    @Autowired
    private ApplicationContext applicationContext;

    /** 构建器map */
    @SuppressWarnings("rawtypes")
    private final Map<CreativeTypeEnum, CreativeService> serviceMap = new HashMap<>();

    /**
     * 执行创作服务
     *
     * @param type    类型
     * @param request 请求
     * @param <T>     请求类型
     * @return 结果
     * @throws IOException 异常
     */
    @SuppressWarnings("unchecked")
    public <T extends CreativeRequest> CreativeBatchVO create(CreativeTypeEnum type, T request) throws IOException {
        return serviceMap.get(type).create(request);
    }

    /**
     * 构建创作任务
     *
     * @param batch    创作批次
     * @param elements 创作元素
     * @return 创作任务列表
     */
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        //noinspection unchecked
        return serviceMap.get(batch.getType()).buildTasks(batch, elements);
    }

    /**
     * 构建创作流程
     *
     * @param task     创作任务
     * @param modelVO  模型
     * @param elements 创作元素
     * @return 流程参数
     */
    @SuppressWarnings("unchecked")
    public String buildFlow(CreativeTaskVO task, MaterialModelVO modelVO, List<CreativeElementVO> elements) {
        return serviceMap.get(task.getType()).buildFlow(task, modelVO, elements);
    }

    @SuppressWarnings("unchecked")
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return serviceMap.get(task.getType()).correctFlow(flow, context, task, flowKey);
    }

    @SuppressWarnings("rawtypes")
    @PostConstruct
    private void init() {
        Map<String, CreativeService> servers = applicationContext.getBeansOfType(CreativeService.class);

        for (CreativeService service : servers.values()) {
            serviceMap.put(service.getCreativeType(), service);
        }

        log.info("creativeServiceFactory init success, serviceMap: {}", serviceMap.size());
    }
}

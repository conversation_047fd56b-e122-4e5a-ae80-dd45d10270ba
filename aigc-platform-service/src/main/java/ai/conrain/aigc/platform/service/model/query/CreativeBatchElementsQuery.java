package ai.conrain.aigc.platform.service.model.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * CreativeBatchElementsQuery
 *
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
@Data
public class CreativeBatchElementsQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 批次id */
    private Integer id;

    /** 模型id */
    private Integer batchId;

    /** 元素id */
    private Integer elementId;

    private String elementKey;

    /** 归属主账号id */
    private Integer userId;

    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private List<String> includesTypes;

    private List<String> excludesTypes;

    /** 只查询Lora文件 */
    private Boolean onlyLora;

    /** 打标类型 */
    private String labelType;

    /** 打标类型不等于 */
    private String labelTypeNotEqual;
}

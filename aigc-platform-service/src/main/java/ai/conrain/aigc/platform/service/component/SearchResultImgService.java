package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.SearchResultImgQuery;
import ai.conrain.aigc.platform.service.model.vo.SearchResultImgVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 搜索结果图片 Service定义
 *
 * <AUTHOR>
 * @version SearchResultImgService.java
 */
public interface SearchResultImgService {
	
	/**
	 * 查询搜索结果图片对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	SearchResultImgVO selectById(Integer id);

	/**
	 * 删除搜索结果图片对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加搜索结果图片对象
	 * @param searchResultImg 对象参数
	 * @return 返回结果
	 */
	SearchResultImgVO insert(SearchResultImgVO searchResultImg);

	/**
	 * 修改搜索结果图片对象
	 * @param searchResultImg 对象参数
	 */
	void updateByIdSelective(SearchResultImgVO searchResultImg);

	/**
	 * 带条件批量查询搜索结果图片列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<SearchResultImgVO> querySearchResultImgList(SearchResultImgQuery query);

	/**
	 * 带条件查询搜索结果图片数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long querySearchResultImgCount(SearchResultImgQuery query);

	/**
	 * 带条件分页查询搜索结果图片
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<SearchResultImgVO> querySearchResultImgByPage(SearchResultImgQuery query);

	/**
	 * 批量插入搜索结果图片
	 * @param searchResultImgs 搜索结果图片列表
	 * @return 插入成功的记录数
	 */
	int batchInsert(List<SearchResultImgVO> searchResultImgs);
}
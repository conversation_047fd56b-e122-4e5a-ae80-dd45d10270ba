package ai.conrain.aigc.platform.service.component.dispatch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.annotation.PostConstruct;

import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 分发服务工厂类
 */
@Slf4j
@Service("dispatchService")
public class DispatchServiceFactory {

    @Autowired
    private ApplicationContext applicationContext;

    /** 构建器map */
    private final Map<DispatchTypeEnum, TaskDispatch> serviceMap = new HashMap<>();

    /**
     * 执行分发服务
     *
     * @param type     类型
     * @param extModel 任务
     * @return 结果
     */
    public <T extends IExtModel> String dispatch(DispatchTypeEnum type, T extModel) {
        return serviceMap.get(type).dispatch(extModel);
    }

    /**
     * 执行分发服务
     *
     * @param type     类型
     * @param extModel 任务
     * @return 结果
     */
    public <T extends IExtModel> String dispatch(DispatchTypeEnum type, T extModel, ServerVO server) {
        return serviceMap.get(type).dispatch(extModel, server);
    }

    /**
     * 查询空闲服务器
     *
     * @param type 类型
     * @return 服务器列表
     */
    public List<PipelineVO> queryIdleServer(DispatchTypeEnum type) {
        return serviceMap.get(type).queryIdleServer();
    }

    /**
     * 释放服务器
     */
    public <T extends IExtModel> void release(DispatchTypeEnum type, T extModel) {
        serviceMap.get(type).release(extModel);
    }

    @PostConstruct
    private void init() {
        Map<String, TaskDispatch> servers = applicationContext.getBeansOfType(TaskDispatch.class);

        for (TaskDispatch service : servers.values()) {
            serviceMap.put(service.getType(), service);
        }

        log.info("dispatchServiceFactory init success, serviceMap: {}", serviceMap.size());
    }

}

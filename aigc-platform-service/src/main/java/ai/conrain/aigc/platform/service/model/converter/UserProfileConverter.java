package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserProfileDO;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.dal.example.UserProfileExample;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * UserProfileConverter
 *
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
public class UserProfileConverter {

    /**
     * DO -> VO
     */
    public static UserProfileVO do2VO(UserProfileDO from) {
        UserProfileVO to = new UserProfileVO();
        to.setId(from.getId());
        to.setUid(from.getUid());
        to.setProfileKey(from.getProfileKey());
        to.setProfileVal(from.getProfileVal());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserProfileDO vo2DO(UserProfileVO from) {
        UserProfileDO to = new UserProfileDO();
        to.setId(from.getId());
        to.setUid(from.getUid());
        to.setProfileKey(from.getProfileKey());
        to.setProfileVal(from.getProfileVal());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserProfileQuery do2Query(UserProfileDO from) {
        UserProfileQuery to = new UserProfileQuery();
        to.setId(from.getId());
        to.setUid(from.getUid());
        to.setProfileKey(from.getProfileKey());
        to.setProfileVal(from.getProfileVal());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserProfileDO query2DO(UserProfileQuery from) {
        UserProfileDO to = new UserProfileDO();
        to.setId(from.getId());
        to.setUid(from.getUid());
        to.setProfileKey(from.getProfileKey());
        to.setProfileVal(from.getProfileVal());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static UserProfileExample query2Example(UserProfileQuery from) {
        UserProfileExample to = new UserProfileExample();
        UserProfileExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUid())) {
            c.andUidEqualTo(from.getUid());
        }
        if (!ObjectUtils.isEmpty(from.getProfileKey())) {
            c.andProfileKeyEqualTo(from.getProfileKey());
        }
        if (!ObjectUtils.isEmpty(from.getProfileKeys())) {
            c.andProfileKeyIn(from.getProfileKeys());
        }
        if (!ObjectUtils.isEmpty(from.getProfileVal())) {
            c.andProfileValEqualTo(from.getProfileVal());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (CollectionUtils.isNotEmpty(from.getUids())) {
            c.andUidIn(from.getUids());
        }
        if (!ObjectUtils.isEmpty(from.getProfileValList())){
            c.andProfileValIn(from.getProfileValList());
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<UserProfileVO> doList2VOList(List<UserProfileDO> list) {
        return CommonUtil.listConverter(list, UserProfileConverter::do2VO);
    }
}
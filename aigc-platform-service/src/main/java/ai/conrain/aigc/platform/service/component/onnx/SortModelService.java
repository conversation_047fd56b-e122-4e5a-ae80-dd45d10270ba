package ai.conrain.aigc.platform.service.component.onnx;

import ai.conrain.aigc.platform.service.model.biz.agent.ProcessedInput;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import com.pgvector.PGvector;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class SortModelService {

    private SimpleOnnxWrapper onnxWrapper;
    private static final String ONNX_MODEL_PATH = "onnx/agent_sort_model.onnx";
    private static final int BATCH_SIZE = 32;

    @PostConstruct
    public void init() {
        onnxWrapper = new SimpleOnnxWrapper(ONNX_MODEL_PATH, "排序模型服务");
        onnxWrapper.init();
    }

    @PreDestroy
    public void destroy() {
        if (onnxWrapper != null) {
            onnxWrapper.destroy();
        }
    }

    /**
     * 批量计算匹配分数
     */
    public void calculateMatchScoresBatch(List<StyleImageCandidate> candidates, ProcessedInput input)
            throws OrtException {
        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        log.info("开始批量计算匹配分数，候选数量: {}", candidates.size());

        // 分批处理
        for (int i = 0; i < candidates.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, candidates.size());
            List<StyleImageCandidate> batch = candidates.subList(i, endIndex);
            batchPredictScores(batch, input);
        }

        log.info("批量计算匹配分数完成");
    }

    /**
     * 批量预测分数
     */
    private void batchPredictScores(List<StyleImageCandidate> batch, ProcessedInput input) throws OrtException {
        // 构建批量输入嵌入
        float[][][] batchInputEmbeddings = buildBatchOnnxInputEmbeddings(batch, input);

        // 批量推理
        OnnxTensor inputTensor = OnnxTensor.createTensor(onnxWrapper.getEnvironment(), batchInputEmbeddings);
        OrtSession.Result result = onnxWrapper.getSession().run(Collections.singletonMap("input_embeddings", inputTensor));

        // 获取批量预测结果
        float[][] batchPredictedScores = (float[][]) result.get("combined_probabilities").get().getValue();

        // 将结果分配给对应的候选项
        for (int j = 0; j < batch.size(); j++) {
            StyleImageCandidate candidate = batch.get(j);
            float[] predictedScores = batchPredictedScores[j];
            candidate.setMatchScore(predictedScores[0]);
        }

        // 释放资源
        inputTensor.close();
        result.close();
    }

    /**
     * 批量构建ONNX模型输入嵌入
     */
    private float[][][] buildBatchOnnxInputEmbeddings(List<StyleImageCandidate> candidates, ProcessedInput input) {
        int batchSize = candidates.size();

        // 获取基础嵌入（所有候选项共享）
        PGvector clothTextVector = input.getClothDescription().getClothTextVector();
        PGvector clothImgVector = input.getClothDescription().getClothImgVector();

        // 构建批量输入：[batch_size, experts, embedding_dim]
        float[][][] batchInputEmbeddings = new float[batchSize][8][2048];

        for (int i = 0; i < batchSize; i++) {
            StyleImageCandidate candidate = candidates.get(i);
            ImageCaptionVO imageCaption = candidate.getImageCaption();

            // 【重要】以下顺序严格按照训练模型要求，不能随意调整
            // 前8个专家输入：clothTextVector与其他8个嵌入的组合
            setExpertInput(batchInputEmbeddings[i][0], clothTextVector, imageCaption.getBgImgEmb());
            setExpertInput(batchInputEmbeddings[i][1], clothTextVector, imageCaption.getBgTextEmb());
            setExpertInput(batchInputEmbeddings[i][2], clothTextVector, imageCaption.getModelFacialImgEmb());
            setExpertInput(batchInputEmbeddings[i][3], clothTextVector, imageCaption.getSortFacialExpressionTextEmb());
            setExpertInput(batchInputEmbeddings[i][4], clothTextVector, imageCaption.getSortAccessoriesTextEmb());
            setExpertInput(batchInputEmbeddings[i][5], clothTextVector, imageCaption.getImgEmb());
            setExpertInput(batchInputEmbeddings[i][6], clothTextVector, imageCaption.getModelPoseImgEmb());
            setExpertInput(batchInputEmbeddings[i][7], clothTextVector, imageCaption.getPoseTextEmb());

            // 后8个专家输入：clothImgVector与其他8个嵌入的组合（当前模型未使用）
            // setExpertInput(batchInputEmbeddings[i][8], clothImgVector,
            // imageCaption.getBgImgEmb());

            // 后8个专家输入：clothImgVector与其他8个嵌入的组合
            // setExpertInput(inputEmbeddings[i][9], clothImgVector,
            // imageCaption.getBgTextEmb());
            // setExpertInput(inputEmbeddings[i][10], clothImgVector,
            // imageCaption.getModelFacialImgEmb());
            // setExpertInput(inputEmbeddings[i][11], clothImgVector,
            // imageCaption.getSortFacialExpressionTextEmb());
            // setExpertInput(inputEmbeddings[i][12], clothImgVector,
            // imageCaption.getSortAccessoriesTextEmb());
            // setExpertInput(inputEmbeddings[i][13], clothImgVector,
            // imageCaption.getImgEmb());
            // setExpertInput(inputEmbeddings[i][14], clothImgVector,
            // imageCaption.getModelPoseImgEmb());
            // setExpertInput(inputEmbeddings[i][15], clothImgVector,
            // imageCaption.getPoseTextEmb());
        }

        return batchInputEmbeddings;
    }

    /**
     * 设置专家输入：将两个1024维向量连接成一个2048维向量
     */
    private void setExpertInput(float[] expertInput, PGvector vector1, PGvector vector2) {
        // 处理第一个向量
        if (vector1 != null) {
            float[] array1 = vector1.toArray();
            System.arraycopy(array1, 0, expertInput, 0, Math.min(array1.length, 1024));
        }

        // 处理第二个向量
        if (vector2 != null) {
            float[] array2 = vector2.toArray();
            System.arraycopy(array2, 0, expertInput, 1024, Math.min(array2.length, 1024));
        }
    }
}
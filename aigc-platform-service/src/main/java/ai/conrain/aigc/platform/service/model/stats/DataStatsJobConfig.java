package ai.conrain.aigc.platform.service.model.stats;

import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * 数据统计任务配置类
 */
@Data
public class DataStatsJobConfig {

    /**
     * 统计开始日期
     */
    private Date startDate;

    /**
     * 统计结束日期
     */
    private Date endDate;

    /**
     * 允许的统计周期
     */
    private final Set<StatsPeriodEnum> allowedPeriods = new HashSet<>();

    /**
     * 允许的统计业务类型
     */
    private final Set<StatsTypeEnum> allowedStatsTypes = new HashSet<>();

    /**
     * 重置所有周期
     */
    public void resetAllPeriods() {
        allowedPeriods.clear();
    }

    /**
     * 添加允许的统计周期
     */
    public void addAllowedPeriod(StatsPeriodEnum period) {
        allowedPeriods.add(period);
    }

    /**
     * 移除允许的统计周期
     */
    public void removeAllowedPeriod(StatsPeriodEnum period) {
        allowedPeriods.remove(period);
    }

    /**
     * 判断是否允许处理指定周期
     */
    public boolean isAllowedPeriod(StatsPeriodEnum period) {
        return allowedPeriods.contains(period);
    }

    /**
     * 获取允许的周期列表字符串
     */
    public String getAllowedPeriodsString() {
        return allowedPeriods.stream()
                .map(StatsPeriodEnum::name)
                .collect(Collectors.joining(", "));
    }

    /**
     * 获取允许的统计业务类型列表字符串
     */
    public String getAllowedStatsTypesString() {
        return allowedStatsTypes.stream()
                .map(StatsTypeEnum::name)
                .collect(Collectors.joining(", "));
    }

    /** 
     * 添加允许的统计业务类型
     */
    public void addAllowedStatsType(StatsTypeEnum statsType) {
        allowedStatsTypes.add(statsType);
    }


    /**
     * 重置所有统计业务类型
     */
    public void resetAllStatsTypes() {
        allowedStatsTypes.clear();
    }

    /**
     * 获取默认的统计任务配置
     * @return 配置信息
     */
    public static DataStatsJobConfig getDefaultConfig() {
        // 初始化配置对象
        DataStatsJobConfig dataStatsJobConfig = new DataStatsJobConfig();

        // 获取前一天的日期
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = cal.getTime();

        // 设置日期范围（只处理前一天）
        dataStatsJobConfig.setStartDate(yesterday);
        dataStatsJobConfig.setEndDate(yesterday);

        // 设置默认的统计类型
        Calendar today = Calendar.getInstance();

        // 每天都处理日统计和总计统计
        dataStatsJobConfig.addAllowedPeriod(StatsPeriodEnum.DAILY);
        dataStatsJobConfig.addAllowedPeriod(StatsPeriodEnum.TOTAL);

        // 周一处理周统计
        if (today.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) {
            dataStatsJobConfig.addAllowedPeriod(StatsPeriodEnum.WEEKLY);
        }

        // 月初处理月统计
        if (today.get(Calendar.DAY_OF_MONTH) == 1) {
            dataStatsJobConfig.addAllowedPeriod(StatsPeriodEnum.MONTHLY);
        }

        // 季度末处理季度统计（3月31日、6月30日、9月30日、12月31日）
        int month = today.get(Calendar.MONTH);
        boolean isLastMonthOfQuarter = month == Calendar.MARCH || month == Calendar.JUNE || 
                                     month == Calendar.SEPTEMBER || month == Calendar.DECEMBER;
        // 判断是否为月份最后一天
        int lastDayOfMonth = today.getActualMaximum(Calendar.DAY_OF_MONTH);
        if (isLastMonthOfQuarter && today.get(Calendar.DAY_OF_MONTH) == lastDayOfMonth) {
            dataStatsJobConfig.addAllowedPeriod(StatsPeriodEnum.QUARTERLY);
        }

        // 添加所有统计业务类型
        StatsTypeEnum.getAllStatsTypes().forEach(dataStatsJobConfig::addAllowedStatsType);

        // 返回默认的统计任务配置
        return dataStatsJobConfig;
    }
}

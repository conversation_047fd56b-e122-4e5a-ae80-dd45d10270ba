package ai.conrain.aigc.platform.service.helper.invoice;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class InvoiceApiResponse implements Serializable {

    /**
     * 接口返回码
     */
    private int code;

    /**
     * 接口返回码对应的描述信息
     */
    private String msg;

    /**
     * 任务订单号
     */
    private String taskNo;

    /**
     * 数据部分
     */
    private InvoiceApiResponseData data;

    @Data
    public static class InvoiceApiResponseData implements Serializable {

        /**
         * 成功列表
         */
        private List<InvoiceSuccess> successList;

        /**
         * 错误列表
         */
        private List<InvoiceError> errorList;
    }

    @Data
    public static class InvoiceSuccess {

        /**
         * 单据号
         */
        @JSONField(name = "XTLSH")
        private String documentNumber;

        /**
         * 发票种类
         */
        @JSONField(name = "FPZL")
        private String invoiceType;

        /**
         * 发票代码
         */
        @JSONField(name = "FPDM")
        private String invoiceCode;

        /**
         * 发票号码
         */
        @JSONField(name = "FPHM")
        private String invoiceNumber;

        /**
         * 开票日期
         */
        @JSONField(name = "KPRQ")
        private String billingDate;

        /**
         * 机器编号
         */
        @JSONField(name = "JQBH")
        private String machineNumber;

        /**
         * 密文
         */
        @JSONField(name = "SKM")
        private String ciphertext;

        /**
         * 校验码
         */
        @JSONField(name = "JYM")
        private String verificationCode;

        /**
         * 二维码
         */
        @JSONField(name = "EWM")
        private String qrCode;

        /**
         * 电子发票下载网址
         */
        @JSONField(name = "URL")
        private String downloadUrl;

        /**
         * 开票结果
         */
        @JSONField(name = "KPJG")
        private String billingResult;

        /**
         * 开票反馈信息
         */
        @JSONField(name = "KPFK")
        private String billingFeedback;
    }

    @Data
    public static class InvoiceError {

        /**
         * 单据号
         */
        @JSONField(name = "XTLSH")
        private String documentNumber;

        /**
         * 错误信息
         */
        @JSONField(name = "ERRMSG")
        private String errMsg;
    }
}
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 单图重新抠图请求
 */
@Data
public class ReCutoutSingleImageRequest {

    @NotNull(message = "模型ID不能为空")
    private Integer modelId;

    @NotBlank(message = "图片目录不能为空")
    private String imageDirectory;

    @NotEmpty(message = "原图文件名列表不能为空")
    @Valid
    private List<@NotBlank(message = "文件名不能为空") String> imageFileName;

    @NotEmpty(message = "目标文件列表不能为空")
    @Valid
    private Map<String,String> targetLabelFiles;


    /**
     * 抠图关键词，用于手动指定抠图目标
     */
    private String cutoutKeyword;

    /**
     * 抠图图片保存尺寸，默认1024
     */
    private String imageSize;

    /**
     * 是否裁剪放大（Y/N）
     */
    private String cut4ScaleUp;

    /**
     * 是否抠人台和衣服（Y/N)
     */
    private String cutoutType;

    /**
     * 抠图模型名称
     */
    private String cutoutModel;

    /**
     * 检查阈值(数值越小，抠图强度越大）
     */
    private String detectThreshold;

    /**
     *  多色背景
     */
    private String bgMultiColor;

    /**
     * 任务id
     */
    private Integer taskId;
}

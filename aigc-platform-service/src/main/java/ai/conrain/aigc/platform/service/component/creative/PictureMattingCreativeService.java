package ai.conrain.aigc.platform.service.component.creative;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.request.PictureMattingRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PictureMattingCreativeService extends AbstractCreativeService<PictureMattingRequest> {

    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.PICTURE_MATTING;
    }

    @Override
    protected CreativeBatchVO buildData(PictureMattingRequest request, MaterialModelVO modelVO) {
        log.info(
            "[ComfyUI流程][图片抠图]PictureMattingCreativeService::buildData::开始构建CreativeBatchVO 批次数据...");
        // 构建批次对象
        CreativeBatchVO batch = CreativeBatchConverter.request2VO(request);

        try {
            // 上传图片至 ComfyUI
            String upLoadImageUrl = comfyUIHelper.upLoadImage(request.getClotheImage());

            // 添加扩展字段
            batch.addExtInfo(CommonConstants.CLOTHE_IMAGE, upLoadImageUrl);
            batch.addExtInfo(CommonConstants.CLOTHE_TYPE, request.getClotheType());
            batch.addExtInfo(CommonConstants.PROMPT, request.getPrompt());
        } catch (IOException e) {
            log.error(
                "[ComfyUI流程][图片抠图]PictureMattingCreativeService::buildData::图片上传至 ComfyUI 时出现错误::{}",
                e.getMessage());
        }

        log.info(
            "[ComfyUI流程][图片抠图]PictureMattingCreativeService::buildData::CreativeBatchVO 批次数据构建完成:{}",
            batch);

        // 返回结果
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        target.addExtInfo(CommonConstants.CLOTHE_IMAGE, batch.getStringFromExtInfo(CommonConstants.CLOTHE_IMAGE));
        target.addExtInfo(CommonConstants.CLOTHE_TYPE, batch.getStringFromExtInfo(CommonConstants.CLOTHE_TYPE));
        target.addExtInfo(CommonConstants.PROMPT, batch.getStringFromExtInfo(CommonConstants.PROMPT));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(CommonConstants.CLOTHE_IMAGE, task.getStringFromExtInfo(CommonConstants.CLOTHE_IMAGE));
        context.put(CommonConstants.CLOTHE_TYPE, task.getStringFromExtInfo(CommonConstants.CLOTHE_TYPE));
        context.put(CommonConstants.PROMPT, task.getStringFromExtInfo(CommonConstants.PROMPT));
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        return SystemConstants.PICTURE_MATTING_COMFYUI_WORKFLOW;
    }

}

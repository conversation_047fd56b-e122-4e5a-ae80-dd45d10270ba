package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class Topup2UserReq {

    @NotNull
    private Integer userId;

    @NotNull
    private BigDecimal amount;

    //产品码，@see ai.conrain.aigc.platform.service.model.biz.PricePlanCode
    private String productCode;

    private String memo;

    private BigDecimal musePoint;

    private Integer creativeImgCountGave;

    //充值日期 2023-07-01
    private String topupDate;

    //@see ai.conrain.aigc.platform.service.constants.PayType
    //前端录入转账的支付方式，  { label: '已收到线下对公汇款', value: 'offlinePayment' },
    //                    { label: '已收到企业微信付款', value: 'corpWx' },
    //                    { label: '创建二维码收款', value: 'qrCode' },
    private String collectMoneyType;
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;


import lombok.Data;

/**
 * 登录请求
 *
 * <AUTHOR>
 * @version : LoginRequest.java, v 0.1 2023/9/4 11:06 renxiao.wu Exp $
 */
@Data
public class LoginRequest {
    /** 登录id */
    @NotBlank(message = "登录id不能为空")
    private String loginId;

    /** 密码 */
    @NotBlank(message = "密码不能为空")
    private String pswd;
}

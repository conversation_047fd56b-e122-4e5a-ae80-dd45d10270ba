package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.TestCaseDAO;
import ai.conrain.aigc.platform.dal.entity.TestCaseDO;
import ai.conrain.aigc.platform.dal.example.TestCaseExample;
import ai.conrain.aigc.platform.service.component.TestCaseItemService;
import ai.conrain.aigc.platform.service.component.TestCaseService;
import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TestCaseConverter;
import ai.conrain.aigc.platform.service.model.query.TestCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.EasyExcelUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * TestCaseService实现
 *
 * <AUTHOR>
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
@Slf4j
@Service
public class TestCaseServiceImpl implements TestCaseService {
    @Autowired
    private TestCaseDAO testCaseDAO;
    @Autowired
    private TestCaseItemService testCaseItemService;

    @Override
    public TestCaseVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestCaseDO data = testCaseDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return TestCaseConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testCaseDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestCase失败");
    }

    @Override
    public TestCaseVO insert(TestCaseVO testCase) {
        AssertUtil.assertNotNull(testCase, ResultCode.PARAM_INVALID, "testCase is null");
        AssertUtil.assertTrue(testCase.getId() == null, ResultCode.PARAM_INVALID, "testCase.id is present");

        //创建时间、修改时间兜底
        if (testCase.getCreateTime() == null) {
            testCase.setCreateTime(new Date());
        }

        if (testCase.getModifyTime() == null) {
            testCase.setModifyTime(new Date());
        }

        TestCaseDO data = TestCaseConverter.vo2DO(testCase);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        int n = testCaseDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestCase失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestCase返回id为空");
        testCase.setId(data.getId());
        return testCase;
    }

    @Override
    public void updateByIdSelective(TestCaseVO testCase) {
        AssertUtil.assertNotNull(testCase, ResultCode.PARAM_INVALID, "testCase is null");
        AssertUtil.assertTrue(testCase.getId() != null, ResultCode.PARAM_INVALID, "testCase.id is null");

        //修改时间必须更新
        testCase.setModifyTime(new Date());
        TestCaseDO data = TestCaseConverter.vo2DO(testCase);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = testCaseDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestCase失败，影响行数:" + n);
    }

    @Override
    public List<TestCaseVO> queryTestCaseList(TestCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestCaseExample example = TestCaseConverter.query2Example(query);

        List<TestCaseDO> list = testCaseDAO.selectByExample(example);
        return TestCaseConverter.doList2VOList(list);
    }

    @Override
    public Long queryTestCaseCount(TestCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestCaseExample example = TestCaseConverter.query2Example(query);
        return testCaseDAO.countByExample(example);
    }

    /**
     * 带条件分页查询测试用例集
     */
    @Override
    public PageInfo<TestCaseVO> queryTestCaseByPage(TestCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestCaseVO> page = new PageInfo<>();

        TestCaseExample example = TestCaseConverter.query2Example(query);
        long totalCount = testCaseDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestCaseDO> list = testCaseDAO.selectByExample(example);
        page.setList(TestCaseConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TestCaseVO importFromExcel(String name, String type, MultipartFile file) throws IOException {
        TestCaseVO testCase = new TestCaseVO();
        testCase.setName(name);
        TestCaseTypeEnum caseType = TestCaseTypeEnum.getByCode(type);
        testCase.setType(caseType);
        testCase.setUserId(OperationContextHolder.getMasterUserId());

        List<List<String>> lines = EasyExcelUtils.read(file.getInputStream(), 0);

        testCase.setCaseNum(CollectionUtils.size(lines));
        testCase = insert(testCase);

        testCaseItemService.batchInitByExcel(testCase.getId(), caseType, lines);

        return testCase;
    }

}
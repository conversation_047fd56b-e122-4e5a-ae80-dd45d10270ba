/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * 任务派发实现
 *
 * <AUTHOR>
 * @version : AbstractTaskDispatch.java, v 0.1 2024/8/6 10:03 renxiao.wu Exp $
 */
@Slf4j
public abstract class AbstractTaskDispatch implements TaskDispatch {
    private static final String LOCK_KEY_PREFIX = "_task_dispatch_lock_";
    private static final String LOCK_KEY_TASK_PREFIX = "_task_dispatch_lock_task_";
    private static final int LOCK_EXPIRE_TIME = 60 * 1000;
    @Autowired
    protected PipelineService pipelineService;
    @Autowired
    protected ServerService serverService;
    @Autowired
    protected ServerHelper serverHelper;
    @Autowired
    protected TairService tairService;
    @Autowired
    protected ComfyUIService comfyUIService;

    @Override
    public String dispatch(IExtModel task) {
        DispatchTypeEnum type = getType();

        //1.从任务的扩展信息中获取已使用的服务地址
        if (alreadyHasServer(task)) {
            return task.getStringFromExtInfo(KEY_SERVER_URL);
        }

        String taskLockKey = buildTaskLockKey(task, type);
        boolean lockTask = tairService.acquireLock(taskLockKey, LOCK_EXPIRE_TIME);

        if (!lockTask) {
            log.warn("【任务分发】当前任务获取派发锁失败，1存在并发情况，type={},taskId={}", type, task.getId());
            return null;
        }

        try {

            //2.基于当前管道的可用服务数，计算业务规则是否能命中
            Integer dispatchUserId = getDispatchUserId(task);
            PipelineVO pipeline = fetchPipeline(dispatchUserId);
            AssertUtil.assertNotNull(pipeline, ResultCode.BIZ_FAIL, "用户未配置管道" + dispatchUserId);

            //2.1.填充管道中闲置的服务和关联的用户
            List<ServerVO> idleServers = getIdleServers(pipeline);

            //2.2.如果管道中已无闲置的服务，直接返回
            if (CollectionUtils.isEmpty(idleServers)) {
                log.warn("【任务分发】未命中服务，当前管道中已无闲置的服务，直接返回,type={},taskId={},pipelineId={}", type,
                    task.getId(), pipeline.getId());
                return null;
            }

            //2.3.计算业务规则是否已排到，刨除已经在处理中的分支
            if (!task.isProcessing() && !canBizRun(task, pipeline, idleServers.size())) {
                return null;
            }

            //3.返回管道中第一个可用服务
            ServerVO server = null;

            for (ServerVO idleServer : idleServers) {
                //3.1.抢锁并保存数据
                if (lockAndCacheData(type, task, idleServer)) {
                    server = idleServer;
                    break;
                }
            }

            if (server == null) {
                log.info("【任务分发】未命中服务，未抢到redis锁或未找到符合要求的机器,type={},taskId={}", type,
                    task.getId());
                return null;
            }

            String serverUrl = serverHelper.getServerUrl(server);
            boolean clear = false;
            if (type == DispatchTypeEnum.CREATIVE_TASK) {
                clear = comfyUIService.clearHistory(serverUrl);
            }

            log.info("【任务分发】命中服务，计算获取到可用服务,type={},taskId={},serverUrl={},清理历史记录={}", type,
                task.getId(), serverUrl, clear);

            return serverUrl;
        } finally {
            tairService.releaseLock(taskLockKey);
        }
    }

    @Override
    public String dispatch(IExtModel task, ServerVO server) {
        //1.从任务的扩展信息中获取已使用的服务地址
        if (alreadyHasServer(task)) {
            return task.getStringFromExtInfo(KEY_SERVER_URL);
        }

        DispatchTypeEnum type = getType();

        String taskLockKey = buildTaskLockKey(task, type);
        boolean lockTask = tairService.acquireLock(taskLockKey, LOCK_EXPIRE_TIME);

        if (!lockTask) {
            log.warn("【任务分发】当前任务获取派发锁失败，2存在并发情况，type={},taskId={}", type, task.getId());
            return null;
        }

        try {
            if (server == null) {
                log.warn("【任务分发】未命中服务，当前服务为空,type={},taskId={}", type, task.getId());
                return null;
            }

            //2.如果没有，则使用服务

            if (!isIdle(server)) {
                log.warn("【任务分发】指定服务器的任务派发失败，当前服务器繁忙，type={},taskId={},serverId={}", type,
                    task.getId(), server.getId());
                return null;
            }

            if (!lockAndCacheData(type, task, server)) {
                log.warn("【任务分发】指定服务器的任务派发失败，当前服务器抢锁失败，type={},taskId={},serverId={}", type,
                    task.getId(), server.getId());
                return null;
            }

            log.info("【任务分发】指定服务器的任务派发成功，type={},taskId={},serverId={}", type, task.getId(),
                server.getId());

            return serverHelper.getServerUrl(server);

        } finally {
            tairService.releaseLock(taskLockKey);
        }
    }

    @Override
    public void release(IExtModel task) {
        DispatchTypeEnum type = getType();
        Integer serverId = task.getExtValue(KEY_SERVER_ID, Integer.class);
        if (serverId == null) {
            log.warn("【任务分发】释放服务时，从任务中未找到对应的服务id,type={},taskId={}", type, task.getId());
            return;
        }

        ServerCache cache = serverHelper.getFromTair(serverId);
        if (cache == null) {
            log.info("【任务分发】释放服务时，从缓存中未找到对应的服务缓存,type={},taskId={}", type, task.getId());
            return;
        }

        if (cache.getStatus() == ServerStatusEnum.IDLE || cache.getTaskId() == null || !cache.getTaskId().equals(
            task.getId())) {
            log.info(
                "【任务分发】释放服务时，缓存中的任务id与当前任务id不一致，不需要释放,taskId={},cacheTaskId={},status={}",
                task.getId(), cache.getTaskId(), cache.getStatus());
            return;
        }

        ServerVO server = serverService.queryByKey(serverId);
        log.info("【任务分发】释放服务成功，taskId={},serverUrl={}", task.getId(), serverHelper.getServerUrl(server));

        serverHelper.putToTair(serverId, ServerStatusEnum.IDLE);
    }

    @Override
    public List<PipelineVO> queryIdleServer() {
        List<PipelineVO> list = pipelineService.queryAll();
        list.forEach(e -> e.setIdleServers(getIdleServers(e)));
        return list;
    }

    /**
     * 查询未被处理的数据
     *
     * @param pipelineId 管道id
     * @param idleNum    空闲服务数
     * @return 未被处理的数据列表
     */
    protected abstract List<? extends IExtModel> queryUnProcessedData(Integer pipelineId, int idleNum);

    /**
     * 获取分发任务的用户id
     *
     * @param task 任务
     * @return 用户id
     */
    protected Integer getDispatchUserId(IExtModel task) {
        return task.getUserId();
    }

    /**
     * 判断是否需要重置服务状态
     *
     * @param taskId 任务id
     * @return true，需要重置
     */
    protected abstract boolean isTaskFinished(Integer taskId);

    /**
     * 获取当前任务所在的管道
     *
     * @param userId 用户id
     * @return 管道
     */
    protected PipelineVO fetchPipeline(Integer userId) {
        return pipelineService.fetchByUserId(userId);
    }

    /**
     * 业务决策是否能执行
     *
     * @param task     任务
     * @param pipeline 管道
     * @param idleNum  空闲服务数
     * @return true，可以执行
     */
    protected boolean canBizRun(IExtModel task, PipelineVO pipeline, int idleNum) {
        List<? extends IExtModel> list = queryUnProcessedData(pipeline.getId(), idleNum);

        if (CollectionUtils.isEmpty(list)) {
            log.info(
                "【任务分发】判断业务是否能执行：false,获取不到可执行的批次列表，可能都已经被processing状态占用了，type={},id={},limit={}",
                getType(), task.getId(), idleNum);
            return false;
        }

        if (list.stream().anyMatch(item -> item.getId().equals(task.getId()))) {
            log.info("【任务分发】判断业务是否能执行：true,可执行的批次列表中包含当前批次，type={},id={},limit={}",
                getType(), task.getId(), idleNum);
            return true;
        }

        log.info("【任务分发】判断业务是否能执行：false,可执行的批次列表中不包含当前批次，type={},id={},limit={}",
            getType(), task.getId(), idleNum);

        return false;
    }

    /**
     * 机器要求检查
     *
     * @param type   业务类型
     * @param task   任务数据
     * @param server 当前遍历服务信息
     * @return true，check通过
     */
    @SuppressWarnings("BooleanMethodIsAlwaysInverted")
    protected boolean checkMachineMatch(DispatchTypeEnum type, IExtModel task, ServerVO server) {
        return true;
    }

    /**
     * 是否已经存在可用的服务
     *
     * @param task 任务
     * @return true，已存在
     */
    private boolean alreadyHasServer(IExtModel task) {
        DispatchTypeEnum type = getType();
        String serverIdStr = task.getStringFromExtInfo(KEY_SERVER_ID);
        if (StringUtils.isBlank(serverIdStr)) {
            return false;
        }

        int serverId = Integer.parseInt(serverIdStr);

        //1.1.服务状态检查
        ServerVO server = serverService.queryByKey(serverId);
        if (!serverHelper.isEnable(server)) {
            //服务状态不可用，重新计算获取服务
            log.warn("【任务分发】从任务扩展中获取到服务，但服务已停用，重新计算可用服务,type={},taskId={},server={}", type,
                task.getId(), server);
            return false;
        }

        ServerCache cache = serverHelper.getFromTair(serverId);
        //1.2.如果服务缓存已存在，且当前任务id与缓存中的任务id一致，则刷新缓存时间
        if (cache != null && cache.getTaskId() != null && cache.getTaskId().equals(task.getId()) && cache.isAvailable()
            && cache.getType() == getType()) {
            cache.refreshTime();
            serverHelper.putToTair(serverId, cache);

            log.info("【任务分发】命中服务，从任务扩展中获取到的可用服务,type={},taskId={},serverIdStr={}", type,
                task.getId(), serverIdStr);

            return true;
        }

        //1.3.否则跳过，重新计算获取服务
        log.warn("【任务分发】从任务扩展中获取到服务，但服务可能已经过期，重新计算可用服务,type={},taskId={},cache={}",
            type, task.getId(), cache);

        return false;
    }

    /**
     * 构建任务锁key
     *
     * @param task 任务
     * @param type 类型
     * @return key
     */
    private static String buildTaskLockKey(IExtModel task, DispatchTypeEnum type) {
        return LOCK_KEY_TASK_PREFIX + type.getCode() + "_" + task.getId();
    }

    /**
     * 获取当前管道中的闲置服务
     *
     * @param pipeline 管道
     */
    private List<ServerVO> getIdleServers(PipelineVO pipeline) {
        List<ServerVO> result = new ArrayList<>();
        List<ServerVO> servers = serverHelper.getServersByType(getType(), pipeline);
        servers.forEach(each -> {
            if (isIdle(each)) {
                result.add(each);
            }
        });

        log.info("【任务分发】获取管道闲置服务,pipelineId={},type={},result={}", pipeline.getId(), getType(),
            CollectionUtils.size(result));

        //对所有服务器进行随机打乱，避免每次都从第一个服务开始执行
        if (CollectionUtils.isNotEmpty(result)) {
            Collections.shuffle(result);
        }

        return result;
    }

    /**
     * 判断当前服务是否闲置
     *
     * @param server 服务
     * @return true，闲置
     */
    private boolean isIdle(ServerVO server) {
        if (!serverHelper.isEnable(server)) {
            return false;
        }

        ServerCache cache = serverHelper.getFromTair(server.getId());

        String serverUrl = serverHelper.getServerUrl(server);

        if (null == cache || cache.getStatus() == null) {
            ServerStatusEnum status = serverService.test(server);

            log.info("【任务分发】判断服务是否闲置，{}缓存不存在，调用测试后状态为：{}", serverUrl, status);
            return status == ServerStatusEnum.IDLE;
        }

        if (cache.getStatus() != ServerStatusEnum.IDLE) {
            //如果缓存时间超过3分钟，且任务状态已经完成，则直接重置缓存并返回闲置
            if (cache.needRecheck() && isTaskFinished(cache.getTaskId())) {
                ServerStatusEnum status = serverService.test(server);

                log.info("【任务分发】判断服务是否闲置，{}缓存存在且任务已完结，调用测试后状态为：{}", serverUrl, status);
                return status == ServerStatusEnum.IDLE;
            }

            if (cache.getStatus() == ServerStatusEnum.UNUSABLE) {
                ServerStatusEnum status = serverService.test(server);

                log.info("【任务分发】判断服务是否闲置，{}缓存存在且状态不可用，调用测试后状态为：{}", serverUrl, status);
                return status == ServerStatusEnum.IDLE;
            }

            if (cache.getStatus() == ServerStatusEnum.DISABLE && server.getStatus() != ServerStatusEnum.DISABLE) {
                ServerStatusEnum status = serverService.test(server);

                log.info("【任务分发】判断服务是否闲置，{}缓存存在且状态为关闭，但当前已开启，调用测试后状态为：{}",
                    serverUrl, status);
                return status == ServerStatusEnum.IDLE;
            }

            log.info("【任务分发】判断服务是否闲置，{}缓存存在，但结果为{},{}", serverUrl, cache.getStatus(), cache);
            return false;
        }

        log.info("【任务分发】判断服务是否闲置，{}缓存中的状态为闲置", serverUrl);
        return true;
    }

    /**
     * 缓存数据
     *
     * @param type   业务类型
     * @param task   任务数据
     * @param server 服务
     * @return true，锁成功
     */
    private boolean lockAndCacheData(DispatchTypeEnum type, IExtModel task, ServerVO server) {

        //决策机器是否满足要求
        if (!checkMachineMatch(type, task, server)) {
            return false;
        }

        String lockKey = LOCK_KEY_PREFIX + server.getId();
        boolean lock = tairService.acquireLock(lockKey, LOCK_EXPIRE_TIME);
        if (!lock) {
            log.warn("【任务分发】当前服务{}获取任务派发锁失败，跳过当前服务，type={},taskId={},serverId={}",
                server.getId(), type, task.getId(), server.getId());
            return false;
        }

        try {
            if (!isIdle(server)) {
                log.warn("【任务分发】锁记录后检查当前服务{}状态不为闲置，跳过当前服务，type={},taskId={},serverId={}",
                    server.getId(), type, task.getId(), server.getId());
                return false;
            }

            serverHelper.putToTair(server.getId(), task.getId(), type);
            task.addExtInfo(KEY_SERVER_URL, serverHelper.getServerUrl(server));
            task.addExtInfo(KEY_SERVER_ID, server.getId().toString());
            return true;
        } finally {
            boolean release = tairService.releaseLock(lockKey);
            if (!release) {
                log.error("【任务分发】当前服务{}释放任务派发锁失败，进行重试，type={},taskId={}", server.getId(), type,
                    task.getId());

                release = tairService.releaseLock(lockKey);
                if (!release) {
                    log.error("【任务分发】当前服务{}释放任务派发锁二次失败，type={},taskId={}", server.getId(), type,
                        task.getId());
                }
            }
        }
    }
}

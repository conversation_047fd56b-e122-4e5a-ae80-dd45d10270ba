package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 图像组标注表用户标注数据，多人打标 Service定义
 *
 * <AUTHOR>
 * @version ImageGroupCaptionUserService.java v 0.1 2025-07-30 08:19:30
 */
public interface ImageGroupCaptionUserService {
	
	/**
	 * 查询图像组标注表用户标注数据，多人打标对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageGroupCaptionUserVO selectById(Integer id);

	/**
	 * 删除图像组标注表用户标注数据，多人打标对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像组标注表用户标注数据，多人打标对象
	 * @param imageGroupCaptionUser 对象参数
	 * @return 返回结果
	 */
	ImageGroupCaptionUserVO insert(ImageGroupCaptionUserVO imageGroupCaptionUser);

    ImageGroupCaptionUserVO save(ImageGroupCaptionUserVO imageGroupCaptionUser);

	/**
	 * 修改图像组标注表用户标注数据，多人打标对象
	 * @param imageGroupCaptionUser 对象参数
	 */
	void updateByIdSelective(ImageGroupCaptionUserVO imageGroupCaptionUser);

	/**
	 * 带条件批量查询图像组标注表用户标注数据，多人打标列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageGroupCaptionUserVO> queryImageGroupCaptionUserList(ImageGroupCaptionUserQuery query);

	/**
	 * 带条件查询图像组标注表用户标注数据，多人打标数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageGroupCaptionUserCount(ImageGroupCaptionUserQuery query);

	/**
	 * 带条件分页查询图像组标注表用户标注数据，多人打标
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageGroupCaptionUserVO> queryImageGroupCaptionUserByPage(ImageGroupCaptionUserQuery query);
}
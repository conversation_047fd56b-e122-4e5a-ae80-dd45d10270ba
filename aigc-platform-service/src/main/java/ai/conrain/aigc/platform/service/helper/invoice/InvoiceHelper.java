//package ai.conrain.aigc.platform.service.helper.invoice;
//
//import ai.conrain.aigc.platform.integration.utils.HttpUtils;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.http.HttpResponse;
//import org.apache.http.util.EntityUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * https://market.aliyun.com/products/56928005/cmapi00060742.html?spm=5176.2020520132.101.3.1e3b7218KtSiTv#sku=yuncode5474200006
// */
//@Slf4j
//@Component
//public class InvoiceHelper {
//
//    String spid = "xx";
//    String host = "https://jmfulpower.market.alicloudapi.com";
//    String path = "/full_power_invoice/makeout";
//    String method = "POST";
//    String appcode = "e5c56030b88148dba866fb2551470659";
//
//    //开发票
//    public InvoiceApiResponse applyInvoice(List<InvoiceApiRequest> items) {
//
//        Map<String, String> headers = new HashMap<String, String>();
//        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
//        headers.put("Authorization", "APPCODE " + appcode);
//        //根据API的要求，定义相对应的Content-Type
//        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
//        Map<String, String> querys = new HashMap<String, String>();
//        Map<String, String> bodys = new HashMap<String, String>();
//        bodys.put("spid", spid);
//        bodys.put("data", JSON.toJSONString(items));
//
//        try {
//            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
//            if (response.getStatusLine().getStatusCode() == 200){
//                //todo http成功，还必须要添加发票是否开成功的判断
//                return JSONObject.parseObject(EntityUtils.toString(response.getEntity()), InvoiceApiResponse.class);
//            } else {
//                log.error("调用外部的发票接口失败，http响应:{},{}", response.getStatusLine().getStatusCode(), response.getStatusLine().getReasonPhrase());
//            }
//
//        } catch (Exception e) {
//            log.error("调用外部的发票接口失败", e);
//        }
//
//        return null;
//    }
//}

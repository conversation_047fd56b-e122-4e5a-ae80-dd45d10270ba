/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import jakarta.annotation.PostConstruct;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 同步转异步执行器基类
 *
 * <AUTHOR>
 * @version : AbstractBatchToAsyncExecutor.java, v 0.1 2024/12/6 17:55 renxiao.wu Exp $
 */
@Slf4j
public abstract class AbstractBatchToAsyncExecutor<T extends CreativeRequest, R> implements NamedBatchToAsyncExecutor {
    /** 缓存前缀 */
    private static final String KEY_CACHE_PREFIX = "_CACHE_PREFIX_";
    private static final String LOCK_PREFIX = "_LOCK_PREFIX_";
    @Autowired
    protected TairService tairService;
    /** 请求类型 */
    private Class<T> type;

    @Override
    public boolean aboutTo(CreativeRequest request, CreativeBatchVO batch) {
        if (aboutToType() != null && aboutToType() != batch.getType()) {
            return false;
        }

        return type.isInstance(request);
    }

    @Override
    public void storeSync(CreativeRequest request, CreativeBatchVO batch) {
        if (!aboutTo(request, batch)) {
            return;
        }
        @SuppressWarnings("unchecked")
        Object value = buildOriginValue((T)request);
        if (value == null) {
            return;
        }
        batch.addExtInfo(getOriginKey(), value);
    }

    @Override
    public void restoreTask(CreativeTaskVO task, CreativeBatchVO batch) {
        JSONObject extInfo = batch.getExtInfo();
        String storeKey = getOriginKey();

        if (extInfo.get(storeKey) != null) {
            task.addExtInfo(storeKey, extInfo.get(storeKey));
        }
    }

    @Override
    public void asyncExecAndStore(CreativeTaskVO task, List<CreativeElementVO> elements) {
        String trans = null;
        R transValue = null;

        //如果转换后结果已经在task中存在，则直接跳过
        String transKey = getTransKey();
        trans = task.getExtInfo(transKey, String.class);
        if (StringUtils.isNotBlank(trans)) {
            if (aboutToExt()) {
                fillExtInfo(transValue, task, elements);
            }
            return;
        }

        //如果原始值不存在，也直接跳过
        String origin = task.getExtInfo(getOriginKey(), String.class);
        if (StringUtils.isBlank(origin)) {
            return;
        }

        //如果在tair中存在时，也直接跳过，直接从tair中取
        String cacheKey = buildCacheKey(task);
        transValue = tairService.getObject(cacheKey, getModelClass());

        if (transValue != null) {
            log.info("从缓存中获取到{},taskId={},batchId={}", transKey, task.getId(), task.getBatchId());

            task.addExtInfo(transKey, transValue);

            if (aboutToExt()) {
                fillExtInfo(transValue, task, elements);
            }

            return;
        }

        String lockKey = LOCK_PREFIX + getTransKey() + "_" + task.getBatchId();
        boolean lock = tairService.acquireLock(lockKey, 5 * 60 * 1000);
        if (!lock) {
            log.info("task任务正在处理中，直接返回,id={},uid={}", task.getId(), task.getOperatorId());
            throw new RuntimeException("task任务正在处理中，直接返回");
        }
        try {

            transValue = buildTransValue(task, origin);
            if (transValue == null) {
                log.info("构建{}失败，taskId={},batchId={}", transKey, task.getId(), task.getBatchId());
                return;
            }

            log.info("构建{}结果成功，保存到tair中，并返回，taskId={},batchId={}", transKey, task.getId(),
                task.getBatchId());

            task.addExtInfo(transKey, transValue);

            //在tair中保存1小时
            tairService.setObject(cacheKey, transValue, 60 * 60);
        } finally {
            if (aboutToExt()) {
                if (transValue == null && StringUtils.isNotBlank(trans)) {
                    transValue = JSONObject.parseObject(trans, getModelClass());
                }
                fillExtInfo(transValue, task, elements);
            }
            tairService.releaseLock(lockKey);
        }
    }

    @PostConstruct
    private void init() {
        Type superclass = getClass().getGenericSuperclass();
        if (superclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType)superclass;
            //noinspection unchecked
            this.type = (Class<T>)parameterizedType.getActualTypeArguments()[0];
            log.info("AbstractBatchToAsyncExecutor init success, type: {}", type);
        } else {
            throw new RuntimeException("Unable to determine type");
        }
    }

    /**
     * 获取模型类型
     *
     * @return 模型类型
     */
    protected abstract Class<R> getModelClass();

    /**
     * 是否要继续执行扩展逻辑
     *
     * @return 是否要继续执行扩展逻辑
     */
    protected boolean aboutToExt() {
        return false;
    }

    /**
     * 命中的类型，空值时表示不限制
     *
     * @return 命中的类型
     */
    protected CreativeTypeEnum aboutToType() {
        return null;
    }

    /**
     * 填充其他信息
     *
     * @param transValue 转换后的模型
     * @param task       任务
     * @param elements   元素列表
     */
    protected void fillExtInfo(R transValue, CreativeTaskVO task, List<CreativeElementVO> elements) {
    }

    /**
     * 构建缓存key
     *
     * @param task 任务
     * @return 缓存key
     */
    private String buildCacheKey(CreativeTaskVO task) {
        return KEY_CACHE_PREFIX + getTransKey() + "_" + task.getBatchId();
    }

    /**
     * 构建存储模型
     *
     * @param request 请求
     * @return 存储模型
     */
    protected abstract Object buildOriginValue(T request);

    /**
     * 构建转换后模型
     *
     * @param task   任务
     * @param origin 保存在task中的原始字符串
     * @return 转换后模型
     */
    protected abstract R buildTransValue(CreativeTaskVO task, String origin);
}

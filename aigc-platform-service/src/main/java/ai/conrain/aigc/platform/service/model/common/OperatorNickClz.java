/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

/**
 * 操作员昵称接口
 *
 * <AUTHOR>
 * @version : OperatorNickClz.java, v 0.1 2024/6/27 16:53 renxiao.wu Exp $
 */
public interface OperatorNickClz extends OperatorIdClz {
    /**
     * 获取操作员昵称
     *
     * @return 操作员昵称
     */
    String getOperatorNick();

    /**
     * 设置操作员昵称
     * @param operatorNick 操作员昵称
     */
    void setOperatorNick(String operatorNick);
}

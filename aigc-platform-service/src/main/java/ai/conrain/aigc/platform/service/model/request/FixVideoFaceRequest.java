/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

/**
 * 视频修脸流程请求
 *
 * <AUTHOR>
 * @version : FixVideoFaceRequest.java, v 0.1 2024/9/7 20:40 renxiao.wu Exp $
 */
@Data
public class FixVideoFaceRequest implements CreativeRequest {
    private static final long serialVersionUID = 8018960405300934506L;
    /** 视频地址 */
    @NotBlank
    private String videoUrl;
    /** 模特id */
    @NotNull
    private Integer faceId;
    /** 原图生视频id */
    @NotNull
    private Integer batchId;
    /** 当前的视频所在index */
    @NotNull
    private Integer index;
    /** 用于设置showImage */
    @NotBlank
    private String imageUrl;
}

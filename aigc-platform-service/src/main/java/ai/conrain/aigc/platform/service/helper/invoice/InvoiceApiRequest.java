package ai.conrain.aigc.platform.service.helper.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class InvoiceApiRequest {

    @JSONField(name = "nsrsbh")
    private String taxPayerId; // 纳税人识别号

    @JSONField(name = "data")
    private InvoiceData data; // 开具内容

    @Data
    public static class InvoiceData {
        @JSONField(name = "ly_ddbh")
        private String orderNumber; // 来源订单编号

        @JSONField(name = "fplxdm")
        private String invoiceTypeCode; // 发票类型代码

        @JSONField(name = "xsf_nsrsbh")
        private String sellerTaxpayerId; // 销方税号

        @JSONField(name = "xsf_nsrmc")
        private String sellerName; // 销方名称

        @JSONField(name = "xsf_dz")
        private String sellerAddress; // 销售方地址

        @JSONField(name = "xsf_dh")
        private String sellerPhone; // 销售方电话

        @JSONField(name = "xsf_yhmc")
        private String sellerBankName; // 销售方开户银行

        @JSONField(name = "xsf_yhzh")
        private String sellerBankAccount; // 销售方银行账号

        @JSONField(name = "gmf_lx")
        private String buyerType; // 购方类型

        @JSONField(name = "gmf_nsrmc")
        private String buyerName; // 购方名称

        @JSONField(name = "xmmx")
        private List<InvoiceItem> items; // 开票明细项目

        @Data
        public static class InvoiceItem {
            @JSONField(name = "fphxz")
            private String invoiceLineNature; // 发票行性质

            @JSONField(name = "spbm")
            private String taxCommodityCode; // 税收商品编码

            @JSONField(name = "spmc")
            private String taxCommodityName; // 税收商品名称

            @JSONField(name = "xmmc")
            private String customCommodityName; // 自定义的商品名称

            @JSONField(name = "sl")
            private BigDecimal taxRate; // 税率

            @JSONField(name = "je")
            private BigDecimal amount; // 金额
        }
    }
}
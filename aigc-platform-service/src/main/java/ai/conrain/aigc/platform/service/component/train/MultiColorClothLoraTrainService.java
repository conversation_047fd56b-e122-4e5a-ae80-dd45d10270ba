/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.train;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.ModelPointService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.AddClothMaterialRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.GrayscaleTestUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.BizConstants.MULTI_COLOR_SPLIT;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_LABEL_SWITCH;

/**
 * 多色服装Lora训练服务
 *
 * <AUTHOR>
 * @version : MultiColorClothLoraTrainService.java, v 0.1 2025/4/23 14:38 renxiao.wu Exp $
 */
@Slf4j
@Service
public class MultiColorClothLoraTrainService extends ClothLoraTrainService {
    @Autowired
    private UserPointService userPointService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ModelPointService modelPointService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialModelVO create(AddClothMaterialRequest request) {
        JSONObject switchJson = systemConfigService.queryJsonValue(LORA_LABEL_SWITCH);
        float flag = switchJson.getFloatValue("multiColorSplit");

        //如果未命中灰度，则走正常流程
        if (!GrayscaleTestUtils.isHit(flag, "服装训练-多色拆分灰度")) {
            return super.create(request);
        }

        log.info("服装训练-多色拆分场景开始执行");

        request.setMainType(MainTypeEnum.MAIN);
        MaterialModelVO main = super.create(request);

        //获取归属用户id
        Integer userId = main.getUserId();

        //1.拆分颜色，每个颜色一种模型
        List<AddClothMaterialRequest> requests = splitByColor(request, main.getId());

        List<MaterialModelVO> modelList = new ArrayList<>();

        for (AddClothMaterialRequest req : requests) {
            modelList.add(buildMaterialInfoAndModel(req, userId));
        }

        AssertUtil.assertNotEmpty(modelList, ResultCode.SYS_ERROR, "多色服装Lora训练服务创建失败");

        return main;
    }

    /**
     * 根据颜色拆分，每个颜色一个请求
     *
     * @param request 原始请求
     * @param mainId  主模型id
     * @return 拆分后请求
     */
    private List<AddClothMaterialRequest> splitByColor(AddClothMaterialRequest request, Integer mainId) {
        List<AddClothMaterialRequest> requests = new ArrayList<>();

        Integer number = request.getColorNumber();
        AssertUtil.assertNotNull(number, ResultCode.PARAM_INVALID, "颜色数量不能为空");
        String batchId = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < number; i++) {
            AddClothMaterialRequest req = buildRequestByColorNum(request, i + 1, batchId);
            req.setMainType(MainTypeEnum.SUB);
            req.setMainId(mainId);
            requests.add(req);
        }

        return requests;
    }

    /**
     * 基于颜色编号拆分，构建请求
     *
     * @param request  原始请求
     * @param colorNum 颜色编号
     * @param batchId  批次id，用于区分不同颜色属于同一服装
     * @return 新的请求
     */
    private AddClothMaterialRequest buildRequestByColorNum(AddClothMaterialRequest request, int colorNum,
                                                           String batchId) {
        AddClothMaterialRequest copy = CommonUtil.deepCopy(request);

        List<ClothMaterialImg> fullImgList = request.getMaterialDetail().getFullShotImgList().stream().filter(
            img -> img.getColorGroupNumber() == colorNum).collect(Collectors.toList());

        List<ClothMaterialImg> detailImageList = request.getMaterialDetail().getDetailShotImgList().stream().filter(
            img -> img.getColorGroupNumber() == colorNum).collect(Collectors.toList());

        copy.getMaterialDetail().setFullShotImgList(fullImgList);
        copy.getMaterialDetail().setDetailShotImgList(detailImageList);
        copy.setName(request.getName() + "_颜色" + colorNum);
        copy.setColorNumber(1);
        copy.setMultiColors(false);
        copy.setBizTag(MULTI_COLOR_SPLIT);
        copy.setBatchId(batchId);
        return copy;
    }
}

package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * WorkSchedule批量创建
 *
 * <AUTHOR>
 * @version BatchCreateWorkScheduleRequest.java v 0.1 2025-04-01 16:11:45
 */
@Data
public class WorkScheduleBatchRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<WorkScheduleVO> list;
}

package ai.conrain.aigc.platform.service.component.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.biz.*;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO;
import ai.conrain.aigc.platform.dal.example.AssessmentPlanExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.AssessmentPlanQuery;
import ai.conrain.aigc.platform.service.model.converter.AssessmentPlanConverter;
import ai.conrain.aigc.platform.dal.dao.AssessmentPlanDAO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import jakarta.annotation.PostConstruct;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.*;

/**   
 * AssessmentPlanService实现
 *
 * <AUTHOR>
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
@Slf4j
@Service
public class AssessmentPlanServiceImpl implements AssessmentPlanService {

	/** DAO */
	@Autowired
	private AssessmentPlanDAO assessmentPlanDAO;
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private DistributorService distributorService;
    @Autowired
    private DistributorSettlementService distributorSettlementService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private OrderInfoService orderInfoService;
	// 状态流转映射: 当前状态 -> 处理方法
	private final Map<AssessStatusEnum, StatusHandler> statusHandlerMap = new EnumMap<>(AssessStatusEnum.class);
    @Autowired
    private UserService userService;
    @Autowired
    private TairService tairService;
    @Autowired
    private UserOrganizationService userOrganizationService;
    @Autowired
    private PrincipalInfoService principalInfoService;

	@Override
	public AssessmentPlanVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		AssessmentPlanDO data = assessmentPlanDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return AssessmentPlanConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = assessmentPlanDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除AssessmentPlan失败");
	}

	@Override
	public AssessmentPlanVO insert(AssessmentPlanVO assessmentPlan) {
		AssertUtil.assertNotNull(assessmentPlan, ResultCode.PARAM_INVALID, "assessmentPlan is null");
		AssertUtil.assertTrue(assessmentPlan.getId() == null, ResultCode.PARAM_INVALID, "assessmentPlan.id is present");

		//创建时间、修改时间兜底
		if (assessmentPlan.getCreateTime() == null) {
			assessmentPlan.setCreateTime(new Date());
		}

		if (assessmentPlan.getModifyTime() == null) {
			assessmentPlan.setModifyTime(new Date());
		}

		AssessmentPlanDO data = AssessmentPlanConverter.vo2DO(assessmentPlan);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = assessmentPlanDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建AssessmentPlan失败");
		AssertUtil.assertNotNull(data.getId(), "新建AssessmentPlan返回id为空");
		assessmentPlan.setId(data.getId());
		return assessmentPlan;
	}


	@Override
	public void updateByIdSelective(AssessmentPlanVO assessmentPlan) {
		AssertUtil.assertNotNull(assessmentPlan, ResultCode.PARAM_INVALID, "assessmentPlan is null");
    	AssertUtil.assertTrue(assessmentPlan.getId() != null, ResultCode.PARAM_INVALID, "assessmentPlan.id is null");

		//修改时间必须更新
		assessmentPlan.setModifyTime(new Date());
		AssessmentPlanDO data = AssessmentPlanConverter.vo2DO(assessmentPlan);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = assessmentPlanDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新AssessmentPlan失败，影响行数:" + n);
	}

	@Override
	public List<AssessmentPlanVO> queryAssessmentPlanList(AssessmentPlanQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssessmentPlanExample example = AssessmentPlanConverter.query2Example(query);

		List<AssessmentPlanDO> list = assessmentPlanDAO.selectByExample(example);
			return AssessmentPlanConverter.doList2VOList(list);
	}

	@Override
	public Long queryAssessmentPlanCount(AssessmentPlanQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssessmentPlanExample example = AssessmentPlanConverter.query2Example(query);
		long c = assessmentPlanDAO.countByExample(example);
		return c;
	}

	@Override
	public int insertOrUpdate(AssessmentPlanVO planVO) {
		AssertUtil.assertNotNull(planVO, ResultCode.PARAM_INVALID, "planVO is null");

		if (ObjectUtils.isEmpty(planVO.getCreatorUserId())) {
			planVO.setCreatorUserId(OperationContextHolder.getOperatorUserId());
		}
		if (ObjectUtils.isEmpty(planVO.getModifyUserId())) {
			planVO.setModifyUserId(OperationContextHolder.getOperatorUserId());
		}
		if (ObjectUtils.isEmpty(planVO.getModifyTime())) {
			planVO.setModifyTime(new Date());
		}
		AssessmentPlanDO data = AssessmentPlanConverter.vo2DO(planVO);
        int n = assessmentPlanDAO.insertOrUpdate(data);
		AssertUtil.assertTrue(n >= 1, ResultCode.BIZ_FAIL, "插入或更新AssessmentPlan失败");
		if (planVO.getId() == null && data.getId() != null) {
			planVO.setId(data.getId());
		}
		return n;
	}

	/**
	 * 带条件分页查询销售考核计划
	 */
	@Override
	public PageInfo<AssessmentPlanVO> queryAssessmentPlanByPage(AssessmentPlanQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<AssessmentPlanVO> page = new PageInfo<>();

		AssessmentPlanExample example = AssessmentPlanConverter.query2Example(query);
		long totalCount = assessmentPlanDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<AssessmentPlanDO> list = assessmentPlanDAO.selectByExample(example);
		page.setList(AssessmentPlanConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public AssessmentPlanVO queryLastAssessmentPlanExclusive(PrincipalModel principal, AssessStatusEnum... exceptStatus) {
		AssessmentPlanExample example = new AssessmentPlanExample();
		example.setOrderByClause("create_time desc");
		AssessmentPlanExample.Criteria criteria = example.createCriteria().andPrincipalIdEqualTo(principal.getId());
		
		if (!ObjectUtils.isEmpty(exceptStatus)) {
			criteria.andStatusNotIn(Arrays.stream(exceptStatus)
                    .map(AssessStatusEnum::getCode)
                    .collect(Collectors.toList()));
		}
		
		List<AssessmentPlanDO> assessmentPlans = assessmentPlanDAO.selectByExample(example);
		return CollectionUtils.isEmpty(assessmentPlans) ? null : AssessmentPlanConverter.do2VO(assessmentPlans.get(0));
	}

	@Override
	public AssessmentPlanVO queryActiveAssessmentPlanExclusive(PrincipalModel principal) {
		return queryLastAssessmentPlanExclusive(principal,
				AssessStatusEnum.ASSESS_FINISHED,
				AssessStatusEnum.ASSESS_FAILED);
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public void modifyAssessmentPlan(AssessmentPlanVO data) {
		AssertUtil.assertNotNull(data.getId(), ResultCode.PARAM_INVALID, "AssessmentPlanVO id is null");
		try {
			// 重制提醒日期
			int remindDays = systemConfigService.queryIntValue(ASSESSMENT_REMIND_DURATION, DEFAULT_REMIND_DURATION);
			data.putExtInfo(KEY_REMIND_START_DATE, DateUtils.formatSimpleDate(DateUtils.getDateAfterNDays(new Date(), remindDays)));

			this.updateByIdSelective(data);

			// 立即执行一次状态流转 - 在同一事务中
			process(PrincipalModel.of(data));
		} catch (Exception e) {
			log.error("[修改考核计划]操作失败, data={}", data, e);
			throw new BizException(ResultCode.SYS_ERROR, "修改考核计划失败: " + e.getMessage());
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public void reviewAssessmentPlan(Integer planId, SettleConfigModel newSettleConfig) {
		AssertUtil.assertNotNull(planId, ResultCode.PARAM_INVALID, "planId is null");
		
		try {
			// 1. 查询并校验考核计划
			AssessmentPlanVO plan = selectById(planId);
			AssertUtil.assertNotNull(plan, ResultCode.BIZ_FAIL, "考核计划不存在");
			AssertUtil.assertTrue(plan.getStatus() == AssessStatusEnum.ASSESS_PASSED ||
							plan.getStatus() == AssessStatusEnum.ASSESS_FAILED,
					ResultCode.BIZ_FAIL, "考核计划状态异常，当前状态:" + plan.getStatus());
			
			// 2. 完成当前考核计划
			plan.setStatus(AssessStatusEnum.ASSESS_FINISHED);
			SettleConfigModel oldSettleConfig = distributorSettlementService.getSettleConfig(PrincipalModel.of(plan));
			plan.putExtInfo(KEY_OLD_SETTLE_CONFIG, oldSettleConfig);
			
			// 3. 更新渠道商结算配置
			if (!ObjectUtils.isEmpty(newSettleConfig)) {
				distributorSettlementService.modifyDistributorSettlementConfig(
						plan.getPrincipalType(), plan.getPrincipalId(), newSettleConfig);
				log.info("【考核审核】已更新结算配置，principalId={}", plan.getPrincipalId());
				plan.putExtInfo(KEY_NEW_SETTLE_CONFIG, newSettleConfig);
			}

			updateAssessmentPlan(plan);
			log.info("【考核审核】已完成考核计划，planId={}, 原状态={}", planId, plan.getStatus());
			
			// 4. 为该主体创建新的考核计划
			createNextAssessmentPlanIfNeeded(plan);
			
		} catch (Exception e) {
			log.error("【考核审核】审核考核计划失败，planId={}", planId, e);
			throw new BizException(ResultCode.SYS_ERROR, "审核考核计划失败: " + e.getMessage());
		}
	}
	
	/**
	 * 如果需要，为主体创建下一个考核计划
	 */
	private void createNextAssessmentPlanIfNeeded(AssessmentPlanVO oldPlan) {
		PrincipalModel principal = PrincipalModel.of(oldPlan);
		// 检查是否已有活跃的考核计划
		AssessmentPlanVO activePlan = queryActiveAssessmentPlanExclusive(principal);
		
		if (ObjectUtils.isEmpty(activePlan)) {
			// 没有活跃计划，创建新的考核计划
			AssessmentPlanVO newPlan = this.create(principal, oldPlan, oldPlan.getType());
			// 根据旧考核计划, 设置新考核计划的考核周期
			Date planFromDate = DateUtils.getDateAfterNDays(oldPlan.getPlanEndDate(), 1);
			Date planEndDate = DateUtils.getDateAfterNNatureMonths(planFromDate, 3, true);
			newPlan.setPlanFromDate(planFromDate);
			newPlan.setPlanEndDate(planEndDate);
			insertAssessmentPlan(newPlan);
			log.info("【考核审核】已为主体{}创建新的{}类型考核计划", principal.getName(), oldPlan.getType());
		} else {
			log.info("【考核审核】主体{}已有活跃考核计划，无需创建新计划", principal.getName());
		}
	}

	@Override
	public List<DistributorEnhancedVO> queryAllDistributorAssessment(Boolean showTest) {
		// 根据showTest参数过滤测试数据
		if (showTest == null) {
			showTest = false;
		}
		List<DistributorEnhancedVO> result = new ArrayList<>();
		// 查询所有渠道商组织
		List<OrganizationVO> distributorCorps = organizationService.queryDistributorCorps();
		for (OrganizationVO distributorCorp : distributorCorps) {
			if (!showTest && distributorCorp.isTest()) {
				continue; // 跳过测试组织
			}
			// 查询 渠道商下所有子账号
			List<PrincipalModel> settlePrincipals = new ArrayList<>();
			distributorService.querySettlePrincipalExclusive(settlePrincipals, distributorCorp);
			for (PrincipalModel settlePrincipal : settlePrincipals) {
				try {
					result.add(queryDistributorEnhancedVO(settlePrincipal));
				} catch (Exception e) {
					log.error("查询渠道商增强配置异常, principal={}, error={}", settlePrincipal, e.getMessage(), e);
				}
			}
		}
		return result;
	}

	@Override
	public DistributorEnhancedVO queryDistributorEnhancedVO(PrincipalModel principal) {
 		DistributorPrincipalBasicVO basicInfo = distributorSettlementService.queryDistributorPrincipalBasicInfo(principal);
		
		DistributorEnhancedVO data = BeanUtils.deepCopy(basicInfo, DistributorEnhancedVO.class);
		AssessmentPlanVO currentPlan = queryLastAssessmentPlanExclusive(principal);
		SettleConfigModel settleConfig = distributorSettlementService.getSettleConfig(principal);
		data.setSettleConfig(settleConfig);
		
		data.setCurrentPlan(currentPlan);

		return data;
	}

	@Override
	public void processAssessment() {
		log.info("【销售考核计划】开始执行...");
		// 获取所有需要结算的组织
		List<OrganizationVO> distributorCrops = organizationService.queryDistributorCorps();
		for (OrganizationVO distributorCrop : distributorCrops) {
			log.info("【销售考核计划】当前渠道组织：{}", distributorCrop.getName());
			// 获取当前组织下所有需要被考核的对象
			List<PrincipalModel> settlePrincipals = new ArrayList<>();
			distributorService.querySettlePrincipalExclusive(settlePrincipals, distributorCrop, CustomRoleEnum.OPS_MEMBER);
			// 处理单个考核
			for (PrincipalModel settlePrincipal : settlePrincipals) {
				log.info("【销售考核计划】当前考核对象：{}", settlePrincipal.getName());
				try {
					process(settlePrincipal);
				} catch (Exception e) {
					log.error("【销售考核计划】处理单个结算异常，考核主体: " + settlePrincipal.getName(), e);
				}
			}
		}
	}

	@Override
	public AssessmentPlanVO create(PrincipalModel principal, AssessmentPlanVO oldPlan, SalesAssessTypeEnum assessType) {
		AssessmentPlanVO data = new AssessmentPlanVO();
		data.setPrincipalId(principal.getId());
		data.setPrincipalType(principal.getType());
		data.setStatus(AssessStatusEnum.ASSESS_INIT);
		if (ObjectUtils.isEmpty(oldPlan)) {
			data.setType(assessType);
			// 获取默认考核指标
			JSONArray assessmentConfigs = systemConfigService.queryJsonArrValue(DEFAULT_ASSESSMENT_CONFIGS);
			JSONObject kpiTargetJson = assessmentConfigs.stream()
					.map(item -> (JSONObject) item)
					.filter(item -> assessType.getCode().equals(item.getString("type")))
					.findFirst()
					.orElse(null);
			KPIModel kpiTarget = kpiTargetJson != null ? JSONObject.parseObject(kpiTargetJson.getJSONObject("kpiTarget").toJSONString(), KPIModel.class) : null;
			if (ObjectUtils.isEmpty(kpiTarget)) {
				log.warn("【销售考核计划】默认考核指标解析异常");
				kpiTarget = new KPIModel();
			}
			data.setKpiTarget(kpiTarget);
			String contractDate = principalInfoService.queryContractDate(principal);
			data.setPlanFromDate(getDefaultFromDate(assessType, contractDate));
			data.setPlanEndDate(getDefaultToDate(assessType, contractDate));
		} else {
			data.setType(oldPlan.getType());
			data.setKpiTarget(oldPlan.getKpiTarget());
			data.setPlanFromDate(DateUtils.getDateAfterNDays(oldPlan.getPlanEndDate(), 1));
			data.setPlanEndDate(DateUtils.getDateAfterNNatureMonths(data.getPlanFromDate(), 3, true));
		}
		// 默认7天后提醒
		int remindDays = systemConfigService.queryIntValue(ASSESSMENT_REMIND_DURATION, DEFAULT_REMIND_DURATION);
		data.putExtInfo(KEY_REMIND_START_DATE, DateUtils.formatSimpleDate(DateUtils.getDateAfterNDays(new Date(), remindDays)));
		return data;
	}

	@Override
	public AssessmentPlanVO init(AssessmentPlanVO originalPlan) {
		Date remindDate = DateUtils.parseSimpleDate(originalPlan.getExtInfo(KEY_REMIND_START_DATE, String.class));
		if (DateUtils.afterDate(remindDate, new Date())) {
			tairService.incr(originalPlan.getStatus().getCode(), 20);
		}
		return null;
	}

	@Override
	public AssessmentPlanVO waiting(AssessmentPlanVO originalPlan) {
		AssessmentPlanVO targetPlan = CommonUtil.deepCopy(originalPlan);
		targetPlan.setId(originalPlan.getId());
		targetPlan.setStatus(originalPlan.getStatus());
		// 如果考核计划开始时间已过, 则进入考核中
		if (DateUtils.afterDate(originalPlan.getPlanFromDate(), new Date(), -1)) {
			targetPlan.setStatus(AssessStatusEnum.ASSESS_ACTIVE);
			// 直接往后流转
			return active(targetPlan);
		}
		return targetPlan;
	}

	@Override
	public AssessmentPlanVO active(AssessmentPlanVO originalPlan) {
		AssessmentPlanVO targetPlan = CommonUtil.deepCopy(originalPlan);
		targetPlan.setId(originalPlan.getId());
		targetPlan.setStatus(originalPlan.getStatus());
		targetPlan.setKpiTarget(originalPlan.getKpiTarget());
		targetPlan.setKpiActual(updateActualKPI(originalPlan));
		// 考核周期结束, 判断考核是否通过
		if (DateUtils.afterDate(originalPlan.getPlanEndDate(), new Date())) {
			if (assessKPI(targetPlan)) {
				targetPlan.setStatus(AssessStatusEnum.ASSESS_PASSED);
				passed(targetPlan);
			} else {
				targetPlan.setStatus(AssessStatusEnum.ASSESS_FAILED);
				failed(targetPlan);
			}
		}
		return targetPlan;
	}

	@Override
	public AssessmentPlanVO passed(AssessmentPlanVO originalPlan) {
		if (DateUtils.afterDate(DateUtils.getDateAfterNDays(originalPlan.getModifyTime(), 3), new Date())) {
			tairService.incr(originalPlan.getStatus().getCode(), 300);
		}
		return null;
	}

	@Override
	public AssessmentPlanVO failed(AssessmentPlanVO originalPlan) {
		if (DateUtils.afterDate(DateUtils.getDateAfterNDays(originalPlan.getModifyTime(), 3), new Date())) {
			tairService.incr(originalPlan.getStatus().getCode(), 300);
		}
		return null;
	}

	@Override
	public AssessmentPlanVO reviewed(AssessmentPlanVO originalPlan) {
		// 考核审查完毕应当转为结束状态
		AssessmentPlanVO targetPlan = CommonUtil.deepCopy(originalPlan);
		targetPlan.setId(originalPlan.getId());
		targetPlan.setStatus(AssessStatusEnum.ASSESS_FINISHED);
		return null;
	}

	@Override
	public AssessmentPlanVO finished(AssessmentPlanVO originalPlan) {
		return null;
	}

	@Override
	public AssessmentPlanVO cancelled(AssessmentPlanVO originalPlan) {
		return null;
	}



	// 状态处理器接口
	@FunctionalInterface
	private interface StatusHandler {
		AssessmentPlanVO handle(AssessmentPlanVO plan);
	}

	@PostConstruct
	public void init() {
		// 初始化状态处理器映射
		statusHandlerMap.put(AssessStatusEnum.ASSESS_INIT, this::init);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_WAITING, this::waiting);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_ACTIVE, this::active);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_PASSED, this::passed);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_FAILED, this::failed);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_REVIEWED, this::reviewed);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_FINISHED, this::finished);
		statusHandlerMap.put(AssessStatusEnum.ASSESS_CANCELLED, this::cancelled);

		log.info("考核状态流转管道初始化完成，支持的考核状态：{}", statusHandlerMap.keySet());
	}

	@Override
	public AssessmentPlanVO processStatusFlow(AssessmentPlanVO plan) {

		AssertUtil.assertNotNull(plan, ResultCode.BIZ_FAIL, "[考核计划状态流转]考核计划不能为空");
		AssertUtil.assertNotNull(plan.getStatus(), ResultCode.BIZ_FAIL, "[考核计划状态流转]考核计划状态不能为空");

		// 根据目标状态，获取对应的处理器
		StatusHandler handler = statusHandlerMap.get(plan.getStatus());

		// 执行状态处理
		return handler.handle(plan);
	}

    @Override
	public void initAssessmentPlan(PrincipalModel principal, UserVO userVO, OrganizationVO orgVO) {
		if (principal == null ) {
			principal = this.getPrincipal(userVO, orgVO);
		}
		// 4. 创建考核计划
		SalesAssessTypeEnum assessmentType = getInitialAssessType(principal,
				CustomRoleEnum.getByCode(userVO.getCustomRole()), orgVO.getSalesType());
		AssessmentPlanVO assessmentPlanVO = create(principal, null, assessmentType);
		insertAssessmentPlan(assessmentPlanVO);
    }

	@Override
	public PrincipalModel getPrincipal(UserVO userVO, OrganizationVO orgVO) {
		if (orgVO == null) {
			// 如果没有传入 orgVO, 则根据用户id获取
			orgVO = organizationService.queryOrganizationByUserId(userVO.getId());
			if (orgVO == null) {
				log.error("[获取主体信息]用户组织信息不存在, userId: {}, nickName: {}", userVO.getId(), userVO.getNickName());
				return null;
			}
		}
		SalesTypeEnum salesType = orgVO.getSalesType();
		// 如果是主账号, 直接根据 salesType 判断主体类型
		// 1. 如果是直营销售, 主体是个人, 2. 如果是外部渠道, 主体是组织
		if (UserTypeEnum.MASTER.equals(userVO.getUserType())) {
			if (SalesTypeEnum.isDirect(salesType)) {
				return PrincipalModel.of(PrincipalTypeEnum.USER, userVO.getId(), userVO.getNickName());
			} else {
				return PrincipalModel.of(PrincipalTypeEnum.CORP, orgVO.getId(), orgVO.getName());
			}
		/*
			如果是子账号
			1. 不是二级渠道管理, 那么结算主体都是个人,
			1.1 如果不是销售, 或者组织层级 >= 2, 忽略
			2. 二级渠道管理, 做如下判断
			2.1 如果是直营, 主体都是个人
			2.2 如果非直营, 主体是子组织
		 */
		} else {
			if (!CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode().equals(userVO.getCustomRole())) {
				if (!CustomRoleEnum.SALES_MEMBER.getCode().equals(userVO.getCustomRole()) ||
						orgVO.getOrgLevel() >= 2) {		// 现有业务逻辑, 二级以下不需要考核/结算
					return null;
				}
				return PrincipalModel.of(PrincipalTypeEnum.USER, userVO.getId(), userVO.getNickName());
			}
			if (SalesTypeEnum.isDirect(salesType)) {
				return PrincipalModel.of(PrincipalTypeEnum.USER, userVO.getId(), userVO.getNickName());
			} else {
				return PrincipalModel.of(PrincipalTypeEnum.SUB_CORP, orgVO.getId(), orgVO.getName());
			}
		}
	}

    private void process(PrincipalModel principal) {
        log.info("【销售考核计划】处理单个考核开始，考核主体: {}", principal.getName());
		AssessmentPlanVO originalPlan = queryActiveAssessmentPlanExclusive(principal);

		// 开始状态流转
		// 如果没有考核计划，则创建一个新的计划, 直接 return
		if (ObjectUtils.isEmpty(originalPlan)) {
			return;
		}

		// 如果已有考核计划，则根据当前状态和条件进行状态流转
		AssessStatusEnum currentStatus = originalPlan.getStatus();
		// 执行状态流转(状态不一定改变)
		AssessmentPlanVO updatedPlan = this.processStatusFlow(originalPlan);
		if (ObjectUtils.isEmpty(updatedPlan)) {
			log.info("【销售考核计划】考核主体{}的考核计划无需流转", principal.getName());
			return;
		}
		// 更新考核计划, 如果没有 id, 说明创建了新的考核计划
		if (ObjectUtils.isEmpty(updatedPlan.getId())) {
			insertAssessmentPlan(updatedPlan);
		} else {
			updateAssessmentPlan(updatedPlan);
		}
		AssessStatusEnum targetStatus = updatedPlan.getStatus();
		if (targetStatus != null && targetStatus != currentStatus) {
			log.info("【销售考核计划】考核主体{}的考核计划状态从{}流转到{}", principal.getName(), currentStatus.getDesc(), targetStatus.getCode());
		} else {
			log.info("【销售考核计划】考核主体{}的考核计划状态{}无需变更", principal.getName(),	currentStatus.getDesc());
		}

	}

	private SalesAssessTypeEnum getInitialAssessType(PrincipalModel principal, CustomRoleEnum customRole, SalesTypeEnum salesType) {
		switch (principal.getType()) {
			case CORP:
				if (SalesTypeEnum.isExclusive(salesType)) {
					return SalesAssessTypeEnum.AGENT_EXCLUSIVE;
				} else {
					return SalesAssessTypeEnum.AGENT_NORMAL;
				}
			case SUB_CORP:
				return SalesAssessTypeEnum.SALES_DEPT;
			case USER:
				if (CustomRoleEnum.CHANNEL_ADMIN.equals(customRole)) {
					return SalesAssessTypeEnum.SALES_LEADER;
				} else {
					return SalesAssessTypeEnum.SALES_SUB;
				}
			default:
				return null;
		}
	}

	private SalesAssessTypeEnum getInitialAssessType(PrincipalModel principal) {
		DistributorSettleConfigVO settleConfig = distributorSettlementService.queryDistributorSettleConfig(principal);
		AssertUtil.assertNotNull(settleConfig, ResultCode.BIZ_FAIL, "数据异常, 未查询到结算配置" + "结算主体: " + principal.getName());
		switch (Objects.requireNonNull(SalesTypeEnum.getByCode(settleConfig.getSalesType()))) {
			case AGENT_EXCLUSIVE:
				return SalesAssessTypeEnum.AGENT_EXCLUSIVE;
			case DIRECT:
				if (settleConfig.isChannelAdmin()) {
					return SalesAssessTypeEnum.SALES_LEADER;
				} else if (settleConfig.isSecondChannelAdmin()) {
					return SalesAssessTypeEnum.SALES_DEPT;
				}
				return SalesAssessTypeEnum.SALES_SUB;
			default:
				return SalesAssessTypeEnum.AGENT_NORMAL;
		}
	}

	/**
	 * 保存考核计划
	 */
	private void insertAssessmentPlan(AssessmentPlanVO plan) {
		// 转换并保存考核计划
		plan.setCreatorUserId(OperationContextHolder.getOperatorUserId());
		plan.setModifyUserId(OperationContextHolder.getOperatorUserId());
		AssessmentPlanDO planDO = AssessmentPlanConverter.vo2DO(plan);
		assessmentPlanDAO.insertSelective(planDO);
	}

	/**
	 * 更新考核计划
	 */
	private void updateAssessmentPlan(AssessmentPlanVO plan) {
		// 转换并更新考核计划
		plan.setModifyUserId(OperationContextHolder.getOperatorUserId());
		AssessmentPlanDO planDO = AssessmentPlanConverter.vo2DO(plan);
		assessmentPlanDAO.updateByPrimaryKeySelective(planDO);
	}



	/**
	 * 判断实际的KPI完成情况
	 */
	private Boolean assessKPI(AssessmentPlanVO plan) {
		// 根据业务规则计算KPI实际完成情况
		AssertUtil.assertNotNull(plan.getKpiTarget(), ResultCode.BIZ_FAIL, "数据异常, 考核计划未设置KPI目标");
		AssertUtil.assertNotNull(plan.getKpiActual(), ResultCode.BIZ_FAIL, "数据异常, 考核计划KPI实际完成情况缺失");
		return plan.getKpiActual().upTo(plan.getKpiTarget());
	}

	/**
	 * 获取默认的考核开始日期, 默认从下个月的第一天开始
	 */
	private Date getDefaultFromDate(SalesAssessTypeEnum assessType, String contractDate) {
		if (StringUtils.isNotBlank(contractDate)) {
			return DateUtils.parseSimpleDate(contractDate);
		}
		switch (assessType) {
			case AGENT_EXCLUSIVE:
			case AGENT_NORMAL:
			case AGENT_RECTIFY:
				return DateUtils.getFirstDayOfNMonth(1);
			default:
				return new Date();
		}
	}

	/**
	 * 获取默认的考核结束日期, 默认从三个月最后一天开始
	 */
	private Date getDefaultToDate(SalesAssessTypeEnum assessType, String contractDate) {
		if (StringUtils.isNotBlank(contractDate)) {
			return DateUtils.getDateAfterNNatureMonths(
					DateUtils.parseSimpleDate(contractDate), 3, true);
		}
		switch (assessType) {
			case AGENT_EXCLUSIVE:
			case AGENT_NORMAL:
			case AGENT_RECTIFY:
				return DateUtils.getLastDayOfNMonth(3);
			default:
				return DateUtils.getDateAfterNNatureMonths(new Date(), 3, true);
		}
	}

	/**
	 * 更新考核计划的实际KPI数据
	 */
	private KPIModel updateActualKPI(AssessmentPlanVO plan) {
		try {
			KPIModel actualKPI = calculateCurrentKPI(plan.getPrincipalType(), plan.getPrincipalId(), plan.getPlanFromDate(), plan.getPlanEndDate());
			log.info("【销售考核计划】更新用户{}的KPI数据，当前销售额:{}, 销售单量:{}", plan.getPrincipalId(), actualKPI.getTotalSalesAmount(), actualKPI.getTotalSalesCount());
			return actualKPI;
		} catch (Exception e) {
			log.error("【销售考核计划】更新用户{}的KPI数据失败", plan.getPrincipalId(), e);
			return null;
		}
	}

	/**
	 * 计算当前KPI完成情况
	 */
	private KPIModel calculateCurrentKPI(PrincipalTypeEnum principalType, Integer principalId, Date fromDate, Date toDate) {
		// 查询关联的商家
		List<Integer> merchants =  distributorService.getPrincipalRelatedMerchants(PrincipalModel.of(principalType, principalId));
		if (CollectionUtils.isEmpty(merchants)) {
			return new KPIModel();
		}
		// 查询关联的商家的订单
		OrderInfoQuery query = new OrderInfoQuery();
		query.setMasterUserIds(merchants);
		query.setDateFrom(DateUtils.formatShort(fromDate));
		query.setDateTo(DateUtils.formatShort(toDate));
		List<OrderInfoVO> orderInfos = orderInfoService.queryOrderInfoListWithSalesInfo(query);
		if (CollectionUtils.isEmpty(orderInfos)) {
			return new KPIModel();
		}
		BigDecimal totalAmount = new BigDecimal(0);
		Integer totalCount = 0;
		// 在系统配置中给业绩考核设计订单下限
		BigDecimal lowerLimit = systemConfigService.queryBigDecimalValue(ASSESSMENT_ORDER_LOWER_LIMIT, DEFAULT_ASSESSMENT_ORDER_LOWER_LIMIT);
		for (OrderInfoVO orderInfo : orderInfos) {
			if (orderInfo.getPayAmount().compareTo(lowerLimit) > 0) {
				totalAmount = totalAmount.add(orderInfo.getPayAmount());
				totalCount++;
			}
		}

		KPIModel kpiModel = new KPIModel();
		kpiModel.setTotalSalesAmount(totalAmount);
		kpiModel.setTotalSalesCount(totalCount);

		return kpiModel;
	}

}
package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;

import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * TestItemQuery
 *
 * @version TestItemService.java v 0.1 2024-12-19 01:24:06
 */
@Data
public class TestItemQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 测试计划id */
    private Integer planId;

    /** 类型，TRAIN、CREATIVE */
    private String type;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private String status;

    /** 前置项目id */
    private Integer preId;

    /** 前置项目状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private String preStatus;

    /** 轮数 */
    private Integer roundsNum;

    /** 操作者id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 共用的参数信息 */
    private String sharedParams;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    private List<Integer> planIds;


    private String name;

}

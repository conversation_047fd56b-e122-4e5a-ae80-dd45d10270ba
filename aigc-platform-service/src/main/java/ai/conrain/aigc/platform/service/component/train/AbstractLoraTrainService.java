/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.train;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.request.AddMaterialRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.MaterialUploadUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * lora训练服务基类
 *
 * <AUTHOR>
 * @version : AbstractLoraTrainService.java, v 0.1 2025/2/23 21:16 renxiao.wu Exp $
 */
@Slf4j
public abstract class AbstractLoraTrainService<T extends AddMaterialRequest> implements LoraTrainService<T> {
    @Autowired
    protected MaterialInfoService materialInfoService;
    @Autowired
    protected MaterialModelService materialModelService;
    @Autowired
    private UserPointService userPointService;
    @Autowired
    private OssHelper ossHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MaterialModelVO create(T request) {
        //1.precheck
        precheck(request);

        Integer userId = fetchOwnUser(request);

        //2.构建materialInfo和materialModel信息
        MaterialModelVO materialModel = buildMaterialInfoAndModel(request, userId);

        //3.消耗缪斯点
        if (needConsumePoint(materialModel)) {
            userPointService.consumeByTrainModel(materialModel.getUserId(), materialModel, isExclusive(request));
        }

        //4.后置处理
        postProcess(materialModel, request);

        return materialModel;
    }

    /**
     * 前置校验
     *
     * @param request 请求
     */
    protected void precheck(T request) {}

    /**
     * 构建materialInfo和materialModel信息
     *
     * @param request 请求
     * @param userId  用户id
     * @return materialModel
     */
    protected MaterialModelVO buildMaterialInfoAndModel(T request, Integer userId) {
        //1.初始化materialInfo信息
        MaterialInfoVO materialInfo = buildMaterialInfo(request, userId);
        materialInfo = materialInfoService.insert(materialInfo);

        //2.初始化materialModel信息
        MaterialModelVO materialModel = buildMaterialModel(request, materialInfo);
        materialModelService.insert(materialModel);
        return materialModel;
    }

    /**
     * 是否专属，默认为false
     *
     * @param request 请求
     * @return true，是专属
     */
    protected boolean isExclusive(T request) {
        return false;
    }

    /**
     * 获取归属用户id
     *
     * @param request 请求
     * @return 归属用户id
     */
    protected Integer fetchOwnUser(T request) {
        return OperationContextHolder.getMasterUserId();
    }

    /**
     * 是否需要消耗缪斯点
     *
     * @param materialModel 模型
     * @return 是否需要消耗缪斯点
     */
    protected abstract boolean needConsumePoint(MaterialModelVO materialModel);

    /**
     * 获取展示图片
     *
     * @param request 请求
     * @return 展示图片
     */
    protected abstract String getShowImage(T request);

    /**
     * 填充其他训练信息
     *
     * @param request     请求
     * @param trainDetail 训练详情
     * @param model       模型
     */
    protected abstract void fillOtherTrainDetail(T request, LoraTrainDetail trainDetail, MaterialModelVO model);

    /**
     * 填充素材信息扩展信息
     *
     * @param extInfo 扩展信息
     * @param request 请求
     */
    protected void fillMaterialInfoExt(JSONObject extInfo, T request) {

    }

    /**
     * 填充materialModel扩展信息
     *
     * @param ext     扩展信息
     * @param request 请求
     * @param userId  归属用户id
     */
    protected void fillMaterialModelExt(JSONObject ext, T request, Integer userId) {

    }

    /**
     * 后置处理
     *
     * @param materialModel 模型
     * @param request       请求
     */
    protected void postProcess(MaterialModelVO materialModel, T request) {

    }

    /**
     * 构建materialInfo信息
     *
     * @param request 请求
     * @param userId  用户id
     * @return materialInfo
     */
    private MaterialInfoVO buildMaterialInfo(T request, Integer userId) {
        MaterialInfoVO materialInfo = new MaterialInfoVO();
        materialInfo.setName(request.getName());
        materialInfo.setType(request.getMaterialType());
        materialInfo.setSubType(request.getMaterialSubType());
        materialInfo.setMaterialDetail(CommonUtil.parseObject(JSONObject.toJSONString(request.getMaterialDetail())));

        materialInfo.setUserId(userId);
        materialInfo.setOperatorId(OperationContextHolder.getOperatorUserId());

        JSONObject extInfo = new JSONObject();
        fillMaterialInfoExt(extInfo, request);
        materialInfo.setExtInfo(extInfo);
        return materialInfo;
    }

    /**
     * 构建materialModel信息
     *
     * @param request      请求
     * @param materialInfo 素材信息
     * @return 模型信息
     */
    private MaterialModelVO buildMaterialModel(T request, MaterialInfoVO materialInfo) {
        MaterialModelVO model = new MaterialModelVO();
        model.setName(materialInfo.getName());
        model.setUserId(materialInfo.getUserId());
        model.setStatus(MaterialModelStatusEnum.IN_TRAINING.getCode());
        model.setOperatorId(materialInfo.getOperatorId());
        model.setMainType(MainTypeEnum.NORMAL);
        String showImage = getShowImage(request);
        //压缩showImage
        model.setShowImage(ossHelper.reprocessImage(showImage));
        MaterialType materialType = StringUtils.isBlank(request.getMaterialType()) ? MaterialType.cloth
            : MaterialType.valueOf(request.getMaterialType());
        model.setMaterialType(materialType);

        // 填充扩展信息
        JSONObject ext = new JSONObject();
        fillMaterialModelExt(ext, request, materialInfo.getUserId());
        model.setExtInfo(ext);

        LoraTrainDetail trainDetail = new LoraTrainDetail();

        String uploadRootDirName = MaterialUploadUtil.getMaterialUploadBaseDir();
        String clothDirName = String.format("%s_%s_%s", CommonUtil.removeWhitespace(request.getName()),
            materialInfo.getId(), new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()));

        String originalClothDir = String.format("/home/<USER>/aigc/ComfyUI/input/%s/%s", uploadRootDirName,
            clothDirName);

        trainDetail.setUploadRootDirName(uploadRootDirName);
        trainDetail.setClothSubDir(clothDirName);
        trainDetail.setClothDir(originalClothDir);
        trainDetail.setOriginalMaterialId(materialInfo.getId());
        trainDetail.setOriginalClothName(materialInfo.getName());
        trainDetail.setLoraModelDir(MaterialUploadUtil.getLoraBaseDir() + clothDirName + "/");
        trainDetail.setMaterialType(materialType.name());

        //服装的男女款式描述（man/woman/child，默认值是woman，解决历史数据为空的情况）
        trainDetail.setClothStyleType(request.getClothStyleType4TrainParam());

        // 填充其他训练详情信息
        fillOtherTrainDetail(request, trainDetail, model);

        model.setClothLoraTrainDetail(trainDetail);
        return model;
    }
}

package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionUserDO;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionUserExample;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ImageGroupCaptionUserConverter
 *
 * @version ImageGroupCaptionUserService.java v 0.1 2025-07-30 08:19:30
 */
public class ImageGroupCaptionUserConverter {

    /**
     * DO -> VO
     */
    public static ImageGroupCaptionUserVO do2VO(ImageGroupCaptionUserDO from) {
        ImageGroupCaptionUserVO to = new ImageGroupCaptionUserVO();
        to.setId(from.getId());
        to.setImageGroupId(from.getImageGroupId());
        to.setUserId(from.getUserId());
        to.setCaption(JSONObject.parseObject(from.getCaption()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageGroupCaptionUserDO vo2DO(ImageGroupCaptionUserVO from) {
        ImageGroupCaptionUserDO to = new ImageGroupCaptionUserDO();
        to.setId(from.getId());
        to.setImageGroupId(from.getImageGroupId());
        to.setUserId(from.getUserId());
        to.setCaption(JSONObject.toJSONString(from.getCaption()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageGroupCaptionUserExample query2Example(ImageGroupCaptionUserQuery from) {
        ImageGroupCaptionUserExample to = new ImageGroupCaptionUserExample();
        ImageGroupCaptionUserExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getImageGroupId())) {
            c.andImageGroupIdEqualTo(from.getImageGroupId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCaption())) {
            c.andCaptionEqualTo(from.getCaption());
        }
        if (!ObjectUtils.isEmpty(from.getCaptionVersion())) {
            c.andCaptionVersionEqualTo(from.getCaptionVersion());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (ImageGroupCaptionUserExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageGroupCaptionUserVO> doList2VOList(List<ImageGroupCaptionUserDO> list) {
        return CommonUtil.listConverter(list, ImageGroupCaptionUserConverter::do2VO);
    }
}
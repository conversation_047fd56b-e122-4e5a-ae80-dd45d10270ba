package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.service.helper.wx.PayInfoVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
public class PayServiceHelper {

    @Autowired
    private WxPayService wxPayService;

    /**
     * 创建微信支付单
     * @param openId
     * @return
     * @throws Exception
     */
    public PayInfoVO createOrder(String openId, String appId) {

        log.info("create order,openId={}", openId);
        AssertUtil.assertNotNull(openId, "openId不能为空");

        AssertUtil.assertNotBlank(appId, "appId不能为空");

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(1);
        amount.setCurrency("CNY");
        request.setAmount(amount);

        request.setAppid(appId);

        request.setOutTradeNo(CommonUtil.uuid());
        request.setDescription("测试商品");
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(openId);
        request.setPayer(payer);
        request.setNotifyUrl("https://aigc-dev.conrain/oa/pay/notify");

        WxPayUnifiedOrderV3Result wxPayUnifiedOrderResult = null;
        try {
            wxPayUnifiedOrderResult = wxPayService.unifiedOrderV3(TradeTypeEnum.JSAPI, request);
        } catch (Exception e) {
            log.error("创建订单异常：{}", e);
            throw new RuntimeException(e);
        }
        log.info("创建订单结果：{}", wxPayUnifiedOrderResult);

        //组合参数构建支付
        String timeStamp = createTimestamp();
        String nonceStr = String.valueOf(System.currentTimeMillis());
        String packageVal = "prepay_id=" + wxPayUnifiedOrderResult.getPrepayId();

        //支付参数
        PayInfoVO payInfoVO = new PayInfoVO();
        payInfoVO.setAppId(appId);
        payInfoVO.setNonceStr(nonceStr);
        try {
            payInfoVO.setPaySign(signPayInfo(appId, timeStamp, nonceStr, packageVal));
        } catch (Exception e) {
            log.error("创建订单时签名异常：{}", e);
            throw new RuntimeException(e);
        }

        payInfoVO.setSignType("RSA");
        payInfoVO.setPackageVal(packageVal);
        payInfoVO.setTimeStamp(timeStamp);

        log.info("返回支付参数：{}", payInfoVO);

        return payInfoVO;
    }


    private String createNonceStr() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }


    // 方法：生成签名
    public Map<String, String> sign4WxJsSdkConfig(String jsapiTicket, String url, String appId) {
        Map<String, String> ret = new HashMap<>();
        String nonceStr = createNonceStr();
        String timestamp = createTimestamp();
        String string1;
        String signature = "";

        // 注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapiTicket +
                "&noncestr=" + nonceStr +
                "&timestamp=" + timestamp +
                "&url=" + url;
        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        } catch (Exception e) {
            log.error("签名失败", e);
        }

        ret.put("appId", appId);
        ret.put("jsApiTicket", jsapiTicket);
        ret.put("nonceStr", nonceStr);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);

        return ret;
    }

    private String signPayInfo(String appId, String timeStamp, String nonceStr, String packageStr) throws Exception {
        // 将参数按照指定格式组合
        String message = String.format("%s\n%s\n%s\n%s\n", appId, timeStamp, nonceStr, packageStr);

        // 从classpath加载商户私钥
        InputStream keyInputStream = this.getClass().getClassLoader().getResourceAsStream("apiclient_key.pem");
        if (keyInputStream == null) {
            throw new Exception("商户密钥文件未找到");
        }
        String privateKeyPem = IOUtils.toString(keyInputStream, StandardCharsets.UTF_8);
        privateKeyPem = privateKeyPem.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

        // 将PEM格式的私钥转换为PrivateKey对象
        byte[] pkcs8EncodedBytes = Base64.getDecoder().decode(privateKeyPem);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8EncodedBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

        // 使用SHA256withRSA算法进行签名
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(message.getBytes(StandardCharsets.UTF_8));

        // 将签名结果转换为Base64编码字符串
        byte[] signatureBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signatureBytes);
    }


    /**
     * 时间
     *
     * @return 时间戳
     */
    private String createTimestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }
}

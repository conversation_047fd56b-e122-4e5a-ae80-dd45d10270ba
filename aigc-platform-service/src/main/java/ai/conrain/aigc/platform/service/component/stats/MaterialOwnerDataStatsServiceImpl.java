package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.StatsMaterialOwnerService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.StatsMaterialOwnerVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("materialOwnerDataStatsServiceImpl")
public class MaterialOwnerDataStatsServiceImpl extends AbstractDataStatsServiceImpl {
    @Autowired
    private UserService userService;
    @Autowired
    private StatsMaterialOwnerService statsMaterialOwnerService;
    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.MATERIAL_OWNER;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {
        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case DAILY:
                return executeDailyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case WEEKLY:
                return executeWeeklyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case MONTHLY:
                return executeMonthlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case TOTAL:
                return executeTotalBusinessStats(startDate, endDate, isTotal);
            default:
                return 0;
        }
    }

    /**
     * 执行每日业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 返回处理的记录数
     */
    private int executeDailyBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum periodEnum) {
        // 查询当天的审核通过的服装（交付日期）
        List<MaterialModelVO> materialModelVOList = fetchMaterialModelsByDeliveryDate(startDate, endDate);
        if (CollectionUtils.isEmpty(materialModelVOList)) {
            return 0;
        }

        // 按操作者ID分组
        Map<Integer, List<MaterialModelVO>> materialModelVOMap = groupMaterialModelsByOperator(materialModelVOList);

        // 获取用户信息
        List<UserVO> userVOS = fetchUserInfoByOwnerIds(materialModelVOMap.keySet());

        // 构建统计数据
        List<StatsMaterialOwnerVO> statsVOList = buildStatsMaterialOwnerVOList(
                materialModelVOMap, userVOS, storageDate, periodEnum);

        // 保存统计数据
        return saveStatsData(statsVOList);
    }


    /**
     * 执行每周业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 返回处理的记录数
     */
    private int executeWeeklyBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行每月业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 返回处理的记录数
     */
    private int executeMonthlyBusinessStats(String storageDate, String startDate, String endDate,
                                            StatsPeriodEnum periodEnum) {
        return executePeriodBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行总统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param isTotal   是否汇总
     * @return 返回处理的记录数
     */
    private int executeTotalBusinessStats(String startDate, String endDate, boolean isTotal) {
        // 实现用户操作的总统计逻辑
        return 0;
    }


    /**
     * 根据交付日期查询素材模型
     */
    private List<MaterialModelVO> fetchMaterialModelsByDeliveryDate(String startDate, String endDate) {
        return materialModelService.queryMaterialModelByDeliveryDate(startDate, endDate);
    }

    /**
     * 根据操作者ID对素材模型进行分组
     */
    private Map<Integer, List<MaterialModelVO>> groupMaterialModelsByOperator(
            List<MaterialModelVO> materialModelVOList) {
        return materialModelVOList.stream()
                .collect(Collectors.groupingBy(MaterialModelVO::getRelatedOperatorId));
    }

    /**
     * 根据所有者ID列表获取用户信息
     */
    private List<UserVO> fetchUserInfoByOwnerIds(Iterable<Integer> ownerIds) {
        List<Integer> ownerIdList = new ArrayList<>();
        ownerIds.forEach(ownerIdList::add);
        return userService.batchQueryById(ownerIdList);
    }

    /**
     * 构建素材所有者统计数据列表
     */
    private List<StatsMaterialOwnerVO> buildStatsMaterialOwnerVOList(
            Map<Integer, List<MaterialModelVO>> materialModelVOMap,
            List<UserVO> userVOS,
            String storageDate,
            StatsPeriodEnum periodEnum) {

        List<StatsMaterialOwnerVO> statsMaterialOwnerVOList = new ArrayList<>();

        for (Map.Entry<Integer, List<MaterialModelVO>> entry : materialModelVOMap.entrySet()) {
            Integer ownerId = entry.getKey();
            List<MaterialModelVO> materialModelList = entry.getValue();

            StatsMaterialOwnerVO statsVO = createStatsMaterialOwnerVO(
                    ownerId, materialModelList, userVOS, storageDate, periodEnum);

            statsMaterialOwnerVOList.add(statsVO);
        }

        return statsMaterialOwnerVOList;
    }

    /**
     * 创建单个统计数据对象
     */
    private StatsMaterialOwnerVO createStatsMaterialOwnerVO(
            Integer ownerId,
            List<MaterialModelVO> materialModelList,
            List<UserVO> userVOS,
            String storageDate,
            StatsPeriodEnum periodEnum) {

        // 提取素材模型ID列表
        List<Integer> materialIdList = materialModelList.stream()
                .map(MaterialModelVO::getId)
                .collect(Collectors.toList());

        // 获取用户昵称
        String nickname = "";
        if (ownerId != null && !CollectionUtils.isEmpty(userVOS)) {
            nickname = userVOS.stream()
                    .filter(userVO -> userVO != null && ownerId.equals(userVO.getId()))
                    .map(UserVO::getNickName)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("");
        }

        // 构建统计数据对象
        StatsMaterialOwnerVO statsVO = new StatsMaterialOwnerVO();
        statsVO.setStatsType(periodEnum.getCode());
        statsVO.setStatsDate(storageDate);
        statsVO.setUserId(ownerId);
        statsVO.setNickname(nickname);
        statsVO.setDeliveryCount(materialModelList.size());
        statsVO.addExtInfo(CommonConstants.KEY_MATERIAL_ID_LIST, materialIdList);
        statsVO.setCreateTime(new Date());
        statsVO.setModifyTime(new Date());

        return statsVO;
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    private int saveStatsData(List<StatsMaterialOwnerVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            return statsMaterialOwnerService.batchInsertOrUpdate(statsList);
        }
        return 0;
    }

    /**
     * 根据userId对统计数据列表进行分组
     */
    private Map<Integer, List<StatsMaterialOwnerVO>> groupStatsMaterialOwnerVOListByUserId(
            List<StatsMaterialOwnerVO> statsVOList) {
        return statsVOList.stream()
                .filter(statsVO -> statsVO.getUserId() != null)
                .collect(Collectors.groupingBy(StatsMaterialOwnerVO::getUserId));
    }

    /**
     * 执行周期性业务统计的通用方法
     *
     * @param storageDate      存储日期
     * @param startDate        开始日期
     * @param endDate          结束日期
     * @param targetPeriodEnum 目标统计周期
     * @return 返回处理的记录数
     */
    private int executePeriodBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum targetPeriodEnum) {
        // 查询某个时间段内的统计数据
        List<StatsMaterialOwnerVO> sourceStatsList = statsMaterialOwnerService.selectStatsInfoByDateAndPeriod(
                startDate, endDate, StatsPeriodEnum.DAILY.getCode());

        // 如果统计数据为空，则直接返回0
        if (CollectionUtils.isEmpty(sourceStatsList)) {
            return 0;
        }

        // 按照 userId 分组
        Map<Integer, List<StatsMaterialOwnerVO>> statsVOMap = groupStatsMaterialOwnerVOListByUserId(sourceStatsList);
        
        // 汇总每个用户的统计数据
        List<StatsMaterialOwnerVO> targetStatsList = new ArrayList<>();
        
        for (Map.Entry<Integer, List<StatsMaterialOwnerVO>> entry : statsVOMap.entrySet()) {
            Integer userId = entry.getKey();
            List<StatsMaterialOwnerVO> userStats = entry.getValue();
            
            // 累加交付数量
            int totalDeliveryCount = userStats.stream()
                    .mapToInt(StatsMaterialOwnerVO::getDeliveryCount)
                    .sum();
            
            // 合并素材ID列表
            List<Integer> mergedMaterialIdList = new ArrayList<>();
            for (StatsMaterialOwnerVO stat : userStats) {
                List<Integer> materialIdList = stat.getExtInfo(CommonConstants.KEY_MATERIAL_ID_LIST, List.class);
                if (materialIdList != null) {
                    mergedMaterialIdList.addAll(materialIdList);
                }
            }
            
            // 创建目标周期统计数据
            StatsMaterialOwnerVO targetStat = new StatsMaterialOwnerVO();
            targetStat.setStatsType(targetPeriodEnum.getCode());
            targetStat.setStatsDate(storageDate);
            targetStat.setUserId(userId);
            
            // 使用第一条记录的用户昵称
            String nickname = "";
            if (userId != null && !CollectionUtils.isEmpty(userStats)) {
                StatsMaterialOwnerVO firstStat = userStats.get(0);
                nickname = firstStat.getNickname();
            }
            targetStat.setNickname(nickname);
            
            targetStat.setDeliveryCount(totalDeliveryCount);
            targetStat.addExtInfo(CommonConstants.KEY_MATERIAL_ID_LIST, mergedMaterialIdList);
            targetStat.setCreateTime(new Date());
            targetStat.setModifyTime(new Date());
            
            targetStatsList.add(targetStat);
        }

        // 保存统计数据
        return saveStatsData(targetStatsList);
    }
}

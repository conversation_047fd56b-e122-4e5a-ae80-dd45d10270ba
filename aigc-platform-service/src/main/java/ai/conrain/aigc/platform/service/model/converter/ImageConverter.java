package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.example.ImageExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageDO;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * ImageConverter
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
public class ImageConverter {

    /**
     * DO -> VO
     */
    public static ImageVO do2VO(ImageDO from) {
        ImageVO to = new ImageVO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setUrl(from.getUrl());
        to.setShowImgUrl(from.getShowImgUrl());
        to.setImagePath(from.getImagePath());
        to.setImageHash(from.getImageHash());
        to.setMetadata(JSON.parseObject(from.getMetadata()));
        to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageDO vo2DO(ImageVO from) {
        ImageDO to = new ImageDO();
        to.setId(from.getId());
        to.setType(from.getType());
        to.setUrl(from.getUrl());
        to.setShowImgUrl(from.getShowImgUrl());
        to.setImagePath(from.getImagePath());
        to.setImageHash(from.getImageHash());
        to.setMetadata(JSON.toJSONString(Optional.ofNullable(from.getMetadata()).orElse(new JSONObject())));
        to.setExtInfo(JSON.toJSONString(Optional.ofNullable(from.getExtInfo()).orElse(new JSONObject())));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageExample query2Example(ImageQuery from) {
        ImageExample to = new ImageExample();
        ImageExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getGroupId())) {
            c.andGroupIdEqualTo(from.getGroupId());
        }
        if (!ObjectUtils.isEmpty(from.getMarker())) {
            c.andMarkerEqualTo(from.getMarker());
        }
        if (Objects.equals(from.getHasMarker(), Boolean.TRUE)) {
            c.andExitsAnyMarker();
        }
        if (Objects.equals(from.getHasMarker(), Boolean.FALSE)) {
            c.andNoMarker();
        }
        if (Objects.equals(from.getLabeled(), Boolean.TRUE)) {
            c.andLabeled();
        }
        if (Objects.equals(from.getLabeled(), Boolean.FALSE)) {
            c.andNotLabeled();
        }
        if (Objects.equals(from.getCompleted(), Boolean.TRUE)) {
            c.andCompleted();
        }
        if (Objects.equals(from.getCompleted(), Boolean.FALSE)) {
            c.andNotCompleted();
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getMetadataKeys())) {
            c.andMetadataKeyIn(from.getMetadataKeys());
            c.andMetadataValueIsNotNull();
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getUrl())) {
            c.andUrlEqualTo(from.getUrl());
        }
        if (!ObjectUtils.isEmpty(from.getShowImgUrl())) {
            c.andShowImgUrlEqualTo(from.getShowImgUrl());
        }
        if (!ObjectUtils.isEmpty(from.getImagePath())) {
            c.andImagePathEqualTo(from.getImagePath());
        }
        if (!ObjectUtils.isEmpty(from.getImageHash())) {
            c.andImageHashEqualTo(from.getImageHash());
        }
        if (Boolean.TRUE == from.getNoImageHash()) {
            c.andImageHashIsEmpty();
        }
        if (Boolean.TRUE == from.getNoImageSize()) {
            c.andImageSizeIsNull();
        }
        if (!ObjectUtils.isEmpty(from.getMetadata())) {
            from.getMetadata().forEach((k, v) -> {
                if (v == null || ObjectUtils.isEmpty(v.toString())) {
                    return;
                }
                if (v instanceof List<?>) {
                    c.andMetadataKeyInValues(k, (List<String>) v);
                } else {
                    List<String> values = Arrays.stream(v.toString().split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotEmpty)
                            .toList();
                    c.andMetadataKeyInValues(k, values);
                }
            });
        }
        if (!ObjectUtils.isEmpty(from.getTags())) {
            if (from.getTags().contains("no_tag")) {
                c.andTagsIsEmpty();
            } else {
                c.andTagsContains(JSONArray.toJSONString(from.getTags()));
            }
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTimeStart())) {
            c.andCreateTimeGreaterThanOrEqualTo(from.getCreateTimeStart());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTimeEnd())) {
            c.andCreateTimeLessThanOrEqualTo(from.getCreateTimeEnd());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (ImageExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageVO> doList2VOList(List<ImageDO> list) {
        return CommonUtil.listConverter(list, ImageConverter::do2VO);
    }
}
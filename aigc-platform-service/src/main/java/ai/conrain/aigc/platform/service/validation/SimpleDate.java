/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

/**
 * yyyyMMdd格式的日期字符串验证注解
 *
 * <AUTHOR>
 * @version : SimpleDate.java, v 0.1 2023/9/10 23:25 renxiao.wu Exp $
 */
@Documented
@Constraint(validatedBy = SimpleDateValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface SimpleDate {
    String message() default "请输入正确的日期";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

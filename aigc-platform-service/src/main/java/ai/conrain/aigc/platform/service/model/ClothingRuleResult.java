package ai.conrain.aigc.platform.service.model;

import ai.conrain.aigc.platform.service.enums.LensCompositionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 服装规则查询结果封装类
 * 用于封装样式白名单、构图黑名单和用途白名单的查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClothingRuleResult {

    /**
     * 样式白名单 - 可作为参考图的服装样式枚举列表
     * 包含一级分类或二级分类枚举，可以反查所属一级类目
     */
    private List<Enum<?>> styleWhiteList;

    /**
     * 构图黑名单 - 不适合的拍摄构图枚举列表
     */
    private List<LensCompositionType> compositionBlackList;

    /**
     * 用途白名单 - 服装用途分类枚举列表
     * 包含主分类或子分类枚举
     */
    private List<Enum<?>> usageWhiteList;

    /**
     * 检查样式白名单是否为空
     * 
     * @return 如果样式白名单为空或null返回true，否则返回false
     */
    public boolean isStyleWhiteListEmpty() {
        return styleWhiteList == null || styleWhiteList.isEmpty();
    }

    /**
     * 检查构图黑名单是否为空
     * 
     * @return 如果构图黑名单为空或null返回true，否则返回false
     */
    public boolean isCompositionBlackListEmpty() {
        return compositionBlackList == null || compositionBlackList.isEmpty();
    }
}
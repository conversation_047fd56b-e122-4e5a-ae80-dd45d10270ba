package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.UserProfileDAO;
import ai.conrain.aigc.platform.dal.entity.UserProfileDO;
import ai.conrain.aigc.platform.dal.example.UserProfileExample;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.UserProfileKeys;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserProfileConverter;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.NO;

/**
 * UserProfileService实现
 *
 * <AUTHOR>
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
@Slf4j
@Service
public class UserProfileServiceImpl implements UserProfileService {

    /** DAO */
    @Autowired
    private UserProfileDAO userProfileDAO;
    @Autowired
    private TairService tairService;

    @Override
    public UserProfileVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        UserProfileDO data = userProfileDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return UserProfileConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = userProfileDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserProfile失败");
    }

    @Override
    public UserProfileVO insertOrUpdate(UserProfileVO userProfile) {
        AssertUtil.assertNotNull(userProfile, ResultCode.PARAM_INVALID, "userProfile is null");
        AssertUtil.assertTrue(userProfile.getId() == null, ResultCode.PARAM_INVALID, "userProfile.id is present");

        //创建时间、修改时间兜底
        if (userProfile.getCreateTime() == null) {
            userProfile.setCreateTime(new Date());
        }

        if (userProfile.getModifyTime() == null) {
            userProfile.setModifyTime(new Date());
        }

        UserProfileDO data = UserProfileConverter.vo2DO(userProfile);
        Integer n = userProfileDAO.insertOrUpdate(data);
        AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "创建UserProfile失败");
        AssertUtil.assertNotNull(data.getId(), "新建UserProfile返回id为空");
        userProfile.setId(data.getId());
        return userProfile;
    }

    @Override
    public UserProfileVO insert(UserProfileVO userProfile) {
        AssertUtil.assertNotNull(userProfile, ResultCode.PARAM_INVALID, "userProfile is null");
        AssertUtil.assertTrue(userProfile.getId() == null, ResultCode.PARAM_INVALID, "userProfile.id is present");

        //创建时间、修改时间兜底
        if (userProfile.getCreateTime() == null) {
            userProfile.setCreateTime(new Date());
        }

        if (userProfile.getModifyTime() == null) {
            userProfile.setModifyTime(new Date());
        }

        UserProfileDO data = UserProfileConverter.vo2DO(userProfile);
        Integer n = userProfileDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建UserProfile失败");
        AssertUtil.assertNotNull(data.getId(), "新建UserProfile返回id为空");
        userProfile.setId(data.getId());
        return userProfile;
    }

    @Override
    public void updateByIdSelective(UserProfileVO userProfile) {
        AssertUtil.assertNotNull(userProfile, ResultCode.PARAM_INVALID, "userProfile is null");
        AssertUtil.assertTrue(userProfile.getId() != null, ResultCode.PARAM_INVALID, "userProfile.id is null");

        //修改时间必须更新
        userProfile.setModifyTime(new Date());
        UserProfileDO data = UserProfileConverter.vo2DO(userProfile);
        int n = userProfileDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserProfile失败，影响行数:" + n);
    }

    @Override
    public List<UserProfileVO> queryUserProfileList(UserProfileQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserProfileExample example = UserProfileConverter.query2Example(query);

        List<UserProfileDO> list = userProfileDAO.selectByExample(example);
        return UserProfileConverter.doList2VOList(list);
    }

    @Override
    public Long queryUserProfileCount(UserProfileQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        UserProfileExample example = UserProfileConverter.query2Example(query);
        long c = userProfileDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询用户属性
     */
    @Override
    public PageInfo<UserProfileVO> queryUserProfileByPage(UserProfileQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<UserProfileVO> page = new PageInfo<>();

        UserProfileExample example = UserProfileConverter.query2Example(query);
        long totalCount = userProfileDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<UserProfileDO> list = userProfileDAO.selectByExample(example);
        page.setList(UserProfileConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public UserProfileVO selectByUidAndProfileKey(Integer uid, String profileKey) {
        AssertUtil.assertNotNull(uid, ResultCode.PARAM_INVALID, "uid is null");
        AssertUtil.assertNotBlank(profileKey, ResultCode.PARAM_INVALID, "profileKey is null");
        UserProfileQuery query = new UserProfileQuery();
        query.setUid(uid);
        query.setProfileKey(profileKey);
        query.setPageNum(1);
        query.setPageSize(1);

        List<UserProfileVO> list = this.queryUserProfileList(query);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    @Async
    public void initRetractNotice(Integer operatorId) {
        //先过一遍缓存
        Boolean inited = tairService.getObject(getTairKey(operatorId), Boolean.class);
        if (inited != null && inited) {
            log.info("创作流程引导设置，已设置过引导流程(cache)，直接跳过，{}", operatorId);
            return;
        }

        UserProfileVO exist = selectByUidAndProfileKey(operatorId, "showFlowSteps");

        if (exist != null) {
            log.info("创作流程引导设置，已设置过引导流程，直接跳过，{}", operatorId);
            tairService.setObject(getTairKey(operatorId), true, 10 * 24 * 60 * 60);//缓存10天
            return;
        }

        log.info("创作流程引导设置，当前操作员已完成创作流程，初始化收起提示，{}", operatorId);
        UserProfileVO userProfile = new UserProfileVO();
        userProfile.setUid(OperationContextHolder.getOperatorUserId());
        userProfile.setProfileKey("showFlowSteps");
        userProfile.setProfileVal(NO);
        insert(userProfile);

        tairService.setObject(getTairKey(operatorId), true, 10 * 24 * 60 * 60);//缓存10天
    }

    @Override
    public Map<Integer, Integer> queryPromptEngineers(List<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        UserProfileQuery query = new UserProfileQuery();
        query.setUids(userIds);
        query.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
        UserProfileExample example = UserProfileConverter.query2Example(query);
        List<UserProfileDO> list = userProfileDAO.selectByExample(example);
        return list.stream()
            .filter(user -> StringUtils.isNotBlank(user.getProfileVal()))
            .collect(Collectors.toMap(
                UserProfileDO::getUid,
                user -> Integer.valueOf(user.getProfileVal()),
                (existing, replacement) -> existing
            ));
    }

    @Override
    public List<Integer> queryUserIdsByPromptionCode(String promotionCode) {
        UserProfileExample exam = new UserProfileExample();
        exam.createCriteria().andProfileKeyEqualTo(UserProfileKeys.REGISTER_INVITE_CODE).andPromptionCodeEqualTo(promotionCode);
        List<UserProfileDO> list = userProfileDAO.selectByExample(exam);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(UserProfileDO::getUid).collect(Collectors.toList());
    }

    private String getTairKey(Integer operatorId) {
        return "_userProfile_showFlowSteps_" + operatorId;
    }
}
package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.TrainParamDO;
import ai.conrain.aigc.platform.service.model.query.TrainParamQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainParamVO;
import ai.conrain.aigc.platform.service.model.converter.TrainParamConverter;
import ai.conrain.aigc.platform.dal.dao.TrainParamDAO;
import ai.conrain.aigc.platform.service.component.TrainParamService;

/**   
 * TrainParamService实现
 *
 * <AUTHOR>
 * @version TrainParamService.java v 0.1 2024-11-19 08:34:41
 */
@Slf4j
@Service
public class TrainParamServiceImpl implements TrainParamService {

	/** DAO */
	@Autowired
	private TrainParamDAO trainParamDAO;

	@Override
	public TrainParamVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		TrainParamDO data = trainParamDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return TrainParamConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = trainParamDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TrainParam失败");
	}

	@Override
	public TrainParamVO insert(TrainParamVO trainParam) {
		AssertUtil.assertNotNull(trainParam, ResultCode.PARAM_INVALID, "trainParam is null");
		AssertUtil.assertTrue(trainParam.getId() == null, ResultCode.PARAM_INVALID, "trainParam.id is present");

		//创建时间、修改时间兜底
		if (trainParam.getCreateTime() == null) {
			trainParam.setCreateTime(new Date());
		}

		if (trainParam.getModifyTime() == null) {
			trainParam.setModifyTime(new Date());
		}

		TrainParamDO data = TrainParamConverter.vo2DO(trainParam);
		Integer n = trainParamDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TrainParam失败");
		AssertUtil.assertNotNull(data.getId(), "新建TrainParam返回id为空");
		trainParam.setId(data.getId());
		return trainParam;
	}

	@Override
	public void updateById(TrainParamVO trainParam) {
		AssertUtil.assertNotNull(trainParam, ResultCode.PARAM_INVALID, "trainParam is null");
		AssertUtil.assertTrue(trainParam.getId() != null, ResultCode.PARAM_INVALID, "trainParam.id is null");
		//修改时间必须更新
		trainParam.setModifyTime(new Date());

		TrainParamDO data = TrainParamConverter.vo2DO(trainParam);
		int n = trainParamDAO.updateByPrimaryKey(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TrainParam失败，影响行数:" + n);
	}

	@Override
	public List<TrainParamVO> findAll() {
		List<TrainParamDO> list = trainParamDAO.selectAll();
		return TrainParamConverter.doList2VOList(list);
	}

	@Override
	public List<TrainParamVO> findAllByPlanId(Integer planId) {
		AssertUtil.assertNotNull(planId, ResultCode.PARAM_INVALID, "planId is null");
		List<TrainParamDO> list = trainParamDAO.selectAllByTrainPlanId(planId);
		return TrainParamConverter.doList2VOList(list);
	}
}
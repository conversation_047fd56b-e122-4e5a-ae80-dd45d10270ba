package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.constants.PayType;
import ai.conrain.aigc.platform.service.model.biz.PayStatusEnum;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.github.binarywang.wxpay.bean.request.WxPayOrderQueryV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WeChatPayService extends AbstractPayService {

    //公众号
    private final static String appId = "wx2833893c59e0a2eb";

    @Autowired
    private WxPayService wxPayService;

    @Override
    protected String _createQrCodeUrl(String orderNo, String payAmount, String planCode, Integer masterUserId) {

        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(appId);
        request.setDescription("服务订购");
        request.setOutTradeNo(orderNo);

        // 订单金额
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        // 总金额（单位为分，只能为整数，即元*100）
        Integer amountInCents = super.getAmountInCent(planCode, masterUserId, payAmount);
        amount.setTotal(amountInCents);
        request.setAmount(amount);

        try {
            String codeUrl = wxPayService.createOrderV3(TradeTypeEnum.NATIVE, request);
            AssertUtil.assertTrue(StringUtils.isNotBlank(codeUrl), "创建支付订单失败");
            return codeUrl;
        } catch (Exception e){
            log.error("创建支付订单失败", e);
            throw new RuntimeException("创建支付订单失败");
        }
    }

    @Override
    protected String _getPayType() {
        return PayType.WX;
    }

    @Override
    protected PayStatusEnum _queryPayResultStatus(String orderNo) {
        try {
            WxPayOrderQueryV3Request req = new WxPayOrderQueryV3Request();
            req.setOutTradeNo(orderNo);
            WxPayOrderQueryV3Result queryResult = wxPayService.queryOrderV3(req);
            AssertUtil.assertNotNull(queryResult, "查询订单失败");
            return PayStatusEnum.getByCode(queryResult.getTradeState());
        } catch (Exception e){
            log.error("查询支付订单失败", e);
            throw new RuntimeException("查询支付订单失败");
        }
    }
}

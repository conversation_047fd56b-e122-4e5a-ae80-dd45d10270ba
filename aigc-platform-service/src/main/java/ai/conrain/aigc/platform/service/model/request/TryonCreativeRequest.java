package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

@Data
public class TryonCreativeRequest implements CreativeRequest {

    //上装图片地址（对应图片已经在前端上传时，同步到comfyui server）
    private String topUrl;

    //下装图片地址（对应图片已经在前端上传时，同步到comfyui server）
    private String bottomUrl;

    //参考人像图片地址（对应图片已经在前端上传时，同步到comfyui server）
    private String personImgUrl;

    //抠图词，重新抠图场景
    private String cutoutKeyword;

    //图片数量（默认缺省为1）
    private String imgNum;
}

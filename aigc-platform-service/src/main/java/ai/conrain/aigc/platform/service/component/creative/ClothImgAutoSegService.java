package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.request.ClothImgAutoSegRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ClothImgAutoSegService extends AbstractCreativeService<ClothImgAutoSegRequest> {

    @Value("${comfyui.input.path}")
    private String inputPath;

    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.CLOTH_AUTO_SEGMENT;
    }

    @Override
    protected CreativeBatchVO buildData(ClothImgAutoSegRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO batch = new CreativeBatchVO();

        // 设置类型
        batch.setType(getCreativeType());
        // 设置用户id
        batch.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置展示图片
        batch.setShowImage(request.getImgUrl());
        // 设置批次数量
        batch.setBatchCnt(1);
        // 设置状态(排队中)
        batch.setStatus(CreativeStatusEnum.QUEUE);
        //默认按参考图片比例
        batch.setImageProportion(CommonConstants.NONE);
        batch.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE, request.getImgUrl());

        try {
            // 上传图片至 ComfyUI
            String upLoadedImgFilePath = comfyUIHelper.upLoadImage(request.getImgUrl());
            batch.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH, upLoadedImgFilePath);
        } catch (Exception e) {
            log.error("图片上传至ComfyUI出现异常", e);
        }
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        target.setBatchCnt(3);
        target.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH, inputPath + batch.getStringFromExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        context.put(CommonConstants.KEY_ORIGIN_IMAGE_PATH, task.getStringFromExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH));
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task, Map<String, Object> context) {
        return SystemConstants.CLOTH_AUTO_SEGMENT_WORKFLOW;
    }
}

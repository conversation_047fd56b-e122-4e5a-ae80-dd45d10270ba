package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ImageCaseDO;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.BadCaseTag;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.dal.example.ImageCaseExample;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;

import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;

/**
 * ImageCaseConverter
 *
 * @version ImageCaseService.java v 0.1 2024-12-09 06:41:17
 */
public class ImageCaseConverter {

    /**
     * DO -> VO
     */
    public static ImageCaseVO do2VO(ImageCaseDO from) {
        ImageCaseVO to = new ImageCaseVO();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setMiniUrl(from.getMiniUrl());
        to.setTaskId(from.getTaskId());
        to.setBatchId(from.getBatchId());
        to.setStorePath(from.getStorePath());
        to.setStoreServer(from.getStoreServer());
        if (StringUtils.isNotBlank(from.getTags())) {
            List<String> list = Arrays.asList(from.getTags().split(","));
            to.setTags(list.stream().map(Integer::parseInt).collect(Collectors.toList()));
        }
        to.setStatus(MaterialModelStatusEnum.getByCode(from.getStatus()));
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSyncStatus(from.getSyncStatus());
        to.setSyncTime(from.getSyncTime());
        to.setReSyncCount(from.getReSyncCount());
        to.setTagDetails(new ArrayList<>());
        // 处理扩展信息
        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageCaseDO vo2DO(ImageCaseVO from) {
        ImageCaseDO to = new ImageCaseDO();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setMiniUrl(from.getMiniUrl());
        to.setTaskId(from.getTaskId());
        to.setBatchId(from.getBatchId());
        to.setStorePath(from.getStorePath());
        to.setStoreServer(from.getStoreServer());
        to.setStatus(from.getStatus().getCode());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSyncStatus(from.getSyncStatus());
        to.setSyncTime(from.getSyncTime());
        to.setReSyncCount(from.getReSyncCount());
        // 处理扩展信息
        JSONObject extInfo = from.getExtInfo();
        if (extInfo != null && !extInfo.isEmpty()) {
            to.setExtInfo(extInfo.toJSONString());
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static ImageCaseQuery do2Query(ImageCaseDO from) {
        ImageCaseQuery to = new ImageCaseQuery();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setMiniUrl(from.getMiniUrl());
        to.setTaskId(from.getTaskId());
        to.setBatchId(from.getBatchId());
        to.setStorePath(from.getStorePath());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSyncStatus(from.getSyncStatus());
        to.setSyncTime(from.getSyncTime());
        to.setReSyncCount(from.getReSyncCount());
        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);

        return to;
    }

    /**
     * Query -> DO
     */
    public static ImageCaseDO query2DO(ImageCaseQuery from) {
        ImageCaseDO to = new ImageCaseDO();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setMiniUrl(from.getMiniUrl());
        to.setTaskId(from.getTaskId());
        to.setBatchId(from.getBatchId());
        to.setStorePath(from.getStorePath());
        to.setStoreServer(from.getStoreServer());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSyncStatus(from.getSyncStatus());
        to.setSyncTime(from.getSyncTime());
        to.setReSyncCount(from.getReSyncCount());
        // 处理扩展信息
        JSONObject extInfo = from.getExtInfo();
        if (extInfo != null && !extInfo.isEmpty()) {
            to.setExtInfo(extInfo.toJSONString());
        }

        return to;
    }

    public static BadCaseTag dictToTag(PromptDictVO from, ImageCaseVO imageCase) {
        BadCaseTag to = new BadCaseTag();
        to.setTitle(from.getWord());
        to.setId(from.getId());
        to.setPrompt(from.getPrompt());
        if (imageCase != null && CollectionUtils.isNotEmpty(imageCase.getTags())) {
            to.setChecked(imageCase.getTags().contains(from.getId()));
        }

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageCaseExample query2Example(ImageCaseQuery from) {
        ImageCaseExample to = new ImageCaseExample();
        ImageCaseExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUrl())) {
            c.andUrlEqualTo(from.getUrl());
        }
        if (!ObjectUtils.isEmpty(from.getMiniUrl())) {
            c.andMiniUrlEqualTo(from.getMiniUrl());
        }
        if (!ObjectUtils.isEmpty(from.getTaskId())) {
            c.andTaskIdEqualTo(from.getTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getStorePath())) {
            c.andStorePathEqualTo(from.getStorePath());
        }
        if (!ObjectUtils.isEmpty(from.getStoreServer())) {
            c.andStoreServerEqualTo(from.getStoreServer());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getTagIds())) {
            to.setTagIds(from.getTagIds());
        }
        if (!ObjectUtils.isEmpty(from.getSyncStatus())) {
            c.andSyncStatusEqualTo(from.getSyncStatus());
        }
        if (!ObjectUtils.isEmpty(from.getSyncTime())) {
            c.andSyncTimeEqualTo(from.getSyncTime());
        }

        // id in
        if (!ObjectUtils.isEmpty(from.getCaseIdList())) {
            c.andIdIn(from.getCaseIdList());
        }

        // 设置最大同步次数
        if (!ObjectUtils.isEmpty(from.getMaxSyncCount())) {
            c.andReSyncCountLessThan(from.getMaxSyncCount());
        }

        // 时间查询
        if (from.getBeginSearchTime() != null && from.getEndSearchTime() != null) {
            c.andCreateTimeBetween(from.getBeginSearchTime(), from.getEndSearchTime());
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ImageCaseVO> doList2VOList(List<ImageCaseDO> list) {
        return CommonUtil.listConverter(list, ImageCaseConverter::do2VO);
    }

    public static List<BadCaseTag> doList2VOList(List<PromptDictVO> list, ImageCaseVO imageCase) {
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<BadCaseTag> result = new ArrayList<>();
        for (PromptDictVO dict : list) {
            result.add(dictToTag(dict, imageCase));
        }
        return result;
    }


}
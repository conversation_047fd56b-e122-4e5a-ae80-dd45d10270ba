/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

import ai.conrain.aigc.platform.service.enums.RequestFromEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

import java.io.Serializable;

/**
 * 操作上下文
 *
 * <AUTHOR>
 * @version : OperationContext.java, v 0.1 2023/9/3 18:08 renxiao.wu Exp $
 */
@Data
public class OperationContext implements Serializable {

    private static final long serialVersionUID = 4955886027705500978L;

    /**
     * ip地址
     */
    private String ip;

    /** 会话模型，缓存到tair */
    private OperationSession operationSession;

    /** 角色类型 */
    private RoleTypeEnum roleType;

    /**
     * 请求来源标
     */
    private String requestFrom;

    /**
     * 小程序appId
     */
    private String miniAppId;

    /**
     * post请求体的json主体
     */
    private JSONObject postRequestBody;

    /** 执行开始时间 */
    private long actionStartTime;

    /** 操作关键字 */
    private Object opKey;

    /** 用于日志的业务场景 */
    private String logBizScene;

    /** 从tair查询刷新时间的map */
    private Map<String, Boolean> tairQueryCacheMap = new HashMap<>();

    public String getSessionId() {
        if (operationSession != null) {
            return operationSession.getSessionId();
        }
        throw new RuntimeException("当前session为空!");
    }

    public RoleTypeEnum getRoleType() {
        if (roleType != null) {
            return roleType;
        }

        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }
        return operationSession.getLoginUser().getRoleType();
    }

    public boolean isMasterUser() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return false;
        }
        return operationSession.getLoginUser().getUserType() == UserTypeEnum.MASTER;
    }

    public boolean isSubUser() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return false;
        }
        return operationSession.getLoginUser().getUserType() == UserTypeEnum.SUB;
    }

    public Integer getMasterUser() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }

        if (isMasterUser()) {
            return operationSession.getLoginUser().getId();
        }

        return operationSession.getLoginUser().getMasterId();
    }

    public Integer getCurrentUserId() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }

        return operationSession.getLoginUser().getId();
    }

    public String getUserNick() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }
        return operationSession.getLoginUser().getNickName();
    }

    public String getMasterNick() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }

        if (isMasterUser()) {
            return operationSession.getLoginUser().getNickName();
        }

        return operationSession.getLoginUser().getMasterNick();
    }

    public String getMasterLoginId() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }

        if (isMasterUser()) {
            return operationSession.getLoginUser().getLoginId();
        }

        return operationSession.getLoginUser().getMasterLoginId();
    }

    public String getOperatorLoginId() {
        if (operationSession == null || operationSession.getLoginUser() == null) {
            return null;
        }

        return operationSession.getLoginUser().getLoginId();
    }

    /**
     * 根据key值获取json中的参数值
     *
     * @param key 关键字
     * @return json参数值
     */
    public Object getParamsFromPostRequestBodyByKey(String key, Class<?> clz) {
        if (null == postRequestBody) {
            return null;
        }

        return postRequestBody.getObject(key, clz);
    }

    public boolean isLoggedIn() {
        return operationSession != null && operationSession.getLoginUser() != null;
    }

    public void setAttribute(String key, String val) {
        if (operationSession == null) {
            operationSession = new OperationSession();
        }
        operationSession.setAttr(key, val);
    }

    public String getAttribute(String key) {
        if (operationSession != null) {
            return operationSession.getAttr(key);
        }
        return null;
    }

    public void setLoginUser(UserVO user) {
        if (operationSession == null) {
            operationSession = new OperationSession();
        }
        operationSession.setLoginUser(user);
        operationSession.setLastRefreshUserTime(System.currentTimeMillis());
    }

    public void setOperationSession(OperationSession operationSession) {
        if (operationSession != null && operationSession.getSessionId() == null) {
            throw new NullPointerException("session id不可为空");
        }
        this.operationSession = operationSession;
    }

    public boolean isRequestFromMiniApp() {
        return RequestFromEnum.isRequestFromMiniapp(this.requestFrom);
    }
}

package ai.conrain.aigc.platform.service.model.converter;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.TestItemGroupDO;
import ai.conrain.aigc.platform.dal.example.TestItemGroupExample;
import ai.conrain.aigc.platform.service.enums.TestGroupTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestItemGroupQuery;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * TestItemGroupConverter
 *
 * @version TestItemGroupService.java v 0.1 2024-12-21 06:16:58
 */
public class TestItemGroupConverter {

    /**
     * DO -> VO
     */
    public static TestItemGroupVO do2VO(TestItemGroupDO from) {
        TestItemGroupVO to = new TestItemGroupVO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setType(TestTypeEnum.getByCode(from.getType()));
        to.setStatus(TestStatusEnum.getByCode(from.getStatus()));
        to.setGroupType(TestGroupTypeEnum.getByCode(from.getGroupType()));
        to.setRoundsNum(from.getRoundsNum());
        to.setBatchId(from.getBatchId());
        to.setPositiveNum(from.getPositiveNum());
        to.setNegativeNum(from.getNegativeNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getComparisonParams())) {
            to.setComparisonParams(JSONObject.parseObject(from.getComparisonParams()));
        }
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestItemGroupDO vo2DO(TestItemGroupVO from) {
        TestItemGroupDO to = new TestItemGroupDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setType(from.getType().getCode());
        to.setStatus(from.getStatus().getCode());
        to.setGroupType(from.getGroupType().getCode());
        to.setRoundsNum(from.getRoundsNum());
        to.setBatchId(from.getBatchId());
        to.setPositiveNum(from.getPositiveNum());
        to.setNegativeNum(from.getNegativeNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComparisonParams(from.getComparisonParams() != null ? from.getComparisonParams().toString() : null);
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static TestItemGroupQuery do2Query(TestItemGroupDO from) {
        TestItemGroupQuery to = new TestItemGroupQuery();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setRoundsNum(from.getRoundsNum());
        to.setPositiveNum(from.getPositiveNum());
        to.setNegativeNum(from.getNegativeNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComparisonParams(from.getComparisonParams());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TestItemGroupDO query2DO(TestItemGroupQuery from) {
        TestItemGroupDO to = new TestItemGroupDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setItemId(from.getItemId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setRoundsNum(from.getRoundsNum());
        to.setPositiveNum(from.getPositiveNum());
        to.setNegativeNum(from.getNegativeNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComparisonParams(from.getComparisonParams());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestItemGroupExample query2Example(TestItemGroupQuery from) {
        TestItemGroupExample to = new TestItemGroupExample();
        TestItemGroupExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPlanId())) {
            c.andPlanIdEqualTo(from.getPlanId());
        }
        if (!ObjectUtils.isEmpty(from.getItemId())) {
            c.andItemIdEqualTo(from.getItemId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getRoundsNum())) {
            c.andRoundsNumEqualTo(from.getRoundsNum());
        }
        if (!ObjectUtils.isEmpty(from.getPositiveNum())) {
            c.andPositiveNumEqualTo(from.getPositiveNum());
        }
        if (!ObjectUtils.isEmpty(from.getNegativeNum())) {
            c.andNegativeNumEqualTo(from.getNegativeNum());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestItemGroupVO> doList2VOList(List<TestItemGroupDO> list) {
        return CommonUtil.listConverter(list, TestItemGroupConverter::do2VO);
    }
}
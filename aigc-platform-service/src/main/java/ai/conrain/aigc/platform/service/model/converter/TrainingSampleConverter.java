package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO;
import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.dal.example.TrainingSampleExample;
import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * TrainingSampleConverter
 *
 * @version TrainingSampleService.java
 */
public class TrainingSampleConverter {

    /**
     * DO -> VO
     */
    public static TrainingSampleVO do2VO(TrainingSampleDO from) {
        TrainingSampleVO to = new TrainingSampleVO();
        to.setId(from.getId());
        to.setSampleDataId(from.getSampleDataId());
        to.setSampleDataIdType(from.getSampleDataIdType());
        to.setSampleCaptionId(from.getSampleCaptionId());
        to.setSampleCaptionIdType(from.getSampleCaptionIdType());
        to.setRelatedTrainingId(from.getRelatedTrainingId());
        to.setRelatedTrainingIdType(from.getRelatedTrainingIdType());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TrainingSampleDO vo2DO(TrainingSampleVO from) {
        TrainingSampleDO to = new TrainingSampleDO();
        to.setId(from.getId());
        to.setSampleDataId(from.getSampleDataId());
        to.setSampleDataIdType(from.getSampleDataIdType());
        to.setSampleCaptionId(from.getSampleCaptionId());
        to.setSampleCaptionIdType(from.getSampleCaptionIdType());
        to.setRelatedTrainingId(from.getRelatedTrainingId());
        to.setRelatedTrainingIdType(from.getRelatedTrainingIdType());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TrainingSampleExample query2Example(TrainingSampleQuery from) {
        TrainingSampleExample to = new TrainingSampleExample();
        TrainingSampleExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getSampleDataId())) {
            c.andSampleDataIdEqualTo(from.getSampleDataId());
        }
        if (!ObjectUtils.isEmpty(from.getSampleDataIdType())) {
            c.andSampleDataIdTypeEqualTo(from.getSampleDataIdType());
        }
        if (!ObjectUtils.isEmpty(from.getSampleCaptionId())) {
            c.andSampleCaptionIdEqualTo(from.getSampleCaptionId());
        }
        if (!ObjectUtils.isEmpty(from.getSampleCaptionIdType())) {
            c.andSampleCaptionIdTypeEqualTo(from.getSampleCaptionIdType());
        }
        if (!ObjectUtils.isEmpty(from.getRelatedTrainingId())) {
            c.andRelatedTrainingIdEqualTo(from.getRelatedTrainingId());
        }
        if (!ObjectUtils.isEmpty(from.getRelatedTrainingIdType())) {
            c.andRelatedTrainingIdTypeEqualTo(from.getRelatedTrainingIdType());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<TrainingSampleVO> doList2VOList(List<TrainingSampleDO> list) {
        return CommonUtil.listConverter(list, TrainingSampleConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.integration.xiutu.XiutuService;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class RemoveWrinkleTaskHelper {

    @Autowired
    private XiutuService xiutuService;

    @Autowired
    private CommonTaskService commonTaskService;

    public CommonTaskVO createRemoveWrinkleTask(String originImgUrl, CreativeBatchVO batchVO, Integer taskOrder, Integer requestFrequency) {

        AssertUtil.assertNotBlank(originImgUrl, ResultCode.BIZ_FAIL, "[创建去皱任务]原始图片为空");
        AssertUtil.assertNotNull(batchVO, ResultCode.BIZ_FAIL, "[创建去皱任务]关联的创作批次为空");

        String picId = IntegrationUtils.uuid();
        // taskOrder 从 0 开始
        String targetOssObjectName = IntegrationUtils.buildOssObjectName(batchVO.getUserId(), originImgUrl,
                "rm" + "_" + batchVO.getId() + "_" + taskOrder, "");
        AssertUtil.assertNotBlank(targetOssObjectName, ResultCode.BIZ_FAIL, "[创建去皱任务]创建去皱图片失败, 文件名解析失败");
        boolean success = xiutuService.applyRemoveWrinkle(picId, originImgUrl, targetOssObjectName, 100);
        AssertUtil.assertTrue(success, ResultCode.BIZ_FAIL, "[创建去皱任务]创建衣服去皱任务失败");

        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(batchVO.getUserId());
            task.setOperatorId(batchVO.getOperatorId());
            task.setTaskType(CommonTaskEnums.TaskType.REMOVE_WRINKLE.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.INIT.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.XIU_TU.name());
            task.setOutTaskId(picId);
            task.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATIVE_BATCH.getCode());
            task.setRelatedBizId(batchVO.getId().toString());
            task.setTaskStartTime(new Date());

            JSONObject req = new JSONObject();
            req.put(KEY_ORIGIN_IMAGE, originImgUrl);
            task.setReqBizParams(req.toJSONString());

            task.addExtInfo(KEY_ORIGIN_IMAGE, originImgUrl);
            task.addExtInfo(KEY_XIU_TU_IMG_ID, picId);
            task.addExtInfo(KEY_TARGET_OSS_OBJECT_NAME, targetOssObjectName);
            task.addExtInfo(KEY_START_TIME, DateUtils.formatTime(new Date()));
            task.addExtInfo(KEY_TASK_ORDER, taskOrder);
            task.addExtInfo(KEY_REQUEST_FREQUENCY, requestFrequency);
            task.addExtInfo(KEY_REQUEST_ORDER, 0);

            task = commonTaskService.insert(task);
        }

        log.info("[创建去皱任务]commonTaskId: {}, 指定的请求次数: {}, 当前请求序号: {}", task.getId(), requestFrequency, 0);
        return task;
    }

    public void retryRemoveWrinkleTask(CommonTaskVO taskVO) {
        AssertUtil.assertNotNull(taskVO, ResultCode.PARAM_INVALID, "[重试去皱任务]taskVO is null");
        String originImgUrl = taskVO.getExtInfo().getString(KEY_ORIGIN_IMAGE);
        AssertUtil.assertNotBlank(originImgUrl, ResultCode.BIZ_FAIL, "[重试去皱任务]originImage is null");
        String targetOssObjectName = taskVO.getExtInfo(KEY_TARGET_OSS_OBJECT_NAME);
        if (StringUtils.isBlank(targetOssObjectName)) {
            targetOssObjectName = IntegrationUtils.buildOssObjectName(taskVO.getUserId(), originImgUrl,
                    taskVO.getRelatedBizId() + "_" + taskVO.getTaskOrder() + "_" + "_rm", "");
        }
        AssertUtil.assertNotBlank(targetOssObjectName, ResultCode.BIZ_FAIL, "[重试去皱任务]创建去皱图片失败, 文件名解析失败");
        String picId = IntegrationUtils.uuid();
        boolean success = xiutuService.applyRemoveWrinkle(picId, originImgUrl, targetOssObjectName, 100);
        AssertUtil.assertTrue(success, ResultCode.BIZ_FAIL, "[创建去皱重试]重试羽绒服去皱任务失败");
        // 更新 task
        taskVO.setTaskStartTime(new Date());
        taskVO.addExtInfo(KEY_TARGET_OSS_OBJECT_NAME, targetOssObjectName);
        taskVO.addExtInfo(KEY_START_TIME, DateUtils.formatTime(new Date()));

        int requestOrder = taskVO.getExtInfo(KEY_REQUEST_ORDER, Integer.class);
        int requestFrequency = taskVO.getExtInfo(KEY_REQUEST_FREQUENCY, Integer.class);
        log.info("[创建去皱任务]去皱任务重试/多次调用, commonTaskId: {}, 指定的请求次数: {}, 当前请求序号: {}", taskVO, requestFrequency, requestOrder);
        commonTaskService.updateByIdSelective(taskVO);
    }
}

package ai.conrain.aigc.platform.service.model.request;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;

import ai.conrain.aigc.platform.service.validation.Mobile;

import lombok.Data;

@Data
public class SmsLoginRequest implements Serializable {

    private static final long serialVersionUID = -7080353339041053727L;

    /** 登录id */
    @NotBlank(message = "手机号不能为空")
    @Mobile
    private String mobile;

    /** 密码 */
    @NotBlank(message = "短信验证码不能为空")
    private String code;
}

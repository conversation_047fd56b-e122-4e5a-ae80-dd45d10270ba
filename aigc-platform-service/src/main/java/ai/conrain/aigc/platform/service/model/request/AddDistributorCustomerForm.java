package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.validation.Mobile;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

@Data
public class AddDistributorCustomerForm implements Serializable {

    /** 公司名 */
    @NotBlank(message = "公司名不能为空")
    @Size(min = 2, max = 50, message = "公司名长度必须在2-50之间")
    private String corpName;

    /** 手机号 */
    @NotBlank
    @Mobile
    private String mobile;

    /** 昵称 */
    @NotBlank(message = "昵称不能为空")
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50之间")
    private String nickName;

    /**
     * 关联的渠道商运营id
     */
    private Integer relatedOperatorId;

    /**
     * 关联的渠道商销售id
     */
    private Integer relatedSalesId;

    //是否需要送muse点（100点），Y|N，缺省为N
    private String needInitMusePoints;
}

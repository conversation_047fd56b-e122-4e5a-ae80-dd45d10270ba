package ai.conrain.aigc.platform.service.component.onnx;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Path;
import java.util.Collections;

@Slf4j
@Component
public class ImageQualityOnnxService {

    private SimpleOnnxWrapper onnxWrapper;
    private static final String ONNX_MODEL_PATH = "onnx/image_quality_model.onnx";

    @PostConstruct
    public void init() {
        onnxWrapper = new SimpleOnnxWrapper(ONNX_MODEL_PATH, "图像质量模型服务");
        onnxWrapper.init();
    }

    @PreDestroy
    public void destroy() {
        if (onnxWrapper != null) {
            onnxWrapper.destroy();
        }
    }

    /**
     * 获取图像质量分数，0-1之间
     * @param imageUrl
     * @return
     * @throws OrtException
     * @throws IOException
     */
    public Double getImageQualityScore(String imageUrl) {
        Path tempFile = null;
        try {
            tempFile = OnnxUtil.downloadImageFromUrl(imageUrl);
            String localPath = tempFile.toAbsolutePath().toString();
            float[][][][] imageData = OnnxUtil.loadAndPreprocessImage(localPath);

            // 创建输入张量
            OnnxTensor inputTensor = OnnxTensor.createTensor(onnxWrapper.getEnvironment(), imageData);

            // 执行推理
            OrtSession.Result result = onnxWrapper.getSession().run(Collections.singletonMap("input", inputTensor));

            // 获取输出
            float[][] output = (float[][]) result.get(0).getValue();
            float[] probabilities = output[0];

            // 释放资源
            inputTensor.close();
            result.close();

            log.info("图像质量预测结果：{},url:{}", output, imageUrl);

            // 计算预测结果：预测类别为1（图像质量为好）的概率，0位是差的概率，1位是好的概率
            return (double) probabilities[1];

        } catch (Exception e) {
            log.error("获取图像质量分数失败: {}", e.getMessage());
            return null;
        } finally {
            // 清理临时文件
            OnnxUtil.cleanupTempFile(tempFile);
        }
     }
}

package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Calendar;
import java.text.SimpleDateFormat;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsClothesInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsClothesInfoVO;
import ai.conrain.aigc.platform.service.model.converter.StatsClothesInfoConverter;
import ai.conrain.aigc.platform.dal.dao.StatsClothesInfoDAO;
import ai.conrain.aigc.platform.service.component.StatsClothesInfoService;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;

/**
 * StatsClothesInfoService实现
 *
 * <AUTHOR>
 * @version StatsClothesInfoService.java v 0.1 2025-04-22 05:07:33
 */
@Slf4j
@Service
public class StatsClothesInfoServiceImpl implements StatsClothesInfoService {

    /** DAO */
    @Autowired
    private StatsClothesInfoDAO statsClothesInfoDAO;

    @Override
    public StatsClothesInfoVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsClothesInfoDO data = statsClothesInfoDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsClothesInfoConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsClothesInfoDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsClothesInfo失败");
    }

    @Override
    public StatsClothesInfoVO insert(StatsClothesInfoVO statsClothesInfo) {
        AssertUtil.assertNotNull(statsClothesInfo, ResultCode.PARAM_INVALID, "statsClothesInfo is null");
        AssertUtil.assertTrue(statsClothesInfo.getId() == null, ResultCode.PARAM_INVALID,
                "statsClothesInfo.id is present");

        // 创建时间、修改时间兜底
        if (statsClothesInfo.getCreateTime() == null) {
            statsClothesInfo.setCreateTime(new Date());
        }

        if (statsClothesInfo.getModifyTime() == null) {
            statsClothesInfo.setModifyTime(new Date());
        }

        StatsClothesInfoDO data = StatsClothesInfoConverter.vo2DO(statsClothesInfo);
        Integer n = statsClothesInfoDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsClothesInfo失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsClothesInfo返回id为空");
        statsClothesInfo.setId(data.getId());
        return statsClothesInfo;
    }

    @Override
    public void updateByIdSelective(StatsClothesInfoVO statsClothesInfo) {
        AssertUtil.assertNotNull(statsClothesInfo, ResultCode.PARAM_INVALID, "statsClothesInfo is null");
        AssertUtil.assertTrue(statsClothesInfo.getId() != null, ResultCode.PARAM_INVALID,
                "statsClothesInfo.id is null");

        // 修改时间必须更新
        statsClothesInfo.setModifyTime(new Date());
        StatsClothesInfoDO data = StatsClothesInfoConverter.vo2DO(statsClothesInfo);
        int n = statsClothesInfoDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsClothesInfo失败，影响行数:" + n);
    }

    @Override
    public List<StatsClothesInfoVO> queryStatsClothesInfoList(StatsClothesInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsClothesInfoExample example = StatsClothesInfoConverter.query2Example(query);

        List<StatsClothesInfoDO> list = statsClothesInfoDAO.selectByExample(example);
        return StatsClothesInfoConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsClothesInfoCount(StatsClothesInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsClothesInfoExample example = StatsClothesInfoConverter.query2Example(query);
        long c = statsClothesInfoDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询服装信息统计表
     */
    @Override
    public PageInfo<StatsClothesInfoVO> queryStatsClothesInfoByPage(StatsClothesInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<StatsClothesInfoVO> page = new PageInfo<>();

        StatsClothesInfoExample example = StatsClothesInfoConverter.query2Example(query);
        long totalCount = statsClothesInfoDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<StatsClothesInfoDO> list = statsClothesInfoDAO.selectByExample(example);
        page.setList(StatsClothesInfoConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsClothesInfoDO> statsList) {
        if (statsList == null || statsList.isEmpty()) {
            return 0;
        }
        return statsClothesInfoDAO.batchInsertOrUpdateSelective(statsList);
    }

    @Override
    public List<StatsClothesInfoDO> selectDailyStatsByWeek(String startDate, String endDate) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return Collections.emptyList();
        }

        // 根据开始日期和结束日期获取对应一周的数据
        return statsClothesInfoDAO.selectDailyStatsByWeek(startDate, endDate);
    }

    @Override
    public List<StatsClothesInfoDO> selectDailyStatsByMonth(String startDate, String endDate) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return Collections.emptyList();
        }

        // 根据开始日期和结束日期获取对应一个月的数据
        return statsClothesInfoDAO.selectDailyStatsByWeek(startDate, endDate);
    }

    @Override
    public StatsClothesInfoDO selectStatsInfoByDateAndPeriod(String date, String statsType) {
        // 参数校验
        if (StringUtils.isBlank(date) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 根据日期和统计周期查询日统计数据
        return statsClothesInfoDAO.selectStatsInfoByDateAndPeriod(date, statsType);
    }

    @Override
    public PageInfo<StatsClothesInfoVO> queryStatsClothesInfoByPageWithRealTime(StatsClothesInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        // 针对WEEKLY和MONTHLY类型进行特殊处理
        if (StatsPeriodEnum.WEEKLY.getCode().equals(query.getStatsType()) || StatsPeriodEnum.MONTHLY.getCode().equals(query.getStatsType())) {
            return getStatsWithRealTimeCalculation(query);
        }

        // 其他类型直接使用原有逻辑
        return queryStatsClothesInfoByPage(query);
    }

    /**
     * 获取包含实时计算的统计数据
     * 对于WEEKLY/MONTHLY类型，需要实时计算本周/月的数据
     */
    private PageInfo<StatsClothesInfoVO> getStatsWithRealTimeCalculation(StatsClothesInfoQuery query) {
        try {
            // 首先获取原有的分页数据（历史汇总数据）
            PageInfo<StatsClothesInfoVO> originalPage = queryStatsClothesInfoByPage(query);

            // 计算当前周期的实时数据
            StatsClothesInfoVO currentPeriodStats = calculateCurrentPeriodStats(query.getStatsType());

            // 如果有当前周期的数据，将其添加到结果中
            if (currentPeriodStats != null) {
                List<StatsClothesInfoVO> resultList = new ArrayList<>(originalPage.getList());

                // 检查是否已存在当前周期的数据，如果存在则替换，否则添加
                boolean found = false;
                String currentPeriodDate = getCurrentPeriodDateStr(query.getStatsType());

                for (int i = 0; i < resultList.size(); i++) {
                    StatsClothesInfoVO item = resultList.get(i);
                    if (currentPeriodDate.equals(item.getStatsDate())) {
                        // 替换现有数据
                        resultList.set(i, currentPeriodStats);
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    // 添加到列表开头（最新数据）
                    resultList.add(0, currentPeriodStats);
                }

                // 重新分页处理
                int start = (query.getPageNum() - 1) * query.getPageSize();
                int end = Math.min(start + query.getPageSize(), resultList.size());
                List<StatsClothesInfoVO> pagedList = start < resultList.size() ?
                        resultList.subList(start, end) : new ArrayList<>();

                PageInfo<StatsClothesInfoVO> resultPage = new PageInfo<>();
                resultPage.setList(pagedList);
                resultPage.setSize(pagedList.size());
                resultPage.setTotalCount(resultList.size());
                resultPage.setHasNextPage(end < resultList.size());

                return resultPage;
            }

            return originalPage;
        } catch (Exception e) {
            log.error("实时计算统计数据失败", e);
            // 如果实时计算失败，返回原始数据
            return queryStatsClothesInfoByPage(query);
        }
    }

    /**
     * 计算当前周期的实时统计数据
     */
    private StatsClothesInfoVO calculateCurrentPeriodStats(String statsType) {
        try {
            StatsPeriodEnum periodEnum = StatsPeriodEnum.getByCode(statsType);
            if (periodEnum == null) {
                return null;
            }

            Date now = new Date();
            String startDate = null;
            String endDate = null;
            String currentPeriodDate = getCurrentPeriodDateStr(statsType);

            // 获取本月的开始和结束日期
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);

            if (StatsPeriodEnum.WEEKLY.getCode().equals(statsType)) {
                cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                startDate = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());

                // 今天即为最后一天
                endDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            } else if (StatsPeriodEnum.MONTHLY.getCode().equals(statsType)) {
                cal.set(Calendar.DAY_OF_MONTH, 1);
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                startDate = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());

                // 今天即为最后一天
                endDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }

            if (startDate == null) {
                return null;
            }

            // 查询该时间段内的所有DAILY数据
            List<StatsClothesInfoDO> dailyStats;
            if (StatsPeriodEnum.WEEKLY.getCode().equals(statsType)) {
                dailyStats = selectDailyStatsByWeek(startDate, endDate);
            } else {
                dailyStats = selectDailyStatsByMonth(startDate, endDate);
            }

            if (dailyStats == null || dailyStats.isEmpty()) {
                return null;
            }

            // 累加计算当前周期的统计数据
            StatsClothesInfoDO aggregatedData = new StatsClothesInfoDO();
            aggregatedData.setStatsType(statsType);
            aggregatedData.setStatsDate(currentPeriodDate);
            aggregatedData.setCreateTime(now);
            aggregatedData.setModifyTime(now);

            int vipClothesCount = 0;
            int autoTrainCount = 0;
            int manualDeliveryCount = 0;
            int autoTrainAndDeliveryCount = 0;
            int retryMattingCount = 0;
            int updatePromptCount = 0;
            int copyCount = 0;

            for (StatsClothesInfoDO daily : dailyStats) {
                if (daily.getVipClothesCount() != null) {
                    vipClothesCount += daily.getVipClothesCount();
                }
                if (daily.getAutoTrainCount() != null) {
                    autoTrainCount += daily.getAutoTrainCount();
                }
                if (daily.getManualDeliveryCount() != null) {
                    manualDeliveryCount += daily.getManualDeliveryCount();
                }
                if (daily.getAutoTrainAndDeliveryCount() != null) {
                    autoTrainAndDeliveryCount += daily.getAutoTrainAndDeliveryCount();
                }
                if (daily.getRetryMattingCount() != null) {
                    retryMattingCount += daily.getRetryMattingCount();
                }
                if (daily.getUpdatePromptCount() != null) {
                    updatePromptCount += daily.getUpdatePromptCount();
                }
                if (daily.getCopyCount() != null) {
                    copyCount += daily.getCopyCount();
                }
            }

            aggregatedData.setVipClothesCount(vipClothesCount);
            aggregatedData.setAutoTrainCount(autoTrainCount);
            aggregatedData.setManualDeliveryCount(manualDeliveryCount);
            aggregatedData.setAutoTrainAndDeliveryCount(autoTrainAndDeliveryCount);
            aggregatedData.setRetryMattingCount(retryMattingCount);
            aggregatedData.setUpdatePromptCount(updatePromptCount);
            aggregatedData.setCopyCount(copyCount);

            return StatsClothesInfoConverter.do2VO(aggregatedData);

        } catch (Exception e) {
            log.error("计算当前周期统计数据失败, statsType: {}", statsType, e);
            return null;
        }
    }

    /**
     * 获取当前周期的日期字符串
     */
    private String getCurrentPeriodDateStr(String statsType) {
        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        if (StatsPeriodEnum.WEEKLY.getCode().equals(statsType)) {
            // 获取本周一的日期
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            return sdf.format(cal.getTime());
        } else if (StatsPeriodEnum.MONTHLY.getCode().equals(statsType)) {
            // 获取本月第一天的日期
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            return sdf.format(cal.getTime());
        }
        return sdf.format(now);
    }

}
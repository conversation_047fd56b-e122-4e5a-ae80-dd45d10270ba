package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import com.alibaba.fastjson.JSON;

import ai.conrain.aigc.platform.dal.entity.StatsSaleIndicatorsDO;
import ai.conrain.aigc.platform.dal.example.StatsSaleIndicatorsExample;
import ai.conrain.aigc.platform.service.model.query.StatsSaleIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsSaleIndicatorsVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * StatsSaleIndicatorsConverter
 *
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
public class StatsSaleIndicatorsConverter {

    /**
     * DO -> VO
     */
    public static StatsSaleIndicatorsVO do2VO(StatsSaleIndicatorsDO from) {
        StatsSaleIndicatorsVO to = new StatsSaleIndicatorsVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setParentId(from.getParentId());
        to.setClothesExpCount(from.getClothesExpCount());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setCreateCount(from.getCreateCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getExtInfo())) {    
            to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsSaleIndicatorsDO vo2DO(StatsSaleIndicatorsVO from) {
        StatsSaleIndicatorsDO to = new StatsSaleIndicatorsDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setParentId(from.getParentId());
        to.setClothesExpCount(from.getClothesExpCount());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setCreateCount(from.getCreateCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        // 如果extInfo为空，则添加默认值
        if (from.getExtInfo() == null){
            from.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, 0);
        }
        
        to.setExtInfo(JSON.toJSONString(from.getExtInfo()));

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsSaleIndicatorsQuery do2Query(StatsSaleIndicatorsDO from) {
        StatsSaleIndicatorsQuery to = new StatsSaleIndicatorsQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setParentId(from.getParentId());
        to.setClothesExpCount(from.getClothesExpCount());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setCreateCount(from.getCreateCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsSaleIndicatorsDO query2DO(StatsSaleIndicatorsQuery from) {
        StatsSaleIndicatorsDO to = new StatsSaleIndicatorsDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setParentId(from.getParentId());
        to.setClothesExpCount(from.getClothesExpCount());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setCreateCount(from.getCreateCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsSaleIndicatorsExample query2Example(StatsSaleIndicatorsQuery from) {
        StatsSaleIndicatorsExample to = new StatsSaleIndicatorsExample();
        StatsSaleIndicatorsExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdLike("%" +from.getUserId() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameLike("%"+from.getName()+"%");
        }
        if (!ObjectUtils.isEmpty(from.getParentId())) {
            c.andParentIdEqualTo(from.getParentId());
        }
        if (!ObjectUtils.isEmpty(from.getClothesExpCount())) {
            c.andClothesExpCountEqualTo(from.getClothesExpCount());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerConversionCount())) {
            c.andCustomerConversionCountEqualTo(from.getCustomerConversionCount());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerConsumptionPoints())) {
            c.andCustomerConsumptionPointsEqualTo(from.getCustomerConsumptionPoints());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerActivityRate())) {
            c.andCustomerActivityRateEqualTo(from.getCustomerActivityRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerRepurchaseRate())) {
            c.andCustomerRepurchaseRateEqualTo(from.getCustomerRepurchaseRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomModelCustomers())) {
            c.andCustomModelCustomersEqualTo(from.getCustomModelCustomers());
        }
        if (!ObjectUtils.isEmpty(from.getCustomSceneCustomers())) {
            c.andCustomSceneCustomersEqualTo(from.getCustomSceneCustomers());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerProtectionMetrics())) {
            c.andCustomerProtectionMetricsEqualTo(from.getCustomerProtectionMetrics());
        }
        if (!ObjectUtils.isEmpty(from.getCreateCount())) {
            c.andCreateCountEqualTo(from.getCreateCount());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<StatsSaleIndicatorsVO> doList2VOList(List<StatsSaleIndicatorsDO> list) {
        return CommonUtil.listConverter(list, StatsSaleIndicatorsConverter::do2VO);
    }
}
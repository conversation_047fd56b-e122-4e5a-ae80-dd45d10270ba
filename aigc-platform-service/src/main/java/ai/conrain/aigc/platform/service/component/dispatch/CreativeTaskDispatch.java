/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.service.component.CreativeBatchElementsService;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.helper.FileHelper;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.BizConstants.BIZ_TAG;
import static ai.conrain.aigc.platform.service.constants.BizConstants.SYSTEM_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_LORA_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CLOTH_LORA_PATHS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_LORA;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LORA_PATH;

/**
 * 创作任务分发
 *
 * <AUTHOR>
 * @version : CreativeTaskDispatch.java, v 0.1 2024/8/9 19:26 renxiao.wu Exp $
 */
@Slf4j
@Service
public class CreativeTaskDispatch extends AbstractTaskDispatch {
    @Value("${comfyui.lora.path}")
    private String loraRoot;
    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeBatchElementsService creativeBatchElementsService;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private FileHelper fileHelper;

    @Override
    public DispatchTypeEnum getType() {
        return DispatchTypeEnum.CREATIVE_TASK;
    }

    @Override
    protected List<? extends IExtModel> queryUnProcessedData(Integer pipelineId, int idleNum) {
        return creativeBatchService.queryUnProcessedTopUser(pipelineId, CreativeTypeEnum.getPictureMattingTypeList(),
            null, idleNum);
    }

    @Override
    protected boolean checkMachineMatch(DispatchTypeEnum type, IExtModel task, ServerVO server) {
        CreativeBatchVO batch = (CreativeBatchVO)task;
        if (batch.getType() != CreativeTypeEnum.CREATE_IMAGE) {
            return true;
        }

        String fileServerUrl = serverHelper.getFileServerUrl(server);
        String serverUrl = serverHelper.getServerUrl(server);
        if (StringUtils.isBlank(fileServerUrl)) {
            log.warn("【任务分发】判断lora文件是否存在，获取当前文件服务器地址失败，直接跳过当前服务，server={},taskId={}",
                serverUrl, task.getId());
            return false;
        }

        // 1.检测服装lora是否存在，不存在，则进行同步
        //TODO by半泉: 这里的KEY_CLOTH_LORA_PATH可能因为主子模型的问题，获取不到，需要做优化，现留个备注
        boolean exists = isExistsLora(batch, fileServerUrl);
        if (!exists) {
            log.warn("【任务分发】判断服装lora文件是否存在，lora不存在，直接跳过当前服务，server={},taskId={}", serverUrl,
                task.getId());
            return false;
        }

        // 2.检测其他lora是否存在，不存在，则进行同步
        boolean result = true; // 汇总，只要有一个lora不存在，则返回false
        List<CreativeElementVO> elements = creativeBatchElementsService.queryBatchElements(batch.getId(), false);
        for (CreativeElementVO element : elements) {
            CreativeElementVO target = element;
            if (element.getLevel() > 2) {
                target = creativeElementService.selectById(element.getParentId());
            }
            if (target == null || target.getLoraModelId() == null) {
                continue;
            }

            String key = StringUtils.equals(target.getConfigKey(), ElementConfigKeyEnum.SCENE.name()) ? KEY_LORA_PATH
                : KEY_FACE_LORA;
            String loraPath = target.getExtInfo(key, String.class);
            exists = checkLoraExists(batch, target.getLoraModelId(), loraPath, fileServerUrl);
            result = result && exists;
        }

        log.info("【任务分发】判断lora文件是否存在，result={}，server={}，taskId={}", result, serverUrl, task.getId());

        return result;
    }

    @Override
    protected boolean isTaskFinished(Integer taskId) {
        if (taskId == null) {
            log.warn("【任务分发】判断任务是否已经执行完毕，taskId为空，type={},taskId={}", getType(), taskId);
            return true;
        }
        CreativeBatchVO batch = creativeBatchService.selectById(taskId);
        return batch == null || batch.getStatus().isEnd();
    }

    @Override
    protected Integer getDispatchUserId(IExtModel task) {
        //如果是系统标，则走系统的分发账号
        if (StringUtils.equals(SYSTEM_IMAGES, task.getExtValue(BIZ_TAG, String.class))) {
            return CommonUtil.mockSystemContext().getMasterUser();
        }

        return super.getDispatchUserId(task);
    }

    /**
     * 判断服装lora是否存在
     *
     * @param batch         批次id
     * @param fileServerUrl 文件服务地址
     * @return true，存在
     */
    private boolean isExistsLora(CreativeBatchVO batch, String fileServerUrl) {
        Integer modelId = batch.getModelId();
        String subLoraNamesStr = batch.getExtInfo(KEY_CLOTH_LORA_PATHS, String.class);
        // 单个lora的情况下只检查一个就可以了
        if (StringUtils.isBlank(subLoraNamesStr)) {
            String clothLoraPath = batch.getExtValue(KEY_CLOTH_LORA_PATH, String.class);
            return checkLoraExists(batch, modelId, clothLoraPath, fileServerUrl);
        }

        // 如果是主模型模式，则遍历所有的lora
        JSONArray subLoraNames = JSONArray.parseArray(subLoraNamesStr);
        log.info("【任务分发】发现主子模型类型，批量检查loraList={},taskId={}", subLoraNames, batch.getId());

        boolean exists = true;
        for (Object subLoraName : subLoraNames) {
            //只要有一个lora不存在，则返回false，但会遍历所有的lora，不存在时下载所有的lora
            exists = exists && checkLoraExists(batch, modelId, (String)subLoraName, fileServerUrl);
        }

        return exists;
    }

    /**
     * 检查lora是否存在，如果不存在，则进行同步
     *
     * @param batch         批次
     * @param modelId       模型id
     * @param filePath      文件相对路径
     * @param fileServerUrl 文件服务
     * @return true，存在
     */
    private boolean checkLoraExists(CreativeBatchVO batch, Integer modelId, String filePath, String fileServerUrl) {
        if (StringUtils.isBlank(filePath)) {
            return true;
        }

        boolean exists = comfyUIService.checkFileExists(loraRoot + filePath, fileServerUrl);
        if (!exists) {
            syncLora(batch, modelId, fileServerUrl, filePath);
        }

        return exists;
    }

    /**
     * 同步lora
     *
     * @param task          任务
     * @param fileServerUrl 文件服务
     * @param filePath      文件地址
     */
    private void syncLora(IExtModel task, Integer modelId, String fileServerUrl, String filePath) {
        boolean success = fileHelper.syncFile(loraRoot + filePath, null);

        log.info(
            "【任务分发】当前服务器无lora，发起同步lora文件的消息，taskId={}，modelId={}，server={}，filePath={},success={}",
            task.getId(), modelId, fileServerUrl, filePath, success);
    }
}

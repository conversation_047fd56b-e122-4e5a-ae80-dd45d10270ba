package ai.conrain.aigc.platform.service.model.converter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.dal.entity.ServerDO;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.query.ServerQuery;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;

/**
 * ServerConverter
 *
 * @version ServerService.java v 0.1 2024-06-15 05:24:29
 */
public class ServerConverter {

    /**
     * DO -> VO
     */
    public static ServerVO do2VO(ServerDO from) {
        ServerVO to = new ServerVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setLevel(from.getLevel());
        to.setConfig(from.getConfig());
        to.setConfigAlias(from.getConfigAlias());
        to.setIntranetAddress(from.getIntranetAddress());
        to.setType(ServerTypeEnum.getByCode(from.getType()));
        to.setStatus(ServerStatusEnum.getByCode(from.getStatus()));
        to.setParentId(from.getParentId());
        to.setPipelineId(from.getPipelineId());
        to.setDeviceId(from.getDeviceId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ServerDO vo2DO(ServerVO from) {
        ServerDO to = new ServerDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setLevel(from.getLevel());
        to.setConfig(from.getConfig());
        to.setConfigAlias(from.getConfigAlias());
        to.setIntranetAddress(from.getIntranetAddress());
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setParentId(from.getParentId());
        to.setPipelineId(from.getPipelineId());
        to.setDeviceId(from.getDeviceId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDeleted(false);

        return to;
    }

    /**
     * DO -> Query
     */
    public static ServerQuery do2Query(ServerDO from) {
        ServerQuery to = new ServerQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setLevel(from.getLevel());
        to.setConfig(from.getConfig());
        to.setType(from.getType());
        to.setParentId(from.getParentId());
        to.setPipelineId(from.getPipelineId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static ServerDO query2DO(ServerQuery from) {
        ServerDO to = new ServerDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setLevel(from.getLevel());
        to.setConfig(from.getConfig());
        to.setType(from.getType());
        to.setParentId(from.getParentId());
        to.setPipelineId(from.getPipelineId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ServerVO> doList2VOList(List<ServerDO> list) {
        List<ServerVO> result = CommonUtil.listConverter(list, ServerConverter::do2VO);
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(vo -> {
                if (vo.getParentId() != null) {
                    vo.setParent(result.stream().filter(parent -> parent.getId().equals(vo.getParentId())).findFirst()
                        .orElse(null));
                }

                if (vo.getLevel() != null && vo.getLevel().equals(1)) {
                    List<ServerVO> children = result.stream().filter(
                        child -> child.getParentId() != null && child.getParentId().equals(vo.getId())).collect(
                        Collectors.toList());
                    vo.setChildren(children);
                }
            });
        }
        return result;
    }

    public static List<ServerVO> toListByLevel(List<ServerVO> list) {
        return toListByLevel(list, 1, null);
    }

    /**
     * 递归方式遍历生成树
     *
     * @param list     列表
     * @param level    登记
     * @param parentId 父节点id
     * @return 结果
     */
    private static List<ServerVO> toListByLevel(List<ServerVO> list, Integer level, Integer parentId) {
        List<ServerVO> current = list.stream().filter(
            vo -> vo.getLevel() != null && vo.getLevel().equals(level) && (parentId == null || parentId.equals(
                vo.getParentId()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(current)) {
            return new ArrayList<>();
        }

        current.forEach(vo -> {
            vo.setChildren(toListByLevel(list, level + 1, vo.getId()));
        });

        return current;
    }
}
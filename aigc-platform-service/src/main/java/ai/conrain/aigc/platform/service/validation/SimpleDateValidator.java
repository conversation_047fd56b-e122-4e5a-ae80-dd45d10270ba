/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.validation;

import java.util.regex.Pattern;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * yyyyMMdd格式的日期字符串校验器
 *
 * <AUTHOR>
 * @version : SimpleDateValidator.java, v 0.1 2023/9/10 23:26 renxiao.wu Exp $
 */
@Slf4j
public class SimpleDateValidator implements ConstraintValidator<SimpleDate, String> {
    private static Pattern PATTERN = Pattern.compile("^(?:19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])$");

    @Override
    public boolean isValid(String date, ConstraintValidatorContext constraintValidatorContext) {
        if (date == null){
            return true;
        }
        if (StringUtils.isBlank(date)) {
            log.warn("日期字符串校验失败，date为空，date={}", date);
            return false;
        }
        boolean result = PATTERN.matcher(date).matches();
        if (!result) {
            log.warn("日期字符串校验失败，date非法，date={}", date);
        }
        return result;
    }
}

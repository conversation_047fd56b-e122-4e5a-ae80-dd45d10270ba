package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import java.util.List;

public interface DistributorService {

    void deleteCustomer(Integer userId);

    DistributorCustomerVO queryDistributorInfoByMerchantId(Integer merchantId);

    String getSalesInfoByMerchantId(Integer userId);

    /**
     * 是否是归属的渠道客户
     *
     * @param merchantId 商户id
     * @return true，是，false，否
     */
    boolean isOwnDistributorCustomer(Integer merchantId);

    List<UserVO> queryAllStaffListByCurrentUser();

    List<UserVO> queryAllStaffListByCurrentUserAndRoles(CustomRoleEnum... customRoleEnums);

    List<UserVO> queryAllCustomersByDistributorMasterId(Integer distributorMasterId);

    /**
     * 查询当前渠道下的结算主体, 直营渠道下有渠道主账号, 销售, 二级渠道管理员, 非直营的结算主体为渠道 corp
     *
     * @param org  组织
     * @param exceptCustomRoles 排除的角色
     * @return 结果
     */

    void querySettlePrincipalExclusive(List<PrincipalModel> results, OrganizationVO org, CustomRoleEnum... exceptCustomRoles);

    /**
     * 获取分佣结算关联的商家
     * @param principal 结算主体
     * @return 商家id
     */
    List<Integer> getPrincipalRelatedMerchants(PrincipalModel principal);

    /**
     * 异步尝试自动为客户指派工程师
     * 逻辑：充值成功后，如果用户缪斯点达到阈值且未指派工程师，则根据其销售/渠道商的工程师进行自动指派
     *
     * @param customerUserId 客户用户ID
     */
    void tryAutoAssignEngineerToCustomerAsync(Integer customerUserId);
}

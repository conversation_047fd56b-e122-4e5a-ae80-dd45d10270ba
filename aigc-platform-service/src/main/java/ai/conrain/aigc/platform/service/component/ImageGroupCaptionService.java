package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 图像组标注表，pair对打标的最终数据 Service定义
 *
 * <AUTHOR>
 * @version ImageGroupCaptionService.java v 0.1 2025-08-14 11:09:51
 */
public interface ImageGroupCaptionService {
	
	/**
	 * 查询图像组标注表，pair对打标的最终数据对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageGroupCaptionVO selectById(Integer id);

	/**
	 * 删除图像组标注表，pair对打标的最终数据对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像组标注表，pair对打标的最终数据对象
	 * @param imageGroupCaption 对象参数
	 * @return 返回结果
	 */
	ImageGroupCaptionVO insert(ImageGroupCaptionVO imageGroupCaption);

	/**
	 * 修改图像组标注表，pair对打标的最终数据对象
	 * @param imageGroupCaption 对象参数
	 */
	void updateByIdSelective(ImageGroupCaptionVO imageGroupCaption);

	/**
	 * 带条件批量查询图像组标注表，pair对打标的最终数据列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageGroupCaptionVO> queryImageGroupCaptionList(ImageGroupCaptionQuery query);

	/**
	 * 带条件查询图像组标注表，pair对打标的最终数据数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageGroupCaptionCount(ImageGroupCaptionQuery query);

	/**
	 * 带条件分页查询图像组标注表，pair对打标的最终数据
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageGroupCaptionVO> queryImageGroupCaptionByPage(ImageGroupCaptionQuery query);

    /**
     * 聚合标注结果
     *
     * @param userIds 需要统计的用户列表
     */
    void aggregateCaptionResult(List<Integer> userIds);

    /**
     * 聚合标注结果（支持强制更新）
     *
     * @param userIds 需要统计的用户列表
     * @param forceUpdate 是否强制更新（忽略时间比较）
     */
    void aggregateCaptionResult(List<Integer> userIds, boolean forceUpdate);

    /**
     * 处理指定图片组的标注聚合（用于测试）
     *
     * @param imageGroupId 图片组ID
     * @param userIds 用户ID列表
     */
    void processSpecificImageGroupCaption(Integer imageGroupId, List<Integer> userIds);

    /**
     * 查询需要更新的图片组数量
     *
     * @param userIds 用户ID列表
     * @param forceUpdate 是否强制更新
     * @return 需要更新的图片组数量
     */
    Long countImageGroupsForUpdate(List<Integer> userIds, boolean forceUpdate);
}
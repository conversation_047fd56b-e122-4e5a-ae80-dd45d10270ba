package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO;
import ai.conrain.aigc.platform.service.model.query.InvoiceTitleQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 发票抬头 Service定义
 *
 * <AUTHOR>
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
public interface InvoiceTitleService {
	
	/**
	 * 查询发票抬头对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	InvoiceTitleVO selectById(Integer id);

	/**
	 * 删除发票抬头对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加发票抬头对象
	 * @param invoiceTitle 对象参数
	 * @return 返回结果
	 */
	InvoiceTitleVO insert(InvoiceTitleVO invoiceTitle);

	/**
	 * 修改发票抬头对象
	 * @param invoiceTitle 对象参数
	 */
	void updateByIdSelective(InvoiceTitleVO invoiceTitle);

	/**
	 * 带条件批量查询发票抬头列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<InvoiceTitleVO> queryInvoiceTitleList(InvoiceTitleQuery query);

	/**
	 * 带条件查询发票抬头数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryInvoiceTitleCount(InvoiceTitleQuery query);

	/**
	 * 带条件分页查询发票抬头
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<InvoiceTitleVO> queryInvoiceTitleByPage(InvoiceTitleQuery query);
}
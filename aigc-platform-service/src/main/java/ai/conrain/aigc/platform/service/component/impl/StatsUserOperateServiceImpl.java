package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO;
import ai.conrain.aigc.platform.dal.example.StatsUserOperateExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsUserOperateQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsUserOperateVO;
import ai.conrain.aigc.platform.service.model.converter.StatsUserOperateConverter;
import ai.conrain.aigc.platform.dal.dao.StatsUserOperateDAO;
import ai.conrain.aigc.platform.service.component.StatsUserOperateService;
import org.springframework.util.StringUtils;

/**
 * StatsUserOperateService实现
 *
 * <AUTHOR>
 * @version StatsUserOperateService.java v 0.1 2025-04-25 02:47:41
 */
@Slf4j
@Service
public class StatsUserOperateServiceImpl implements StatsUserOperateService {

    /** DAO */
    @Autowired
    private StatsUserOperateDAO statsUserOperateDAO;
    @Autowired
    private UserService userService;
    @Autowired private MaterialModelDAO materialModelDAO;

    @Override
    public StatsUserOperateVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsUserOperateDO data = statsUserOperateDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsUserOperateConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsUserOperateDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsUserOperate失败");
    }

    @Override
    public StatsUserOperateVO insert(StatsUserOperateVO statsUserOperate) {
        AssertUtil.assertNotNull(statsUserOperate, ResultCode.PARAM_INVALID, "statsUserOperate is null");
        AssertUtil.assertTrue(statsUserOperate.getId() == null, ResultCode.PARAM_INVALID, "statsUserOperate.id is present");

        // 创建时间、修改时间兜底
        if (statsUserOperate.getCreateTime() == null) {
            statsUserOperate.setCreateTime(new Date());
        }

        if (statsUserOperate.getModifyTime() == null) {
            statsUserOperate.setModifyTime(new Date());
        }

        StatsUserOperateDO data = StatsUserOperateConverter.vo2DO(statsUserOperate);
        Integer n = statsUserOperateDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsUserOperate失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsUserOperate返回id为空");
        statsUserOperate.setId(data.getId());
        return statsUserOperate;
    }


    @Override
    public void updateByIdSelective(StatsUserOperateVO statsUserOperate) {
        AssertUtil.assertNotNull(statsUserOperate, ResultCode.PARAM_INVALID, "statsUserOperate is null");
        AssertUtil.assertTrue(statsUserOperate.getId() != null, ResultCode.PARAM_INVALID, "statsUserOperate.id is null");

        // 修改时间必须更新
        statsUserOperate.setModifyTime(new Date());
        StatsUserOperateDO data = StatsUserOperateConverter.vo2DO(statsUserOperate);
        int n = statsUserOperateDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsUserOperate失败，影响行数:" + n);
    }

    @Override
    public List<StatsUserOperateVO> queryStatsUserOperateList(StatsUserOperateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsUserOperateExample example = StatsUserOperateConverter.query2Example(query);

        List<StatsUserOperateDO> list = statsUserOperateDAO.selectByExample(example);
        return StatsUserOperateConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsUserOperateCount(StatsUserOperateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsUserOperateExample example = StatsUserOperateConverter.query2Example(query);
        long c = statsUserOperateDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询用户操作统计表（出图、下载）
     */
    @Override
    public PageInfo<StatsUserOperateVO> queryStatsUserOperateByPage(StatsUserOperateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        // 若 parentId 不为空则查询其子账号id 集合
        List<Integer> childIds = new ArrayList<>();
        if (!StringUtils.isEmpty(query.getParentId())) {
            UserQuery userQuery = new UserQuery();
            userQuery.setMasterId(query.getParentId());
            childIds = userService.queryIdList(userQuery);
        }else {
            childIds.add(query.getUserId());
        }

        // 分页查询
        PageInfo<StatsUserOperateVO> page = new PageInfo<>();

        // 若无子账号则直接返回
        if (CollectionUtils.isEmpty(childIds)){
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);
            return page;
        }

        // 设置查询条件
        query.setUserIdList(childIds);


        StatsUserOperateExample example = StatsUserOperateConverter.query2Example(query);
        long totalCount = statsUserOperateDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<StatsUserOperateDO> list = statsUserOperateDAO.selectByExampleWithBLOBs(example);
        page.setList(StatsUserOperateConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsUserOperateVO> statsList) {
        List<StatsUserOperateDO> statsDOList = new ArrayList<>();
        for (StatsUserOperateVO statsVO : statsList) {
            StatsUserOperateDO statsDO = StatsUserOperateConverter.vo2DO(statsVO);
            statsDOList.add(statsDO);
        }

        return statsUserOperateDAO.batchInsertOrUpdate(statsDOList);
    }

    @Override
    public Map<String, Object> getUserCreativeData(Integer userId, Boolean isParent) {
        // 创建返回结果
        Map<String, Object> result = new HashMap<>();

        // 如果为父账号，则查询其子账号
        List<Integer> userIdList = new ArrayList<>();
        if (isParent) {
            UserQuery userQuery = new UserQuery();
            userQuery.setMasterId(userId);
            userIdList = userService.queryIdList(userQuery);
        } else {
            userIdList.add(userId);
        }

        if (CollectionUtils.isEmpty(userIdList)){
            return null;
        }

        // 用户累计出图量（图片数量）
        result.put("totalCreateCount", statsUserOperateDAO.selectImageCount(userIdList));
        // 用户累计下载量（图片数量）
        result.put("totalDownloadCount", statsUserOperateDAO.selectDownloadCount(userIdList));
        // 获取用户使用了多少套服装
        result.put("modelCount", statsUserOperateDAO.selectModelCount(userIdList));
        // 统计用户累计出图多少次
        result.put("createCount", statsUserOperateDAO.selectCreativeCount(userIdList));
        // 获取用户使用最多的5套服装
        List<Map<String, Object>> favoriteMaterials = statsUserOperateDAO.selectTopMaterialsByUsage(userIdList, 5);
        favoriteMaterials = favoriteMaterials.stream().map(material -> {
            // 获取id 值
            Integer materialId = (Integer) material.get("materialId");
            // 添加服装名称
            MaterialModelDO materialDO = materialModelDAO.selectByPrimaryKey(materialId);
            material.put("materialName", materialDO != null ? materialDO.getName() : null);
            return material;
        }).collect(Collectors.toList());
        result.put("topMaterials", favoriteMaterials);

        // 用户出图下载数量信息 
        List<Map<String, Object>> userOperateData = statsUserOperateDAO.selectUserOperateData(userIdList);
        // 填充用户昵称
        userOperateData = userOperateData.stream().map(data -> {
            Integer currentUserId = (Integer) data.get("userId");
            UserVO userVO = userService.selectById(currentUserId);
            data.put("nickname", userVO != null ? userVO.getNickName() : null);
            return data;
        }).collect(Collectors.toList());
        result.put("userOperateData", userOperateData);

        // 返回结果
        return result;
    }

}
package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.dal.dao.UserDAO;
import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.PromptModificationLogService;
import ai.conrain.aigc.platform.service.component.StatsClothesInfoService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.PromptModificationLogQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.function.BiFunction;
import java.util.ArrayList;

@Slf4j
@Service("clothesInfoDataStatsServiceImpl")
public class ClothesInfoDataStatsServiceImpl extends AbstractDataStatsServiceImpl {
    @Autowired
    private StatsClothesInfoService statsClothesInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private PromptModificationLogService promptModificationLogService;
    @Autowired
    private MaterialModelDAO materialModelDAO;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.CLOTHES_INFO;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
            boolean isTotal) {
        // 初始化 统计数据对象
        StatsClothesInfoDO statsClothesInfoDO = new StatsClothesInfoDO();
        statsClothesInfoDO.setStatsDate(storageDate);
        statsClothesInfoDO.setStatsType(periodEnum.getCode());
        statsClothesInfoDO.setCreateTime(new Date());
        statsClothesInfoDO.setModifyTime(new Date());

        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case DAILY:
                return executeDailyBusinessStats(startDate, endDate, statsClothesInfoDO);
            case WEEKLY:
                return executeWeeklyBusinessStats(startDate, endDate, statsClothesInfoDO);
            case MONTHLY:
                return executeMonthlyBusinessStats(startDate, endDate, statsClothesInfoDO);
            case TOTAL:
                return executeTotalBusinessStats(startDate, endDate, statsClothesInfoDO);
            default:
                return 0;
        }
    }

    /**
     * 执行每日业务统计
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     * @return 变更条数
     */
    private int executeDailyBusinessStats(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        // 参数校验
        if (startDate == null || endDate == null) {
            log.warn("【{}】executeDailyBusinessStats 参数为空: startDate={}, endDate={}", getStatsType().getCode(),
                    startDate, endDate);
            return 0;
        }

        // 1、获取 vip 客户上传服装数量
        fillVipUploadClothesCount(startDate, endDate, statsClothesInfoDO);
        // 2、获取自动训练、自动交付、人工交付以及二次抠图（上传图片、系统级抠图）、copy 的服装数量
        fillTrainAndDeliveryAndReMattingCount(startDate, endDate, statsClothesInfoDO);
        // 3、获取修改 prompt 的数量
        fillModifyPromptCount(startDate, endDate, statsClothesInfoDO);
        // 4、获取 copy 的服装数量
        fillCopyClothesCount(startDate, endDate, statsClothesInfoDO);

        // 返回处理的记录数
        return statsClothesInfoService.batchInsertOrUpdate(Collections.singletonList(statsClothesInfoDO));
    }

    /**
     * 执行每周业务统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 变更条数
     */
    private int executeWeeklyBusinessStats(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        // 调用通用周期统计方法，传入周数据查询方法
        return executePeriodBusinessStats(startDate, endDate, statsClothesInfoDO,
                (start, end) -> statsClothesInfoService.selectDailyStatsByWeek(start, end));
    }

    /**
     * 执行每月业务统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 变更条数
     */
    private int executeMonthlyBusinessStats(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        // 调用通用周期统计方法，传入月数据查询方法
        return executePeriodBusinessStats(startDate, endDate, statsClothesInfoDO,
                (start, end) -> statsClothesInfoService.selectDailyStatsByMonth(start, end));
    }

    /**
     * 执行总业务统计
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     * @return 变更条数
     */
    private int executeTotalBusinessStats(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        return executePeriodBusinessStats(startDate, endDate, statsClothesInfoDO, (start, end) -> {
            // 获取数据集合
            List<StatsClothesInfoDO> resultList = new ArrayList<>();
            
            // 查询昨天的Total记录
            StatsClothesInfoDO yesterdayTotalStats = statsClothesInfoService
                    .selectStatsInfoByDateAndPeriod(start, StatsPeriodEnum.TOTAL.getCode());
            if (yesterdayTotalStats != null) {
                resultList.add(yesterdayTotalStats);
            }
            
            // 查询今天的Daily记录
            StatsClothesInfoDO todayDailyStats = statsClothesInfoService
                    .selectStatsInfoByDateAndPeriod(end, StatsPeriodEnum.DAILY.getCode());
            if (todayDailyStats != null) {
                resultList.add(todayDailyStats);
            }
            
            return resultList;
        });
    }

    /**
     * 获取当前日期的 vip 客户上传服装数量
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     */
    private void fillVipUploadClothesCount(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        try {
            // vip客户列表
            List<UserVO> vipUserList = userService.queryAll3999VIPOrPaidCustomer();

            // vip 客户 id 列表
            List<Integer> vipUserIds = vipUserList.stream().map(UserVO::getId).distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(vipUserIds)) {
                // 构建查询条件
                MaterialModelQuery materialModelQuery = new MaterialModelQuery();
                materialModelQuery.setUserIds(vipUserIds);
                materialModelQuery.setStartDate(startDate);
                materialModelQuery.setEndDate(endDate);
                materialModelQuery.setIsIgnoreCopy(Boolean.TRUE);
                materialModelQuery.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
                materialModelQuery.setMaterialType(MaterialType.cloth.name());
                materialModelQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
                materialModelQuery.setType(ModelTypeEnum.CUSTOM.getCode());

                // 查询数量
                Long count = materialModelService.queryMaterialModelCount(materialModelQuery);

                // 设置统计结果
                statsClothesInfoDO.setVipClothesCount(count.intValue());
            } else {
                log.info("【{}】无VIP客户数据，跳过统计", getStatsType().getCode());
            }
        } catch (Exception e) {
            log.error("【{}】fillVipUploadClothesCount执行异常: startDate={}, endDate={}",
                    getStatsType().getCode(), startDate, endDate, e);
        }
    }

    /**
     * 填充自动训练、自动交付、人工交付的服装数量
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     */
    private void fillTrainAndDeliveryAndReMattingCount(String startDate, String endDate,
            StatsClothesInfoDO statsClothesInfoDO) {
        try {
            // 封装查询条件
            MaterialModelQuery materialModelQuery = new MaterialModelQuery();
            materialModelQuery.setStartDate(startDate);
            materialModelQuery.setEndDate(endDate);
            materialModelQuery.setIsIgnoreCopy(Boolean.TRUE);
            materialModelQuery.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
            materialModelQuery.setMaterialType(MaterialType.cloth.name());
            materialModelQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
            materialModelQuery.setType(ModelTypeEnum.CUSTOM.getCode());

            // 查询当天的服装
            List<MaterialModelVO> materialModelVOS = materialModelService.queryMaterialModelList(materialModelQuery);

            // 如果查询结果为空,则直接返回
            if (materialModelVOS == null || materialModelVOS.isEmpty()) {
                return;
            }

            // 统计自动训练数量
            long autoTrainCount = materialModelVOS.stream()
                    .map(model -> model.getExtInfo(CommonConstants.KEY_AUTO_TRAIN, String.class))
                    .filter("true"::equals)
                    .count();

            // 统计自动训练且自动交付的数量
            long bothAutoCount = materialModelVOS.stream()
                    .filter(model -> "true".equals(model.getExtInfo(CommonConstants.KEY_AUTO_TRAIN, String.class))
                            && "true".equals(model.getExtInfo(CommonConstants.KEY_AUTO_DELIVERY, String.class)))
                    .count();

            // 统计人工交付数量
            long manualDeliveryCount = materialModelVOS.stream()
                    .filter(model -> {
                        String autoDelivery = model.getExtInfo(CommonConstants.KEY_AUTO_DELIVERY, String.class);
                        Date deliveryTime = model.getExtInfo(CommonConstants.KEY_DELIVERY_TIME, Date.class);
                        return StringUtils.isEmpty(autoDelivery)
                                && deliveryTime != null;
                    })
                    .count();

            // 统计二次抠图（上传图片、系统级抠图）的服装数量
            long retryMattingCount = materialModelVOS.stream()
                    .filter(model -> {
                        String isManualReplacement = model.getExtInfo(CommonConstants.IS_MANUAL_REPLACEMENT,
                                String.class);
                        String isLoraSystemReload = model.getExtInfo(CommonConstants.IS_LORA_SYSTEM_RELOAD,
                                String.class);

                        // 尝试将字符串转换为数字并判断是否大于0
                        try {
                            // 任一字段大于0则满足条件
                            if (isManualReplacement != null && Integer.parseInt(isManualReplacement) > 0) {
                                return true;
                            }
                            if (isLoraSystemReload != null && Integer.parseInt(isLoraSystemReload) > 0) {
                                return true;
                            }
                        } catch (NumberFormatException e) {
                            // 忽略转换异常，视为不满足条件
                        }
                        return false;
                    })
                    .count();

            // 设置统计结果
            statsClothesInfoDO.setAutoTrainCount((int) autoTrainCount);
            statsClothesInfoDO.setManualDeliveryCount((int) manualDeliveryCount);
            statsClothesInfoDO.setAutoTrainAndDeliveryCount((int) (bothAutoCount));
            statsClothesInfoDO.setRetryMattingCount((int) retryMattingCount);
        } catch (Exception e) {
            log.error("【{}】fillTrainAndDeliveryCount执行异常: startDate={}, endDate={}", getStatsType().getCode(),
                    startDate, endDate, e);
        }
    }

    /**
     * 填充修改 prompt 的数量
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     */
    private void fillModifyPromptCount(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        try {
            // 封装查询条件
            PromptModificationLogQuery promptModificationLogQuery = new PromptModificationLogQuery();
            promptModificationLogQuery.setStartDate(startDate);
            promptModificationLogQuery.setEndDate(endDate);

            // 查询当前修改 prompt 记录
            Long count = promptModificationLogService.queryPromptModificationLogCount(promptModificationLogQuery);

            // 设置数量
            statsClothesInfoDO.setUpdatePromptCount(count.intValue());
        } catch (Exception e) {
            log.error("【{}】fillModifyPromptCount执行异常: startDate={}, endDate={}",
                    getStatsType().getCode(), startDate, endDate, e);
        }
    }

    /**
     * 填充 copy 的服装数量
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     */
    private void fillCopyClothesCount(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO) {
        try {
            // 封装查询条件
            MaterialModelQuery materialModelQuery = new MaterialModelQuery();
            materialModelQuery.setNameLike(CommonConstants.KEY_COPY_SUFFIX);
            materialModelQuery.setStartDate(startDate);
            materialModelQuery.setEndDate(endDate);

            // 查询当前 copy 的服装
            List<MaterialModelVO> materialModelVOS = materialModelService.queryMaterialModelList(materialModelQuery);

            // 提取服装名称并进行去重（比如：白色球衣_copy ，提取出名称为 白色球衣_）
            List<String> baseNames = materialModelVOS.stream()
                    .map(model -> {
                        String name = model.getName();
                        int copyIndex = name.indexOf(CommonConstants.KEY_COPY_SUFFIX);
                        return copyIndex > 0 ? name.substring(0, copyIndex + 1) : name;
                    })
                    .distinct()
                    .collect(Collectors.toList());

            // 如果没有提取到基础名称，则直接返回
            if (baseNames.isEmpty()) {
                statsClothesInfoDO.setCopyCount(0);
                return;
            }

            // 直接在数据库中进行匹配查询，提高效率
            int totalCopyCount = materialModelDAO.countCopyClothesWithPrefix(
                    baseNames, startDate, endDate);

            // 设置统计结果
            statsClothesInfoDO.setCopyCount(totalCopyCount);
        } catch (Exception e) {
            log.error("【{}】fillCopyClothesCount执行异常: startDate={}, endDate={}", getStatsType().getCode(), startDate,
                    endDate, e);
        }
    }

    /**
     * 执行周期性业务统计的通用方法
     *
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @param statsClothesInfoDO 统计数据对象
     * @param dataFetcher        数据获取函数，根据周期类型不同而不同
     * @return 变更条数
     */
    private int executePeriodBusinessStats(String startDate, String endDate, StatsClothesInfoDO statsClothesInfoDO,
            BiFunction<String, String, List<StatsClothesInfoDO>> dataFetcher) {
        // 根据日期获取对应周期的数据
        List<StatsClothesInfoDO> statsClothesInfoDOList = dataFetcher.apply(startDate, endDate);

        if (CollectionUtils.isNotEmpty(statsClothesInfoDOList)) {
            // 重新计算
            reCalculationStatsClothesInfo(statsClothesInfoDOList, statsClothesInfoDO);

            // 保存统计结果
            return statsClothesInfoService.batchInsertOrUpdate(Collections.singletonList(statsClothesInfoDO));
        }
        return 0;
    }

    /**
     * 重新计算统计数据
     * 
     * @param statsClothesInfoDOList 统计数据列表
     * @return 统计数据对象
     */
    private void reCalculationStatsClothesInfo(List<StatsClothesInfoDO> statsClothesInfoDOList,
            StatsClothesInfoDO statsClothesInfoDO) {
        statsClothesInfoDO.setVipClothesCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getVipClothesCount).sum());
        statsClothesInfoDO.setAutoTrainCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getAutoTrainCount).sum());
        statsClothesInfoDO.setManualDeliveryCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getManualDeliveryCount).sum());
        statsClothesInfoDO.setAutoTrainAndDeliveryCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getAutoTrainAndDeliveryCount).sum());
        statsClothesInfoDO.setRetryMattingCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getRetryMattingCount).sum());
        statsClothesInfoDO.setUpdatePromptCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getUpdatePromptCount).sum());
        statsClothesInfoDO.setCopyCount(
                statsClothesInfoDOList.stream().mapToInt(StatsClothesInfoDO::getCopyCount).sum());
    }

}

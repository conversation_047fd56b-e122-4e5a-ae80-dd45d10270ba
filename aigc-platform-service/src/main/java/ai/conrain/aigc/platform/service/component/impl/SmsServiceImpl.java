/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.AliyunSmsService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.SmsService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 短信服务实现
 *
 * <AUTHOR>
 * @version : SmsServiceImpl.java, v 0.1 2023/9/7 00:21 renxiao.wu Exp $
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    /** tair服务 */
    @Autowired
    private TairService tairService;

    /** 阿里云短信服务 */
    @Autowired
    private AliyunSmsService aliyunSmsService;

    /** 系统配置服务 */
    @Autowired
    private SystemConfigService systemConfigService;

    @Value("${aliyun.sms.mock}")
    private String mockSms;

    @Override
    public boolean sendCaptcha(String mobile) {
        //1.生成随机数作为短信验证码
        String random = RandomStringUtils.randomNumeric(4);
        //2.放到缓存中
        tairService.setString(getCaptchaKey(mobile), random, CommonConstants.CAPTCHA_EXPIRE_TIME);

        JSONObject json = new JSONObject();
        json.put("code", random);
        String param = json.toJSONString();

        //3.1.获取短信模板
        String templateId = systemConfigService.queryValueByKey(SystemConstants.CAPTCHA_SMS_TEMPLATE);

        //3.2.发送短信
        return sendSms(mobile, templateId, param);
    }

    @Override
    public boolean verifyCaptcha(String mobile, String captcha) {
        String key = getCaptchaKey(mobile);
        String cacheCaptcha = tairService.getString(key);

        if (!isMockSms() && !StringUtils.equals(captcha, cacheCaptcha)) {
            log.warn("短信验证失败，验证码不一致，mobile={}，captcha={}", mobile, captcha);
            return false;
        }

        //成功才清缓存
        tairService.clear(key);

        if (log.isInfoEnabled()) {
            log.info("短信验证成功，mobile={}，并完成缓存清理", SecurityUtils.maskPhoneNumber(mobile));
        }
        return true;
    }

    /**
     * 发送短信
     *
     * @param mobile     手机号
     * @param templateId 模板id
     * @param param      参数
     * @return true成功
     */
    private boolean sendSms(String mobile, String templateId, String param) {
        if (isMockSms()) {
            if (log.isInfoEnabled()) {
                log.info("mock sms send");
            }
            return true;
        }
        return aliyunSmsService.sendSms(mobile, templateId, param);
    }

    private boolean isMockSms() {
        return StringUtils.equals(mockSms, "true");
    }

    /**
     * 获取验证码的tair key
     *
     * @param mobile 手机号
     * @return tair key
     */
    private static String getCaptchaKey(String mobile) {
        return CommonConstants.CAPTCHA_KEY_PREFIX + mobile;
    }
}

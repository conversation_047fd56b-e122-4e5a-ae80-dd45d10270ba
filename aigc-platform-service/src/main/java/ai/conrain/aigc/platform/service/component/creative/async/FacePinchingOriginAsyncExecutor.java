/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.FacePinchingCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE_PATH;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * 模特捏脸原始图片异步处理器
 *
 * <AUTHOR>
 * @version : FacePinchingOriginAsyncExecutor.java, v 0.1 2025/4/8 23:21 renxiao.wu Exp $
 */
@Slf4j
@Service
public class FacePinchingOriginAsyncExecutor extends AbstractBatchToAsyncExecutor<FacePinchingCreativeRequest, String> {
    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private OssHelper ossHelper;

    @Override
    protected Class<String> getModelClass() {
        return String.class;
    }

    @Override
    protected Object buildOriginValue(FacePinchingCreativeRequest request) {
        return request.getOriginImages();
    }

    @Override
    protected String buildTransValue(CreativeTaskVO task, String origin) {
        List<String> list = JSONArray.parseArray(origin).toJavaList(String.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        String path = "face_pinching/" + sdf.format(new Date()) + task.getUserId() + "/" + task.getBatchId()
                      + "/origin";
        String serverUrl = task.getExtInfo(KEY_SERVER_URL, String.class);

        boolean success = ossHelper.downloadAndUploadToPath(list, path, task.getUserId(), serverUrl, true);
        if (!success) {
            throw new BizException(ResultCode.SYS_ERROR);
        }

        return inputPath + path;
    }

    @Override
    public String getOriginKey() {
        return KEY_ORIGIN_IMAGE;
    }

    @Override
    public String getTransKey() {
        return KEY_ORIGIN_IMAGE_PATH;
    }

    @Override
    protected CreativeTypeEnum aboutToType() {
        return CreativeTypeEnum.FACE_PINCHING;
    }

}

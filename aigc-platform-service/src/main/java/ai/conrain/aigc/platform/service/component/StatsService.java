package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.util.strategy.StatsPeriodStrategy;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 统计服务接口 - 定义统计相关操作
 */
public interface StatsService {

    int generateStatsForPeriod(Date date, StatsPeriodStrategy strategy);

    int generateAllUserStatsForPeriod(Date date, StatsPeriodStrategy strategy);

    int generateStatsForPeriod(Date date, StatsPeriodEnum period);

    int generateAllUserStatsForPeriod(Date date, StatsPeriodEnum period);
}

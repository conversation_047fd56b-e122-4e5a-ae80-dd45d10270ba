package ai.conrain.aigc.platform.service.enums;

import lombok.Getter;

@Getter
public enum ClothShootGenreEnum {
    LOOKBOOK("Product_lookbook", "产品lookbook"),
    SOCIAL_NETWORK("Internet_style_photo", "网感图"),
    COMMERCIAL_PHOTO("LRSP2111_commercial", "商拍图"),
    MIRROR_SELFIE("Selfie", "对镜自拍"),
    FASHION_BLOCKBUSTER("Vogue_style_fashion_shoot","时尚大片"),
    SHOW("Runway_shot","走秀"),
    ;

    private final String code;
    private final String displayName;

    ClothShootGenreEnum(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public static ClothShootGenreEnum getByCode(String code) {
        for (ClothShootGenreEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}

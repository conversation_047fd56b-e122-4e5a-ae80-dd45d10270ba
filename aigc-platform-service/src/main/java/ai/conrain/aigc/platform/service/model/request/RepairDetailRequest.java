package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.model.biz.MaskMergeModel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class RepairDetailRequest implements MaskMergeCreativeRequest{
    private static final long serialVersionUID = -4440659260651418033L;

    @NotBlank
    private String image;

    /** 服装图片 */
    @NotBlank
    private String clothImage;

    /** 出图数量 */
    @NotNull
    private Integer imageNum;

    @Valid
    @NotEmpty
    private List<MaskMergeModel> maskMergeModels;

    /** 图片来源：上传upload/历史纪录history */
    @NotBlank
    private String imageSource;
}

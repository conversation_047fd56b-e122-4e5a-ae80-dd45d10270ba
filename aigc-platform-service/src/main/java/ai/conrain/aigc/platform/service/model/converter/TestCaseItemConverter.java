package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.TestCaseItemDO;
import ai.conrain.aigc.platform.dal.example.TestCaseItemExample;
import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.abtest.BaseCaseItemDetail;
import ai.conrain.aigc.platform.service.model.biz.abtest.PromptCreativeItemDetail;
import ai.conrain.aigc.platform.service.model.query.TestCaseItemQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseItemVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * TestCaseItemConverter
 *
 * @version TestCaseItemService.java v 0.1 2025-08-12 07:18:08
 */
public class TestCaseItemConverter {

    /**
     * DO -> VO
     */
    public static TestCaseItemVO do2VO(TestCaseItemDO from) {
        TestCaseItemVO to = new TestCaseItemVO();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setName(from.getName());
        to.setType(TestCaseTypeEnum.getByCode(from.getType()));
        to.setRunTimes(from.getRunTimes());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        to.setDetail(convertDetail(to.getType(), to.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestCaseItemDO vo2DO(TestCaseItemVO from) {
        TestCaseItemDO to = new TestCaseItemDO();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setName(from.getName());
        to.setType(from.getType() == null ? null : from.getType().getCode());
        to.setRunTimes(from.getRunTimes());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toJSONString() : null);

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestCaseItemExample query2Example(TestCaseItemQuery from) {
        TestCaseItemExample to = new TestCaseItemExample();
        TestCaseItemExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getCaseId())) {
            c.andCaseIdEqualTo(from.getCaseId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getRunTimes())) {
            c.andRunTimesEqualTo(from.getRunTimes());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (TestCaseItemExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestCaseItemVO> doList2VOList(List<TestCaseItemDO> list) {
        return CommonUtil.listConverter(list, TestCaseItemConverter::do2VO);
    }

    public static List<TestCaseItemDO> voList2DOList(List<TestCaseItemVO> list) {
        return CommonUtil.listConverter(list, TestCaseItemConverter::vo2DO);
    }

    public static <T extends BaseCaseItemDetail> T convertDetail(TestCaseTypeEnum type, List<String> props) {
        switch (type) {
            case PROMPT_CREATE_IMAGE:
                //noinspection unchecked
                return (T)convertPromptCreative(props);
            default:
                return null;
        }
    }

    public static <T extends BaseCaseItemDetail> T convertDetail(TestCaseTypeEnum type, JSONObject extInfo) {
        switch (type) {
            case PROMPT_CREATE_IMAGE:
                //noinspection unchecked
                return (T)JSONObject.toJavaObject(extInfo, PromptCreativeItemDetail.class);
            default:
                return null;
        }
    }

    public static PromptCreativeItemDetail convertPromptCreative(List<String> props) {
        PromptCreativeItemDetail detail = new PromptCreativeItemDetail();
        detail.setDataId(Integer.parseInt(props.get(0)));
        detail.setImagePath(props.get(1));
        detail.setPrompt(props.get(2));
        detail.setWidth(Integer.parseInt(props.get(3)));
        detail.setHeight(Integer.parseInt(props.get(4)));
        if (props.size() > 5) {
            detail.setImageUrl(props.get(5));
        }
        return detail;
    }
}
package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionUserDO;
import ai.conrain.aigc.platform.service.model.query.CaptionUserQuery;
import ai.conrain.aigc.platform.dal.example.CaptionUserExample;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * CaptionUserConverter
 *
 * @version CaptionUserService.java v 0.1 2025-08-12 12:55:30
 */
public class CaptionUserConverter {

    /**
     * DO -> VO
     */
    public static CaptionUserVO do2VO(CaptionUserDO from) {
        CaptionUserVO to = new CaptionUserVO();
        to.setId(from.getId());
        to.setUsername(from.getUsername());
        to.setPasswordHash(from.getPasswordHash());
        to.setNickname(from.getNickname());
        to.setRole(from.getRole());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CaptionUserDO vo2DO(CaptionUserVO from) {
        CaptionUserDO to = new CaptionUserDO();
        to.setId(from.getId());
        to.setUsername(from.getUsername());
        to.setPasswordHash(from.getPasswordHash());
        to.setNickname(from.getNickname());
        to.setRole(from.getRole());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CaptionUserExample query2Example(CaptionUserQuery from) {
        CaptionUserExample to = new CaptionUserExample();
        CaptionUserExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUsername())) {
            c.andUsernameEqualTo(from.getUsername());
        }
        if (!ObjectUtils.isEmpty(from.getUsernameLike())) {
            c.andUsernameLike("%" + from.getUsernameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getPasswordHash())) {
            c.andPasswordHashEqualTo(from.getPasswordHash());
        }
        if (!ObjectUtils.isEmpty(from.getNickname())) {
            c.andNicknameEqualTo(from.getNickname());
        }
        if (!ObjectUtils.isEmpty(from.getNicknameLike())) {
            c.andNicknameLike("%" + from.getNicknameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getRole())) {
            List<String> roles = Arrays.stream(from.getRole().split(",")).toList();
            c.andRoleIn(roles);
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<CaptionUserVO> doList2VOList(List<CaptionUserDO> list) {
        return CommonUtil.listConverter(list, CaptionUserConverter::do2VO);
    }
}
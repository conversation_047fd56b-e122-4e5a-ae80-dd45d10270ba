package ai.conrain.aigc.platform.service.model.common;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class PageInfo<T> implements Serializable {

    private static final long serialVersionUID = 6382311867635133447L;

    //返回的数据列表
    private List<T> list;

    /** 扩展信息 */
    private Map<String, Object> extInfo;

    //是否有下一页
    private boolean hasNextPage;

    //返回数据的条数
    private int size;

    //总条数
    private long totalCount;

    public void putExtInfo(String key, Object value) {
        if (extInfo == null) {
            extInfo = new java.util.HashMap<>();
        }
        extInfo.put(key, value);
    }
}
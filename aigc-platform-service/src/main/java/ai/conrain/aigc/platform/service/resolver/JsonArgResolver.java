/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 * @version : JsonArgResolver.java, v 0.1 2023/9/7 21:30 renxiao.wu Exp $
 */
@Slf4j
public class JsonArgResolver implements HandlerMethodArgumentResolver {
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(JsonArg.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        OperationContext context = OperationContextHolder.getContext();

        //Payload的作用是为了防止第二次调用request.getReader().lines()时取不到数据
        //1.初始化请求payload
        if (null == context.getPostRequestBody()) {
            HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
            if (null == request) {
                log.error("解析json单个字符串，获取到的request为空");
                return null;
            }
            String body = request.getReader().lines().collect(Collectors.joining());

            if (log.isDebugEnabled()) {
                log.debug("解析json单个字符串，获取json={}", body);
            }
            context.setPostRequestBody(JSONObject.parseObject(body));
        }

        //2.获取字段名称
        String name = parameter.getParameter().getName();
        if (log.isDebugEnabled()) {
            log.debug("解析json单个字符串，获取name={}", name);
        }

        //3.获取值
        return context.getParamsFromPostRequestBodyByKey(name, parameter.getParameterType());
    }
}

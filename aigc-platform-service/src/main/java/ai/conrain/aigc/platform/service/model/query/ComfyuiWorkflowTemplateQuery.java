package ai.conrain.aigc.platform.service.model.query;


import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * ComfyuiWorkflowTemplateQuery
 *
 * @version ComfyuiWorkflowTemplateService.java v 0.1 2025-06-11 03:56:03
 */
@Data
public class ComfyuiWorkflowTemplateQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Integer id;

    /** 模板key */
    private String templateKey;

    /** 模板描述 */
    private String templateDesc;

    /** 模板版本，如20250610.1 */
    private String version;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;

    /** 创建人id */
    private Integer createBy;

    /** 修改人id */
    private Integer modifyBy;

    /** 模板数据 */
    private String templateData;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 是否仅显示测试版本 */
    private boolean onlyShowTestVersion;

    /** 是否需要删除的 */
    private boolean needDeleted;

}

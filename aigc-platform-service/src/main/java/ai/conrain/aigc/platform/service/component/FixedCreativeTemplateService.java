package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.FixedCreativeTemplateDO;
import ai.conrain.aigc.platform.service.model.query.FixedCreativeTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.FixedCreativeTemplateVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
public interface FixedCreativeTemplateService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	FixedCreativeTemplateVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param fixedCreativeTemplate 对象参数
	 * @return 返回结果
	 */
	FixedCreativeTemplateVO insert(FixedCreativeTemplateVO fixedCreativeTemplate);

	/**
	 * 修改对象
	 * @param fixedCreativeTemplate 对象参数
	 */
	void updateByIdSelective(FixedCreativeTemplateVO fixedCreativeTemplate);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<FixedCreativeTemplateVO> queryFixedCreativeTemplateList(FixedCreativeTemplateQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryFixedCreativeTemplateCount(FixedCreativeTemplateQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<FixedCreativeTemplateVO> queryFixedCreativeTemplateByPage(FixedCreativeTemplateQuery query);
}
package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.PromptModificationLogDO;
import ai.conrain.aigc.platform.service.model.query.PromptModificationLogQuery;
import ai.conrain.aigc.platform.dal.example.PromptModificationLogExample;
import ai.conrain.aigc.platform.service.model.vo.PromptModificationLogVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSONObject;

import static com.alibaba.fastjson.JSON.parseObject;

/**
 * PromptModificationLogConverter
 *
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
public class PromptModificationLogConverter {

    /**
     * DO -> VO
     */
    public static PromptModificationLogVO do2VO(PromptModificationLogDO from) {
        PromptModificationLogVO to = new PromptModificationLogVO();
        to.setId(from.getId());
        to.setModuleCode(from.getModuleCode());
        to.setModuleType(from.getModuleType());
        to.setParentElementId(from.getParentElementId());
        to.setOperatorId(from.getOperatorId());
        to.setOperatorName(from.getOperatorName());
        to.setOperationTime(from.getOperationTime());

        if (from.getOldFieldValue() != null) {
            JSONObject oldFieldValue = parseObject(from.getOldFieldValue());
            to.setOldFieldValue(oldFieldValue);
        }
        if (from.getNewFieldValue() != null) {
            JSONObject newFieldValue = parseObject(from.getNewFieldValue());
            to.setNewFieldValue(newFieldValue);
        }
        if (from.getFixedAttrs() != null) {
            JSONObject fixedAttrs = parseObject(from.getFixedAttrs());
            to.setFixedAttrs(fixedAttrs);
        }
        if (from.getExtInfo() != null) {
            JSONObject extInfo = parseObject(from.getExtInfo());
            to.setExtInfo(extInfo);
        }
        to.setRemark(from.getRemark());

        return to;
    }

    /**
     * VO -> DO
     */
    public static PromptModificationLogDO vo2DO(PromptModificationLogVO from) {
        PromptModificationLogDO to = new PromptModificationLogDO();
        to.setId(from.getId());
        to.setModuleCode(from.getModuleCode());
        to.setModuleType(from.getModuleType());
        to.setParentElementId(from.getParentElementId());
        to.setOperatorId(from.getOperatorId());
        to.setOperatorName(from.getOperatorName());
        to.setOperationTime(from.getOperationTime());
        if (from.getOldFieldValue() != null) {
            to.setOldFieldValue(from.getOldFieldValue().toJSONString());
        }
        if (from.getNewFieldValue() != null) {
            to.setNewFieldValue(from.getNewFieldValue().toJSONString());
        }
        if (from.getFixedAttrs() != null) {
            to.setFixedAttrs(from.getFixedAttrs().toJSONString());
        }
        if (from.getExtInfo() != null) {
            to.setExtInfo(from.getExtInfo().toJSONString());
        }
        to.setRemark(from.getRemark());

        return to;
    }

    /**
     * DO -> Query
     */
    public static PromptModificationLogQuery do2Query(PromptModificationLogDO from) {
        PromptModificationLogQuery to = new PromptModificationLogQuery();
        to.setId(from.getId());
        to.setModuleCode(from.getModuleCode());
        to.setModuleType(from.getModuleType());
        to.setParentElementId(from.getParentElementId());
        to.setOperatorId(from.getOperatorId());
        to.setOperatorName(from.getOperatorName());
        to.setOperationTime(from.getOperationTime());
        to.setOldFieldValue(from.getOldFieldValue());
        to.setNewFieldValue(from.getNewFieldValue());
        to.setFixedAttrs(from.getFixedAttrs());
        to.setExtInfo(from.getExtInfo());
        to.setRemark(from.getRemark());

        return to;
    }

    /**
     * Query -> DO
     */
    public static PromptModificationLogDO query2DO(PromptModificationLogQuery from) {
        PromptModificationLogDO to = new PromptModificationLogDO();
        to.setId(from.getId());
        to.setModuleCode(from.getModuleCode());
        to.setModuleType(from.getModuleType());
        to.setParentElementId(from.getParentElementId());
        to.setOperatorId(from.getOperatorId());
        to.setOperatorName(from.getOperatorName());
        to.setOperationTime(from.getOperationTime());
        to.setOldFieldValue(from.getOldFieldValue());
        to.setNewFieldValue(from.getNewFieldValue());
        to.setFixedAttrs(from.getFixedAttrs());
        to.setExtInfo(from.getExtInfo());
        to.setRemark(from.getRemark());

        return to;
    }

    /**
     * Query -> Example
     */
    public static PromptModificationLogExample query2Example(PromptModificationLogQuery from) {
        PromptModificationLogExample to = new PromptModificationLogExample();
        PromptModificationLogExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getModuleCode())) {
            c.andModuleCodeEqualTo(from.getModuleCode());
        }
        if (!ObjectUtils.isEmpty(from.getModuleType())) {
            c.andModuleTypeEqualTo(from.getModuleType());
        }
        if (!ObjectUtils.isEmpty(from.getParentElementId())) {
            c.andParentElementIdEqualTo(from.getParentElementId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorName())) {
            c.andOperatorNameEqualTo(from.getOperatorName());
        }
        if (!ObjectUtils.isEmpty(from.getOperationTime())) {
            c.andOperationTimeEqualTo(from.getOperationTime());
        }

        if (!ObjectUtils.isEmpty(from.getStartDate()) && !ObjectUtils.isEmpty(from.getEndDate())) {
            c.andOperationTimeBetween(from.getStartDate(), from.getEndDate());
        }

        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<PromptModificationLogVO> doList2VOList(List<PromptModificationLogDO> list) {
        return CommonUtil.listConverter(list, PromptModificationLogConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.PromptModificationLogDO;
import ai.conrain.aigc.platform.service.model.query.PromptModificationLogQuery;
import ai.conrain.aigc.platform.service.model.vo.PromptModificationLogVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 修改记录 Service定义
 *
 * <AUTHOR>
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
public interface PromptModificationLogService {
	
	/**
	 * 查询修改记录对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	PromptModificationLogVO selectById(Integer id);

	/**
	 * 删除修改记录对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加修改记录对象
	 * @param promptModificationLog 对象参数
	 * @return 返回结果
	 */
	PromptModificationLogVO insert(PromptModificationLogVO promptModificationLog);

	/**
	 * 修改修改记录对象
	 * @param promptModificationLog 对象参数
	 */
	void updateByIdSelective(PromptModificationLogVO promptModificationLog);

	/**
	 * 带条件批量查询修改记录列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<PromptModificationLogVO> queryPromptModificationLogList(PromptModificationLogQuery query);

	/**
	 * 带条件查询修改记录数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryPromptModificationLogCount(PromptModificationLogQuery query);

	/**
	 * 带条件分页查询修改记录
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<PromptModificationLogVO> queryPromptModificationLogByPage(PromptModificationLogQuery query);
}
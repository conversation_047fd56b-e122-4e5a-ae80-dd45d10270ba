package ai.conrain.aigc.platform.service.job;

import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.example.MaterialModelExample;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.WorkScheduleService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.MainTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.converter.MaterialModelConverter;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.NumberCounter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分配审核任务
 */
@Slf4j
@Component
public class CreateReviewTaskJob extends JavaProcessor {

    @Autowired
    private MaterialModelDAO materialModelDAO;

    @Autowired
    private WorkScheduleService workScheduleService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
        Params params = Optional.ofNullable(context.getInstanceParameters())
            .filter(StringUtils::isNotBlank)
            .map(s -> JSONObject.parseObject(s, Params.class))
            .orElseGet(() -> Optional.ofNullable(context.getJobParameters())
                .filter(StringUtils::isNotBlank)
                .map(s -> JSONObject.parseObject(s, Params.class))
                .orElse(new Params()));
        if (params.getIntervalDays() > 0) {
            // 默认取近1天的素材，避免第二天审核员不在排班中
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime someDaysAgo = now.minusDays(params.getIntervalDays());
            Date dateSomeDaysAgo = Date.from(someDaysAgo.atZone(ZoneId.systemDefault()).toInstant());
            params.setStartTime(dateSomeDaysAgo);
        }
        distributeReviewer(params);
        return new ProcessResult(true);
    }

    /**
     * 分配审核人，策略-均匀分配
     */
    private void distributeReviewer(Params params) {
        WorkScheduleQuery workScheduleQuery = new WorkScheduleQuery();
        workScheduleQuery.setStartTime(DateUtils.getFirstOfDay());
        workScheduleQuery.setEndTime(DateUtils.getLastOfDay());
        List<Integer> reviewerIds = CollectionUtils.isNotEmpty(params.getReviewerIds()) ?
            params.getReviewerIds() : workScheduleService.queryWorkScheduleList(workScheduleQuery)
            .stream()
            .map(WorkScheduleVO::getUserId)
            .distinct()
            .collect(Collectors.toList());
        if (reviewerIds.isEmpty()) {
            log.warn("审核人员未排班");
            return;
        }
        log.info("审核人员列表: {}", reviewerIds);

        // 自动训练和自动交付客户，无需分配审核
        JSONArray autoDeliveryMerchant = systemConfigService.queryJsonArrValue(SystemConstants.AUTO_DELIVERY_MERCHANT);
        JSONArray autoTrainMerchant = systemConfigService.queryJsonArrValue(SystemConstants.AUTO_TRAIN_MERCHANT);
        Set<Integer> autoDeliveryMerchantIds = toIntegerSet(autoDeliveryMerchant);
        Set<Integer> autoTrainMerchantIds = toIntegerSet(autoTrainMerchant);
        Set<Integer> autoDeliveryAndTrainMerchantIds = new HashSet<>(autoDeliveryMerchantIds);
        autoDeliveryAndTrainMerchantIds.retainAll(autoTrainMerchantIds);

        // 获取测试中的素材
        MaterialModelQuery materialModelQuery = getMaterialModelQuery(params);
        MaterialModelExample materialModelExample = MaterialModelConverter.query2Example(materialModelQuery);
        List<MaterialModelDO> materialModelDOS = materialModelDAO.selectByExampleWithBLOBs(materialModelExample);
        List<Integer> allUserIds = materialModelDOS.stream()
            .map(MaterialModelDO::getUserId)
            .distinct()
            .collect(Collectors.toList());
        List<UserVO> userList = userService.batchQueryById(allUserIds);
        // 只有前台用户上传的素材需要审核
        List<MaterialModelVO> models = materialModelDOS
            .stream()
            .filter(item -> item.getUserId() == null || !autoDeliveryAndTrainMerchantIds.contains(item.getUserId()))
            .filter(item -> userList.stream()
                .filter(u -> u.getId().equals(item.getUserId()))
                .findFirst()
                .map(UserVO::getRoleType)
                .map(RoleTypeEnum::isFrontRoleOrOperator)
                .orElse(false))
            .map(m -> {
                MaterialModelVO model = MaterialModelConverter.do2VO(m);
                model.setExtInfo(
                    StringUtils.isBlank(m.getExtInfo()) ? new JSONObject() : JSONObject.parseObject(m.getExtInfo()));
                return model;
            })
            .collect(Collectors.toList());

        if (models.isEmpty()) {
            return;
        }

        // 筛选出未分配的素材
        // fix: 前一天未审核完的服装，第二天审核员不在排班表中，需要重新分配
        // fix: 只有审核中和训练中才需要分配审核人
        List<MaterialModelVO> unreviewedModels = models.stream()
            .filter(item -> Arrays.asList(MaterialModelStatusEnum.IN_TRAINING.getCode(), MaterialModelStatusEnum.TESTING.getCode()).contains(item.getStatus()))
            .filter(model -> !reviewerIds.contains(model.getExtInfo(CommonConstants.KEY_REVIEWER_ID, Integer.class)))
            .collect(Collectors.toList());

        if (unreviewedModels.isEmpty()) {
            return;
        }

        NumberCounter numberCounter = new NumberCounter(reviewerIds);
        // 分组筛选出任务量最小的
        // 筛选出已经分配的素材，用于均匀分配策略
        // fix: 去除掉不在人员排班中的人，因为有可能是克隆产生的审核员
        // 优化：只取八点以后的来初始化每个人的任务量
        models.stream()
            .map(model -> model.getExtInfo(CommonConstants.KEY_REVIEWER_ID, Integer.class))
            .filter(reviewerIds::contains)
            .filter()
            .forEach(numberCounter::add);

        // 分配审核人
        // 多色服装分给同一个人
        Map<String, Integer> reviewerIdByBatchId = new HashMap<>(32);
        for (MaterialModelVO model : unreviewedModels) {
            Integer targetReviewerId = chooseReviewer(numberCounter, model, reviewerIdByBatchId);
            numberCounter.add(targetReviewerId);
            model.addExtInfo(CommonConstants.KEY_REVIEWER_ID, targetReviewerId);
            model.addExtInfo(CommonConstants.KEY_WORK_SCHEDULED_TIME, new Date());
            MaterialModelDO materialModelDO = new MaterialModelDO();
            materialModelDO.setId(model.getId());
            materialModelDO.setExtInfo(JSONObject.toJSONString(model.getExtInfo()));
            materialModelDAO.updateByPrimaryKeySelective(materialModelDO);
        }
    }

    private MaterialModelQuery getMaterialModelQuery(Params params) {
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setOnlyExperimental(false);
        materialModelQuery.setMaterialType(MaterialType.cloth.name());
        materialModelQuery.setType(ModelTypeEnum.CUSTOM.getCode());
        materialModelQuery.setMainTypes(MainTypeEnum.USER_VIEW_MAIN_TYPES);
        // 互斥查询，指定素材或指定时间范围
        if (CollectionUtils.isNotEmpty(params.getModelIds())) {
            materialModelQuery.setIds(params.getModelIds());
        } else {
            materialModelQuery.setStartCreateTime(params.getStartTime());
        }
        return materialModelQuery;
    }

    /** 选择审核人 */
    private Integer chooseReviewer(NumberCounter numberCounter,
                                   MaterialModelVO model,
                                   Map<String, Integer> reviewerIdByBatchId) {
        String bizTag = model.getExtInfo(BizConstants.BIZ_TAG, String.class);
        String batchId = model.getExtInfo(CommonConstants.KEY_BATCH_ID, String.class);

        // 若不是多色服装，直接进行均匀分配
        if (!BizConstants.MULTI_COLOR_SPLIT.equals(bizTag) || StringUtils.isBlank(batchId)) {
            return numberCounter.getLeast();
        }

        // 检查批次 ID 是否已有对应的审核人
        Integer reviewerId = reviewerIdByBatchId.get(batchId);
        if (reviewerId != null) {
            return reviewerId;
        }

        // 若没有，选择任务量最小的审核人并记录
        reviewerId = numberCounter.getLeast();
        reviewerIdByBatchId.put(batchId, reviewerId);
        return reviewerId;
    }

    private Set<Integer> toIntegerSet(JSONArray array) {
        if (CollectionUtils.isEmpty(array)) {
            return Collections.emptySet();
        }

        return array.stream()
            .filter(Objects::nonNull)
            .map(Object::toString)
            .filter(StringUtils::isNotBlank)
            .map(Integer::valueOf)
            .collect(Collectors.toSet());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class Params {
        private int intervalDays = 1;
        private Date startTime;
        private List<Integer> reviewerIds;
        private List<Integer> modelIds;
    }
}

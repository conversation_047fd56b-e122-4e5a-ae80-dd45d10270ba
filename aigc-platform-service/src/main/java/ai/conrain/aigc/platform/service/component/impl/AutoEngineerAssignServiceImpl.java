package ai.conrain.aigc.platform.service.component.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserProfileService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 自动工程师分配服务
 */
@Slf4j
@Service
public class AutoEngineerAssignServiceImpl {

    @Autowired
    private UserPointService userPointService;
    
    @Autowired
    private UserProfileService userProfileService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private DistributorCustomerService distributorCustomerService;

    /**
     * 异步尝试自动为客户指派工程师
     * 逻辑：充值成功后，如果用户缪斯点达到阈值且未指派工程师，则根据其销售/渠道商的工程师进行自动指派
     * 
     * @param customerUserId 客户用户ID
     */
    @Async
    public void tryAutoAssignEngineerToCustomerAsync(Integer customerUserId) {
        try {
            log.info("开始异步自动指派工程师，客户ID: {}", customerUserId);
            
            // 1. 配置缪斯点阈值 (后续可配置化)
            BigDecimal threshold = new BigDecimal("3999.00");
            
            // 2. 检查客户当前缪斯点余额
            UserPointVO userPoint = userPointService.queryImagePoint(customerUserId);
            if (userPoint == null || userPoint.getImagePoint() == null) {
                log.info("客户{}缪斯点查询失败或为空，跳过自动指派工程师", customerUserId);
                return;
            }
            
            BigDecimal currentMusePoints = userPoint.getImagePoint();
            log.info("客户{}当前缪斯点: {}, 阈值: {}", customerUserId, currentMusePoints, threshold);
            
            // 3. 检查是否达到阈值
            if (currentMusePoints.compareTo(threshold) < 0) {
                log.info("客户{}缪斯点{}未达到阈值{}，跳过自动指派工程师", 
                    customerUserId, currentMusePoints, threshold);
                return;
            }

            // 4. 检查客户是否已经指派了工程师
            UserProfileVO existingEngineer = userProfileService.selectByUidAndProfileKey(
                customerUserId, CommonConstants.KEY_PROMPT_USER_ID);
            //已经指派交付工程师了就跳过
            if (existingEngineer != null && StringUtils.isNotBlank(existingEngineer.getProfileVal())) {
                log.info("客户{}已指派工程师ID: {}，跳过自动指派",
                    customerUserId, existingEngineer.getProfileVal());
                return;
            }
            // 没有指派交付工程师继续
            // 5. 查询客户关联的销售/渠道商
            UserVO customer = userService.selectById(customerUserId);
            if (customer == null) {
                log.warn("客户{}信息查询失败，跳过自动指派工程师", customerUserId);
                return;
            }

            // 直接查询distributor_customer表获取销售ID
            DistributorCustomerQuery query = new DistributorCustomerQuery();
            query.setCustomerMasterUserId(customerUserId);
            List<DistributorCustomerVO> relations = distributorCustomerService.queryDistributorCustomerList(query);

            if (CollectionUtils.isEmpty(relations)) {
                log.info("客户{}未关联销售/渠道商，跳过自动指派工程师", customerUserId);
                return;
            }
            // 客户关联的销售id
            Integer distributorUserId = relations.get(0).getDistributorSalesUserId();
            if (distributorUserId == null) {
                log.info("客户{}关联的销售用户ID为空，跳过自动指派工程师", customerUserId);
                return;
            }

            log.info("客户{}关联的销售/渠道商ID: {}", customerUserId, distributorUserId);

            // 6、根据销售id，查是否设置了这个销售的地区 。
            // 销售指定的地区
            UserProfileVO distributorProfileVO = userProfileService.selectByUidAndProfileKey(distributorUserId,
                CommonConstants.KEY_DISTRIBUTOR_REGION_ASSIGNMENT);
            // 地区
            String profileVal = "";
            if (distributorProfileVO != null) {
                profileVal = distributorProfileVO.getProfileVal();
            }

            // 7、查询销售/渠道商指派的工程师
            // 销售的交付工程师ID
            UserProfileVO distributorEngineer = userProfileService.selectByUidAndProfileKey(
                distributorUserId, CommonConstants.KEY_PROMPT_USER_ID);
            String engineerId = "";
            if (distributorEngineer != null) {
                engineerId = distributorEngineer.getProfileVal();
            }

            // 如果客户的销售没指定地区，且销售没指派交付工程师，自动分配交付工程师就无法分配,直接返回
            if (StringUtils.isBlank(profileVal) && StringUtils.isBlank(
                engineerId)) {
                log.info("销售/渠道商{}未指定地区且未指派交付工程师，跳过为客户{}自动指派交付工程师",
                    distributorUserId, customerUserId);
                return;
            }

            // 如果销售没有指定地区，但是指派了交付工程师，那就把客户指派给这个交付工程师
            if (StringUtils.isBlank(profileVal) && !StringUtils.isBlank(engineerId)) {
                log.info("销售/渠道商{}的工程师ID: {}", distributorUserId, engineerId);
                // 自动为客户指派工程师
                // 设置user_profile
                autoEngineer(customerUserId,engineerId);
                // 检查交付工程师的客户数量，客户数量处于大于等于70
                sendEngineerClientAlert(Integer.valueOf(engineerId),0,customer);
                return;
            }

            // 获取到同地区的交付工程师列表
            UserProfileQuery userProfileQuery = new UserProfileQuery();
            userProfileQuery.setProfileKey(CommonConstants.KEY_ADMIN_REGION_ASSIGNMENT);
            userProfileQuery.setProfileVal(profileVal);
            List<UserProfileVO> userProfileVOList = userProfileService.queryUserProfileList(userProfileQuery);
            // 该地区没有可用的交付工程师，直接退出
            if (CollectionUtils.isEmpty(userProfileVOList)) {
                log.info("该地区{}没有可用的交付工程师", profileVal);
                return;
            }

            // 8、销售指定了地区，该地区有交付工程师，且这个地区中有这个交付工程师，直接指派
            if (!StringUtils.isBlank(profileVal) &&  !StringUtils.isBlank(engineerId)) {
                // 销售指定的交付工程师id
                Integer engineerIdInter = Integer.valueOf(engineerId);

                // 销售的交付工程师 是否在该地区
                boolean exists = userProfileVOList.stream().anyMatch(
                    // profile.getUid获取到的是交付工程师id，因为查询到本就是该地区的交付工程师列表
                    profile -> profile.getUid() != null && profile.getUid().equals(engineerIdInter));
                //如果在，给客户分配交付工程师，流程结束
                if (exists) {
                    autoEngineer(customerUserId, engineerId);
                    // 交付工程师客户数量预警
                    sendEngineerClientAlert(Integer.valueOf(engineerId),0,customer);
                    return;
                }
                // 如果没在，那就走下面的，找该地区客户最少的交付工程师

            }

            // 选择客户最少的交付工程师
            Integer minCountEngineerId = getEngineerCustomerCount(userProfileVOList,customer);
            if (minCountEngineerId != null) {
                // 给客户分配交付工程师
                autoEngineer(customerUserId, String.valueOf(minCountEngineerId));
            }else {
                log.warn("未找到合适的交付工程师进行分配，客户ID: {}",
                    customerUserId);
            }

        } catch (Exception e) {
            log.error("异步自动指派工程师处理异常，客户ID: " + customerUserId, e);
        }
    }

    /**
     * 根据客户id和工程师id 分配交付工程师
     * @param customerUserId  客户id
     * @param engineerId 交付工程师id
     */
    private void autoEngineer (Integer customerUserId,String engineerId) {
        boolean success = false;

        // 自动分配工程师,向user_profile中插入数据
        UserProfileVO userProfileVO = new UserProfileVO();
        userProfileVO.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
        userProfileVO.setUid(customerUserId);
        userProfileVO.setProfileVal(engineerId);
        UserProfileVO result = userProfileService.insertOrUpdate(userProfileVO);
        success = result != null;
        // 注意结果，更新，标记数量都要
        if (success) {
            log.info("异步自动指派工程师成功: 客户ID={}, 工程师ID={}", customerUserId, engineerId);
        } else {
            log.error("异步自动指派工程师失败: 客户ID={}, 工程师ID={}", customerUserId, engineerId);
        }

    }

    /**
     * 获取交付工程师的客户数量，返回客户最少的交付工程师id
     * @param  userProfileVOList 指定地区交付工程师列表
     * @return
     */
    public Integer getEngineerCustomerCount(List<UserProfileVO> userProfileVOList,UserVO userVO) {
        // 客户最小的工程师id
        Integer minCountEngineerId =null;
        // 初始值是最大值
        int currentMinCount = Integer.MAX_VALUE;
        // 遍历获取list中每个工程师的客户数量
        for (UserProfileVO userProfileVO : userProfileVOList) {
            // 获取到这个交付工程师的客户列表
            Integer uid = userProfileVO.getUid();
            UserProfileQuery userProfileQuery = new UserProfileQuery();
            userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
            userProfileQuery.setProfileVal(String.valueOf(uid));
            List<UserProfileVO> userProfileVOS = userProfileService.queryUserProfileList(userProfileQuery);
            // 对比客户数量
            int size = userProfileVOS.size();
            if (size < currentMinCount) {
                minCountEngineerId = uid;
                currentMinCount = size;
            }
        }
        // 如果当前指定的交付工程师客户数量 大于等于70，触发钉钉预警
        if (currentMinCount >= 70) {
            sendEngineerClientAlert(minCountEngineerId,currentMinCount,userVO);
        }



        log.info("选择客户数量最少的工程师: ID={}, 客户数量={}",
            minCountEngineerId, currentMinCount);
        return minCountEngineerId;
    }

    /**
     * 当前选择的交付工程师 客户数量处于 70～80之间触发预警消息
     * @param uid 交付工程师id
     * @param size 客户数量
     */
    @Async
    public void sendEngineerClientAlert(Integer uid, int size, UserVO customerUserVO) {
        // 商家vo
        Integer customerUserId = customerUserVO.getId();
        String customerUserName = customerUserVO.getNickName();
        String customerCorpName = customerUserVO.getCorpName();
        Integer customerCorpOrgId = customerUserVO.getCorpOrgId();

        // 交付工程师vo
        UserVO userVO = userService.selectById(uid);
        // 如果size时零表示需要查询这个交付工程师的客户数量
        if (size == 0) {
            UserProfileQuery userProfileQuery = new UserProfileQuery();
            userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
            userProfileQuery.setProfileVal(String.valueOf(uid));
            List<UserProfileVO> userProfileVOS = userProfileService.queryUserProfileList(userProfileQuery);
            size = userProfileVOS.size();
        }
        // 交付工程师客户数量 70～80之间 普通预警 包含70，不包含80  size >= 70 && size <80
        if (size >= 70 && size <80) {
            String alertMsg = String.format("🚨 交付工程师客户数量预警\n\n" +
                                            "工程师ID: %s\n" +
                                            "工程师姓名: %s\n" +
                                            "当前客户数量: %d\n" +
                                            "预警阈值: 70\n" +
                                            "备注：%s\n\n" +
                                            "商家id：%d\n" +
                                            "商家名称：%s\n" +
                                            "商家公司id：%d\n" +
                                            "商家公司名称：%s",
                uid, userVO.getNickName(), size,userVO.getMemo(),customerUserId,customerUserName,customerCorpOrgId,customerCorpName);
            // 钉钉发送普通预警
            DingTalkNoticeHelper.sendMsg2EngineerClientAlert(alertMsg);
        } else if(size >=80) {
            // 强制预警，@负责人
            List<String> atMobiles = Arrays.asList("15515706862");
            String alertMsg = String.format(
                "🚨🚨🚨 紧急！交付工程师客户数量超限\n\n" +
                "工程师ID: %s\n" +
                "工程师姓名: %s\n" +
                "当前客户数量: %d\n" +
                "⚠️ 超过危险阈值: 80\n" +
                "备注：%s\n\n" +
                "商家id：%d\n" +
                "商家名称：%s\n" +
                "商家公司id：%d\n" +
                "商家公司名称：%s\n"+
                "请立即手动分配交付工程师！",
                uid, userVO.getNickName(), size, userVO.getMemo(),
                customerUserId, customerUserName,customerCorpOrgId,customerCorpName);
            // 钉钉发送强制报警
            DingTalkNoticeHelper.sendMsg2EngineerClientAlert(alertMsg,
                atMobiles);

        }



    }
}
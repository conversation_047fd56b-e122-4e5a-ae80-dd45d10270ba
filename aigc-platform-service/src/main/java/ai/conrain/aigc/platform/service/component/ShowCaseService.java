package ai.conrain.aigc.platform.service.component;

import java.io.IOException;
import java.util.List;

import ai.conrain.aigc.platform.service.model.vo.ShowCaseVO;

/**
 * 优秀案例 Service定义
 *
 * <AUTHOR>
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
public interface ShowCaseService extends CachedService<ShowCaseVO, Integer, ShowCaseVO>{
	
	/**
	 * 查询优秀案例对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ShowCaseVO selectById(Integer id);

	/**
	 * 删除优秀案例对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加优秀案例对象
	 * @param showCase 对象参数
	 * @return 返回结果
	 */
	ShowCaseVO insert(ShowCaseVO showCase) throws IOException;

	/**
	 * 修改优秀案例对象
	 * @param showCase 对象参数
	 */
	void updateById(ShowCaseVO showCase) throws IOException;
}
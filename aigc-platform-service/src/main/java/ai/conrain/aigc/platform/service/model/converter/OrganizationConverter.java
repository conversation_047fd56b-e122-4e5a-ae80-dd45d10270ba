package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.OrganizationDO;
import ai.conrain.aigc.platform.service.model.biz.DistributorOrgTree;
import ai.conrain.aigc.platform.service.model.query.OrganizationQuery;
import ai.conrain.aigc.platform.dal.example.OrganizationExample;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * OrganizationConverter
 *
 * @version OrganizationService.java v 0.1 2024-07-12 04:26:40
 */
public class OrganizationConverter {

    /**
     * DO -> VO
     */
    public static OrganizationVO do2VO(OrganizationDO from) {
        OrganizationVO to = new OrganizationVO();
        to.setId(from.getId());
        to.setParentId(from.getParentId());
        to.setRoot(from.getRoot());
        to.setRootId(from.getRootId());
        to.setOrgType(from.getOrgType());
        to.setName(from.getName());
        to.setTags(from.getTags());
        to.setOrgLevel(from.getOrgLevel());
        to.setChannelAdminId(from.getChannelAdminId());
        to.setChannelAdminNickName(from.getChannelAdminNickName());
        to.setCreatorMasterUserId(from.getCreatorMasterUserId());
        to.setCreatorUserRoleType(from.getCreatorUserRoleType());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo() != null ? com.alibaba.fastjson.JSON.parseObject(from.getExtInfo()) : null);
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static OrganizationDO vo2DO(OrganizationVO from) {
        OrganizationDO to = new OrganizationDO();
        to.setId(from.getId());
        to.setParentId(from.getParentId());
        to.setRoot(from.getRoot());
        to.setRootId(from.getRootId());
        to.setOrgType(from.getOrgType());
        to.setName(from.getName());
        to.setTags(from.getTags());
        to.setOrgLevel(from.getOrgLevel());
        to.setChannelAdminId(from.getChannelAdminId());
        to.setChannelAdminNickName(from.getChannelAdminNickName());
        to.setCreatorMasterUserId(from.getCreatorMasterUserId());
        to.setCreatorUserRoleType(from.getCreatorUserRoleType());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toJSONString() : null);
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static OrganizationQuery do2Query(OrganizationDO from) {
        OrganizationQuery to = new OrganizationQuery();
        to.setId(from.getId());
        to.setParentId(from.getParentId());
        to.setRoot(from.getRoot());
        to.setRootId(from.getRootId());
        to.setLeaf(from.getLeaf());
        to.setOrgType(from.getOrgType());
        to.setName(from.getName());
        to.setTags(from.getTags());
        to.setOrgLevel(from.getOrgLevel());
        to.setCreatorMasterUserId(from.getCreatorMasterUserId());
        to.setCreatorUserRoleType(from.getCreatorUserRoleType());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static OrganizationDO query2DO(OrganizationQuery from) {
        OrganizationDO to = new OrganizationDO();
        to.setId(from.getId());
        to.setParentId(from.getParentId());
        to.setRoot(from.getRoot());
        to.setRootId(from.getRootId());
        to.setLeaf(from.getLeaf());
        to.setOrgType(from.getOrgType());
        to.setName(from.getName());
        to.setTags(from.getTags());
        to.setOrgLevel(from.getOrgLevel());
        to.setCreatorMasterUserId(from.getCreatorMasterUserId());
        to.setCreatorUserRoleType(from.getCreatorUserRoleType());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static OrganizationExample query2Example(OrganizationQuery from) {
        OrganizationExample to = new OrganizationExample();
        OrganizationExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getParentId())) {
            c.andParentIdEqualTo(from.getParentId());
        }
        if (!ObjectUtils.isEmpty(from.getRoot())) {
            c.andRootEqualTo(from.getRoot());
        }
        if (!ObjectUtils.isEmpty(from.getRootId())) {
            c.andRootIdEqualTo(from.getRootId());
        }
        if (!ObjectUtils.isEmpty(from.getLeaf())) {
            c.andLeafEqualTo(from.getLeaf());
        }
        if (!ObjectUtils.isEmpty(from.getOrgType())) {
            c.andOrgTypeEqualTo(from.getOrgType());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getTags())) {
            c.andTagsEqualTo(from.getTags());
        }
        if (!ObjectUtils.isEmpty(from.getOrgLevel())) {
            c.andOrgLevelEqualTo(from.getOrgLevel());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorMasterUserId())) {
            c.andCreatorMasterUserIdEqualTo(from.getCreatorMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorUserRoleType())) {
            c.andCreatorUserRoleTypeEqualTo(from.getCreatorUserRoleType());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorOperatorUserId())) {
            c.andCreatorOperatorUserIdEqualTo(from.getCreatorOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getModifierOperatorUserId())) {
            c.andModifierOperatorUserIdEqualTo(from.getModifierOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getOrgIdList())) {
            c.andOrgIdIn(from.getOrgIdList());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    public static DistributorOrgTree vo2Tree(OrganizationVO vo) {
        DistributorOrgTree tree = new DistributorOrgTree();
        tree.setOrgId(vo.getId());
        tree.setOrgName(vo.getName());
        tree.setSalesType(vo.getSalesTypeCode());
        return tree;
    }
    /**
     * do list -> vo list
     */
    public static List<OrganizationVO> doList2VOList(List<OrganizationDO> list) {
        return CommonUtil.listConverter(list, OrganizationConverter::do2VO);
    }
}
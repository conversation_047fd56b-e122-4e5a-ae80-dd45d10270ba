package ai.conrain.aigc.platform.service.constants;

import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONObject;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CODE_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PAY_TYPE;

public class PayType {
    public static final String ALIPAY = "alipay";
    public static final String WX = "wx";
    public static final String OFFLINE_PAYMENT = "offlinePayment";
    public static final String CORP_WX = "corpWx";

    public static String parsePayTypeByPayDetail(String payDetail) {
        if (CommonUtil.isValidJson(payDetail)) {
            JSONObject json = JSONObject.parseObject(payDetail);
            if (json != null && json.containsKey(KEY_PAY_TYPE)) {
                return json.getString(KEY_PAY_TYPE);
            } else if (json != null) {
                if (json.containsKey(KEY_CODE_URL)){
                    return PayType.WX;
                }
                return PayType.OFFLINE_PAYMENT;
            }
        }

        return PayType.OFFLINE_PAYMENT;
    }
}

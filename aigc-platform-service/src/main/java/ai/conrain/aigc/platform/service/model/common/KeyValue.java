/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

import java.io.Serializable;

import lombok.Data;

/**
 * 键值对
 *
 * <AUTHOR>
 * @version : KeyValue.java, v 0.1 2023/9/24 11:18 renxiao.wu Exp $
 */
@Data
public class KeyValue implements Serializable {
    private static final long serialVersionUID = -7954746177066387248L;
    /** key */
    private String key;
    /** value */
    private String value;

    public KeyValue(String key, String value) {
        this.key = key;
        this.value = value;
    }
}

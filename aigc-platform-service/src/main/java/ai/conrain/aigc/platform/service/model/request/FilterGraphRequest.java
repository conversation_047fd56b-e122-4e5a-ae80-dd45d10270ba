package ai.conrain.aigc.platform.service.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class FilterGraphRequest implements CreativeRequest {

    @JSONField(serialize = false)
    @NotEmpty
    private List<MultipartFile> files;

    @JSONField(serialize = false)
    @NotBlank
    private String showImage;

    /** second 放大 10倍, 单位是 0.1s */
    @NotNull(message = "抽帧间隔不能为空")
    @DecimalMin(value = "0.1", message = "抽帧间隔不能小于 0.1")
    private Double second;

    private Integer frame;

    @NotNull
    private Boolean filterLowQuality;
}

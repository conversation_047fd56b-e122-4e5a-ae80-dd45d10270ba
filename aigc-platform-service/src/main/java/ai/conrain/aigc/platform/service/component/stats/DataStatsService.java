package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import ai.conrain.aigc.platform.service.model.stats.DataStatsJobConfig;

import java.util.Map;

public interface DataStatsService {

    /**
     * 获取数据统计类型
     * 
     * @return 数据统计类型
     */
    StatsTypeEnum getStatsType();

    /**
     * 执行数据统计
     * 
     * @param config 数据统计配置
     * @return 数据统计结果
     */
    Map<String, Integer> executeDataStats(DataStatsJobConfig config);

}

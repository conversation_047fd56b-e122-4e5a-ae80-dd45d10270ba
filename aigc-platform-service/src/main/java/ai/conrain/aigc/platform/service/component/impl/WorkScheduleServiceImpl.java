package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.WorkScheduleDO;
import ai.conrain.aigc.platform.dal.example.WorkScheduleExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.model.converter.WorkScheduleConverter;
import ai.conrain.aigc.platform.dal.dao.WorkScheduleDAO;
import ai.conrain.aigc.platform.service.component.WorkScheduleService;

/**   
 * WorkScheduleService实现
 *
 * <AUTHOR>
 * @version WorkScheduleService.java v 0.1 2025-04-01 03:40:32
 */
@Slf4j
@Service
public class WorkScheduleServiceImpl implements WorkScheduleService {

	/** DAO */
	@Autowired
	private WorkScheduleDAO workScheduleDAO;

	@Override
	public WorkScheduleVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		WorkScheduleDO data = workScheduleDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return WorkScheduleConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = workScheduleDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除WorkSchedule失败");
	}

	@Override
	public WorkScheduleVO insert(WorkScheduleVO workSchedule) {
		AssertUtil.assertNotNull(workSchedule, ResultCode.PARAM_INVALID, "workSchedule is null");
		AssertUtil.assertTrue(workSchedule.getId() == null, ResultCode.PARAM_INVALID, "workSchedule.id is present");

		//创建时间、修改时间兜底
		if (workSchedule.getCreateTime() == null) {
			workSchedule.setCreateTime(new Date());
		}

		if (workSchedule.getModifyTime() == null) {
			workSchedule.setModifyTime(new Date());
		}

		WorkScheduleDO data = WorkScheduleConverter.vo2DO(workSchedule);
		Integer n = workScheduleDAO.insert(data);
		AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "创建WorkSchedule失败");
		AssertUtil.assertNotNull(data.getId(), "新建WorkSchedule返回id为空");
		workSchedule.setId(data.getId());
		return workSchedule;
	}


	@Override
	public void updateByIdSelective(WorkScheduleVO workSchedule) {
		AssertUtil.assertNotNull(workSchedule, ResultCode.PARAM_INVALID, "workSchedule is null");
    	AssertUtil.assertTrue(workSchedule.getId() != null, ResultCode.PARAM_INVALID, "workSchedule.id is null");

		//修改时间必须更新
		workSchedule.setModifyTime(new Date());
		WorkScheduleDO data = WorkScheduleConverter.vo2DO(workSchedule);
		int n = workScheduleDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新WorkSchedule失败，影响行数:" + n);
	}

	@Override
	public List<WorkScheduleVO> queryWorkScheduleList(WorkScheduleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		WorkScheduleExample example = WorkScheduleConverter.query2Example(query);

		List<WorkScheduleDO> list = workScheduleDAO.selectByExample(example);
			return WorkScheduleConverter.doList2VOList(list);
	}

	@Override
	public Long queryWorkScheduleCount(WorkScheduleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		WorkScheduleExample example = WorkScheduleConverter.query2Example(query);
		long c = workScheduleDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询
	 */
	@Override
	public PageInfo<WorkScheduleVO> queryWorkScheduleByPage(WorkScheduleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<WorkScheduleVO> page = new PageInfo<>();

		WorkScheduleExample example = WorkScheduleConverter.query2Example(query);
		long totalCount = workScheduleDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<WorkScheduleDO> list = workScheduleDAO.selectByExample(example);
		page.setList(WorkScheduleConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public List<WorkScheduleVO> batchInsert(List<WorkScheduleVO> workScheduleList) {
		AssertUtil.assertNotNull(workScheduleList, ResultCode.PARAM_INVALID, "workScheduleList is null");
		AssertUtil.assertTrue(!workScheduleList.isEmpty(), ResultCode.PARAM_INVALID, "workScheduleList is empty");
		
		Date now = new Date();
		List<WorkScheduleVO> resultList = new ArrayList<>();
		
		for (WorkScheduleVO workSchedule : workScheduleList) {
			AssertUtil.assertTrue(workSchedule.getId() == null, ResultCode.PARAM_INVALID, "workSchedule.id is present");
			
			//创建时间、修改时间兜底
			if (workSchedule.getCreateTime() == null) {
				workSchedule.setCreateTime(now);
			}

			if (workSchedule.getModifyTime() == null) {
				workSchedule.setModifyTime(now);
			}

			WorkScheduleDO data = WorkScheduleConverter.vo2DO(workSchedule);
			Integer n = workScheduleDAO.insert(data);
			AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "创建WorkSchedule失败");
			AssertUtil.assertNotNull(data.getId(), "新建WorkSchedule返回id为空");
			workSchedule.setId(data.getId());
			resultList.add(workSchedule);
		}
		
		return resultList;
	}
	
	@Override
	public void batchDeleteByIds(List<Integer> ids) {
		AssertUtil.assertTrue(CollectionUtils.isNotEmpty(ids), ResultCode.PARAM_INVALID, "ids is empty");
		
		int n = workScheduleDAO.deleteByIds(ids);
		AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "删除WorkSchedule失败");
	}

}
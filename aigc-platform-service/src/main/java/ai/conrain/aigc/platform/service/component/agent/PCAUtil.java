package ai.conrain.aigc.platform.service.component.agent;

import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.linear.EigenDecomposition;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.stat.correlation.Covariance;

public class PCAUtil {

    /**
     * PCA降维
     * @param data 原始数据矩阵
     * @param targetDim 目标维度
     * @return 降维后的数据矩阵
     */
    public static double[][] performPCA(double[][] data, int targetDim) {
        int n = data.length;
        int originalDim = data[0].length;

        // 如果目标维度大于等于原始维度，直接返回原数据
        if (targetDim >= originalDim) {
            return data;
        }

        // 计算每列的均值并中心化数据
        double[] means = new double[originalDim];
        for (int j = 0; j < originalDim; j++) {
            double sum = 0;
            for (int i = 0; i < n; i++) {
                sum += data[i][j];
            }
            means[j] = sum / n;
        }

        // 中心化数据
        double[][] centeredData = new double[n][originalDim];
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < originalDim; j++) {
                centeredData[i][j] = data[i][j] - means[j];
            }
        }

        RealMatrix centeredMatrix = new Array2DRowRealMatrix(centeredData);

        // 计算协方差矩阵
        Covariance covariance = new Covariance(centeredMatrix);
        RealMatrix covMatrix = covariance.getCovarianceMatrix();

        // 特征值分解
        EigenDecomposition eigenDecomp = new EigenDecomposition(covMatrix);

        // 获取前targetDim个主成分
        RealMatrix eigenVectors = eigenDecomp.getV();
        double[][] principalComponents = new double[originalDim][targetDim];

        // 按特征值大小排序，取前targetDim个特征向量
        for (int i = 0; i < targetDim; i++) {
            for (int j = 0; j < originalDim; j++) {
                // 特征向量按列存储，从最后一列开始取（特征值从大到小）
                principalComponents[j][i] = eigenVectors.getEntry(j, originalDim - 1 - i);
            }
        }

        RealMatrix pcMatrix = new Array2DRowRealMatrix(principalComponents);

        // 投影到主成分空间
        RealMatrix projectedData = centeredMatrix.multiply(pcMatrix);

        return projectedData.getData();
    }
}

package ai.conrain.aigc.platform.service.helper.media;

import ai.conrain.aigc.platform.integration.aliyun.IceClientService;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 【重要】输入或输出 OSS Bucket 所在 Region，必须和使用 IMS 的 Region 保持一致
 */
@Component
public class MediaProduceHelper {

    //oss存储地址，以/结尾
    @Value("${aliyun.ice.ossUrl}")
    private String ossUrl;

    @Value("${aliyun.ice.callback}")
    private String callback;

    @Autowired
    private IceClientService iceClientService;

    /**
     * 使用时间线，创建媒体生产任务
     * https://help.aliyun.com/zh/ims/developer-reference/timeline-configuration-description?spm=a2c4g.11186623.0.0.72d3742e03Gz9Q#section-fop-no2-66i
     */
    public String produceMediaByTimeline(JSONObject timeline, String outoutMediaFileName) {
        AssertUtil.assertNotNull(timeline, "timeline must not be null");
        AssertUtil.assertNotNull(outoutMediaFileName, "outoutMediaFileName must not be null");

        SubmitMediaProducingJobRequest req = new SubmitMediaProducingJobRequest();
        req.setTimeline(timeline.toJSONString());

        fillOutputAndUserData(outoutMediaFileName, req);

        SubmitMediaProducingJobResponseBody ret = iceClientService.submitMediaProducingJob(req);
        return ret.getJobId();
    }

    /**
     * 根据云剪辑普通模板，创建媒体生产任务
     * https://help.aliyun.com/zh/ims/developer-reference/timeline-configuration-description?spm=a2c4g.11186623.0.0.72d3742e03Gz9Q#section-fop-no2-66i
     * @param templateId
     * @param params
     * @return
     */
    public String produceMediaByTemplate(String templateId, JSONObject params, String outoutMediaFileName) {
        AssertUtil.assertNotNull(templateId, "templateId must not be null");
        AssertUtil.assertTrue(params != null && !params.isEmpty(), "params must not be empty");

        SubmitMediaProducingJobRequest req = new SubmitMediaProducingJobRequest();
        req.setTemplateId(templateId).setClipsParam(params.toJSONString());

        fillOutputAndUserData(outoutMediaFileName, req);

        SubmitMediaProducingJobResponseBody ret = iceClientService.submitMediaProducingJob(req);
        return ret.getJobId();
    }

    private void fillOutputAndUserData(String outputFileName, SubmitMediaProducingJobRequest req) {
        JSONObject outputConfig = new JSONObject();
        outputConfig.put("MediaURL", ossUrl + outputFileName);
        req.setOutputMediaConfig(outputConfig.toJSONString());

        JSONObject userData = new JSONObject();
        if (!EnvUtil.isLocalEnv()) {
            userData.put("NotifyAddress", callback);
        }
        req.setUserData(userData.toJSONString());
    }
}

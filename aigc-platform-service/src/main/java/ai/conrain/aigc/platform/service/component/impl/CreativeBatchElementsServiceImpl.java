package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.CreativeBatchElementsDAO;
import ai.conrain.aigc.platform.dal.entity.CreativeBatchElementsDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchElementsExample;
import ai.conrain.aigc.platform.service.component.CreativeBatchElementsService;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchElementsConverter;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchElementsVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * CreativeBatchElementsService实现
 *
 * <AUTHOR>
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
@Slf4j
@Service
public class CreativeBatchElementsServiceImpl implements CreativeBatchElementsService {

    /** DAO */
    @Autowired
    private CreativeBatchElementsDAO creativeBatchElementsDAO;
    @Autowired
    private CreativeElementService creativeElementService;

    @Override
    public CreativeBatchElementsVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CreativeBatchElementsDO data = creativeBatchElementsDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return CreativeBatchElementsConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = creativeBatchElementsDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CreativeBatchElements失败");
    }

    @Override
    public CreativeBatchElementsVO insert(CreativeBatchElementsVO creativeBatchElements) {
        AssertUtil.assertNotNull(creativeBatchElements, ResultCode.PARAM_INVALID, "creativeBatchElements is null");
        AssertUtil.assertTrue(creativeBatchElements.getId() == null, ResultCode.PARAM_INVALID,
                "creativeBatchElements.id is present");

        // 创建时间、修改时间兜底
        if (creativeBatchElements.getCreateTime() == null) {
            creativeBatchElements.setCreateTime(new Date());
        }

        if (creativeBatchElements.getModifyTime() == null) {
            creativeBatchElements.setModifyTime(new Date());
        }

        CreativeBatchElementsDO data = CreativeBatchElementsConverter.vo2DO(creativeBatchElements);
        Integer n = creativeBatchElementsDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CreativeBatchElements失败");
        AssertUtil.assertNotNull(data.getId(), "新建CreativeBatchElements返回id为空");
        creativeBatchElements.setId(data.getId());
        return creativeBatchElements;
    }

    @Override
    public void updateByIdSelective(CreativeBatchElementsVO creativeBatchElements) {
        AssertUtil.assertNotNull(creativeBatchElements, ResultCode.PARAM_INVALID, "creativeBatchElements is null");
        AssertUtil.assertTrue(creativeBatchElements.getId() != null, ResultCode.PARAM_INVALID,
                "creativeBatchElements.id is null");

        // 修改时间必须更新
        creativeBatchElements.setModifyTime(new Date());
        CreativeBatchElementsDO data = CreativeBatchElementsConverter.vo2DO(creativeBatchElements);
        int n = creativeBatchElementsDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CreativeBatchElements失败，影响行数:" + n);
    }

    @Override
    public List<CreativeBatchElementsVO> queryCreativeBatchElementsList(CreativeBatchElementsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CreativeBatchElementsExample example = CreativeBatchElementsConverter.query2Example(query);

        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectByExample(example);
        return CreativeBatchElementsConverter.doList2VOList(list);
    }

    @Override
    public List<CreativeElementVO> queryRecentElements(CreativeBatchElementsQuery query) {
        CreativeBatchElementsExample example = CreativeBatchElementsConverter.query2Example(query);
        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectRecentElementsByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<CreativeElementVO> elements = new ArrayList<>();
        list.forEach(e -> {
            CreativeElementVO element = creativeElementService.queryByIdAndLevel(e.getElementId(), 2);
            if (element != null && !elements.contains(element)) {

                // 如果是实验元素，则不返回
                if (Objects.nonNull(element.getExtInfo())) {
                    Boolean experimental = element.getExtInfo(CommonConstants.KEY_EXPERIMENTAL, Boolean.class);
                    if (Boolean.TRUE.equals(experimental)) {
                        return;
                    }
                }

                // 是否只查询lora元素
                if (Objects.nonNull(query.getOnlyLora()) && query.getOnlyLora()) {
                    if (Objects.isNull(element.getExtInfo())) {
                        return;
                    }
                    String isLora = element.getExtInfo(CommonConstants.KEY_SWAP_TYPE, String.class);
                    if (!"LORA".equals(isLora) && !CommonConstants.YES.equals(isLora)) {
                        return;
                    }
                }


                elements.add(element);
            }
        });
        return elements;
    }

    @Override
    public List<CreativeElementVO> queryRecentFaceLora(Integer userId) {
        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectRecentFaceLoraById(userId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<CreativeElementVO> elements = new ArrayList<>();
        list.forEach(e -> {
            CreativeElementVO element = creativeElementService.queryByIdAndLevel(e.getElementId(), 2);
            if (element != null && !elements.contains(element)) {
                elements.add(element);
            }
        });
        return elements;
    }

    @Override
    public Long queryCreativeBatchElementsCount(CreativeBatchElementsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CreativeBatchElementsExample example = CreativeBatchElementsConverter.query2Example(query);
        return creativeBatchElementsDAO.countByExample(example);
    }

    /**
     * 带条件分页查询创作批次属性
     */
    @Override
    public PageInfo<CreativeBatchElementsVO> queryCreativeBatchElementsByPage(CreativeBatchElementsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CreativeBatchElementsVO> page = new PageInfo<>();

        CreativeBatchElementsExample example = CreativeBatchElementsConverter.query2Example(query);
        long totalCount = creativeBatchElementsDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectByExample(example);
        page.setList(CreativeBatchElementsConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        Integer userId = OperationContextHolder.getMasterUserId();
        Integer operatorId = OperationContextHolder.getOperatorUserId();

        List<CreativeBatchElementsDO> list = new ArrayList<>();

        elements.forEach(e -> {
            CreativeBatchElementsDO data = new CreativeBatchElementsDO();
            data.setBatchId(batch.getId());
            data.setElementId(e.getId());
            data.setElementKey(e.getConfigKey());
            data.setOperatorId(operatorId);
            data.setUserId(userId);
            list.add(data);
        });

        creativeBatchElementsDAO.batchInsert(list);
    }

    @Override
    public List<CreativeElementVO> queryBatchElements(Integer batchId, boolean includesChildren) {
        CreativeBatchElementsExample example = new CreativeBatchElementsExample();
        example.createCriteria().andBatchIdEqualTo(batchId);
        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectByExample(example);
        AssertUtil.assertNotNull(list, ResultCode.SYS_ERROR, "创作任务关联元素为空" + batchId);

        if (includesChildren) {
            return creativeElementService.batchQueryByIdsWithChildren(
                    list.stream().map(CreativeBatchElementsDO::getElementId).collect(Collectors.toList()));
        } else {
            return creativeElementService.batchQueryByIds(
                    list.stream().map(CreativeBatchElementsDO::getElementId).collect(Collectors.toList()));
        }

    }

    @Override
    public Map<Integer, List<CreativeElementVO>> batchQueryBatchElements(List<Integer> batchIds) {
        CreativeBatchElementsExample example = new CreativeBatchElementsExample();
        example.createCriteria().andBatchIdIn(batchIds);
        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectByExample(example);
        Map<Integer, List<CreativeElementVO>> result = new HashMap<>();

        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        List<CreativeElementVO> elements = creativeElementService.batchQueryByIds(
                list.stream().map(CreativeBatchElementsDO::getElementId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(elements)) {
            return result;
        }

        batchIds.forEach(batchId -> result.put(batchId, elements.stream().filter(
                item -> list.stream().filter(i -> i.getBatchId().equals(batchId))
                        .anyMatch(i -> i.getElementId().equals(item.getId()))).collect(Collectors.toList())));

        return result;
    }

    @Override
    public List<CreativeElementVO> queryBatchElementsNotEmpty(Integer batchId, boolean includesChildren) {
        CreativeBatchElementsExample example = new CreativeBatchElementsExample();
        example.createCriteria().andBatchIdEqualTo(batchId);
        List<CreativeBatchElementsDO> list = creativeBatchElementsDAO.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        if (includesChildren) {
            return creativeElementService.batchQueryByIdsWithChildren(
                    list.stream().map(CreativeBatchElementsDO::getElementId).collect(Collectors.toList()));
        } else {
            return creativeElementService.batchQueryByIds(
                    list.stream().map(CreativeBatchElementsDO::getElementId).collect(Collectors.toList()));
        }
    }

    @Override
    public List<Integer> queryHasShowImageChildrenIds(CreativeElementQuery query, Integer needResultCount) {
        // 参数提取
        List<Integer> idList = query.getIdList();

        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Integer> result = new ArrayList<>();

        // 排除掉实验状态的场景
        List<Integer> idListWithoutExperimental = creativeElementService.queryExperimentalIds(idList);

        // idList 排除掉 idListWithoutExperimental
        if (idListWithoutExperimental != null && !idListWithoutExperimental.isEmpty()) {
            idList = idList.stream()
                    .filter(id -> !idListWithoutExperimental.contains(id))
                    .collect(Collectors.toList());
        }


        // 设置为 1000
        query.setPageSize(1000);
        query.setIsHasShowImage(Boolean.TRUE);
        query.setIsExclusive(null);
        query.setOpenScope(null);

        // 如果ID数量较少，直接查询
        if (idList.size() <= 1000) {
            query.setIds(idList);
            // 查询含有参考图的场景
            PageInfo<CreativeElementVO> creativeElementVOPageInfo = creativeElementService.queryByPage(query);

            // 如果没有结果，直接返回
            if (creativeElementVOPageInfo.getTotalCount() < 0) {
                return null;
            }
            List<CreativeElementVO> creativeElementVOList = creativeElementVOPageInfo.getList();
            // 提取 ID
            List<Integer> idlist = creativeElementVOList.stream().map(CreativeElementVO::getId).collect(Collectors.toList());
            // 去重并限制数量
            return idlist.stream()
                    .distinct()
                    .limit(needResultCount)
                    .collect(Collectors.toList());
        }

        // 大数据量时分批查询，避免IN子句过长影响性能
        int batchSize = 1000;

        for (int i = 0; i < idList.size(); i += batchSize) {
            // 如果已经找到足够的记录，直接跳出
            if (result.size() >= needResultCount) {
                break;
            }

            int end = Math.min(i + batchSize, idList.size());
            List<Integer> batch = idList.subList(i, end);

            query.setIds(batch);

            // 查询含有参考图的场景
            PageInfo<CreativeElementVO> creativeElementVOPageInfo = creativeElementService.queryByPage(query);

            // 如果没有结果，直接返回
            if (creativeElementVOPageInfo.getTotalCount() < 0) {
                return null;
            }
            List<CreativeElementVO> creativeElementVOList = creativeElementVOPageInfo.getList();
            // 提取 ID
            List<Integer> idlist = creativeElementVOList.stream().map(CreativeElementVO::getId).collect(Collectors.toList());

            // 添加批次结果，去重并检查是否达到上限
            for (Integer id : idlist) {
                if (!result.contains(id)) {
                    result.add(id);
                    // 达到20条记录后直接跳出
                    if (result.size() >= needResultCount) {
                        break;
                    }
                }
            }
        }

        return result;
    }

}
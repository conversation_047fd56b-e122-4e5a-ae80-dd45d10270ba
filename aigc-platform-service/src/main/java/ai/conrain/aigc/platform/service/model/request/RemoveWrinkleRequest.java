package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class RemoveWrinkleRequest implements CreativeRequest {

    //图片url
    @NotBlank
    private String originImage;

    private String rmDegree;

    private List<Integer> alphas;

    /** 图片来源：上传upload/历史纪录history */
    @NotBlank
    private String imageSource;
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.LogoCombineRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.LOGO_COMBINE_BACK_FLOW_PARAMS;

/**
 * 印花上身创作服务
 *
 * <AUTHOR>
 * @version : LogoCombineCreativeService.java, v 0.1 2024/7/18 15:36 renxiao.wu Exp $
 */
@Service
public class LogoCombineCreativeService extends AbstractCreativeService<LogoCombineRequest> {
    @Autowired
    private OssService ossService;
    @Autowired
    private ComfyUIService comfyUIService;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.LOGO_COMBINE;
    }

    @Override
    protected CreativeBatchVO buildData(LogoCombineRequest request, MaterialModelVO modelVO) throws IOException {
        BufferedImage image = ImageIO.read(request.getImage().getInputStream());

        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = CommonUtil.uuid() + "_logo.png";
        String inputImagePath = ComfyUIUtils.buildInputPath(OperationContextHolder.getMasterUserId()) + imageName;
        fileDispatch.uploadFile(inputImagePath, new ByteArrayInputStream(imageBytes),
            OperationContextHolder.getMasterUserId());
        //2.2.上传到oss
        ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
        String ossUrl = ossService.upload(imageName, bais);

        return buildBatch(inputImagePath, ossUrl, request);
    }

    @Override
    protected Map<Integer, List<Integer>> getConfigs(LogoCombineRequest request) {
        return request.getConfigs();
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put("lora", modelVO);
        elements.forEach(element -> context.put(element.getConfigKey(), element));

        context.put("seed2", RandomStringUtils.randomNumeric(15));
        context.put(KEY_LOGO_IMAGE, task.getStringFromExtInfo(KEY_LOGO_IMAGE));
        context.put(KEY_REFER_IMAGE, task.getStringFromExtInfo(KEY_REFER_IMAGE));
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        List<CreativeTaskVO> result = new ArrayList<>();

        List<CreativeElementVO> refers = elements.stream().filter(
            e -> ElementConfigKeyEnum.valueOf(e.getConfigKey()) == ElementConfigKeyEnum.REFER).collect(
            Collectors.toList());

        AssertUtil.assertTrue(CollectionUtils.size(refers) == batch.getBatchCnt(), "REFER数量与批次数量不一致");

        //每张图生成一个任务
        refers.forEach(e -> {
            //插入任务数据
            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);
            target.addExtInfo(KEY_REFER_IMAGE, e.getExtInfo(KEY_REFER_POSE));
            target.addExtInfo(KEY_REFER_ID, e.getId());

            CreativeTaskVO data = creativeTaskService.insert(target);
            result.add(data);
        });

        return result;
    }

    @Override
    protected String postParse(String prompt) {
        prompt = prompt.replaceAll("\r\n|\r|\n", "");
        prompt = prompt.replaceAll("\\\\n", "\\\\n");
        return prompt;
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        //当没有人脸时，则是背部，这里做简单处理
        if (elements.stream().noneMatch(
            e -> ElementConfigKeyEnum.valueOf(e.getConfigKey()) == ElementConfigKeyEnum.FACE)) {
            return LOGO_COMBINE_BACK_FLOW_PARAMS;
        }
        return super.getFlowKey(elements, modelVO, task, context);
    }

    private CreativeBatchVO buildBatch(String inputImagePath, String ossUrl, LogoCombineRequest request) {
        CreativeBatchVO data = new CreativeBatchVO();
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setShowImage(ossUrl);
        data.setImageProportion(request.getProportion());
        data.addExtInfo(KEY_LOGO_IMAGE, inputImagePath);
        data.addExtInfo(KEY_LOGO_IMAGE_OSS, ossUrl);
        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setBatchCnt(request.getImageNum());
        data.setType(getCreativeType());
        return data;
    }
}

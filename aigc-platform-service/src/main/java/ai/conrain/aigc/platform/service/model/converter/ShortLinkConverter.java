package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ShortLinkDO;
import ai.conrain.aigc.platform.dal.example.ShortLinkExample;
import ai.conrain.aigc.platform.service.model.query.ShortLinkQuery;
import ai.conrain.aigc.platform.service.model.vo.ShortLinkVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * ShortLinkConverter
 *
 * @version ShortLinkService.java
 */
public class ShortLinkConverter {

    /**
     * DO -> VO
     */
    public static ShortLinkVO do2VO(ShortLinkDO from) {
        ShortLinkVO to = new ShortLinkVO();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setShortCode(from.getShortCode());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ShortLinkDO vo2DO(ShortLinkVO from) {
        ShortLinkDO to = new ShortLinkDO();
        to.setId(from.getId());
        to.setUrl(from.getUrl());
        to.setShortCode(from.getShortCode());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ShortLinkExample query2Example(ShortLinkQuery from) {
        ShortLinkExample to = new ShortLinkExample();
        ShortLinkExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!CollectionUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getUrl())) {
            c.andUrlEqualTo(from.getUrl());
        }
        if (!CollectionUtils.isEmpty(from.getUrls())) {
            c.andUrlIn(from.getUrls());
        }
        if (!ObjectUtils.isEmpty(from.getShortCode())) {
            c.andShortCodeEqualTo(from.getShortCode());
        }
        if (!CollectionUtils.isEmpty(from.getShortCodes())) {
            c.andShortCodeIn(from.getShortCodes());
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ShortLinkVO> doList2VOList(List<ShortLinkDO> list) {
        return CommonUtil.listConverter(list, ShortLinkConverter::do2VO);
    }

    /**
     * vo list -> do list
     */
    public static List<ShortLinkDO> voList2DOList(List<ShortLinkVO> list) {
        return CommonUtil.listConverter(list, ShortLinkConverter::vo2DO);
    }
}
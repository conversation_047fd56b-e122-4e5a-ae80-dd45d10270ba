package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserOrganizationDO;
import ai.conrain.aigc.platform.service.model.query.UserOrganizationQuery;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.List;

/**
 * UserOrganizationConverter
 *
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
public class UserOrganizationConverter {

    /**
     * DO -> VO
     */
    public static UserOrganizationVO do2VO(UserOrganizationDO from) {
        UserOrganizationVO to = new UserOrganizationVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOrgId(from.getOrgId());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserOrganizationDO vo2DO(UserOrganizationVO from) {
        UserOrganizationDO to = new UserOrganizationDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOrgId(from.getOrgId());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserOrganizationQuery do2Query(UserOrganizationDO from) {
        UserOrganizationQuery to = new UserOrganizationQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOrgId(from.getOrgId());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserOrganizationDO query2DO(UserOrganizationQuery from) {
        UserOrganizationDO to = new UserOrganizationDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOrgId(from.getOrgId());
        to.setCreatorOperatorUserId(from.getCreatorOperatorUserId());
        to.setModifierOperatorUserId(from.getModifierOperatorUserId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * do list -> vo list
     */
    public static List<UserOrganizationVO> doList2VOList(List<UserOrganizationDO> list) {
        return CommonUtil.listConverter(list, UserOrganizationConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.helper.wx;

import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletRequest;
/**
 * 微信通知处理工具
 */
@Slf4j
public class WeixinNotifyUtils {
    /**
     * 获取请求头签名
     */
    public static SignatureHeader getSignatureHeader(HttpServletRequest request) {
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setSignature(request.getHeader("Wechatpay-Signature"));
        signatureHeader.setNonce(request.getHeader("Wechatpay-Nonce"));
        signatureHeader.setSerial(request.getHeader("Wechatpay-Serial"));
        signatureHeader.setTimeStamp(request.getHeader("Wechatpay-TimeStamp"));
        return signatureHeader;
    }
}
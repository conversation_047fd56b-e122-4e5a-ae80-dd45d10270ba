package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import java.util.List;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 图片抠图任务分发
 */
@Slf4j
@Service
public class PictureMattingDispatch extends AbstractTaskDispatch {

    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Override
    public DispatchTypeEnum getType() {
        return DispatchTypeEnum.PICTURE_MATTING;
    }

    @Override
    protected List<? extends IExtModel> queryUnProcessedData(Integer pipelineId, int idleNum) {
        return creativeBatchService.queryUnProcessedTopUser(pipelineId, null,
            CreativeTypeEnum.getPictureMattingTypeList(), idleNum);
    }

    @Override
    protected boolean isTaskFinished(Integer taskId) {
        if (taskId == null) {
            log.warn("【任务分发】判断任务是否已经执行完毕，taskId为空，type={},taskId={}", getType(), taskId);
            return true;
        }
        CreativeBatchVO batch = creativeBatchService.selectById(taskId);
        return batch == null || batch.getStatus().isEnd();
    }

}

package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.FixedCreativeTemplateDO;
import ai.conrain.aigc.platform.dal.example.FixedCreativeTemplateExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.FixedCreativeTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.FixedCreativeTemplateVO;
import ai.conrain.aigc.platform.service.model.converter.FixedCreativeTemplateConverter;
import ai.conrain.aigc.platform.dal.dao.FixedCreativeTemplateDAO;
import ai.conrain.aigc.platform.service.component.FixedCreativeTemplateService;

/**   
 * FixedCreativeTemplateService实现
 *
 * <AUTHOR>
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
@Slf4j
@Service
public class FixedCreativeTemplateServiceImpl implements FixedCreativeTemplateService {

	/** DAO */
	@Autowired
	private FixedCreativeTemplateDAO fixedCreativeTemplateDAO;

	@Override
	public FixedCreativeTemplateVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		FixedCreativeTemplateDO data = fixedCreativeTemplateDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return FixedCreativeTemplateConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = fixedCreativeTemplateDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除FixedCreativeTemplate失败");
	}

	@Override
	public FixedCreativeTemplateVO insert(FixedCreativeTemplateVO fixedCreativeTemplate) {
		AssertUtil.assertNotNull(fixedCreativeTemplate, ResultCode.PARAM_INVALID, "fixedCreativeTemplate is null");
		AssertUtil.assertTrue(fixedCreativeTemplate.getId() == null, ResultCode.PARAM_INVALID, "fixedCreativeTemplate.id is present");

		//创建时间、修改时间兜底
		if (fixedCreativeTemplate.getCreateTime() == null) {
			fixedCreativeTemplate.setCreateTime(new Date());
		}

		if (fixedCreativeTemplate.getModifyTime() == null) {
			fixedCreativeTemplate.setModifyTime(new Date());
		}

		FixedCreativeTemplateDO data = FixedCreativeTemplateConverter.vo2DO(fixedCreativeTemplate);
		Integer n = fixedCreativeTemplateDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建FixedCreativeTemplate失败");
		AssertUtil.assertNotNull(data.getId(), "新建FixedCreativeTemplate返回id为空");
		fixedCreativeTemplate.setId(data.getId());
		return fixedCreativeTemplate;
	}


	@Override
	public void updateByIdSelective(FixedCreativeTemplateVO fixedCreativeTemplate) {
		AssertUtil.assertNotNull(fixedCreativeTemplate, ResultCode.PARAM_INVALID, "fixedCreativeTemplate is null");
    	AssertUtil.assertTrue(fixedCreativeTemplate.getId() != null, ResultCode.PARAM_INVALID, "fixedCreativeTemplate.id is null");

		//修改时间必须更新
		fixedCreativeTemplate.setModifyTime(new Date());
		FixedCreativeTemplateDO data = FixedCreativeTemplateConverter.vo2DO(fixedCreativeTemplate);
		int n = fixedCreativeTemplateDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新FixedCreativeTemplate失败，影响行数:" + n);
	}

	@Override
	public List<FixedCreativeTemplateVO> queryFixedCreativeTemplateList(FixedCreativeTemplateQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		FixedCreativeTemplateExample example = FixedCreativeTemplateConverter.query2Example(query);

		List<FixedCreativeTemplateDO> list = fixedCreativeTemplateDAO.selectByExample(example);
			return FixedCreativeTemplateConverter.doList2VOList(list);
	}

	@Override
	public Long queryFixedCreativeTemplateCount(FixedCreativeTemplateQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		FixedCreativeTemplateExample example = FixedCreativeTemplateConverter.query2Example(query);
		long c = fixedCreativeTemplateDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询
	 */
	@Override
	public PageInfo<FixedCreativeTemplateVO> queryFixedCreativeTemplateByPage(FixedCreativeTemplateQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<FixedCreativeTemplateVO> page = new PageInfo<>();

		FixedCreativeTemplateExample example = FixedCreativeTemplateConverter.query2Example(query);
		long totalCount = fixedCreativeTemplateDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<FixedCreativeTemplateDO> list = fixedCreativeTemplateDAO.selectByExampleWithBLOBs(example);
		page.setList(FixedCreativeTemplateConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.ai.model.ModelValidationResponse;
import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationResponse;
import ai.conrain.aigc.platform.integration.ai.model.TrainModelResponse;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.io.IOException;
import java.util.List;

/**
 * 训练轮次表，记录每轮训练的详细信息和作业状态 Service定义
 *
 * <AUTHOR>
 * @version TrainingRoundService.java
 */
public interface TrainingRoundService {
	
	/**
	 * 查询训练轮次表，记录每轮训练的详细信息和作业状态对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TrainingRoundVO selectById(Integer id);

    TrainingRoundVO lockById(Integer id);

	/**
	 * 删除训练轮次表，记录每轮训练的详细信息和作业状态对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加训练轮次表，记录每轮训练的详细信息和作业状态对象
	 * @param trainingRound 对象参数
	 * @return 返回结果
	 */
	TrainingRoundVO insert(TrainingRoundVO trainingRound);

	/**
	 * 修改训练轮次表，记录每轮训练的详细信息和作业状态对象
	 * @param trainingRound 对象参数
	 */
	void updateByIdSelective(TrainingRoundVO trainingRound);

	/**
	 * 带条件批量查询训练轮次表，记录每轮训练的详细信息和作业状态列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<TrainingRoundVO> queryTrainingRoundList(TrainingRoundQuery query);

	/**
	 * 带条件查询训练轮次表，记录每轮训练的详细信息和作业状态数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryTrainingRoundCount(TrainingRoundQuery query);

	/**
	 * 带条件分页查询训练轮次表，记录每轮训练的详细信息和作业状态
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<TrainingRoundVO> queryTrainingRoundByPage(TrainingRoundQuery query);

    //挑选样本
    void startSelectSamples(Integer roundId);
    //样本挑选完成-通知
    void onLabelSamplesFinished(SamplePreparationResponse samplePreparationResponse);
    void querySelectedSampleResult(Integer roundId);

    //验证模型性能
    void startVerifyModelPerformance(Integer roundId);
    //模型性能验证完成-通知
    void onModelPerformanceVerified(ModelValidationResponse modelValidationResponse);

    void queryModelPerformanceResult(Integer roundId);

    //训练模型
    void startTrainJob(Integer roundId);
    //模型训练完成-通知
    void onTrainJobFinished(TrainModelResponse trainNextModelResponse);

    void queryTrainJobResult(Integer roundId);
}
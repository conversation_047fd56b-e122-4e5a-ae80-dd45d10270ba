package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.TrainingTaskExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.TrainingTaskDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingTaskDO;
import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import ai.conrain.aigc.platform.service.component.TrainingTaskService;
import ai.conrain.aigc.platform.service.enums.TrainingTaskStatusEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TrainingTaskConverter;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.service.model.query.TrainingTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.TaskProgressVO;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundStatus;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.model.vo.TrainingTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**   
 * TrainingTaskService实现
 *
 * <AUTHOR>
 * @version TrainingTaskService.java
 */
@Slf4j
@Service
public class TrainingTaskServiceImpl implements TrainingTaskService {

	/** DAO */
	@Autowired
	private TrainingTaskDAO trainingTaskDAO;

    @Autowired
    private TrainingRoundService trainingRoundService;

	@Override
	public TrainingTaskVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		TrainingTaskDO data = trainingTaskDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return TrainingTaskConverter.do2VO(data);
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TrainingRoundQuery roundQuery = new TrainingRoundQuery();
        roundQuery.setTaskId(id);
        List<TrainingRoundVO> rounds = trainingRoundService.queryTrainingRoundList(roundQuery);
        if (CollectionUtils.isNotEmpty(rounds)) {
            for (TrainingRoundVO round : rounds) {
                trainingRoundService.deleteById(round.getId());
            }
        }

		int n = trainingTaskDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TrainingTask失败");
	}

	@Override
    @Transactional(rollbackFor = Exception.class)
	public TrainingTaskVO insert(TrainingTaskVO trainingTask) {
		AssertUtil.assertNotNull(trainingTask, ResultCode.PARAM_INVALID, "trainingTask is null");
		AssertUtil.assertTrue(trainingTask.getId() == null, ResultCode.PARAM_INVALID, "trainingTask.id is present");

		//创建时间、修改时间兜底
		if (trainingTask.getCreateTime() == null) {
			trainingTask.setCreateTime(new Date());
		}

		if (trainingTask.getModifyTime() == null) {
			trainingTask.setModifyTime(new Date());
		}

        trainingTask.setCurrentRoundNum(0);
        trainingTask.setStatus(TrainingTaskStatusEnum.running);

		TrainingTaskDO data = TrainingTaskConverter.vo2DO(trainingTask);
		Integer n = trainingTaskDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TrainingTask失败");
		AssertUtil.assertNotNull(data.getId(), "新建TrainingTask返回id为空");
		trainingTask.setId(data.getId());

        //创建一个TrainingRoundVO
        createFirstTrainingRound(trainingTask);

        return trainingTask;
	}

    private void createFirstTrainingRound(TrainingTaskVO trainingTask) {
        TrainingRoundVO trainingRound = new TrainingRoundVO();
        trainingRound.setTaskId(trainingTask.getId());
        trainingRound.setRoundNumber(0);
        trainingRound.setStatus(TrainingRoundStatus.pending);
        trainingRound.setTrainingConfig(trainingTask.getTrainingConfig());
        trainingRound.setModelFilePath(trainingTask.getTrainingConfig().getBaseModelPathOrDir());

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        trainingRound.setModelVersion(String.format("%s.%s.base", sdf.format(new Date()), trainingTask.getId()));

        AssertUtil.assertNotNull(trainingTask.getModelScene(), "trainingTask.modelScene is null");
        trainingRound.setModelScene(trainingTask.getModelScene().getCode());

        trainingRound = trainingRoundService.insert(trainingRound);
    }

    @Override
	public void updateByIdSelective(TrainingTaskVO trainingTask) {
		AssertUtil.assertNotNull(trainingTask, ResultCode.PARAM_INVALID, "trainingTask is null");
    	AssertUtil.assertTrue(trainingTask.getId() != null, ResultCode.PARAM_INVALID, "trainingTask.id is null");

		//修改时间必须更新
		trainingTask.setModifyTime(new Date());
		TrainingTaskDO data = TrainingTaskConverter.vo2DO(trainingTask);
		int n = trainingTaskDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TrainingTask失败，影响行数:" + n);
	}

	@Override
	public List<TrainingTaskVO> queryTrainingTaskList(TrainingTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingTaskExample example = TrainingTaskConverter.query2Example(query);

		List<TrainingTaskDO> list = trainingTaskDAO.selectByExample(example);
		return TrainingTaskConverter.doList2VOList(list);
	}

	@Override
	public Long queryTrainingTaskCount(TrainingTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingTaskExample example = TrainingTaskConverter.query2Example(query);
		return trainingTaskDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询训练任务主
	 */
	@Override
	public PageInfo<TrainingTaskVO> queryTrainingTaskByPage(TrainingTaskQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<TrainingTaskVO> page = new PageInfo<>();

		TrainingTaskExample example = TrainingTaskConverter.query2Example(query);
		long totalCount = trainingTaskDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<TrainingTaskDO> list = trainingTaskDAO.selectByExample(example);
		page.setList(TrainingTaskConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public void startTask(Integer taskId) {
		AssertUtil.assertNotNull(taskId, ResultCode.PARAM_INVALID, "taskId is null");
		
		// 更新任务状态为运行中
		TrainingTaskVO task = new TrainingTaskVO();
		task.setId(taskId);
		task.setStatus(TrainingTaskStatusEnum.running);
		task.setModifyTime(new Date());
		
		updateByIdSelective(task);
		
		// TODO: 调用Python服务启动训练
		log.info("启动训练任务: {}", taskId);
	}

	@Override
	public void stopTask(Integer taskId) {
		AssertUtil.assertNotNull(taskId, ResultCode.PARAM_INVALID, "taskId is null");
		
		// 更新任务状态为已停止
		TrainingTaskVO task = new TrainingTaskVO();
		task.setId(taskId);
		task.setStatus(TrainingTaskStatusEnum.stopped);
		task.setModifyTime(new Date());
		
		updateByIdSelective(task);
		
		// TODO: 调用Python服务停止训练
		log.info("停止训练任务: {}", taskId);
	}

	@Override
	public TaskProgressVO getTaskProgress(Integer taskId) {
		AssertUtil.assertNotNull(taskId, ResultCode.PARAM_INVALID, "taskId is null");
		
		// 获取任务信息
		TrainingTaskVO task = selectById(taskId);
		AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "训练任务不存在");
		
		// 构造进度信息
		TaskProgressVO progress = new TaskProgressVO();
		progress.setTaskId(taskId);
		progress.setStatus(task.getStatus() != null ? task.getStatus().getCode() : null);
		progress.setCurrentRound(task.getCurrentRoundNum());
		progress.setTotalSamples(task.getTrainingConfig().getSamplesNum());
		
		// TODO: 从数据库查询实际的标注进度
		progress.setLabeledSamples(0);
		progress.setProgress(0.0);
		
		// TODO: 查询轮次信息
		progress.setRounds(new ArrayList<>());
		
		return progress;
	}
}
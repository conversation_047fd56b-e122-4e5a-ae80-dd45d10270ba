package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ComfyuiTaskDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiTaskExample;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.model.query.ComfyuiTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * ComfyuiTaskConverter
 *
 * @version ComfyuiTaskService.java v 0.1 2024-05-30 04:05:20
 */
public class ComfyuiTaskConverter {

    /**
     * DO -> VO
     */
    public static ComfyuiTaskVO do2VO(ComfyuiTaskDO from) {
        ComfyuiTaskVO to = new ComfyuiTaskVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(ComfyuiTaskTypeEnum.getByCode(from.getTaskType()));
        to.setTaskStatus(QueueResult.QueueCodeEnum.valueOf(from.getTaskStatus()));
        to.setReqParams(CommonUtil.parseObject(from.getReqParams()));
        to.setPromptId(from.getPromptId());
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComfyuiRequest(from.getComfyuiRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ComfyuiTaskDO vo2DO(ComfyuiTaskVO from) {
        ComfyuiTaskDO to = new ComfyuiTaskDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        if (from.getTaskType() != null) {
            to.setTaskType(from.getTaskType().getCode());
        }
        if (from.getTaskStatus() != null) {
            to.setTaskStatus(from.getTaskStatus().name());
        }
        to.setReqParams(CommonUtil.toJSONString(from.getReqParams()));
        to.setPromptId(from.getPromptId());
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComfyuiRequest(from.getComfyuiRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * DO -> Query
     */
    public static ComfyuiTaskQuery do2Query(ComfyuiTaskDO from) {
        ComfyuiTaskQuery to = new ComfyuiTaskQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setReqParams(from.getReqParams());
        to.setPromptId(from.getPromptId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComfyuiRequest(from.getComfyuiRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * Query -> DO
     */
    public static ComfyuiTaskDO query2DO(ComfyuiTaskQuery from) {
        ComfyuiTaskDO to = new ComfyuiTaskDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setTaskType(from.getTaskType());
        to.setTaskStatus(from.getTaskStatus());
        to.setReqParams(from.getReqParams());
        to.setPromptId(from.getPromptId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setComfyuiRequest(from.getComfyuiRequest());
        to.setRetDetail(from.getRetDetail());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ComfyuiTaskExample query2Example(ComfyuiTaskQuery from) {
        ComfyuiTaskExample to = new ComfyuiTaskExample();
        ComfyuiTaskExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getTaskType())) {
            c.andTaskTypeEqualTo(from.getTaskType());
        }
        if (!ObjectUtils.isEmpty(from.getTaskStatus())) {
            c.andTaskStatusEqualTo(from.getTaskStatus());
        }
        if (!ObjectUtils.isEmpty(from.getReqParams())) {
            c.andReqParamsEqualTo(from.getReqParams());
        }
        if (!ObjectUtils.isEmpty(from.getPromptId())) {
            c.andPromptIdEqualTo(from.getPromptId());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getTaskTypeList())) {
            c.andTaskTypeIn(from.getTaskTypeList());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ComfyuiTaskVO> doList2VOList(List<ComfyuiTaskDO> list) {
        return CommonUtil.listConverter(list, ComfyuiTaskConverter::do2VO);
    }
}
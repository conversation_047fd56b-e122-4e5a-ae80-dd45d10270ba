package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.aliyun.model.ImageOperateOutputModal;
import ai.conrain.aigc.platform.service.model.vo.ImageOperateVO;

/**
 * 图片处理接口
 */
public interface ImageOperateService {

    /**
     * 图片擦除
     * @param imageOperateVO 图片处理参数
     * @param serverType     擦除服务类型
     * @return taskId（任务 Id）
     */
    String imageErase(ImageOperateVO imageOperateVO, String serverType);

    /**
     * 图片擦除查询
     * @param taskId     擦除任务 Id
     * @param serverType 擦除服务类型
     * @return 图片处理结果
     */
    ImageOperateOutputModal imageEraseQuery(String taskId, String serverType);
}

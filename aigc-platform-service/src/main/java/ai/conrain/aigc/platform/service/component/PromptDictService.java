package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import java.util.List;

/**
 * prompt关键字字典 Service定义
 *
 * <AUTHOR>
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
public interface PromptDictService extends CachedService<PromptDictVO, String, String> {

    /**
     * 查询prompt关键字字典对象
     *
     * @param id 主键
     * @return 返回结果
     */
    PromptDictVO selectByIdFromDB(Integer id);

    /**
     * 删除prompt关键字字典对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加prompt关键字字典对象
     *
     * @param promptDict 对象参数
     * @return 返回结果
     */
    PromptDictVO insert(PromptDictVO promptDict);

    /**
     * 修改prompt关键字字典对象
     *
     * @param promptDict 对象参数
     */
    void updateById(PromptDictVO promptDict);

    /**
     * 批量添加prompt关键字字典对象
     *
     * @param target 批量对象参数
     */
    void batchInsert(List<PromptDictVO> target);

    /**
     * 查询系统搭配词
     *
     * @return 返回结果
     */
    List<PromptDictVO> querySystemCollocation();

    /**
     * 查询典型场景
     *
     * @return 返回结果
     */
    List<PromptDictVO> querySystemScene();

    /**
     * 查询系统搭配词
     *
     * @return 返回结果
     */
    List<PromptDictVO> queryGarmentList();

    /**
     * 根据类型查询字典对象列表
     *
     * @return 返回结果
     */
    List<PromptDictVO> queryListByType(DictTypeEnum type, ImageCaseTypeEnum imageCaseTypeEnum);

    /**
     * 根据类型查询字典对象列表
     *
     * @return 返回结果
     */
    List<PromptDictVO> queryListByType(DictTypeEnum type, String tagName);

    /**
     * 根据类型和值查询字典对象
     *
     * @param type  类型
     * @param value 值
     * @return 返回结果
     */
    PromptDictVO queryByTypeAndPrompt(DictTypeEnum type, String value);

    /**
     * 根据类型和标签列表查询字典对象
     *
     * @param type 类型
     * @param tags 标签类型
     * @return 返回结果
     */
    List<PromptDictVO> queryByTypeAndTags(DictTypeEnum type, List<DictTagsEnum> tags);

    /**
     * 根据类型和标签列表字典对象
     *
     * @param type    类型
     * @param tagName 标签名称
     * @return 返回结果
     */
    PromptDictVO queryByTypeAndTag(DictTypeEnum type, String tagName);

    /**
     * 根据类型和标签列表字典对象
     *
     * @param type     类型
     * @param tagNames 标签名称列表
     * @return 返回结果
     */
    List<PromptDictVO> queryListByType(DictTypeEnum type, List<String> tagNames);

    /**
     * 根据id列表查询字典对象列表
     *
     * @param ids ids
     * @return 返回结果
     */
    List<PromptDictVO> queryByIds(List<Integer> ids);
}
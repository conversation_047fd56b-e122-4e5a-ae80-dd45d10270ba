package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageNavigationVO;
import ai.conrain.aigc.platform.service.model.vo.ImageRecordVO;
import java.util.List;

public interface ImageRecordService {
	
	/**
	 * 带条件分页查询图像基本信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageRecordVO> queryImageByPage(ImageRecordQuery query);

    /**
     * 带条件分页查询图像基本信息
     *
     * @param query 查询条件
     *              return 分页结果
     */
    ImageNavigationVO queryNavigation(ImageRecordQuery query);

    /**
     * 查询图像标签
     *
     * @param query
     * @return
     */
    List<String> queryImageTags(ImageRecordQuery query);
}
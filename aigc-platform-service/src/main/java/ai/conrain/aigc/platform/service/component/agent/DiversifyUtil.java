package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.model.biz.agent.ProcessedInput;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.util.MapUtil;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.pgvector.PGvector;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DiversifyUtil {

    public static List<StyleImageCandidate> diversifyByMMRWithWindow(List<StyleImageCandidate> candidates,
                                                               ProcessedInput input, Double lambda, int windowSize) {

        List<StyleImageCandidate> selectedCandidates = new ArrayList<>();
        List<StyleImageCandidate> remainingCandidates = new ArrayList<>(candidates);

        // 目标选择数量：取流派的50%
        int targetSize = candidates.size() / 2;

        while (selectedCandidates.size() < targetSize && !remainingCandidates.isEmpty()) {
            StyleImageCandidate bestCandidate = null;
            double bestMMRScore = Double.NEGATIVE_INFINITY;

            for (StyleImageCandidate candidate : remainingCandidates) {
                // 计算相关性得分
                double relevanceScore = calculateRelevanceScore(candidate, input);

                // 计算滑动窗口内的多样性惩罚
                double diversityPenalty = calculateDiversityPenaltyWithWindow(candidate, selectedCandidates, input,
                        windowSize);

                // 计算MMR得分
                double mmrScore = lambda * relevanceScore - (1 - lambda) * diversityPenalty;

                if (mmrScore > bestMMRScore) {
                    bestMMRScore = mmrScore;
                    bestCandidate = candidate;
                }
            }

            if (bestCandidate != null) {
                selectedCandidates.add(bestCandidate);
                remainingCandidates.remove(bestCandidate);
            }
        }

        return selectedCandidates;
    }

    /**
     * 计算相关性得分
     */
    private static double calculateRelevanceScore(StyleImageCandidate candidate, ProcessedInput input) {

        // 相关性 = 款式相似度×0.5 + 参考图相关性×0.5
        double styleRelevance = candidate.getStyleSimilarity();

        double referenceRelevance = 0.0;
        double totalWeight = 0.0;
        double weightedSum = 0.0;

        if (MapUtil.isNotEmpty(input.getAverageDimensionVectors())) {
            for (Map.Entry<String, PGvector> entry : input.getAverageDimensionVectors().entrySet()) {
                String dimensionName = entry.getKey();
                PGvector averageVector = entry.getValue();

                double weight = input.getDynamicWeights().getOrDefault(dimensionName, 0.0);
                if (weight > 0) {
                    PGvector candidateVector = candidate.getDimensionVectors().get(dimensionName);
                    if (candidateVector != null) {
                        double similarity = VectorUtil.innerProduct(candidateVector, averageVector);
                        weightedSum += weight * similarity;
                        totalWeight += weight;
                    }
                }
            }
        }

        if (totalWeight > 0) {
            referenceRelevance = weightedSum / totalWeight;
        }

        return 0.5 * styleRelevance + 0.5 * referenceRelevance;
    }

    /**
     * 计算滑动窗口内的多样性惩罚
     *
     * @param candidate          当前候选项
     * @param selectedCandidates 已选择的候选项列表
     * @param input              处理后的输入
     * @param windowSize         滑动窗口大小
     * @return 多样性惩罚值
     */
    private static double calculateDiversityPenaltyWithWindow(StyleImageCandidate candidate,
                                                       List<StyleImageCandidate> selectedCandidates, ProcessedInput input, int windowSize) {
        if (selectedCandidates.isEmpty()) {
            return 0.0;
        }

        // 确定滑动窗口的范围：取最近选择的windowSize个候选项
        int startIndex = Math.max(0, selectedCandidates.size() - windowSize);
        List<StyleImageCandidate> windowCandidates = selectedCandidates.subList(startIndex, selectedCandidates.size());

        double maxSimilarity = 0.0;

        // 只与窗口内的候选项计算相似度
        for (StyleImageCandidate selected : windowCandidates) {
            double similarity = calculateComprehensiveSimilarity(candidate, selected, input);
            maxSimilarity = Math.max(maxSimilarity, similarity);
        }

        return maxSimilarity;
    }

    private static double calculateComprehensiveSimilarity(StyleImageCandidate candidate1, StyleImageCandidate candidate2,
                                                    ProcessedInput input) {
        // 计算综合相似度
        double auxSimilarity = VectorUtil.innerProduct(
                candidate1.getImageCaption().getClothTextEmb(),
                candidate2.getImageCaption().getClothTextEmb());

        double totalWeight = 0.5; // 服装辅助特征权重
        double weightedSum = auxSimilarity * 0.5;

        // 使用预计算的平均向量优化计算参考图各维度的相似度
        if (MapUtil.isNotEmpty(input.getAverageDimensionVectors())) {
            for (Map.Entry<String, PGvector> entry : input.getAverageDimensionVectors().entrySet()) {
                String dimensionName = entry.getKey();
                double weight = input.getDynamicWeights().getOrDefault(dimensionName, 0.0);

                if (weight > 0) {
                    PGvector vec1 = candidate1.getDimensionVectors().get(dimensionName);
                    PGvector vec2 = candidate2.getDimensionVectors().get(dimensionName);

                    if (vec1 != null && vec2 != null) {
                        double similarity = VectorUtil.innerProduct(vec1, vec2);
                        weightedSum += weight * similarity;
                        totalWeight += weight;
                    }
                }
            }
        }

        return totalWeight > 0 ? weightedSum / totalWeight : 0.0;
    }
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 导入测试用例集请求
 *
 * <AUTHOR>
 * @version : ImportTestCaseRequest.java, v 0.1 2025/8/12 19:50 renxiao.wu Exp $
 */
@Data
public class ImportTestCaseRequest implements Serializable {
    private static final long serialVersionUID = 8223074208428369552L;
    /** 用例名称 */
    @NotBlank
    private String name;
    /** 用例类型 */
    @NotBlank
    private String type;
    /** excel文件 */
    @NotEmpty
    private MultipartFile file;
}

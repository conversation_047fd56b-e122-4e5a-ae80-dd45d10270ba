package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.SearchTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 搜索记录历史 Service定义
 *
 * <AUTHOR>
 * @version SearchTaskService.java
 */
public interface SearchTaskService {
	
	/**
	 * 查询搜索记录历史对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	SearchTaskVO selectById(Integer id);

	/**
	 * 删除搜索记录历史对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加搜索记录历史对象
	 * @param searchTask 对象参数
	 * @return 返回结果
	 */
	SearchTaskVO insert(SearchTaskVO searchTask);

	/**
	 * 修改搜索记录历史对象
	 * @param searchTask 对象参数
	 */
	void updateByIdSelective(SearchTaskVO searchTask);

	/**
	 * 带条件批量查询搜索记录历史列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<SearchTaskVO> querySearchTaskList(SearchTaskQuery query);

	/**
	 * 带条件查询搜索记录历史数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long querySearchTaskCount(SearchTaskQuery query);

	/**
	 * 带条件分页查询搜索记录历史
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<SearchTaskVO> querySearchTaskByPage(SearchTaskQuery query);
}
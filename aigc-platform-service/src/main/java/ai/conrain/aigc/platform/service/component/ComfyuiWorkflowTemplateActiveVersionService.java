package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateActiveVersionVO;
import java.util.List;

/**
 * comfyui模板激活版本 Service定义
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
public interface ComfyuiWorkflowTemplateActiveVersionService {

    /**
     * 查询comfyui模板激活版本对象
     *
     * @param id 主键
     * @return 返回结果
     */
    ComfyuiWorkflowTemplateActiveVersionVO selectById(Integer id);

    /**
     * 删除comfyui模板激活版本对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加comfyui模板激活版本对象
     *
     * @param comfyuiWorkflowTemplateActiveVersion 对象参数
     * @return 返回结果
     */
    ComfyuiWorkflowTemplateActiveVersionVO insert(
        ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion);

    /**
     * 修改comfyui模板激活版本对象
     *
     * @param comfyuiWorkflowTemplateActiveVersion 对象参数
     */
    void updateById(ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion);

    /**
     * 全量查询
     * return 结果
     */
    List<ComfyuiWorkflowTemplateActiveVersionVO> findAll();

    /**
     * 根据key批量查询激活版本
     *
     * @param templateKeys 查询参数
     * @return 列表结果
     */
    List<ComfyuiWorkflowTemplateActiveVersionVO> batchQueryByTemplateKey(List<String> templateKeys);

    /**
     * 根据key查询激活版本
     *
     * @param templateKey 模板key
     * @return 列表结果
     */
    ComfyuiWorkflowTemplateActiveVersionVO queryByTemplateKey(String templateKey);
}
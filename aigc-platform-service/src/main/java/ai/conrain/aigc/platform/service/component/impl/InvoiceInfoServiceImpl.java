package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.InvoiceInfoDAO;
import ai.conrain.aigc.platform.dal.entity.InvoiceInfoDO;
import ai.conrain.aigc.platform.dal.example.InvoiceInfoExample;
import ai.conrain.aigc.platform.integration.invoice.FPApiService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.InvoiceInfoService;
import ai.conrain.aigc.platform.service.component.InvoiceOrderService;
import ai.conrain.aigc.platform.service.component.InvoiceTitleService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.enums.InvoiceStatus;
import ai.conrain.aigc.platform.service.enums.OrderStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.InvoiceInfoConverter;
import ai.conrain.aigc.platform.service.model.query.InvoiceInfoQuery;
import ai.conrain.aigc.platform.service.model.query.InvoiceOrderQuery;
import ai.conrain.aigc.platform.service.model.query.InvoiceTitleQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceOrderVO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**   
 * InvoiceInfoService实现
 *
 * <AUTHOR>
 * @version InvoiceInfoService.java v 0.1 2024-06-27 01:42:09
 */
@Slf4j
@Service
public class InvoiceInfoServiceImpl implements InvoiceInfoService {

	/** DAO */
	@Autowired
	private InvoiceInfoDAO invoiceInfoDAO;

	@Autowired
	private InvoiceOrderService invoiceOrderService;

	@Autowired
	private InvoiceTitleService invoiceTitleService;

	@Autowired
	private OrderInfoService orderInfoService;

	@Autowired
	private FPApiService fpApiService;

	//税率1%，写死
	private static final BigDecimal TAX_RATE = new BigDecimal("0.01");

	@Override
	public InvoiceInfoVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		InvoiceInfoDO data = invoiceInfoDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return InvoiceInfoConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = invoiceInfoDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除InvoiceInfo失败");
	}

	@Override
	public InvoiceInfoVO insert(InvoiceInfoVO invoiceInfo) {
		AssertUtil.assertNotNull(invoiceInfo, ResultCode.PARAM_INVALID, "invoiceInfo is null");
		AssertUtil.assertTrue(invoiceInfo.getId() == null, ResultCode.PARAM_INVALID, "invoiceInfo.id is present");

		//创建时间、修改时间兜底
		if (invoiceInfo.getCreateTime() == null) {
			invoiceInfo.setCreateTime(new Date());
		}

		if (invoiceInfo.getModifyTime() == null) {
			invoiceInfo.setModifyTime(new Date());
		}

		InvoiceInfoDO data = InvoiceInfoConverter.vo2DO(invoiceInfo);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = invoiceInfoDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建InvoiceInfo失败");
		AssertUtil.assertNotNull(data.getId(), "新建InvoiceInfo返回id为空");
		invoiceInfo.setId(data.getId());
		return invoiceInfo;
	}

	/**
	 * 申请冲销发票
	 *
	 * @param id
	 */
	@Override
	public void applyReverseInvoice(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
		InvoiceInfoVO invoiceInfoVO = this.selectById(id);
		AssertUtil.assertNotNull(invoiceInfoVO, ResultCode.PARAM_INVALID, "发票不存在");
		AssertUtil.assertTrue(invoiceInfoVO.getStatus().equals(InvoiceStatus.INVOICE_END.getCode()), ResultCode.BIZ_FAIL, "当前发票状态不可冲退");

		invoiceInfoVO.setStatus(InvoiceStatus.INVOICE_REVERSEING.getCode());
		AssertUtil.assertNotBlank(invoiceInfoVO.getInvoiceNo(), ResultCode.PARAM_INVALID, "发票号为空不可冲退，历史脏数据");
		AssertUtil.assertNotNull(invoiceInfoVO.getFinishTime(), ResultCode.PARAM_INVALID, "发票完成时间为空，历史脏数据");

		String taskId = fpApiService.offsetInvoice(invoiceInfoVO.getInvoiceNo(), DateUtils.formatSimpleDate(invoiceInfoVO.getFinishTime()));
		AssertUtil.assertNotBlank(taskId, ResultCode.BIZ_FAIL, "发票冲退失败");

		//冲退发票记录
		JSONObject negativeInvoiceDetail = new JSONObject();
		negativeInvoiceDetail.put("negativeInvoiceTaskId", taskId);
		negativeInvoiceDetail.put("negativeInvoiceApplyTime", DateUtils.formatTime(new Date()));

		invoiceInfoVO.setNegativeInvoiceDetail(negativeInvoiceDetail.toJSONString());

		this.updateByIdSelective(invoiceInfoVO);
	}

	/**
	 * 申请发票
	 */
	@Override
	@Transactional(rollbackFor = Throwable.class)
	public Integer applyInvoice(InvoiceTitleVO invoiceTitle, Integer orderId) {

		//检查当前订单是否已经开票，不可重复开票
		AssertUtil.assertNotNull(invoiceTitle, ResultCode.PARAM_INVALID, "invoiceTitle is null");
		AssertUtil.assertNotNull(orderId, ResultCode.PARAM_INVALID, "orderId is null");
		OrderInfoVO order = orderInfoService.lockById(orderId);
		AssertUtil.assertTrue(order != null && order.getOrderStatus().equals(OrderStatusEnum.TRADE_FINISHED.getCode()), ResultCode.BIZ_FAIL, "订单不存在或状态异常");

		//校验发票抬头和统一社会信用代码中不可包含空格字符
		AssertUtil.assertTrue(!invoiceTitle.getSubjectName().contains(" "), ResultCode.PARAM_INVALID, "发票抬头不能包含空格");
		AssertUtil.assertTrue(!invoiceTitle.getCreditCode().contains(" "), ResultCode.PARAM_INVALID, "统一社会信用代码不能包含空格");

		InvoiceInfoVO exist = this.queryInvoiceInfoByOrderId(orderId);
		if (exist != null) {
			log.info("当前订单已开票，直接跳过，{}", orderId);
			return exist.getId();
		}

		//记录开票流水
		InvoiceInfoVO invoice = getInvoiceInfoVO(invoiceTitle, order);
		invoice = this.insert(invoice);

		//记录订单开票关联关系流水
		InvoiceOrderVO invoiceOrder = getInvoiceOrderVO(orderId, invoice);
		invoiceOrder = invoiceOrderService.insert(invoiceOrder);

		//如果用户名下没有关联的抬头，则记录或更新开票抬头
		List<InvoiceTitleVO> titles = getExistedInvoiceTitles(invoiceTitle);
		if (CollectionUtils.isEmpty(titles)) {
			invoiceTitleService.insert(invoiceTitle);
		} else {
			invoiceTitle.setId(titles.get(0).getId());
			invoiceTitleService.updateByIdSelective(invoiceTitle);
		}

		//请求三方开票服务
		if (EnvUtil.isProdEnv()) {
			createInvoiceTask(invoiceTitle, invoice, order);
		}

		return invoice.getId();
	}

	@Override
	public void retry(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
		InvoiceInfoVO invoiceInfoVO = this.selectById(id);
		AssertUtil.assertNotNull(invoiceInfoVO, ResultCode.PARAM_INVALID, "发票不存在");
		AssertUtil.assertTrue(invoiceInfoVO.getStatus().equals(InvoiceStatus.INVOICING.getCode()), "当前发票状态不是进行中，不可重试");

		//请求三方开票服务
		if (EnvUtil.isProdEnv()) {
			InvoiceInfoVO target = new InvoiceInfoVO();
			target.setId(id);

			String newInnerInvoiceNo = 	EnvUtil.getEnv() + "_" + CommonUtil.uuid().substring(0, 10);

			String thirdTaskId = fpApiService.createInvoiceTask(newInnerInvoiceNo, getBuyerType(invoiceInfoVO.getSubjectType()), invoiceInfoVO.getSubjectName(),
					invoiceInfoVO.getCreditCode(), BigDecimalUtils.formatWith2DigitsIfExist(invoiceInfoVO.getAmountWithTax()));

			target.setInnerInvoiceNo(newInnerInvoiceNo);

			//请求三方开票服务成功
			if (StringUtils.isNotBlank(thirdTaskId)) {
				log.info("重试请求三方开票服务成功，thirdTaskId:{}", thirdTaskId);

				//更新三方开票任务信息
				target.setInvoiceTaskThirdPlatform("FPAPI");
				target.setInvoiceTaskThirdReqId(thirdTaskId);
				this.updateByIdSelective(target);

			} else {
				log.info("重试请求三方开票服务失败");
				throw new BizException("重试请求三方开票服务失败");
			}
		}

	}

	private List<InvoiceTitleVO> getExistedInvoiceTitles(InvoiceTitleVO invoiceTitle) {
		InvoiceTitleQuery titleQuery = new InvoiceTitleQuery();
		titleQuery.setMasterUserId(OperationContextHolder.getMasterUserId());
		titleQuery.setSubjectType(invoiceTitle.getSubjectType());
		titleQuery.setPageNum(1);
		titleQuery.setPageSize(1);

        return invoiceTitleService.queryInvoiceTitleList(titleQuery);
	}

	@NotNull
	private static InvoiceOrderVO getInvoiceOrderVO(Integer orderId, InvoiceInfoVO invoice) {
		InvoiceOrderVO invoiceOrder = new InvoiceOrderVO();
		{
			invoiceOrder.setUserId(OperationContextHolder.getMasterUserId());
			invoiceOrder.setOperatorId(OperationContextHolder.getOperatorUserId());
			invoiceOrder.setInvoiceId(invoice.getId());
			invoiceOrder.setOrderId(orderId);
		}
		return invoiceOrder;
	}

	@NotNull
	private static InvoiceInfoVO getInvoiceInfoVO(InvoiceTitleVO invoiceTitle, OrderInfoVO order) {
		InvoiceInfoVO invoice = new InvoiceInfoVO();
		{
			//内部发票号，用于标识内部唯一性；不是最终真实发票的那个发票代码
			invoice.setInnerInvoiceNo(EnvUtil.getEnv() + "_" + CommonUtil.uuid().substring(0, 10));
			invoice.setMasterUserId(OperationContextHolder.getMasterUserId());
			invoice.setMasterUserNick(OperationContextHolder.getMasterNick());
			invoice.setMasterUserLoginId(OperationContextHolder.getMasterLoginId());

			invoice.setOperatorUserId(OperationContextHolder.getOperatorUserId());
			invoice.setOperatorUserNick(OperationContextHolder.getOperatorNick());
			invoice.setOperatorUserLoginId(OperationContextHolder.getOperatorLoginId());

			invoice.setInvoiceType(invoiceTitle.getInvoiceType());
			invoice.setSubjectType(invoiceTitle.getSubjectType());
			invoice.setSubjectName(invoiceTitle.getSubjectName());
			invoice.setCreditCode(invoiceTitle.getCreditCode());
			invoice.setBusinessAddress(invoiceTitle.getBusinessAddress());
			invoice.setBusinessPhone(invoiceTitle.getBusinessPhone());
			invoice.setBankName(invoiceTitle.getBankName());
			invoice.setBankAccount(invoiceTitle.getBankAccount());

			invoice.setStatus(InvoiceStatus.INVOICING.getCode());
			invoice.setApplyTime(new Date());

			AssertUtil.assertNotNull(order.getPayAmount(), "订单支付金额为空");

			invoice.setAmountWithTax(order.getPayAmount());
			invoice.setTaxRate(TAX_RATE);
			invoice.setTaxAmount(order.getPayAmount().multiply(TAX_RATE));
			invoice.setAmountNoTax(order.getPayAmount().subtract(order.getPayAmount().multiply(TAX_RATE)));
		}
		return invoice;
	}

	private void createInvoiceTask(InvoiceTitleVO invoiceTitle, InvoiceInfoVO invoice, OrderInfoVO order) {
		String thirdTaskId = fpApiService.createInvoiceTask(invoice.getInnerInvoiceNo(), getBuyerType(invoiceTitle.getSubjectType()), invoiceTitle.getSubjectName(),
					invoiceTitle.getCreditCode(), BigDecimalUtils.formatWith2DigitsIfExist(order.getPayAmount()));

		//请求三方开票服务成功
		if (StringUtils.isNotBlank(thirdTaskId)) {
			log.info("请求三方开票服务成功，thirdTaskId:{}", thirdTaskId);

			//更新三方开票任务信息
			invoice.setInvoiceTaskThirdPlatform("FPAPI");
			invoice.setInvoiceTaskThirdReqId(thirdTaskId);
			this.updateByIdSelective(invoice);

			//请求三方开票服务失败
		} else {
			//用户侧无感，避免业务中断
			log.error("请求三方开票服务失败，需要人工介入invoice.id:{}", invoice.getId());

			//钉钉消息报警，圈松然
			DingTalkNoticeHelper.sendMsg2DevGroup(String.format("用户申请发票，三方开票服务异常\n用户：%s\n金额：%s元\n详情：%s",
					OperationContextHolder.getOperatorNick(), BigDecimalUtils.formatWith2DigitsIfExist(order.getPayAmount()),
					EnvUtil.getDomain() + "/#/invoice-mng"), Collections.singletonList("15906660486"));
		}
	}

	private String getBuyerType(String subjectType) {
		if (subjectType.equals("企业")) {
			return "1";
		} else if (subjectType.equals("个人")){
			return "2";
		}

		throw new RuntimeException("[自动开票]购方身份类型只能为1或2");
	}

	@Override
	public void updateByIdSelective(InvoiceInfoVO invoiceInfo) {
		AssertUtil.assertNotNull(invoiceInfo, ResultCode.PARAM_INVALID, "invoiceInfo is null");
    	AssertUtil.assertTrue(invoiceInfo.getId() != null, ResultCode.PARAM_INVALID, "invoiceInfo.id is null");

		InvoiceInfoVO current = this.selectById(invoiceInfo.getId());
		AssertUtil.assertNotNull(current, ResultCode.BIZ_FAIL, "待更新的InvoiceInfo不存在");

		if (!StringUtils.equals(current.getStatus(), InvoiceStatus.INVOICE_END.getCode())
				&& StringUtils.equals(invoiceInfo.getStatus(), InvoiceStatus.INVOICE_END.getCode())) {
			invoiceInfo.setFinishTime(new Date());
		}

		//修改时间必须更新
		invoiceInfo.setModifyTime(new Date());
		InvoiceInfoDO data = InvoiceInfoConverter.vo2DO(invoiceInfo);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = invoiceInfoDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新InvoiceInfo失败，影响行数:" + n);
	}

	@Override
	public InvoiceInfoVO queryInvoiceInfoByOrderId(Integer orderId) {
		InvoiceOrderQuery query = new InvoiceOrderQuery();
		query.setOrderId(orderId);
		query.setPageNum(1);
		query.setPageSize(1);
		List<InvoiceOrderVO> list = invoiceOrderService.queryInvoiceOrderList(query);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		InvoiceOrderVO invoiceOrderVO = list.get(0);
		AssertUtil.assertNotNull(invoiceOrderVO.getInvoiceId(), ResultCode.BIZ_FAIL, "发票id为空");

		return this.selectById(invoiceOrderVO.getInvoiceId());
	}

	@Override
	public List<InvoiceInfoVO> queryInvoiceInfoList(InvoiceInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceInfoExample example = InvoiceInfoConverter.query2Example(query);

		List<InvoiceInfoDO> list = invoiceInfoDAO.selectByExample(example);
			return InvoiceInfoConverter.doList2VOList(list);
	}

	@Override
	public Long queryInvoiceInfoCount(InvoiceInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceInfoExample example = InvoiceInfoConverter.query2Example(query);
		long c = invoiceInfoDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询发票记录
	 */
	@Override
	public PageInfo<InvoiceInfoVO> queryInvoiceInfoByPage(InvoiceInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<InvoiceInfoVO> page = new PageInfo<>();

		InvoiceInfoExample example = InvoiceInfoConverter.query2Example(query);
		long totalCount = invoiceInfoDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<InvoiceInfoDO> list = invoiceInfoDAO.selectByExample(example);
		page.setList(InvoiceInfoConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
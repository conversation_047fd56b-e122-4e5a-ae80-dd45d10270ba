package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingTaskDO;
import ai.conrain.aigc.platform.service.model.query.TrainingTaskQuery;
import ai.conrain.aigc.platform.dal.example.TrainingTaskExample;
import ai.conrain.aigc.platform.service.model.vo.ModelSceneEnum;
import ai.conrain.aigc.platform.service.model.vo.TrainingTaskVO;
import ai.conrain.aigc.platform.service.enums.TrainingWayEnum;
import ai.conrain.aigc.platform.service.enums.TrainingTaskStatusEnum;

import ai.conrain.aigc.platform.service.model.vo.training.TrainingConfig;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * TrainingTaskConverter
 *
 * @version TrainingTaskService.java
 */
public class TrainingTaskConverter {

    /**
     * DO -> VO
     */
    public static TrainingTaskVO do2VO(TrainingTaskDO from) {
        TrainingTaskVO to = new TrainingTaskVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setDescription(from.getDescription());
        if (from.getModelScene() != null) {
            to.setModelScene(ModelSceneEnum.getByCode(from.getModelScene()));
        }
        to.setTrainingConfig(CommonUtil.parseObject(from.getTrainingConfig(), TrainingConfig.class));
        to.setStatus(TrainingTaskStatusEnum.getByCode(from.getStatus()));
        to.setCurrentRoundNum(from.getCurrentRoundNum());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TrainingTaskDO vo2DO(TrainingTaskVO from) {
        TrainingTaskDO to = new TrainingTaskDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setDescription(from.getDescription());
        to.setModelScene(from.getModelScene() != null ? from.getModelScene().getCode() : null);
        to.setTrainingConfig(CommonUtil.toJSONString(from.getTrainingConfig()));
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setCurrentRoundNum(from.getCurrentRoundNum());
        String extInfo = from.getExtInfo();
        to.setExtInfo(StringUtils.isBlank(extInfo) ? null : extInfo);
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TrainingTaskExample query2Example(TrainingTaskQuery from) {
        TrainingTaskExample to = new TrainingTaskExample();
        TrainingTaskExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getDescription())) {
            c.andDescriptionEqualTo(from.getDescription());
        }
        if (!ObjectUtils.isEmpty(from.getModelScene())) {
            c.andModelSceneEqualTo(from.getModelScene());
        }
        if (!ObjectUtils.isEmpty(from.getTrainingConfig())) {
            c.andTrainingConfigEqualTo(from.getTrainingConfig());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getCurrentRoundNum())) {
            c.andCurrentRoundNumEqualTo(from.getCurrentRoundNum());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<TrainingTaskVO> doList2VOList(List<TrainingTaskDO> from) {
        List<TrainingTaskVO> to = new ArrayList<>();
        from.forEach(f -> to.add(do2VO(f)));
        return to;
    }
}
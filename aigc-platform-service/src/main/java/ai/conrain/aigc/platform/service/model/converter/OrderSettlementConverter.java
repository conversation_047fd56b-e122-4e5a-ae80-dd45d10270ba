package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.OrderSettlementDO;
import ai.conrain.aigc.platform.service.enums.OrderSettleStatusEnum;
import ai.conrain.aigc.platform.service.enums.OrderSettlementTypeEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.model.query.OrderSettlementQuery;
import ai.conrain.aigc.platform.dal.example.OrderSettlementExample;
import ai.conrain.aigc.platform.service.model.vo.OrderSettlementVO;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * OrderSettlementConverter
 *
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
public class OrderSettlementConverter {

    /**
     * DO -> VO
     */
    public static OrderSettlementVO do2VO(OrderSettlementDO from) {
        OrderSettlementVO to = new OrderSettlementVO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setType(OrderSettlementTypeEnum.getByCode(from.getType()));
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setPrincipalType(PrincipalTypeEnum.getByCode(from.getPrincipalType()));
        to.setPrincipalId(from.getPrincipalId());
        to.setStatus(OrderSettleStatusEnum.getByCode(from.getStatus()));
        to.setMerchantCorpId(from.getMerchantCorpId());
        to.setMerchantName(from.getMerchantName());
        to.setMerchantId(from.getMerchantId());
        to.setMerchantCorpName(from.getMerchantCorpName());
        to.setOrderFinishTime(from.getOrderFinishTime());

        to.setSettleId(from.getSettleId());
        to.setSettleTime(from.getSettleTime());
        to.setTotalAmount(from.getTotalAmount());
        to.setChannelRate(from.getChannelRate());
        to.setSettleAmount(from.getSettleAmount());
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static OrderSettlementDO vo2DO(OrderSettlementVO from) {
        OrderSettlementDO to = new OrderSettlementDO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setType(from.getType().getCode());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setPrincipalType(from.getPrincipalType().getCode());
        to.setPrincipalId(from.getPrincipalId());
        to.setStatus(from.getStatus().getCode());
        to.setSettleId(from.getSettleId());
        to.setSettleTime(from.getSettleTime());
        to.setTotalAmount(from.getTotalAmount());
        to.setChannelRate(from.getChannelRate());
        to.setSettleAmount(from.getSettleAmount());
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static OrderSettlementQuery do2Query(OrderSettlementDO from) {
        OrderSettlementQuery to = new OrderSettlementQuery();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setType(from.getType());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setStatus(from.getStatus());
        to.setSettleId(from.getSettleId());
        to.setSettleTime(from.getSettleTime());
        to.setTotalAmount(from.getTotalAmount());
        to.setChannelRate(from.getChannelRate());
        to.setSettleAmount(from.getSettleAmount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static OrderSettlementDO query2DO(OrderSettlementQuery from) {
        OrderSettlementDO to = new OrderSettlementDO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setType(from.getType());
        to.setDistributorCorpId(from.getDistributorCorpId());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setStatus(from.getStatus());
        to.setSettleId(from.getSettleId());
        to.setSettleTime(from.getSettleTime());
        to.setTotalAmount(from.getTotalAmount());
        to.setChannelRate(from.getChannelRate());
        to.setSettleAmount(from.getSettleAmount());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static OrderSettlementExample query2Example(OrderSettlementQuery from) {
        OrderSettlementExample to = new OrderSettlementExample();
        OrderSettlementExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getOrderNo())) {
            c.andOrderNoEqualTo(from.getOrderNo());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpId())) {
            c.andDistributorCorpIdEqualTo(from.getDistributorCorpId());
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpName())) {
            c.andDistributorCorpNameEqualTo(from.getDistributorCorpName());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalType())) {
            c.andPrincipalTypeEqualTo(from.getPrincipalType());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalId())) {
            c.andPrincipalIdEqualTo(from.getPrincipalId());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getStatusNotIn())) {
            c.andStatusNotIn(from.getStatusNotIn());
        }
        if (!ObjectUtils.isEmpty(from.getSettleId())) {
            c.andSettleIdEqualTo(from.getSettleId());
        }
        if (!ObjectUtils.isEmpty(from.getSettleTime())) {
            c.andSettleTimeEqualTo(from.getSettleTime());
        }
        if (!ObjectUtils.isEmpty(from.getTotalAmount())) {
            c.andTotalAmountEqualTo(from.getTotalAmount());
        }
        if (!ObjectUtils.isEmpty(from.getChannelRate())) {
            c.andChannelRateEqualTo(from.getChannelRate());
        }
        if (!ObjectUtils.isEmpty(from.getSettleAmount())) {
            c.andSettleAmountEqualTo(from.getSettleAmount());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<OrderSettlementVO> doList2VOList(List<OrderSettlementDO> list) {
        return CommonUtil.listConverter(list, OrderSettlementConverter::do2VO);
    }
}
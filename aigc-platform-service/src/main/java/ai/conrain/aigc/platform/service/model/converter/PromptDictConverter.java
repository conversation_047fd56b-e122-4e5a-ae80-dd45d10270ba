package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.PromptDictDO;
import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.model.query.PromptDictQuery;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * PromptDictConverter
 *
 * @version PromptDictService.java v 0.1 2024-11-15 07:24:51
 */
public class PromptDictConverter {

    /**
     * DO -> VO
     */
    public static PromptDictVO do2VO(PromptDictDO from) {
        PromptDictVO to = new PromptDictVO();
        to.setId(from.getId());
        to.setWord(from.getWord());
        to.setPrompt(from.getPrompt());
        to.setShowImage(from.getShowImage());
        if (StringUtils.isNotBlank(from.getTags())) {
            to.setTags(Arrays.asList(from.getTags().split(",")));
        }
        to.setMemo(from.getMemo());
        to.setType(DictTypeEnum.getByCode(from.getType()));
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static PromptDictDO vo2DO(PromptDictVO from) {
        PromptDictDO to = new PromptDictDO();
        to.setId(from.getId());
        to.setWord(from.getWord());
        to.setPrompt(from.getPrompt());
        to.setShowImage(from.getShowImage());
        if (CollectionUtils.isNotEmpty(from.getTags())) {
            to.setTags(String.join(",", from.getTags()));
        }
        to.setMemo(from.getMemo());
        to.setType(from.getType().getCode());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static PromptDictQuery do2Query(PromptDictDO from) {
        PromptDictQuery to = new PromptDictQuery();
        to.setId(from.getId());
        to.setWord(from.getWord());
        to.setPrompt(from.getPrompt());
        to.setShowImage(from.getShowImage());
        to.setTags(from.getTags());
        to.setMemo(from.getMemo());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static PromptDictDO query2DO(PromptDictQuery from) {
        PromptDictDO to = new PromptDictDO();
        to.setId(from.getId());
        to.setWord(from.getWord());
        to.setPrompt(from.getPrompt());
        to.setShowImage(from.getShowImage());
        to.setTags(from.getTags());
        to.setMemo(from.getMemo());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<PromptDictVO> doList2VOList(List<PromptDictDO> list) {
        return CommonUtil.listConverter(list, PromptDictConverter::do2VO);
    }

    public static List<PromptDictDO> voList2DOList(List<PromptDictVO> list) {
        return CommonUtil.listConverter(list, PromptDictConverter::vo2DO);
    }
}
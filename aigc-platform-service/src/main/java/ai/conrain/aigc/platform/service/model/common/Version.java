package ai.conrain.aigc.platform.service.model.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本类
 * <p>
 * 支持日期+自增号格式的版本管理，版本格式为：YYYYMMDD.N
 * 例如：20250619.0, 20250619.1, 20250620.0
 * </p>
 * <p>
 * 主要功能：
 * <ul>
 *   <li>版本格式验证</li>
 *   <li>版本比较（支持 Comparable 接口）</li>
 *   <li>版本解析和构建</li>
 *   <li>获取下一个版本号</li>
 *   <li>获取当前日期的初始版本</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-19
 */
@Slf4j
@Data
@AllArgsConstructor
public class Version implements Comparable<Version> {

    /**
     * 版本格式正则表达式：YYYYMMDD.N
     */
    private static final Pattern PATTERN = Pattern.compile("^(\\d{8})\\.(\\d+)$");

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 日期部分（格式：YYYYMMDD）
     */
    private String datePart;

    /**
     * 自增号部分
     */
    private Integer incrementPart;

    /**
     * 完整版本字符串
     */
    private String fullVersion;

    /**
     * 构造函数
     */
    public Version(String datePart, Integer incrementPart) {
        this.datePart = datePart;
        this.incrementPart = incrementPart;
        this.fullVersion = datePart + "." + incrementPart;
    }

    /**
     * 从版本字符串创建 Version 对象
     */
    public Version(String versionString) {
        Version parsed = parse(versionString);
        if (parsed == null) {
            throw new IllegalArgumentException("无效的版本格式: " + versionString);
        }
        this.datePart = parsed.datePart;
        this.incrementPart = parsed.incrementPart;
        this.fullVersion = parsed.fullVersion;
    }

    /**
     * 从日期和自增号创建 Version 对象
     */
    public Version(LocalDate date, int increment) {
        if (date == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        if (increment < 0) {
            throw new IllegalArgumentException("自增号不能为负数");
        }
        this.datePart = date.format(DATE_FORMATTER);
        this.incrementPart = increment;
        this.fullVersion = this.datePart + "." + this.incrementPart;
    }

    @Override
    public int compareTo(Version other) {
        if (other == null) {
            return 1;
        }

        // 首先比较日期部分
        int dateComparison = this.datePart.compareTo(other.datePart);
        if (dateComparison != 0) {
            return dateComparison;
        }

        // 日期相同时比较自增号
        return this.incrementPart.compareTo(other.incrementPart);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {return true;}
        if (obj == null || getClass() != obj.getClass()) {return false;}
        Version version = (Version)obj;
        return Objects.equals(datePart, version.datePart) &&
               Objects.equals(incrementPart, version.incrementPart);
    }

    @Override
    public int hashCode() {
        return Objects.hash(datePart, incrementPart);
    }

    @Override
    public String toString() {
        return fullVersion;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 验证版本格式是否正确
     *
     * @param version 版本字符串
     * @return true 如果格式正确，false 否则
     */
    public static boolean isValid(String version) {
        if (StringUtils.isBlank(version)) {
            return false;
        }

        Matcher matcher = PATTERN.matcher(version.trim());
        return matcher.matches();
    }

    /**
     * 解析版本字符串为 Version 对象
     *
     * @param version 版本字符串
     * @return Version 对象，如果解析失败返回 null
     */
    public static Version parse(String version) {
        if (!isValid(version)) {
            log.warn("无效的版本格式: {}", version);
            return null;
        }

        Matcher matcher = PATTERN.matcher(version.trim());
        if (matcher.matches()) {
            String datePart = matcher.group(1);
            Integer incrementPart = Integer.parseInt(matcher.group(2));
            return new Version(datePart, incrementPart, version.trim());
        }

        return null;
    }

    /**
     * 默认初始的版本
     *
     * @return Version 第0个版本
     */
    public static Version empty() {
        return new Version("00000000.0");
    }

    /**
     * 从版本字符串创建 Version 对象
     *
     * @param version 版本字符串
     * @return Version 对象
     */
    public static Version ofNullable(String version) {
        if (StringUtils.isBlank(version)) {
            return empty();
        }
        return new Version(version);
    }

    /**
     * 从版本字符串创建 Version 对象（抛出异常版本）
     *
     * @param version 版本字符串
     * @return Version 对象
     * @throws IllegalArgumentException 如果版本格式无效
     */
    public static Version of(String version) {
        return new Version(version);
    }

    /**
     * 从日期和自增号创建 Version 对象
     *
     * @param date      日期
     * @param increment 自增号
     * @return Version 对象
     */
    public static Version of(LocalDate date, int increment) {
        return new Version(date, increment);
    }

    /**
     * 从日期字符串和自增号创建 Version 对象
     *
     * @param dateStr   日期字符串（格式：YYYYMMDD）
     * @param increment 自增号
     * @return Version 对象
     */
    public static Version of(String dateStr, int increment) {
        if (StringUtils.isBlank(dateStr)) {
            throw new IllegalArgumentException("日期字符串不能为空");
        }
        if (increment < 0) {
            throw new IllegalArgumentException("自增号不能为负数");
        }

        // 验证日期格式
        try {
            LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("无效的日期格式: " + dateStr);
        }

        return new Version(dateStr, increment);
    }

    /**
     * 获取当前日期的初始版本（自增号为0）
     *
     * @return 当前日期的初始版本
     */
    public static Version currentDateInitial() {
        return new Version(LocalDate.now(), 0);
    }

    /**
     * 获取指定日期的初始版本（自增号为0）
     *
     * @param date 指定日期
     * @return 指定日期的初始版本
     */
    public static Version dateInitial(LocalDate date) {
        return new Version(date, 0);
    }

    // ==================== 静态比较方法 ====================

    /**
     * 比较两个版本字符串
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示 version1 < version2，0 表示相等，正数表示 version1 > version2
     */
    public static int compare(String version1, String version2) {
        Version v1 = parse(version1);
        Version v2 = parse(version2);

        if (v1 == null && v2 == null) {
            return 0;
        }
        if (v1 == null) {
            return -1;
        }
        if (v2 == null) {
            return 1;
        }

        return v1.compareTo(v2);
    }

    /**
     * 判断版本1是否大于版本2
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果版本1大于版本2
     */
    public static boolean isGreaterThan(String version1, String version2) {
        return compare(version1, version2) > 0;
    }

    /**
     * 判断版本1是否小于版本2
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果版本1小于版本2
     */
    public static boolean isLessThan(String version1, String version2) {
        return compare(version1, version2) < 0;
    }

    /**
     * 判断两个版本是否相等
     *
     * @param version1 版本1
     * @param version2 版本2
     * @return true 如果两个版本相等
     */
    public static boolean isEqual(String version1, String version2) {
        return compare(version1, version2) == 0;
    }

    // ==================== 实例方法 ====================

    /**
     * 判断当前版本是否大于另一个版本
     *
     * @param other 另一个版本
     * @return true 如果当前版本大于另一个版本
     */
    public boolean isGreaterThan(Version other) {
        return this.compareTo(other) > 0;
    }

    /**
     * 判断当前版本是否大于另一个版本字符串
     *
     * @param other 另一个版本字符串
     * @return true 如果当前版本大于另一个版本
     */
    public boolean isGreaterThan(String other) {
        Version otherVersion = parse(other);
        return otherVersion != null && this.compareTo(otherVersion) > 0;
    }

    /**
     * 判断当前版本是否小于另一个版本
     *
     * @param other 另一个版本
     * @return true 如果当前版本小于另一个版本
     */
    public boolean isLessThan(Version other) {
        return this.compareTo(other) < 0;
    }

    /**
     * 判断当前版本是否小于另一个版本字符串
     *
     * @param other 另一个版本字符串
     * @return true 如果当前版本小于另一个版本
     */
    public boolean isLessThan(String other) {
        Version otherVersion = parse(other);
        return otherVersion != null && this.compareTo(otherVersion) < 0;
    }

    /**
     * 获取版本对应的日期对象
     *
     * @return LocalDate 对象
     */
    public LocalDate getDate() {
        try {
            return LocalDate.parse(this.datePart, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            log.warn("解析版本日期失败: {}", this.datePart);
            return null;
        }
    }

    /**
     * 判断版本是否为今天的版本
     *
     * @return true 如果是今天的版本
     */
    public boolean isToday() {
        LocalDate versionDate = getDate();
        return versionDate != null && versionDate.equals(LocalDate.now());
    }

    /**
     * 判断版本是否为指定日期的版本
     *
     * @param date 指定日期
     * @return true 如果是指定日期的版本
     */
    public boolean isDate(LocalDate date) {
        if (date == null) {
            return false;
        }
        LocalDate versionDate = getDate();
        return versionDate != null && versionDate.equals(date);
    }

    /**
     * 获取下一个版本号（基于当前日期）
     * 如果是同一天，自增号+1；如果是新的一天，自增号重置为0
     *
     * @return 下一个版本号
     */
    public Version getNext() {
        String currentDate = LocalDate.now().format(DATE_FORMATTER);

        // 如果是同一天，自增号+1
        if (this.datePart.equals(currentDate)) {
            return new Version(currentDate, this.incrementPart + 1);
        } else {
            // 如果是新的一天，自增号重置为0
            return new Version(currentDate, 0);
        }
    }

    /**
     * 获取指定日期的下一个版本号
     *
     * @param targetDate 目标日期
     * @return 下一个版本号
     */
    public Version getNext(LocalDate targetDate) {
        if (targetDate == null) {
            throw new IllegalArgumentException("目标日期不能为空");
        }

        String targetDateStr = targetDate.format(DATE_FORMATTER);

        // 如果是同一天，自增号+1
        if (this.datePart.equals(targetDateStr)) {
            return new Version(targetDateStr, this.incrementPart + 1);
        } else {
            // 如果是不同的日期，自增号重置为0
            return new Version(targetDateStr, 0);
        }
    }

    /**
     * 获取版本的可读描述
     *
     * @return 可读描述，如 "2025年06月19日第1个版本"
     */
    public String getDescription() {
        LocalDate date = getDate();
        if (date == null) {
            return "无效版本";
        }

        return String.format("%d年%02d月%02d日第%d个版本",
            date.getYear(),
            date.getMonthValue(),
            date.getDayOfMonth(),
            this.incrementPart + 1);
    }

    /**
     * 格式化版本显示
     *
     * @return 格式化后的版本显示，如 "2025-06-19 v0"
     */
    public String formatDisplay() {
        LocalDate date = getDate();
        if (date == null) {
            return "无效版本";
        }

        return String.format("%s v%d", date.toString(), this.incrementPart);
    }
}

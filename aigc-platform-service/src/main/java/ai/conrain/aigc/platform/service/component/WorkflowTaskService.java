package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.WorkflowTaskEnum;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
public interface WorkflowTaskService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	WorkflowTaskVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param workflowTask 对象参数
	 * @return 返回结果
	 */
	WorkflowTaskVO insert(WorkflowTaskVO workflowTask);

	/**
	 * 修改对象
	 * @param workflowTask 对象参数
	 */
	void updateByIdSelective(WorkflowTaskVO workflowTask);

	/**
	 * 修改对象
	 * @param workflowTask 对象参数
	 */
	void updateByBizIdSelective(WorkflowTaskVO workflowTask);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<WorkflowTaskVO> queryWorkflowTaskList(WorkflowTaskQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryWorkflowTaskCount(WorkflowTaskQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<WorkflowTaskVO> queryWorkflowTaskByPage(WorkflowTaskQuery query);

	/**
	 * 为模型分配审核员
	 *
	 * @param modelIds 模型列表
	 * @param taskType 任务类型
	 */
	void createReviewTaskFor(List<Integer> modelIds, WorkflowTaskEnum taskType);
}
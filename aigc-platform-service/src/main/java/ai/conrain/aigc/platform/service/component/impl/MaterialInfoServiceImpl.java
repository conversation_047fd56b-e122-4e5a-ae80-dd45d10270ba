package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.MaterialInfoDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialInfoDO;
import ai.conrain.aigc.platform.dal.example.MaterialInfoExample;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.LoraActivateKeys;
import ai.conrain.aigc.platform.service.enums.LabelTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.MaterialInfoConverter;
import ai.conrain.aigc.platform.service.model.query.MaterialInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.LORA_MINI_ACTIVATE_KEYS;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.NO_CUTOUT_MERCHANT_LIST;

/**
 * MaterialInfoService实现
 *
 * <AUTHOR>
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
@Slf4j
@Service
public class MaterialInfoServiceImpl implements MaterialInfoService {

    /** DAO */
    @Autowired
    private MaterialInfoDAO materialInfoDAO;

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public MaterialInfoVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        MaterialInfoDO data = materialInfoDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return MaterialInfoConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = materialInfoDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除MaterialInfo失败");
    }

    @Override
    public MaterialInfoVO insert(MaterialInfoVO materialInfo) {
        AssertUtil.assertNotNull(materialInfo, ResultCode.PARAM_INVALID, "materialInfo is null");
        AssertUtil.assertTrue(materialInfo.getId() == null, ResultCode.PARAM_INVALID, "materialInfo.id is present");

        //创建时间、修改时间兜底
        if (materialInfo.getCreateTime() == null) {
            materialInfo.setCreateTime(new Date());
        }

        if (materialInfo.getModifyTime() == null) {
            materialInfo.setModifyTime(new Date());
        }

        MaterialInfoDO data = MaterialInfoConverter.vo2DO(materialInfo);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        int n = materialInfoDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建MaterialInfo失败");
        AssertUtil.assertNotNull(data.getId(), "新建MaterialInfo返回id为空");
        materialInfo.setId(data.getId());
        return materialInfo;
    }

    @Override
    public void updateByIdSelective(MaterialInfoVO materialInfo) {
        AssertUtil.assertNotNull(materialInfo, ResultCode.PARAM_INVALID, "materialInfo is null");
        AssertUtil.assertTrue(materialInfo.getId() != null, ResultCode.PARAM_INVALID, "materialInfo.id is null");

        //修改时间必须更新
        materialInfo.setModifyTime(new Date());
        MaterialInfoDO data = MaterialInfoConverter.vo2DO(materialInfo);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = materialInfoDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新MaterialInfo失败，影响行数:" + n);
    }

    @Override
    public List<MaterialInfoVO> queryMaterialInfoList(MaterialInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MaterialInfoExample example = MaterialInfoConverter.query2Example(query);

        List<MaterialInfoDO> list = materialInfoDAO.selectByExample(example);
        return MaterialInfoConverter.doList2VOList(list);
    }

    @Override
    public Long queryMaterialInfoCount(MaterialInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        MaterialInfoExample example = MaterialInfoConverter.query2Example(query);
        long c = materialInfoDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询素材流水
     */
    @Override
    public PageInfo<MaterialInfoVO> queryMaterialInfoByPage(MaterialInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<MaterialInfoVO> page = new PageInfo<>();

        MaterialInfoExample example = MaterialInfoConverter.query2Example(query);
        long totalCount = materialInfoDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<MaterialInfoDO> list = materialInfoDAO.selectByExample(example);
        page.setList(MaterialInfoConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<MaterialInfoVO> batchQueryByIds(List<Integer> ids) {
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(ids), ResultCode.PARAM_INVALID, "ids is empty");

        MaterialInfoExample example = new MaterialInfoExample();
        example.createCriteria().andLogicalDeleted(false).andIdIn(ids);
        return MaterialInfoConverter.doList2VOList(materialInfoDAO.selectByExampleWithBLOBs(example));
    }

    @Override
    public String buildActivateKey(String materialType, String labelType, boolean noCutout) {
        LabelTypeEnum labelTypeEnum = LabelTypeEnum.getByCode(labelType);
        if (labelTypeEnum == LabelTypeEnum.MINI) {
            return systemConfigService.queryValueByKey(LORA_MINI_ACTIVATE_KEYS);
        }
        boolean isNew = systemConfigService.queryBoolValue(NO_CUTOUT_MERCHANT_LIST, false) || noCutout;
        return LoraActivateKeys.getActivateKey(materialType, isNew);
    }

}
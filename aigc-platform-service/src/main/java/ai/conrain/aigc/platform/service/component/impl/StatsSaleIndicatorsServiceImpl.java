package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.StatsSaleIndicatorsDAO;
import ai.conrain.aigc.platform.dal.entity.StatsSaleIndicatorsDO;
import ai.conrain.aigc.platform.dal.example.StatsSaleIndicatorsExample;
import ai.conrain.aigc.platform.service.component.CreativeElementService;
import ai.conrain.aigc.platform.service.component.DistributorCustomerService;
import ai.conrain.aigc.platform.service.component.StatsSaleIndicatorsService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.StatsSaleIndicatorsConverter;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.query.StatsSaleIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.constants.CommonConstants;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * StatsSaleIndicatorsService实现
 *
 * <AUTHOR>
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
@Slf4j
@Service
public class StatsSaleIndicatorsServiceImpl implements StatsSaleIndicatorsService {


    @Autowired
    private UserService userService;

    /** DAO */
    @Autowired
    private StatsSaleIndicatorsDAO statsSaleIndicatorsDAO;

    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private DistributorCustomerService distributorCustomerService;

    @Override
    public StatsSaleIndicatorsVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsSaleIndicatorsDO data = statsSaleIndicatorsDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsSaleIndicatorsConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsSaleIndicatorsDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsSaleIndicators失败");
    }

    @Override
    public StatsSaleIndicatorsVO insert(StatsSaleIndicatorsVO statsSaleIndicators) {
        AssertUtil.assertNotNull(statsSaleIndicators, ResultCode.PARAM_INVALID, "statsSaleIndicators is null");
        AssertUtil.assertTrue(statsSaleIndicators.getId() == null, ResultCode.PARAM_INVALID, "statsSaleIndicators.id is present");

        //创建时间、修改时间兜底
        if (statsSaleIndicators.getCreateTime() == null) {
            statsSaleIndicators.setCreateTime(new Date());
        }

        if (statsSaleIndicators.getModifyTime() == null) {
            statsSaleIndicators.setModifyTime(new Date());
        }

        StatsSaleIndicatorsDO data = StatsSaleIndicatorsConverter.vo2DO(statsSaleIndicators);
        Integer n = statsSaleIndicatorsDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsSaleIndicators失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsSaleIndicators返回id为空");
        statsSaleIndicators.setId(data.getId());
        return statsSaleIndicators;
    }


    @Override
    public void updateByIdSelective(StatsSaleIndicatorsVO statsSaleIndicators) {
        AssertUtil.assertNotNull(statsSaleIndicators, ResultCode.PARAM_INVALID, "statsSaleIndicators is null");
        AssertUtil.assertTrue(statsSaleIndicators.getId() != null, ResultCode.PARAM_INVALID, "statsSaleIndicators.id is null");

        //修改时间必须更新
        statsSaleIndicators.setModifyTime(new Date());
        StatsSaleIndicatorsDO data = StatsSaleIndicatorsConverter.vo2DO(statsSaleIndicators);
        int n = statsSaleIndicatorsDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsSaleIndicators失败，影响行数:" + n);
    }

    @Override
    public List<StatsSaleIndicatorsVO> queryStatsSaleIndicatorsList(StatsSaleIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsSaleIndicatorsExample example = StatsSaleIndicatorsConverter.query2Example(query);

        List<StatsSaleIndicatorsDO> list = statsSaleIndicatorsDAO.selectByExample(example);
        return StatsSaleIndicatorsConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsSaleIndicatorsCount(StatsSaleIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsSaleIndicatorsExample example = StatsSaleIndicatorsConverter.query2Example(query);
        long c = statsSaleIndicatorsDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询销售指标
     */
    @Override
    public PageInfo<StatsSaleIndicatorsVO> queryStatsSaleIndicatorsByPage(StatsSaleIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        // 设置 order  by
        query.setOrderBy("customer_consumption_points DESC");


        // 根据parentId判断查询父列表还是子列表
        if (query.getParentId() == null || query.getParentId() == 0) {
            // 查询父列表（parentId为0的数据）
            query.setParentId(0);
            return queryParentList(query);
        } else {
            // 查询子列表（指定parentId的数据）
            return queryChildList(query);
        }
    }

    /**
     * 查询父列表数据（parentId为0的数据）
     *
     * @param query 查询条件
     * @return 分页结果
     */
    private PageInfo<StatsSaleIndicatorsVO> queryParentList(StatsSaleIndicatorsQuery query) {
        String statsDate = query.getStatsDate();
        String statsType = query.getStatsType();

        // 判断今天的日期所在的 周/月/季 的第一天是否是statsDate,若是则需要走新查询逻辑，若不是则按照原有逻辑
        boolean isFirstDayOfPeriod = isFirstDayOfCurrentPeriod(statsDate, statsType);

        // 根据 isFirstDayOfPeriod 的值，可以决定走新查询逻辑还是原有逻辑
        if (isFirstDayOfPeriod) {
            log.info("statsDate {} 是今天所在周/月/季的第一天，需要走新查询逻辑", statsDate);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String todayDateStr = dateFormat.format(new Date());

            List<StatsSaleIndicatorsVO> aggregatedList = executePeriodBusinessStats(statsDate, statsDate, todayDateStr, statsType, 0, query.getUserId(), query.getName());

            // 重新排序
            if (aggregatedList != null && !aggregatedList.isEmpty()) {
                Comparator<StatsSaleIndicatorsVO> comparator = Comparator.comparing(StatsSaleIndicatorsVO::getCreateCount, Comparator.nullsLast(Comparator.reverseOrder()));
                aggregatedList.sort(comparator);
            }

            PageInfo<StatsSaleIndicatorsVO> page = new PageInfo<>();
            if (CollectionUtils.isEmpty(aggregatedList)) {
                page.setList(new ArrayList<>());
                page.setSize(0);
                page.setTotalCount(0);
                page.setHasNextPage(false);
            } else {
                page.setList(aggregatedList);
                page.setSize(aggregatedList.size());
                page.setTotalCount((long) aggregatedList.size());
                page.setHasNextPage(false); // 聚合结果通常只有一页
            }
            return page;
        }

        PageInfo<StatsSaleIndicatorsVO> page = new PageInfo<>();

        StatsSaleIndicatorsExample example = StatsSaleIndicatorsConverter.query2Example(query);

        // 设置分页参数
        example.setOffset((query.getPageNum() - 1) * query.getPageSize());
        example.setRows(query.getPageSize());

        // 查询父级记录总数
        long totalCount = statsSaleIndicatorsDAO.countByExample(example);
        if (totalCount == 0) {
            // 无数据时返回空结果
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);
            return page;
        }

        // 查询父级数据列表
        List<StatsSaleIndicatorsDO> list = statsSaleIndicatorsDAO.selectByExample(example);
        List<StatsSaleIndicatorsVO> resultList = StatsSaleIndicatorsConverter.doList2VOList(list);

        // 设置分页信息
        page.setList(resultList);
        page.setSize(CollectionUtils.size(resultList));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 查询子列表数据（指定parentId的数据）
     *
     * @param query 查询条件（必须包含parentId）
     * @return 分页结果
     */
    private PageInfo<StatsSaleIndicatorsVO> queryChildList(StatsSaleIndicatorsQuery query) {
        String statsDate = query.getStatsDate();
        String statsType = query.getStatsType();

        // 判断今天的日期所在的 周/月/季 的第一天是否是statsDate,若是则需要走新查询逻辑，若不是则按照原有逻辑
        boolean isFirstDayOfPeriod = isFirstDayOfCurrentPeriod(statsDate, statsType);

        // 根据 isFirstDayOfPeriod 的值，可以决定走新查询逻辑还是原有逻辑
        if (isFirstDayOfPeriod) {
            log.info("statsDate {} 是今天所在周/月/季的第一天，需要走新查询逻辑", statsDate);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String todayDateStr = dateFormat.format(new Date());

            List<StatsSaleIndicatorsVO> aggregatedList = executePeriodBusinessStats(statsDate, statsDate, todayDateStr, statsType, query.getParentId(), query.getUserId(), query.getName());

            // 重新排序
            if (aggregatedList != null && !aggregatedList.isEmpty()) {
                Comparator<StatsSaleIndicatorsVO> comparator = Comparator.comparing(StatsSaleIndicatorsVO::getCreateCount, Comparator.nullsLast(Comparator.reverseOrder()));
                aggregatedList.sort(comparator);
            }

            PageInfo<StatsSaleIndicatorsVO> page = new PageInfo<>();
            if (CollectionUtils.isEmpty(aggregatedList)) {
                page.setList(new ArrayList<>());
                page.setSize(0);
                page.setTotalCount(0);
                page.setHasNextPage(false);
            } else {
                page.setList(aggregatedList);
                page.setSize(aggregatedList.size());
                page.setTotalCount((long) aggregatedList.size());
                page.setHasNextPage(false); // 聚合结果通常只有一页
            }
            return page;
        }

        PageInfo<StatsSaleIndicatorsVO> page = new PageInfo<>();

        // 构造查询条件
        StatsSaleIndicatorsExample example = StatsSaleIndicatorsConverter.query2Example(query);

        // 设置分页参数
        example.setOffset((query.getPageNum() - 1) * query.getPageSize());
        example.setRows(query.getPageSize());

        // 查询子记录总数
        long totalCount = statsSaleIndicatorsDAO.countByExample(example);
        if (totalCount == 0) {
            // 无数据时返回空结果
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);
            return page;
        }

        // 查询子数据列表
        List<StatsSaleIndicatorsDO> list = statsSaleIndicatorsDAO.selectByExampleWithBLOBs(example);
        List<StatsSaleIndicatorsVO> resultList = StatsSaleIndicatorsConverter.doList2VOList(list);

        // 设置分页信息
        page.setList(resultList);
        page.setSize(CollectionUtils.size(resultList));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsSaleIndicatorsVO> statsList) {
        if (CollectionUtils.isEmpty(statsList)) {
            return 0;
        }

        List<StatsSaleIndicatorsDO> statsSaleIndicatorsDOList = new ArrayList<>();
        for (StatsSaleIndicatorsVO statsVO : statsList) {
            StatsSaleIndicatorsDO statsDO = StatsSaleIndicatorsConverter.vo2DO(statsVO);
            statsSaleIndicatorsDOList.add(statsDO);
        }

        return statsSaleIndicatorsDAO.batchInsertOrUpdate(statsSaleIndicatorsDOList);
    }

    /**
     * 根据父ID列表批量查询子数据
     *
     * @param parentIds 父ID列表
     * @param query 其他查询条件（可选）
     * @return 子数据Map，key为父ID，value为该父ID下的所有子数据列表
     */
    @Override
    public Map<Integer, List<StatsSaleIndicatorsVO>> queryChildrenByParentIds(List<Integer> parentIds, StatsSaleIndicatorsQuery query) {
        AssertUtil.assertNotNull(parentIds, ResultCode.PARAM_INVALID, "parentIds is null");
        if (CollectionUtils.isEmpty(parentIds)) {
            return new HashMap<>();
        }

        // 创建查询条件
        StatsSaleIndicatorsExample example = new StatsSaleIndicatorsExample();
        StatsSaleIndicatorsExample.Criteria criteria = example.createCriteria();
        criteria.andParentIdIn(parentIds);

        // 添加其他查询条件
        if (query != null) {
            if (query.getStatsType() != null) {
                criteria.andStatsTypeEqualTo(query.getStatsType());
            }
            if (query.getStatsDate() != null) {
                criteria.andStatsDateEqualTo(query.getStatsDate());
            }
            if (query.getUserId() != null) {
                criteria.andUserIdEqualTo(query.getUserId());
            }
            if (query.getName() != null) {
                criteria.andNameEqualTo(query.getName());
            }
        }

        // 按ID排序
        example.setOrderByClause("id DESC");

        // 查询子数据
        List<StatsSaleIndicatorsDO> childList = statsSaleIndicatorsDAO.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(childList)) {
            return new HashMap<>();
        }

        // 将查询结果按parentId分组
        Map<Integer, List<StatsSaleIndicatorsVO>> resultMap = new HashMap<>();
        List<StatsSaleIndicatorsVO> childVOList = StatsSaleIndicatorsConverter.doList2VOList(childList);

        for (StatsSaleIndicatorsVO child : childVOList) {
            Integer parentId = child.getParentId();
            if (!resultMap.containsKey(parentId)) {
                resultMap.put(parentId, new ArrayList<>());
            }
            resultMap.get(parentId).add(child);
        }

        return resultMap;
    }

    @Override
    public List<StatsSaleIndicatorsVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsSaleIndicatorsDO> statsMaterialOwnerDOList = statsSaleIndicatorsDAO.selectStatsInfoByDateAndPeriod(startDate, endDate, statsType);

        // 转换为VO列表
        return StatsSaleIndicatorsConverter.doList2VOList(statsMaterialOwnerDOList);
    }

    public List<StatsSaleIndicatorsVO> selectStatsInfoByDateAndParentId(String startDate, String endDate, String statsType, Integer parentId, Integer userId, String nickName) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsSaleIndicatorsDO> statsMaterialOwnerDOList = statsSaleIndicatorsDAO.selectStatsInfoByDateAndParentId(startDate, endDate, statsType, parentId, userId, nickName);

        // 转换为VO列表
        return StatsSaleIndicatorsConverter.doList2VOList(statsMaterialOwnerDOList);
    }

    /**
     * 判断给定的日期是否是当前日期所在周/月/季的第一天。
     * statsDate为需要对比的日期，statsDate必定为周一，每个月的第一天或季度的第一天，且格式为yyyy-MM-DD。
     * statsType为时间类型（WEEKLY/MONTHLY/QUARTERLY）需要根据type进行判断取哪个的第一天。
     *
     * @param statsDate 统计日期，格式为yyyy-MM-DD
     * @param statsType 统计类型 (WEEKLY/MONTHLY/QUARTERLY)
     * @return 如果是当前周/月/季的第一天，则返回true，否则返回false
     */
    private boolean isFirstDayOfCurrentPeriod(String statsDate, String statsType) {
        if (StringUtils.isBlank(statsDate) || StringUtils.isBlank(statsType)) {
            return false;
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parsedStatsDate = dateFormat.parse(statsDate);

            // 当天时间
            Calendar calToday = Calendar.getInstance();
            Date today = new Date();
            calToday.setTime(today);

            Calendar calStatsDate = Calendar.getInstance();
            calStatsDate.setTime(parsedStatsDate);

            switch (statsType) {
                case "WEEKLY":
                    // 判断是否是本周第一天 (周一)
                    Calendar firstDayOfWeek = (Calendar) calToday.clone();
                    firstDayOfWeek.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                    // 确保年份和月份也匹配，因为set(DAY_OF_WEEK, MONDAY)可能会跨月或跨年
                    if (firstDayOfWeek.get(Calendar.DAY_OF_MONTH) > calToday.get(Calendar.DAY_OF_MONTH)) {
                        firstDayOfWeek.add(Calendar.WEEK_OF_YEAR, -1);
                    }
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfWeek.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfWeek.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfWeek.get(Calendar.DAY_OF_MONTH);

                case "MONTHLY":
                    // 判断是否是本月第一天
                    Calendar firstDayOfMonth = (Calendar) calToday.clone();
                    firstDayOfMonth.set(Calendar.DAY_OF_MONTH, 1);
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfMonth.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfMonth.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfMonth.get(Calendar.DAY_OF_MONTH);

                case "QUARTERLY":
                    // 判断是否是本季度第一天
                    Calendar firstDayOfQuarter = (Calendar) calToday.clone();
                    int currentMonth = firstDayOfQuarter.get(Calendar.MONTH);
                    int quarterFirstMonth = (currentMonth / 3) * 3; // 0, 3, 6, 9
                    firstDayOfQuarter.set(Calendar.MONTH, quarterFirstMonth);
                    firstDayOfQuarter.set(Calendar.DAY_OF_MONTH, 1);
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfQuarter.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfQuarter.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfQuarter.get(Calendar.DAY_OF_MONTH);

                default:
                    return false;
            }
        } catch (ParseException e) {
            log.warn("解析statsDate失败: {}", statsDate, e);
            return false;
        }
    }


    private List<StatsSaleIndicatorsVO> executePeriodBusinessStats(String storageDate, String startDate, String endDate,
                                                                   String statsType, Integer parentId, Integer queryUserId, String nickName) {
        // 查询某个时间段内的统计数据
        List<StatsSaleIndicatorsVO> sourceStatsList = selectStatsInfoByDateAndParentId(startDate, endDate, StatsPeriodEnum.DAILY.getCode(), parentId, queryUserId, nickName);

        // 如果统计数据为空，则直接返回0
        if (CollectionUtils.isEmpty(sourceStatsList)) {
            return null;
        }

        // 1、 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 根据用户ID进行分组
        Map<Integer, List<StatsSaleIndicatorsVO>> userIdStatsMap = sourceStatsList.stream()
                .collect(Collectors.groupingBy(StatsSaleIndicatorsVO::getUserId));

        // 汇总每个用户的统计数据
        List<StatsSaleIndicatorsVO> targetStatsList = new ArrayList<>();

        for (Map.Entry<Integer, List<StatsSaleIndicatorsVO>> entry : userIdStatsMap.entrySet()) {
            Integer userId = entry.getKey();
            List<StatsSaleIndicatorsVO> userStats = entry.getValue();

            // 确保有数据
            if (CollectionUtils.isEmpty(userStats)) {
                continue;
            }

            // 获取销售关联的所有客户
            SalesCustomerData customerData = getSalesCustomerData(userId);
            List<Integer> distinctCustomerIds = customerData.getDistinctCustomerIds();

            // 获取最新的一条记录，用于提取用户名称和父级ID等基本信息
            StatsSaleIndicatorsVO firstStat = userStats.stream()
                    .max(Comparator.comparing(StatsSaleIndicatorsVO::getCreateTime))
                    .orElse(userStats.get(0));

            // 进行累加以及比例计算
            StatsSaleIndicatorsVO targetStats = new StatsSaleIndicatorsVO();
            targetStats.setUserId(userId);
            targetStats.setStatsDate(storageDate);
            targetStats.setStatsType(statsType);
            targetStats.setName(firstStat.getName());
            targetStats.setParentId(firstStat.getParentId());
            targetStats.setCreateTime(new Date());
            targetStats.setModifyTime(new Date());

            // 初始化各项指标的默认值，避免空值问题
            targetStats.setClothesExpCount(0);
            targetStats.setCustomerConversionCount(0);
            targetStats.setCustomerConsumptionPoints(0);
            targetStats.setCustomerActivityRate("0.00");
            targetStats.setCustomerRepurchaseRate("0.00");
            targetStats.setCustomModelCustomers("0.00");
            targetStats.setCustomSceneCustomers("0.00");
            targetStats.setCustomerProtectionMetrics(0);
            targetStats.setCreateCount(0);

            // 累加各数值型指标
            int totalClothesExpCount = userStats.stream()
                    .filter(data -> data.getClothesExpCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getClothesExpCount)
                    .sum();

            int totalCustomerConversionCount = userStats.stream()
                    .filter(data -> data.getCustomerConversionCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCustomerConversionCount)
                    .sum();

            int totalCustomerConsumptionPoints = userStats.stream()
                    .filter(data -> data.getCustomerConsumptionPoints() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCustomerConsumptionPoints)
                    .sum();

            int totalCreateCount = userStats.stream()
                    .filter(data -> data.getCreateCount() != null)
                    .mapToInt(StatsSaleIndicatorsVO::getCreateCount)
                    .sum();

            // 设置累加的数值型指标
            targetStats.setClothesExpCount(totalClothesExpCount);
            targetStats.setCustomerConversionCount(totalCustomerConversionCount);
            targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
            targetStats.setCreateCount(totalCreateCount);


            // 若为企业则取最后一条记录的客户保护指标数据
            if (firstStat.getParentId() == 0) {
                targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());
            } else {
                // 重新计算客户保护指标
                int customerProtectionMetrics = getCustomerProtectionMetrics(startDate, endDate, userId, distinctCustomerIds);

                targetStats.setCustomerProtectionMetrics(customerProtectionMetrics);
            }


            // 计算加权平均比率指标 - 使用我们优化后的calculateWeightedAverageRatios方法
            calculateWeightedAverageRatios(userStats, targetStats);

            // 获取销售的准确客户总数
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            Long customerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

            // 重新计算模型比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, targetStats);
            // 重新计算场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, customerCount, targetStats);


            // 将汇总的统计数据添加到结果列表中
            targetStatsList.add(targetStats);
        }


        return targetStatsList;
    }

    /**
     * 销售客户数据类，用于存储销售关联的客户信息
     */
    @Data
    private static class SalesCustomerData {
        // 直接关联的客户ID列表
        private List<Integer> directCustomerIds = new ArrayList<>();
        // 虚拟账户ID列表
        private List<Integer> virtualUserIds = new ArrayList<>();
        // 虚拟账户关联的客户ID列表
        private List<Integer> virtualCustomerIds = new ArrayList<>();
        // 合并去重后的所有客户ID列表
        private List<Integer> distinctCustomerIds = new ArrayList<>();
    }

    /**
     * 获取销售关联的所有客户数据
     *
     * @param userId 销售用户ID
     * @return 销售客户数据
     */
    private SalesCustomerData getSalesCustomerData(Integer userId) {
        SalesCustomerData result = new SalesCustomerData();

        try {
            // 获取销售关联的所有客户（直接客户）
            DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
            customerQuery.setDistributorSalesUserId(userId);
            List<DistributorCustomerVO> customerList = distributorCustomerService
                    .queryDistributorCustomerList(customerQuery);

            List<Integer> directCustomerIds = org.springframework.util.CollectionUtils.isEmpty(customerList) ? new ArrayList<>()
                    : customerList.stream()
                    .map(DistributorCustomerVO::getCustomerMasterUserId)
                    .distinct()
                    .collect(Collectors.toList());

            // 2.过滤出 3999 以上的用户
            List<Integer> more3999userIdList = userService.fetch3999UserIdList(userId);

            // 取directCustomerIds 和 more3999userIdList 两个集合的交集
            List<Integer> intersection = directCustomerIds.stream()
                    .filter(more3999userIdList::contains)
                    .distinct()
                    .collect(Collectors.toList());

            result.setDistinctCustomerIds(intersection);

        } catch (Exception e) {
            log.error("获取销售客户数据异常，销售ID:{}, 异常:{}", userId, e.getMessage(), e);
        }

        return result;
    }

    /**
     * 获取客户保护指标（大于60天未充值的客户数量）
     *
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param userId              用户ID
     * @param distinctCustomerIds 去重后的客户ID列表
     * @return 大于60天未充值的客户数量
     */
    private Integer getCustomerProtectionMetrics(String startDate, String endDate, Integer userId,
                                                 List<Integer> distinctCustomerIds) {
        try {
            if (org.springframework.util.CollectionUtils.isEmpty(distinctCustomerIds)) {
                return 0;
            }

            // 获取准确的客户总数
            long totalCustomerCount = getAccurateCustomerCount(userId, distinctCustomerIds);

            // 使用UserService中的queryBefore60Days方法获取所有超过60天创建且非VIP的用户
            List<SalesInfoVO> salesInfoList = userService.queryBefore60Days(endDate);
            if (org.springframework.util.CollectionUtils.isEmpty(salesInfoList)) {
                return 0;
            }

            // 筛选当前销售相关的记录并累加sleepCnt
            int sleepCustomerCount = salesInfoList.stream()
                    .filter(info -> userId.equals(info.getSalesId()))
                    .mapToInt(SalesInfoVO::getSleepCnt)
                    .sum();

            log.info("客户保护指标统计(超60天非VIP客户) - 销售ID:{}, 实际客户总数:{}, 超60天非VIP客户数:{}",
                    userId, totalCustomerCount, sleepCustomerCount);

            return sleepCustomerCount;
        } catch (Exception e) {
            log.error("计算客户保护指标异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取销售关联的准确客户总数
     *
     * @param userId              销售用户ID
     * @param distinctCustomerIds 去重后的客户ID列表（作为备选数据源）
     * @return 客户总数
     */
    private long getAccurateCustomerCount(Integer userId, List<Integer> distinctCustomerIds) {
        // 获取销售的准确客户总数
        DistributorCustomerQuery customerQuery = new DistributorCustomerQuery();
        customerQuery.setDistributorSalesUserId(userId);
        Long totalCustomerCount = distributorCustomerService.queryDistributorCustomerCount(customerQuery);

        // 如果查询结果为空或为0，则使用传入的客户列表大小作为备选
        if (totalCustomerCount == null || totalCustomerCount == 0) {
            totalCustomerCount = (long) distinctCustomerIds.size();
        }

        return totalCustomerCount;
    }


    /**
     * 计算加权平均比率指标
     *
     * @param subResultDataList 子账号统计数据列表
     * @param totalIndicators   汇总统计数据
     */
    private void calculateWeightedAverageRatios(List<StatsSaleIndicatorsVO> subResultDataList,
                                                StatsSaleIndicatorsVO totalIndicators) {
        // 累计所有指标的分子和分母
        int totalActivityRateMolecular = 0;   // 活跃率分子（活跃客户数）
        int totalActivityRateDenominator = 0; // 活跃率分母（总客户数）

        int totalRepurchaseRateMolecular = 0;   // 复购率分子（复购客户数）
        int totalRepurchaseRateDenominator = 0; // 复购率分母（有首次大额付款的客户数）

        // 从每个子账号的extInfo中获取分子和分母信息，分别累加
        for (StatsSaleIndicatorsVO data : subResultDataList) {
            if (data.getExtInfo() == null) {
                continue;
            }

            // 客户活跃率
            Integer activityRateMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR);
            Integer activityRateDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);
            if (activityRateMolecular != null && activityRateDenominator != null) {
                totalActivityRateMolecular += activityRateMolecular;
                totalActivityRateDenominator += activityRateDenominator;
                log.debug("累加活跃率数据 - 销售:{}, 活跃客户数:{}, 总客户数:{}",
                        data.getName(), activityRateMolecular, activityRateDenominator);
            }

            // 客户复购率
            Integer repurchaseRateMolecular = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR);
            Integer repurchaseRateDenominator = data.getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);
            if (repurchaseRateMolecular != null && repurchaseRateDenominator != null) {
                totalRepurchaseRateMolecular += repurchaseRateMolecular;
                totalRepurchaseRateDenominator += repurchaseRateDenominator;
                log.debug("累加复购率数据 - 销售:{}, 复购客户数:{}, 有首付客户数:{}",
                        data.getName(), repurchaseRateMolecular, repurchaseRateDenominator);
            }
        }

        // 计算各项指标的比率并设置到结果对象中

        // 1. 计算客户活跃率
        calculateRatio(totalActivityRateMolecular, totalActivityRateDenominator,
                "客户活跃率", "活跃客户总数", "客户总数",
                totalIndicators,
                totalIndicators::setCustomerActivityRate,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);

        // 2. 计算客户复购率
        calculateRatio(totalRepurchaseRateMolecular, totalRepurchaseRateDenominator,
                "客户复购率", "复购客户总数", "有首付客户总数",
                totalIndicators,
                totalIndicators::setCustomerRepurchaseRate,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);
    }

    /**
     * 计算比率并设置到结果对象中
     *
     * @param molecular       分子值
     * @param denominator     分母值
     * @param indicatorName   指标名称
     * @param molecularDesc   分子描述
     * @param denominatorDesc 分母描述
     * @param target          目标对象
     * @param setter          设置方法
     * @param molecularKey    分子键名
     * @param denominatorKey  分母键名
     */
    private void calculateRatio(int molecular, int denominator,
                                String indicatorName, String molecularDesc, String denominatorDesc,
                                StatsSaleIndicatorsVO target, Consumer<String> setter,
                                String molecularKey, String denominatorKey) {

        // 如果分母大于0，计算比率
        if (denominator > 0) {
            double ratio = (double) molecular / denominator * 100;
            String formattedRatio = String.format("%.2f", ratio);

            // 设置结果
            setter.accept(formattedRatio);

            // 保存分子分母到target的extInfo中
            target.addExtInfo(molecularKey, molecular);
            target.addExtInfo(denominatorKey, denominator);

            log.info("计算总{} - {}:{}, {}:{}, 计算结果:{}%",
                    indicatorName, molecularDesc, molecular, denominatorDesc, denominator, formattedRatio);
        } else {
            // 设置默认值
            setter.accept("0.00");

            // 保存默认分子分母
            target.addExtInfo(molecularKey, 0);
            target.addExtInfo(denominatorKey, 0);

            log.info("计算总{} - 无有效数据，设置为0.00", indicatorName);
        }
    }

    /**
     * 获取定制模特比例
     *
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param userFaceSceneList     所有用户的定制元素信息
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomModelRatio(Integer userId,
                                     List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                     long totalCustomerCount, StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (org.springframework.util.CollectionUtils.isEmpty(distinctCustomerIds) || org.springframework.util.CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
                return;
            }

            // 统计使用定制模特的客户数
            int customModelCustomerCount = 0; // 使用了定制模特的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制模特
                boolean hasCustomModel = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getFaceCnt() != null && element.getFaceCnt() > 0);

                if (hasCustomModel) {
                    customModelCustomerCount++;
                }
            }

            // 计算定制模特比例 = 使用定制模特的客户数 / 总客户数
            double customModelRatio = (double) customModelCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customModelRatio);

            log.info("定制模特比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制模特客户数:{}, 定制模特比例:{}%",
                    userId, totalCustomerCount, customModelCustomerCount, formattedRatio);

            // 设置定制模特比例
            statsSaleIndicatorsVO.setCustomModelCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    customModelCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制模特比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomModelCustomers("0.00");
        }
    }

    /**
     * 获取定制场景比例
     *
     * @param userId                用户ID
     * @param distinctCustomerIds   去重后的客户ID列表
     * @param userFaceSceneList     所有用户的定制元素信息
     * @param totalCustomerCount    客户总数
     * @param statsSaleIndicatorsVO 统计结果对象
     */
    private void getCustomSceneRatio(Integer userId,
                                     List<Integer> distinctCustomerIds, List<UserFaceSceneVO> userFaceSceneList,
                                     long totalCustomerCount, StatsSaleIndicatorsVO statsSaleIndicatorsVO) {
        try {
            if (org.springframework.util.CollectionUtils.isEmpty(distinctCustomerIds) || org.springframework.util.CollectionUtils.isEmpty(userFaceSceneList)) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            if (totalCustomerCount == 0) {
                statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
                return;
            }

            // 统计使用定制场景的客户数
            int customSceneCustomerCount = 0; // 使用了定制场景的客户数

            // userId 也添加进来
            distinctCustomerIds.add(userId);

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制场景
                boolean hasCustomScene = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getSceneCnt() != null && element.getSceneCnt() > 0);

                if (hasCustomScene) {
                    customSceneCustomerCount++;
                }
            }

            // 计算定制场景比例 = 使用定制场景的客户数 / 总客户数
            double customSceneRatio = (double) customSceneCustomerCount / totalCustomerCount * 100;
            String formattedRatio = String.format("%.2f", customSceneRatio);

            log.info("定制场景比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制场景客户数:{}, 定制场景比例:{}%",
                    userId, totalCustomerCount, customSceneCustomerCount, formattedRatio);

            // 设置定制场景比例
            statsSaleIndicatorsVO.setCustomSceneCustomers(formattedRatio);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    customSceneCustomerCount);
            statsSaleIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR, totalCustomerCount);

        } catch (Exception e) {
            log.error("计算定制场景比例异常，用户ID:{}, 异常:{}", userId, e.getMessage(), e);
            statsSaleIndicatorsVO.setCustomSceneCustomers("0.00");
        }
    }


}
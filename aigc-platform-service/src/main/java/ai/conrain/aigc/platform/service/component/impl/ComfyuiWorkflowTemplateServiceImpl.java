package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.ComfyuiWorkflowTemplateActiveVersionDAO;
import ai.conrain.aigc.platform.dal.dao.ComfyuiWorkflowTemplateDAO;
import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateActiveVersionDO;
import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiWorkflowTemplateExample;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateActiveVersionService;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.model.biz.WorkflowOpenScopeModel;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ComfyuiWorkflowTemplateActiveVersionConverter;
import ai.conrain.aigc.platform.service.model.converter.ComfyuiWorkflowTemplateConverter;
import ai.conrain.aigc.platform.service.model.query.ComfyuiWorkflowTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateActiveVersionVO;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ComfyuiWorkflowTemplateService实现
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateService.java v 0.1 2025-06-11 03:56:03
 */
@Slf4j
@Service
public class ComfyuiWorkflowTemplateServiceImpl implements ComfyuiWorkflowTemplateService {
    /** DAO */
    @Autowired
    private ComfyuiWorkflowTemplateDAO comfyuiWorkflowTemplateDAO;
    @Autowired
    private ComfyuiWorkflowTemplateActiveVersionDAO comfyuiWorkflowTemplateActiveVersionDAO;
    @Autowired
    private ComfyuiWorkflowTemplateActiveVersionService comfyuiWorkflowTemplateActiveVersionService;
    @Autowired
    private UserService userService;

    @Override
    public ComfyuiWorkflowTemplateVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ComfyuiWorkflowTemplateDO data = comfyuiWorkflowTemplateDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ComfyuiWorkflowTemplateConverter.do2VO(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
        ComfyuiWorkflowTemplateVO template = selectById(id);
        AssertUtil.assertNotNull(template, ResultCode.BIZ_FAIL, "模板不存在");

        ComfyuiWorkflowTemplateActiveVersionVO activeVersion
            = comfyuiWorkflowTemplateActiveVersionService.queryByTemplateKey(template.getTemplateKey());

        if (StringUtils.equals(activeVersion.getActiveVersion(), template.getVersion())) {
            log.error("模板正在使用中，请先取消激活,id={}", id);
            throw new BizException(ResultCode.BIZ_FAIL, "模板正在使用中，请先取消激活");
        }

        if (StringUtils.equals(activeVersion.getTestVersion(), template.getVersion())) {
            log.info("模板正在测试中，设置测试版本为空,id={}", id);
            activeVersion.setTestVersion(null);
            activeVersion.setTestOpenScope(null);
            comfyuiWorkflowTemplateActiveVersionService.updateById(activeVersion);
        }

        int n = comfyuiWorkflowTemplateDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ComfyuiWorkflowTemplate失败");
    }

    @Override
    public ComfyuiWorkflowTemplateVO insert(ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate) {
        AssertUtil.assertNotNull(comfyuiWorkflowTemplate, ResultCode.PARAM_INVALID, "comfyuiWorkflowTemplate is null");
        AssertUtil.assertTrue(comfyuiWorkflowTemplate.getId() == null, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplate.id is present");

        //创建时间、修改时间兜底
        if (comfyuiWorkflowTemplate.getCreateTime() == null) {
            comfyuiWorkflowTemplate.setCreateTime(new Date());
        }

        if (comfyuiWorkflowTemplate.getModifyTime() == null) {
            comfyuiWorkflowTemplate.setModifyTime(new Date());
        }

        ComfyuiWorkflowTemplateDO data = ComfyuiWorkflowTemplateConverter.vo2DO(comfyuiWorkflowTemplate);
        Integer n = comfyuiWorkflowTemplateDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ComfyuiWorkflowTemplate失败");
        AssertUtil.assertNotNull(data.getId(), "新建ComfyuiWorkflowTemplate返回id为空");
        comfyuiWorkflowTemplate.setId(data.getId());
        return comfyuiWorkflowTemplate;
    }

    @Override
    public void updateByIdSelective(ComfyuiWorkflowTemplateVO comfyuiWorkflowTemplate) {
        AssertUtil.assertNotNull(comfyuiWorkflowTemplate, ResultCode.PARAM_INVALID, "comfyuiWorkflowTemplate is null");
        AssertUtil.assertTrue(comfyuiWorkflowTemplate.getId() != null, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplate.id is null");

        //修改时间必须更新
        comfyuiWorkflowTemplate.setModifyTime(new Date());
        ComfyuiWorkflowTemplateDO data = ComfyuiWorkflowTemplateConverter.vo2DO(comfyuiWorkflowTemplate);
        int n = comfyuiWorkflowTemplateDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ComfyuiWorkflowTemplate失败，影响行数:" + n);
    }

    @Override
    public List<ComfyuiWorkflowTemplateVO> queryComfyuiWorkflowTemplateList(ComfyuiWorkflowTemplateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ComfyuiWorkflowTemplateExample example = ComfyuiWorkflowTemplateConverter.query2Example(query);

        List<ComfyuiWorkflowTemplateDO> list = comfyuiWorkflowTemplateDAO.selectByExampleWithBLOBs(example);
        return ComfyuiWorkflowTemplateConverter.doList2VOList(list);
    }

    @Override
    public Long queryComfyuiWorkflowTemplateCount(ComfyuiWorkflowTemplateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ComfyuiWorkflowTemplateExample example = ComfyuiWorkflowTemplateConverter.query2Example(query);
        long c = comfyuiWorkflowTemplateDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询comfyui模板
     */
    @Override
    public PageInfo<ComfyuiWorkflowTemplateVO> queryComfyuiWorkflowTemplateByPage(ComfyuiWorkflowTemplateQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ComfyuiWorkflowTemplateVO> page = new PageInfo<>();

        ComfyuiWorkflowTemplateExample example = ComfyuiWorkflowTemplateConverter.query2Example(query);
        long totalCount = comfyuiWorkflowTemplateDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ComfyuiWorkflowTemplateDO> list = comfyuiWorkflowTemplateDAO.selectByExampleWithBLOBs(example);
        page.setList(ComfyuiWorkflowTemplateConverter.doList2VOList(list));

        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<ComfyuiWorkflowTemplateActiveVersionVO> activeVersions
                = comfyuiWorkflowTemplateActiveVersionService.batchQueryByTemplateKey(
                page.getList().stream().map(ComfyuiWorkflowTemplateVO::getTemplateKey).collect(Collectors.toList()));

            page.getList().forEach(e -> {
                activeVersions.stream().filter(i -> e.getTemplateKey().equals(i.getTemplateKey())).findFirst()
                    .ifPresent(activeVersion -> {
                        if (StringUtils.equals(activeVersion.getActiveVersion(), e.getVersion())) {
                            e.setIsActive(true);
                        }
                        if (StringUtils.equals(activeVersion.getTestVersion(), e.getVersion())) {
                            e.setIsTest(true);
                            e.setOpenScope(activeVersion.getTestOpenScope());
                        }
                    });
            });
        }

        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public ComfyuiWorkflowTemplateVO queryTemplateByKeyAndVersion(String key, String version) {
        AssertUtil.assertNotBlank(key, ResultCode.SYS_ERROR, "模板key不能为空");
        AssertUtil.assertNotBlank(version, ResultCode.SYS_ERROR, "模板version不能为空");
        ComfyuiWorkflowTemplateDO templateDO = comfyuiWorkflowTemplateDAO.selectByTemplateKeyAndVersion(key, version);
        if (templateDO != null) {
            return ComfyuiWorkflowTemplateConverter.do2VO(templateDO);
        }
        return null;
    }

    @Override
    public ComfyuiWorkflowTemplateVO getActiveTemplateByKey(String key, Integer userId) {
        AssertUtil.assertNotBlank(key, ResultCode.SYS_ERROR, "模板key不能为空");
        ComfyuiWorkflowTemplateActiveVersionVO activeTplVersion = ComfyuiWorkflowTemplateActiveVersionConverter.do2VO(
            comfyuiWorkflowTemplateActiveVersionDAO.selectByTemplateKey(key));
        String activeVersion = activeTplVersion.getActiveVersion();

        if (StringUtils.isBlank(activeVersion)) {
            return null;
        }

        String testVersion = activeTplVersion.getTestVersion();
        // 测试版本
        if (StringUtils.isNotBlank(testVersion) && StringUtils.compare(testVersion, activeVersion) > 0) {
            // 1. 查到用户信息
            UserVO user = userService.selectByIdWithoutMasterInfo(userId);
            WorkflowOpenScopeModel openScope = activeTplVersion.getTestOpenScope();
            // 2. 测试版本是否在opeScope内
            if (ObjectUtils.isNotEmpty(user) && (ObjectUtils.isEmpty(openScope) || openScope.isInScope(user.getId(),
                user.getRoleType()))) {
                log.info("当前任务获取工作流模板，命中测试版本，key={},testVersion={},activeVersion={},userId={}", key,
                    testVersion, activeVersion, userId);

                return ComfyuiWorkflowTemplateConverter.do2VO(
                    comfyuiWorkflowTemplateDAO.selectByTemplateKeyAndVersion(key, testVersion));
            } else {
                log.warn(
                    "当前任务获取工作流模板，有测试版本，但未命中opeScope，key={},testVersion={},activeVersion={},userId={}",
                    key, testVersion, activeVersion, userId);
            }
        } else if (StringUtils.isNotBlank(testVersion)) {
            log.warn("当前任务获取工作流模板，有测试版本，但未命中，key={},testVersion={},activeVersion={},userId={}", key,
                testVersion, activeVersion, userId);
        }

        return ComfyuiWorkflowTemplateConverter.do2VO(
            comfyuiWorkflowTemplateDAO.selectByTemplateKeyAndVersion(key, activeVersion));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ComfyuiWorkflowTemplateVO createActiveVersion(ComfyuiWorkflowTemplateVO newVersionTpl) {

        ComfyuiWorkflowTemplateActiveVersionDO activeTplVersion
            = comfyuiWorkflowTemplateActiveVersionDAO.selectByTemplateKey(newVersionTpl.getTemplateKey());
        //新建
        if (activeTplVersion == null) {
            AssertUtil.assertNotBlank(newVersionTpl.getTemplateDesc(), ResultCode.SYS_ERROR,
                "创建模板失败，模板描述不能为空");

            activeTplVersion = new ComfyuiWorkflowTemplateActiveVersionDO();
            activeTplVersion.setTemplateKey(newVersionTpl.getTemplateKey());
            activeTplVersion.setTemplateDesc(newVersionTpl.getTemplateDesc());
            activeTplVersion.setActiveVersion(newVersionTpl.getVersion());
            activeTplVersion.setCreateTime(new Date());
            activeTplVersion.setModifyTime(new Date());
            activeTplVersion.setCreateBy(newVersionTpl.getCreateBy());
            activeTplVersion.setModifyBy(newVersionTpl.getModifyBy());

            comfyuiWorkflowTemplateActiveVersionDAO.insert(activeTplVersion);

            //存量模板，发新版本
        } else {
            String tplDesc = StringUtils.defaultIfBlank(newVersionTpl.getTemplateDesc(),
                activeTplVersion.getTemplateDesc());
            AssertUtil.assertNotBlank(tplDesc, ResultCode.SYS_ERROR, "模板描述不能为空");

            activeTplVersion.setTemplateDesc(tplDesc);
            //分流测试中和线上版本
            if (newVersionTpl.getIsTest() != null && newVersionTpl.getIsTest()) {
                activeTplVersion.setTestVersion(newVersionTpl.getVersion());
                // 设置开放范围
                if (!ObjectUtils.isEmpty(newVersionTpl.getOpenScope())) {
                    activeTplVersion.setTestOpenScope(JSONObject.toJSONString(newVersionTpl.getOpenScope()));
                }
            } else {
                activeTplVersion.setActiveVersion(newVersionTpl.getVersion());
                //覆盖测试中的版本
                activeTplVersion.setTestVersion(null);
                activeTplVersion.setTestOpenScope(null);
            }
            activeTplVersion.setModifyBy(newVersionTpl.getModifyBy());

            comfyuiWorkflowTemplateActiveVersionDAO.updateByPrimaryKey(activeTplVersion);

            newVersionTpl.setTemplateDesc(tplDesc);
        }

        return this.insert(newVersionTpl);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activeVersion(Integer id) {
        activeVersion(id, true);
    }

    @Override
    public void rollbackById(Integer id) {
        activeVersion(id, false);
    }

    /**
     * 激活版本
     *
     * @param id           id
     * @param checkVersion 是否验证版本
     */
    private void activeVersion(Integer id, boolean checkVersion) {
        ComfyuiWorkflowTemplateVO template = selectById(id);
        AssertUtil.assertNotNull(template, ResultCode.BIZ_FAIL, "模板不存在");

        ComfyuiWorkflowTemplateActiveVersionVO activeTplVersion
            = comfyuiWorkflowTemplateActiveVersionService.queryByTemplateKey(template.getTemplateKey());

        AssertUtil.assertNotNull(activeTplVersion, ResultCode.SYS_ERROR, "模板版本信息不存在");

        String activeVersion = activeTplVersion.getActiveVersion();
        String targetVersion = template.getVersion();

        if (StringUtils.equals(activeVersion, targetVersion)) {
            log.warn("模板版本激活，当前版本已是正式版本，无需设置，activeVersion={},targetVersion={}", activeVersion,
                targetVersion);
            return;
        }

        if (checkVersion && StringUtils.compare(activeVersion, targetVersion) > 0) {
            log.warn("模板版本激活，当前版本不是最新的版本，请检查，activeVersion={},targetVersion={}", activeVersion,
                targetVersion);
            throw new BizException(ResultCode.BIZ_FAIL, "当前版本非最新版本，请刷新后重试");
        }

        activeTplVersion.setActiveVersion(targetVersion);
        activeTplVersion.setTestVersion(null);
        activeTplVersion.setTestOpenScope(null);
        comfyuiWorkflowTemplateActiveVersionService.updateById(activeTplVersion);
    }
}
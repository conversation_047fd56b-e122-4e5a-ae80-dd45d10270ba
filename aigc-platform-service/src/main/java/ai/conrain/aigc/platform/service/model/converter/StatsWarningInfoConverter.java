package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.StatsWarningInfoDO;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.query.StatsWarningInfoQuery;
import ai.conrain.aigc.platform.dal.example.StatsWarningInfoExample;
import ai.conrain.aigc.platform.service.model.vo.StatsWarningInfoVO;

import com.alibaba.fastjson.JSON;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * StatsWarningInfoConverter
 *
 * @version StatsWarningInfoService.java v 0.1 2025-05-19 04:36:33
 */
public class StatsWarningInfoConverter {

    /**
     * DO -> VO
     */
    public static StatsWarningInfoVO do2VO(StatsWarningInfoDO from) {
        StatsWarningInfoVO to = new StatsWarningInfoVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setWeeklyNoConsumptionRate(from.getWeeklyNoConsumptionRate());
        to.setMonthlyNoConsumptionRate(from.getMonthlyNoConsumptionRate());
        to.setCustomerRefundRateCount(from.getCustomerRefundRateCount());
        to.setDeliveryTimeoutCount(from.getDeliveryTimeoutCount());
        to.setCustomerBalanceAlertCount(from.getCustomerBalanceAlertCount());
        to.setCustomerNotConvertCount(from.getCustomerNotConvertCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        }
        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsWarningInfoDO vo2DO(StatsWarningInfoVO from) {
        StatsWarningInfoDO to = new StatsWarningInfoDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setWeeklyNoConsumptionRate(from.getWeeklyNoConsumptionRate());
        to.setMonthlyNoConsumptionRate(from.getMonthlyNoConsumptionRate());
        to.setCustomerRefundRateCount(from.getCustomerRefundRateCount());
        to.setDeliveryTimeoutCount(from.getDeliveryTimeoutCount());
        to.setCustomerBalanceAlertCount(from.getCustomerBalanceAlertCount());
        to.setCustomerNotConvertCount(from.getCustomerNotConvertCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        // 如果extInfo为空，则添加默认值
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSON.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsWarningInfoQuery do2Query(StatsWarningInfoDO from) {
        StatsWarningInfoQuery to = new StatsWarningInfoQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setWeeklyNoConsumptionRate(from.getWeeklyNoConsumptionRate());
        to.setMonthlyNoConsumptionRate(from.getMonthlyNoConsumptionRate());
        to.setCustomerRefundRateCount(from.getCustomerRefundRateCount());
        to.setDeliveryTimeoutCount(from.getDeliveryTimeoutCount());
        to.setCustomerBalanceAlertCount(from.getCustomerBalanceAlertCount());
        to.setCustomerNotConvertCount(from.getCustomerNotConvertCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsWarningInfoDO query2DO(StatsWarningInfoQuery from) {
        StatsWarningInfoDO to = new StatsWarningInfoDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setWeeklyNoConsumptionRate(from.getWeeklyNoConsumptionRate());
        to.setMonthlyNoConsumptionRate(from.getMonthlyNoConsumptionRate());
        to.setCustomerRefundRateCount(from.getCustomerRefundRateCount());
        to.setDeliveryTimeoutCount(from.getDeliveryTimeoutCount());
        to.setCustomerBalanceAlertCount(from.getCustomerBalanceAlertCount());
        to.setCustomerNotConvertCount(from.getCustomerNotConvertCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsWarningInfoExample query2Example(StatsWarningInfoQuery from) {
        StatsWarningInfoExample to = new StatsWarningInfoExample();
        StatsWarningInfoExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getWeeklyNoConsumptionRate())) {
            c.andWeeklyNoConsumptionRateEqualTo(from.getWeeklyNoConsumptionRate());
        }
        if (!ObjectUtils.isEmpty(from.getMonthlyNoConsumptionRate())) {
            c.andMonthlyNoConsumptionRateEqualTo(from.getMonthlyNoConsumptionRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerRefundRateCount())) {
            c.andCustomerRefundRateCountEqualTo(from.getCustomerRefundRateCount());
        }
        if (!ObjectUtils.isEmpty(from.getDeliveryTimeoutCount())) {
            c.andDeliveryTimeoutCountEqualTo(from.getDeliveryTimeoutCount());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerBalanceAlertCount())) {
            c.andCustomerBalanceAlertCountEqualTo(from.getCustomerBalanceAlertCount());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerNotConvertCount())) {
            c.andCustomerNotConvertCountEqualTo(from.getCustomerNotConvertCount());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<StatsWarningInfoVO> doList2VOList(List<StatsWarningInfoDO> list) {
        return CommonUtil.listConverter(list, StatsWarningInfoConverter::do2VO);
    }
}
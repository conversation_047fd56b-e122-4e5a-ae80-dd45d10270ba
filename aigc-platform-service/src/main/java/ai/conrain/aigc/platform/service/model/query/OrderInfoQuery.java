package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.validation.SimpleDate;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * OrderInfoQuery
 *
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
@Data
public class OrderInfoQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 订单号 */
    private String orderNo;

    /** 关联的主用户id */
    private Integer masterUserId;

    private List<Integer> masterUserIds;

    /** 关联的主用户昵称快照 */
    private String masterUserNick;

    /** 关联的主用户登录账号快照 */
    private String masterUserLoginId;

    /** 关联的操作员用户id */
    private Integer operatorUserId;

    /** 关联的操作员用户昵称快照 */
    private String operatorUserNick;

    /** 关联的操作员用户登录账号快照 */
    private String operatorUserLoginId;

    /** 订单原始金额，不可修改（单位元） */
    private BigDecimal originalAmount;

    /** 订单支付金额 */
    private BigDecimal payAmount;

    /** 支付信息 */
    private String payDetail;

    /** 订单状态 */
    private String orderStatus;

    /** 产品码 */
    private String productCode;

    /** 产品详情 */
    private String productDetail;

    /** 订单完结时间 */
    private Date finishTime;

    /** 扩展信息 */
    private String extInfo;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    // 起始日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateFrom;

    // 截止日期
    @SimpleDate(message = "yyyyMMdd格式")
    private String dateTo;

    /** 开始时间 */
    private String startDate;

    /** 结束时间 */
    private String endDate;

    /** 大于金额 */
    private BigDecimal bigThanAmount;

}

package ai.conrain.aigc.platform.service.config;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@EnableAsync
@Configuration
public class AsyncConfiguration implements AsyncConfigurer {

    /**
     * 获取异步线程池执行对象
     */
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(4);
        taskExecutor.setMaxPoolSize(100);
        taskExecutor.setQueueCapacity(200);
        taskExecutor.setThreadNamePrefix("async-task");
        taskExecutor.setTaskDecorator(new AsyncTaskContextDecorator());
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();
        return taskExecutor;
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new AsyncExceptionHandler();
    }

    static class AsyncExceptionHandler implements AsyncUncaughtExceptionHandler {
        @Override
        public void handleUncaughtException(@NotNull Throwable throwable, Method method, Object @NotNull ... obj) {
            log.info("Method name:{}, params:{}", method.getName(), JSONArray.toJSONString(obj));
            log.error("async task exception", throwable);
        }
    }

    static class AsyncTaskContextDecorator implements TaskDecorator {
        @Override
        public @NotNull Runnable decorate(@NotNull Runnable runnable) {
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            OperationContext ctx = OperationContextHolder.getContext();
            //如果为空时，初始化一下，防止出现空指针异常
            Map<String, String> origin = MDC.getCopyOfContextMap();
            if (origin == null) {
                MDC.put("traceId", CommonUtil.uuid());
                MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));
            }

            Map<String, String> mdc = MDC.getCopyOfContextMap();
            try {
                return () -> {
                    try {
                        RequestContextHolder.setRequestAttributes(attributes);
                        OperationContextHolder.setContext(ctx);
                        MDC.setContextMap(mdc);
                        runnable.run();
                    } finally {
                        RequestContextHolder.resetRequestAttributes();
                        OperationContextHolder.clean();
                        MDC.clear();
                    }
                };
            } catch (Exception e) {
                return runnable;
            }
        }
    }
}


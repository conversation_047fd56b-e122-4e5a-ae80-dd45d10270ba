package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.InvoiceInfoDO;
import ai.conrain.aigc.platform.service.enums.InvoiceStatus;
import ai.conrain.aigc.platform.service.model.query.InvoiceInfoQuery;
import ai.conrain.aigc.platform.dal.example.InvoiceInfoExample;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * InvoiceInfoConverter
 *
 * @version InvoiceInfoService.java v 0.1 2024-06-27 01:42:09
 */
public class InvoiceInfoConverter {

    /**
     * DO -> VO
     */
    public static InvoiceInfoVO do2VO(InvoiceInfoDO from) {
        InvoiceInfoVO to = new InvoiceInfoVO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setStatus(from.getStatus());
        to.setApplyTime(from.getApplyTime());
        to.setFinishTime(from.getFinishTime());
        to.setInvoiceNo(from.getInvoiceNo());
        to.setAmountNoTax(from.getAmountNoTax());
        to.setTaxRate(from.getTaxRate());
        to.setTaxAmount(from.getTaxAmount());
        to.setAmountWithTax(from.getAmountWithTax());
        to.setInvoiceTaskThirdPlatform(from.getInvoiceTaskThirdPlatform());
        to.setInvoiceTaskThirdReqId(from.getInvoiceTaskThirdReqId());
        to.setInvoiceTaskDetail(from.getInvoiceTaskDetail());
        to.setInvoiceDownloadUrl(from.getInvoiceDownloadUrl());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        if (StringUtils.isNotBlank(from.getStatus()) && InvoiceStatus.getByCode(from.getStatus()) != null) {
            to.setStatusName(InvoiceStatus.getByCode(from.getStatus()).getDesc());
        }

        to.setInnerInvoiceNo(from.getInnerInvoiceNo());
        to.setNegativeInvoiceNo(from.getNegativeInvoiceNo());
        to.setNegativeInvoiceDetail(from.getNegativeInvoiceDetail());

        return to;
    }

    /**
     * VO -> DO
     */
    public static InvoiceInfoDO vo2DO(InvoiceInfoVO from) {
        InvoiceInfoDO to = new InvoiceInfoDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setStatus(from.getStatus());
        to.setApplyTime(from.getApplyTime());
        to.setFinishTime(from.getFinishTime());
        to.setInvoiceNo(from.getInvoiceNo());
        to.setAmountNoTax(from.getAmountNoTax());
        to.setTaxRate(from.getTaxRate());
        to.setTaxAmount(from.getTaxAmount());
        to.setAmountWithTax(from.getAmountWithTax());
        to.setInvoiceTaskThirdPlatform(from.getInvoiceTaskThirdPlatform());
        to.setInvoiceTaskThirdReqId(from.getInvoiceTaskThirdReqId());
        to.setInvoiceTaskDetail(from.getInvoiceTaskDetail());
        to.setInvoiceDownloadUrl(from.getInvoiceDownloadUrl());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setInnerInvoiceNo(from.getInnerInvoiceNo());
        to.setNegativeInvoiceNo(from.getNegativeInvoiceNo());
        to.setNegativeInvoiceDetail(from.getNegativeInvoiceDetail());

        return to;
    }

    /**
     * DO -> Query
     */
    public static InvoiceInfoQuery do2Query(InvoiceInfoDO from) {
        InvoiceInfoQuery to = new InvoiceInfoQuery();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setStatus(from.getStatus());
        to.setApplyTime(from.getApplyTime());
        to.setFinishTime(from.getFinishTime());
        to.setInvoiceNo(from.getInvoiceNo());
        to.setAmountNoTax(from.getAmountNoTax());
        to.setTaxRate(from.getTaxRate());
        to.setTaxAmount(from.getTaxAmount());
        to.setAmountWithTax(from.getAmountWithTax());
        to.setInvoiceTaskThirdPlatform(from.getInvoiceTaskThirdPlatform());
        to.setInvoiceTaskThirdReqId(from.getInvoiceTaskThirdReqId());
        to.setInvoiceTaskDetail(from.getInvoiceTaskDetail());
        to.setInvoiceDownloadUrl(from.getInvoiceDownloadUrl());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setInnerInvoiceNo(from.getInnerInvoiceNo());
        to.setNegativeInvoiceNo(from.getNegativeInvoiceNo());
        to.setNegativeInvoiceDetail(from.getNegativeInvoiceDetail());

        return to;
    }

    /**
     * Query -> DO
     */
    public static InvoiceInfoDO query2DO(InvoiceInfoQuery from) {
        InvoiceInfoDO to = new InvoiceInfoDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setInvoiceType(from.getInvoiceType());
        to.setSubjectType(from.getSubjectType());
        to.setSubjectName(from.getSubjectName());
        to.setCreditCode(from.getCreditCode());
        to.setBusinessAddress(from.getBusinessAddress());
        to.setBusinessPhone(from.getBusinessPhone());
        to.setBankName(from.getBankName());
        to.setBankAccount(from.getBankAccount());
        to.setStatus(from.getStatus());
        to.setApplyTime(from.getApplyTime());
        to.setFinishTime(from.getFinishTime());
        to.setInvoiceNo(from.getInvoiceNo());
        to.setAmountNoTax(from.getAmountNoTax());
        to.setTaxRate(from.getTaxRate());
        to.setTaxAmount(from.getTaxAmount());
        to.setAmountWithTax(from.getAmountWithTax());
        to.setInvoiceTaskThirdPlatform(from.getInvoiceTaskThirdPlatform());
        to.setInvoiceTaskThirdReqId(from.getInvoiceTaskThirdReqId());
        to.setInvoiceTaskDetail(from.getInvoiceTaskDetail());
        to.setInvoiceDownloadUrl(from.getInvoiceDownloadUrl());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setInnerInvoiceNo(from.getInnerInvoiceNo());
        to.setNegativeInvoiceNo(from.getNegativeInvoiceNo());
        to.setNegativeInvoiceDetail(from.getNegativeInvoiceDetail());

        return to;
    }


    /**
     * Query -> Example
     */
    public static InvoiceInfoExample query2Example(InvoiceInfoQuery from) {
        InvoiceInfoExample to = new InvoiceInfoExample();
        InvoiceInfoExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserId())) {
            c.andMasterUserIdEqualTo(from.getMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserNick())) {
            c.andMasterUserNickEqualTo(from.getMasterUserNick());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserLoginId())) {
            c.andMasterUserLoginIdEqualTo(from.getMasterUserLoginId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserId())) {
            c.andOperatorUserIdEqualTo(from.getOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserNick())) {
            c.andOperatorUserNickEqualTo(from.getOperatorUserNick());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserLoginId())) {
            c.andOperatorUserLoginIdEqualTo(from.getOperatorUserLoginId());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceType())) {
            c.andInvoiceTypeEqualTo(from.getInvoiceType());
        }
        if (!ObjectUtils.isEmpty(from.getSubjectType())) {
            c.andSubjectTypeEqualTo(from.getSubjectType());
        }
        if (!ObjectUtils.isEmpty(from.getSubjectName())) {
            c.andSubjectNameEqualTo(from.getSubjectName());
        }
        if (!ObjectUtils.isEmpty(from.getCreditCode())) {
            c.andCreditCodeEqualTo(from.getCreditCode());
        }
        if (!ObjectUtils.isEmpty(from.getBusinessAddress())) {
            c.andBusinessAddressEqualTo(from.getBusinessAddress());
        }
        if (!ObjectUtils.isEmpty(from.getBusinessPhone())) {
            c.andBusinessPhoneEqualTo(from.getBusinessPhone());
        }
        if (!ObjectUtils.isEmpty(from.getBankName())) {
            c.andBankNameEqualTo(from.getBankName());
        }
        if (!ObjectUtils.isEmpty(from.getBankAccount())) {
            c.andBankAccountEqualTo(from.getBankAccount());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getApplyTime())) {
            c.andApplyTimeEqualTo(from.getApplyTime());
        }
        if (!ObjectUtils.isEmpty(from.getFinishTime())) {
            c.andFinishTimeEqualTo(from.getFinishTime());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceNo())) {
            c.andInvoiceNoEqualTo(from.getInvoiceNo());
        }
        if (!ObjectUtils.isEmpty(from.getAmountNoTax())) {
            c.andAmountNoTaxEqualTo(from.getAmountNoTax());
        }
        if (!ObjectUtils.isEmpty(from.getTaxRate())) {
            c.andTaxRateEqualTo(from.getTaxRate());
        }
        if (!ObjectUtils.isEmpty(from.getTaxAmount())) {
            c.andTaxAmountEqualTo(from.getTaxAmount());
        }
        if (!ObjectUtils.isEmpty(from.getAmountWithTax())) {
            c.andAmountWithTaxEqualTo(from.getAmountWithTax());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceTaskThirdPlatform())) {
            c.andInvoiceTaskThirdPlatformEqualTo(from.getInvoiceTaskThirdPlatform());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceTaskThirdReqId())) {
            c.andInvoiceTaskThirdReqIdEqualTo(from.getInvoiceTaskThirdReqId());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceTaskDetail())) {
            c.andInvoiceTaskDetailEqualTo(from.getInvoiceTaskDetail());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceDownloadUrl())) {
            c.andInvoiceDownloadUrlEqualTo(from.getInvoiceDownloadUrl());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getInnerInvoiceNo())) {
            c.andInnerInvoiceNoEqualTo(from.getInnerInvoiceNo());
        }

        if (!ObjectUtils.isEmpty(from.getNegativeInvoiceNo())) {
            c.andNegativeInvoiceNoEqualTo(from.getNegativeInvoiceNo());
        }

        //逻辑删除过滤
        for (InvoiceInfoExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<InvoiceInfoVO> doList2VOList(List<InvoiceInfoDO> list) {
        return CommonUtil.listConverter(list, InvoiceInfoConverter::do2VO);
    }
}
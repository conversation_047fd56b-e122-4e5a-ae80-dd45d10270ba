package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.stats.DataStatsJobConfig;
import ai.conrain.aigc.platform.service.util.DateUtils;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractDataStatsServiceImpl implements DataStatsService {

    /**
     * 执行特定统计类型的具体业务逻辑
     *
     * @param storageDate 存储日期
     * @param startDate   统计开始日期
     * @param endDate     统计结束日期
     * @param periodEnum  统计周期
     * @param isTotal     是否计算所有用户汇总
     * @return 统计结果
     */
    protected abstract int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                                boolean isTotal);

    /**
     * 数据统计
     *
     * @param date       日期
     * @param periodEnum 统计周期
     * @return 变更条数
     */
    private int saveDataStats(Date date, StatsPeriodEnum periodEnum) {
        String storageDate = periodEnum.getStorageDateStr(date);
        // 构建开始和结束时间
        String startDate = periodEnum.getStartDateStr(date);
        String endDate = periodEnum.getEndDateStr(date);

        // 执行业务逻辑
        return executeBusinessStats(storageDate, startDate, endDate, periodEnum, false);
    }

    /**
     * 保存时间周期数据统计
     *
     * @param date       日期
     * @param periodEnum 统计周期
     * @return 变更条数
     */
    private int saveTimePeriodDataStats(Date date, StatsPeriodEnum periodEnum) {
        String storageDate = periodEnum.getStorageDateStr(date);
        // 构建开始和结束时间
        String startDate = periodEnum.getStartDateStr(date);
        String endDate = periodEnum.getEndDateStr(date);

        // 执行业务逻辑
        return executeBusinessStats(storageDate, startDate, endDate, periodEnum, true);
    }

    /**
     * 执行数据统计
     *
     * @param config 数据统计配置
     * @return 数据统计结果
     */
    @Override
    public Map<String, Integer> executeDataStats(DataStatsJobConfig config) {
        log.info("【数据报表统计】【{}】开始执行，日期范围: {} 至 {}",
                getStatsType().getCode(),
                DateUtils.formatSimpleDate(config.getStartDate()),
                DateUtils.formatSimpleDate(config.getEndDate()));

        // 初始化结果参数
        Map<String, Integer> results = new HashMap<>();

        // 需要处理的时间范围
        Calendar cal = Calendar.getInstance();
        cal.setTime(config.getStartDate());
        Date endDate = config.getEndDate();

        // 收集需要处理的周和月
        Set<String> weeksToProcess = new HashSet<>();
        Set<String> monthsToProcess = new HashSet<>();
        Set<String> quartersToProcess = new HashSet<>();

        // 1、统计每天的数据
        log.info("【数据报表统计】【{}】【日统计】开始处理每日数据", getStatsType().getCode());
        executeDailyStats(config, results, cal, endDate, weeksToProcess, monthsToProcess, quartersToProcess);

        // 2、数据不为空且需要周统计
        if (!weeksToProcess.isEmpty() && getStatsType().getPeriodTypeList().contains(StatsPeriodEnum.WEEKLY)) {
            log.info("【数据报表统计】【{}】【周统计】开始处理周数据，共{}个周", getStatsType().getCode(), weeksToProcess.size());
            executeWeeklyStats(config, results, weeksToProcess);
        }

        // 3、数据不为空且需要月统计
        if (!monthsToProcess.isEmpty() && getStatsType().getPeriodTypeList().contains(StatsPeriodEnum.MONTHLY)) {
            log.info("【数据报表统计】【{}】【月统计】开始处理月数据，共{}个月", getStatsType().getCode(), monthsToProcess.size());
            executeMonthlyStats(config, results, monthsToProcess);
        }

        // 4、数据不为空且需要季度统计
        if (!quartersToProcess.isEmpty() && getStatsType().getPeriodTypeList().contains(StatsPeriodEnum.QUARTERLY)) {
            log.info("【数据报表统计】【{}】【季度统计】开始处理季度数据，共{}个季度", getStatsType().getCode(), quartersToProcess.size());
            executeQuarterlyStats(config, results, quartersToProcess);
        }

        log.info("【数据报表统计】【{}】执行完成", getStatsType().getCode());
        return results;
    }

    /**
     * 执行每日数据统计
     *
     * @param config          数据统计配置
     * @param results         结果参数
     * @param cal             日期计算器
     * @param endDate         结束日期
     * @param weeksToProcess  需要处理的周
     * @param monthsToProcess 需要处理的月
     */
    private void executeDailyStats(DataStatsJobConfig config, Map<String, Integer> results, Calendar cal, Date endDate,
                                   Set<String> weeksToProcess, Set<String> monthsToProcess, Set<String> quartersToProcess) {
        // 计算总天数
        Calendar tempCalendar = Calendar.getInstance();
        tempCalendar.setTime(cal.getTime());
        int totalDays = 0;
        while (!tempCalendar.getTime().after(endDate)) {
            totalDays++;
            tempCalendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        int processedDays = 0;
        
        while (!cal.getTime().after(endDate)) {
            Date currentDate = cal.getTime();
            String dateStr = DateUtils.formatSimpleDate(currentDate);
            
            processedDays++;
            if (processedDays % 10 == 0 || processedDays == totalDays) {
                log.info("【数据报表统计】【{}】【日统计】目前统计{}/{}天，还有{}天待统计",
                        getStatsType().getCode(), processedDays, totalDays, totalDays - processedDays);
            }

            // 处理日统计
            if (config.isAllowedPeriod(StatsPeriodEnum.DAILY)) {
                // 每日统计
                int count = saveDataStats(currentDate, StatsPeriodEnum.DAILY);
                results.put("daily_" + dateStr, count);

                // 是否需要每天所有数据汇总
                if (getStatsType().getIsNeedSummary()) {
                    int countAll = saveTimePeriodDataStats(currentDate, StatsPeriodEnum.DAILY);
                    count += countAll;
                    results.put("all_daily_" + dateStr, countAll);
                }

                results.put("daily", results.getOrDefault("daily", 0) + count);
            }

            // 处理总计统计
            if (config.isAllowedPeriod(StatsPeriodEnum.TOTAL)) {
                // 单日汇总
                int count = saveDataStats(currentDate, StatsPeriodEnum.TOTAL);
                results.put("total_" + dateStr, count);

                // 汇总当日所有数据
                if (getStatsType().getIsNeedTotalSummary()) {
                    int countAll = saveTimePeriodDataStats(currentDate, StatsPeriodEnum.TOTAL);
                    count += countAll;
                    results.put("all_total_" + dateStr, countAll);
                }

                results.put("total", results.getOrDefault("total", 0) + count);
            }

            // 收集周标识 - 只在处理周的周一日期时添加
            if (config.isAllowedPeriod(StatsPeriodEnum.WEEKLY)) {
                Calendar weekCal = Calendar.getInstance();
                weekCal.setTime(currentDate);
                // 判断是否为周一（Calendar.MONDAY = 2）
                if (weekCal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                    String weekKey = DateUtils.getWeekKey(currentDate);
                    weeksToProcess.add(weekKey);
                }
            }

            // 收集月标识 - 只在处理月初日期时添加
            if (config.isAllowedPeriod(StatsPeriodEnum.MONTHLY)) {
                Calendar monthCal = Calendar.getInstance();
                monthCal.setTime(currentDate);
                // 获取当前月份的最后一天
                int lastDay = monthCal.getActualMaximum(Calendar.DAY_OF_MONTH);
                // 判断是否为每月最后一天
                if (monthCal.get(Calendar.DAY_OF_MONTH) == lastDay) {
                    String monthKey = DateUtils.getMonthKey(currentDate);
                    monthsToProcess.add(monthKey);
                }
            }

            // 收集季度标识 - 只在处理季度末日期时添加
            if (config.isAllowedPeriod(StatsPeriodEnum.QUARTERLY)) {
                Calendar quarterCal = Calendar.getInstance();
                quarterCal.setTime(currentDate);
                int month = quarterCal.get(Calendar.MONTH);
                // 判断是否为季度的最后一个月
                boolean isLastMonthOfQuarter = month == Calendar.MARCH || month == Calendar.JUNE || 
                                             month == Calendar.SEPTEMBER || month == Calendar.DECEMBER;
                // 获取当月的最后一天
                int lastDay = quarterCal.getActualMaximum(Calendar.DAY_OF_MONTH);
                // 判断是否为季度最后一个月的最后一天
                if (isLastMonthOfQuarter && quarterCal.get(Calendar.DAY_OF_MONTH) == lastDay) {
                    String quarterKey = DateUtils.getQuarterKey(currentDate);
                    quartersToProcess.add(quarterKey);
                }
            }

            // 移动到下一天
            cal.add(Calendar.DAY_OF_MONTH, 1);
        }
        log.info("【数据报表统计】【{}】每日数据处理完成", getStatsType().getCode());
    }

    /**
     * 执行每周数据统计
     *
     * @param config         数据统计配置
     * @param results        结果参数
     * @param weeksToProcess 需要处理的周
     */
    private void executeWeeklyStats(DataStatsJobConfig config, Map<String, Integer> results,
                                    Set<String> weeksToProcess) {
        // 处理周统计
        int totalWeeks = weeksToProcess.size();
        int processedWeeks = 0;
        
        for (String weekKey : weeksToProcess) {
            try {
                processedWeeks++;
                if (processedWeeks % 5 == 0 || processedWeeks == totalWeeks) {
                    log.info("【数据报表统计】【{}】【周统计】目前统计{}/{}周，还有{}周待统计",
                            getStatsType().getCode(), processedWeeks, totalWeeks, totalWeeks - processedWeeks);
                }
                
                Date weekDate = DateUtils.parseWeekKey(weekKey);
                // 单周统计
                int count = saveDataStats(weekDate, StatsPeriodEnum.WEEKLY);
                results.put("weekly_" + weekKey, count);

                // 是否需要单周所有数据汇总
                if (getStatsType().getIsNeedSummary()) {
                    int countAll = saveTimePeriodDataStats(weekDate, StatsPeriodEnum.WEEKLY);
                    count += countAll;
                    results.put("all_weekly_" + weekKey, countAll);
                }

                results.put("weekly", results.getOrDefault("weekly", 0) + count);
            } catch (Exception e) {
                log.error("【数据报表统计】【{}】周份统计处理周统计异常，周: {}", getStatsType().getCode(), weekKey, e);
            }
        }
        log.info("【数据报表统计】【{}】周数据处理完成", getStatsType().getCode());
    }

    /**
     * 执行每月数据统计
     *
     * @param config          数据统计配置
     * @param results         结果参数
     * @param monthsToProcess 需要处理的月
     */
    private void executeMonthlyStats(DataStatsJobConfig config, Map<String, Integer> results,
                                     Set<String> monthsToProcess) {
        // 处理月统计
        int totalMonths = monthsToProcess.size();
        int processedMonths = 0;
        
        for (String monthKey : monthsToProcess) {
            try {
                processedMonths++;
                if (processedMonths % 3 == 0 || processedMonths == totalMonths) {
                    log.info("【数据报表统计】【{}】【月统计】目前统计{}/{}月，还有{}月待统计",
                            getStatsType().getCode(), processedMonths, totalMonths, totalMonths - processedMonths);
                }
                
                Date monthDate = DateUtils.parseMonthKey(monthKey);

                // 每月统计
                int count = saveDataStats(monthDate, StatsPeriodEnum.MONTHLY);
                results.put("monthly_" + monthKey, count);

                // 是否需要每月所有数据汇总
                if (getStatsType().getIsNeedSummary()) {
                    int countAll = saveTimePeriodDataStats(monthDate, StatsPeriodEnum.MONTHLY);
                    count += countAll;
                    results.put("all_monthly_" + monthKey, countAll);
                }
                results.put("monthly", results.getOrDefault("monthly", 0) + count);
            } catch (Exception e) {
                log.error("【数据报表统计】【{}】月份统计处理月统计异常，月: {}", getStatsType().getCode(), monthKey, e);
            }
        }
        log.info("【数据报表统计】【{}】月数据处理完成", getStatsType().getCode());
    }

    /**
     * 执行每季度数据统计
     *
     * @param config            数据统计配置
     * @param results           结果参数
     * @param quartersToProcess 需要处理的季度
     */
    private void executeQuarterlyStats(DataStatsJobConfig config, Map<String, Integer> results,
                                       Set<String> quartersToProcess) {
        // 处理季度统计
        int totalQuarters = quartersToProcess.size();
        int processedQuarters = 0;
        
        for (String quarterKey : quartersToProcess) {
            try {
                processedQuarters++;
                log.info("【数据报表统计】【{}】【季度统计】目前统计{}/{}季度，还有{}季度待统计",
                        getStatsType().getCode(), processedQuarters, totalQuarters, totalQuarters - processedQuarters);
                
                Date quarterDate = DateUtils.parseQuarterKey(quarterKey);

                // 每季度统计
                int count = saveDataStats(quarterDate, StatsPeriodEnum.QUARTERLY);
                results.put("quarterly_" + quarterKey, count);

                // 是否需要季度所有数据汇总
                if (getStatsType().getIsNeedSummary()) {
                    int countAll = saveTimePeriodDataStats(quarterDate, StatsPeriodEnum.QUARTERLY);
                    count += countAll;
                    results.put("all_quarterly_" + quarterKey, countAll);
                }
                results.put("quarterly", results.getOrDefault("quarterly", 0) + count);
            } catch (Exception e) {
                log.error("【数据报表统计】【{}】季度统计处理季度统计异常，季度: {}", getStatsType().getCode(), quarterKey, e);
            }
        }
        log.info("【数据报表统计】【{}】季度数据处理完成", getStatsType().getCode());
    }
}

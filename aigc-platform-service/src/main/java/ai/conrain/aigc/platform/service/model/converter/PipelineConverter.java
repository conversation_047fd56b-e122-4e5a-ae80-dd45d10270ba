package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.PipelineDO;
import ai.conrain.aigc.platform.service.model.query.PipelineQuery;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSONObject;

/**
 * PipelineConverter
 *
 * @version PipelineService.java v 0.1 2024-06-15 05:51:42
 */
public class PipelineConverter {

    /**
     * DO -> VO
     */
    public static PipelineVO do2VO(PipelineDO from) {
        PipelineVO to = new PipelineVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setUserRelation(JSONObject.parseObject(from.getUserRelation()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static PipelineDO vo2DO(PipelineVO from) {
        PipelineDO to = new PipelineDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setUserRelation(from.getUserRelation() != null ? from.getUserRelation().toString() : null);
        to.setDeleted(false);

        return to;
    }

    /**
     * DO -> Query
     */
    public static PipelineQuery do2Query(PipelineDO from) {
        PipelineQuery to = new PipelineQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setUserRelation(from.getUserRelation());

        return to;
    }

    /**
     * Query -> DO
     */
    public static PipelineDO query2DO(PipelineQuery from) {
        PipelineDO to = new PipelineDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setUserRelation(from.getUserRelation());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<PipelineVO> doList2VOList(List<PipelineDO> list) {
        return CommonUtil.listConverter(list, PipelineConverter::do2VO);
    }
}
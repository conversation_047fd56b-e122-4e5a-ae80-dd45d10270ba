/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.ImageUpscaleRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * 图片放大创作服务
 *
 * <AUTHOR>
 * @version : ImageUpscaleCreativeService.java, v 0.1 2024/7/18 15:26 renxiao.wu Exp $
 */
@Service
public class ImageUpscaleCreativeService extends AbstractCreativeService<ImageUpscaleRequest> {
    @Autowired
    private OssService ossService;
    @Autowired
    private ComfyUIService comfyUIService;
    @Value("${comfyui.output.path}")
    private String outputPath;
    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.IMAGE_UPSCALE;
    }

    @Override
    protected CreativeBatchVO buildData(ImageUpscaleRequest request, MaterialModelVO modelVO) throws IOException {
        String upscaleImage = request.getUpscaleImage();
        Integer taskId = null;
        String imageProportion = null;

        //优先取task中的图片
        CreativeTaskVO task = null;
        if (request.getTaskId() != null) {
            task = creativeTaskService.selectById(request.getTaskId());
            AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "创作任务不存在");

            taskId = task.getId();
            if (StringUtils.isBlank(upscaleImage)) {
                upscaleImage = task.getResultImages().get(0);
            }
            imageProportion = task.getImageProportion();
        }

        //1.1.从oss中获取原始图片
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(upscaleImage);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        BufferedImage originalImage = ImageIO.read(new File(tmpUrl));

        //2.上传到comfyui的input下
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(originalImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + ".png";

        String comfyuiPath = ComfyUIUtils.buildInputPath(OperationContextHolder.getMasterUserId()) + imageName;
        fileDispatch.uploadFile(comfyuiPath, new ByteArrayInputStream(imageBytes),
            OperationContextHolder.getMasterUserId());

        if (imageProportion == null) {
            imageProportion = originalImage.getWidth() == originalImage.getHeight()
                ? ProportionTypeEnum.ONE_ONE.getCode() : ProportionTypeEnum.THREE_FOUR.getCode();
        }

        //3.生成创作批次记录
        boolean isUpload = StringUtils.equalsIgnoreCase(UPLOAD, request.getImageSource());
        return buildBatchData(task, upscaleImage, comfyuiPath, imageProportion, isUpload);
    }

    /**
     * 构建修复手势的批次信息
     *
     * @param task         任务id
     * @param upscaleImage    放大图片的oss地址
     * @param comfyuiPath     放大图片的地址
     * @param imageProportion 图片的比例
     * @param isUpload        是否上传
     * @return 批次信息
     */
    @NotNull
    private CreativeBatchVO buildBatchData(CreativeTaskVO task, String upscaleImage, String comfyuiPath,
                                           String imageProportion, boolean isUpload) {
        CreativeBatchVO data = new CreativeBatchVO();
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setShowImage(upscaleImage);
        data.setImageProportion(imageProportion);
        Integer taskId = ObjectUtils.isNotEmpty(task) ? task.getId() : null;

        data.addExtInfo(KEY_TARGET_IMAGE, comfyuiPath);
        data.addExtInfo(KEY_ORIGIN_IMAGE, upscaleImage);
        data.addExtInfo(KEY_ORIGIN_TASK, taskId);
        if (!isUpload) {
            data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setBatchCnt(1);

        data.setType(getCreativeType());

        batchFillHelper.fillOriginBatchInfo(upscaleImage, taskId, data);
        return data;
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(KEY_TARGET_IMAGE, task.getStringFromExtInfo(KEY_TARGET_IMAGE));
        context.put(KEY_RESCALE_FACTOR, 4);
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        super.fillTaskExt(target, batch, elements, idx);
        target.addExtInfo(KEY_TARGET_IMAGE, batch.getStringFromExtInfo(KEY_TARGET_IMAGE));
    }
}

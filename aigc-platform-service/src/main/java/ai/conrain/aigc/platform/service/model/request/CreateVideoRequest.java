/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NonNull;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 图生视频请求
 *
 * <AUTHOR>
 * @version : CreateVideoRequest.java, v 0.1 2024/8/9 10:55 renxiao.wu Exp $
 */
@Data
public class CreateVideoRequest implements CreativeRequest {
    private static final long serialVersionUID = -5057724159942916048L;
    /** 标题 */
    @Length(max = 32, message = "标题长度不能超过32个字符")
    private String title;
    /** 生视频的图片列表 */
    @NonNull
    @Size(min = 1)
    private List<String> images;

    //视频时长，缺省为5秒
    private Integer timeSecs4Video;

    @JsonCreator
    public CreateVideoRequest(@JsonProperty("images") List<String> images) {
        this.images = images;
    }
}

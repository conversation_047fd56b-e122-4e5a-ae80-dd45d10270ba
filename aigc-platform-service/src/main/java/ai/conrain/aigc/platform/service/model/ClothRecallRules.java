package ai.conrain.aigc.platform.service.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 所有规则查询结果封装类
 * 用于一次性返回样式白名单、功能白名单和构图黑名单的查询结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClothRecallRules {

    /**
     * 样式白名单规则
     */
    private Map<String, List<String>> styleWhitelistRules;

    /**
     * 功能白名单规则
     */
    private Map<String, List<String>> functionWhitelistRules;

    /**
     * 构图黑名单规则
     */
    private Map<String, List<String>> compositionBlacklistRules;
}
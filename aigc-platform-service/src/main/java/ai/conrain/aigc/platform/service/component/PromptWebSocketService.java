/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component;

/**
 * prompt websocket服务
 *
 * <AUTHOR>
 * @version : WebSocketClientManager.java, v 0.1 2024/5/13 14:52 renxiao.wu Exp $
 */
public interface PromptWebSocketService {
    /**
     * 开始监听prompt任务节点状态
     *
     * @param clientId 客户端id
     * @param url      服务地址
     */
    void startListening(String clientId, String url);
}

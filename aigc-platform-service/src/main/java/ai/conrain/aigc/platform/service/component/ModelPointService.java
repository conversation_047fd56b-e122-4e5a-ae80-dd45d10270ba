package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.ModelPointQuery;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 模型套餐积分 Service定义
 *
 * <AUTHOR>
 * @version ModelPointService.java v 0.1 2024-06-21 12:01:15
 */
public interface ModelPointService {
    /**
     * 查询模型套餐积分对象
     *
     * @param modelId 模型id
     * @param userId  用户id
     * @return 返回结果
     */
    ModelPointVO selectByModelId(Integer modelId, Integer userId);

    /**
     * 根据模型id锁记录
     *
     * @param modelId 模型id
     * @param userId  用户id
     * @return 返回结果
     */
    ModelPointVO lockByModelId(Integer modelId, Integer userId);

    /**
     * 修改模型套餐积分对象
     *
     * @param modelPoint 对象参数
     */
    void updateByIdSelective(ModelPointVO modelPoint);

    /**
     * 带条件批量查询模型套餐积分列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<ModelPointVO> queryModelPointList(ModelPointQuery query);

    /**
     * 带条件查询模型套餐积分数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryModelPointCount(ModelPointQuery query);

    /**
     * 带条件分页查询模型套餐积分
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<ModelPointVO> queryModelPointByPage(ModelPointQuery query);

    /**
     * 在创建服装lora时进行充值
     *
     * @param modelId 模型id
     * @param userId  用户id
     */
    void init(Integer modelId, Integer userId);

    /**
     * 批量查询某个用户id下的模型数据
     *
     * @param modelIds   模型id列表
     * @param userId 用户id
     * @return 结果
     */
    List<ModelPointVO> batchQueryByModels(List<Integer> modelIds, Integer userId);
}
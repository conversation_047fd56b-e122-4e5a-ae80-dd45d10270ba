package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionAttributeDO;
import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.dal.example.CaptionAttributeExample;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * CaptionAttributeConverter
 *
 * @version CaptionAttributeService.java v 0.1 2025-08-14 07:49:02
 */
public class CaptionAttributeConverter {

    /**
     * DO -> VO
     */
    public static CaptionAttributeVO do2VO(CaptionAttributeDO from) {
        CaptionAttributeVO to = new CaptionAttributeVO();
        to.setId(from.getId());
        to.setKey(from.getKey());
        to.setName(from.getName());
        to.setDisplayName(from.getDisplayName());
        to.setEnumerations(from.getEnumerations());
        to.setDisplayEnumerations(from.getDisplayEnumerations());
        to.setIsActive(from.getIsActive());
        to.setSortOrder(from.getSortOrder());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setType(from.getType());
        to.setFormat(from.getFormat());
        to.setDisabled(from.getDisabled());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CaptionAttributeDO vo2DO(CaptionAttributeVO from) {
        CaptionAttributeDO to = new CaptionAttributeDO();
        to.setId(from.getId());
        to.setKey(from.getKey());
        to.setName(from.getName());
        to.setDisplayName(from.getDisplayName());
        to.setEnumerations(from.getEnumerations());
        to.setDisplayEnumerations(from.getDisplayEnumerations());
        to.setIsActive(from.getIsActive());
        to.setSortOrder(from.getSortOrder());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setType(from.getType());
        to.setFormat(from.getFormat());
        to.setDisabled(from.getDisabled());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CaptionAttributeExample query2Example(CaptionAttributeQuery from) {
        CaptionAttributeExample to = new CaptionAttributeExample();
        CaptionAttributeExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getKey())) {
            c.andKeyEqualTo(from.getKey());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getDisplayName())) {
            c.andDisplayNameEqualTo(from.getDisplayName());
        }
        if (!ObjectUtils.isEmpty(from.getEnumerations())) {
            c.andEnumerationsEqualTo(from.getEnumerations());
        }
        if (!ObjectUtils.isEmpty(from.getDisplayEnumerations())) {
            c.andDisplayEnumerationsEqualTo(from.getDisplayEnumerations());
        }
        if (!ObjectUtils.isEmpty(from.getIsActive())) {
            c.andIsActiveEqualTo(from.getIsActive());
        }
        if (!ObjectUtils.isEmpty(from.getSortOrder())) {
            c.andSortOrderEqualTo(from.getSortOrder());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getFormat())) {
            c.andFormatEqualTo(from.getFormat());
        }
        if (!ObjectUtils.isEmpty(from.getDisabled())) {
            c.andDisabledEqualTo(from.getDisabled());
        }
        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CaptionAttributeVO> doList2VOList(List<CaptionAttributeDO> list) {
        return CommonUtil.listConverter(list, CaptionAttributeConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.StatsWarningInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsWarningInfoVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version StatsWarningInfoService.java v 0.1 2025-05-19 04:36:33
 */
public interface StatsWarningInfoService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	StatsWarningInfoVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param statsWarningInfo 对象参数
	 * @return 返回结果
	 */
	StatsWarningInfoVO insert(StatsWarningInfoVO statsWarningInfo);

	/**
	 * 修改对象
	 * @param statsWarningInfo 对象参数
	 */
	void updateByIdSelective(StatsWarningInfoVO statsWarningInfo);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<StatsWarningInfoVO> queryStatsWarningInfoList(StatsWarningInfoQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryStatsWarningInfoCount(StatsWarningInfoQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<StatsWarningInfoVO> queryStatsWarningInfoByPage(StatsWarningInfoQuery query);

	/**
	 * 批量插入或更新
	 * @param statsList 统计数据列表
	 * @return 影响行数
	 */
    int batchInsertOrUpdate(List<StatsWarningInfoVO> statsList);

	/**
	 * 根据日期和统计周期查询统计数据
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @param statsType 统计类型
	 * @return 统计数据列表
	 */
	List<StatsWarningInfoVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType);

	/**
	 * 根据预警id查询统计数据
	 * @param warningId 预警id
	 * @return 预警信息
	 */
	StatsWarningInfoVO getWarningInfo(Integer warningId);
}
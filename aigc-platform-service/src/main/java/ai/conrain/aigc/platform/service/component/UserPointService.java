package ai.conrain.aigc.platform.service.component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.RevertByVideoBatchVO;
import ai.conrain.aigc.platform.service.model.vo.RevertUnstartedVideoVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;

/**
 * 用户算力点 Service定义
 *
 * <AUTHOR>
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
public interface UserPointService {
    /**
     * 查询用户算力点对象
     *
     * @param userId 用户id
     * @return 返回结果
     */
    UserPointVO selectByUserId(Integer userId);

    /**
     * 删除用户算力点对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加用户算力点对象
     *
     * @param userPoint 对象参数
     * @return 返回结果
     */
    UserPointVO insert(UserPointVO userPoint);

    /**
     * 修改用户算力点对象
     *
     * @param userPoint 对象参数
     */
    void updateByIdSelective(UserPointVO userPoint);

    /**
     * 带条件批量查询用户算力点列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<UserPointVO> queryUserPointList(UserPointQuery query);

    /**
     * 带条件查询用户算力点数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryUserPointCount(UserPointQuery query);

    /**
     * 带条件分页查询用户算力点
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<UserPointVO> queryUserPointByPage(UserPointQuery query);

    /**
     * 查询用户的图片生成算力点
     * <p>
     * 注意这里是实时查询
     * </p>
     *
     * @param userId 用户id
     * @return 图片生成张数
     */
    UserPointVO queryImagePoint(Integer userId);

    /**
     * 预估生成图片需要扣减的点数
     *
     * @param modelId        模型id
     * @param imageNum       图片数量
     * @param type           创作类型
     * @param isUpload       是否上传
     * @param proportionType 图片比例
     * @return 评估结果
     */
    PredictVO predict(Integer modelId, Integer imageNum, CreativeTypeEnum type, Integer timeSecs4Video,
                      boolean isUpload, ProportionTypeEnum proportionType);

    /**
     * 消耗图片生成张数
     *
     * @param data 创作记录
     */
    void consumeByImage(CreativeBatchVO data);

    /**
     * 恢复图片生成张数
     *
     * @param data 创作记录
     */
    void revertByImage(CreativeBatchVO data);

    /**
     * 恢复lora训练的muse点
     */
    void revertByTrainModel(MaterialModelVO model);

    /**
     * 恢复视频创作的muse点
     * @param data 创作批次vo
     */
    void revertByVideo(CreativeBatchVO data);

    /**
     * 退点还没开始生成任务的视频
     * @param data 需要退点的视频
     */
    void revertUnstartedVideoTask(RevertUnstartedVideoVO data);
    /**
     * 退款组内的指定视频
     * @param data 需要退款的视频vo
     */
    Result<?> revertByVideoBatch(RevertByVideoBatchVO data);

    /**
     * 为某个账户充值
     *
     * @param userId          用户id
     * @param imageNum        图片数量
     * @param experiencePoint 体验点数
     * @param givePoint       赠送点数
     */
    void topupByImage(Integer userId, Integer imageNum, Integer experiencePoint, Integer givePoint);

    /**
     * 根据支付单进行充值
     *
     * @param order 充值单
     */
    void rechargeByPay(OrderInfoVO order);

    /**
     * 训练lora消耗点数
     *
     * @param userId 用户id
     * @param model  模型id
     */
    void consumeByTrainModel(Integer userId, MaterialModelVO model, boolean exclusiveElementModel);

    /**
     * 赠送调整
     *
     * @param userId          用户id
     * @param experiencePoint 体验点数
     * @param givePoint       赠送图片数
     */
    @Deprecated
    void setGivePointAndExperiencePoint(Integer userId, Integer experiencePoint, Integer givePoint);

    // 调整赠送图片数，正数代表增加，负数代表减少
    void adjustGivePoint(Integer userId, int experiencePoint, int givePoint);

    /**
     * 调整缪斯点
     *
     * @param userId    用户id
     * @param musePoint 缪斯点
     * @param memo      备注
     */
    void adjustMuse(Integer userId, BigDecimal musePoint, String memo);

    /**
     * 查询用户累计充值金额
     *
     * @param userIdList 用户id列表
     * @return 用户充值金额
     */
    Map<Integer, Integer> queryUserAccumulatedRechargeByUserIdList(List<Integer> userIdList);

    /**
     * 重新审核通过，进行扣除muse点
     * @param model 现在的衣服lora模型
     */
    void deductByTrainModel(MaterialModelVO model);
}
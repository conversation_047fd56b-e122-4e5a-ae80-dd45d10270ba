package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.GalleryQuery;
import ai.conrain.aigc.platform.service.model.request.GalleryUploadRequest;
import ai.conrain.aigc.platform.service.model.vo.GalleryVO;
import com.alibaba.fastjson.JSONObject;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 图库表: 保存系统配置的商品图、用户的上传历史等 Service定义
 *
 * <AUTHOR>
 * @version GalleryService.java v 0.1 2025-08-20 02:49:20
 */
public interface GalleryService {
	
	/**
	 * 查询图库表 
	 * @param   id 主键
	 * @return 返回结果
	 */
	GalleryVO selectById(Integer id);

    /**
	 * 删除图库表 
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图库表 
	 * @param gallery 对象参数
	 * @return 返回结果
	 */
	GalleryVO insert(GalleryVO gallery);

	/**
	 * 修改图库表 
	 * @param gallery 对象参数
	 */
	void updateByIdSelective(GalleryVO gallery);

	/**
	 * 带条件批量查询图库表  
	 * @param query 查询条件
	 * return 结果
	 */
	List<GalleryVO> queryGalleryList(GalleryQuery query);

	/**
	 * 带条件查询图库表
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryGalleryCount(GalleryQuery query);

	/**
	 * 带条件分页查询图库表
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<GalleryVO> queryGalleryByPage(GalleryQuery query);

	/**
	 * 带条件分页查询用户收藏的图库表
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<GalleryVO> queryFavoredGalleryByPage(@Valid GalleryQuery query);

	/**
	 * 带条件分页查询用户收藏的图库表
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<GalleryVO> queryUsedGalleryByPage(@Valid GalleryQuery query);

    /**
     * 处理图片上传
     */
    JSONObject galleryUpload(GalleryUploadRequest request);
}
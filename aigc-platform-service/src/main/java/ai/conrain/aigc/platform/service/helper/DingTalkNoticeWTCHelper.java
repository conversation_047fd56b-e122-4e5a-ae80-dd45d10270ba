/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 带时间控制的钉钉通知助手
 *
 * <AUTHOR>
 * @version : DingTalkNoticeWTCHelper.java, v 0.1 2025/7/3 10:18 renxiao.wu Exp $
 */
@Slf4j
@Component
public class DingTalkNoticeWTCHelper {
    public static final String WTC_NOTICE_CACHE_PREFIX = "_wtc_notice_cache";
    @Autowired
    private TairService tairService;

    /**
     * 发送钉钉消息
     *
     * @param bizType 业务类型
     * @param bizId   业务Id
     * @param msg     消息内容
     */
    public void sendMsg2DevGroup(DingNoticeBizTypeEnum bizType, Integer bizId, String msg) {
        if (bizId == null || StringUtils.isBlank(msg) || bizType == null) {
            log.error("【钉钉消息】参数错误,bizType={},bizId={},msg={}", bizType, bizId, msg);
            return;
        }

        String cacheKey = String.format("%s_%s_%s", WTC_NOTICE_CACHE_PREFIX, bizType, bizId);
        String object = tairService.getString(cacheKey);

        if (object != null) {
            log.info("【钉钉消息】在周期内已发送过，忽略当前消息,bizType={}，bizId={},msg={}", bizType, bizId, msg);
            return;
        }

        DingTalkNoticeHelper.sendMsg2DevGroup(msg);

        tairService.setString(cacheKey, "1", bizType.getIntervalTime());
    }

    @Getter
    public enum DingNoticeBizTypeEnum {

        AUTO_GEN_TASK_NONE_SCENE("AUTO_GEN_TASK_NONE_SCENE", "用户自动出图任务无可用场景", 2 * 60 * 60),
        MODEL_SERVER_ERROR("MODEL_SERVER_ERROR", "model server 服务异常", 5 * 60),
        ;

        /** 枚举码 */
        private final String code;

        /** 枚举描述 */
        private final String desc;

        /** 发送间隔时间，单位：秒 */
        private final Integer intervalTime;

        private DingNoticeBizTypeEnum(String code, String desc, Integer intervalTime) {
            this.code = code;
            this.desc = desc;
            this.intervalTime = intervalTime;
        }

        /**
         * 根据枚举码获取枚举
         *
         * @param code 枚举码
         * @return 对应枚举
         */
        public static DingNoticeBizTypeEnum getByCode(String code) {
            if (StringUtils.isBlank(code)) {
                return null;
            }

            for (DingNoticeBizTypeEnum item : values()) {
                if (StringUtils.equals(item.getCode(), code)) {
                    return item;
                }
            }

            return null;
        }
    }
}

package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.SearchResultImgDO;
import ai.conrain.aigc.platform.dal.example.SearchResultImgExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.SearchResultImgQuery;
import ai.conrain.aigc.platform.service.model.vo.SearchResultImgVO;
import ai.conrain.aigc.platform.service.model.converter.SearchResultImgConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.SearchResultImgDAO;
import ai.conrain.aigc.platform.service.component.SearchResultImgService;

/**   
 * SearchResultImgService实现
 *
 * <AUTHOR>
 * @version SearchResultImgService.java
 */
@Slf4j
@Service
public class SearchResultImgServiceImpl implements SearchResultImgService {

	/** DAO */
	@Autowired
	private SearchResultImgDAO searchResultImgDAO;

	@Override
	public SearchResultImgVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		SearchResultImgDO data = searchResultImgDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return SearchResultImgConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = searchResultImgDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除SearchResultImg失败");
	}

	@Override
	public SearchResultImgVO insert(SearchResultImgVO searchResultImg) {
		AssertUtil.assertNotNull(searchResultImg, ResultCode.PARAM_INVALID, "searchResultImg is null");
		AssertUtil.assertTrue(searchResultImg.getId() == null, ResultCode.PARAM_INVALID, "searchResultImg.id is present");

		//创建时间、修改时间兜底
		if (searchResultImg.getCreateTime() == null) {
			searchResultImg.setCreateTime(new Date());
		}

		if (searchResultImg.getModifyTime() == null) {
			searchResultImg.setModifyTime(new Date());
		}

		SearchResultImgDO data = SearchResultImgConverter.vo2DO(searchResultImg);
		Integer n = searchResultImgDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建SearchResultImg失败");
		AssertUtil.assertNotNull(data.getId(), "新建SearchResultImg返回id为空");
		searchResultImg.setId(data.getId());
		return searchResultImg;
	}


	@Override
	public void updateByIdSelective(SearchResultImgVO searchResultImg) {
		AssertUtil.assertNotNull(searchResultImg, ResultCode.PARAM_INVALID, "searchResultImg is null");
    	AssertUtil.assertTrue(searchResultImg.getId() != null, ResultCode.PARAM_INVALID, "searchResultImg.id is null");

		//修改时间必须更新
		searchResultImg.setModifyTime(new Date());
		SearchResultImgDO data = SearchResultImgConverter.vo2DO(searchResultImg);
		int n = searchResultImgDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新SearchResultImg失败，影响行数:" + n);
	}

	@Override
	public List<SearchResultImgVO> querySearchResultImgList(SearchResultImgQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		SearchResultImgExample example = SearchResultImgConverter.query2Example(query);

		List<SearchResultImgDO> list = searchResultImgDAO.selectByExample(example);
		return SearchResultImgConverter.doList2VOList(list);
	}

	@Override
	public Long querySearchResultImgCount(SearchResultImgQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		SearchResultImgExample example = SearchResultImgConverter.query2Example(query);
		return searchResultImgDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询搜索结果图片
	 */
	@Override
	public PageInfo<SearchResultImgVO> querySearchResultImgByPage(SearchResultImgQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<SearchResultImgVO> page = new PageInfo<>();

		SearchResultImgExample example = SearchResultImgConverter.query2Example(query);
		long totalCount = searchResultImgDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<SearchResultImgDO> list = searchResultImgDAO.selectByExample(example);
		page.setList(SearchResultImgConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	/**
	 * 批量插入搜索结果图片
	 */
	@Override
	public int batchInsert(List<SearchResultImgVO> searchResultImgs) {
		AssertUtil.assertNotNull(searchResultImgs, ResultCode.PARAM_INVALID, "searchResultImgs is null");
		
		if (CollectionUtils.isEmpty(searchResultImgs)) {
			return 0;
		}
		
		// 转换VO为DO
		List<SearchResultImgDO> searchResultImgDOs = new ArrayList<>();
		for (SearchResultImgVO vo : searchResultImgs) {
			SearchResultImgDO searchResultImgDO = SearchResultImgConverter.vo2DO(vo);
			searchResultImgDO.setCreateTime(new Date());
			searchResultImgDO.setModifyTime(new Date());
			searchResultImgDOs.add(searchResultImgDO);
		}
		
		return searchResultImgDAO.batchInsert(searchResultImgDOs);
	}
}
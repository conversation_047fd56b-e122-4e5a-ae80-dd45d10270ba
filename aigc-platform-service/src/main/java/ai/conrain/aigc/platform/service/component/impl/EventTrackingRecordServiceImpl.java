package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.Date;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.constants.TrackEventCachePrefix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.EventTrackingRecordDO;
import ai.conrain.aigc.platform.service.model.vo.EventTrackingRecordVO;
import ai.conrain.aigc.platform.service.model.converter.EventTrackingRecordConverter;
import ai.conrain.aigc.platform.dal.dao.EventTrackingRecordDAO;
import ai.conrain.aigc.platform.service.component.EventTrackingRecordService;
import org.springframework.transaction.annotation.Transactional;

/**
 * EventTrackingRecordService实现
 *
 * <AUTHOR>
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
@Slf4j
@Service
public class EventTrackingRecordServiceImpl implements EventTrackingRecordService {

    /** DAO */
    @Autowired
    private EventTrackingRecordDAO eventTrackingRecordDAO;

    @Autowired
    private TairService tairService;

    @Override
    public EventTrackingRecordVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        EventTrackingRecordDO data = eventTrackingRecordDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return EventTrackingRecordConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = eventTrackingRecordDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除EventTrackingRecord失败");
    }

    @Override
    public void insert(EventTrackingRecordVO eventTrackingRecord) {
        // 参数校验 以及 参数转化
        EventTrackingRecordDO data = paramsCheckAndConverter(eventTrackingRecord);

        // 插入
        int n = eventTrackingRecordDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建EventTrackingRecord失败");
        AssertUtil.assertNotNull(data.getId(), "新建EventTrackingRecord返回id为空");
        eventTrackingRecord.setId(data.getId());
    }

    @Override
    public void updateById(EventTrackingRecordVO eventTrackingRecord) {
        AssertUtil.assertNotNull(eventTrackingRecord, ResultCode.PARAM_INVALID, "eventTrackingRecord is null");
        AssertUtil.assertTrue(eventTrackingRecord.getId() != null, ResultCode.PARAM_INVALID,
            "eventTrackingRecord.id is null");
        //修改时间必须更新
        eventTrackingRecord.setModifyTime(new Date());

        EventTrackingRecordDO data = EventTrackingRecordConverter.vo2DO(eventTrackingRecord);
        int n = eventTrackingRecordDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新EventTrackingRecord失败，影响行数:" + n);
    }

    @Override
    public void record(EventTrackingRecordVO eventTrackingRecord) {
        // 参数校验 以及 参数转化
        EventTrackingRecordDO data = paramsCheckAndConverter(eventTrackingRecord);

        // 开始缓存记录
        tairService.addToList(TrackEventCachePrefix.WAIT_CACHE_TRACK_EVENT_LIST, data,
            TrackEventCachePrefix.WAIT_CACHE_TRACK_EVENT_LIST_TIMEOUT);
    }

    @Transactional
    @Override
    public void batchInsert(List<EventTrackingRecordDO> list) {
        // 执行批量添加
        int n = eventTrackingRecordDAO.batchInsert(list);
    }

    /**
     * 参数校验 以及 参数转化
     *
     * @param eventTrackingRecord 参数记录
     */
    private EventTrackingRecordDO paramsCheckAndConverter(EventTrackingRecordVO eventTrackingRecord) {
        // 参数非空校验
        AssertUtil.assertNotNull(eventTrackingRecord, ResultCode.PARAM_INVALID, "eventTrackingRecord is null");

        //创建时间、修改时间兜底
        if (eventTrackingRecord.getCreateTime() == null) {
            eventTrackingRecord.setCreateTime(new Date());
        }

        if (eventTrackingRecord.getModifyTime() == null) {
            eventTrackingRecord.setModifyTime(new Date());
        }

        // 参数转换
        return EventTrackingRecordConverter.vo2DO(eventTrackingRecord);
    }
}
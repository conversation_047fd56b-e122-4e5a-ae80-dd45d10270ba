package ai.conrain.aigc.platform.service.model.request;

import java.util.List;
import lombok.Data;

@Data
public class QueryAutoGenElementRecommendRequest {
    /**
     * 服装类型
     *
     * @see ai.conrain.aigc.platform.service.enums.ClothTypeEnum
     */
    private String clothType;

    /** 年龄区段 */
    private String ageRange;

    /**
     * 男装｜女装｜童装｜男女通用
     *
     * @see ai.conrain.aigc.platform.service.constants.CommonConstants
     * String female = "female";
     * String male = "male";
     * String child = "child";
     * String unisex = "unisex";
     */
    private String clothStyleType;

    //服装样本图片地址列表
    private List<String> clothImgUrls;

    /** 排除的元素id列表 */
    private List<Integer> excludeElementIds;
}

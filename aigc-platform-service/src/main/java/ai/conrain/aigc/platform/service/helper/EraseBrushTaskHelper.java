package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.aliyun.EraseService;
import ai.conrain.aigc.platform.service.component.CommonTaskService;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_MASK_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;

@Service
public class EraseBrushTaskHelper {

    @Autowired
    private CommonTaskService commonTaskService;
    @Autowired
    private EraseService eraseService;

    public CommonTaskVO createEraseBrushTask(String originImgUrl, String maskImgUrl, CreativeBatchVO batchVO) {
        AssertUtil.assertNotNull(originImgUrl, ResultCode.BIZ_FAIL, "[创建图片擦除任务]原始图片为空");
        AssertUtil.assertNotNull(maskImgUrl, ResultCode.BIZ_FAIL, "[创建图片擦除任务]蒙版图片为空");
        AssertUtil.assertNotNull(batchVO, ResultCode.BIZ_FAIL, "[创建图片擦除任务]关联的创作批次为空");

        String outTaskId = eraseService.createEraseTask(originImgUrl, maskImgUrl, null);
        AssertUtil.assertNotBlank(outTaskId, ResultCode.BIZ_FAIL, "[创建图片擦除任务]创建失败");

        CommonTaskVO task = new CommonTaskVO();
        {
            task.setUserId(batchVO.getUserId());
            task.setOperatorId(batchVO.getOperatorId());
            task.setTaskType(CommonTaskEnums.TaskType.ERASE_BRUSH.name());
            task.setTaskStatus(CommonTaskEnums.TaskStatus.RUNNING.name());
            task.setOutTaskStatus(CommonTaskEnums.TaskStatus.RUNNING.name());
            task.setOutTaskPlatform(CommonTaskEnums.OutTaskPlatform.ALIYUN.name());
            task.setRelatedBizType(CommonTaskEnums.RelatedBizType.CREATIVE_BATCH.getCode());
            task.setOutTaskId(outTaskId);
            task.setRelatedBizId(batchVO.getId().toString());
            task.setTaskStartTime(new Date());

            JSONObject req = new JSONObject();
            req.put(KEY_ORIGIN_IMAGE, originImgUrl);
            req.put(KEY_MASK_IMAGE, maskImgUrl);
            task.setReqBizParams(req.toJSONString());

            JSONObject extInfo = new JSONObject();
            extInfo.put(KEY_ORIGIN_IMAGE, originImgUrl);
            extInfo.put(KEY_MASK_IMAGE, maskImgUrl);
            task.setExtInfo(extInfo);

            task = commonTaskService.insert(task);
        }

        return task;
    }
}

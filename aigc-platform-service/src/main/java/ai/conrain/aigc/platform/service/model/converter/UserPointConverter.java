package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserPointDO;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.dal.example.UserPointExample;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * UserPointConverter
 *
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
public class UserPointConverter {

    /**
     * DO -> VO
     */
    public static UserPointVO do2VO(UserPointDO from) {
        if (from == null) {
            return null;
        }
        UserPointVO to = new UserPointVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserPointDO vo2DO(UserPointVO from) {
        UserPointDO to = new UserPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserPointQuery do2Query(UserPointDO from) {
        UserPointQuery to = new UserPointQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserPointDO query2DO(UserPointQuery from) {
        UserPointDO to = new UserPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static UserPointExample query2Example(UserPointQuery from) {
        UserPointExample to = new UserPointExample();
        UserPointExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getUserIdList())) {
            c.andUserIdIn(from.getUserIdList());
        }
        if (!ObjectUtils.isEmpty(from.getPoint())) {
            c.andPointEqualTo(from.getPoint());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getStartTime()) && !ObjectUtils.isEmpty(from.getEndTime())) {
            c.andModifyTimeBetween(from.getStartTime(), from.getEndTime());
        }


        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<UserPointVO> doList2VOList(List<UserPointDO> list) {
        return CommonUtil.listConverter(list, UserPointConverter::do2VO);
    }
}
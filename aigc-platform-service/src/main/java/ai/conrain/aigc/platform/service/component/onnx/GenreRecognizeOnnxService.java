package ai.conrain.aigc.platform.service.component.onnx;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collections;
import java.util.Map;

@Slf4j
@Component
public class GenreRecognizeOnnxService {

    private SimpleOnnxWrapper onnxWrapper;
    private static final String ONNX_MODEL_PATH = "onnx/genre_model.onnx";

    // 类别标签映射
    private static final Map<Integer, String> CLASS_LABELS = Map.of(
            0, "Product_lookbook",
            1, "Runway_shot",
            2, "Vogue_style_fashion_shoot",
            3, "LRSP2111_commercial",
            4, "Selfie",
            5, "Internet_style_photo");

    @PostConstruct
    public void init() {
        onnxWrapper = new SimpleOnnxWrapper(ONNX_MODEL_PATH, "流派识别模型服务");
        onnxWrapper.init();
    }

    @PreDestroy
    public void destroy() {
        if (onnxWrapper != null) {
            onnxWrapper.destroy();
        }
    }

    /**
     * 基于onnx包，识别图像的流派类别
     */
    public String recognizeGenreFromPath(String imgPath) throws OrtException {

        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imgPath == null || imgPath.trim().isEmpty()) {
            throw new IllegalArgumentException("图像路径不能为空");
        }

        try {
            log.info("开始识别图像流派，图像路径: {}", imgPath);

            // 加载和预处理图像
            float[][][][] imageData = OnnxUtil.loadAndPreprocessImage(imgPath);

            // 创建输入张量
            OnnxTensor inputTensor = OnnxTensor.createTensor(onnxWrapper.getEnvironment(), imageData);

            // 执行推理
            Map<String, OnnxTensor> inputs = Collections.singletonMap("image", inputTensor);
            OrtSession.Result result = onnxWrapper.getSession().run(inputs);

            // 获取输出
            OnnxTensor outputTensor = (OnnxTensor) result.get(0);
            long[][] predictions = (long[][]) outputTensor.getValue();

            // 获取预测类别
            int predictedClass = (int) predictions[0][0];
            String genreLabel = CLASS_LABELS.get(predictedClass);

            log.info("图像流派识别完成，预测类别: {} ({})", predictedClass, genreLabel);

            // 清理资源
            inputTensor.close();
            result.close();

            return genreLabel;

        } catch (Exception e) {
            log.error("图像流派识别失败，图像路径: {}", imgPath, e);
            throw new OrtException("图像流派识别失败: " + e.getMessage());
        }
    }

    /**
     * 从URL下载图像并识别流派
     */
    public String recognizeGenreFromUrl(String imageUrl) {

        if (!onnxWrapper.isInitialized()) {
            throw new IllegalStateException("ONNX模型未初始化");
        }

        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("图像URL不能为空");
        }

        Path tempFile = null;

        try {
            log.info("开始下载并识别网络图像流派，图像URL: {}", imageUrl);

            // 下载图片到本地
            tempFile = OnnxUtil.downloadImageFromUrl(imageUrl);
            String localPath = tempFile.toAbsolutePath().toString();
            log.debug("图片下载完成: {} -> {}", imageUrl, localPath);

            // 调用现有的本地文件识别方法
            String result = recognizeGenreFromPath(localPath);

            log.info("网络图像流派识别完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("下载图像失败，URL: {}", imageUrl, e);
            return null;

        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                    log.debug("临时文件已清理: {}", tempFile);
                } catch (Exception e) {
                    log.warn("清理临时文件失败: {}", tempFile, e);
                }
            }
        }
    }
}

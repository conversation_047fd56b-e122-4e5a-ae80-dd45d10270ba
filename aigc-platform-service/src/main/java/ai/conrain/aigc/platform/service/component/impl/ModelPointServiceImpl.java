package ai.conrain.aigc.platform.service.component.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import ai.conrain.aigc.platform.dal.dao.ModelPointDAO;
import ai.conrain.aigc.platform.dal.entity.ModelPointDO;
import ai.conrain.aigc.platform.dal.example.ModelPointExample;
import ai.conrain.aigc.platform.service.component.ModelPointService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ModelPointConverter;
import ai.conrain.aigc.platform.service.model.query.ModelPointQuery;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ModelPointService实现
 *
 * <AUTHOR>
 * @version ModelPointService.java v 0.1 2024-06-21 12:01:15
 */
@Slf4j
@Service
public class ModelPointServiceImpl implements ModelPointService {
    /** 默认初始化模型套餐积分 */
    public static final Integer INIT_MODEL_POINT = 100;

    /** DAO */
    @Autowired
    private ModelPointDAO modelPointDAO;

    @Override
    public ModelPointVO selectByModelId(Integer modelId, Integer userId) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");

        ModelPointExample example = new ModelPointExample();
        example.createCriteria().andModelIdEqualTo(modelId).andUserIdEqualTo(userId);
        List<ModelPointDO> list = modelPointDAO.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return ModelPointConverter.do2VO(list.get(0));
    }

    @Override
    public ModelPointVO lockByModelId(Integer modelId, Integer userId) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "id is null");
        ModelPointDO data = modelPointDAO.lockByModelId(modelId, userId);
        if (null == data) {
            return null;
        }
        return ModelPointConverter.do2VO(data);
    }

    @Override
    public void updateByIdSelective(ModelPointVO modelPoint) {
        AssertUtil.assertNotNull(modelPoint, ResultCode.PARAM_INVALID, "modelPoint is null");
        AssertUtil.assertTrue(modelPoint.getId() != null, ResultCode.PARAM_INVALID, "modelPoint.id is null");

        //修改时间必须更新
        modelPoint.setModifyTime(new Date());
        ModelPointDO data = ModelPointConverter.vo2DO(modelPoint);
        int n = modelPointDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ModelPoint失败，影响行数:" + n);
    }

    @Override
    public List<ModelPointVO> queryModelPointList(ModelPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ModelPointExample example = ModelPointConverter.query2Example(query);

        List<ModelPointDO> list = modelPointDAO.selectByExample(example);
        return ModelPointConverter.doList2VOList(list);
    }

    @Override
    public Long queryModelPointCount(ModelPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ModelPointExample example = ModelPointConverter.query2Example(query);
        return modelPointDAO.countByExample(example);
    }

    /**
     * 带条件分页查询模型套餐积分
     */
    @Override
    public PageInfo<ModelPointVO> queryModelPointByPage(ModelPointQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ModelPointVO> page = new PageInfo<>();

        ModelPointExample example = ModelPointConverter.query2Example(query);
        long totalCount = modelPointDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ModelPointDO> list = modelPointDAO.selectByExample(example);
        page.setList(ModelPointConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void init(Integer modelId, Integer userId) {
        AssertUtil.assertNotNull(modelId, ResultCode.PARAM_INVALID, "modelId is null");
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");

        ModelPointDO data = new ModelPointDO();
        data.setModelId(modelId);
        data.setUserId(userId);
        data.setPoint(INIT_MODEL_POINT);
        int n = modelPointDAO.insertSelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ModelPoint失败");
        AssertUtil.assertNotNull(data.getId(), "新建ModelPoint返回id为空");
    }

    @Override
    public List<ModelPointVO> batchQueryByModels(List<Integer> modelIds, Integer userId) {
        if (CollectionUtils.isEmpty(modelIds) || userId == null) {
            log.warn("批量查询模型积分数据异常，传入的list或userId为空,modelIds={},userId={}", modelIds, userId);
            return null;
        }
        List<ModelPointDO> list = modelPointDAO.batchSelectByModel(modelIds, userId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return ModelPointConverter.doList2VOList(list);
    }
}
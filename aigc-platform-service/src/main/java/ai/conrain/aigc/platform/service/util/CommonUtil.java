package ai.conrain.aigc.platform.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PricePlanCode;
import ai.conrain.aigc.platform.service.model.common.OperationContext;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CLOTH_MODEL_CONSUME_POINT;

@Slf4j
public abstract class CommonUtil {
    /**
     * list -> list
     */
    public static <T, R> List<R> listConverter(List<T> fromList, Function<? super T, ? extends R> f) {
        if (CollectionUtils.isEmpty(fromList)) {
            return new ArrayList<>();
        }

        return fromList.stream().map(f).collect(Collectors.toList());
    }

    /**
     * 判断日期是否为指定格式
     */
    public static boolean isLegalDate(String sDate, String pattern) {
        if (sDate == null || sDate.length() != pattern.length()) {
            return false;
        }

        DateFormat formatter = new SimpleDateFormat(pattern);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isValidJsonArray(String s) {
        try {
            return JSONArray.parse(s) != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isValidJson(String s) {
        try {
            return JSONObject.parseObject(s) != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成指定长度的随机数字字符串
     *
     * @param length 指定的字符串长度
     * @return 指定长度的随机数字字符串
     */
    public static String generateRandomNumberString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be greater than 0");
        }

        Random random = new Random();
        // 最大值为 10^length - 1, 保证生成的数字位数不超过指定长度
        int maxValue = (int)Math.pow(10, length) - 1;
        int randomNumber = random.nextInt(maxValue + 1);

        // 将随机数字格式化为指定长度，左侧补0
        return String.format("%0" + length + "d", randomNumber);
    }

    /**
     * 生成指定长度的随机数字字符串
     *
     * @param length 指定的字符串长度
     * @return 指定长度的随机数字字符串
     */
    public static String generateRandomNumberString(int length, long seed) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be greater than 0");
        }

        Random random = new Random(seed);
        // 最大值为 10^length - 1, 保证生成的数字位数不超过指定长度
        int maxValue = (int)Math.pow(10, length) - 1;
        int randomNumber = random.nextInt(maxValue + 1);

        // 将随机数字格式化为指定长度，左侧补0
        return String.format("%0" + length + "d", randomNumber);
    }

    public static String getLinuxExportIp() {
        try {
            InetAddress localhost = InetAddress.getLocalHost();
            InetAddress[] addresses = InetAddress.getAllByName(localhost.getHostName());
            for (InetAddress address : addresses) {
                if (!address.isLoopbackAddress() && address.isSiteLocalAddress()) {
                    return address.getHostAddress();
                }
            }
        } catch (UnknownHostException e) {
            log.error("获取出口ip异常", e);
        }
        return null;
    }

    /**
     * 生成与时间、用户关联的业务单号
     *
     * @return 业务号
     */
    public static String genBizNo(Integer userId) {
        StringBuilder builder = new StringBuilder();
        //日期前缀，8位
        builder.append(DateUtils.getNowShort());

        //用户id,100000,当前6位,前置补齐0到8位
        builder.append(String.format("%08d", userId));

        //增加4位随机数
        Random random = new Random(System.currentTimeMillis());
        int randomValue = random.nextInt(10000);
        builder.append(String.format("%04d", randomValue));

        return builder.toString();
    }

    public static String formatJsonOneRow(String source) {

        //不是有效的json object string，直接返回不处理
        if (!CommonUtil.isValidJson(source)) {
            return source;
        }

        JSONObject object = JSONObject.parseObject(source);
        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat);
    }

    public static String formatJson(String source) {

        //不是有效的json object string，直接返回不处理
        if (!CommonUtil.isValidJson(source)) {
            return source;
        }

        JSONObject object = JSONObject.parseObject(source);
        return JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
            SerializerFeature.WriteDateUseDateFormat);
    }

    public static JSONObject java2JSONObject(Object o) {
        if (o == null) {
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(o));
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        if (CommonUtil.isValidJson(json)) {
            return JSON.parseObject(json, clazz);
        }
        return null;
    }

    public static JSONObject parseObject(String s) {
        if (CommonUtil.isValidJson(s)) {
            return JSONObject.parseObject(s);
        }
        return null;
    }

    public static String toJSONString(Object o) {
        if (o != null) {
            return JSON.toJSONString(o);
        }
        return null;
    }

    public static String toJSONString(Object o, String ...ignoreFields){
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().addAll(Arrays.stream(ignoreFields).toList());

        return JSONObject.toJSONString(o, filter);
    }

    public static boolean endsWithAny(String s, Collection<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (String str : list) {
                if (s.endsWith(str)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static boolean containsAny(String s, Collection<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (String str : list) {
                if (s.contains(str)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static <T> String join(List<T> list) {
        return CommonUtil.join(list, ",");
    }

    public static String sortAndJoin(List<String> list) {
        Collections.sort(list);
        return CommonUtil.join(list, ",");
    }

    public static <T> String join(List<T> list, String sep) {
        if (list == null || sep == null) {
            throw new NullPointerException("list or sep is null");
        }
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        StringJoiner sj = new StringJoiner(sep);
        for (T i : list) {
            sj.add(i.toString());
        }
        return sj.toString();
    }

    public static List<Integer> split(String s, String sep) {
        if (StringUtils.isBlank(s) || StringUtils.isBlank(sep)) {
            throw new NullPointerException("s or sep is null");
        }
        return Arrays.stream(s.split(sep)).map(Integer::parseInt).collect(Collectors.toList());
    }

    public static List<Integer> split(String s) {
        return CommonUtil.split(s, ",");
    }

    public static String getFilePathAndNameFromURL(String url) {
        try {
            // 去掉查询参数
            int queryStartIndex = url.indexOf('?');
            if (queryStartIndex != -1) {
                url = url.substring(0, queryStartIndex);
            }

            // 提取路径和文件名部分
            String urlDomainFlag = url.contains(".com/") ? ".com/" : ".tech/";
            int startIndex = url.indexOf(urlDomainFlag);
            if (startIndex != -1) {
                return url.substring(startIndex + urlDomainFlag.length());
            } else {
                throw new IllegalArgumentException("Invalid URL format");
            }
        } catch (Exception e) {
            log.error("getFilePathAndNameFromURL fail,url=" + url, e);
            return null;
        }
    }

    /**
     * 使用java.nio.file.Paths类获取文件名部分
     * a/b.txt => b.txt
     *
     * @param filePath 路径字符串
     * @return 文件名部分
     */
    public static String getFileNameWithExtension(String filePath) {
        Path path = Paths.get(filePath);
        return path.getFileName().toString();
    }

    /**
     * 获取不带扩展名的文件名部分
     *
     * @param filePath 路径字符串
     * @return 不带扩展名的文件名部分
     */
    public static String getFileNameWithoutExtension(String filePath) {
        Path path = Paths.get(filePath);
        String fileName = path.getFileName().toString();
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? fileName : fileName.substring(0, dotIndex);
    }

    /**
     * 从url中获取文件名
     * https://a.com/b.mp4?c=d => b.mp4
     *
     * @param fileUrl 路径字符串
     * @return 带扩展名的文件名部分
     */
    public static String getFileNameFromUrl(String fileUrl) {
        String fileName = fileUrl.substring(fileUrl.lastIndexOf('/') + 1);
        if (fileName.contains("?")) {
            return fileName.substring(0, fileName.indexOf('?'));
        } else {
            return fileName;
        }
    }

    /**
     * 获取文件扩展名部分(.txt/.png)
     *
     * @param filePath 路径字符串
     * @return 文件扩展名部分
     */
    public static String getFileExtension(String filePath) {
        Path path = Paths.get(filePath);
        String fileName = path.getFileName().toString();
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex);
    }

    public static String getRelativePath(String fullPath, String parentPath) {
        // 将字符串转换为Path对象
        Path full = Paths.get(fullPath);
        Path parent = Paths.get(parentPath);

        // 使用Path类的relativize方法获取相对路径
        Path relative = parent.relativize(full);

        // 返回相对路径的字符串表示
        return relative.toString();
    }

    /**
     * 去除字符串中的所有空白字符。
     *
     * @param input 需要去除空白字符的原始字符串。
     * @return 去除空白字符后的字符串。
     */
    public static String removeWhitespace(String input) {
        if (input == null) {
            return null;
        }
        return input.replaceAll("\\s+", "");
    }

    /**
     * 根据逗号拆分字符串，并去除每个元素的空白字符。
     * 如果字符串不包含逗号，则返回包含原字符串的列表。
     *
     * @param input 需要拆分的字符串。
     * @return 拆分后的字符串列表。
     */
    public static List<String> splitByComma(String input) {
        if (input == null || input.isEmpty()) {
            return new ArrayList<>(); // 返回空列表
        }

        // 使用正则表达式拆分字符串，同时去除空白字符
        String[] parts = input.split(","); // 拆分字符串
        List<String> result = new ArrayList<>();
        for (String part : parts) {
            if (!part.trim().isEmpty()) {
                result.add(part.trim());
            }
        }
        return result;
    }

    public static boolean allBlank(String... ss) {
        if (ss == null) {
            return true;
        }
        for (String s : ss) {
            if (StringUtils.isNotBlank(s)) {
                return false;
            }
        }
        return true;
    }

    public static String replaceIgnoreCase(String input, String target, String replacement) {

        // 创建一个忽略大小写的正则表达式模式
        Pattern pattern = Pattern.compile(target, Pattern.CASE_INSENSITIVE);
        // 创建一个Matcher对象
        Matcher matcher = pattern.matcher(input);
        // 使用Matcher对象的replaceAll方法进行替换
        String ret = matcher.replaceAll(replacement);

        if (!ret.equals(input)) {
            log.info("replaceIgnoreCase,target:{} => replacement:{},original:{} => ret:{}", target, replacement, input,
                ret);
        }

        return ret;
    }

    /**
     * 判断一个字符串是否在忽略大小写的情况下包含另一个字符串
     *
     * @param str       原始字符串
     * @param searchStr 要搜索的字符串
     * @return 如果包含则返回true，否则返回false
     */
    public static boolean containsIgnoreCase(String str, String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.toLowerCase().contains(searchStr.toLowerCase());
    }

    /**
     * 判断一个字符串是否在忽略大小写的情况下，包含列表中的任一个字符串
     *
     * @param str  原始字符串
     * @param list 要搜索的字符串列表
     * @return 如果包含任意一个字符串则返回true，否则返回false
     */
    public static boolean containsAnyIgnoreCase(String str, List<String> list) {
        if (str == null || list == null || list.isEmpty()) {
            return false;
        }
        String lowerCaseStr = str.toLowerCase();
        for (String searchStr : list) {
            if (searchStr != null && lowerCaseStr.contains(searchStr.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算图片算力点
     *
     * @param point 用户算力点
     * @return 图片算力点
     */
    public static BigDecimal point2MusePoint(Integer point) {
        if (point == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal divisor = BigDecimal.valueOf(CommonConstants.MUSE_POINT_COEFFICIENT);
        return BigDecimal.valueOf(point).divide(divisor, 3, RoundingMode.HALF_UP);
    }

    public static Integer musePoint2Point(BigDecimal musePoint) {
        if (musePoint == null) {
            return 0;
        }

        BigDecimal divisor = BigDecimal.valueOf(CommonConstants.MUSE_POINT_COEFFICIENT);
        return musePoint.multiply(divisor).toBigInteger().intValue();
    }

    /**
     * 返回示例：[/a/b/c, filename, .txt]
     *
     * @param filePath
     * @return
     */
    public static String[] parseFilePath(String filePath) {
        File file = new java.io.File(filePath);
        String directory = file.getParent();
        String fullFileName = file.getName();

        int lastDotIndex = fullFileName.lastIndexOf('.');
        String fileNameWithoutExtension = (lastDotIndex == -1) ? fullFileName : fullFileName.substring(0, lastDotIndex);
        String fileExtension = (lastDotIndex == -1) ? "" : fullFileName.substring(lastDotIndex);

        return new String[] {directory, fileNameWithoutExtension, fileExtension};
    }

    // 从 URL 中获取文件名
    private static String getImageName(String url) {
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        int queryIndex = fileName.indexOf('?');
        if (queryIndex != -1) {
            fileName = fileName.substring(0, queryIndex);
        }
        return fileName;
    }

    // 根据图片 URL 获取任务 ID
    public static int getCreativeTaskIdByImageUrl(String imageUrl) {
        try {
            String imageName = getImageName(imageUrl);
            String[] parts = imageName.split("_");
            if (parts.length > 1) {
                return Integer.parseInt(parts[1]);
            }
        } catch (Exception e) {
            // 处理其他异常，如必要可添加日志或其他处理方式
            log.error("处理其他异常", e);
        }
        return -1;
    }

    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(T object) {
        if (object == null) {
            return null;
        }

        Class<T> clz = (Class<T>)object.getClass();

        return JSONObject.parseObject(JSONObject.toJSONString(object), clz);
    }

    public static <T> List<T> deepCopyList(List<T> object) {
        if (CollectionUtils.isEmpty(object)) {
            return object;
        }

        //noinspection unchecked
        Class<T> clz = (Class<T>)object.get(0).getClass();

        return JSONArray.parseArray(JSONArray.toJSONString(object), clz);
    }

    public static String getProductShowName(OrderInfoVO vo) {
        String name = null;
        if (isValidJson(vo.getProductDetail())) {
            name = JSONObject.parseObject(vo.getProductDetail()).getString("name");
        }
        if (StringUtils.isBlank(name) && PricePlanCode.getByCode(vo.getProductCode()) != null) {
            name = PricePlanCode.getByCode(vo.getProductCode()).getDesc();
        }
        return name;
    }

    //返回.jpg/.png
    public static String getFileExtensionFromUrl(String url) {
        // 找到最后一个斜杠的位置
        int lastSlashIndex = url.lastIndexOf('/');

        // 找到问号的位置（如果有）
        int queryIndex = url.indexOf('?', lastSlashIndex);

        // 如果有查询参数，用查询参数之前的部分，否则到末尾
        String fileName;
        if (queryIndex != -1) {
            fileName = url.substring(lastSlashIndex + 1, queryIndex);
        } else {
            fileName = url.substring(lastSlashIndex + 1);
        }

        // 获取文件扩展名（找到最后一个点 . 之后的部分）
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex != -1 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex);
        } else {
            // 返回 null 或者默认值，如果没有扩展名
            return null;
        }
    }

    //训练模型消耗的点数，注意，返回的是muse点*存储倍率1000，不是muse点
    public static int getNeedPoint4Train(MaterialModelVO model, boolean exclusiveElementModel) {

        //服装
        if (model.getMaterialType() == MaterialType.cloth) {

            boolean multiColor = model.getClothLoraTrainDetail() != null && "Y".equals(
                model.getClothLoraTrainDetail().getMultiColors());

            boolean ironingCloth = model.isIroningCloth();

            //代传价格：额外消耗
            int tenPoints = 10 * CommonConstants.MUSE_POINT_COEFFICIENT;

            //学习费，单色40点，多色50点
            int ret = multiColor ? (tenPoints + CLOTH_MODEL_CONSUME_POINT) : CLOTH_MODEL_CONSUME_POINT;

            int colorNumber = 1;
            if (model.getExtInfo() != null && model.getExtInfo().getInteger(CommonConstants.colorNumber) != null) {
                colorNumber = model.getExtInfo().getInteger(CommonConstants.colorNumber);
            }

            //代拍，每色加20点
            if (model.isAgentUploadModel()) {
                ret += 2 * tenPoints * colorNumber;
            }

            //代熨烫，每色加10点
            if (ironingCloth) {
                ret += tenPoints * colorNumber;
            }

            return ret;

            //模特1200点
        } else if (model.getMaterialType() == MaterialType.face) {
            return 1200 * CommonConstants.MUSE_POINT_COEFFICIENT;

            //场景40点
        } else if (model.getMaterialType() == MaterialType.scene) {
            if (exclusiveElementModel) {
                return 200 * CommonConstants.MUSE_POINT_COEFFICIENT;
            } else {
                return 40 * CommonConstants.MUSE_POINT_COEFFICIENT;
            }
        } else {
            throw new RuntimeException("getNeedPoint4Train unknown materialType:" + model.getMaterialType());
        }
    }

    /**
     * 首字母大写
     *
     * @param text 文本
     * @return 转换后的文本
     */
    public static String capitalizeFirstLetter(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        return text.substring(0, 1).toUpperCase() + text.substring(1);
    }

    // 转义换行符，将字符串的换行改为换行符\n
    public static String unescapeLineBreak(String s) {
        return StringUtils.isBlank(s) ? s : s.replaceAll("\r\n|\r|\n", "\\\\n");
    }

    //  获取安全提示词
    public static String getPromptSafely(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return null;
        }
        return ComfyUIUtils.parseParams(CommonUtil.unescapeLineBreak(prompt));
    }

    /**
     * mock系统上下文
     *
     * @return 系统操作上下文
     */
    public static OperationContext mockSystemContext() {
        OperationContext operationContext = new OperationContext();
        operationContext.setRoleType(RoleTypeEnum.SYSTEM);
        UserVO user = new UserVO();
        user.setId(1);
        user.setStatus(UserStatusEnum.ENABLED);
        user.setUserType(UserTypeEnum.MASTER);
        user.setNickName("系统调度");
        user.setRoleType(RoleTypeEnum.SYSTEM);
        operationContext.setLoginUser(user);
        return operationContext;
    }

    /**
     * mock自动创作上下文
     *
     * @return 自动创作操作上下文
     */
    public static OperationContext mockAutoCreativeContext() {
        OperationContext operationContext = new OperationContext();
        operationContext.setRoleType(RoleTypeEnum.SYSTEM);
        UserVO user = new UserVO();
        user.setId(2);
        user.setStatus(UserStatusEnum.ENABLED);
        user.setUserType(UserTypeEnum.MASTER);
        user.setNickName("自动创作");
        user.setRoleType(RoleTypeEnum.SYSTEM);
        operationContext.setLoginUser(user);
        return operationContext;
    }

    /**
     * mock客户自动创作上下文
     *
     * @return 自动创作操作上下文
     */
    public static OperationContext mockCustomerAutoCreativeContext() {
        OperationContext operationContext = new OperationContext();
        operationContext.setRoleType(RoleTypeEnum.SYSTEM);
        UserVO user = new UserVO();
        user.setId(5);
        user.setStatus(UserStatusEnum.ENABLED);
        user.setUserType(UserTypeEnum.MASTER);
        user.setNickName("客户自动创作");
        user.setRoleType(RoleTypeEnum.SYSTEM);
        operationContext.setLoginUser(user);
        return operationContext;
    }

    /**
     * mock自动创作上下文
     *
     * @return 自动创作操作上下文
     */
    public static OperationContext mockPostureSampleDiagramContext() {
        OperationContext operationContext = new OperationContext();
        operationContext.setRoleType(RoleTypeEnum.SYSTEM);
        UserVO user = new UserVO();
        user.setId(3);
        user.setStatus(UserStatusEnum.ENABLED);
        user.setUserType(UserTypeEnum.MASTER);
        user.setNickName("示例图创作");
        user.setRoleType(RoleTypeEnum.SYSTEM);
        operationContext.setLoginUser(user);
        return operationContext;
    }

    /**
     * mock特殊创作上下文
     *
     * @return 特殊创作上下文
     */
    public static OperationContext mockSpecialTestContext() {
        OperationContext operationContext = new OperationContext();
        operationContext.setRoleType(RoleTypeEnum.SYSTEM);
        UserVO user = new UserVO();
        user.setId(4);
        user.setStatus(UserStatusEnum.ENABLED);
        user.setUserType(UserTypeEnum.MASTER);
        user.setNickName("特殊评测");
        user.setRoleType(RoleTypeEnum.SYSTEM);
        operationContext.setLoginUser(user);
        return operationContext;
    }

    public static boolean isSystemUser(Integer userId) {
        return userId != null && (userId.equals(1) || userId.equals(2));
    }

    public static String formatText(String text) {
        text = StringUtils.trim(text);
        if (StringUtils.isBlank(text)) {
            return "";
        }
        return text;
    }

    public static String formatTextWithComma(String text) {
        String result = formatText(text);
        if (isNoneString(result)) {
            return result;
        }

        if (!StringUtils.endsWith(result, ".") && !StringUtils.endsWith(result, ",")) {
            return text + ",";
        }
        return text;
    }

    public static String appendWithComma(String current, String text) {
        String result = formatText(text);
        if (isNoneString(result)) {
            return current;
        }

        return StringUtils.isNotBlank(current) ? current + "," + result : result;
    }

    public static String appendWithComma(String current, List<String> texts) {
        if (CollectionUtils.isEmpty(texts)) {
            return current;
        }
        StringBuilder builder = new StringBuilder();
        texts.forEach(text -> builder.append(appendWithComma(builder.toString(), text)));

        return builder.toString();
    }

    public static boolean isNoneString(String s) {
        s = StringUtils.trim(s);
        return StringUtils.isBlank(s) || StringUtils.equalsIgnoreCase("none", s) || StringUtils.equalsIgnoreCase(
            "none.", s) || StringUtils.equalsIgnoreCase("无,", s) || StringUtils.equalsIgnoreCase("无", s);
    }

    public static boolean isMobile(String mobile) {
        return StringUtils.isNotBlank(mobile) && CommonConstants.MOBILE_PATTERN.matcher(mobile).find();
    }

    /**
     * 计算文件字节码的md5
     *
     * @param fileBytes 文件字节码
     * @return md5
     */
    public static String calculateMD5(byte[] fileBytes) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");

            md.update(fileBytes);

            byte[] mdBytes = md.digest();

            // convert the byte to hex format
            StringBuilder hexString = new StringBuilder();
            for (byte b : mdBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("计算文件字节码的md5异常", e);
            return null;
        }
    }

    public static String calculateMD5(File file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            FileInputStream fis = null;

            fis = new FileInputStream(file);

            byte[] dataBytes = new byte[4096];
            int nread = 0;

            while ((nread = fis.read(dataBytes)) != -1) {
                md.update(dataBytes, 0, nread);
            }
            fis.close();

            byte[] mdbytes = md.digest();

            // convert the byte to hex format
            StringBuilder hexString = new StringBuilder();
            for (byte b : mdbytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {hexString.append('0');}
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件字节码的md5异常", e);
            return null;
        }
    }

    public static boolean isExpression(String s) {
        return StringUtils.isNotBlank(s) && s.contains("{") && s.contains("}");
    }

    /**
     * 格式化{}的表达式
     *
     * @param template  模板
     * @param variables 参数map
     * @return 格式化后的字符串
     */
    public static String formatExpression(String template, Map<String, Object> variables) {
        Pattern pattern = Pattern.compile("\\{(\\w+)\\}");
        Matcher matcher = pattern.matcher(template);
        StringBuffer buffer = new StringBuffer();

        while (matcher.find()) {
            String key = matcher.group(1);
            Object value = variables.getOrDefault(key, "{" + key + "}"); // 未找到则保留原占位符
            matcher.appendReplacement(buffer, Matcher.quoteReplacement(value.toString()));
        }
        matcher.appendTail(buffer);
        return buffer.toString();
    }

    /**
     * 将a:1;b:2;c:3格式的字符串转化成map
     *
     * @param str 字符串
     * @return map
     */
    public static Map<String, String> parseStrToMap(String str) {
        if (StringUtils.isBlank(str)) {
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();
        String[] split = StringUtils.split(str, ";");
        for (String s : split) {
            String[] subSplit = StringUtils.split(s, ":");
            if (subSplit.length == 2) {
                result.put(subSplit[0], subSplit[1]);
            }
        }

        return result;
    }

    /**
     * 解析所有的对象中的值
     *
     * @param data   数据
     * @param result 结果
     */
    public static void getJsonValue(Object data, List<String> result) {
        //根据jsonKey的顺序，正序输出到list
        if (data instanceof Map) {
            Map<?, ?> map = (Map<?, ?>)data;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                getJsonValue(entry.getValue(), result);
            }

        } else if (data instanceof List) {
            List<?> list = (List<?>)data;
            for (Object item : list) {
                getJsonValue(item, result);
            }
        } else {
            String formattedValue = data.toString();
            if (formattedValue.endsWith(".")) {
                formattedValue = formattedValue.replace(".", ",");
            }
            if (!formattedValue.endsWith(",")) {
                formattedValue = formattedValue + ",";
            }
            result.add(formattedValue);
        }
    }

    /**
     * 解析gpt返回的json字符串，如果带有```json```，则去除后提取出json字符串
     *
     * @param content
     * @return
     */
    public static String parseJsonStringFromGpt(String content) {
        log.info("parseJsonFromGpt原始内容：{}", content);
        if (StringUtils.isBlank(content)) {
            log.warn("内容为空");
            return null;
        }

        // 兼容内容是markdown的json格式内容时，需要提取出json块
        Pattern pattern = Pattern.compile("```json\\s*\\n(.*?)\\n```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(content);

        if (matcher.find()) {
            content = matcher.group(1);
        }

        return content;
    }

    /**
     * 将列表转换为 Map，key 由 keyExtractor 提取
     */
    public static <K, V> Map<K, V> toMap(List<V> list, Function<V, K> keyExtractor) {
        if (list == null || list.isEmpty()) {
            return new HashMap<>();
        }
        return list.stream().collect(
            Collectors.toMap(keyExtractor, v -> v, (existing, replacement) -> existing // 碰撞时保留旧值
            ));
    }

    /**
     * Integer 是否 为 0(或 null)
     */
    public static Boolean equalsZero(Integer integer) {
        return integer == null || integer == 0;
    }
}

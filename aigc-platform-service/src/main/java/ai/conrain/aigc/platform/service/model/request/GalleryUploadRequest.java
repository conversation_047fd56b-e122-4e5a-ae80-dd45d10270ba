package ai.conrain.aigc.platform.service.model.request;

import com.alibaba.fastjson.annotation.JSONField;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class GalleryUploadRequest {
    // 图片文件
    @NotEmpty
    @JSONField(serialize = false)
    private List<MultipartFile> imageFiles;

    // 一级类型
    @NotBlank
    private String type;

    // 二级类型
    private String subType;

    // 图片标签
    private List<String> tags;

    // 用户id
    private Integer userId;

    // 图片归属类型
    private String belong;

    private boolean withResult;
}

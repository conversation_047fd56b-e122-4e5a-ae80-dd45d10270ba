package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 属性定义表，用于存储图像描述（caption）中可配置的属性元信息 Service定义
 *
 * <AUTHOR>
 * @version CaptionAttributeService.java v 0.1 2025-08-14 07:49:02
 */
public interface CaptionAttributeService {
	
	/**
	 * 查询属性定义表，用于存储图像描述（caption）中可配置的属性元信息对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	CaptionAttributeVO selectById(Integer id);

	/**
	 * 删除属性定义表，用于存储图像描述（caption）中可配置的属性元信息对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加属性定义表，用于存储图像描述（caption）中可配置的属性元信息对象
	 * @param captionAttribute 对象参数
	 * @return 返回结果
	 */
	CaptionAttributeVO insert(CaptionAttributeVO captionAttribute);

	/**
	 * 修改属性定义表，用于存储图像描述（caption）中可配置的属性元信息对象
	 * @param captionAttribute 对象参数
	 */
	void updateByIdSelective(CaptionAttributeVO captionAttribute);

	/**
	 * 带条件批量查询属性定义表，用于存储图像描述（caption）中可配置的属性元信息列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<CaptionAttributeVO> queryCaptionAttributeList(CaptionAttributeQuery query);

	/**
	 * 带条件查询属性定义表，用于存储图像描述（caption）中可配置的属性元信息数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryCaptionAttributeCount(CaptionAttributeQuery query);

	/**
	 * 带条件分页查询属性定义表，用于存储图像描述（caption）中可配置的属性元信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<CaptionAttributeVO> queryCaptionAttributeByPage(CaptionAttributeQuery query);
}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.qichacha.model.QiChaChaModelPageVO;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.SalesTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.OrganizationQuery;
import ai.conrain.aigc.platform.service.model.vo.CorpAuthVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import java.util.List;

/**
 * 组织 Service定义
 *
 * <AUTHOR>
 * @version OrganizationService.java v 0.1 2024-07-12 04:26:40
 */
public interface OrganizationService {
	
	/**
	 * 查询组织对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	OrganizationVO selectById(Integer id);

	/**
	 * 根据操作员id查询企业对象
	 * @param operatorUserId
	 * @return
	 */
	OrganizationVO getCorpOrganizationTreeByUid(Integer operatorUserId);

	/**
	 * 根据操作员id查询部门组织对象
	 * @param operatorUserId
	 * @return
	 */
	OrganizationVO queryOrganizationByUserId(Integer operatorUserId);

	/**
	 * 获取整个企业组织树
	 */
	OrganizationVO getCorpOrganizationTreeByCorpId(Integer corpId);

	/**
	 * 获取以指定组织id查询子组织树，返回企业树的子树，orgId为根结点（不一定是整个企业树的根）
	 * @param orgId
	 * @return
	 */
	OrganizationVO getSubOrganizationTree(Integer orgId);

	/**
	 * 获取指定用户id查询所在部门组织树，返回企业树的子树，orgId为根结点（不一定是整个企业树的根）
	 * @param operatorUserId
	 * @return
	 */
	OrganizationVO getSubOrganizationTreeByUserId(Integer operatorUserId);

	/**
	 * 获取指定部门的所有子组织id
	 * @param orgId
	 * @return
	 */
	List<Integer> getSubOrgIds(Integer orgId, boolean includeRoot);

	/**
	 * 获取指定部门id查询所在部门组织树，返回企业树的子树，orgId为根结点
	 * @param orgId
	 * @return
	 */
	List<Integer> getUserIdsOfSubOrgIdsByOrgId(Integer orgId);

	/**
	 * 从指定部门id查询指定子部门id是否是该部门的子部门（树结构上的子树）
	 * @param orgId
	 * @param subOrgId
	 * @return
	 */
	boolean checkIfSubOrg(Integer orgId, Integer subOrgId);

	void onDeleteUser(UserVO user);

	/**
	 * 删除组织对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加组织对象
	 * @param organization 对象参数
	 * @return 返回结果
	 */
	OrganizationVO insert(OrganizationVO organization);

	/**
	 * 创建组织
	 * @param organization
	 * @return
	 */
	OrganizationVO create(OrganizationVO organization);

	/**
	 * 修改组织对象
	 * @param organization 对象参数
	 */
	void updateByIdSelective(OrganizationVO organization);

	/**
	 * 带条件批量查询组织列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<OrganizationVO> queryOrganizationList(OrganizationQuery query);

	// 渠道商组织树查询，每个元素是一个一级组织
	List<OrganizationVO> queryDistributorOrganizationTrees();

	List<UserVO> queryUsersByOrgIdAndUserRole(Integer orgId, RoleTypeEnum roleType, CustomRoleEnum customRole);

	// 查询指定组织id的父组织id路径
	List<Integer> queryParentOrgIdPath(Integer orgId);

	/**
	 * 带条件查询组织数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryOrganizationCount(OrganizationQuery query);

	/**
	 * 带条件分页查询组织
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<OrganizationVO> queryOrganizationByPage(OrganizationQuery query);

	/**
	 * 通过【企查查】查询企业信息
	 * @param corpName 企业名称
	 * @param pageIndex 页码
	 * @return 查询到的相关信息
	 */
	QiChaChaModelPageVO qiChaChaFuzzyQuery(String corpName,Integer pageIndex);

	/**
	 * 更新企业认证信息
	 *
	 * @param corpAuthVO 企业认证信息
	 */
	void updateCorpAuthInfo(CorpAuthVO corpAuthVO);

	/**
	 * 查询所有渠道根组织
	 *
	 * @return 结果
	 */
	List<OrganizationVO> queryDistributorCorps();

	/**
	 * 分页查询渠道根组织
	 * @param query 查询条件
	 * @return 分页结果
	 */
	PageInfo<OrganizationVO> queryDistributorCorpByPage(OrganizationQuery query);


	/**
	 * 查询直接子组织
	 * @param orgId 父组织 id
	 * @return 结果
	 */
	List<OrganizationVO> querySubOrg(Integer orgId);

	/**
	 * 获取根组织销售类型
	 * @param orgId 组织id
	 * @return 结果
	 */
	SalesTypeEnum getRootSalesType(Integer orgId);

	/**
	 * 获取销售类型
	 * @param orgId 组织id
	 * @return 结果
	 */
	SalesTypeEnum getSalesType(Integer orgId);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.constants;

/**
 * 事件常量
 *
 * <AUTHOR>
 * @version : EventConstants.java, v 0.1 2024/8/26 19:19 renxiao.wu Exp $
 */
public interface EventConstants {
    /** 文件同步事件 */
    String TOPIC_FILE_SYNC = "topic-file-sync";

    /** 文件同步事件group */
    String GROUP_FILE_SYNC = "group-aigc-file-sync";

    /** 上传lora到oss事件 */
    String TOPIC_UPLOAD_LORA_TO_OSS = "topic-upload-lora-to-oss";

    /** 上传lora到oss事件group */
    String GROUP_UPLOAD_LORA_TO_OSS = "group-aigc-upload-lora-to-oss";

    /** 批次任务触发事件 */
    String TOPIC_CREATIVE_TRIGGER = "topic-creative-trigger";

    /** 批次任务触发事件 */
    String GROUP_CREATIVE_TRIGGER = "group-aigc-topic-creative-trigger";

    String TOPIC_CREATE_TEST_IMGS = "topic-create-test-imgs";

    String GROUP_CREATE_TEST_IMGS = "group-create-test-imgs";

    /** 文件夹同步事件 */
    String TOPIC_FOLDER_SYNC = "topic-folder-sync";

    /** 文件夹同步事件group */
    String GROUP_FOLDER_SYNC = "group-aigc-folder-sync";

    /** 文件夹拷贝事件 */
    String TOPIC_FOLDER_COPY = "topic-folder-copy";

    /** 文件夹拷贝事件group */
    String GROUP_FOLDER_COPY = "group-aigc-folder-copy";

    /** 自动交付延迟消息 */
    String TOPIC_AUTO_DELIVERY = "topic-auto-delivery";

    /** 自动交付延迟消息group */
    String GROUP_AUTO_DELIVERY = "group-aigc-uto-delivery";

    /** 文件操作消息topic */
    String TOPIC_FILE_OPERATE = "topic-file-operate";

    /** 文件操作消息group */
    String GROUP_FILE_OPERATE = "group-file-operate";
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component;

import java.util.List;

import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;

/**
 * 缓存的服务
 *
 * <AUTHOR>
 * @version : CachedService.java, v 0.1 2023/9/12 18:09 renxiao.wu Exp $
 */
public interface CachedService<CacheClz extends ModifyTimeClz, KeyClz, ValueClz> {

    /**
     * 根据key值获取到缓存模型
     *
     * @param key 关键字
     * @return 缓存值
     */
    CacheClz queryByKey(KeyClz key);

    /**
     * 根据key值获取到缓存值
     *
     * @param key 关键字
     * @return 缓存值
     */
    ValueClz queryValueByKey(KeyClz key);

    /**
     * 查询所有缓存数据
     *
     * @return 缓存数据
     */
    List<CacheClz> queryAll();

    /**
     * 刷新缓存
     */
    void refresh();

    /**
     * 强制刷新缓存
     */
    void forceRefresh();

    /**
     * 异步刷新
     */
    void asyncRefresh();
}

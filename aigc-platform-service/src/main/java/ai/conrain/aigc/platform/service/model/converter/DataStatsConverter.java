/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.converter;

import java.util.List;

import ai.conrain.aigc.platform.dal.entity.StatsDeliveryDO;
import ai.conrain.aigc.platform.service.model.stats.DeliveryStatsItem;
import ai.conrain.aigc.platform.service.model.stats.DeliveryStatsVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;

/**
 * 数据统计转换器
 *
 * <AUTHOR>
 * @version : DataStatsConverter.java, v 0.1 2024/9/14 12:09 renxiao.wu Exp $
 */
public abstract class DataStatsConverter {
    public static DeliveryStatsVO convert(StatsDeliveryDO from) {
        DeliveryStatsVO to = new DeliveryStatsVO();
        to.setDate(from.getDt());
        to.setAutoDelivery(from.getAutoDelivery());
        to.setTotal(from.getTotal());
        to.setNormal(
            new DeliveryStatsItem(from.getNormalTotal(), from.getNormal24(), from.getNormal48(), from.getNormalOther(),
                from.getNormalUnknown()));
        to.setVip(new DeliveryStatsItem(from.getVipTotal(), from.getVip24(), from.getVip48(), from.getVipOther(),
            from.getVipUnknown()));
        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<DeliveryStatsVO> doList2VOList(List<StatsDeliveryDO> list) {
        return CommonUtil.listConverter(list, DataStatsConverter::convert);
    }
}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO;
import ai.conrain.aigc.platform.service.model.query.StatsUserOperateQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsUserOperateVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;
import java.util.Map;

/**
 * 用户操作统计表（出图、下载）
 * Service定义
 *
 * <AUTHOR>
 * @version StatsUserOperateService.java v 0.1 2025-04-25 02:47:41
 */
public interface StatsUserOperateService {

    /**
     * 查询用户操作统计表（出图、下载）
     * 对象
     *
     * @param id 主键
     * @return 返回结果
     */
    StatsUserOperateVO selectById(Integer id);

    /**
     * 删除用户操作统计表（出图、下载）
     * 对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加用户操作统计表（出图、下载）
     * 对象
     *
     * @param statsUserOperate 对象参数
     * @return 返回结果
     */
    StatsUserOperateVO insert(StatsUserOperateVO statsUserOperate);

    /**
     * 修改用户操作统计表（出图、下载）
     * 对象
     *
     * @param statsUserOperate 对象参数
     */
    void updateByIdSelective(StatsUserOperateVO statsUserOperate);

    /**
     * 带条件批量查询用户操作统计表（出图、下载）
     * 列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<StatsUserOperateVO> queryStatsUserOperateList(StatsUserOperateQuery query);

    /**
     * 带条件查询用户操作统计表（出图、下载）
     * 数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryStatsUserOperateCount(StatsUserOperateQuery query);

    /**
     * 带条件分页查询用户操作统计表（出图、下载）
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<StatsUserOperateVO> queryStatsUserOperateByPage(StatsUserOperateQuery query);

    /**
     * 批量插入或更新
     *
     * @param statsList 统计数据列表
     * @return 插入或更新数量
     */
    int batchInsertOrUpdate(List<StatsUserOperateVO> statsList);

    /**
     * 获取用户创作数据
     *
     * @param userId   用户id
     * @param isParent 是否获取父级数据
     * @return 用户创作数据
     */
    Map<String, Object> getUserCreativeData(Integer userId, Boolean isParent);
}
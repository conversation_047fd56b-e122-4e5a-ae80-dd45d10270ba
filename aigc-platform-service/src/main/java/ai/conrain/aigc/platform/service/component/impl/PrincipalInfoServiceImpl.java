package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;

import ai.conrain.aigc.platform.service.component.AssessmentPlanService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.model.biz.ContractModel;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO;
import ai.conrain.aigc.platform.dal.example.PrincipalInfoExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.PrincipalInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;
import ai.conrain.aigc.platform.service.model.converter.PrincipalInfoConverter;
import ai.conrain.aigc.platform.dal.dao.PrincipalInfoDAO;
import ai.conrain.aigc.platform.service.component.PrincipalInfoService;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CONTRACT_INFO;

/**   
 * PrincipalInfoService实现
 *
 * <AUTHOR>
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
@Slf4j
@Service
public class PrincipalInfoServiceImpl implements PrincipalInfoService {

	/** DAO */
	@Autowired
	private PrincipalInfoDAO principalInfoDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private AssessmentPlanService assessmentPlanService;

	@Override
	public PrincipalInfoVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		PrincipalInfoDO data = principalInfoDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return PrincipalInfoConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = principalInfoDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除PrincipalInfo失败");
	}

	@Override
	public PrincipalInfoVO insert(PrincipalInfoVO principalInfo) {
		AssertUtil.assertNotNull(principalInfo, ResultCode.PARAM_INVALID, "principalInfo is null");
		AssertUtil.assertTrue(principalInfo.getId() == null, ResultCode.PARAM_INVALID, "principalInfo.id is present");

		//创建时间、修改时间兜底
		if (principalInfo.getCreateTime() == null) {
			principalInfo.setCreateTime(new Date());
		}

		if (principalInfo.getModifyTime() == null) {
			principalInfo.setModifyTime(new Date());
		}

		PrincipalInfoDO data = PrincipalInfoConverter.vo2DO(principalInfo);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = principalInfoDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建PrincipalInfo失败");
		AssertUtil.assertNotNull(data.getId(), "新建PrincipalInfo返回id为空");
		principalInfo.setId(data.getId());
		return principalInfo;
	}

	@Override
	public PrincipalInfoVO insertOrUpdate(PrincipalInfoVO principalInfo) {
		AssertUtil.assertNotNull(principalInfo, ResultCode.PARAM_INVALID, "principalInfo is null");
		AssertUtil.assertNotNull(principalInfo.getPrincipalType(), ResultCode.PARAM_INVALID, "principalType is null");
		AssertUtil.assertNotNull(principalInfo.getPrincipalId(), ResultCode.PARAM_INVALID, "principalId is null");

		//创建时间、修改时间兜底
		if (principalInfo.getCreateTime() == null) {
			principalInfo.setCreateTime(new Date());
		}
		//添加创建人 id (如果已经存在, 不会覆盖)
		if (principalInfo.getCreatorUserId() == null) {
			principalInfo.setCreatorUserId(OperationContextHolder.getOperatorUserId());
		}
		// 添加修改人 id (会覆盖)
		if (principalInfo.getModifyUserId() == null) {
			principalInfo.setModifyUserId(OperationContextHolder.getOperatorUserId());
		}

		//修改时间必须更新
		principalInfo.setModifyTime(new Date());

		PrincipalInfoDO data = PrincipalInfoConverter.vo2DO(principalInfo);
		Integer n = principalInfoDAO.insertOrUpdate(data);
		AssertUtil.assertTrue(n >= 1, ResultCode.BIZ_FAIL, "插入或更新PrincipalInfo失败");
		
		// 如果原来没有ID，则设置新生成的ID
		if (principalInfo.getId() == null && data.getId() != null && data.getId() > 0) {
			principalInfo.setId(data.getId());
		}
		
		return principalInfo;
	}

	@Override
	public void updateByIdSelective(PrincipalInfoVO principalInfo) {
		AssertUtil.assertNotNull(principalInfo, ResultCode.PARAM_INVALID, "principalInfo is null");
    	AssertUtil.assertTrue(principalInfo.getId() != null, ResultCode.PARAM_INVALID, "principalInfo.id is null");

		//修改时间必须更新
		principalInfo.setModifyTime(new Date());
		PrincipalInfoDO data = PrincipalInfoConverter.vo2DO(principalInfo);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = principalInfoDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新PrincipalInfo失败，影响行数:" + n);
	}

	@Override
	public List<PrincipalInfoVO> queryPrincipalInfoList(PrincipalInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PrincipalInfoExample example = PrincipalInfoConverter.query2Example(query);

		List<PrincipalInfoDO> list = principalInfoDAO.selectByExample(example);
			return PrincipalInfoConverter.doList2VOList(list);
	}

	@Override
	public Long queryPrincipalInfoCount(PrincipalInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		PrincipalInfoExample example = PrincipalInfoConverter.query2Example(query);
		long c = principalInfoDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询通用主体属性
	 */
	@Override
	public PageInfo<PrincipalInfoVO> queryPrincipalInfoByPage(PrincipalInfoQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<PrincipalInfoVO> page = new PageInfo<>();

		PrincipalInfoExample example = PrincipalInfoConverter.query2Example(query);
		long totalCount = principalInfoDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<PrincipalInfoDO> list = principalInfoDAO.selectByExample(example);
		page.setList(PrincipalInfoConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public PrincipalInfoVO selectByPrincipal(PrincipalModel principal, String key) {
		PrincipalInfoExample example = new PrincipalInfoExample();
		example.createCriteria()
				.andPrincipalTypeEqualTo(principal.getType().getCode())
				.andPrincipalIdEqualTo(principal.getId())
				.andInfoKeyEqualTo(key);
		List<PrincipalInfoDO> list = principalInfoDAO.selectByExample(example);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return PrincipalInfoConverter.do2VO(list.get(0));
	}

	@Override
	public List<PrincipalInfoVO> queryRelatedCommissionPrincipal(PrincipalModel principal) {
		List<PrincipalInfoDO> list = principalInfoDAO.selectCommissionRelatePrincipal(principal.getId());
		return CollectionUtils.isEmpty(list) ?
				new ArrayList<>() :
				PrincipalInfoConverter.doList2VOList(list);
	}

	@Override
	public void modifyContractDate(Integer userId, String contractDate) {
		// 查询用户信息
		UserVO userVO = userService.selectById(userId);
		OrganizationVO orgVO = organizationService.queryOrganizationByUserId(userId);
		// 获取主体信息
		PrincipalModel principal = assessmentPlanService.getPrincipal(userVO, orgVO);
		AssertUtil.assertNotNull(principal, ResultCode.BIZ_FAIL, "当前用户暂不能录入合同时间");

		ContractModel contractModel = new ContractModel();
		contractModel.setUserId(userId);
		contractModel.setBeginDate(contractDate);

		PrincipalInfoVO principalInfoVO = new PrincipalInfoVO();
		principalInfoVO.setPrincipalType(principal.getType());
		principalInfoVO.setPrincipalId(principal.getId());
		principalInfoVO.setInfoKey(KEY_CONTRACT_INFO);
		principalInfoVO.setInfoValue((JSONObject) JSONObject.toJSON(contractModel));

		this.insertOrUpdate(principalInfoVO);

		// 同时创建考核计划
		assessmentPlanService.initAssessmentPlan(principal, userVO, orgVO);
	}

	@Override
	public String queryContractDate(PrincipalModel principal) {
		PrincipalInfoExample  example = new PrincipalInfoExample();
		example.createCriteria()
			.andPrincipalTypeEqualTo(principal.getType().getCode())
			.andPrincipalIdEqualTo(principal.getId())
			.andInfoKeyEqualTo(KEY_CONTRACT_INFO);
		List<PrincipalInfoDO> list = principalInfoDAO.selectByExample(example);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return JSONObject.parseObject(list.get(0).getInfoValue(), ContractModel.class).getBeginDate();
	}

	@Override
	public boolean isInitYear(PrincipalModel principal) {
		String contractDate = queryContractDate(principal);
		if (contractDate == null) {
			log.warn("[签约首年]当前结算主体没有录入合同时间, 主体TYPE:{} 主体ID:{}",principal.getType(), principal.getId());
			return false;
		}
		Date date = DateUtils.parseSimpleDate(contractDate);
		// 获取合同时间之后12个月, 例如: 合同时间 2025-06-11, 12个月后 2026-06-11 00:00:00,
		// 如果当前时间在此时间日后, 判定为签约首年
		Date dateAfterInitYear = DateUtils.getDateAfterNNatureMonths(date, 12, false);
		return !DateUtils.beforeNow(dateAfterInitYear);
	}

}
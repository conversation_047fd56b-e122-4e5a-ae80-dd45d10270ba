/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.CachedService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.common.ModifyTimeClz;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import jakarta.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 缓存服务基类
 *
 * <AUTHOR>
 * @version : AbstractCachedService.java, v 0.1 2023/9/12 17:08 renxiao.wu Exp $
 */
@Slf4j
public abstract class AbstractCachedService<CacheClz extends ModifyTimeClz, KeyClz, ValueClz>
    implements CachedService<CacheClz, KeyClz, ValueClz> {
    @Autowired
    private TairService tairService;
    /** list类型缓存 */
    private List<CacheClz> cacheList;

    /** map类型缓存 */
    private Map<KeyClz, CacheClz> cacheMap;

    /** 最新缓存刷新时间 */
    private long lastRefreshTime = 0L;

    /** 最新版本号 */
    private long lastVersion = 0L;

    /**
     * 将结果结构化存储
     *
     * @param list 缓存数据
     * @return 结构化后的结果
     */
    protected List<CacheClz> structuring(List<CacheClz> list) {
        return list;
    }

    /**
     * 将数据放入map中
     *
     * @param cache 数据模型
     * @return 反馈key值
     */
    protected abstract KeyClz getKey(CacheClz cache);

    /**
     * 加载所有数据
     *
     * @return 加载所有数据
     */
    protected abstract List<CacheClz> loadAll();

    /**
     * 从数据模型中获取值
     *
     * @param cache 缓存模型
     * @return 缓存值
     */
    protected abstract ValueClz getValue(CacheClz cache);

    /**
     * 根据key值获取到缓存值
     *
     * @param key 关键字
     * @return 缓存值
     */
    @Override
    public ValueClz queryValueByKey(KeyClz key) {
        CacheClz cache = queryByKey(key);
        if (null == cache) {
            return null;
        }
        return getValue(cache);
    }

    @Override
    public CacheClz queryByKey(KeyClz key) {
        refreshOrNot();

        if (MapUtils.isEmpty(cacheMap)) {
            return null;
        }
        return cacheMap.get(key);
    }

    /**
     * 查询所有缓存数据
     *
     * @return 缓存数据
     */
    public List<CacheClz> queryAll() {
        refreshOrNot();

        if (null == cacheList) {
            return new ArrayList<>(0);
        }

        return cacheList;
    }

    /**
     * 刷新缓存
     */
    public void refresh() {
        refresh(0L);
    }

    /**
     * 一般删除情况下取不到最后更新时间的，需要强制刷新
     */
    @Override
    public void forceRefresh() {
        log.info("开始执行强制刷新");
        lastVersion = System.currentTimeMillis();
        refresh(lastVersion);
    }

    @Async
    @Override
    public void asyncRefresh() {
        refresh();
    }

    /**
     * 加载bean时进行一次刷新
     */
    @PostConstruct
    public void init() {
        refresh();
    }

    /**
     * 获取缓存过期时间，精确到毫秒
     *
     * @return 缓存过期时间
     */
    protected long getCacheExpireTimeMillis() {
        return CommonConstants.LOCAL_CACHE_EXPIRE_TIME_MILLIS;
    }

    /**
     * 判断是否需要刷新
     */
    private void refreshOrNot() {
        String cacheVersionKey = getCacheVersionKey();

        //当前线程已经刷新过，则直接返回
        Map<String, Boolean> tairQueryCacheMap = OperationContextHolder.getContext().getTairQueryCacheMap();
        if (tairQueryCacheMap.containsKey(cacheVersionKey)) {
            return;
        }

        tairQueryCacheMap.put(cacheVersionKey, true);

        Long version = tairService.getObject(cacheVersionKey, Long.class);

        //如果上一次间隔时间小于5分钟则不刷新
        if (System.currentTimeMillis() - lastRefreshTime < getCacheExpireTimeMillis() && version != null
            && version == lastVersion) {
            //TODO 这里还有并发问题，待解决
            return;
        }

        refresh(version != null ? version : 0L);
    }

    /**
     * 刷新缓存
     *
     * @param newLastVersion 最新版本号
     */
    private void refresh(long newLastVersion) {
        //先设置刷新时间，防止其他请求也到这个流程中
        lastRefreshTime = System.currentTimeMillis();
        long start = System.currentTimeMillis();

        List<CacheClz> list = loadAll();

        //增加临时变量，进行结构化处理，以便map.put时能填充到children数据
        List<CacheClz> temp = structuring(list);

        //if (CollectionUtils.isEmpty(list)) {
        //    return;
        //}

        Map<KeyClz, CacheClz> map = new HashMap<>(list.size());

        for (CacheClz item : list) {
            map.put(getKey(item), item);

            if (item.getModifyTime() != null && item.getModifyTime().getTime() > lastVersion) {
                lastVersion = item.getModifyTime().getTime();
            }
        }

        //指针移动
        this.cacheList = temp;
        this.cacheMap = map;

        if (newLastVersion > lastVersion) {
            lastVersion = newLastVersion;
        }

        tairService.setObject(getCacheVersionKey(), lastVersion, CommonConstants.ONE_DAY_SECONDS);

        log.info("加载缓存数据{},耗时{}ms", getClass().getSimpleName(), System.currentTimeMillis() - start);
    }

    /**
     * 获取缓存的版本key
     *
     * @return 版本key
     */
    private String getCacheVersionKey() {
        return "_tair_" + getClass().getSimpleName() + "_version";
    }
}

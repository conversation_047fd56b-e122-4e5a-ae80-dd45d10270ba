/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.helper.WeakLockHelper;
import ai.conrain.aigc.platform.service.helper.WeakLockHelper.WeakType;
import java.net.URI;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.ServiceUtils;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.PromptWebSocketService;
import ai.conrain.aigc.platform.service.model.biz.PromptWSSession;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import reactor.core.publisher.Mono;

/**
 * prompt websocket服务实现
 *
 * <AUTHOR>
 * @version : PromptWebSocketServiceImpl.java, v 0.1 2024/5/13 14:55 renxiao.wu Exp $
 */
@Slf4j
@Component
public class PromptWebSocketServiceImpl implements PromptWebSocketService {
    private final ReactorNettyWebSocketClient client = new ReactorNettyWebSocketClient();
    @Value("${comfyui.ws.url}")
    private String comfyuiWsUrl;
    @Autowired
    private TairService tairService;
    @Lazy
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private WeakLockHelper weakLockHelper;

    @Async
    @Override
    public void startListening(String clientId, String url) {
        PromptWSSession promptWSSession = tairService.getObject(getClientKey(clientId, url), PromptWSSession.class);
        if (promptWSSession != null && !promptWSSession.isExpired()) {
            log.info("ws连接，ws任务执行进度监听已存在且未过期，直接返回，clientId={}", clientId);
            return;
        }

        promptWSSession = new PromptWSSession(clientId, url);
        //缓存5分钟
        saveToCache(promptWSSession);

        if (StringUtils.isBlank(url)) {
            url = comfyuiWsUrl;
        }
        String wsUrl = ServiceUtils.transServiceToWs(url) + "/ws?clientId=" + clientId;

        PromptWSSession finalPromptWSSession = promptWSSession;
        client.execute(URI.create(wsUrl), (session) -> this.initSession(session, finalPromptWSSession)).doOnError(
            Throwable::printStackTrace).subscribe();

        log.info("ws连接，新启ws监听任务进度，clientId={}", clientId);
    }

    private Mono<Void> initSession(WebSocketSession session, PromptWSSession promptWSSession) {
        return session.receive().map(WebSocketMessage::getPayloadAsText).doOnNext(
            (message) -> handleTextMessage(message, promptWSSession)).then();
    }

    protected void handleTextMessage(String message, PromptWSSession promptWSSession) {
        MDC.put("traceId", CommonUtil.uuid());
        MDC.put("env", StringUtils.upperCase(EnvUtil.getEnv()));

        log.info("ws连接，Received:{}", message);

        try {
            ComfyUIUtils.parseWsMessage(promptWSSession, message);

            //更新记录状态
            if (promptWSSession.isNeedUpdate() && StringUtils.isNotEmpty(promptWSSession.getPromptId())) {

                creativeTaskService.syncStatusByWS(promptWSSession.getPromptId(), promptWSSession.getStatus(),
                    promptWSSession.getDoneNodes(), promptWSSession.getNodeMax(), promptWSSession.getNodeSchadule());
            }

            //大于5秒时才更新
            if (System.currentTimeMillis() - promptWSSession.getLastActiveTime() > 5 * 1000) {
                saveToCache(promptWSSession);
            }

            promptWSSession.refreshLastActiveTime();
        } finally {
            MDC.remove("env");
            MDC.remove("traceId");
        }
    }

    private void saveToCache(PromptWSSession promptWSSession) {
        tairService.setObject(getClientKey(promptWSSession.getClientId(), promptWSSession.getUrl()), promptWSSession,
            5 * 60);
    }

    private static String getClientKey(String clientId, String url) {
        return "_ws_client" + clientId + "_" + url;
    }
}

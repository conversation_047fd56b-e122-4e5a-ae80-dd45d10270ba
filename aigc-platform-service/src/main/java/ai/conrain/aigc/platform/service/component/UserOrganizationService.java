package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;

import java.util.List;

/**
 * 用户组织关系 Service定义
 *
 * <AUTHOR>
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
public interface UserOrganizationService {
	
	/**
	 * 查询用户组织关系对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	UserOrganizationVO selectById(Integer id);

	/**
	 * 查询用户组织关系对象
	 * @param   userIds 用户id
	 * @return 返回结果
	 */
	List<UserOrganizationVO> selectByUserIds(List<Integer> userIds);

	/**
	 * 删除用户组织关系对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	void deleteByOrgId(Integer orgId);

	/**
	 * 添加用户组织关系对象
	 * @param userOrganization 对象参数
	 * @return 返回结果
	 */
	UserOrganizationVO insert(UserOrganizationVO userOrganization);

	/**
	 * 修改用户组织关系对象
	 * @param userOrganization 对象参数
	 */
	void updateById(UserOrganizationVO userOrganization);

	/**
	 * 全量查询
	 * return 结果
	 */
	List<UserOrganizationVO> findAll();

	void deleteByUserId(Integer userId);

	List<UserOrganizationVO> findAllByOrgIds(List<Integer> orgIds);

    List<UserOrganizationVO> queryUserPointList(List<Integer> userIdList);

	/**
	 *
	 * 查询直接用户组织关系
	 * @param orgId 组织账号
	 * @return 结果
	 */
	List<UserOrganizationVO> selectByOrgId(Integer orgId);

	UserOrganizationVO findByUserId(Integer userId);
}
package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.CreativeBatchDO;
import ai.conrain.aigc.platform.dal.entity.UserCountDO;
import ai.conrain.aigc.platform.dal.example.CreativeBatchExample;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModelExt;
import ai.conrain.aigc.platform.service.model.biz.VideoClipTask;
import ai.conrain.aigc.platform.service.model.common.UserCountVO;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.request.*;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.PromptUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.regex.Pattern;

import static ai.conrain.aigc.platform.service.constants.BizConstants.BIZ_TAG;
import static ai.conrain.aigc.platform.service.constants.BizConstants.EXAMPLE_IMAGES;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * CreativeBatchConverter
 *
 * @version CreativeBatchService.java v 0.1 2024-05-09 03:34:03
 */
@Slf4j
public class CreativeBatchConverter {
    /** 动作正则 */
    private static final Pattern ACTION_PATTERN = Pattern.compile("^[a-zA-Z]+ing ");

    /**
     * DO -> VO
     */
    public static CreativeBatchVO do2VO(CreativeBatchDO from) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setId(from.getId());
        to.setModelId(from.getModelId());
        to.setModelType(ModelTypeEnum.getByCode(from.getModelType()));
        to.setType(CreativeTypeEnum.getByCode(from.getType()));
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        to.setStatus(CreativeStatusEnum.getByCode(from.getStatus()));
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTitle(from.getTitle());
        to.setBizType(CreativeBizTypeEnum.getByCode(from.getBizType()));
        to.setAigcRequest(from.getAigcRequest());

        List<String> resultImages = StringUtils.isBlank(from.getResultImages()) ? null : JSONArray.parseArray(
            from.getResultImages()).toJavaList(String.class);
        to.setResultImages(resultImages);

        to.setOperatorNick(from.getOperatorNick());
        to.setUserNick(from.getUserNick());

        to.setVideoClipGenTasks(getVideoClipGenTasks(to));

        return to;
    }

    public static void fillModelExt(CreativeBatchVO to, MaterialModelVO model) {
        if (model == null || MapUtils.isEmpty(model.getExtInfo())) {
            return;
        }

        JSONObject modelExt = model.getExtInfo();

        //服装显示图片（高清版的url）
        if (modelExt.getString(CommonConstants.highResModelShowImgUrl) != null) {
            to.setModelShowImg(modelExt.getString(CommonConstants.highResModelShowImgUrl));
        }

        //"clothMark": "{\"markedColor\":\"red\"}"
        if (modelExt.containsKey(CommonConstants.KEY_CLOTH_MARK) && CommonUtil.isValidJson(
            modelExt.getString(CommonConstants.KEY_CLOTH_MARK))) {
            JSONObject clothMark = modelExt.getJSONObject(CommonConstants.KEY_CLOTH_MARK);
            if (clothMark != null && clothMark.containsKey(CommonConstants.markedColor)) {
                to.setModelMarkedColor(clothMark.getString(CommonConstants.markedColor));
            }
        }
    }

    private static List<VideoClipTask> getVideoClipGenTasks(CreativeBatchVO target) {
        List<VideoClipTask> videoClipTasks = new ArrayList<>();
        if (target != null && target.getType() != null && target.getType().isVideoCreative()
            && target.getExtInfo() != null) {
            for (String key : target.getExtInfo().keySet()) {
                if (key.startsWith(KEY_TEMP_VIDEO_TASK)) {
                    if (CommonUtil.isValidJson(target.getStringFromExtInfo(key))) {
                        String batchExtInfo = target.getStringFromExtInfo(key);
                        // 如果角色是商家，并且他的视频已经退点了 不显示视频的url
                        if (Objects.requireNonNull(OperationContextHolder.getCurrentUser()).getRoleType() == RoleTypeEnum.MERCHANT && CommonUtil.isValidJson(batchExtInfo)) {
                            JSONObject batchExtObject = JSONObject.parseObject(batchExtInfo);
                            // 如果存在退点标记并且为Y,设置输出的视频url为null
                            VideoClipTask videoClipTask = JSONObject.parseObject(batchExtInfo,
                                VideoClipTask.class);
                            if (batchExtObject.containsKey(CommonConstants.REFUND_MUSE_POINT_COMPLETED) && "Y".equals(batchExtObject.getString(CommonConstants.REFUND_MUSE_POINT_COMPLETED))) {
                                videoClipTask.setOssVideoUrl(null);
                                videoClipTask.setOutVideoUrl(null);
                            }
                            videoClipTasks.add(videoClipTask);

                        }else {
                            videoClipTasks.add(
                                JSONObject.parseObject(target.getStringFromExtInfo(key), VideoClipTask.class));
                        }

                    }
                }
            }
        }
        return videoClipTasks;
    }

    public static CreativeBatchVO request2VO(AddCreativeRequest request, String showImage) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setModelId(request.getLoraId());
        to.setUserId(OperationContextHolder.getMasterUserId());
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        to.setImageProportion(request.getProportion());
        to.setBatchCnt(request.getImageNum());
        to.setShowImage(showImage);
        to.setStatus(CreativeStatusEnum.QUEUE);
        to.setType(CreativeTypeEnum.CREATE_IMAGE);
        to.setBizType(CreativeBizTypeEnum.getByCode(request.getBizType()));
        if (StringUtils.isNotBlank(request.getBizTag())) {
            to.addExtInfo(BIZ_TAG, request.getBizTag());
        }
        if (StringUtils.isNotBlank(request.getClothColor())) {
            to.addExtInfo(KEY_CLOTH_COLOR, request.getClothColor());
        }
        return to;
    }

    /**
     * 换头换背景 request -> VO
     *
     * @param request 请求
     * @return VO
     */
    public static CreativeBatchVO request2VO(FaceSceneSwitchRequest request) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setUserId(OperationContextHolder.getMasterUserId());
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        to.setImageProportion("NONE");
        to.setBatchCnt(request.getImageNum());
        to.setStatus(CreativeStatusEnum.QUEUE);
        to.setShowImage(request.getImage());
        to.setType(CreativeTypeEnum.FACE_SCENE_SWITCH);
        return to;
    }

    /**
     * 细节修补 request -> VO
     */
    public static CreativeBatchVO request2VO(RepairDetailRequest request) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setBatchCnt(request.getImageNum());
        to.setType(CreativeTypeEnum.REPAIR_DETAIL);
        to.setStatus(CreativeStatusEnum.QUEUE);
        to.setShowImage(request.getImage());
        to.setUserId(OperationContextHolder.getMasterUserId());
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        to.setImageProportion("NONE");
        return to;
    }

    /**
     * 大牌上身
     */
    public static CreativeBatchVO request2VO(BrandTryOnRequest request) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setBatchCnt(request.getReferenceImages().size());
        to.setType(CreativeTypeEnum.BRAND_TRY_ON);
        to.setStatus(CreativeStatusEnum.QUEUE);
        to.setShowImage(request.getClothImage());
        to.setUserId(OperationContextHolder.getMasterUserId());
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        to.setImageProportion("NONE");
        return to;
    }

    /**
     * 消除笔 V2
     */
    public static CreativeBatchVO request2VO(EraseBrushV2Request request) {
        CreativeBatchVO to = new CreativeBatchVO();
        to.setUserId(OperationContextHolder.getMasterUserId());
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        to.setType(CreativeTypeEnum.ERASE_BRUSH_V2);
        // 任务状态直接置为 FINISHED, 另添加 customStatus 到扩展字段, 单独标记任务状态
        to.setStatus(CreativeStatusEnum.FINISHED);
        to.addExtInfo(KEY_CUSTOM_STATUS, "INIT");
        to.setShowImage(request.getOriginImage());
        to.setModelId(request.getModelId());
        to.setImageProportion("NONE");
        // 任务创建, 用户还未进行涂抹操作
        to.setBatchCnt(0);
        to.setResultImages(Collections.singletonList(request.getOriginImage()));
        return to;
    }

    /**
     * 基础款换衣 request -> VO
     */
    public static CreativeBatchVO request2VO(BasicChangingClothesRequest request) {
        // 构建数据
        CreativeBatchVO to = new CreativeBatchVO();

        // 设置类型
        to.setType(CreativeTypeEnum.BASIC_CHANGING_CLOTHES);
        // 设置用户id
        to.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置展示图片
        to.setShowImage(request.getClotheImage());
        // 设置批次数量
        to.setBatchCnt(request.getReferenceInfoList().size());
        // 设置状态(排队中)
        to.setStatus(CreativeStatusEnum.QUEUE);
        // 设置图片比例
        to.setImageProportion(StringUtils.defaultIfEmpty(request.getProportion(), "NONE"));
        // 设置展示图片
        to.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE, request.getClotheImage());
        // 设置扩展信息
        to.setExtInfo(CommonUtil.java2JSONObject(request));
        // 设置展示图片
        to.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE, request.getClotheImage());

        // 返回结果信息
        return to;
    }

    /**
     * 图片抠图 request -> VO
     */
    public static CreativeBatchVO request2VO(PictureMattingRequest request) {
        // 构建数据
        CreativeBatchVO to = new CreativeBatchVO();

        // 设置类型
        to.setType(CreativeTypeEnum.PICTURE_MATTING);
        // 设置用户id
        to.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置展示图片
        to.setShowImage(request.getClotheImage());
        // 设置批次数量
        to.setBatchCnt(1);
        // 设置状态(排队中)
        to.setStatus(CreativeStatusEnum.QUEUE);
        //默认按参考图片比例
        to.setImageProportion("NONE");
        // 设置扩展信息
        to.setExtInfo(CommonUtil.java2JSONObject(request));
        // 设置展示图片
        to.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE, request.getClotheImage());

        // 返回结果信息
        return to;
    }

    /**
     * 姿势示例图 request -> VO
     */
    public static CreativeBatchVO request2VO(PoseSampleDiagramRequest request) {
        // 构建数据
        CreativeBatchVO to = new CreativeBatchVO();

        // 设置类型
        to.setType(CreativeTypeEnum.POSE_SAMPLE_DIAGRAM);
        // 设置用户id
        to.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        to.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置批次数量
        to.setBatchCnt(request.getImgNum());
        // 设置状态(排队中)
        to.setStatus(CreativeStatusEnum.QUEUE);
        // 设置图片比例
        to.setImageProportion(StringUtils.defaultIfEmpty(request.getProportion(), "NONE"));
        // 设置扩展信息
        to.setExtInfo(CommonUtil.java2JSONObject(request));

        // 返回结果信息
        return to;
    }

    /**
     * VO -> DO
     */
    public static CreativeBatchDO vo2DO(CreativeBatchVO from) {
        CreativeBatchDO to = new CreativeBatchDO();
        to.setId(from.getId());
        to.setModelId(from.getModelId());
        to.setModelType(from.getModelType() != null ? from.getModelType().getCode() : null);
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTitle(from.getTitle());
        if (from.getBizType() != null) {
            to.setBizType(from.getBizType().getCode());
        }
        to.setAigcRequest(from.getAigcRequest());
        to.setResultImages(from.getResultImages() != null ? JSONObject.toJSONString(from.getResultImages()) : null);
        to.setDeleted(false);

        return to;
    }

    /**
     * DO -> Query
     */
    public static CreativeBatchQuery do2Query(CreativeBatchDO from) {
        CreativeBatchQuery to = new CreativeBatchQuery();
        to.setId(from.getId());
        to.setModelId(from.getModelId());
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setBizType(from.getBizType());
        to.setAigcRequest(from.getAigcRequest());
        to.setResultImages(from.getResultImages());

        return to;
    }

    /**
     * Query -> DO
     */
    public static CreativeBatchDO query2DO(CreativeBatchQuery from) {
        CreativeBatchDO to = new CreativeBatchDO();
        to.setId(from.getId());
        to.setModelId(from.getModelId());
        to.setUserId(from.getUserId());
        to.setShowImage(from.getShowImage());
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setBizType(from.getBizType());
        to.setAigcRequest(from.getAigcRequest());
        to.setResultImages(from.getResultImages());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CreativeBatchExample query2Example(CreativeBatchQuery from) {
        CreativeBatchExample to = new CreativeBatchExample();
        CreativeBatchExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getModelId())) {
            c.andModelIdEqualTo(from.getModelId());
        }
        if (!ObjectUtils.isEmpty(from.getModelType())) {
            c.andModelTypeEqualTo(from.getModelType());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getTypeNotIn())) {
            c.andTypeNotIn(from.getTypeNotIn());
        }
        if (!ObjectUtils.isEmpty(from.getTypeList())) {
            c.andTypeIn(from.getTypeList());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getShowImage())) {
            c.andShowImageEqualTo(from.getShowImage());
        }
        if (!ObjectUtils.isEmpty(from.getImageProportion())) {
            c.andImageProportionEqualTo(from.getImageProportion());
        }
        if (!ObjectUtils.isEmpty(from.getBatchCnt())) {
            c.andBatchCntEqualTo(from.getBatchCnt());
        }
        if (ObjectUtils.isEmpty(from.getBatchCnt()) && !ObjectUtils.isEmpty(from.getBatchCntGreaterThan())) {
            c.andBatchCntGreaterThan(from.getBatchCntGreaterThan());
        }
        if (!ObjectUtils.isEmpty(from.getPromptId())) {
            c.andPromptIdEqualTo(from.getPromptId());
        }
        if (!ObjectUtils.isEmpty(from.getResultPath())) {
            c.andResultPathEqualTo(from.getResultPath());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getStatusList())) {
            c.andStatusIn(from.getStatusList());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            if (!ObjectUtils.isEmpty(from.getBizTagOrOperator())) {
                c.andOperatorIdOrBizTagEqualTo(from.getOperatorId(), from.getBizTagOrOperator());
            } else {
                c.andOperatorIdEqualTo(from.getOperatorId());
            }
        }
        if (!ObjectUtils.isEmpty(from.getOperatorIds())) {
            c.andOperatorIdIn(from.getOperatorIds());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getDateFrom())) {
            c.andCreateTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getDateFrom()));
        }

        if (!ObjectUtils.isEmpty(from.getDateTo())) {
            c.andCreateTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getDateTo()));
        }

        if (!ObjectUtils.isEmpty(from.getNickLike())) {
            c.andUserNickLike("%" + from.getNickLike() + "%");
        }

        if (from.isOnlyShowDislike()) {
            c.andLikeContains("DISLIKE");
        }

        if (from.isOnlyShowLike()) {
            c.andLikeContains("LIKE");
        }

        if (from.isOnlyShowRefine()) {
            c.andRefineStatusEqualTo(TaskStatusEnum.INIT.getCode());
        }
        if (from.isOnlyShowNewModel()) {
            c.andNewModelEqualTo(true);
        }

        if (!ObjectUtils.isEmpty(from.getUserIds())) {
            c.andUserIdIn(from.getUserIds());
        }

        if (!ObjectUtils.isEmpty(from.getUserIdsNotIn())) {
            c.andUserIdNotIn(from.getUserIdsNotIn());
        }

        if (!ObjectUtils.isEmpty(from.getBizTag())) {
            c.andBizTagEqualTo(from.getBizTag());
        }

        if (!ObjectUtils.isEmpty(from.getBizTagNotIn())) {
            c.andBizTagNotIn(from.getBizTagNotIn());
        }

        if (from.isBizTagNull()) {
            c.andBizTagIsNull();
        }

        if (!ObjectUtils.isEmpty(from.getIsSelectNotSyncData())) {
            c.andSelectNotSyncData();
        }

        if (!ObjectUtils.isEmpty(from.getEnabledUserModel())) {
            c.andEnabledModel(from.getEnabledUserModel());
        }

        if (!ObjectUtils.isEmpty(from.getRelatedOperator())) {
            if (StringUtils.equals("unset", from.getRelatedOperator())) {
                c.andRelatedOperatorIsNull();
            } else {
                c.andRelatedOperatorEqualTo(from.getRelatedOperator());
            }
        }

        if (!ObjectUtils.isEmpty(from.getEndDate())) {
            c.andCreateTimeLessThan(from.getEndDate());
        }

        if (!ObjectUtils.isEmpty(from.getGarmentType())) {
            c.andGarmentTypeEquals(from.getGarmentType());
        }

        if (!ObjectUtils.isEmpty(from.getTitle())) {
            c.andTitleLike(from.getTitle());
        }

        if (!ObjectUtils.isEmpty(from.getCustomerType())) {
            if (StringUtils.equals("customer", from.getCustomerType())) {
                c.andUserTypeIsCustomer();
                c.andBizTagNotEqualTo(BizConstants.SYSTEM_IMAGES);
            } else if (StringUtils.equals("normal", from.getCustomerType())) {
                c.andUserTypeIsNormal();
                c.andBizTagNotEqualTo(BizConstants.SYSTEM_IMAGES);
            } else if (StringUtils.equals("vip", from.getCustomerType())) {
                c.andUserTypeIsVIP();
                c.andBizTagNotEqualTo(BizConstants.SYSTEM_IMAGES);
            } else if (StringUtils.equals("autoGen", from.getCustomerType())) {
                c.andBizTagEqualTo(BizConstants.SYSTEM_IMAGES);
            } else if (StringUtils.equals("operator", from.getCustomerType())) {
                c.andUserTypeIsNotCustomer();
                c.andBizTagNotEqualTo(BizConstants.SYSTEM_IMAGES);
                c.andBizTagNotEqualTo(BizConstants.TEST_CASE);
            }
        }

        if (from.isOnlyShowUserCreative()) {
            c.andBizTagNotEqualTo(BizConstants.SYSTEM_IMAGES);
            c.andBizTagNotEqualTo(BizConstants.TEST_CASE);
        }

        if (!ObjectUtils.isEmpty(from.getBizType())) {
            c.andBizTypeEqualTo(from.getBizType());
        }

        if (!ObjectUtils.isEmpty(from.getSceneId())) {
            c.andSceneIdEqualTo(from.getSceneId());
        }

        if (from.isOnlyShowDemo()) {
            c.andIsDemoTag();
        }

        if (!ObjectUtils.isEmpty(from.getGarmentTypeList())) {
            c.andGarmentTypeIn(from.getGarmentTypeList());
        }

        if (!ObjectUtils.isEmpty(from.getElementIds())) {
            c.andElementIdIn(from.getElementIds());
        }

        if (!ObjectUtils.isEmpty(from.getPostureIdList())) {
            c.andPostureIdIn(from.getPostureIdList());
        }

        if (!ObjectUtils.isEmpty(from.getCreateTimeBefore())) {
            c.andCreateTimeLessThan(from.getCreateTimeBefore());
        }

        if (!ObjectUtils.isEmpty(from.getStartTimeBefore())) {
            c.andStartTimeLessThan(from.getStartTimeBefore());
        }

        if (from.isOnlyNearingDelivery()) {
            if (StringUtils.isBlank(from.getCustomerType())) {
                c.andUserTypeIsCustomer();
            }
            c.andStatusIn(CreativeStatusEnum.getUncompleteStatusList());
            //创建时间在 20 小时前，并且创建时间在 1 周内
            c.andCreateTimeLessThan(DateUtils.addHours(new Date(), -20));
            c.andCreateTimeGreaterThan(DateUtils.addWeeks(new Date(), -1));
        }

        if (!ObjectUtils.isEmpty(from.getStartTime()) && !ObjectUtils.isEmpty(from.getEndTime())) {
            c.andCreateTimeBetween(from.getStartTime(), from.getEndTime());
        }

        if (from.isOnlyDownloaded()) {
            c.andOnlyDownloaded();
        }

        if (from.isRelatedToMe()) {
            c.andRelatedToUser(OperationContextHolder.getOperatorUserId());
        }

        //逻辑删除过滤
        for (CreativeBatchExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CreativeBatchVO> doList2VOList(List<CreativeBatchDO> list) {
        return CommonUtil.listConverter(list, CreativeBatchConverter::do2VO);
    }

    /**
     * 构建示例图的VO
     *
     * @param modelId       模型id
     * @param userId        用户id
     * @param exampleImages 示例图列表
     * @return 示例图VO
     */
    public static CreativeBatchVO buildExampleImagesVO(Integer modelId, Integer userId, List<String> exampleImages) {
        CreativeBatchVO data = new CreativeBatchVO();

        data.setModelId(modelId);
        data.setResultImages(exampleImages);
        data.setStatus(CreativeStatusEnum.FINISHED);
        data.setType(CreativeTypeEnum.CREATE_IMAGE);
        data.setBatchCnt(exampleImages.size());
        data.setImageProportion(ProportionTypeEnum.THREE_FOUR.getCode());
        data.setShowImage(exampleImages.get(0));
        data.setUserId(userId);
        data.setOperatorId(userId);
        data.addExtInfo(BIZ_TAG, EXAMPLE_IMAGES);

        return data;
    }

    public static void mergeCollocation(ClothCollocationModelExt target, ClothCollocationModel source,
                                        boolean isUserSet) {
        if (source == null) {
            return;
        }
        if (StringUtils.isNotBlank(source.getTops())) {
            target.setTops(source.getTops());
            if (isUserSet) {
                target.setTopsUserSet(true);
            }
        }
        if (StringUtils.isNotBlank(source.getBottoms())) {
            target.setBottoms(source.getBottoms());
            if (isUserSet) {
                target.setBottomsUserSet(true);
            }
        }
        if (StringUtils.isNotBlank(source.getOthers())) {
            target.setOthers(source.getOthers());
            if (isUserSet) {
                target.setOthersUserSet(true);
            }
        }
        if (StringUtils.isNotBlank(source.getShoe())) {
            target.setShoe(source.getShoe());
            if (isUserSet) {
                target.setShoeUserSet(true);
            }
        }
        if (StringUtils.isNotBlank(source.getProps())) {
            target.setProps(source.getProps());
            if (isUserSet) {
                target.setPropsUserSet(true);
            }
        }
    }

    /**
     * 构建配饰补充特征
     *
     * @param target           配饰信息
     * @param shoeSuffixDict   鞋子后缀字典
     * @param promptSeed       提示种子
     * @param clothIncludesBra 服装是否包含内衣
     * @param clothType        服装类型
     * @return 补充特征
     */
    public static String buildFeatures(ClothCollocationModelExt target, JSONObject shoeSuffixDict, long promptSeed,
                                       boolean clothIncludesBra, String clothType) {
        String collocation = "";

        // 1.上/下装用and连接，单独用一句话描述
        String tops = formatCollocation(target.getTops(), target.isTopsUserSet());
        if (StringUtils.isNotBlank(tops) && !StringUtils.equals(clothType, CommonConstants.lowerGarment)) {
            tops = clothIncludesBra ? " open " + tops + " outside" : tops + " inside";
        }
        collocation = appendCollocationWithAnd(collocation, tops);

        String bottoms = formatCollocation(target.getBottoms(), target.isBottomsUserSet());
        bottoms = StringUtils.isNotBlank(bottoms) ? bottoms + " underneath" : bottoms;
        collocation = appendCollocationWithAnd(collocation, bottoms);

        if (StringUtils.isNotBlank(collocation)) {
            collocation = "The model is wearing " + collocation + ". ";
        }

        // 2.其他配饰单独用一句话描述
        collocation = appendCollocationWithNew(collocation,
            formatCollocation(target.getOthers(), target.isOthersUserSet()));

        // 3.鞋子单独用一句话描述，且为了解决出全身问题，需要详细描述鞋子的样子
        String shoe = formatCollocation(target.getShoe(), target.isShoeUserSet());
        // 3.1. 需要在应用层先处理好随机，保证鞋子和详细描述一致
        String shoeRandom = PromptUtils.random(shoe, promptSeed);

        collocation = appendCollocationWithNew(collocation, shoeRandom);

        // 3.2. 添加鞋子详细描述
        String suffix = buildShoeSuffix(shoeRandom, shoeSuffixDict);

        collocation = collocation + ". " + suffix;

        // 4. 道具单独一句话描述
        String holding = "";
        if (StringUtils.isNotBlank(target.getProps())) {
            holding = "The model is " + (ACTION_PATTERN.matcher(target.getProps()).find() ? "" : " holding ")
                      + appendCollocationWithAnd("", formatCollocation(target.getProps(), target.isPropsUserSet()))
                      + ". ";
        }

        String result = collocation + holding;

        log.info("服装配饰生成结果：{}", result);

        return result;
    }

    public static UserCountVO userCount2VO(UserCountDO from) {
        UserCountVO to = new UserCountVO();
        to.setUserId(from.getUserId());
        to.setCnt(from.getCnt());
        return to;
    }

    public static List<UserCountVO> userCount2VO(List<UserCountDO> list) {
        return CommonUtil.listConverter(list, CreativeBatchConverter::userCount2VO);
    }

    /**
     * 构建鞋的后缀描绘
     *
     * @param shoe           鞋子描述
     * @param shoeSuffixDict 鞋子后缀字典
     * @return 后缀描述
     */
    private static String buildShoeSuffix(String shoe, JSONObject shoeSuffixDict) {
        String suffix = "";
        if (StringUtils.isBlank(shoe)) {
            return suffix;
        }

        for (String k : shoeSuffixDict.keySet()) {
            if (StringUtils.containsIgnoreCase(shoe, k)) {
                return String.format(shoeSuffixDict.getString(k), shoe);
            }
        }

        log.warn("服装配饰，无法识别的鞋子：{}，走了兜底逻辑", shoe);

        suffix = (StringUtils.startsWithIgnoreCase(shoe, "the") ? shoe : "The " + StringUtils.trim(shoe))
                 + " have a fashionable style. ";

        return suffix;
    }

    /**
     * 追加配饰,通过and连接
     *
     * @param collocation 原有配饰
     * @param target      追加的配饰
     * @return 完整的配饰描述
     */
    private static String appendCollocationWithAnd(String collocation, String target) {
        if (StringUtils.isBlank(target)) {
            return collocation;
        }

        String prefix = StringUtils.isNotBlank(collocation) ? " and " : "";
        return collocation + prefix + target;
    }

    /**
     * 追加配饰,通过新起一句：the model is wearing连接
     *
     * @param collocation 原有配饰
     * @param target      追加的配饰
     * @return 完整的配饰描述
     */
    private static String appendCollocationWithNew(String collocation, String target) {
        if (StringUtils.isBlank(target)) {
            return collocation;
        }

        String prefix = "The model is ";
        if (!ACTION_PATTERN.matcher(target).find()) {
            prefix += "wearing ";
        }
        if (StringUtils.isNotBlank(collocation) && !StringUtils.endsWith(StringUtils.trim(collocation), ".")) {
            prefix = ". " + prefix;
        }

        return collocation + prefix + StringUtils.trim(target);
    }

    /**
     * 对配饰内容进行格式化
     *
     * @param target    原始配饰内容
     * @param isUserSet 是否用户设置
     * @return 格式化后内容
     */
    private static String formatCollocation(String target, boolean isUserSet) {
        target = StringUtils.trim(target);
        if (StringUtils.isBlank(target)) {
            return target;
        }

        //替换中文逗号为英文逗号
        target = target.replaceAll("，", ",");

        if (StringUtils.endsWith(target, ",")) {
            target = target.substring(0, target.length() - 1);
        }
        if (StringUtils.startsWith(target, ",")) {
            target = target.substring(1);
        }

        if (StringUtils.isBlank(target)) {
            return target;
        }

        target = StringUtils.startsWith(target, "wearing ") ? target.substring(7) : target;
        target = StringUtils.endsWith(target, ".") ? target.substring(0, target.length() - 1) : target;

        return "{" + (isUserSet ? target : target.replaceAll(",", "|")) + "}";
    }
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.TEN_MINUTES;

/**
 * 弱锁帮助累
 *
 * <AUTHOR>
 * @version : WeakLockHelper.java, v 0.1 2024/5/22 22:17 renxiao.wu Exp $
 */
@Component
public class WeakLockHelper {
    @Autowired
    private TairService tairService;

    /**
     * 获取状态锁
     * <p>
     * 1.由于已经被锁，获取不到锁时，返回false
     * 2.获取到锁时，返回true，同时刷新锁最新更新时间
     * </p>
     *
     * @param type 类型
     * @param id   id
     * @return 是否上锁
     */
    public boolean lock(WeakType type, Integer id) {
        boolean isLocked = isStatusSyncInTime(type, id);
        if (isLocked) {
            return false;
        }
        refreshStatusTime(type, id);

        return true;
    }

    /**
     * 刷新最新同步状态时间
     *
     * @param type 类型
     * @param id   id
     */
    public void refreshStatusTime(WeakType type, Integer id) {
        tairService.setObject(getStatusTairKey(type, id), System.currentTimeMillis(), type.getStoreTime());
    }

    /**
     * 3秒内状态是否已同步
     *
     * @param type 类型
     * @param id   id
     * @return true：已同步
     */
    public boolean isStatusSyncInTime(WeakType type, Integer id) {
        Long latestUpdateTime = tairService.getObject(getStatusTairKey(type, id), Long.class);
        if (latestUpdateTime == null) {
            return false;
        }
        return System.currentTimeMillis() - latestUpdateTime < type.getExpireTime();
    }

    private String getStatusTairKey(WeakType type, Integer id) {
        return type.getCode() + "_latest_update_time_" + id;
    }

    @Getter
    public enum WeakType {
        BATCH("_batch", TEN_MINUTES, 3 * 1000),
        TASK("_task", TEN_MINUTES, 3 * 1000),
        SYNC_IMAGE("_sync_image", TEN_MINUTES, 2 * 1000),
        BATCH_JOB("_batch_job", 5, 5 * 1000),
        WS_SCHEDULE("_ws_schedule", 3, 3 * 1000),
        ;

        private String code;
        private Integer storeTime;
        private Integer expireTime;

        private WeakType(String code, Integer storeTime, Integer expireTime) {
            this.code = code;
            this.storeTime = storeTime;
            this.expireTime = expireTime;
        }

        /**
         * 根据枚举码获取枚举
         *
         * @param code 枚举码
         * @return 对应枚举
         */
        public static WeakType getByCode(String code) {
            if (StringUtils.isBlank(code)) {
                return null;
            }

            for (WeakType item : values()) {
                if (StringUtils.equals(item.getCode(), code)) {
                    return item;
                }
            }

            return null;
        }
    }

}





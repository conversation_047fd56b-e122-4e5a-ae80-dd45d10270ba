/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

import ai.conrain.aigc.platform.integration.aliyun.model.StsCredentials;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version : OssTokenInfo.java, v 0.1 2024/7/17 10:59 renxiao.wu Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OssTokenInfo extends StsCredentials {
    String bucketName;
    String region;

    public OssTokenInfo() {}

    public OssTokenInfo(StsCredentials credentials, String bucketName, String region) {
        this.accessKeyId = credentials.getAccessKeyId();
        this.accessKeySecret = credentials.getAccessKeySecret();
        this.securityToken = credentials.getSecurityToken();
        this.expiration = credentials.getExpiration();
        this.bucketName = bucketName;
        this.region = region;
    }
}

package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ShootingStyleDO;
import ai.conrain.aigc.platform.service.model.query.ShootingStyleQuery;
import ai.conrain.aigc.platform.service.model.vo.ShootingStyleVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ShootingStyleConverter
 *
 * @version ShootingStyleService.java v 0.1 2025-07-04 05:16:37
 */
public class ShootingStyleConverter {

    /**
     * DO -> VO
     */
    public static ShootingStyleVO do2VO(ShootingStyleDO from) {
        ShootingStyleVO to = new ShootingStyleVO();
        to.setId(from.getId());
        to.setType1Name(from.getType1Name());
        to.setType2Name(from.getType2Name());
        to.setType2EnName(from.getType2EnName());
        to.setRepresentativeBrand(from.getRepresentativeBrand());
        to.setClassicElements(from.getClassicElements());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExamImageUrls(from.getExamImageUrls());
        to.setStyleDesc(from.getStyleDesc());
        to.setModelTags(from.getModelTags());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ShootingStyleDO vo2DO(ShootingStyleVO from) {
        ShootingStyleDO to = new ShootingStyleDO();
        to.setId(from.getId());
        to.setType1Name(from.getType1Name());
        to.setType2Name(from.getType2Name());
        to.setType2EnName(from.getType2EnName());
        to.setRepresentativeBrand(from.getRepresentativeBrand());
        to.setClassicElements(from.getClassicElements());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExamImageUrls(from.getExamImageUrls());
        to.setStyleDesc(from.getStyleDesc());
        to.setModelTags(from.getModelTags());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ShootingStyleVO> doList2VOList(List<ShootingStyleDO> list) {
        return CommonUtil.listConverter(list, ShootingStyleConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.OrderInfoService;
import ai.conrain.aigc.platform.service.component.OrderSettlementService;
import ai.conrain.aigc.platform.service.component.PayService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.OrderStatusEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.helper.OrderNoGenHelper;
import ai.conrain.aigc.platform.service.helper.PricePlanHelper;
import ai.conrain.aigc.platform.service.model.biz.CachedOrder;
import ai.conrain.aigc.platform.service.model.biz.PayStatusEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.request.CreateQrCodeRequest;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.PayQRCode;
import ai.conrain.aigc.platform.service.model.vo.PayQueryRet;
import ai.conrain.aigc.platform.service.model.vo.PricePlan;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 支付服务抽象类
 */
@Slf4j
public abstract class AbstractPayService implements PayService {

    //支付cache过期时间（二维码在微信侧的有效期为2小时，这里缓存12小时）
    public final static int payExpireSecs = 12 * 60 * 60;

    @Autowired
    private UserService userService;

    @Autowired
    private TairService tairService;

    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private PricePlanHelper pricePlanHelper;

    @Autowired
    private UserPointService userPointService;

    @Autowired
    private OrderNoGenHelper orderNoGenHelper;

    @Autowired
    private OrderSettlementService orderSettlementService;

    @Autowired
    private DistributorService distributorService;


    @Override
    public PayQRCode createPayQRCode(CreateQrCodeRequest request) {
        Integer payMasterUserId = request.getPayMasterUserId();
        String planCode = request.getPlanCode();
        String payAmount = request.getPayAmount();

        boolean backRoleAdminAgentOrder = payMasterUserId != null && !OperationContextHolder.getMasterUserId().equals(payMasterUserId);
        if (backRoleAdminAgentOrder) {
            AssertUtil.assertTrue(OperationContextHolder.isAdmin() || OperationContextHolder.isDistributorRole(), "只有管理员和渠道商可以操作");
        }

        Integer masterUserId = payMasterUserId != null ? payMasterUserId : OperationContextHolder.getMasterUserId();

        // 总金额（单位为分，只能为整数，即元*100）
        Integer amountInCents = getAmountInCent(planCode, masterUserId, payAmount);

        // 订单号唯一且不超过32位长度
        String orderNo = orderNoGenHelper.generateOrderNo();
        String codeUrl = this._createQrCodeUrl(orderNo, payAmount, planCode, masterUserId);

        AssertUtil.assertTrue(StringUtils.isNotBlank(codeUrl), "创建支付订单失败");

        PayQRCode ret = new PayQRCode();
        ret.setCodeUrl(codeUrl);
        ret.setOrderNo(orderNo);

        CachedOrder cachedOrder = new CachedOrder();
        cachedOrder.setCreateTime(new Date());
        cachedOrder.setPlanCode(planCode);
        cachedOrder.setOperatorUserId(masterUserId);
        cachedOrder.setMasterUserId(masterUserId);
        cachedOrder.setCodeUrl(codeUrl);
        cachedOrder.setOrderNo(orderNo);
        cachedOrder.setAmountInCents(amountInCents);
        cachedOrder.setPayType(this._getPayType());
        cachedOrder.setMusePoint(request.getMusePoint());
        cachedOrder.setCreativeImgCountGave(request.getCreativeImgCountGave());

        //后台管理员，指定金额收款
        if (backRoleAdminAgentOrder) {
            cachedOrder.setOrderAgentUserId(OperationContextHolder.getMasterUserId());
        }

        cachedOrder.setOrderInfo(initOrderModel(cachedOrder));

        log.info("创建订单缓存模型orderNo:{}, cachedOrder:{}", orderNo, JSON.toJSONString(cachedOrder));

        boolean saved = tairService.setObject(getCachedOrderKey(orderNo), cachedOrder, payExpireSecs);
        AssertUtil.assertTrue(saved, "缓存redis失败");

        return ret;
    }

    @Override
    public PayQueryRet queryPayResult(String orderNo) {
        PayQueryRet ret = new PayQueryRet();

        PayStatusEnum payStatus = this._queryPayResultStatus(orderNo);
        AssertUtil.assertNotNull(payStatus, "查询订单失败，订单状态不合法");

        ret.setTradeState(payStatus.getCode());
        ret.setDesc(payStatus.getDesc());

        log.info("支付宝查询订单号{}状态【{}】", orderNo, payStatus.getDesc());

        //支付处理
        if (StringUtils.equalsIgnoreCase(ret.getTradeState(), PayStatusEnum.SUCCESS.getCode())) {
            boolean success = onPaySuccess(orderNo);
            if (!success) {
                ret.setTradeState(PayStatusEnum.USERPAYING.getCode());
                ret.setDesc("支付中");
                log.error("支付成功，但处理失败，继续轮询重试");
            }
        }

        return ret;
    }

    @Override
    public boolean onPaySuccess(String orderNo) {
        CachedOrder cachedOrder = tairService.getObject(getCachedOrderKey(orderNo), CachedOrder.class);
        if (cachedOrder == null || cachedOrder.getOrderInfo() == null) {
            log.error("缓存已经过期，需要刷新页面，无效的outTradeNo:{}", orderNo);
            throw new BizException(ResultCode.PAY_CACHE_EXPIRED, "页面太久没刷新，请重新页面");
        }

        log.info("onPaySuccess开始,支付成功orderNo:{},取出cachedOrder:{}", orderNo,JSON.toJSONString(cachedOrder));

        String lockKey = "PS_" + orderNo;
        String requestId = "1";

        boolean lockSuccess = tairService.acquireLock(lockKey, requestId, 60000);
        if (lockSuccess) {
            try {
                //落订单
                OrderInfoQuery query = new OrderInfoQuery();
                query.setOrderNo(orderNo);
                query.setMasterUserId(cachedOrder.getMasterUserId());

                OrderInfoVO order = null;
                List<OrderInfoVO> existed = orderInfoService.queryOrderInfoListWithSalesInfo(query);
                AssertUtil.assertTrue(CollectionUtils.size(existed) <= 1, "订单信息超过一条，充值中断" + existed);

                //落订单、更新积分
                //幂等处理，为空时才处理
                if (CollectionUtils.isEmpty(existed)) {
                    order = cachedOrder.getOrderInfo();
                    order.setFinishTime(new Date());
                    order = orderInfoService.insert(order);
                    userPointService.rechargeByPay(order);

                    // 异步自动指派工程师
                    distributorService.tryAutoAssignEngineerToCustomerAsync(cachedOrder.getMasterUserId());

                    String salesInfo = distributorService.getSalesInfoByMerchantId(cachedOrder.getMasterUserId());
                    DingTalkNoticeHelper.sendMsg2BizGroup(String.format("客户充值\n客户：%s\n金额：%s元\n销售：%s",
                            order.getMasterCorpName(), order.getPayAmount(), salesInfo));

                    log.info("onPaySuccess支付成功处理完成，orderNo:{}", orderNo);

                } else {
                    log.info("onPaySuccess订单已经存在，直接返回，orderNo:{}", orderNo);
                }

                return true;

            } catch (Throwable t) {
                log.error("onPaySuccess处理异常", t);
                return false;

            } finally {
                tairService.releaseLock(lockKey, requestId);
            }

        } else {
            log.warn("支付成功，获取锁失败");
            return false;
        }
    }

    protected abstract String _createQrCodeUrl(String orderNo, String payAmount, String planCode, Integer masterUserId);
    protected abstract String _getPayType();
    protected abstract PayStatusEnum _queryPayResultStatus(String orderNo);

    //指定金额时，以指定金额为准
    protected Integer getAmountInCent(String planCode, Integer masterUserId, String payAmount) {
        if (StringUtils.isNotBlank(payAmount)) {
            return new BigDecimal(payAmount).movePointRight(2).intValueExact();
        } else {
            PricePlan p = pricePlanHelper.queryPricePlanByCode(planCode, masterUserId);

            //字符串元 => 整数分
            return new BigDecimal(p.getAmount()).movePointRight(2).intValueExact();
        }
    }

    protected OrderInfoVO initOrderModel(CachedOrder cachedOrder) {
        OrderInfoVO order = new OrderInfoVO();
        {
            order.setOrderNo(cachedOrder.getOrderNo());
            order.setOperatorUserId(cachedOrder.getOperatorUserId());

            BigDecimal amount = BigDecimal.valueOf(cachedOrder.getAmountInCents(), 2);

            //后台指定金额收款
            if (cachedOrder.getOrderAgentUserId() != null) {

                Integer payMasterId = cachedOrder.getMasterUserId();
                AssertUtil.assertTrue(payMasterId != null, "支付masterId不能为空");
                UserVO payMaster = userService.selectById(payMasterId);
                AssertUtil.assertNotNull(payMaster, "查询master失败");

                order.setOperatorUserNick(payMaster.getNickName());
                order.setOperatorUserLoginId(payMaster.getLoginId());

                order.setMasterUserId(payMasterId);
                order.setMasterUserNick(payMaster.getNickName());
                order.setMasterCorpName(payMaster.getCorpName());
                order.setMasterUserLoginId(payMaster.getLoginId());

                JSONObject productDetail = getProductDetail(cachedOrder, payMaster, amount);
                order.setProductDetail(productDetail.toJSONString());

                //本人充值
            } else {
                UserVO master = userService.selectById(OperationContextHolder.getMasterUserId());
                AssertUtil.assertNotNull(master, "查询master失败");

                order.setOperatorUserNick(OperationContextHolder.getContext().getUserNick());
                order.setOperatorUserLoginId(
                        OperationContextHolder.getContext().getOperationSession().getLoginUser().getLoginId());

                order.setMasterUserId(OperationContextHolder.getMasterUserId());
                order.setMasterCorpName(master.getCorpName());

                //当前是master
                if (OperationContextHolder.getContext().isMasterUser()) {
                    order.setMasterUserNick(OperationContextHolder.getMasterNick());
                    order.setMasterUserLoginId(
                            OperationContextHolder.getContext().getOperationSession().getLoginUser().getLoginId());
                } else {

                    order.setMasterUserNick(master.getNickName());
                    order.setMasterUserLoginId(master.getLoginId());
                }

                order.setProductDetail(
                        JSONObject.toJSONString(pricePlanHelper.queryPricePlanByCode(cachedOrder.getPlanCode(), cachedOrder.getMasterUserId())));
            }

            AssertUtil.assertNotNull(cachedOrder.getAmountInCents(), "无效的订单缓存，金额为空");
            //将分转化为元
            order.setPayAmount(BigDecimal.valueOf(cachedOrder.getAmountInCents(), 2));
            order.setOriginalAmount(BigDecimal.valueOf(cachedOrder.getAmountInCents(), 2));

            order.setPayDetail(JSONObject.toJSONString(cachedOrder));

            //交易完成
            order.setOrderStatus(OrderStatusEnum.TRADE_FINISHED.getCode());
            order.setProductCode(cachedOrder.getPlanCode());
        }

        return order;
    }

    @NotNull
    private static JSONObject getProductDetail(CachedOrder cachedOrder, UserVO payMaster, BigDecimal amount) {
        JSONObject productDetail = new JSONObject();
        productDetail.put("planCode", cachedOrder.getPlanCode());
        //name字段用于展示在订单列表页面中作为‘产品套餐’
        productDetail.put("name", "转账充值");
        productDetail.put("memo", OperationContextHolder.getOperatorNick() + "向" + payMaster.getNickName() + "收款" + amount + "元");
        productDetail.put("payAmount", amount);
        productDetail.put("payMasterUserId", cachedOrder.getMasterUserId());

        // Muse点数
        if (cachedOrder.getMusePoint() != null && cachedOrder.getMusePoint() > 0) {
            productDetail.put(CommonConstants.MUSE_POINT, cachedOrder.getMusePoint());
        }

        // 赠送的创作图片次数
        if (cachedOrder.getCreativeImgCountGave() != null && cachedOrder.getCreativeImgCountGave() > 0) {
            productDetail.put(CommonConstants.GAVE_IMG_COUNT, cachedOrder.getCreativeImgCountGave());
        }
        return productDetail;
    }

    protected String getCachedOrderKey(String ordNo) {
        return "Pay_" + ordNo;
    }

}

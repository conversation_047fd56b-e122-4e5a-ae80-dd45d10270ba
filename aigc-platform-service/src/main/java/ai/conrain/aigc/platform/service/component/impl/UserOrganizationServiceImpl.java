package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.UserOrganizationDAO;
import ai.conrain.aigc.platform.dal.entity.UserOrganizationDO;
import ai.conrain.aigc.platform.service.component.UserOrganizationService;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.UserOrganizationConverter;
import ai.conrain.aigc.platform.service.model.query.UserOrganizationQuery;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**   
 * UserOrganizationService实现
 *
 * <AUTHOR>
 * @version UserOrganizationService.java v 0.1 2024-07-12 03:53:58
 */
@Slf4j
@Service
public class UserOrganizationServiceImpl implements UserOrganizationService {

	/** DAO */
	@Autowired
	private UserOrganizationDAO userOrganizationDAO;

	@Override
	public UserOrganizationVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		UserOrganizationDO data = userOrganizationDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return UserOrganizationConverter.do2VO(data);
	}

	@Override
	public List<UserOrganizationVO> selectByUserIds(List<Integer> userIds) {
        return CommonUtil.listConverter(userOrganizationDAO.selectByUserIds(userIds), UserOrganizationConverter::do2VO);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = userOrganizationDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserOrganization失败");
	}

	@Override
	public void deleteByOrgId(Integer orgId) {
		AssertUtil.assertNotNull(orgId, ResultCode.PARAM_INVALID, "id is null");
		userOrganizationDAO.deleteByOrgId(orgId);
	}

	@Override
	public UserOrganizationVO insert(UserOrganizationVO userOrganization) {
		AssertUtil.assertNotNull(userOrganization, ResultCode.PARAM_INVALID, "userOrganization is null");
		AssertUtil.assertTrue(userOrganization.getId() == null, ResultCode.PARAM_INVALID, "userOrganization.id is present");

		//创建时间、修改时间兜底
		if (userOrganization.getCreateTime() == null) {
			userOrganization.setCreateTime(new Date());
		}

		if (userOrganization.getModifyTime() == null) {
			userOrganization.setModifyTime(new Date());
		}

		UserOrganizationDO data = UserOrganizationConverter.vo2DO(userOrganization);
		Integer n = userOrganizationDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建UserOrganization失败");
		AssertUtil.assertNotNull(data.getId(), "新建UserOrganization返回id为空");
		userOrganization.setId(data.getId());

		return userOrganization;
	}

	@Override
	public void updateById(UserOrganizationVO userOrganization) {
		AssertUtil.assertNotNull(userOrganization, ResultCode.PARAM_INVALID, "userOrganization is null");
		AssertUtil.assertTrue(userOrganization.getId() != null, ResultCode.PARAM_INVALID, "userOrganization.id is null");
		//修改时间必须更新
		userOrganization.setModifyTime(new Date());

		UserOrganizationDO data = UserOrganizationConverter.vo2DO(userOrganization);
		int n = userOrganizationDAO.updateByPrimaryKey(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserOrganization失败，影响行数:" + n);
	}

	@Override
	public List<UserOrganizationVO> findAll() {
		List<UserOrganizationDO> list = userOrganizationDAO.selectAll();
		return UserOrganizationConverter.doList2VOList(list);
	}

	@Override
	public void deleteByUserId(Integer userId) {
		userOrganizationDAO.deleteByUserId(userId);
	}

	@Override
	public List<UserOrganizationVO> findAllByOrgIds(List<Integer> orgIds) {
		List<UserOrganizationDO> list = userOrganizationDAO.selectByOrgIds(orgIds);
		return UserOrganizationConverter.doList2VOList(list);
	}

	@Override
	public List<UserOrganizationVO> queryUserPointList(List<Integer> userIdList) {
		List<UserOrganizationDO> userOrganizationDOS = userOrganizationDAO.selectByUserIds(userIdList);
		return UserOrganizationConverter.doList2VOList(userOrganizationDOS);
	}

    @Override
    public List<UserOrganizationVO> selectByOrgId(Integer orgId) {
		AssertUtil.assertNotNull(orgId, ResultCode.PARAM_INVALID, "orgId is null");
		List<Integer> orgIds = Collections.singletonList(orgId);
        List<UserOrganizationDO> doList = userOrganizationDAO.selectByOrgIds(orgIds);
		return UserOrganizationConverter.doList2VOList(doList);
    }

	@Override
	public UserOrganizationVO findByUserId(Integer userId) {
		AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
		List<Integer> userIds = Collections.singletonList(userId);
		List<UserOrganizationDO> doList = userOrganizationDAO.selectByUserIds(userIds);
		if (CollectionUtils.isEmpty(doList)) {
			return null;
		}
		return UserOrganizationConverter.do2VO(doList.get(0));
	}

}
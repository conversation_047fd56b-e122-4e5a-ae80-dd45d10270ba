package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsUserPointDO;
import ai.conrain.aigc.platform.service.model.query.StatsUserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsUserPointVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.Date;
import java.util.List;

/**
 * 用户点数消耗统计 Service定义
 *
 * <AUTHOR>
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
public interface StatsUserPointService {
	
	/**
	 * 查询用户点数消耗统计对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	StatsUserPointVO selectById(Integer id);

	/**
	 * 删除用户点数消耗统计对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加用户点数消耗统计对象
	 * @param statsUserPoint 对象参数
	 * @return 返回结果
	 */
	StatsUserPointVO insert(StatsUserPointVO statsUserPoint);

	/**
	 * 修改用户点数消耗统计对象
	 * @param statsUserPoint 对象参数
	 */
	void updateByIdSelective(StatsUserPointVO statsUserPoint);

	/**
	 * 带条件批量查询用户点数消耗统计列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<StatsUserPointVO> queryStatsUserPointList(StatsUserPointQuery query);

	/**
	 * 带条件查询用户点数消耗统计数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryStatsUserPointCount(StatsUserPointQuery query);

	/**
	 * 带条件分页查询用户点数消耗统计
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<StatsUserPointVO> queryStatsUserPointByPage(StatsUserPointQuery query);

	/**
	 * 获取用户点数消耗统计数据
	 *
	 * @param userId    用户ID
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 包含消费点数和充值金额的Map
	 */
	StatsUserPointDO getUserPointConsumption(Integer userId, Date startDate, Date endDate);
	
	/**
	 * 获取所有用户点数消耗汇总统计数据
	 *
	 * @param startDate 开始日期
	 * @param endDate   结束日期
	 * @return 包含消费点数和充值金额的Map
	 */
	StatsUserPointDO getAllUserPointConsumption(Date startDate, Date endDate);
	
	/**
	 * 查询指定日期范围内有消费记录的用户
	 * @param startDate 开始日期
	 * @param endDate 结束日期
	 * @return 用户ID列表
	 */
	List<Integer> findUsersWithPointConsumption(Date startDate, Date endDate);
	
	/**
	 * 批量插入或更新统计数据
	 * @param statsList 统计数据列表
	 * @return 处理的记录数
	 */
	int batchInsertOrUpdateStats(List<StatsUserPointDO> statsList);

	/**
	 * 批量查询多个用户的点数消费数据
	 */
	List<StatsUserPointDO> getUsersPointConsumptionBatch(List<Integer> userIds, Date startDate, Date endDate);
}
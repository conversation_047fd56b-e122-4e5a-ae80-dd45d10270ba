package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.query.OrderSettlementQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorSettlementVO;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.model.vo.OrderSettlementVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.Date;
import java.util.List;

/**
 * 订单结算表（流水） Service定义
 *
 * <AUTHOR>
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
public interface OrderSettlementService {
	
	/**
	 * 查询订单结算表（流水）对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	OrderSettlementVO selectById(Integer id);

	/**
	 * 删除订单结算表（流水）对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加订单结算表（流水）对象
	 * @param orderSettlement 对象参数
	 * @return 返回结果
	 */
	OrderSettlementVO insert(OrderSettlementVO orderSettlement);

	/**
	 * 修改订单结算表（流水）对象
	 * @param orderSettlement 对象参数
	 */
	void updateByIdSelective(OrderSettlementVO orderSettlement);

	/**
	 * 带条件批量查询订单结算表（流水）列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<OrderSettlementVO> queryOrderSettlementList(OrderSettlementQuery query);

	/**
	 * 带条件查询订单结算表（流水）数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryOrderSettlementCount(OrderSettlementQuery query);

	/**
	 * 带条件分页查询订单结算表（流水）
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<OrderSettlementVO> queryOrderSettlementByPage(OrderSettlementQuery query);

	/**
	 * 聚合订单分佣结算
	 *
	 * @param startDate
	 * @param endDate
	 * @param data
	 * @param settleConfigVO
	 */
	void aggregateCommissionSettlement4Distributor(Date startDate, Date endDate, DistributorSettlementVO data, DistributorSettleConfigVO settleConfigVO);

	/**
	 * 聚合订单抽成结算
	 *
	 * @param principal
	 * @param startDate
	 * @param endDate
	 * @param data
	 */
	void aggregateCommissionRelateSettlement(PrincipalModel principal, Date startDate, Date endDate, DistributorSettlementVO data);

	/**
     * 初始化订单结算表
     *
     * @param orderNo             订单号
     * @param distributorCorpId   商户id
     * @param distributorCorpName
     * @param newOrder
     */
	void initSettlement(String orderNo, int distributorCorpId, String distributorCorpName, boolean newOrder) throws BizException;

	/**
	 * 关闭订单结算表
	 *
	 * @param orderNo 订单号
	 */
//	void closeSettlement(String orderNo) throws BizException;

	/**
	 * 开始执行订单结算表
	 *
	 * @param orderDO 订单数据
	 */
	void startSettlement(OrderInfoVO orderDO) throws BizException;

	/**
	 * 完结订单结算
	 *
	 * @param settleId 结算id
	 */
	void finishSettlement(String settleId);

	/**
	 * 初始化月结账单
	 * @param month
	 */
	void batchInitSettlement4Distributor(String month);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import java.io.Serializable;

import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import lombok.Data;

/**
 * 服务状态缓存数据
 *
 * <AUTHOR>
 * @version : ServerCache.java, v 0.1 2024/8/6 14:42 renxiao.wu Exp $
 */
@Data
public class ServerCache implements Serializable {
    private static final long serialVersionUID = -5045829668585738892L;
    /** 状态 */
    private ServerStatusEnum status;
    /** 任务id */
    private Integer taskId;
    /** 业务类型 */
    private DispatchTypeEnum type;
    /** 缓存时间 */
    private long cacheTime;
    /** 失败次数 */
    private int failTimes = 0;

    public ServerCache() {
        this.cacheTime = System.currentTimeMillis();
    }

    public ServerCache(ServerStatusEnum status, Integer taskId, DispatchTypeEnum type) {
        this();
        this.status = status;
        this.taskId = taskId;
        this.type = type;
    }

    public ServerCache(ServerStatusEnum status) {
        this.status = status;
        this.cacheTime = System.currentTimeMillis();
    }

    /**
     * 是否需要重新检查
     * 超过3分钟时，疑似死锁，需要重新检查
     *
     * @return true，可以释放
     */
    public boolean needRecheck() {
        return ServerStatusEnum.BUSY.equals(status) && System.currentTimeMillis() - cacheTime > 1000 * 60;
    }

    /**
     * 刷新缓存时间
     */
    public void refreshTime() {
        cacheTime = System.currentTimeMillis();
    }

    /**
     * 是否可用
     *
     * @return true，可用
     */
    public boolean isAvailable() {
        return status == null || (ServerStatusEnum.DISABLE != status && ServerStatusEnum.UNUSABLE != status);
    }

    public void fail() {
        failTimes++;
        //当失败次数大于等于50次时，将该服务置为不可用
        //用户等待情况下3秒/次 * 50次 = 150秒= 2.5分钟，目前comfyui监控+重启=60+30=1.5分钟
        if (failTimes >= 50) {
            status = ServerStatusEnum.UNUSABLE;
        }
    }
}

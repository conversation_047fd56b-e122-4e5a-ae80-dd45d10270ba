package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version WorkScheduleService.java v 0.1 2025-04-01 03:40:32
 */
public interface WorkScheduleService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	WorkScheduleVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param workSchedule 对象参数
	 * @return 返回结果
	 */
	WorkScheduleVO insert(WorkScheduleVO workSchedule);

	/**
	 * 修改对象
	 * @param workSchedule 对象参数
	 */
	void updateByIdSelective(WorkScheduleVO workSchedule);

	/**
	 * 带条件批量查询列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<WorkScheduleVO> queryWorkScheduleList(WorkScheduleQuery query);

	/**
	 * 带条件查询数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryWorkScheduleCount(WorkScheduleQuery query);

	/**
	 * 带条件分页查询
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<WorkScheduleVO> queryWorkScheduleByPage(WorkScheduleQuery query);

	/**
	 * 批量添加对象
	 * @param workScheduleList 对象参数列表
	 * @return 返回结果
	 */
	List<WorkScheduleVO> batchInsert(List<WorkScheduleVO> workScheduleList);
	
	/**
     * 批量删除对象
     *
     * @param ids 主键列表
     */
	void batchDeleteByIds(List<Integer> ids);
}
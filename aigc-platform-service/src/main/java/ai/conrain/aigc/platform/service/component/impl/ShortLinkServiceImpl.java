package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.ShortLinkDAO;
import ai.conrain.aigc.platform.dal.entity.ShortLinkDO;
import ai.conrain.aigc.platform.dal.example.ShortLinkExample;
import ai.conrain.aigc.platform.service.component.ShortLinkService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ShortLinkConverter;
import ai.conrain.aigc.platform.service.model.query.ShortLinkQuery;
import ai.conrain.aigc.platform.service.model.vo.ShortLinkVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * ShortLinkService实现
 *
 * <AUTHOR>
 * @version ShortLinkService.java
 */
@Slf4j
@Service
public class ShortLinkServiceImpl implements ShortLinkService {

	private static final String SHORT_LINK_API = "link";
	private static final String CACHE_PREFIX = "short-link:";
	private static final int CACHE_EXPIRE = 60 * 60 * 2;

	@Value("${app.custom.domain}")
	private String domain;

	/** DAO */
	@Autowired
	private ShortLinkDAO shortLinkDAO;

	@Override
	public ShortLinkVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ShortLinkDO data = shortLinkDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return ShortLinkConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = shortLinkDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ShortLink失败");
	}

	@Override
	public ShortLinkVO insert(ShortLinkVO shortLink) {
		AssertUtil.assertNotNull(shortLink, ResultCode.PARAM_INVALID, "shortLink is null");
		AssertUtil.assertTrue(shortLink.getId() == null, ResultCode.PARAM_INVALID, "shortLink.id is present");

		ShortLinkDO data = ShortLinkConverter.vo2DO(shortLink);
		int n = shortLinkDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ShortLink失败");
		AssertUtil.assertNotNull(data.getId(), "新建ShortLink返回id为空");
		shortLink.setId(data.getId());
		return shortLink;
	}

	@Override
	public List<ShortLinkVO> batchInsert(List<ShortLinkVO> shortLinks) {
		AssertUtil.assertNotEmpty(shortLinks, ResultCode.PARAM_INVALID, "shortLinks is empty");
		shortLinks.forEach(shortLink ->
				AssertUtil.assertTrue(shortLink.getId() == null, ResultCode.PARAM_INVALID, "shortLink.id is present"));

		List<ShortLinkDO> data = ShortLinkConverter.voList2DOList(shortLinks);
		int n = shortLinkDAO.batchInsert(data);

		AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "批量创建ShortLink失败");
		data.forEach(shortLinkDO ->
			AssertUtil.assertNotNull(shortLinkDO.getId(), ResultCode.PARAM_INVALID, "shortLinkDO.id is invalid"));

		Map<String, Integer> resultMap = new LinkedHashMap<>();
		data.forEach(shortLinkDO -> resultMap.put(shortLinkDO.getShortCode(), shortLinkDO.getId()));
		shortLinks.forEach(shortLink -> shortLink.setId(resultMap.get(shortLink.getShortCode())));

		return shortLinks;
	}


	@Override
	public void updateByIdSelective(ShortLinkVO shortLink) {
		AssertUtil.assertNotNull(shortLink, ResultCode.PARAM_INVALID, "shortLink is null");
    	AssertUtil.assertTrue(shortLink.getId() != null, ResultCode.PARAM_INVALID, "shortLink.id is null");

		ShortLinkDO data = ShortLinkConverter.vo2DO(shortLink);
		int n = shortLinkDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ShortLink失败，影响行数:" + n);
	}

	@Override
	public List<ShortLinkVO> queryShortLinkList(ShortLinkQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ShortLinkExample example = ShortLinkConverter.query2Example(query);

		List<ShortLinkDO> list = shortLinkDAO.selectByExample(example);
		return ShortLinkConverter.doList2VOList(list);
	}

	@Override
	public Long queryShortLinkCount(ShortLinkQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ShortLinkExample example = ShortLinkConverter.query2Example(query);
		return shortLinkDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询短链接
	 */
	@Override
	public PageInfo<ShortLinkVO> queryShortLinkByPage(ShortLinkQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ShortLinkVO> page = new PageInfo<>();

		ShortLinkExample example = ShortLinkConverter.query2Example(query);
		long totalCount = shortLinkDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ShortLinkDO> list = shortLinkDAO.selectByExample(example);
		page.setList(ShortLinkConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	public ShortLinkVO selectByUrl(String url) {
		AssertUtil.assertNotBlank(url, ResultCode.PARAM_INVALID, "url is null");
		ShortLinkDO data = shortLinkDAO.selectByUrl(url);
		if (Objects.isNull(data)) {
			return null;
		}
		return ShortLinkConverter.do2VO(data);
	}

	public ShortLinkVO selectByCode(String shortCode) {
		AssertUtil.assertNotBlank(shortCode, ResultCode.PARAM_INVALID, "url is null");
		ShortLinkDO data = shortLinkDAO.selectByCode(shortCode);
		if (Objects.isNull(data)) {
			return null;
		}
		return ShortLinkConverter.do2VO(data);
	}

	@Override
	public String shortId2Original(Integer id) {
		if (id == null) {
			return null;
		}
		ShortLinkVO shortLink = selectById(id);
		if (Objects.isNull(shortLink)) {
			return null;
		}
		return shortLink.getUrl();
	}

	@Override
	public List<String> shortIdList2OriginalList(List<Integer> ids) {
		return shortIdList2OriginalList(ids, null);
	}

	@Override
	public List<String> shortIdList2OriginalList(List<Integer> ids, String orderBy) {
		if (CollectionUtils.isEmpty(ids)) {
			return new ArrayList<>();
		}
		ShortLinkQuery query = new ShortLinkQuery();
		query.setIds(ids);
		query.setOrderBy(orderBy);
		List<ShortLinkVO> shortLinks = queryShortLinkList(query);
		if (CollectionUtils.isEmpty(shortLinks)) {
			return new ArrayList<>();
		}
		return CommonUtil.listConverter(shortLinks, ShortLinkVO::getUrl);
	}

	@Override
	public String shortId2ShortUrl(Integer id) {
		if (id == null) {
			return null;
		}
		ShortLinkVO shortLink = selectById(id);
		if (Objects.isNull(shortLink)) {
			return null;
		}
		return getShortUrl(shortLink);
	}

	@Override
	public List<String> shortIdList2ShortUrlList(List<Integer> ids) {
		return shortIdList2ShortUrlList(ids, null);
	}

	@Override
	public List<String> shortIdList2ShortUrlList(List<Integer> ids, String orderBy) {
		if (CollectionUtils.isEmpty(ids)) {
			return new ArrayList<>();
		}
		ShortLinkQuery query = new ShortLinkQuery();
		query.setIds(ids);
		query.setOrderBy(orderBy);
		List<ShortLinkVO> shortLinks = queryShortLinkList(query);
		if (CollectionUtils.isEmpty(shortLinks)) {
			return new ArrayList<>();
		}
		return CommonUtil.listConverter(shortLinks, this::getShortUrl);
	}

	@Override
	public Integer original2ShortId(String originalUrl) {
		if (StringUtils.isBlank(originalUrl)) {
			return null;
		}
		// 检查长链接是否已存在
		ShortLinkVO exist = selectByUrl(originalUrl);
		if (exist != null) {
			return exist.getId();
		}

		// 基于MD5生成短码
		String shortCode = generateShortCode(originalUrl);

		// 插入数据
		ShortLinkVO data = new ShortLinkVO();
		data.setUrl(originalUrl);
		data.setShortCode(shortCode);
		insert(data);

		return data.getId();
	}

	@Override
	public List<Integer> originalList2ShortIdList(List<String> originalUrls) {
		if (CollectionUtils.isEmpty(originalUrls)) {
			return new ArrayList<>();
		}

		List<Integer> shortIds = new ArrayList<>();
		ShortLinkQuery query = new ShortLinkQuery();
		query.setUrls(originalUrls);
		List<ShortLinkVO> existList = queryShortLinkList(query);
		if (!CollectionUtils.isEmpty(existList)) {
			shortIds.addAll(existList.stream()
					.map(ShortLinkVO::getId)
					.toList());
		}

		Set<String> existSet = new HashSet<>(existList.stream().map(ShortLinkVO::getUrl).toList());
		List<String> nonExistList = originalUrls.stream().filter(originalUrl -> !existSet.contains(originalUrl)).toList();
		if (CollectionUtils.isEmpty(nonExistList)) {
			return shortIds;
		}
		List<ShortLinkVO> data = CommonUtil.listConverter(nonExistList, this::buildData);

		batchInsert(data);
		shortIds.addAll(data.stream()
				.map(ShortLinkVO::getId)
				.toList());

		return shortIds;
	}


	@Override
	public String original2Short(String originalUrl) {
		if (StringUtils.isBlank(originalUrl)) {
			return originalUrl;
		}
		// 检查长链接是否已存在
		ShortLinkVO exist = selectByUrl(originalUrl);
		if (exist != null) {
			return getShortUrlByCode(exist.getShortCode());
		}

		// 基于MD5生成短码
		String shortCode = generateShortCode(originalUrl);

		// 插入数据
		ShortLinkVO data = new ShortLinkVO();
		data.setUrl(originalUrl);
		data.setShortCode(shortCode);
		insert(data);

		return getShortUrlByCode(shortCode);
	}

	@Override
	public List<String> originalList2ShortList(List<String> originalUrls) {
		if (CollectionUtils.isEmpty(originalUrls)) {
			return originalUrls;
		}
		List<String> shortUrls = new ArrayList<>();
		ShortLinkQuery query = new ShortLinkQuery();
		query.setUrls(originalUrls);
		List<ShortLinkVO> existList = queryShortLinkList(query);
		if (!CollectionUtils.isEmpty(existList)) {
			shortUrls.addAll(existList.stream()
					.map(ShortLinkVO::getShortCode)
					.map(this::getShortUrlByCode)
					.toList());
		}
		Set<String> existSet = new HashSet<>(existList.stream().map(ShortLinkVO::getUrl).toList());
		List<String> nonExistList = originalUrls.stream().filter(originalUrl -> !existSet.contains(originalUrl)).toList();
		if (CollectionUtils.isEmpty(nonExistList)) {
			return shortUrls;
		}

		List<ShortLinkVO> data = CommonUtil.listConverter(nonExistList, this::buildData);
		batchInsert(data);
		shortUrls.addAll(data.stream()
				.map(ShortLinkVO::getShortCode)
				.map(this::getShortUrlByCode)
				.toList());

		return shortUrls;
	}

	@Override
	public String short2Original(String shortUrl) {
		if (StringUtils.isBlank(shortUrl)) {
			return shortUrl;
		}

		// 如果不是短链接, 直接返回
		if (!isShort(shortUrl)) {
			return shortUrl;
		}

		String shortCode = getCodeByShortUrl(shortUrl);
		return getOriginalUrlByCode(shortCode);
	}

	@Override
	public List<String> shortList2OriginalList(List<String> shortUrls) {
		if (CollectionUtils.isEmpty(shortUrls)) {
			return shortUrls;
		}
		// 批量查询
		List<String> shortCodes = shortUrls.stream().map(this::getCodeByShortUrl).toList();
		ShortLinkQuery query = new ShortLinkQuery();
		query.setShortCodes(shortCodes);
		List<ShortLinkVO> shortLinkList = queryShortLinkList(query);
		if (CollectionUtils.isEmpty(shortLinkList)) {
			return new ArrayList<>();
		}
		return shortLinkList.stream().map(ShortLinkVO::getUrl).toList();
	}

	@Override
	public String getOriginalUrlByCode(String shortCode) {
		ShortLinkVO shortLink = selectByCode(shortCode);
		if (Objects.isNull(shortLink)) {
			return null;
		}
		return shortLink.getUrl();
	}


	private String generateShortCode(String originalUrl) {
	    String md5 = DigestUtils.md5Hex(originalUrl);
		String timeStamp = String.valueOf(System.currentTimeMillis() % 10000);
		return md5 + timeStamp;
	}

	private String getShortUrlByCode(String code) {
		return domain + "/" + SHORT_LINK_API + "/" + code;
	}

	private String getShortUrl(ShortLinkVO shortLink) {
		return getShortUrlByCode(shortLink.getShortCode());
	}

	private String getCodeByShortUrl(String shortUrl) {
		if (StringUtils.isBlank(shortUrl)) {
			return shortUrl;
		}
		return shortUrl.substring(shortUrl.lastIndexOf("/") + 1);
	}

	private boolean isShort(String shortUrl) {
		if (StringUtils.isBlank(shortUrl)) {
			return false;
		}
        return shortUrl.startsWith(domain + "/" + SHORT_LINK_API);
    }

	private ShortLinkVO buildData(String originalUrl) {
		ShortLinkVO shortLinkVO = new ShortLinkVO();
		shortLinkVO.setUrl(originalUrl);
		shortLinkVO.setShortCode(generateShortCode(originalUrl));
		return shortLinkVO;
	}
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import java.util.List;

import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;

/**
 * 同步转异步执行器
 *
 * <AUTHOR>
 * @version : SyncToAsyncExecutor.java, v 0.1 2024/12/6 15:44 renxiao.wu Exp $
 */
public interface BatchToAsyncExecutor {
    /**
     * 将同步任务数据进行存储
     *
     * @param request 请求
     * @param batch   批次信息
     */
    void storeSync(CreativeRequest request, CreativeBatchVO batch);

    /**
     * 将同步任务数据从batch转存到task中
     *
     * @param task  任务信息
     * @param batch 批次信息
     */
    void restoreTask(CreativeTaskVO task, CreativeBatchVO batch);

    /**
     * 异步执行
     *
     * @param task     任务信息
     * @param elements 元素信息列表
     */
    void asyncExecAndStore(CreativeTaskVO task, List<CreativeElementVO> elements);
}

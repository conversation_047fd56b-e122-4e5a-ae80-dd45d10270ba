package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;

import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsUserPointDO;
import ai.conrain.aigc.platform.dal.example.StatsUserPointExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsUserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsUserPointVO;
import ai.conrain.aigc.platform.service.model.converter.StatsUserPointConverter;
import ai.conrain.aigc.platform.dal.dao.StatsUserPointDAO;
import ai.conrain.aigc.platform.service.component.StatsUserPointService;

/**   
 * StatsUserPointService实现
 *
 * <AUTHOR>
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
@Slf4j
@Service
public class StatsUserPointServiceImpl implements StatsUserPointService {

	/** DAO */
	@Autowired
	private StatsUserPointDAO statsUserPointDAO;

	@Override
	public StatsUserPointVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		StatsUserPointDO data = statsUserPointDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return StatsUserPointConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = statsUserPointDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsUserPoint失败");
	}

	@Override
	public StatsUserPointVO insert(StatsUserPointVO statsUserPoint) {
		AssertUtil.assertNotNull(statsUserPoint, ResultCode.PARAM_INVALID, "statsUserPoint is null");
		AssertUtil.assertTrue(statsUserPoint.getId() == null, ResultCode.PARAM_INVALID, "statsUserPoint.id is present");

		//创建时间、修改时间兜底
		if (statsUserPoint.getCreateTime() == null) {
			statsUserPoint.setCreateTime(new Date());
		}

		if (statsUserPoint.getModifyTime() == null) {
			statsUserPoint.setModifyTime(new Date());
		}

		StatsUserPointDO data = StatsUserPointConverter.vo2DO(statsUserPoint);
		int n = statsUserPointDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsUserPoint失败");
		AssertUtil.assertNotNull(data.getId(), "新建StatsUserPoint返回id为空");
		statsUserPoint.setId(data.getId());
		return statsUserPoint;
	}


	@Override
	public void updateByIdSelective(StatsUserPointVO statsUserPoint) {
		AssertUtil.assertNotNull(statsUserPoint, ResultCode.PARAM_INVALID, "statsUserPoint is null");
    	AssertUtil.assertTrue(statsUserPoint.getId() != null, ResultCode.PARAM_INVALID, "statsUserPoint.id is null");

		//修改时间必须更新
		statsUserPoint.setModifyTime(new Date());
		StatsUserPointDO data = StatsUserPointConverter.vo2DO(statsUserPoint);
		int n = statsUserPointDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsUserPoint失败，影响行数:" + n);
	}

	@Override
	public List<StatsUserPointVO> queryStatsUserPointList(StatsUserPointQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		StatsUserPointExample example = StatsUserPointConverter.query2Example(query);

		List<StatsUserPointDO> list = statsUserPointDAO.selectByExample(example);
			return StatsUserPointConverter.doList2VOList(list);
	}

	@Override
	public Long queryStatsUserPointCount(StatsUserPointQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		StatsUserPointExample example = StatsUserPointConverter.query2Example(query);
        return statsUserPointDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询用户点数消耗统计
	 */
	@Override
	public PageInfo<StatsUserPointVO> queryStatsUserPointByPage(StatsUserPointQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<StatsUserPointVO> page = new PageInfo<>();

		StatsUserPointExample example = StatsUserPointConverter.query2Example(query);
		long totalCount = statsUserPointDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<StatsUserPointDO> list = statsUserPointDAO.selectByExampleWithBlob(example);
		page.setList(StatsUserPointConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

	@Override
	public List<Integer> findUsersWithPointConsumption(Date startDate, Date endDate) {
	    return statsUserPointDAO.findUsersWithConsumptionOrRecharge(startDate, endDate);
	}
	
	@Override
	public StatsUserPointDO getUserPointConsumption(Integer userId, Date startDate, Date endDate) {
        return statsUserPointDAO.getUserPointConsumption(userId, startDate, endDate);
	}

	@Override
	public StatsUserPointDO getAllUserPointConsumption(Date startDate, Date endDate) {
		// 查询所有用户的总消费情况，不传userId表示汇总所有用户
		return statsUserPointDAO.getAllUserPointConsumption(null, startDate, endDate);
	}

	/** 单次 IN 查询需限制在 500 个参数以内 */
	@Override
	public List<StatsUserPointDO> getUsersPointConsumptionBatch(List<Integer> userIds, Date startDate, Date endDate) {
	    List<StatsUserPointDO> results = statsUserPointDAO.getUsersPointConsumptionBatch(userIds, startDate, endDate);
	    return results != null ? results : new ArrayList<>();
	}
	
	@Override
	public int batchInsertOrUpdateStats(List<StatsUserPointDO> statsList) {
	    if (statsList == null || statsList.isEmpty()) {
	        return 0;
	    }
	    return statsUserPointDAO.batchInsertOrUpdateSelective(statsList);
	}

}
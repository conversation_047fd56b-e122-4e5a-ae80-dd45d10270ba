package ai.conrain.aigc.platform.service.component.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.MaterialModelDO;
import ai.conrain.aigc.platform.dal.entity.PatchCutoutTaskDO;
import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.component.ComfyuiTaskService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.PatchTaskService;
import ai.conrain.aigc.platform.service.component.dispatch.FileDispatch;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.enums.PathTaskStatusEnum;
import ai.conrain.aigc.platform.service.model.biz.CutoutTaskRetDetail;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.request.ReCutoutSingleImageRequest;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * PathTaskService实现
 */
@Slf4j
@Service
public class PathTaskServiceImpl implements PatchTaskService {
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private MaterialModelDAO materialModelDAO;
    @Autowired
    private ComfyuiTaskService comfyuiTaskService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private FileDispatch fileDispatch;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private MaterialModelServiceImpl materialModelServiceImpl;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createRecutPatchTask(ReCutoutSingleImageRequest request) {
        // 1. 加锁，保证数据一致性
        Integer modelId = request.getModelId();
        MaterialModelVO model = materialModelService.lockById(modelId);
        AssertUtil.assertNotNull(model, "模型不存在: " + modelId);

        // 2. 从extInfo中获取或初始化补丁任务列表
        List<PatchCutoutTaskDO> patchTasks = model.getExtInfo("patchCutoutTasks", List.class);
        if (patchTasks == null) {
            patchTasks = new ArrayList<>();
        }

        // 3. 创建新的补丁任务对象
        String imageDirectory = request.getImageDirectory();
        // 批量抠图文件名形式
        List<String> imageFileName = request.getImageFileName();
        String imageFileNameStr = StringUtils.join(imageFileName, ",");
        Map<String, String> targetLabelFiles = request.getTargetLabelFiles();

        PatchCutoutTaskDO patchCutoutTaskDO = new PatchCutoutTaskDO();
        patchCutoutTaskDO.setImageDirectory(imageDirectory);
        patchCutoutTaskDO.setImageFileName(imageFileNameStr);
        patchCutoutTaskDO.setTargetLabelFiles(targetLabelFiles);
        patchCutoutTaskDO.setCutoutKeyword(request.getCutoutKeyword());
        patchCutoutTaskDO.setImageSize(request.getImageSize());
        patchCutoutTaskDO.setCut4ScaleUp(request.getCut4ScaleUp());
        patchCutoutTaskDO.setCutoutType(request.getCutoutType());
        patchCutoutTaskDO.setCutoutModel(request.getCutoutModel());
        patchCutoutTaskDO.setDetectThreshold(request.getDetectThreshold());
        patchCutoutTaskDO.setBgMultiColor(request.getBgMultiColor());
        patchCutoutTaskDO.setStatus("PENDING");
        patchCutoutTaskDO.setCreatedAt(new Date());
        patchCutoutTaskDO.setTaskId(null);

        // 4. 将新任务添加到列表
        patchTasks.add(patchCutoutTaskDO);

        // 5. 将更新后的列表写回extInfo
        model.addExtInfo("patchCutoutTasks", patchTasks);

        // 6. 更新数据库
        materialModelService.innerUpdate(model);
        log.info("已为模型 {} 创建一个重新抠图的补丁任务，待处理。图片目录: {}, 文件名: {},  目标替换标签文件: {}",
            modelId, imageDirectory, imageFileName, targetLabelFiles);
    }

    @Override
    public List<Map<String, Object>> getPatchCutoutTasksList(Integer id) {
        MaterialModelDO materialModelDO = materialModelDAO.selectByPrimaryKey(id);
        if (materialModelDO == null) {
            return null;
        }
        String extInfo = materialModelDO.getExtInfo();
        List<Map<String, Object>> patchCutoutTaskList = new ArrayList<>();
        if (StringUtils.isNotBlank(extInfo)) {
            try {
                JSONObject extInfoObject = JSONObject.parseObject(extInfo);
                JSONArray patchCutoutTasks = extInfoObject.getJSONArray("patchCutoutTasks");
                if (patchCutoutTasks != null) {
                    for (int i = 0; i < patchCutoutTasks.size(); i++) {
                        JSONObject taskObject = patchCutoutTasks.getJSONObject(i);
                        Map<String, Object> taskMap =
                            taskObject.toJavaObject(Map.class);
                        patchCutoutTaskList.add(taskMap);
                    }
                    Collections.reverse(patchCutoutTaskList);
                }

            } catch (Exception e) {
                log.error("解析补丁任务列表失败，模型id：{}", id);
            }
        }
        return patchCutoutTaskList;
    }

    @Override
    @Transactional
    public Boolean deletePatchCutoutTask(Integer id, String taskId) {
        MaterialModelDO materialModelDO = materialModelDAO.selectByPrimaryKey(id);
        if (materialModelDO == null) {
            return false;
        }
        String extInfo = materialModelDO.getExtInfo();
        JSONObject extInfoObject = JSONObject.parseObject(extInfo);
        JSONArray patchCutoutTasks = extInfoObject.getJSONArray("patchCutoutTasks");
        if (patchCutoutTasks == null) {
            return false;
        }
        boolean removed = false;
        for (int i = 0; i < patchCutoutTasks.size(); i++) {
            JSONObject task = patchCutoutTasks.getJSONObject(i);
            String patchTaskId = task.getString("taskId");
            String patchStatus = task.getString("status");
            if (patchTaskId.equals(taskId) && patchStatus.equals("FAILED")) {
                patchCutoutTasks.remove(i);
                removed = true;
                break;
            }
        }

        if (removed) {
            extInfoObject.put("patchCutoutTasks", patchCutoutTasks);
            materialModelDO.setExtInfo(extInfoObject.toJSONString());
            materialModelDAO.updateByPrimaryKeySelective(materialModelDO);
            return true;
        }
        return false;
    }

    /**
     * 处理补丁任务
     *
     * @param model 模型
     */
    public void processPatchTasks(MaterialModelVO model) {
        // 检查是否有补丁任务
        @SuppressWarnings("unchecked")
        List<JSONObject> patchTasksJson = model.getExtInfo("patchCutoutTasks", List.class);
        if (CollectionUtils.isEmpty(patchTasksJson)) {
            return;
        }
        // 转换JSONObject 到PatchCutoutTaskDO
        ArrayList<PatchCutoutTaskDO> patchCutoutTasks = new ArrayList<>();
        for (JSONObject task : patchTasksJson) {
            PatchCutoutTaskDO patchCutoutTask = (PatchCutoutTaskDO)CommonUtil.parseObject(task.toJSONString(),
                PatchCutoutTaskDO.class);
            patchCutoutTasks.add(patchCutoutTask);
        }

        boolean hasUpdates = false;
        for (PatchCutoutTaskDO patchTask : patchCutoutTasks) {
            String status = patchTask.getStatus();

            if (PathTaskStatusEnum.PENDING.getCode().equals(status)) {
                // 未进行 - 创建抠图任务
                hasUpdates |= createCutoutTaskForPatch(model, patchTask);
            } else if (PathTaskStatusEnum.RUNNING.getCode().equals(status)) {
                // 进行中 - 检查状态
                hasUpdates |= checkCutoutTaskStatus(model, patchTask);
            }
        }

        // 如果有更新，保存到数据库
        if (hasUpdates) {
            model.addExtInfo("patchCutoutTasks", patchCutoutTasks);
        }
    }

    /**
     * 为补丁任务创建抠图任务
     *
     * @param model     模型
     * @param patchTask 补丁任务
     * @return 是否有更新
     */
    private boolean createCutoutTaskForPatch(MaterialModelVO model, PatchCutoutTaskDO patchTask) {
        try {
            // 构建抠图任务参数
            LoraTrainDetail trainDetail = model.getClothLoraTrainDetail();
            if (trainDetail == null) {
                log.warn("模型 {} 训练详情为空，跳过补丁任务处理", model.getId());
                return false;
            }

            // 创建临时的训练详情用于抠图任务
            LoraTrainDetail patchTrainDetail = new LoraTrainDetail();
            BeanUtils.copyProperties(trainDetail, patchTrainDetail);

            // 设置补丁任务的特定参数
            // 图片目录
            String imageDirectory = patchTask.getImageDirectory();
            // 图片名
            String imageFileName = patchTask.getImageFileName();

            // 设置抠图源目录
            patchTrainDetail.setClothDir(imageDirectory);
            patchTrainDetail.setCutoutSourceDir(imageDirectory);
            // 设置具体的抠图文件名字（需要完整路径）
            String detectThreshold = patchTask.getDetectThreshold();

            JSONObject appendInfo = new JSONObject();
            // 原图文件名
            appendInfo.put("fileNames", imageFileName);
            // 检查阈值
            if (detectThreshold != null) {
                appendInfo.put("detectThreshold", detectThreshold);
            } else {
                appendInfo.put("detectThreshold", "0.3");
            }
            patchTrainDetail.setAppendInfo(appendInfo.toJSONString());
            // 设置重新抠图参数
            patchTrainDetail.setCutoutKeyword(patchTask.getCutoutKeyword());
            patchTrainDetail.setCut4ScaleUp(patchTask.getCut4ScaleUp());
            patchTrainDetail.setCutoutModel(patchTask.getCutoutModel());
            patchTrainDetail.setImageSize(patchTask.getImageSize());
            patchTrainDetail.setBgMultiColor(patchTask.getBgMultiColor());
            patchTrainDetail.setCutoutType(patchTask.getCutoutType());
            // bugfix 。如果是补丁抠图任务，直接设置一个标记，防止getTrainTaskWorkflowCfg获取到不同的工作流
            patchTrainDetail.setPatchCutout(true);
            // 创建comfyui抠图任务
            ComfyuiTaskVO cutoutTask = materialModelServiceImpl.buildTask(model, ComfyuiTaskTypeEnum.cutout, null);
            cutoutTask.setReqParams(CommonUtil.java2JSONObject(patchTrainDetail));
            cutoutTask = comfyuiTaskService.createTask(cutoutTask);

            // 更新补丁任务状态
            patchTask.setStatus(PathTaskStatusEnum.RUNNING.getCode());
            patchTask.setTaskId(String.valueOf(cutoutTask.getId()));
            patchTask.setStartedAt(LocalDateTime.now().toString());
            patchTask.setUpdatedAt(LocalDateTime.now().toString());

            log.info("为模型 {} 的补丁任务创建抠图任务，图片目录: {}, 文件名: {}, taskId: {}",
                model.getId(), imageDirectory, imageFileName, cutoutTask.getId());
            return true;
        } catch (Exception e) {
            log.error("为模型 {} 创建补丁抠图任务失败", model.getId(), e);
            patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
            patchTask.setErrorMessage(e.getMessage());
            return true;
        }
    }

    /**
     * 检查补丁抠图任务状态
     *
     * @param model     模型
     * @param patchTask 补丁任务
     * @return 是否有更新
     */
    private boolean checkCutoutTaskStatus(MaterialModelVO model, PatchCutoutTaskDO patchTask) {
        Integer taskId = Integer.valueOf(patchTask.getTaskId());
        if (taskId == null) {
            log.warn("补丁任务状态为RUNNING但taskId为空，模型ID: {}", model.getId());
            patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
            patchTask.setErrorMessage("taskId为空");
            return true;
        }

        try {
            ComfyuiTaskVO cutoutTask = comfyuiTaskService.pollingTaskStatus(taskId);
            if (cutoutTask == null) {
                log.warn("找不到抠图任务，taskId: {}, 模型ID: {}", taskId, model.getId());
                patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
                patchTask.setErrorMessage("找不到抠图任务");
                return true;
            }

            if (cutoutTask.getTaskStatus() == QueueResult.QueueCodeEnum.COMPLETED) {
                // 抠图完成，处理文件替换
                handleCutoutTaskCompleted(model, patchTask, cutoutTask);
                return true;
            } else if (cutoutTask.getTaskStatus() == QueueResult.QueueCodeEnum.FAILED) {
                // 抠图失败
                patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
                patchTask.setErrorMessage("抠图任务失败");
                patchTask.setCompletedAt(String.valueOf(new DateTime()));
                log.warn("补丁抠图任务失败，taskId: {}, 模型ID: {}", taskId, model.getId());
                return true;
            }
            // 任务仍在进行中，无需更新
            return false;
        } catch (Exception e) {
            log.error("检查补丁抠图任务状态失败，taskId: {}, 模型ID: {}", taskId, model.getId(), e);
            patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
            patchTask.setErrorMessage(e.getMessage());
            return true;
        }
    }

    /**
     * 处理抠图任务完成后的文件替换
     *
     * @param model      模型
     * @param patchTask  补丁任务
     * @param cutoutTask 抠图任务
     * @return 是否有更新
     */
    private void handleCutoutTaskCompleted(MaterialModelVO model, PatchCutoutTaskDO patchTask,
                                           ComfyuiTaskVO cutoutTask) {
        try {
            // 获取新抠图文件路径
            CutoutTaskRetDetail retDetail = CommonUtil.parseObject(cutoutTask.getRetDetail(),
                CutoutTaskRetDetail.class);
            if (retDetail == null || StringUtils.isBlank(retDetail.getCutoutRetDir())) {
                log.warn("抠图任务完成但结果详情为空，taskId: {}, 模型ID: {}", cutoutTask.getId(), model.getId());
                patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
                patchTask.setErrorMessage("抠图结果为空");
            }

            String newCutoutDir = retDetail.getCutoutRetDir();
            // 要替换的打标文件对象。key是文件名 value是文件路径
            Map<String, String> targetLabelFiles = patchTask.getTargetLabelFiles();

            // 获取文件服务器URL
            String fileServerUrl = serverHelper.getFileServerUrlByTask(cutoutTask);

            // 查找新生成的抠图文件
            List<FileVO> newCutoutFiles = comfyUIService.viewFiles(newCutoutDir, new String[] {"img", "text"},
                fileServerUrl);
            if (CollectionUtils.isEmpty(newCutoutFiles)) {
                log.warn("抠图完成但没有找到新的抠图文件，目录: {}, 模型ID: {}", newCutoutDir, model.getId());
                patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
                patchTask.setErrorMessage("没有找到新的抠图文件");
            }

            // 批量替换打标文件
            targetLabelFiles.forEach((targetFileNames, targetLabelPath) -> {
                // 删除老的打标图片
                if (StringUtils.isNotBlank(targetLabelPath) && StringUtils.isNotBlank(targetFileNames)) {
                    String targetFileNamePrefix = targetFileNames.substring(0, targetFileNames.lastIndexOf("."));
                    fileDispatch.removeImageOrTxtFile(targetLabelPath, targetFileNamePrefix, fileServerUrl);
                    log.info("删除旧打标文件: {}", targetLabelPath + "/" + targetFileNames);
                }

                String newCutoutPath = newCutoutDir + "/" + targetFileNames;
                String replaceTargetLabelPath = targetLabelPath + "/" + targetFileNames;
                boolean copySuccess = comfyUIService.fileCopy(newCutoutPath, replaceTargetLabelPath, fileServerUrl);

                if (copySuccess) {
                    log.info("成功复制抠图文件到打标目录:{} -> {}", newCutoutDir, replaceTargetLabelPath);
                } else {
                    log.error("复制抠图文件到打标目录失败：{} -> {}", newCutoutDir, replaceTargetLabelPath);
                    throw new RuntimeException("复制抠图文件到打标目录失败");
                }
            });

            // 更新补丁任务状态
            patchTask.setStatus(PathTaskStatusEnum.COMPLETED.getCode());
            patchTask.setCompletedAt(String.valueOf(new DateTime()));
            log.info("补丁抠图任务完成，模型ID: {}, 图片目录: {}, 文件名: {}",
                model.getId(), patchTask.getImageDirectory(), patchTask.getImageFileName());
        } catch (Exception e) {
            log.error("处理补丁抠图任务完成失败，模型ID: {}", model.getId(), e);
            patchTask.setStatus(PathTaskStatusEnum.FAILED.getCode());
            patchTask.setErrorMessage(e.getMessage());
            patchTask.setCompletedAt(String.valueOf(new DateTime()));
        }
    }

}

package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.service.acm.AcmConfigService;
import ai.conrain.aigc.platform.service.acm.AcmResource;
import ai.conrain.aigc.platform.service.component.DistributorService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PricePlanCode;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.PricePlan;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class PricePlanHelper {

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private AcmConfigService acmConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private DistributorService distributorService;

    public List<PricePlan> queryPricePlansByUser(Integer masterUserId) {
        AssertUtil.assertNotNull(masterUserId, ResultCode.PARAM_INVALID, "masterUserId is null");

        UserVO userVO = userService.selectById(masterUserId);
        AssertUtil.assertNotNull(userVO, "查询用户失败");

        Integer distributorCorpId = null;

        //商户，检查其是否是渠道商的会员
        if (RoleTypeEnum.MERCHANT == userVO.getRoleType()) {
            DistributorCustomerVO customer = distributorService.queryDistributorInfoByMerchantId(masterUserId);

            // 渠道商的客户
            if (customer != null) {
                distributorCorpId = customer.getDistributorCorpOrgId();
            }

            //渠道商
        } else if (RoleTypeEnum.DISTRIBUTOR == userVO.getRoleType()) {
            distributorCorpId = userVO.getCorpOrgId();
        }

        log.info("queryPricePlansByUser distributorCorpId={}", distributorCorpId);

        //渠道商或渠道商的会员，优先取渠道商配置
        if (distributorCorpId != null) {
            String cfg = systemConfigService.queryValueByKey(SystemConstants.DISTRIBUTOR_PRICE_PLAN_CFG_PREFIX + distributorCorpId);
            if (CommonUtil.isValidJsonArray(cfg)) {
                return JSONArray.parseArray(cfg, PricePlan.class);
            } else {
                log.info("当前账号为渠道商或渠道商会员，但没有配置特定渠道商价格方案，因此走默认方案，distributorCorpId={}", distributorCorpId);
            }
        }

        //默认配置
        String cfg = systemConfigService.queryValueByKey(SystemConstants.PRICE_PLAN_CFG);
        if (CommonUtil.isValidJsonArray(cfg)) {
            List<PricePlan> list = JSONArray.parseArray(cfg, PricePlan.class);

            //当前用户在白名单里，修改一下配置价格，让它为4，3，2，1，内部开发测试用
            List<Integer> testUsers = acmConfigService.getConfigAsArray(AcmResource.PRICE_TEST_WHITELIST,
                    Integer.class);
            if (OperationContextHolder.getOperatorUserId() != null && CollectionUtils.isNotEmpty(testUsers)
                    && testUsers.contains(OperationContextHolder.getOperatorUserId())) {
                for (int i = 0; i < list.size(); ++i) {
                    String m = "0." + (list.size() - i);
                    log.warn("当前用户在白名单里，修改一下配置价格:{},{}=>{}", list.get(i).getCode(),
                            list.get(i).getAmount(), m);
                    list.get(i).setAmount(m);
                }
            }

            return list;
        }

        return new ArrayList<>();
    }

    public PricePlan queryPricePlanByCode(String planCode, Integer masterUserId) {
        AssertUtil.assertNotBlank(planCode, "planCode为空");

        PricePlanCode code = PricePlanCode.getByCode(planCode);
        AssertUtil.assertNotNull(code, "非法的planCode:" + planCode);

        List<PricePlan> list = queryPricePlansByUser(masterUserId);
        for (PricePlan p : list) {
            if (StringUtils.equalsIgnoreCase(p.getCode(), planCode)) {
                return p;
            }
        }
        throw new RuntimeException("获取价格失败：" + planCode);
    }
}

package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.CaptionAttributeDO;
import ai.conrain.aigc.platform.dal.example.CaptionAttributeExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;
import ai.conrain.aigc.platform.service.model.converter.CaptionAttributeConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.CaptionAttributeDAO;
import ai.conrain.aigc.platform.service.component.CaptionAttributeService;

/**   
 * CaptionAttributeService实现
 *
 * <AUTHOR>
 * @version CaptionAttributeService.java v 0.1 2025-08-14 07:49:02
 */
@Slf4j
@Service
public class CaptionAttributeServiceImpl implements CaptionAttributeService {

	/** DAO */
	@Autowired
	private CaptionAttributeDAO captionAttributeDAO;

	@Override
	public CaptionAttributeVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		CaptionAttributeDO data = captionAttributeDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return CaptionAttributeConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = captionAttributeDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CaptionAttribute失败");
	}

	@Override
	public CaptionAttributeVO insert(CaptionAttributeVO captionAttribute) {
		AssertUtil.assertNotNull(captionAttribute, ResultCode.PARAM_INVALID, "captionAttribute is null");
		AssertUtil.assertTrue(captionAttribute.getId() == null, ResultCode.PARAM_INVALID, "captionAttribute.id is present");

		//创建时间、修改时间兜底
		if (captionAttribute.getCreateTime() == null) {
			captionAttribute.setCreateTime(new Date());
		}

		if (captionAttribute.getModifyTime() == null) {
			captionAttribute.setModifyTime(new Date());
		}

		CaptionAttributeDO data = CaptionAttributeConverter.vo2DO(captionAttribute);
		Integer n = captionAttributeDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CaptionAttribute失败");
		AssertUtil.assertNotNull(data.getId(), "新建CaptionAttribute返回id为空");
		captionAttribute.setId(data.getId());
		return captionAttribute;
	}


	@Override
	public void updateByIdSelective(CaptionAttributeVO captionAttribute) {
		AssertUtil.assertNotNull(captionAttribute, ResultCode.PARAM_INVALID, "captionAttribute is null");
    	AssertUtil.assertTrue(captionAttribute.getId() != null, ResultCode.PARAM_INVALID, "captionAttribute.id is null");

		//修改时间必须更新
		captionAttribute.setModifyTime(new Date());
		CaptionAttributeDO data = CaptionAttributeConverter.vo2DO(captionAttribute);
		int n = captionAttributeDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CaptionAttribute失败，影响行数:" + n);
	}

	@Override
	public List<CaptionAttributeVO> queryCaptionAttributeList(CaptionAttributeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CaptionAttributeExample example = CaptionAttributeConverter.query2Example(query);

		List<CaptionAttributeDO> list = captionAttributeDAO.selectByExample(example);
		return CaptionAttributeConverter.doList2VOList(list);
	}

	@Override
	public Long queryCaptionAttributeCount(CaptionAttributeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		CaptionAttributeExample example = CaptionAttributeConverter.query2Example(query);
		return captionAttributeDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询属性定义表，用于存储图像描述（caption）中可配置的属性元信息
	 */
	@Override
	public PageInfo<CaptionAttributeVO> queryCaptionAttributeByPage(CaptionAttributeQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<CaptionAttributeVO> page = new PageInfo<>();

		CaptionAttributeExample example = CaptionAttributeConverter.query2Example(query);
		long totalCount = captionAttributeDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<CaptionAttributeDO> list = captionAttributeDAO.selectByExample(example);
		page.setList(CaptionAttributeConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
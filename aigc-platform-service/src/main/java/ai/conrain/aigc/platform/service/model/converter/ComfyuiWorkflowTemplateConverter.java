package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateDO;
import ai.conrain.aigc.platform.dal.example.ComfyuiWorkflowTemplateExample;
import ai.conrain.aigc.platform.service.model.query.ComfyuiWorkflowTemplateQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * ComfyuiWorkflowTemplateConverter
 *
 * @version ComfyuiWorkflowTemplateService.java v 0.1 2025-06-11 03:56:03
 */
public class ComfyuiWorkflowTemplateConverter {

    /**
     * DO -> VO
     */
    public static ComfyuiWorkflowTemplateVO do2VO(ComfyuiWorkflowTemplateDO from) {
        ComfyuiWorkflowTemplateVO to = new ComfyuiWorkflowTemplateVO();
        to.setId(from.getId());
        to.setTemplateKey(from.getTemplateKey());
        to.setTemplateDesc(from.getTemplateDesc());
        to.setVersion(from.getVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setTemplateData(from.getTemplateData());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ComfyuiWorkflowTemplateDO vo2DO(ComfyuiWorkflowTemplateVO from) {
        ComfyuiWorkflowTemplateDO to = new ComfyuiWorkflowTemplateDO();
        to.setId(from.getId());
        to.setTemplateKey(from.getTemplateKey());
        to.setTemplateDesc(from.getTemplateDesc());
        to.setVersion(from.getVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setTemplateData(from.getTemplateData());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ComfyuiWorkflowTemplateExample query2Example(ComfyuiWorkflowTemplateQuery from) {
        ComfyuiWorkflowTemplateExample to = new ComfyuiWorkflowTemplateExample();
        ComfyuiWorkflowTemplateExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getTemplateKey())) {
            c.andTemplateKeyEqualTo(from.getTemplateKey());
        }
        if (!ObjectUtils.isEmpty(from.getTemplateDesc())) {
            c.andTemplateDescEqualTo(from.getTemplateDesc());
        }
        if (!ObjectUtils.isEmpty(from.getVersion())) {
            c.andVersionEqualTo(from.getVersion());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateBy())) {
            c.andCreateByEqualTo(from.getCreateBy());
        }
        if (!ObjectUtils.isEmpty(from.getModifyBy())) {
            c.andModifyByEqualTo(from.getModifyBy());
        }

        if (from.isOnlyShowTestVersion()) {
            c.andInTestVersion();
        }

        if (!from.isNeedDeleted()) {
            c.andNotDeleted();
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ComfyuiWorkflowTemplateVO> doList2VOList(List<ComfyuiWorkflowTemplateDO> list) {
        return CommonUtil.listConverter(list, ComfyuiWorkflowTemplateConverter::do2VO);
    }
}
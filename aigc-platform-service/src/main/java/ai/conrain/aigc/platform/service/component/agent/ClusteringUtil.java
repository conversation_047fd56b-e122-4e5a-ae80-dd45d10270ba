package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import lombok.extern.slf4j.Slf4j;
import org.tribuo.Dataset;
import org.tribuo.Example;
import org.tribuo.Model;
import org.tribuo.MutableDataset;
import org.tribuo.Prediction;
import org.tribuo.clustering.ClusterID;
import org.tribuo.clustering.ClusteringFactory;
import org.tribuo.clustering.hdbscan.HdbscanTrainer;
import org.tribuo.impl.ArrayExample;
import org.tribuo.provenance.DataSourceProvenance;
import org.tribuo.provenance.SimpleDataSourceProvenance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ClusteringUtil {

    public static List<List<StyleImageCandidate>> performHDBSCANClustering(List<StyleImageCandidate> candidates,
            double[][] vectors) {
        Dataset<ClusterID> dataset = createTribuoDataset(vectors);
        HdbscanTrainer trainer = new HdbscanTrainer(
                2, // 最小聚类大小
                HdbscanTrainer.Distance.COSINE, // 距离函数
                2, // 计算核心距离的邻居数量
                4 // 并行计算线程数
        );

        // 训练HDBSCAN模型
        Model<ClusterID> model = trainer.train(dataset);

        // 获取聚类结果
        List<Prediction<ClusterID>> predictions = new ArrayList<>();
        for (Example<ClusterID> example : dataset) {
            predictions.add(model.predict(example));
        }

        // 将Tribuo聚类结果转换为我们的数据结构
        Map<Integer, List<StyleImageCandidate>> clusterMap = convertTribuoResults(predictions, candidates);

        // 过滤掉小于5个图片的簇
        clusterMap.entrySet().removeIf(entry -> entry.getValue().size() < 5);

        List<List<StyleImageCandidate>> clusters = new ArrayList<>(clusterMap.values());

        return clusters;
    }

    private static Dataset<ClusterID> createTribuoDataset(double[][] vectors) {
        ClusteringFactory factory = new ClusteringFactory();
        DataSourceProvenance provenance = new SimpleDataSourceProvenance("Generated clustering data", factory);
        MutableDataset<ClusterID> dataset = new MutableDataset<>(provenance, factory);

        for (int i = 0; i < vectors.length; i++) {
            double[] vector = vectors[i];

            // 使用 ArrayExample 创建示例
            ArrayExample<ClusterID> example = new ArrayExample<>(factory.getUnknownOutput(), 1.0f, vector.length);

            // 添加特征到示例
            for (int j = 0; j < vector.length; j++) {
                example.add("feature_" + j, vector[j]);
            }

            dataset.add(example);
        }

        return dataset;
    }

    private static Map<Integer, List<StyleImageCandidate>> convertTribuoResults(
            List<Prediction<ClusterID>> predictions, List<StyleImageCandidate> candidates) {
        Map<Integer, List<StyleImageCandidate>> clusterMap = new HashMap<>();

        for (int i = 0; i < predictions.size() && i < candidates.size(); i++) {
            Prediction<ClusterID> prediction = predictions.get(i);
            ClusterID clusterId = prediction.getOutput();

            // 跳过噪声点（聚类ID为0的点被认为是噪声）
            if (clusterId.getID() != 0) {
                int clusterIdInt = clusterId.getID();
                clusterMap.computeIfAbsent(clusterIdInt, k -> new ArrayList<>()).add(candidates.get(i));
            }
        }

        return clusterMap;
    }

}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

/**
 * 图片放大请求
 *
 * <AUTHOR>
 * @version : ImageUpscaleRequest.java, v 0.1 2024/8/9 10:55 renxiao.wu Exp $
 */
@Data
public class ImageUpscaleRequest implements CreativeRequest {
    private static final long serialVersionUID = 9038388273793609246L;

    /** 放大图片的taskId */
    private Integer taskId;
    /** 待放大的图片ossUrl */
    private String upscaleImage;
    /** 图片来源：上传upload/历史纪录history */
    private String imageSource;
}

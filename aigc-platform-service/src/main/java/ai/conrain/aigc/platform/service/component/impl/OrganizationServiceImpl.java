package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.OrganizationDAO;
import ai.conrain.aigc.platform.dal.entity.OrganizationDO;
import ai.conrain.aigc.platform.dal.example.OrganizationExample;
import ai.conrain.aigc.platform.integration.qichacha.QiChaChaService;
import ai.conrain.aigc.platform.integration.qichacha.model.QiChaChaModelPageVO;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.UserOrganizationService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.AdministrativeDivisionCodeEnum;
import ai.conrain.aigc.platform.service.enums.SalesTypeEnum;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.OrganizationTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.OrganizationConverter;
import ai.conrain.aigc.platform.service.model.query.OrganizationQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.CorpAuthVO;
import ai.conrain.aigc.platform.service.model.vo.OrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserOrganizationVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DingTalkBuildUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * OrganizationService实现
 *
 * <AUTHOR>
 * @version OrganizationService.java v 0.1 2024-07-12 10:10:23
 */
@Slf4j
@Service
public class OrganizationServiceImpl implements OrganizationService {

    /**
     * DAO
     */
    @Autowired
    private OrganizationDAO organizationDAO;

    @Autowired
    private UserOrganizationService userOrganizationService;

    @Autowired
    private QiChaChaService qiChaChaService;

    @Autowired
    private UserService userService;

    @Override
    public OrganizationVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        OrganizationDO data = organizationDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return OrganizationConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        userOrganizationService.deleteByOrgId(id);

        int n = organizationDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Organization失败");
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public OrganizationVO insert(OrganizationVO organization) {
        AssertUtil.assertNotNull(organization, ResultCode.PARAM_INVALID, "organization is null");
        AssertUtil.assertTrue(organization.getId() == null, ResultCode.PARAM_INVALID, "organization.id is present");

        //创建时间、修改时间兜底
        if (organization.getCreateTime() == null) {
            organization.setCreateTime(new Date());
        }

        if (organization.getModifyTime() == null) {
            organization.setModifyTime(new Date());
        }

        OrganizationDO data = OrganizationConverter.vo2DO(organization);
        Integer n = organizationDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Organization失败");
        AssertUtil.assertNotNull(data.getId(), "新建Organization返回id为空");
        organization.setId(data.getId());

        if (organization.getRootId() == null) {
            OrganizationVO target = new OrganizationVO();
            target.setId(data.getId());
            target.setRootId(data.getId());
            this.updateByIdSelective(target);

            organization.setRootId(data.getId());
        }

        return organization;
    }

    /**
     * 创建组织
     *
     * @param organization
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrganizationVO create(OrganizationVO organization) {
        AssertUtil.assertNotNull(organization, ResultCode.PARAM_INVALID, "organization is null");

        AssertUtil.assertTrue(organization.getRoot() != null, ResultCode.PARAM_INVALID, "root is null");

        AssertUtil.assertNotBlank(organization.getName(), ResultCode.PARAM_INVALID, "name is empty");

        organization.setCreatorMasterUserId(OperationContextHolder.getMasterUserId());
        organization.setCreatorUserRoleType(OperationContextHolder.getRoleType().getCode());
        organization.setCreatorOperatorUserId(OperationContextHolder.getOperatorUserId());
        organization.setModifierOperatorUserId(OperationContextHolder.getOperatorUserId());

        //创建根组织（企业）
        if (organization.getRoot()) {
            AssertUtil.assertTrue(organization.getParentId() == null, ResultCode.PARAM_INVALID, "parentId is not null");
            AssertUtil.assertTrue(organization.getRootId() == null, ResultCode.PARAM_INVALID, "rootId is not null");
            organization.setParentId(0);
            organization.setOrgLevel(0);

            //创建子组织（部门 | 用户组）
        } else {
            AssertUtil.assertTrue(organization.getParentId() != null, ResultCode.PARAM_INVALID, "parentId is null");
            OrganizationVO parent = selectById(organization.getParentId());
            organization.setOrgLevel(parent.getOrgLevel() + 1);
            organization.setRootId(parent.getRootId());
        }

        organization = insert(organization);

        return organization;
    }

    @Override
    public void updateByIdSelective(OrganizationVO organization) {
        AssertUtil.assertNotNull(organization, ResultCode.PARAM_INVALID, "organization is null");
        AssertUtil.assertTrue(organization.getId() != null, ResultCode.PARAM_INVALID, "organization.id is null");

        //修改时间必须更新
        organization.setModifyTime(new Date());
        OrganizationDO data = OrganizationConverter.vo2DO(organization);
        int n = organizationDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Organization失败，影响行数:" + n);
    }

    @Override
    public List<OrganizationVO> queryOrganizationList(OrganizationQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        OrganizationExample example = OrganizationConverter.query2Example(query);

        List<OrganizationDO> list = organizationDAO.selectByExample(example);
        return OrganizationConverter.doList2VOList(list);
    }

    @Override
    public Long queryOrganizationCount(OrganizationQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        OrganizationExample example = OrganizationConverter.query2Example(query);
        long c = organizationDAO.countByExample(example);
        return c;
    }

    @Override
    public List<OrganizationVO> queryDistributorOrganizationTrees() {
        OrganizationQuery query = new OrganizationQuery();
        query.setRoot(true);
        query.setOrgType(OrganizationTypeEnum.CORP.getCode());
        query.setTags("DISTRIBUTOR");
        List<OrganizationVO> list = queryOrganizationList(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<OrganizationVO> ret = new ArrayList<>();
        for (OrganizationVO item : list) {
            OrganizationVO vo = getCorpOrganizationTreeByCorpId(item.getRootId());
            if (vo != null) {
                ret.add(vo);
            }
        }
        return ret;
    }

    @Override
    public List<UserVO> queryUsersByOrgIdAndUserRole(Integer orgId, RoleTypeEnum roleType, CustomRoleEnum customRole) {
        AssertUtil.assertNotNull(orgId, ResultCode.PARAM_INVALID, "orgId is null");
        List<Integer> userIdsOfSubOrgIdsByOrgId = getUserIdsOfSubOrgIdsByOrgId(orgId);
        if (CollectionUtils.isEmpty(userIdsOfSubOrgIdsByOrgId)) {
            return new ArrayList<>();
        }
        UserQuery userQuery = new UserQuery();
        userQuery.setIds(userIdsOfSubOrgIdsByOrgId);
        userQuery.setRoleTypeList(Collections.singletonList(roleType.getCode()));
        userQuery.setCustomRoleList(Collections.singletonList(customRole.getCode()));
        return userService.queryUsers(userQuery);
    }

    @Override
    public List<Integer> queryParentOrgIdPath(Integer orgId) {
        //根据当前orgId，查询从根结点orgId开始到当前orgId的路径列表，以root开始，以orgId结束
        LinkedList<Integer> path = new LinkedList<>();
        while (orgId != null && orgId > 0) {
            OrganizationVO organization = selectById(orgId);
            if (organization == null) {
                log.error("queryParentOrgIdPath: orgId is invalid,orgId:{}" ,orgId);
                return new ArrayList<>();
            }
            path.push(orgId);
            orgId = organization.getParentId();
        }
        return new ArrayList<>(path);
    }

    /**
     * 带条件分页查询组织
     */
    @Override
    public PageInfo<OrganizationVO> queryOrganizationByPage(OrganizationQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<OrganizationVO> page = new PageInfo<>();

        OrganizationExample example = OrganizationConverter.query2Example(query);
        long totalCount = organizationDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<OrganizationDO> list = organizationDAO.selectByExample(example);
        page.setList(OrganizationConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 根据操作员id查询组织对象
     *
     * @param operatorUserId
     * @return
     */
    @Override
    public OrganizationVO getCorpOrganizationTreeByUid(Integer operatorUserId) {
        AssertUtil.assertNotNull(operatorUserId, ResultCode.PARAM_INVALID, "operatorUserId is null");
        OrganizationDO org = organizationDAO.selectOrganizationByUserId(operatorUserId);
        if (org != null) {
            return getCorpOrganizationTreeByCorpId(org.getRootId());
        }
        return null;
    }

    /**
     * 根据操作员id查询部门组织对象
     *
     * @param operatorUserId
     * @return
     */
    @Override
    public OrganizationVO queryOrganizationByUserId(Integer operatorUserId) {
        AssertUtil.assertNotNull(operatorUserId, ResultCode.PARAM_INVALID, "operatorUserId is null");
        OrganizationDO org = organizationDAO.selectOrganizationByUserId(operatorUserId);
        if (org == null) {
            return null;
        }
        return OrganizationConverter.do2VO(org);
    }

    @Override
    public OrganizationVO getCorpOrganizationTreeByCorpId(Integer corpId) {
        List<OrganizationDO> organizations = organizationDAO.selectByRootId(corpId);
        if (organizations.isEmpty()) {
            return null;
        }

        List<OrganizationVO> list = OrganizationConverter.doList2VOList(organizations);

        Map<Integer, OrganizationVO> organizationMap = new HashMap<>();
        OrganizationVO rootOrganization = null;

        for (OrganizationVO org : list) {
            organizationMap.put(org.getId(), org);
            if (org.getRoot() && org.getRootId().equals(corpId)) {
                rootOrganization = org;
            }
        }

        for (OrganizationVO org : list) {
            if (org.getParentId() != null && organizationMap.containsKey(org.getParentId())) {
                OrganizationVO parent = organizationMap.get(org.getParentId());
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(org);
            }
        }

        return rootOrganization;
    }

    /**
     * 获取以指定组织id查询子组织树，返回企业树的子树，orgId为根结点（不一定是整个企业树的根）
     *
     * @param orgId
     * @return
     */
    @Override
    public OrganizationVO getSubOrganizationTree(Integer orgId) {
        OrganizationVO org = selectById(orgId);
        if (org == null) {
            return null;
        }
        OrganizationVO corpRootOrg = this.getCorpOrganizationTreeByCorpId(org.getRootId());
        return getSubTree(corpRootOrg, orgId);
    }

    /**
     * 获取指定用户id查询所在部门组织树，返回企业树的子树，orgId为根结点（不一定是整个企业树的根）
     *
     * @param operatorUserId
     * @return
     */
    @Override
    public OrganizationVO getSubOrganizationTreeByUserId(Integer operatorUserId) {
        OrganizationVO org = this.queryOrganizationByUserId(operatorUserId);
        if (org != null) {
            return getSubOrganizationTree(org.getId());
        }
        return null;
    }

    /**
     * 获取指定部门的所有子组织id（不包括orgId)
     *
     * @param orgId
     * @return
     */
    @Override
    public List<Integer> getSubOrgIds(Integer orgId, boolean includeRoot) {
        OrganizationVO org = getSubOrganizationTree(orgId);
        AssertUtil.assertNotNull(org, ResultCode.PARAM_INVALID, "orgId is invalid");

        List<Integer> list = new ArrayList<>();
        walk(org, list, includeRoot);

        return list;
    }

    /**
     * 获取指定部门id查询所在部门组织树，返回企业树的子树，orgId为根结点
     *
     * @param orgId
     * @return
     */
    @Override
    public List<Integer> getUserIdsOfSubOrgIdsByOrgId(Integer orgId) {
        AssertUtil.assertNotNull(orgId, ResultCode.PARAM_INVALID, "orgId is null");

        List<Integer> subOrgIds = this.getSubOrgIds(orgId, true);
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(subOrgIds), ResultCode.PARAM_INVALID, "查询下属组织列表失败");

        return userOrganizationService.findAllByOrgIds(subOrgIds).stream().map(UserOrganizationVO::getUserId).distinct()
                .collect(Collectors.toList());
    }

    /**
     * 从指定部门id查询指定子部门id是否是该部门的子部门（树结构上的子树）
     *
     * @param orgId
     * @param subOrgId
     * @return
     */
    @Override
    public boolean checkIfSubOrg(Integer orgId, Integer subOrgId) {
        List<Integer> subIds = this.getSubOrgIds(orgId, false);
        return CollectionUtils.isNotEmpty(subIds) && subIds.contains(subOrgId);
    }

    private void walk(OrganizationVO root, List<Integer> list, boolean includeRoot) {
        if (includeRoot) {
            list.add(root.getId());
        }
        if (CollectionUtils.isEmpty(root.getChildren())) {
            return;
        }
        for (OrganizationVO child : root.getChildren()) {
            list.add(child.getId());
            walk(child, list, includeRoot);
        }
    }

    /**
     * 递归遍历组织树，查找指定 orgId 的子树
     *
     * @param root  根节点
     * @param orgId 目标组织 ID
     * @return 目标组织子树，如果未找到返回 null
     */
    private OrganizationVO findSubTree(OrganizationVO root, int orgId) {
        // 检查当前节点是否为目标节点
        if (root.getId() == orgId) {
            return root;
        }

        // 递归遍历子节点
        if (root.getChildren() != null) {
            for (OrganizationVO child : root.getChildren()) {
                OrganizationVO result = findSubTree(child, orgId);
                if (result != null) {
                    return result;
                }
            }
        }

        // 如果未找到目标节点，返回 null
        return null;
    }

    /**
     * 获取指定 orgId 对应的子树
     *
     * @param root  根节点
     * @param orgId 目标组织 ID
     * @return 目标组织子树，如果未找到返回 null
     */
    private OrganizationVO getSubTree(OrganizationVO root, int orgId) {
        if (root == null) {
            return null;
        }
        return findSubTree(root, orgId);
    }

    @Override
    public void onDeleteUser(UserVO user) {
        userOrganizationService.deleteByUserId(user.getId());
    }

    @Override
    public QiChaChaModelPageVO qiChaChaFuzzyQuery(String corpName, Integer pageIndex) {
        // 校验参数
        AssertUtil.assertNotNull(corpName, ResultCode.PARAM_INVALID, "corpName is null");

        // 调用接口
        return qiChaChaService.fuzzySearch(corpName, pageIndex);
    }

    @Override
    public void updateCorpAuthInfo(CorpAuthVO corpAuthVO) {
        // 1、数据提取
        String creditCode = corpAuthVO.getStringFromExtInfo("creditCode");

        //  2、获取省份信息，完善扩展信息
        AdministrativeDivisionCodeEnum administrativeDivisionCodeEnum
                = AdministrativeDivisionCodeEnum.getEnumByCreditCode(creditCode);
        if (administrativeDivisionCodeEnum != null) {
            // 添加省份信息
            corpAuthVO.addExtInfo("provinceInfo", administrativeDivisionCodeEnum.getDesc());
        }

        // 3、更新数据
        OrganizationVO organizationVO = new OrganizationVO();
        organizationVO.setId(corpAuthVO.getId());
        organizationVO.setExtInfo(corpAuthVO.getExtInfo());
        updateByIdSelective(organizationVO);

        // 发送钉钉通知(构建请求入参)
        UserVO userVO = userService.selectById(corpAuthVO.getUserId());
        String message = DingTalkBuildUtils.buildCorpAuthTextMessage(userVO, corpAuthVO, null);
        DingTalkNoticeHelper.sendMsg2UserRegister(message);
    }

    @Override
    public List<OrganizationVO> queryDistributorCorps() {
        return CommonUtil.listConverter(
                organizationDAO.selectDistributorCorps(null, null),
                OrganizationConverter::do2VO);
    }

    @Override
    public PageInfo<OrganizationVO> queryDistributorCorpByPage(OrganizationQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        query.setOrgType("CORP");
        query.setTags("DISTRIBUTOR");
        PageInfo<OrganizationVO> page = new PageInfo<>();

        OrganizationExample example = OrganizationConverter.query2Example(query);
        long totalCount = organizationDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }
        int limit = (query.getPageNum() - 1) * query.getPageSize();
        int offset = query.getPageSize();

        List<OrganizationDO> list = organizationDAO.selectDistributorCorps(limit, offset);
        page.setList(OrganizationConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<OrganizationVO> querySubOrg(Integer orgId) {
        OrganizationQuery query = new OrganizationQuery();
        query.setRoot(false);
        query.setParentId(orgId);
        return queryOrganizationList(query);
    }

    @Override
    public SalesTypeEnum getRootSalesType(Integer orgId) {
        OrganizationQuery query = new OrganizationQuery();
        query.setId(orgId);
        OrganizationVO orgVO = this.selectById(orgId);
        AssertUtil.assertNotNull(orgVO, ResultCode.PARAM_INVALID, "orgId is invalid");
        // 获取根组织
        if (!orgVO.getRoot()) {
            orgVO = this.selectById(orgVO.getRootId());
        }
        return orgVO.getSalesType();
    }

    @Override
    public SalesTypeEnum getSalesType(Integer orgId) {
        OrganizationQuery query = new OrganizationQuery();
        query.setId(orgId);
        OrganizationVO orgVO = this.selectById(orgId);
        AssertUtil.assertNotNull(orgVO, ResultCode.PARAM_INVALID, "orgId is invalid");
        return orgVO.getSalesType();
    }


}
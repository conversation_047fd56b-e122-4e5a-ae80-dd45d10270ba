package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import ai.conrain.aigc.platform.service.model.query.ImageCaseSyncRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;
import ai.conrain.aigc.platform.service.model.converter.ImageCaseSyncRecordConverter;
import ai.conrain.aigc.platform.dal.dao.ImageCaseSyncRecordDAO;
import ai.conrain.aigc.platform.service.component.ImageCaseSyncRecordService;

/**   
 * ImageCaseSyncRecordService实现
 *
 * <AUTHOR>
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
@Slf4j
@Service
public class ImageCaseSyncRecordServiceImpl implements ImageCaseSyncRecordService {

	/** DAO */
	@Autowired
	private ImageCaseSyncRecordDAO imageCaseSyncRecordDAO;

	@Override
	public ImageCaseSyncRecordVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageCaseSyncRecordDO data = imageCaseSyncRecordDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return ImageCaseSyncRecordConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageCaseSyncRecordDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCaseSyncRecord失败");
	}

	@Override
	public ImageCaseSyncRecordVO insert(ImageCaseSyncRecordVO imageCaseSyncRecord) {
		AssertUtil.assertNotNull(imageCaseSyncRecord, ResultCode.PARAM_INVALID, "imageCaseSyncRecord is null");
		AssertUtil.assertTrue(imageCaseSyncRecord.getId() == null, ResultCode.PARAM_INVALID, "imageCaseSyncRecord.id is present");


		ImageCaseSyncRecordDO data = ImageCaseSyncRecordConverter.vo2DO(imageCaseSyncRecord);
		Integer n = imageCaseSyncRecordDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCaseSyncRecord失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageCaseSyncRecord返回id为空");
		imageCaseSyncRecord.setId(data.getId());
		return imageCaseSyncRecord;
	}

	@Override
	public void updateById(ImageCaseSyncRecordVO imageCaseSyncRecord) {
		AssertUtil.assertNotNull(imageCaseSyncRecord, ResultCode.PARAM_INVALID, "imageCaseSyncRecord is null");
		AssertUtil.assertTrue(imageCaseSyncRecord.getId() != null, ResultCode.PARAM_INVALID, "imageCaseSyncRecord.id is null");


		ImageCaseSyncRecordDO data = ImageCaseSyncRecordConverter.vo2DO(imageCaseSyncRecord);
		int n = imageCaseSyncRecordDAO.updateByPrimaryKey(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCaseSyncRecord失败，影响行数:" + n);
	}

	@Override
	public List<ImageCaseSyncRecordVO> findAll() {
		List<ImageCaseSyncRecordDO> list = imageCaseSyncRecordDAO.selectAll();
		return ImageCaseSyncRecordConverter.doList2VOList(list);
	}
}
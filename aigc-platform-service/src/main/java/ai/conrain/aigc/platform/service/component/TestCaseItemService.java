package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestCaseItemQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseItemVO;
import java.util.List;

/**
 * 测试用例项 Service定义
 *
 * <AUTHOR>
 * @version TestCaseItemService.java v 0.1 2025-08-12 07:18:08
 */
public interface TestCaseItemService {

    /**
     * 查询测试用例项对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestCaseItemVO selectById(Integer id);

    /**
     * 删除测试用例项对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加测试用例项对象
     *
     * @param testCaseItem 对象参数
     * @return 返回结果
     */
    TestCaseItemVO insert(TestCaseItemVO testCaseItem);

    /**
     * 修改测试用例项对象
     *
     * @param testCaseItem 对象参数
     */
    void updateByIdSelective(TestCaseItemVO testCaseItem);

    /**
     * 带条件批量查询测试用例项列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestCaseItemVO> queryTestCaseItemList(TestCaseItemQuery query);

    /**
     * 带条件查询测试用例项数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestCaseItemCount(TestCaseItemQuery query);

    /**
     * 带条件分页查询测试用例项
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestCaseItemVO> queryTestCaseItemByPage(TestCaseItemQuery query);

    /**
     * 批量插入测试用例项
     *
     * @param items 批量插入数据
     */
    void batchInsert(List<TestCaseItemVO> items);

    /**
     * 通过Excel批量初始化测试用例项
     *
     * @param caseId   用例ID
     * @param caseType 用例类型
     * @param lines    Excel数据
     */
    void batchInitByExcel(Integer caseId, TestCaseTypeEnum caseType, List<List<String>> lines);

    /**
     * 通过用例ID查询测试用例项
     *
     * @param id 用例ID
     * @return 测试用例项列表
     */
    List<TestCaseItemVO> queryByCaseId(Integer id);
}
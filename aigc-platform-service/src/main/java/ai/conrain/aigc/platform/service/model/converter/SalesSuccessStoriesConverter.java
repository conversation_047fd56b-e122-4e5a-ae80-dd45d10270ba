package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.dal.entity.SalesSuccessStoriesDO;
import ai.conrain.aigc.platform.dal.example.SalesSuccessStoriesExample;
import ai.conrain.aigc.platform.service.model.query.SalesSuccessStoriesQuery;
import ai.conrain.aigc.platform.service.model.vo.SalesSuccessStoriesVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * SalesSuccessStoriesConverter
 *
 * @version SalesSuccessStoriesService.java v 0.1 2025-06-26 05:49:50
 */
public class SalesSuccessStoriesConverter {

    /**
     * DO -> VO
     */
    public static SalesSuccessStoriesVO do2VO(SalesSuccessStoriesDO from) {
        SalesSuccessStoriesVO to = new SalesSuccessStoriesVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setTopped(from.getTopped());
        to.setCustomerId(from.getCustomerId());
        to.setCustomerName(from.getCustomerName());
        to.setBatchId(from.getBatchId());
        to.setModelId(from.getModelId());
        to.setModelName(from.getModelName());
        to.setModelUrl(from.getModelUrl());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getImageUrls())) {
            to.setImageUrls(JSONArray.parseArray(from.getImageUrls()).toJavaList(String.class));
        }
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * VO -> DO
     */
    public static SalesSuccessStoriesDO vo2DO(SalesSuccessStoriesVO from) {
        SalesSuccessStoriesDO to = new SalesSuccessStoriesDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setTopped(from.getTopped());
        to.setCustomerId(from.getCustomerId());
        to.setCustomerName(from.getCustomerName());
        to.setBatchId(from.getBatchId());
        to.setModelId(from.getModelId());
        to.setModelName(from.getModelName());
        to.setModelUrl(from.getModelUrl());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (CollectionUtils.isNotEmpty(from.getImageUrls())) {
            to.setImageUrls(JSONArray.toJSON(from.getImageUrls()).toString());
        }
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> Example
     */
    public static SalesSuccessStoriesExample query2Example(SalesSuccessStoriesQuery from) {
        SalesSuccessStoriesExample to = new SalesSuccessStoriesExample();
        SalesSuccessStoriesExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameLike(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getTopped())) {
            c.andToppedEqualTo(from.getTopped());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerId())) {
            c.andCustomerIdEqualTo(from.getCustomerId());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerName())) {
            c.andCustomerNameEqualTo(from.getCustomerName());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getModelId())) {
            c.andModelIdEqualTo(from.getModelId());
        }
        if (!ObjectUtils.isEmpty(from.getModelUrl())) {
            c.andModelUrlEqualTo(from.getModelUrl());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (SalesSuccessStoriesExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<SalesSuccessStoriesVO> doList2VOList(List<SalesSuccessStoriesDO> list) {
        return CommonUtil.listConverter(list, SalesSuccessStoriesConverter::do2VO);
    }
}
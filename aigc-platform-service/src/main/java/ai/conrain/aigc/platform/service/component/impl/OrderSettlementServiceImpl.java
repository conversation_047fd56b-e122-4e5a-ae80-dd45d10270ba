package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;

import ai.conrain.aigc.platform.dal.dao.OrderInfoDAO;
import ai.conrain.aigc.platform.dal.dao.OrderSettlementDAO;
import ai.conrain.aigc.platform.dal.entity.OrderInfoDO;
import ai.conrain.aigc.platform.dal.example.OrderInfoExample;
import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.OrderSettlementDO;
import ai.conrain.aigc.platform.dal.example.OrderSettlementExample;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.biz.SettleConfigModel;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.converter.OrderInfoConverter;
import ai.conrain.aigc.platform.service.model.converter.OrderSettlementConverter;
import ai.conrain.aigc.platform.service.model.query.OrderSettlementQuery;
import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import com.alibaba.fastjson.JSONArray;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**   
 * OrderSettlementService实现
 *
 * <AUTHOR>
 * @version OrderSettlementService.java v 0.1 2025-05-22 03:41:06
 */
@Slf4j
@Service
public class OrderSettlementServiceImpl implements OrderSettlementService {

	/** DAO */
	@Autowired
	private OrderSettlementDAO orderSettlementDAO;

	@Autowired
	private OrderInfoDAO orderInfoDAO;

	@Autowired
	private UserService userService;

	@Autowired
	private OrderInfoService orderInfoService;

	@Autowired
	private DistributorService distributorService;

    @Autowired
    private PrincipalInfoService principalInfoService;

	@Override
	public OrderSettlementVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		OrderSettlementDO data = orderSettlementDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return OrderSettlementConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = orderSettlementDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除OrderSettlement失败");
	}

	@Override
	public OrderSettlementVO insert(OrderSettlementVO orderSettlement) {
		AssertUtil.assertNotNull(orderSettlement, ResultCode.PARAM_INVALID, "orderSettlement is null");
		AssertUtil.assertTrue(orderSettlement.getId() == null, ResultCode.PARAM_INVALID, "orderSettlement.id is present");

		//创建时间、修改时间兜底
		if (orderSettlement.getCreateTime() == null) {
			orderSettlement.setCreateTime(new Date());
		}

		if (orderSettlement.getModifyTime() == null) {
			orderSettlement.setModifyTime(new Date());
		}

		OrderSettlementDO data = OrderSettlementConverter.vo2DO(orderSettlement);
		Integer n = orderSettlementDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建OrderSettlement失败");
		AssertUtil.assertNotNull(data.getId(), "新建OrderSettlement返回id为空");
		orderSettlement.setId(data.getId());
		return orderSettlement;
	}


	@Override
	public void updateByIdSelective(OrderSettlementVO orderSettlement) {
		AssertUtil.assertNotNull(orderSettlement, ResultCode.PARAM_INVALID, "orderSettlement is null");
    	AssertUtil.assertTrue(orderSettlement.getId() != null, ResultCode.PARAM_INVALID, "orderSettlement.id is null");

		//修改时间必须更新
		orderSettlement.setModifyTime(new Date());
		OrderSettlementDO data = OrderSettlementConverter.vo2DO(orderSettlement);
		int n = orderSettlementDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新OrderSettlement失败，影响行数:" + n);
	}

	@Override
	public List<OrderSettlementVO> queryOrderSettlementList(OrderSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		OrderSettlementExample example = OrderSettlementConverter.query2Example(query);

		List<OrderSettlementDO> list = orderSettlementDAO.selectByExample(example);
			return OrderSettlementConverter.doList2VOList(list);
	}

	@Override
	public Long queryOrderSettlementCount(OrderSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		OrderSettlementExample example = OrderSettlementConverter.query2Example(query);
		long c = orderSettlementDAO.countByExample(example);
		return c;
	}

	@Override
	public void aggregateCommissionSettlement4Distributor(Date startDate, Date endDate,
														  DistributorSettlementVO data, DistributorSettleConfigVO settleConfigVO) {
		// 1. 获取 当前 销售/渠道 关联的 商家
		List<Integer> merchants = distributorService.getPrincipalRelatedMerchants(PrincipalModel.of(data));
		if (merchants.isEmpty()) {
			return;
		}

		// 2. 查询对应的订单
		OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
		orderInfoQuery.setDateFrom(DateUtils.formatShort(startDate));
		orderInfoQuery.setDateTo(DateUtils.formatShort(endDate));
		orderInfoQuery.setMasterUserIds(merchants);
		List<OrderInfoVO> orderInfoList = orderInfoService.queryOrderInfoList(orderInfoQuery);

		BigDecimal totalAmount = BigDecimalUtils.newZero();
		BigDecimal settleAmount = BigDecimalUtils.newZero();
		int orderNum = 0;
		SettleConfigModel configModel = settleConfigVO.getSettleConfig();

		// 3. 遍历每个订单
		for (OrderInfoVO orderInfo : orderInfoList) {
			OrderSettlementQuery settlementQuery = new OrderSettlementQuery();
			settlementQuery.setOrderNo(orderInfo.getOrderNo());
			settlementQuery.setType(OrderSettlementTypeEnum.COMMISSION.getCode());
			List<OrderSettlementVO> orderSettlementList = this.queryOrderSettlementList(settlementQuery);
			// 如果订单结算单已经存在, 判断是否已经完成, 如果已经完成, 跳过, 如果没有完成, 删除重新结算
			if (!CollectionUtils.isEmpty(orderSettlementList)) {
				AssertUtil.assertTrue(orderSettlementList.size() == 1, ResultCode.BIZ_FAIL, "订单结算单数量异常");
				OrderSettlementVO oldOrderSettlement = orderSettlementList.get(0);
				// 如果这笔订单已经完成结算, 跳过
				if (OrderSettleStatusEnum.SUCCESS.equals(oldOrderSettlement.getStatus())) {
					continue;
				}
				this.deleteById(oldOrderSettlement.getId());
			}
			// 创建新的结算单
			boolean isNewOrder = orderInfoService.isNew(orderInfo);
			BigDecimal channelRate = isNewOrder ? configModel.getNewOrderCommRate() : configModel.getRenewOrderCommRate();
			OrderSettlementVO newOrderSettlement = new OrderSettlementVO();
			newOrderSettlement.setOrderNo(orderInfo.getOrderNo());
			newOrderSettlement.setType(OrderSettlementTypeEnum.COMMISSION);
			newOrderSettlement.setDistributorCorpId(data.getDistributorCorpId());
			newOrderSettlement.setDistributorCorpName(data.getDistributorCorpName());
			newOrderSettlement.setPrincipalType(data.getPrincipalType());
			newOrderSettlement.setPrincipalId(data.getPrincipalId());
			newOrderSettlement.setStatus(OrderSettleStatusEnum.SETTLING);
			newOrderSettlement.setSettleId(data.getSettleId());
			newOrderSettlement.setTotalAmount(orderInfo.getPayAmount());
			newOrderSettlement.setChannelRate(channelRate);
			newOrderSettlement.setSettleAmount(BigDecimalUtils.multiply(orderInfo.getPayAmount(), channelRate));
			newOrderSettlement.putExtInfo(KEY_IS_RENEW_ORDER, !isNewOrder);
			newOrderSettlement.putExtInfo(KEY_ORDER_SETTLE_CONFIG, configModel);
			// 聚合结算金额
			totalAmount = BigDecimalUtils.add(totalAmount, orderInfo.getPayAmount());
			settleAmount = BigDecimalUtils.add(settleAmount, newOrderSettlement.getSettleAmount());
			orderNum++;
			// 插入结算单
			this.insert(newOrderSettlement);
		}
		data.putExtInfo(OrderSettlementTypeEnum.COMMISSION.getCode(), totalAmount);
		data.addTotalAmount(totalAmount);
		data.setSettleAmount(settleAmount);
		data.addOrderNum(orderNum);
	}
	@Override
	public void aggregateCommissionRelateSettlement(PrincipalModel principal, Date startDate, Date endDate, DistributorSettlementVO data) {
		// 1. 获取 需要被当前的销售 抽成的 销售
		List<PrincipalInfoVO> commissionPrincipalList = principalInfoService.queryRelatedCommissionPrincipal(principal);
		if (CollectionUtils.isEmpty(commissionPrincipalList)) {
			return;
		}
		BigDecimal totalAmount = BigDecimalUtils.newZero();
		BigDecimal percentageAmount = BigDecimalUtils.newZero();
		int orderNum = 0;

		for (PrincipalInfoVO commissionPrincipal : commissionPrincipalList) {
			SettleConfigModel configModel = JSONObject.toJavaObject(commissionPrincipal.getInfoValue(), SettleConfigModel.class);
			List<Integer> merchantIds = distributorService.getPrincipalRelatedMerchants(PrincipalModel.of(commissionPrincipal));
			if (merchantIds.isEmpty()) {
				return;
			}
			OrderInfoQuery orderInfoQuery = new OrderInfoQuery();
			orderInfoQuery.setDateFrom(DateUtils.formatShort(startDate));
			orderInfoQuery.setDateTo(DateUtils.formatShort(endDate));
			orderInfoQuery.setMasterUserIds(merchantIds);
			List<OrderInfoVO> orderInfoList = orderInfoService.queryOrderInfoList(orderInfoQuery);

			// 3. 遍历每个订单
			for (OrderInfoVO orderInfo : orderInfoList) {
				OrderSettlementQuery settlementQuery = new OrderSettlementQuery();
				settlementQuery.setOrderNo(orderInfo.getOrderNo());
				settlementQuery.setType(OrderSettlementTypeEnum.COMMISSION_RELATED.getCode());
				List<OrderSettlementVO> orderSettlementList = this.queryOrderSettlementList(settlementQuery);
				// 如果订单结算单已经存在, 判断是否已经完成, 如果已经完成, 跳过, 如果没有完成, 删除重新结算
				if (!CollectionUtils.isEmpty(orderSettlementList)) {
					AssertUtil.assertTrue(orderSettlementList.size() == 1, ResultCode.BIZ_FAIL, "订单结算单数量异常");
					OrderSettlementVO oldOrderSettlement = orderSettlementList.get(0);
					// 如果这笔订单已经完成结算, 跳过
					if (OrderSettleStatusEnum.SUCCESS.equals(oldOrderSettlement.getStatus())) {
						continue;
					}
					this.deleteById(oldOrderSettlement.getId());
				}
				// 创建新的结算单
				// 当前被抽成主体是否在签约首年
				boolean isInitYear = principalInfoService.isInitYear(principal);
				BigDecimal channelRate = isInitYear ? configModel.getInitYrRate() : configModel.getSubseqYrsRate();
				OrderSettlementVO newOrderSettlement = new OrderSettlementVO();
				newOrderSettlement.setOrderNo(orderInfo.getOrderNo());
				newOrderSettlement.setType(OrderSettlementTypeEnum.COMMISSION_RELATED);
				newOrderSettlement.setDistributorCorpId(data.getDistributorCorpId());
				newOrderSettlement.setDistributorCorpName(data.getDistributorCorpName());
				newOrderSettlement.setPrincipalType(data.getPrincipalType());
				newOrderSettlement.setPrincipalId(data.getPrincipalId());
				newOrderSettlement.setStatus(OrderSettleStatusEnum.SETTLING);
				newOrderSettlement.setSettleId(data.getSettleId());
				newOrderSettlement.setTotalAmount(orderInfo.getPayAmount());
				newOrderSettlement.setChannelRate(channelRate);
				newOrderSettlement.setSettleAmount(BigDecimalUtils.multiply(orderInfo.getPayAmount(), channelRate));
				newOrderSettlement.putExtInfo(KEY_IS_INIT_YEAR, isInitYear);
				newOrderSettlement.putExtInfo(KEY_ORDER_SETTLE_CONFIG, configModel);
				// 聚合结算金额
				totalAmount = BigDecimalUtils.add(totalAmount, orderInfo.getPayAmount());
				percentageAmount = BigDecimalUtils.add(percentageAmount, newOrderSettlement.getSettleAmount());
				orderNum++;
				// 插入结算单
				this.insert(newOrderSettlement);
			}
		}
		data.putExtInfo(OrderSettlementTypeEnum.COMMISSION_RELATED.getCode(), totalAmount);
		data.addTotalAmount(totalAmount);
		data.addSettleAmount(percentageAmount);
		data.addOrderNum(orderNum);
	}

	@Override
	public void initSettlement(String orderNo, int distributorCorpId, String distributorCorpName, boolean renewedOrder) throws BizException {
		OrderSettlementDO settlement = new OrderSettlementDO();
		settlement.setOrderNo(orderNo);
		settlement.setStatus(OrderSettleStatusEnum.SETTLING.getCode());
		settlement.setDistributorCorpId(distributorCorpId);
		settlement.setDistributorCorpName(distributorCorpName);

		//暂写死按A级来计算新签和续签
		BigDecimal rate = renewedOrder ? new BigDecimal("0.30") : new BigDecimal("0.50");
		settlement.setChannelRate(rate);

		JSONObject ext = new JSONObject();
		ext.put("level", "A");
		ext.put("renewedOrder", renewedOrder);
		settlement.setExtInfo(ext.toJSONString());

		int cnt = orderSettlementDAO.insertSelective(settlement);

		if (cnt < 1) {
			log.error("插入订单结算表异常，未插入成功");
			throw new BizException("创建订单表异常");
		}

		log.info("插入订单结算表成功,orderNo={}", orderNo);
	}

	//获取用户首单超过3999及以上的订单
	private OrderInfoDO getFirstOrder(Integer userId, BigDecimal notLessThanAmount) {
		AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
		AssertUtil.assertNotNull(notLessThanAmount, ResultCode.PARAM_INVALID, "notLessThanAmount is null");

		OrderInfoExample exam = new OrderInfoExample();
		exam.createCriteria().andMasterUserIdEqualTo(userId).andOriginalAmountGreaterThanOrEqualTo(notLessThanAmount).andLogicalDeleted(false);
		exam.setOrderByClause("id asc");
		exam.page(1,1);

		List<OrderInfoDO> list = orderInfoDAO.selectByExample(exam);
		if (CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}

		return null;
	}

/*
	@Override
	public void closeSettlement(String orderNo) throws BizException {
		OrderSettlementQuery query = new OrderSettlementQuery();
		query.setOrderNo(orderNo);

		OrderSettlementDO target = new OrderSettlementDO();
		target.setStatus(OrderSettleStatusEnum.TRADE_CLOSED.getCode());
		int cnt = orderSettlementDAO.updateByExampleSelective(target, OrderSettlementConverter.query2Example(query));

		if (cnt < 1) {
			log.error("关闭订单结算异常,orderNo={},变更数量为{}", orderNo, cnt);
			throw new BizException(ResultCode.SYS_ERROR);
		}
	}
*/

	@Override
	public void startSettlement(OrderInfoVO order) throws BizException {
		String orderNo = order.getOrderNo();

		OrderSettlementQuery query = new OrderSettlementQuery();
		query.setOrderNo(orderNo);
		List<OrderSettlementDO> list = orderSettlementDAO.selectByExample(
				OrderSettlementConverter.query2Example(query));

		if (CollectionUtils.size(list) != 1) {
			log.error("开始订单结算异常，订单对应结算数据为空或大于1条，orderNo={},size={}", orderNo,
					CollectionUtils.size(list));
			throw new BizException(ResultCode.SYS_ERROR);
		}

		OrderSettlementDO original = list.get(0);

		OrderSettlementDO target = new OrderSettlementDO();
		target.setId(original.getId());
		target.setStatus(OrderSettleStatusEnum.SETTLING.getCode());
		BigDecimal payAmount = order.getPayAmount();
		AssertUtil.assertNotNull(payAmount, ResultCode.BIZ_FAIL, "payAmount is null");
		AssertUtil.assertNotNull(original.getChannelRate(), ResultCode.BIZ_FAIL, "channelRate is null");

		target.setTotalAmount(payAmount);
		target.setSettleAmount(payAmount.multiply(original.getChannelRate()));

		int cnt = orderSettlementDAO.updateByPrimaryKeySelective(target);
		AssertUtil.assertTrue(cnt == 1, ResultCode.BIZ_FAIL, "更新订单结算记录失败");
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public void finishSettlement(String settleId) {
		//1.查询该结算单下所有的结算订单
		OrderSettlementQuery orderQuery = new OrderSettlementQuery();
		orderQuery.setSettleId(settleId);
		orderQuery.setStatus(OrderSettleStatusEnum.SETTLING.getCode());
		List<OrderSettlementDO> list = orderSettlementDAO.selectByExample(
				OrderSettlementConverter.query2Example(orderQuery));

		if (CollectionUtils.isEmpty(list)) {
			log.error("完结订单结算异常，settleId={}未查到任何订单结算信息", settleId);
			throw new BizException(ResultCode.SYS_ERROR);
		}

		//2.遍历更新订单数据状态
		for (OrderSettlementDO each : list) {
			OrderSettlementDO data = new OrderSettlementDO();
			data.setId(each.getId());
			data.setStatus(OrderSettleStatusEnum.SUCCESS.getCode());
			data.setSettleTime(new Date());

			int cnt = orderSettlementDAO.updateByPrimaryKeySelective(data);
			if (cnt < 1) {
				log.error("手动结算异常，id={}订单结算失败，未完成更新", data.getId());
				throw new BizException(ResultCode.SYS_ERROR);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public void batchInitSettlement4Distributor(String month) {
		AssertUtil.assertNotBlank(month, "month is blank");
		List<OrderInfoDO> orders = orderInfoDAO.queryOrdersByMonth(month);
		for (OrderInfoDO each : orders) {
			initSettlement4Distributor(OrderInfoConverter.do2VO(each));
		}
	}

	private void initSettlement4Distributor(OrderInfoVO order) {
		Integer merchantId = order.getMasterUserId();
		AssertUtil.assertNotNull(merchantId, "商户id不能为空");

		UserVO merchant = userService.selectById(merchantId);
		if (merchant == null) {
			log.error("商户不存在 merchantId:{}，忽略对应订单:{}", merchantId, order.getOrderNo());
			return;
		}

		if (RoleTypeEnum.MERCHANT.equals(merchant.getRoleType()) && merchant.getStatus() == UserStatusEnum.ENABLED) {
			DistributorCustomerVO customer = distributorService.queryDistributorInfoByMerchantId(merchantId);

			// 渠道商的客户
			if (customer != null) {
				log.info("初始化商家结算orderNo={}开始", order.getOrderNo());

				AssertUtil.assertNotNull(order.getOrderNo(), "订单号不能为空");
				AssertUtil.assertNotNull(customer.getDistributorCorpOrgId(), "渠道商实体id不能为空");

				OrderSettlementQuery orderSettlementQuery = new OrderSettlementQuery();
				orderSettlementQuery.setOrderNo(order.getOrderNo());

				List<OrderSettlementVO> list = this.queryOrderSettlementList(orderSettlementQuery);
				if (CollectionUtils.isNotEmpty(list)) {
					OrderSettlementVO orderSettlementVO = list.get(0);

					// 如果结算记录状态为结算成功，则跳过
					if (Objects.equals(orderSettlementVO.getStatus(), OrderSettleStatusEnum.SUCCESS)) {
						log.info("订单结算记录已存在且状态为结算成功状态，直接跳过，orderNo={},list={}", order.getOrderNo(), JSONArray.toJSONString(list));
						return;

						//只要不是完结状态，则先删除重新结算
					} else {
						log.info("订单结算记录已存在且状态不是结算成功状态，先删除，orderNo={},list={}", order.getOrderNo(), JSONArray.toJSONString(list));
						this.deleteById(orderSettlementVO.getId());
					}
				}

				//记录一笔待结算单据到渠道商实体
				AssertUtil.assertNotNull(order.getFinishTime(), ResultCode.BIZ_FAIL, "订单完成时间为空");

				OrderInfoDO firstOrder = getFirstOrder(merchantId, new BigDecimal("3999.00"));

				//是否为续签订单
				boolean renewedOrder = false;
				if (firstOrder != null) {
					log.info("商户id={},{}的首个充值订单是orderNo={},时间={}",merchant.getId(), merchant.getNickName(), firstOrder.getOrderNo(), DateUtils.formatTime(firstOrder.getFinishTime()));

					Date firstOrderTime = firstOrder.getFinishTime();
					AssertUtil.assertNotNull(firstOrderTime, ResultCode.BIZ_FAIL, "订单完成时间为空，历史脏数据");
					//如果当前订单的完成时间在firstOrderTime后180天以内，则为新订单，超过180天则为续签订单
					renewedOrder = order.getFinishTime().after(DateUtils.addDays(firstOrderTime, 180));
				} else {
					log.info("用户id={},{}没有充值3999及以上订单",merchant.getId(), merchant.getNickName());
				}

				this.initSettlement(order.getOrderNo(), customer.getDistributorCorpOrgId(), customer.getDistributorCorpName(), renewedOrder);
				this.startSettlement(order);

				log.info("初始化商家结算orderNo={}完成", order.getOrderNo());
			}
		}
	}

	/**
	 * 带条件分页查询订单结算表（流水）
	 */
	@Override
	public PageInfo<OrderSettlementVO> queryOrderSettlementByPage(OrderSettlementQuery query) {
		if (query == null || query.getPageNum() == null || query.getPageSize() == null || query.getPageNum() < 1
				|| query.getPageSize() < 1) {
			throw new BizException("翻页查询请求参数不合法");
		}

		PageInfo<OrderSettlementVO> page = new PageInfo<>();

		OrderSettlementExample example = OrderSettlementConverter.query2Example(query);
		long totalCount = orderSettlementDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<OrderSettlementDO> list = orderSettlementDAO.selectByExample(example);
		AssertUtil.assertNotNull(list, ResultCode.SYS_ERROR, "分页查询OrderSettlement失败，返回null");
		page.setList(OrderSettlementConverter.doList2VOList(list));
		page.setSize(list.size());
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		fillStatsInfo(page);

		return page;
	}

	/**
	 * 填充统计信息
	 *
	 * @param pageInfo 分页信息
	 */
	private void fillStatsInfo(PageInfo<OrderSettlementVO> pageInfo) {
		List<OrderSettlementVO> list = pageInfo.getList();
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		HashMap<String, Object> extInfo = new HashMap<>();

		BigDecimal totalOrderAmount = BigDecimalUtils.newZero();
		BigDecimal totalCommissionAmount = BigDecimalUtils.newZero();
		BigDecimal totalCommissionRelatedAmount = BigDecimalUtils.newZero();
		for (OrderSettlementVO each : list) {
			totalOrderAmount = BigDecimalUtils.add(totalOrderAmount, each.getTotalAmount());
			switch (each.getType()) {
				case COMMISSION:
					totalCommissionAmount = BigDecimalUtils.add(totalCommissionAmount, each.getSettleAmount());
					break;
				case COMMISSION_RELATED:
					totalCommissionRelatedAmount = BigDecimalUtils.add(totalCommissionRelatedAmount, each.getSettleAmount());
					break;
				default:
					break;
			}
		}

		extInfo.put("totalOrderAmount", CommonConstants.COMMA_DECIMAL_FORMAT.format(totalOrderAmount));
		extInfo.put("totalSettleAmount", CommonConstants.COMMA_DECIMAL_FORMAT.format(totalCommissionAmount));
		extInfo.put("totalCommissionRelatedAmount", CommonConstants.COMMA_DECIMAL_FORMAT.format(totalCommissionRelatedAmount));

		pageInfo.setExtInfo(extInfo);
	}

}
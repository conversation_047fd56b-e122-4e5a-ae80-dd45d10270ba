package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.StatsUserOperateDO;
import ai.conrain.aigc.platform.dal.example.StatsUserOperateExample;
import ai.conrain.aigc.platform.service.model.query.StatsUserOperateQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsUserOperateVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * StatsUserOperateConverter
 *
 * @version StatsUserOperateService.java v 0.1 2025-04-25 02:47:41
 */
public class StatsUserOperateConverter {

    /**
     * DO -> VO
     */
    public static StatsUserOperateVO do2VO(StatsUserOperateDO from) {
        StatsUserOperateVO to = new StatsUserOperateVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setMaterialId(from.getMaterialId());
        to.setUserType(from.getUserType());
        to.setCreateCount(from.getCreateCount());
        to.setDownloadCount(from.getDownloadCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsUserOperateDO vo2DO(StatsUserOperateVO from) {
        StatsUserOperateDO to = new StatsUserOperateDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setMaterialId(from.getMaterialId());
        to.setUserType(from.getUserType());
        to.setCreateCount(from.getCreateCount());
        to.setDownloadCount(from.getDownloadCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        
        // 处理extInfo字段，确保不为null时才设置JSON字符串，为null时设置为null或空JSON字符串
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        } else {
            // 当extInfo为null时，设置为null（让数据库处理默认值）
            to.setExtInfo(null);
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsUserOperateQuery do2Query(StatsUserOperateDO from) {
        StatsUserOperateQuery to = new StatsUserOperateQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setMaterialId(from.getMaterialId());
        to.setUserType(from.getUserType());
        to.setCreateCount(from.getCreateCount());
        to.setDownloadCount(from.getDownloadCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsUserOperateDO query2DO(StatsUserOperateQuery from) {
        StatsUserOperateDO to = new StatsUserOperateDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setMaterialId(from.getMaterialId());
        to.setUserType(from.getUserType());
        to.setCreateCount(from.getCreateCount());
        to.setDownloadCount(from.getDownloadCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsUserOperateExample query2Example(StatsUserOperateQuery from) {
        StatsUserOperateExample to = new StatsUserOperateExample();
        StatsUserOperateExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getMaterialId())) {
            c.andMaterialIdEqualTo(from.getMaterialId());
        }
        if (!ObjectUtils.isEmpty(from.getUserType())) {
            c.andUserTypeEqualTo(from.getUserType());
        }
        if (!ObjectUtils.isEmpty(from.getCreateCount())) {
            c.andCreateCountEqualTo(from.getCreateCount());
        }
        if (!ObjectUtils.isEmpty(from.getDownloadCount())) {
            c.andDownloadCountEqualTo(from.getDownloadCount());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        // 用户 id 不为空则查询 id 为 userIdList 的数据
        if (!ObjectUtils.isEmpty(from.getUserIdList())){
            c.andUserIdIn(from.getUserIdList());
        }


        // 时间范围
        if (!ObjectUtils.isEmpty(from.getStartDate()) && !ObjectUtils.isEmpty(from.getEndDate())) {
            c.andStatsDateBetween(from.getStartDate(), from.getEndDate());
        }

        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<StatsUserOperateVO> doList2VOList(List<StatsUserOperateDO> list) {
        return CommonUtil.listConverter(list, StatsUserOperateConverter::do2VO);
    }
}
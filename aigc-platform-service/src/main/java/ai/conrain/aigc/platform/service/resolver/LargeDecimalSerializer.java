/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;
import java.math.BigDecimal;

import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.DECIMAL_FORMAT;

/**
 * 大额的BigDecimal序列化器
 *
 * <AUTHOR>
 * @version : LargeDecimalSerializer.java, v 0.1 2023/9/25 10:13 renxiao.wu Exp $
 */
public class LargeDecimalSerializer extends CommaDecimalSerializer {
    /** 万 */
    private static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");

    @Override
    public void serialize(BigDecimal bigDecimal, <PERSON>son<PERSON><PERSON><PERSON> jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
        if (null == bigDecimal) {
            return;
        }

        //小于1万和pc端都按原有格式化逻辑
        if (BigDecimalUtils.lessThan(bigDecimal.abs(), TEN_THOUSAND) || !OperationContextHolder.getContext()
            .isRequestFromMiniApp()) {
            super.serialize(bigDecimal, jsonGenerator, serializerProvider);
            return;
        }

        //否则，将将值转换为"多少万"
        BigDecimal inMillions = bigDecimal.divide(TEN_THOUSAND);

        // 格式化保留两位小数
        jsonGenerator.writeString(DECIMAL_FORMAT.format(inMillions) + "万");
    }
}

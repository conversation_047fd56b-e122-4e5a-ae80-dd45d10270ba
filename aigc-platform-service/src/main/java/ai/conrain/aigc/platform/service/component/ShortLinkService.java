package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ShortLinkQuery;
import ai.conrain.aigc.platform.service.model.vo.ShortLinkVO;

import java.util.List;

/**
 * 短链接  Service定义
 *
 * <AUTHOR>
 * @version ShortLinkService.java
 */
public interface ShortLinkService {

	/**
	 * 查询短链接 对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ShortLinkVO selectById(Integer id);

	/**
	 * 删除短链接 对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加短链接 对象
	 * @param shortLink 对象参数
	 * @return 返回结果
	 */
	ShortLinkVO insert(ShortLinkVO shortLink);

	/**
	 * 批量添加短链接
	 * @param shortLinks 短链接列表
	 * @return 返回结果
	 */
	List<ShortLinkVO> batchInsert(List<ShortLinkVO> shortLinks);

	/**
	 * 修改短链接 对象
	 * @param shortLink 对象参数
	 */
	void updateByIdSelective(ShortLinkVO shortLink);

	/**
	 * 带条件批量查询短链接 列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ShortLinkVO> queryShortLinkList(ShortLinkQuery query);

	/**
	 * 带条件查询短链接 数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryShortLinkCount(ShortLinkQuery query);

	/**
	 * 带条件分页查询短链接
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ShortLinkVO> queryShortLinkByPage(ShortLinkQuery query);

	/**
	 * 根据id 获取长链接
	 * @param id id
	 * @return 结果
	 */
	String shortId2Original(Integer id);

	/**
	 * 根据id 批量获取长链接
	 * @param ids ids
	 * @return 结果
	 */
	List<String> shortIdList2OriginalList(List<Integer> ids);

	List<String> shortIdList2OriginalList(List<Integer> ids, String orderBy);

	/**
	 * 根据id 获取短链接
	 * @param id id
	 * @return 结果
	 */
	String shortId2ShortUrl(Integer id);

	/**
	 * 根据id 批量获取短链接
	 * @param ids ids
	 * @return 结果
	 */
	List<String> shortIdList2ShortUrlList(List<Integer> ids);

	/**
	 * 根据id 批量获取短链接
	 * @param ids ids
	 * @return 结果
	 */
	List<String> shortIdList2ShortUrlList(List<Integer> ids, String orderBy);

	/**
	 * 根据长链接获取 id
	 * @param originalUrl 长链接
	 * @return 结果
	 */
	Integer original2ShortId(String originalUrl);

	/**
	 * 根据长链接批量 获取 id
	 * @param originalUrls 长链接
	 * @return 结果
	 */
	List<Integer> originalList2ShortIdList(List<String> originalUrls);

	/**
	 * 根据长链接 获取短链接
	 * @param originalUrl 原始链接
	 * @return 短链接
	 */
	String original2Short(String originalUrl);

	/**
	 * 根据长链接 批量获取短链接
	 * @param originalUrls 原始链接列表
	 * @return 短链接列表
	 */
	List<String> originalList2ShortList(List<String> originalUrls);

	/**
	 * 根据短链接 获取原始链接
	 * @param shortUrl 短链接
	 * @return 原始链接
	 */
	String short2Original(String shortUrl);

	/**
	 * 根据短链接 批量获取原始链接
	 * @param shortUrls 短链接列表
	 * @return 原始链接列表
	 */
	List<String> shortList2OriginalList(List<String> shortUrls);

	/**
	 * 获取原始链接
	 * @param shortCode 短链码
	 * @return 原始链接
	 */
	String getOriginalUrlByCode(String shortCode);
}
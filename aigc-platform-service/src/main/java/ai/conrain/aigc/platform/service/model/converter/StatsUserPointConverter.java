package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.StatsUserPointDO;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.query.StatsUserPointQuery;
import ai.conrain.aigc.platform.dal.example.StatsUserPointExample;
import ai.conrain.aigc.platform.service.model.vo.StatsUserPointVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * StatsUserPointConverter
 *
 * @version StatsUserPointService.java v 0.1 2025-04-11 10:09:06
 */
public class StatsUserPointConverter {

    /**
     * DO -> VO
     */
    public static StatsUserPointVO do2VO(StatsUserPointDO from) {
        StatsUserPointVO to = new StatsUserPointVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setNickName(from.getNickName());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setStatsType(StatsPeriodEnum.getByCode(from.getStatsType()));
        to.setStatsDate(from.getStatsDate());
        to.setPointConsumed(from.getPointConsumed());
        to.setGivePointConsumed(from.getGivePointConsumed());
        to.setExpPointConsumed(from.getExpPointConsumed());
        to.setModelPointConsumed(from.getModelPointConsumed());
        to.setRechargeAmount(from.getRechargeAmount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsUserPointDO vo2DO(StatsUserPointVO from) {
        StatsUserPointDO to = new StatsUserPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setNickName(from.getNickName());
        to.setDistributorCorpName(from.getDistributorCorpName());
        to.setStatsType(from.getStatsType().getCode());
        to.setStatsDate(from.getStatsDate());
        to.setPointConsumed(from.getPointConsumed());
        to.setGivePointConsumed(from.getGivePointConsumed());
        to.setExpPointConsumed(from.getExpPointConsumed());
        to.setModelPointConsumed(from.getModelPointConsumed());
        to.setRechargeAmount(from.getRechargeAmount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsUserPointQuery do2Query(StatsUserPointDO from) {
        StatsUserPointQuery to = new StatsUserPointQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setNickNameLike(from.getNickName());
        to.setDistributorCorpNameLike(from.getDistributorCorpName());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setPointConsumed(from.getPointConsumed());
        to.setGivePointConsumed(from.getGivePointConsumed());
        to.setExpPointConsumed(from.getExpPointConsumed());
        to.setModelPointConsumed(from.getModelPointConsumed());
        to.setRechargeAmount(from.getRechargeAmount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsUserPointDO query2DO(StatsUserPointQuery from) {
        StatsUserPointDO to = new StatsUserPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setNickName(from.getNickNameLike());
        to.setDistributorCorpName(from.getDistributorCorpNameLike());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setPointConsumed(from.getPointConsumed());
        to.setGivePointConsumed(from.getGivePointConsumed());
        to.setExpPointConsumed(from.getExpPointConsumed());
        to.setModelPointConsumed(from.getModelPointConsumed());
        to.setRechargeAmount(from.getRechargeAmount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsUserPointExample query2Example(StatsUserPointQuery from) {
        StatsUserPointExample to = new StatsUserPointExample();
        StatsUserPointExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdNotEqualTo(0);
        }
        if (!ObjectUtils.isEmpty(from.getNickNameLike())) {
            c.andNickNameLike("%" + from.getNickNameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getDistributorCorpNameLike())) {
            c.andDistributorCorpNameLike("%" + from.getDistributorCorpNameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDateLower()) && !ObjectUtils.isEmpty(from.getStatsDateUpper())) {
            c.andStatsDateBetween(from.getStatsDateLower(), from.getStatsDateUpper());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDateLower()) && ObjectUtils.isEmpty(from.getStatsDateUpper())) {
            c.andStatsDateGreaterThanOrEqualTo(from.getStatsDateLower());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDateUpper()) && ObjectUtils.isEmpty(from.getStatsDateLower())) {
            c.andStatsDateLessThanOrEqualTo(from.getStatsDateUpper());
        }
        if (!ObjectUtils.isEmpty(from.getPointConsumed())) {
            c.andPointConsumedEqualTo(from.getPointConsumed());
        }
        if (!ObjectUtils.isEmpty(from.getPointConsumedLower()) && !ObjectUtils.isEmpty(from.getPointConsumedUpper())) {
            c.andPointConsumedBetween(from.getPointConsumedLower(), from.getPointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getPointConsumedLower()) && ObjectUtils.isEmpty(from.getPointConsumedUpper())) {
            c.andPointConsumedGreaterThanOrEqualTo(from.getPointConsumedLower());
        }
        if (!ObjectUtils.isEmpty(from.getPointConsumedUpper()) && ObjectUtils.isEmpty(from.getPointConsumedLower())) {
            c.andPointConsumedLessThanOrEqualTo(from.getPointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getGivePointConsumed())) {
            c.andGivePointConsumedEqualTo(from.getGivePointConsumed());
        }
        if (!ObjectUtils.isEmpty(from.getGivePointConsumedLower()) && !ObjectUtils.isEmpty(from.getGivePointConsumedUpper())) {
            c.andGivePointConsumedBetween(from.getGivePointConsumedLower(), from.getGivePointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getGivePointConsumedLower()) && ObjectUtils.isEmpty(from.getGivePointConsumedUpper())) {
            c.andGivePointConsumedGreaterThanOrEqualTo(from.getGivePointConsumedLower());
        }
        if (!ObjectUtils.isEmpty(from.getGivePointConsumedUpper()) && ObjectUtils.isEmpty(from.getGivePointConsumedLower())) {
            c.andGivePointConsumedLessThanOrEqualTo(from.getGivePointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getExpPointConsumed())) {
            c.andExpPointConsumedEqualTo(from.getExpPointConsumed());
        }
        if (!ObjectUtils.isEmpty(from.getExpPointConsumedLower()) && !ObjectUtils.isEmpty(from.getExpPointConsumedUpper())) {
            c.andExpPointConsumedBetween(from.getExpPointConsumedLower(), from.getExpPointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getExpPointConsumedLower()) && ObjectUtils.isEmpty(from.getExpPointConsumedUpper())) {
            c.andExpPointConsumedGreaterThanOrEqualTo(from.getExpPointConsumedLower());
        }
        if (!ObjectUtils.isEmpty(from.getModelPointConsumed())) {
            c.andModelPointConsumedEqualTo(from.getModelPointConsumed());
        }
        if (!ObjectUtils.isEmpty(from.getModelPointConsumedLower()) && !ObjectUtils.isEmpty(from.getModelPointConsumedUpper())) {
            c.andModelPointConsumedBetween(from.getModelPointConsumedLower(), from.getModelPointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getModelPointConsumedLower()) && ObjectUtils.isEmpty(from.getModelPointConsumedUpper())) {
            c.andModelPointConsumedGreaterThanOrEqualTo(from.getModelPointConsumedLower());
        }
        if (!ObjectUtils.isEmpty(from.getModelPointConsumedUpper()) && ObjectUtils.isEmpty(from.getModelPointConsumedLower())) {
            c.andModelPointConsumedLessThanOrEqualTo(from.getModelPointConsumedUpper());
        }
        if (!ObjectUtils.isEmpty(from.getRechargeAmount())) {
            c.andRechargeAmountEqualTo(from.getRechargeAmount());
        }
        if (!ObjectUtils.isEmpty(from.getRechargeAmountLower()) && !ObjectUtils.isEmpty(from.getRechargeAmountUpper())) {
            c.andRechargeAmountBetween(from.getRechargeAmountLower(), from.getRechargeAmountUpper());
        }
        if (!ObjectUtils.isEmpty(from.getRechargeAmountLower()) && ObjectUtils.isEmpty(from.getRechargeAmountUpper())) {
            c.andRechargeAmountGreaterThanOrEqualTo(from.getRechargeAmountLower());
        }
        if (!ObjectUtils.isEmpty(from.getRechargeAmountUpper()) && ObjectUtils.isEmpty(from.getRechargeAmountLower())) {
            c.andRechargeAmountLessThanOrEqualTo(from.getRechargeAmountUpper());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<StatsUserPointVO> doList2VOList(List<StatsUserPointDO> list) {
        return CommonUtil.listConverter(list, StatsUserPointConverter::do2VO);
    }

    /**
     * 检查StatsUserPointDO的消耗字段和充值字段是否为null或空
     * 如果有任一字段为null或空，则返回null
     * 
     * @param statsDO 需要检查的StatsUserPointDO对象
     * @return 如果所有字段都有值则返回原对象，否则返回null
     */
    public static boolean checkStatsFieldsNotEmpty(StatsUserPointDO statsDO) {
        if (statsDO == null) {
            return false;
        }
        // 检查消耗字段
        return statsDO.getPointConsumed() != null &&
                statsDO.getGivePointConsumed() != null &&
                statsDO.getExpPointConsumed() != null &&
                statsDO.getModelPointConsumed() != null &&
                statsDO.getRechargeAmount() != null;
    }
}
package ai.conrain.aigc.platform.service.component;


import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.model.request.SubUserUpdate;
import ai.conrain.aigc.platform.service.model.request.UserRegister;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import java.util.List;

/**
 * 用户子账号服务
 *
 * <AUTHOR>
 * @version : SubUserService.java, v 0.1 2024/1/20 20:24 renxiao.wu Exp $
 */
public interface SubUserService {
    /**
     * 查询所有子账号列表
     *
     * @return 所有子账号列表
     */
    List<UserVO> queryList();

    /**
     * 添加子账号
     *
     * @param user 用户信息
     * @return 返回结果
     */
    UserVO create(UserRegister user);

    /**
     * 前台更新用户信息
     *
     * @param user 用户信息
     */
    void update(SubUserUpdate user);

    /**
     * 停用/启用子账号
     *
     * @param userId 用户id
     * @param status 状态
     */
    void enableOrDisable(Integer userId, UserStatusEnum status);
}

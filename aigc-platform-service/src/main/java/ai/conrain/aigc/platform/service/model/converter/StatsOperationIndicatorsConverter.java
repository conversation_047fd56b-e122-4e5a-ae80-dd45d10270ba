package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.StatsOperationIndicatorsDO;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.query.StatsOperationIndicatorsQuery;
import ai.conrain.aigc.platform.dal.example.StatsOperationIndicatorsExample;
import ai.conrain.aigc.platform.service.model.vo.StatsOperationIndicatorsVO;

import com.alibaba.fastjson.JSON;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * StatsOperationIndicatorsConverter
 *
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-16 11:38:50
 */
public class StatsOperationIndicatorsConverter {

    /**
     * DO -> VO
     */
    public static StatsOperationIndicatorsVO do2VO(StatsOperationIndicatorsDO from) {
        StatsOperationIndicatorsVO to = new StatsOperationIndicatorsVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerConsumptionPointsAvg(from.getCustomerConsumptionPointsAvg());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setDeliveryClothingCount(from.getDeliveryClothingCount());
        to.setApproveClothingCount(from.getApproveClothingCount());
        to.setApproveErrorRate(from.getApproveErrorRate());
        to.setGarmentRebateRate(from.getGarmentRebateRate());
        to.setCustomerComplaintRate(from.getCustomerComplaintRate());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        }

        to.setCustomerTotalCount(from.getCustomerTotalCount());
        to.setVideoCount(from.getVideoCount());
        to.setVideoCountAvg(from.getVideoCountAvg());
        to.setCustomerUploadMaterialCount(from.getCustomerUploadMaterialCount());

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsOperationIndicatorsDO vo2DO(StatsOperationIndicatorsVO from) {
        StatsOperationIndicatorsDO to = new StatsOperationIndicatorsDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerConsumptionPointsAvg(from.getCustomerConsumptionPointsAvg());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setDeliveryClothingCount(from.getDeliveryClothingCount());
        to.setApproveClothingCount(from.getApproveClothingCount());
        to.setApproveErrorRate(from.getApproveErrorRate());
        to.setGarmentRebateRate(from.getGarmentRebateRate());
        to.setCustomerComplaintRate(from.getCustomerComplaintRate());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        // 如果extInfo为空，则添加默认值
        if (from.getExtInfo() == null) {
            from.addExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR, 0);
            from.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, 0);
        }

        to.setExtInfo(JSON.toJSONString(from.getExtInfo()));
        to.setCustomerTotalCount(from.getCustomerTotalCount());
        to.setVideoCount(from.getVideoCount());
        to.setVideoCountAvg(from.getVideoCountAvg());
        to.setCustomerUploadMaterialCount(from.getCustomerUploadMaterialCount());
        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsOperationIndicatorsQuery do2Query(StatsOperationIndicatorsDO from) {
        StatsOperationIndicatorsQuery to = new StatsOperationIndicatorsQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerConsumptionPointsAvg(from.getCustomerConsumptionPointsAvg());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setDeliveryClothingCount(from.getDeliveryClothingCount());
        to.setApproveClothingCount(from.getApproveClothingCount());
        to.setApproveErrorRate(from.getApproveErrorRate());
        to.setGarmentRebateRate(from.getGarmentRebateRate());
        to.setCustomerComplaintRate(from.getCustomerComplaintRate());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());
        to.setCustomerTotalCount(from.getCustomerTotalCount());
        to.setVideoCount(from.getVideoCount());
        to.setVideoCountAvg(from.getVideoCountAvg());
        to.setCustomerUploadMaterialCount(from.getCustomerUploadMaterialCount());
        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsOperationIndicatorsDO query2DO(StatsOperationIndicatorsQuery from) {
        StatsOperationIndicatorsDO to = new StatsOperationIndicatorsDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setUserId(from.getUserId());
        to.setName(from.getName());
        to.setCustomerConversionCount(from.getCustomerConversionCount());
        to.setCustomerConsumptionPoints(from.getCustomerConsumptionPoints());
        to.setCustomerConsumptionPointsAvg(from.getCustomerConsumptionPointsAvg());
        to.setCustomerActivityRate(from.getCustomerActivityRate());
        to.setCustomerRepurchaseRate(from.getCustomerRepurchaseRate());
        to.setCustomModelCustomers(from.getCustomModelCustomers());
        to.setCustomSceneCustomers(from.getCustomSceneCustomers());
        to.setCustomerProtectionMetrics(from.getCustomerProtectionMetrics());
        to.setDeliveryClothingCount(from.getDeliveryClothingCount());
        to.setApproveClothingCount(from.getApproveClothingCount());
        to.setApproveErrorRate(from.getApproveErrorRate());
        to.setGarmentRebateRate(from.getGarmentRebateRate());
        to.setCustomerComplaintRate(from.getCustomerComplaintRate());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());
        to.setCustomerTotalCount(from.getCustomerTotalCount());
        to.setVideoCount(from.getVideoCount());
        to.setVideoCountAvg(from.getVideoCountAvg());
        to.setCustomerUploadMaterialCount(from.getCustomerUploadMaterialCount());
        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsOperationIndicatorsExample query2Example(StatsOperationIndicatorsQuery from) {
        StatsOperationIndicatorsExample to = new StatsOperationIndicatorsExample();
        StatsOperationIndicatorsExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdLike("%" + from.getUserId() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getExcludeUserId())) {
            c.andUserIdNotEqualTo(from.getExcludeUserId());
        }
        if (!ObjectUtils.isEmpty(from.getSystemUserId())) {
            c.andUserIdEqualTo(from.getSystemUserId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameLike("%" + from.getName() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getCustomerConversionCount())) {
            c.andCustomerConversionCountEqualTo(from.getCustomerConversionCount());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerConsumptionPoints())) {
            c.andCustomerConsumptionPointsEqualTo(from.getCustomerConsumptionPoints());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerConsumptionPointsAvg())) {
            c.andCustomerConsumptionPointsAvgEqualTo(from.getCustomerConsumptionPointsAvg());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerActivityRate())) {
            c.andCustomerActivityRateEqualTo(from.getCustomerActivityRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerRepurchaseRate())) {
            c.andCustomerRepurchaseRateEqualTo(from.getCustomerRepurchaseRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomModelCustomers())) {
            c.andCustomModelCustomersEqualTo(from.getCustomModelCustomers());
        }
        if (!ObjectUtils.isEmpty(from.getCustomSceneCustomers())) {
            c.andCustomSceneCustomersEqualTo(from.getCustomSceneCustomers());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerProtectionMetrics())) {
            c.andCustomerProtectionMetricsEqualTo(from.getCustomerProtectionMetrics());
        }
        if (!ObjectUtils.isEmpty(from.getDeliveryClothingCount())) {
            c.andDeliveryClothingCountEqualTo(from.getDeliveryClothingCount());
        }
        if (!ObjectUtils.isEmpty(from.getApproveClothingCount())) {
            c.andApproveClothingCountEqualTo(from.getApproveClothingCount());
        }
        if (!ObjectUtils.isEmpty(from.getApproveErrorRate())) {
            c.andApproveErrorRateEqualTo(from.getApproveErrorRate());
        }
        if (!ObjectUtils.isEmpty(from.getGarmentRebateRate())) {
            c.andGarmentRebateRateEqualTo(from.getGarmentRebateRate());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerComplaintRate())) {
            c.andCustomerComplaintRateEqualTo(from.getCustomerComplaintRate());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerTotalCount())) {
            c.andCustomerTotalCountEqualTo(from.getCustomerTotalCount());
        }
        if (!ObjectUtils.isEmpty(from.getVideoCount())) {
            c.andVideoCountEqualTo(from.getVideoCount());
        }
        if (!ObjectUtils.isEmpty(from.getVideoCountAvg())) {
            c.andVideoCountAvgEqualTo(from.getVideoCountAvg());
        }
        if (!ObjectUtils.isEmpty(from.getCustomerUploadMaterialCount())) {
            c.andCustomerUploadMaterialCountEqualTo(from.getCustomerUploadMaterialCount());
        }
        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<StatsOperationIndicatorsVO> doList2VOList(List<StatsOperationIndicatorsDO> list) {
        return CommonUtil.listConverter(list, StatsOperationIndicatorsConverter::do2VO);
    }
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * 服务工具类
 *
 * <AUTHOR>
 * @version : ServerHelper.java, v 0.1 2024/8/8 17:08 renxiao.wu Exp $
 */
@Slf4j
@Component
public class ServerHelper {
    /** 过期时间，1天 */
    private static final int EXPIRE_SECONDS = 24 * 60 * 60;
    @Autowired
    private TairService tairService;
    @Lazy
    @Autowired
    private ServerService serverService;
    @Lazy
    @Autowired
    private PipelineService pipelineService;
    @Value("${comfyui.file.a800url}")
    private String comfyuiFileUrlA800;
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 将服务状态放入缓存
     *
     * @param serverId 服务id
     * @param status   状态
     */
    public void putToTair(Integer serverId, ServerStatusEnum status) {
        putToTair(serverId, new ServerCache(status));
    }

    /**
     * 将保存任务到缓存
     *
     * @param serverId 服务id
     * @param taskId   任务id
     * @param type     任务类型
     */
    public void putToTair(Integer serverId, Integer taskId, DispatchTypeEnum type) {
        putToTair(serverId, new ServerCache(ServerStatusEnum.BUSY, taskId, type));
    }

    /**
     * 将服务状态放入缓存
     *
     * @param serverId 服务id
     * @param cache    缓存数据
     */
    public void putToTair(Integer serverId, ServerCache cache) {
        tairService.setObject(getServerTairKey(serverId), cache, EXPIRE_SECONDS);
    }

    /**
     * 从缓存中获取缓存数据
     *
     * @param serverId 服务id
     * @return 缓存数据
     */
    public ServerCache getFromTair(Integer serverId) {
        return tairService.getObject(getServerTairKey(serverId), ServerCache.class);
    }

    /**
     * 获取服务地址+端口
     *
     * @param server 服务配置
     * @return 服务地址
     */
    public String getServerUrl(ServerVO server) {
        if (server == null) {
            return null;
        }

        //如果带有服务别名，则直接使用服务别名
        if (StringUtils.isNotBlank(server.getConfigAlias())) {
            return server.getConfigAlias() + ":" + server.getConfig();
        }

        ServerVO parent = server.getParent();
        if (parent == null) {
            parent = serverService.queryByKey(server.getParentId());
        }
        if (parent == null) {
            log.warn("【服务路由】获取服务信息失败,获取到的parent配置为空2,id={}", server.getId());
            return null;
        }
        return parent.getConfig() + ":" + server.getConfig();
    }

    /**
     * 获取传输服务地址+端口
     *
     * @param transServerUrl 转发地址
     * @param server         目标服务
     * @return 服务地址
     */
    public String getTransServerUrl(String transServerUrl, ServerVO server) {
        if (StringUtils.isBlank(transServerUrl) || server == null) {
            return transServerUrl;
        }

        ServerVO parent = server.getParent();
        if (parent == null) {
            parent = serverService.queryByKey(server.getParentId());
        }
        if (parent == null) {
            return transServerUrl;
        }

        ServerVO transServer = serverService.parseByUrl(transServerUrl);
        ServerVO transParent = serverService.queryByKey(transServer.getParentId());

        //两个地址在同一个域名/ip下时，且有配置内网地址，则使用内网地址
        if (isSameRoom(transParent, server)) {
            if (StringUtils.isNotBlank(transServer.getDeviceId()) && StringUtils.isNotBlank(
                transServer.getIntranetAddress())) {
                String internalAddress = systemConfigService.queryServerUrlByPort(transServer.getDeviceId(), false);
                log.info("传输地址与目标地址在同一个域名下，1开始使用内网地址传输{}", internalAddress);
                return internalAddress;
            } else if (StringUtils.isNotBlank(transParent.getIntranetAddress())) {
                log.info("传输地址与目标地址在同一个域名下，2开始使用内网地址传输{}", transParent.getIntranetAddress());
                return "http://" + transParent.getIntranetAddress() + ":" + transServer.getConfig();
            }
        }

        return transServerUrl;
    }

    /**
     * 获取传输服务地址+端口
     *
     * @param transServerUrl 转发地址
     * @param originUrl      源服务
     * @return 服务地址
     */
    public String getTransServerUrl(String transServerUrl, String originUrl) {
        ServerVO server = serverService.parseByUrl(originUrl);
        return getTransServerUrl(transServerUrl, server);
    }

    /**
     * 根据服务类型筛选服务
     *
     * @param type     业务类型
     * @param pipeline 管道
     * @return 服务列表
     */
    public List<ServerVO> getServersByType(DispatchTypeEnum type, PipelineVO pipeline) {
        return getServersByType(type.getServerType(), pipeline);
    }

    /**
     * 根据服务类型筛选服务
     *
     * @param type     服务类型
     * @param pipeline 管道
     * @return 服务列表
     */
    public List<ServerVO> getServersByType(ServerTypeEnum type, PipelineVO pipeline) {
        List<ServerVO> result = new ArrayList<>();

        if (pipeline == null) {
            log.error("pipeline is null, type={}", type);
            return result;
        }
        List<ServerVO> servers = pipeline.getServers();
        if (CollectionUtils.isEmpty(servers)) {
            log.warn("pipeline {} has no servers", pipeline.getId());
            return result;
        }
        servers.forEach(server -> {
            if (server.getStatus() != ServerStatusEnum.DISABLE && CollectionUtils.isNotEmpty(server.getChildren())) {
                server.getChildren().forEach(child -> {
                    if (child.getType() == type && child.getStatus() != ServerStatusEnum.DISABLE) {
                        result.add(child);
                    }
                });
            }
        });

        return result;
    }

    /**
     * 获取所有文件服务列表
     *
     * @return 文件服务列表
     */
    public List<ServerVO> getAllFileServers() {
        List<ServerVO> serverList = serverService.queryAll();
        //筛选出是端口的服务 & 是文件服务 & 非当前文件服务（当前服务不需要通知） & 机器状态未关闭
        return serverList.stream().filter(e -> e.getLevel() == 2 && e.getType() == ServerTypeEnum.FILE_SERVICE).filter(
            this::isEnable).collect(Collectors.toList());
    }

    /**
     * 获取所有文件服务地址列表
     *
     * @return 文件服务地址列表
     */
    public List<String> getAllFileServerUrl() {
        List<ServerVO> allFileServers = getAllFileServers();

        List<String> fileServerUrls = new ArrayList<>();
        allFileServers.forEach(server -> {
            String fileServerUrl = getFileServerUrl(server);
            if (!fileServerUrls.contains(fileServerUrl)) {
                fileServerUrls.add(fileServerUrl);
            }
        });

        // 随机打乱
        Collections.shuffle(fileServerUrls);

        return fileServerUrls;
    }

    /**
     * 获取创作任务的文件服务地址
     *
     * @param task 任务
     * @return 文件服务地址
     */
    public String getFileServerUrlByTask(IExtModel task) {
        Integer serverId = task.getExtValue(KEY_SERVER_ID, Integer.class);
        if (serverId != null) {
            return getFileServerUrl(serverId);
        }

        return getFileServerUrl(task.getStringFromExtInfo(KEY_SERVER_URL));
    }

    /**
     * 获取当前服务的文件服务地址
     *
     * @param serverUrl 业务服务地址，带端口
     * @return 文件服务地址
     */
    public String getFileServerUrl(String serverUrl) {
        if (StringUtils.isBlank(serverUrl)) {
            return null;
        }
        ServerVO server = serverService.parseByUrl(serverUrl);
        if (server == null) {
            String serverIp = StringUtils.substringBeforeLast(serverUrl, ":");
            server = serverService.parseByUrl(serverIp);

            if (server == null) {
                log.warn("【服务路由】获取文件服务信息失败,找不到当前服务地址关联信息{}", serverUrl);
                return null;
            }
        }

        //如果包含内网地址，则直接返回
        ServerVO parent = serverService.queryByKey(server.getParentId());
        if (parent != null && StringUtils.isNotBlank(parent.getIntranetAddress()) && StringUtils.contains(serverUrl,
            parent.getIntranetAddress())) {
            return serverUrl;
        }

        return getFileServerUrl(server);
    }

    /**
     * 获取当前服务的文件服务地址
     *
     * @param serverId 业务服务id
     * @return 文件服务地址
     */
    public String getFileServerUrl(Integer serverId) {
        ServerVO server = serverService.queryByKey(serverId);
        return getFileServerUrl(server);
    }

    /**
     * 获取当前用户的文件服务地址
     *
     * @param userId 用户id
     * @return 文件服务地址
     */
    public String getFileServerUrlByUser(Integer userId) {
        return getServerUrl(getFileServerByUserId(userId, DispatchTypeEnum.CREATIVE_TASK));
    }

    /**
     * 获取当前用户的ModerServer服务地址
     *
     * @param userId 用户id
     * @return 模型服务地址
     */
    public String getModelServerUrlByUser(Integer userId) {
        PipelineVO pipeline = fetchPipelineByUserAndType(userId, ServerTypeEnum.MODEL_SERVER);
        List<ServerVO> servers = getServersByType(ServerTypeEnum.MODEL_SERVER, pipeline);

        //对servers进行随机排序
        if (CollectionUtils.isEmpty(servers)) {
            return null;
        }

        Collections.shuffle(servers);
        return getServerUrl(servers.get(0));
    }

    /**
     * 获取当前用户的ModerServer
     *
     * @param userId 用户id
     * @return 模型服务
     */
    public ServerVO getModelServerByUser(Integer userId) {
        PipelineVO pipeline = fetchPipelineByUserAndType(userId, ServerTypeEnum.MODEL_SERVER);
        List<ServerVO> servers = getServersByType(ServerTypeEnum.MODEL_SERVER, pipeline);

        //对servers进行随机排序
        if (CollectionUtils.isEmpty(servers)) {
            return null;
        }

        Collections.shuffle(servers);
        return servers.get(0);
    }

    /**
     * 根据用户id和分发类型获取文件服务
     *
     * @param userId 用户id
     * @param type   分发类型
     * @return 文件服务
     */
    public List<ServerVO> getFileServersByUserId(Integer userId, DispatchTypeEnum type) {
        PipelineVO pipeline = fetchPipelineByUserAndType(userId, type);
        return getServersByType(ServerTypeEnum.FILE_SERVICE, pipeline);
    }

    /**
     * 根据用户id和分发类型获取文件服务
     *
     * @param userId 用户id
     * @param type   分发类型
     * @return 文件服务
     */
    public ServerVO getFileServerByUserId(Integer userId, DispatchTypeEnum type) {
        PipelineVO pipeline = fetchPipelineByUserAndType(userId, type);

        List<ServerVO> servers = getServersByType(ServerTypeEnum.FILE_SERVICE, pipeline);
        if (CollectionUtils.isEmpty(servers)) {
            log.warn("user {} has no file server", userId);
            return null;
        }

        if (servers.size() > 1) {
            Random random = new Random();
            int index = random.nextInt(servers.size());
            return servers.get(index);
        }

        return servers.get(0);
    }

    /**
     * 根据用户和任务类型获取管道
     *
     * @param userId 用户id
     * @param type   类型
     * @return 管道
     */
    public PipelineVO fetchPipelineByUserAndType(Integer userId, DispatchTypeEnum type) {
        return fetchPipelineByUserAndType(userId, type.getServerType());
    }

    /**
     * 根据用户和任务类型获取管道
     *
     * @param userId 用户id
     * @param type   类型
     * @return 管道
     */
    public PipelineVO fetchPipelineByUserAndType(Integer userId, ServerTypeEnum type) {
        PipelineVO pipeline = pipelineService.fetchByUserId(userId);

        if (pipeline != null && hasCurrentTypeServer(pipeline, type)) {
            return pipeline;
        }

        //lora服务特殊处理
        List<PipelineVO> list = pipelineService.queryAll();
        pipeline = list.stream().filter(e -> hasCurrentTypeServer(e, type)).findFirst().orElse(null);

        return pipeline;
    }

    /**
     * server缓存key
     *
     * @param serverId 服务id
     * @return 缓存key
     */
    private String getServerTairKey(Integer serverId) {
        return "_dispatch_server_" + serverId;
    }

    /**
     * 获取当前服务的文件服务地址
     *
     * @param server 业务服务
     * @return 文件服务地址
     */
    public String getFileServerUrl(ServerVO server) {
        if (server == null) {
            return null;
        }
        if (server.getType() == ServerTypeEnum.FILE_SERVICE && isEnable(server)) {
            return getServerUrl(server);
        }

        ServerVO parent = server.getLevel() == 1 ? server : server.getParent();
        if (parent == null) {
            parent = serverService.queryByKey(server.getParentId());
        }

        if (parent == null || CollectionUtils.isEmpty(parent.getChildren())) {
            log.warn("【服务路由】获取服务信息失败,获取到的parent配置为空,id={}", server.getId());
            return null;
        }

        ServerVO fileServer = parent.getChildren().stream().filter(
            e -> e.getType() == ServerTypeEnum.FILE_SERVICE && isEnable(e)).findFirst().orElse(null);

        if (fileServer == null) {
            log.warn("【服务路由】获取服务信息失败,过滤parent所有子节点，未查找到可用文件服务,id={},parentId={}",
                server.getId(), parent.getId());
        }

        return getServerUrl(fileServer);
    }

    /**
     * 判断当前服务是否可用
     *
     * @param server 当前服务
     * @return true，可用
     */
    public boolean isEnable(ServerVO server) {
        if (server == null) {
            return false;
        }
        if (server.getStatus() == ServerStatusEnum.DISABLE) {
            return false;
        }

        if (server.getLevel() == 2) {
            ServerVO parent = server.getParent();
            return parent != null && parent.getStatus() != ServerStatusEnum.DISABLE;
        }

        return true;
    }

    /**
     * 判断当前服务是否可用
     *
     * @param serverUrl 当前服务地址
     * @return true，可用
     */
    public boolean isEnable(String serverUrl) {
        ServerVO server = serverService.parseByUrl(serverUrl);
        return isEnable(server);
    }

    /**
     * 判断当前服务是否可用
     *
     * @param serverUrl 当前服务地址
     * @return true，可用
     */
    public boolean isEnableMachine(String serverUrl) {
        ServerVO server = serverService.parseByUrl(serverUrl);

        if (server.getLevel() == 2) {
            ServerVO parent = server.getParent();

            if (parent == null) {
                parent = serverService.queryByKey(server.getParentId());
            }

            return parent != null && parent.getStatus() != ServerStatusEnum.DISABLE;
        } else {
            return server.getStatus() != ServerStatusEnum.DISABLE;
        }

    }

    /**
     * 判断是否同一机房
     *
     * @param server1 服务1
     * @param server2 服务2
     * @return true，同一机房
     */
    public boolean isSameRoom(ServerVO server1, ServerVO server2) {
        if (server1 == null || server2 == null) {
            return false;
        }

        //如果已经设置了设备信息，则直接比较设备信息的机房编码段即可
        if (StringUtils.isNotBlank(server1.getDeviceId()) && StringUtils.isNotBlank(server2.getDeviceId())) {
            return StringUtils.equals(StringUtils.split(server1.getDeviceId(), "-")[0],
                StringUtils.split(server2.getDeviceId(), "-")[0]);
        }

        //TODO 兼容老数据
        ServerVO rootServer1 = server1.getLevel() == 1 ? server1 : serverService.queryByKey(server1.getParentId());
        ServerVO rootServer2 = server2.getLevel() == 1 ? server2 : serverService.queryByKey(server2.getParentId());

        //公网IP一致时，也当做是同一机房
        return StringUtils.equals(rootServer1.getConfig(), rootServer2.getConfig());
    }

    public boolean isSameMachine(ServerVO server1, ServerVO server2) {
        if (server1 == null || server2 == null) {
            return false;
        }

        //如果已经设置了设备信息，则直接比较设备信息的机房编码段即可
        if (StringUtils.isNotBlank(server1.getDeviceId()) && StringUtils.isNotBlank(server2.getDeviceId())) {
            return StringUtils.equals(StringUtils.substringBeforeLast(server1.getDeviceId(), "-"),
                StringUtils.substringBeforeLast(server2.getDeviceId(), "-"));
        }

        //TODO 兼容老数据
        ServerVO rootServer1 = server1.getLevel() == 1 ? server1 : serverService.queryByKey(server1.getParentId());
        ServerVO rootServer2 = server2.getLevel() == 1 ? server2 : serverService.queryByKey(server2.getParentId());

        //公网IP一致时，也当做是同一机房
        return StringUtils.equals(rootServer1.getConfig(), rootServer2.getConfig()) && StringUtils.equals(
            rootServer1.getIntranetAddress(), rootServer2.getIntranetAddress());
    }

    /**
     * 判断当前集群是否有当前类型的服务
     *
     * @param pipeline 管道
     * @param type     分发类型
     * @return true，有
     */
    private boolean hasCurrentTypeServer(PipelineVO pipeline, DispatchTypeEnum type) {
        return hasCurrentTypeServer(pipeline, type.getServerType());
    }

    /**
     * 判断当前集群是否有当前类型的服务
     *
     * @param pipeline 管道
     * @param type     分发类型
     * @return true，有
     */
    private boolean hasCurrentTypeServer(PipelineVO pipeline, ServerTypeEnum type) {
        List<ServerVO> servers = pipeline.getServers();

        if (CollectionUtils.isEmpty(servers)) {
            return false;
        }

        return servers.stream().anyMatch(
            item -> item.getStatus() != ServerStatusEnum.DISABLE && CollectionUtils.isNotEmpty(item.getChildren())
                    && item.getChildren().stream()
                        .anyMatch(child -> child.getStatus() != ServerStatusEnum.DISABLE && child.getType() == type));
    }

}

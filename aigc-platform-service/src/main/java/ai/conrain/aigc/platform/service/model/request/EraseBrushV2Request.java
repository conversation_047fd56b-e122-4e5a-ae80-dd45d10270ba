package ai.conrain.aigc.platform.service.model.request;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

@Data
public class EraseBrushV2Request implements CreativeRequest {
    private static final long serialVersionUID = 1L;

    /** 批次 id */
    Integer batchId;

    /**
     * 原图 task_id
     * taskId 不为空时, 表示退回到指定的 task
     * taskId = 0 时, 表示清空所有任务, 回退到原图
     */
    Integer taskId;

    /** 原图模特图的 modelId */
    Integer modelId;

    // 创建消除笔任务时, 传原图 url
    /** 原图 url */
    String originImage;

    /** 蒙版文件 */
    @JSONField(serialize = false)
    private MultipartFile maskFile;

    /** 关联的去皱批次id */
    Integer relatedRemoveWrinkleId;

    /** 关联的去皱类型(中度、重度) */
    String removeWrinkleType;
}
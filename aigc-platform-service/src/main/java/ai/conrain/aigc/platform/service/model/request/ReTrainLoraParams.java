package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

@Data
public class ReTrainLoraParams {

    /**
     * 是否自动训练
     */
    private boolean autoTrain;

    /**
     * 标签类型
     */
    private String labelType;

    /**
     * 是否细节放大
     */
    private String cut4ScaleUp;

    /**
     * 是否白头处理
     */
    private String preprocessCensoredFace;

    /**
     * 抠图关键词
     */
    private String cutoutKeyword;

    /**
     * 重新抠图时是否准备预览
     */
    private boolean prepareViewAgainWhenCutoutAgain;

    /**
     * 图片尺寸
     */
    private String imageSize;

    /**
     * 标题提示词
     */
    private String captionPrompt;

    /**
     * 水印描述
     */
    private String waterMarkDesc;

    /**
     * 是否正方形
     */
    private boolean isSquare;

    /**
     * 仅缩放处理
     */
    private String cutoutOnlyUpscale;

    /** 抠图类型 */
    private String cutoutType;

    /**
     * 服装细节提示词
     */
    private String clothDetailsPrompt;

    /**
     * 训练重复次数
     */
    private Integer trainRepeatTimes;

    /**
     * 不显示脸部
     */
    private String noshowFace;

    //抠图模型
    private String cutoutModel;

    /** 自动完成 */
    private boolean autoComplete = false;

    /** 检测阈值 */
    private Double detectThreshold;

    /** 是否是补丁任务抠图 */
    private boolean isPatchCutout;


}

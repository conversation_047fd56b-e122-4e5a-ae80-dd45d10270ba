package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.WorkflowTaskDO;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums.TaskStatus;
import ai.conrain.aigc.platform.service.enums.TaskStatusEnum;
import ai.conrain.aigc.platform.service.enums.WorkflowTaskEnum;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.dal.example.WorkflowTaskExample;
import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;

import ai.conrain.aigc.platform.service.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * WorkflowTaskConverter
 *
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
public class WorkflowTaskConverter {

    /**
     * DO -> VO
     */
    public static WorkflowTaskVO do2VO(WorkflowTaskDO from) {
        WorkflowTaskVO to = new WorkflowTaskVO();
        to.setId(from.getId());
        to.setBizId(from.getBizId());
        to.setType(from.getType());
        to.setOperatorId(from.getOperatorId());
        to.setStatus(from.getStatus());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMeta(from.getMeta() == null ? new JSONObject() : JSON.parseObject(from.getMeta()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static WorkflowTaskDO vo2DO(WorkflowTaskVO from) {
        WorkflowTaskDO to = new WorkflowTaskDO();
        to.setId(from.getId());
        to.setBizId(from.getBizId());
        to.setType(from.getType());
        to.setOperatorId(from.getOperatorId());
        to.setStatus(from.getStatus());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMeta(from.getMeta() == null ? null : JSON.toJSONString(from.getMeta()));

        return to;
    }

    /**
     * DO -> Query
     */
    public static WorkflowTaskQuery do2Query(WorkflowTaskDO from) {
        WorkflowTaskQuery to = new WorkflowTaskQuery();
        to.setId(from.getId());
        to.setBizId(from.getBizId());
        to.setType(from.getType());
        to.setOperatorId(from.getOperatorId());
        to.setStatus(from.getStatus());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMeta(from.getMeta());

        return to;
    }

    /**
     * Query -> DO
     */
    public static WorkflowTaskDO query2DO(WorkflowTaskQuery from) {
        WorkflowTaskDO to = new WorkflowTaskDO();
        to.setId(from.getId());
        to.setBizId(from.getBizId());
        to.setType(from.getType());
        to.setOperatorId(from.getOperatorId());
        to.setStatus(from.getStatus());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setMeta(from.getMeta());

        return to;
    }


    /**
     * Query -> Example
     */
    public static WorkflowTaskExample query2Example(WorkflowTaskQuery from) {
        WorkflowTaskExample to = new WorkflowTaskExample();
        WorkflowTaskExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getBizId())) {
            c.andBizIdEqualTo(from.getBizId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (CollectionUtils.isNotEmpty(from.getTypes())) {
            c.andTypeIn(from.getTypes());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        if (from.isOnlyToday()) {
            c.andCreateTimeBetween(DateUtils.getFirstOfDay(), DateUtils.getLastOfDay());
        }
        if (CollectionUtils.isNotEmpty(from.getOperatorIds())) {
            c.andOperatorIdIn(from.getOperatorIds());
        }
        if (CollectionUtils.isNotEmpty(from.getBizIds())) {
            c.andBizIdIn(from.getBizIds());
        }
        if (Boolean.FALSE.equals(from.getReviewed())) {
            c.andTypeIn(Arrays.asList(WorkflowTaskEnum.INITIAL_REVIEW.getType(), WorkflowTaskEnum.AGAIN_REVIEW.getType()));
            c.andStatusIn(Arrays.asList(TaskStatus.INIT.name(), TaskStatus.RUNNING.name()));
        }
        if (Boolean.TRUE.equals(from.getReviewed())) {
            c.andTypeIn(Arrays.asList(WorkflowTaskEnum.INITIAL_REVIEW.getType(), WorkflowTaskEnum.AGAIN_REVIEW.getType()));
            c.andStatusIn(Arrays.asList(TaskStatus.COMPLETED.name(), TaskStatus.FAILED.name()));
        }
        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<WorkflowTaskVO> doList2VOList(List<WorkflowTaskDO> list) {
        return CommonUtil.listConverter(list, WorkflowTaskConverter::do2VO);
    }
}
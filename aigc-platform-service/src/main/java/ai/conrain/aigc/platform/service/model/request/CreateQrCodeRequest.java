package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
public class CreateQrCodeRequest {
    @NotBlank
    private String planCode;
    //支付金额，单位元，不传则使用方案默认金额
    private String payAmount;
    private Integer payMasterUserId;
    //充值muse点，有值则充值成功后，会自动加点
    private Integer musePoint;
    //充值创意图片数，有值时，充值成功后会赠送图片
    private Integer creativeImgCountGave;
}

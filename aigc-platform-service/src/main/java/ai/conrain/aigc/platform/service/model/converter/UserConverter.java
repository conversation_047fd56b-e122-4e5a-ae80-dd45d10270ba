package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserDO;
import ai.conrain.aigc.platform.dal.example.UserExample;
import ai.conrain.aigc.platform.service.enums.CustomRoleEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.UserStatusEnum;
import ai.conrain.aigc.platform.service.enums.UserTypeEnum;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.UserReviewInfo;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * UserConverter
 *
 * @version UserService.java v 0.1 2024-01-20 01:18:37
 */
public class UserConverter {

    /**
     * DO -> VO
     */
    public static UserVO do2VO(UserDO from) {
        UserVO to = new UserVO();
        to.setId(from.getId());
        to.setNickName(from.getNickName());
        to.setRealName(from.getRealName());
        to.setLoginId(from.getLoginId());
        to.setPswd(from.getPswd());
        to.setMobile(from.getMobile());
        to.setRoleType(RoleTypeEnum.getByCode(from.getRoleType()));
        to.setCustomRole(from.getCustomRole());
        to.setCorpOrgId(from.getCorpOrgId());
        to.setCorpName(StringUtils.defaultIfBlank(from.getRelatedCorpName(), from.getCorpName()));
        if (StringUtils.isNotBlank(from.getCustomRole())) {
            to.setCustomRoleName(CustomRoleEnum.getByCode(from.getCustomRole()).getDesc());
        }
        if (CommonUtil.isValidJson(from.getUserReviewInfo())) {
            to.setUserReviewInfo(JSONObject.parseObject(from.getUserReviewInfo(), UserReviewInfo.class));
        }

        to.setUserType(UserTypeEnum.getByCode(from.getUserType()));
        to.setMasterId(from.getMasterId());
        to.setStatus(UserStatusEnum.getByCode(from.getStatus()));
        if (UserStatusEnum.getByCode(from.getStatus()) != null) {
            to.setStatusDesc(UserStatusEnum.getByCode(from.getStatus()).getDesc());
        }
        to.setOperatorId(from.getOperatorId());
        to.setRegisterFrom(from.getRegisterFrom());
        to.setMemo(from.getMemo());
        to.setLoginFailCount(from.getLoginFailCount());
        to.setLastLoginTime(from.getLastLoginTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setLastVisitDate(from.getLastVisitDate());

        to.setRelatedDistributorCorpName(from.getRelatedDistributorCorpName());
        to.setRelatedDistributorCorpId(from.getRelatedDistributorCorpId());
        to.setRelatedSalesUserId(from.getRelatedSalesUserId());
        to.setRelatedSalesUserName(from.getRelatedSalesUserName());
        to.setContractDate(from.getContractDate());

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserDO vo2DO(UserVO from) {
        UserDO to = new UserDO();
        to.setId(from.getId());
        to.setNickName(from.getNickName());
        to.setRealName(from.getRealName());
        to.setLoginId(from.getLoginId());
        to.setPswd(from.getPswd());
        to.setMobile(from.getMobile());
        to.setRoleType(from.getRoleType() != null ? from.getRoleType().getCode() : null);
        to.setCustomRole(from.getCustomRole());
        to.setCorpOrgId(from.getCorpOrgId());
        if (from.getUserReviewInfo() != null) {
            to.setUserReviewInfo(JSONObject.toJSONString(from.getUserReviewInfo()));
        }
        to.setUserType(from.getUserType() != null ? from.getUserType().getCode() : null);
        to.setMasterId(from.getMasterId());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setOperatorId(from.getOperatorId());
        to.setRegisterFrom(from.getRegisterFrom());
        to.setMemo(from.getMemo());
        to.setLoginFailCount(from.getLoginFailCount());
        to.setLastLoginTime(from.getLastLoginTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        to.setLastVisitDate(from.getLastVisitDate());

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserQuery do2Query(UserDO from) {
        UserQuery to = new UserQuery();
        to.setId(from.getId());
        to.setNickName(from.getNickName());
        to.setRealName(from.getRealName());
        to.setLoginId(from.getLoginId());
        to.setPswd(from.getPswd());
        to.setMobile(from.getMobile());
        to.setRoleType(from.getRoleType());
        to.setCustomRole(from.getCustomRole());
        to.setCorpOrgId(from.getCorpOrgId());
        to.setUserType(from.getUserType());
        to.setMasterId(from.getMasterId());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setRegisterFrom(from.getRegisterFrom());
        to.setMemo(from.getMemo());
        to.setLoginFailCount(from.getLoginFailCount());
        to.setLastLoginTime(from.getLastLoginTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserDO query2DO(UserQuery from) {
        UserDO to = new UserDO();
        to.setId(from.getId());
        to.setNickName(from.getNickName());
        to.setRealName(from.getRealName());
        to.setLoginId(from.getLoginId());
        to.setPswd(from.getPswd());
        to.setMobile(from.getMobile());
        to.setRoleType(from.getRoleType());
        to.setCustomRole(from.getCustomRole());
        to.setCorpOrgId(from.getCorpOrgId());
        to.setUserType(from.getUserType());
        to.setMasterId(from.getMasterId());
        to.setStatus(from.getStatus());
        to.setOperatorId(from.getOperatorId());
        to.setRegisterFrom(from.getRegisterFrom());
        to.setMemo(from.getMemo());
        to.setLoginFailCount(from.getLoginFailCount());
        to.setLastLoginTime(from.getLastLoginTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static UserExample query2Example(UserQuery from) {
        UserExample to = new UserExample();
        UserExample.Criteria c = to.createCriteria();

        // 各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getNickName())) {
            c.andNickNameEqualTo(from.getNickName());
        }
        if (!ObjectUtils.isEmpty(from.getNickNameLike())) {
            c.andNickNameLike("%" + from.getNickNameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getCorpNameLike())) {
            c.andCorpNameLike("%" + from.getCorpNameLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getMobileLike())) {
            c.andMobileLike("%" + from.getMobileLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getRealName())) {
            c.andRealNameEqualTo(from.getRealName());
        }
        if (!ObjectUtils.isEmpty(from.getLoginId())) {
            c.andLoginIdEqualTo(from.getLoginId());
        }
        if (!ObjectUtils.isEmpty(from.getPswd())) {
            c.andPswdEqualTo(from.getPswd());
        }
        if (!ObjectUtils.isEmpty(from.getMobile())) {
            c.andMobileEqualTo(from.getMobile());
        }
        if (!ObjectUtils.isEmpty(from.getRoleType())) {
            c.andRoleTypeEqualTo(from.getRoleType());
        }
        if (!ObjectUtils.isEmpty(from.getCustomRole())) {
            c.andCustomRoleEqualTo(from.getCustomRole());
        }
        if (!ObjectUtils.isEmpty(from.getCorpOrgId())) {
            c.andCorpOrgIdEqualTo(from.getCorpOrgId());
        }
        if (!ObjectUtils.isEmpty(from.getUserType())) {
            c.andUserTypeEqualTo(from.getUserType());
        }
        if (!ObjectUtils.isEmpty(from.getMasterId())) {
            c.andMasterIdEqualTo(from.getMasterId());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getRegisterFrom())) {
            c.andRegisterFromEqualTo(from.getRegisterFrom());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getMemoLike())) {
            c.andMemoLike("%" + from.getMemoLike() + "%");
        }
        if (!ObjectUtils.isEmpty(from.getLoginFailCount())) {
            c.andLoginFailCountEqualTo(from.getLoginFailCount());
        }
        if (!ObjectUtils.isEmpty(from.getLastLoginTime())) {
            c.andLastLoginTimeEqualTo(from.getLastLoginTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getRoleTypeList())) {
            c.andRoleTypeIn(from.getRoleTypeList());
        }

        if (!ObjectUtils.isEmpty(from.getCustomRoleList())) {
            c.andCustomRoleIn(from.getCustomRoleList());
        }

        if (!ObjectUtils.isEmpty(from.getExceptCustomRoleList())) {
            c.andCustomRoleNotIn(from.getExceptCustomRoleList());
        }

        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }

        if (!ObjectUtils.isEmpty(from.getIdNotIn())) {
            c.andIdNotIn(from.getIdNotIn());
        }

        if (from.isOnlyNeedPromptEngineer()) {
            c.andOnlyNeedPromptEngineer();
        }

        if (from.isOnlyNeedContract()) {
            c.andOnlyNeedContract();
        }

        if (!ObjectUtils.isEmpty(from.getLastVisitDateBefore())) {
            c.andLastVisitDateBefore(from.getLastVisitDateBefore());
        }

        //客户关联销售主账号，或是销售账号，它对应的主账号
        if (!ObjectUtils.isEmpty(from.getRelatedDistributorMasterUserId())) {
            c.andDistributorMasterUserId(from.getRelatedDistributorMasterUserId());
        }

        //客户关联销售账号
        if (!ObjectUtils.isEmpty(from.getRelatedDistributorSalesUserId())) {
            c.andRelatedDistributorSalesUserId(from.getRelatedDistributorSalesUserId());
        }

        if (!ObjectUtils.isEmpty(from.getRelatedPromptEngineerUserId())) {
            c.andPromptEngineerUserIdEqualTo(from.getRelatedPromptEngineerUserId());
        }

        if (!ObjectUtils.isEmpty(from.getBefore60Days())) {
            c.andCreateTimeLessThan(from.getBefore60Days());
        }

        if (!ObjectUtils.isEmpty(from.getMasterIdList())) {
            c.andMasterIdIn(from.getMasterIdList());
        }

        // 虚拟备注
        if (!ObjectUtils.isEmpty(from.getVirtualMemo()) && from.getVirtualMemo()){
            c.andVirtualMemo();
        }

        // 已退款备注
        if (!ObjectUtils.isEmpty(from.getRefundedMemo()) && from.getRefundedMemo()){
            c.andRefundedMemo();
        }

        // 逻辑删除过滤
        for (UserExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        // 翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        // 排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<UserVO> doList2VOList(List<UserDO> list) {
        return CommonUtil.listConverter(list, UserConverter::do2VO);
    }
}
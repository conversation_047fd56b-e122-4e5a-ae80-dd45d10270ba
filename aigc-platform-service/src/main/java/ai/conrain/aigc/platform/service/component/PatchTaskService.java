package ai.conrain.aigc.platform.service.component;

import java.util.List;
import java.util.Map;

import ai.conrain.aigc.platform.service.model.request.ReCutoutSingleImageRequest;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;

public interface PatchTaskService {
    /**
     * 创建重新抠图补丁任务
     * @param request 单图重新抠图请求
     */
    void createRecutPatchTask(ReCutoutSingleImageRequest request);

    /**
     * 获取模型补丁任务列表
     *
     * @param id 模型id
     * @return 模型补丁任务列表
     */
    List<Map<String, Object>> getPatchCutoutTasksList(Integer id);

    /**
     * 根据任务id，删除补丁任务
     *
     * @param id     模型id
     * @param taskId 任务id
     * @return
     */
    Boolean deletePatchCutoutTask(Integer id,String taskId);

    /**
     * 处理模型补丁任务（重新抠图）
     * @param model 模型VO
     */
    void processPatchTasks(MaterialModelVO model);
}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.TrainPlanDO;
import ai.conrain.aigc.platform.service.model.query.TrainPlanQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainPlanVO;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version TrainPlanService.java v 0.1 2024-11-19 08:34:41
 */
public interface TrainPlanService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TrainPlanVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param trainPlan 对象参数
	 * @return 返回结果
	 */
	TrainPlanVO insert(TrainPlanVO trainPlan);

	TrainPlanVO createPlan(TrainPlanVO trainPlan);

	/**
	 * 修改对象
	 * @param trainPlan 对象参数
	 */
	void updateById(TrainPlanVO trainPlan);

	/**
	 * 全量查询
	 * return 结果
	 */
	List<TrainPlanVO> findAll();
}
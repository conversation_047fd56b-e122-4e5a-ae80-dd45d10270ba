package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateActiveVersionDO;
import ai.conrain.aigc.platform.service.model.biz.WorkflowOpenScopeModel;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateActiveVersionVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * ComfyuiWorkflowTemplateActiveVersionConverter
 *
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
public class ComfyuiWorkflowTemplateActiveVersionConverter {

    /**
     * DO -> VO
     */
    public static ComfyuiWorkflowTemplateActiveVersionVO do2VO(ComfyuiWorkflowTemplateActiveVersionDO from) {
        ComfyuiWorkflowTemplateActiveVersionVO to = new ComfyuiWorkflowTemplateActiveVersionVO();
        to.setId(from.getId());
        to.setTemplateKey(from.getTemplateKey());
        to.setTemplateDesc(from.getTemplateDesc());
        to.setActiveVersion(from.getActiveVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setTestVersion(from.getTestVersion());
        if (!ObjectUtils.isEmpty(from.getTestOpenScope())) {
            to.setTestOpenScope(JSONObject.parseObject(from.getTestOpenScope(), WorkflowOpenScopeModel.class));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static ComfyuiWorkflowTemplateActiveVersionDO vo2DO(ComfyuiWorkflowTemplateActiveVersionVO from) {
        ComfyuiWorkflowTemplateActiveVersionDO to = new ComfyuiWorkflowTemplateActiveVersionDO();
        to.setId(from.getId());
        to.setTemplateKey(from.getTemplateKey());
        to.setTemplateDesc(from.getTemplateDesc());
        to.setActiveVersion(from.getActiveVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setCreateBy(from.getCreateBy());
        to.setModifyBy(from.getModifyBy());
        to.setTestVersion(from.getTestVersion());
        if (!ObjectUtils.isEmpty(from.getTestVersion())) {
            to.setTestOpenScope(JSONObject.toJSONString(from.getTestOpenScope()));
        }
        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ComfyuiWorkflowTemplateActiveVersionVO> doList2VOList(List<ComfyuiWorkflowTemplateActiveVersionDO> list) {
        return CommonUtil.listConverter(list, ComfyuiWorkflowTemplateActiveVersionConverter::do2VO);
    }
}
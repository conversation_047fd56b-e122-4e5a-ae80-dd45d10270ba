package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.DistributorCustomerQuery;
import ai.conrain.aigc.platform.service.model.vo.DistributorCustomerVO;
import ai.conrain.aigc.platform.service.model.vo.DistributorMerchantVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;

import java.util.List;

/**
 * 渠道商客户信息 Service定义
 *
 * <AUTHOR>
 * @version DistributorCustomerService.java v 0.1 2024-07-09 05:15:57
 */
public interface DistributorCustomerService {
	
	/**
	 * 查询渠道商客户信息对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	DistributorCustomerVO selectById(Integer id);

	/**
	 * 删除渠道商客户信息对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加渠道商客户信息对象
	 * @param distributorCustomer 对象参数
	 * @return 返回结果
	 */
	DistributorCustomerVO insert(DistributorCustomerVO distributorCustomer);

	/**
	 * 修改渠道商客户信息对象
	 * @param distributorCustomer 对象参数
	 */
	void updateByIdSelective(DistributorCustomerVO distributorCustomer);

	/**
	 * 删除渠道商的员工与客户的关联信息（销售、运营离职的情况）
	 * @param staffId
	 */
	void onDeleteDistributorStaff(Integer staffId);

	/**
	 * 带条件批量查询渠道商客户信息列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<DistributorCustomerVO> queryDistributorCustomerList(DistributorCustomerQuery query);

	/**
	 * 带条件查询渠道商客户信息数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryDistributorCustomerCount(DistributorCustomerQuery query);

	/**
	 * 带条件分页查询渠道商客户信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<DistributorCustomerVO> queryDistributorCustomerByPage(DistributorCustomerQuery query);

	/**
	 * 查询当前用户权限下的渠道商客户信息列表
	 * @return
	 */
	List<DistributorCustomerVO> queryDistributorCustomersByCurrentUserAuth(boolean includeCreatedCustomer);

	/**
	 * 查询当前用户权限下的渠道商简易客户信息列表，id/nickName
	 * @return
	 */
	List<UserVO> queryCustomerUsersOptionsByCurrentUserAuth(boolean includeCreatedCustomer);

	/**
	 * 带条件分页查询渠道商客户信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<DistributorMerchantVO> queryDistributorMerchantByPage(DistributorCustomerQuery query);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import java.util.List;

import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;

/**
 * 任务派发
 *
 * <AUTHOR>
 * @version : TaskDispatch.java, v 0.1 2024/8/5 21:30 renxiao.wu Exp $
 */
public interface TaskDispatch {
    /**
     * 派发任务
     * <p>当返回为空时，表示服务器繁忙，无可用服务</p>
     *
     * @param task 数据模型
     * @return 服务配置
     */
    String dispatch(IExtModel task);

    /**
     * 指定服务派发任务
     * <p>当返回为空时，表示服务器繁忙，无可用服务</p>
     *
     * @param task   数据模型
     * @param server 服务配置
     * @return 服务配置
     */
    String dispatch(IExtModel task, ServerVO server);

    /**
     * 释放服务器
     *
     * @param task 数据模型
     */
    void release(IExtModel task);

    /**
     * 查询可用管道的空闲服务数据
     *
     * @return 管道闲置服务数据
     */
    List<PipelineVO> queryIdleServer();

    /**
     * 获取当前的任务派发类型
     *
     * @return 任务派发类型
     */
    DispatchTypeEnum getType();
}

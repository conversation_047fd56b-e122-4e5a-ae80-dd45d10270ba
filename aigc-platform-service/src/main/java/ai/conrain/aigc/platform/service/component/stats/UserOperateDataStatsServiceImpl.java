package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.StatsUserOperateService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.StatsUserOperateQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.StatsUserOperateVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("userOperateDataStatsServiceImpl")
public class UserOperateDataStatsServiceImpl extends AbstractDataStatsServiceImpl {

    @Autowired
    private UserService userService;
    @Autowired
    private StatsUserOperateService statsUserOperateService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.USER_OPERATE;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {
        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case DAILY:
                return executeDailyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case WEEKLY:
                return executeWeeklyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case MONTHLY:
                return executeMonthlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case TOTAL:
                return executeTotalBusinessStats(storageDate, startDate, endDate, periodEnum);
            default:
                return 0;
        }
    }

    /**
     * 执行每日业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  时间周期枚举
     * @return 变更条数
     */
    private int executeDailyBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum periodEnum) {
        // 查询销售列表
        UserQuery userQuery = new UserQuery();
        userQuery.setRoleType(RoleTypeEnum.DISTRIBUTOR.getCode());
        userQuery.setCustomRoleList(Arrays.asList(CustomRoleEnum.SALES_MEMBER.getCode(), CustomRoleEnum.OPS_MEMBER.getCode(), CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode()));
        List<Integer> saleIdList = userService.queryIdList(userQuery);

        log.info("查询到销售列表数量: {}, 销售ID列表: {}", saleIdList.size(), saleIdList);

        // 查询创作批次数据
        List<CreativeBatchVO> creativeBatchVOList = queryCreativeBatchData(startDate, endDate);
        if (CollectionUtils.isEmpty(creativeBatchVOList)) {
            return 0;
        }

        log.info("查询到创作批次数据数量: {}", creativeBatchVOList.size());

        // 按userId分组，但如果userId在销售列表中，则按operatorId分组（但operatorId不能为null）
        Map<Integer, List<CreativeBatchVO>> userGroupMap = creativeBatchVOList.stream()
                .collect(Collectors.groupingBy(batch -> {
                    Integer userId = batch.getUserId();
                    Integer operatorId = batch.getOperatorId();
                    // 如果userId是销售人员，则按operatorId分组（但operatorId不能为null）
                    if (saleIdList.contains(operatorId) && batch.getOperatorId() != null) {
                        return batch.getOperatorId();
                    }
                    // 否则按userId分组
                    return userId;
                }));


        // 按modelId分组(排除 modelId为null的批次数据)
        Map<Integer, List<CreativeBatchVO>> modelGroupMap = creativeBatchVOList.stream()
                .filter(batch -> batch.getModelId() != null)
                .collect(Collectors.groupingBy(CreativeBatchVO::getModelId));

        // 批量查询用户信息
        Map<Integer, UserVO> userMap = queryUserTypeMap(userGroupMap.keySet());

        // 批量查询服装名称信息
        Map<Integer, MaterialModelVO> modelMap = queryModelNameMap(modelGroupMap.keySet());

        // 统计用户操作数据
        List<StatsUserOperateVO> statsList = buildDailyStatsData(userGroupMap, userMap, modelMap, storageDate,
                periodEnum);

        // 批量保存数据
        return saveStatsData(statsList);
    }

    private Map<Integer, MaterialModelVO> queryModelNameMap(Set<Integer> modeIdList) {
        if (CollectionUtils.isEmpty(modeIdList)) {
            return new HashMap<>();
        }

        List<Integer> modelIdList = new ArrayList<>(modeIdList);
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setIds(modelIdList);
        List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);
        if (CollectionUtils.isEmpty(materialModelVOList)) {
            return new HashMap<>();
        }

        return materialModelVOList.stream().collect(Collectors.toMap(MaterialModelVO::getId, Function.identity()));
    }

    /**
     * 执行每周业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  时间周期枚举
     * @return 变更条数
     */
    private int executeWeeklyBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        return executePeriodicalBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行每月业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  时间周期枚举
     * @return 变更条数
     */
    private int executeMonthlyBusinessStats(String storageDate, String startDate, String endDate,
                                            StatsPeriodEnum periodEnum) {
        return executePeriodicalBusinessStats(storageDate, startDate, endDate, periodEnum);
    }

    /**
     * 执行总业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  时间周期枚举
     * @return 变更条数
     */
    private int executeTotalBusinessStats(String storageDate, String startDate, String endDate,
                                          StatsPeriodEnum periodEnum) {
        // 查询统计数据
        List<StatsUserOperateVO> statsUserOperateVOS = queryStatsUserOperateData(storageDate);
        if (CollectionUtils.isEmpty(statsUserOperateVOS)) {
            return 0;
        }

        // 按userId分组
        Map<Integer, List<StatsUserOperateVO>> userGroupMap = statsUserOperateVOS.stream()
                .collect(Collectors.groupingBy(StatsUserOperateVO::getUserId));

        // 批量查询用户信息
        Map<Integer, UserVO> userMap = queryUserTypeMap(userGroupMap.keySet());

        // 构建总计统计数据
        List<StatsUserOperateVO> statsList = buildTotalStatsData(userGroupMap, userMap, storageDate, periodEnum);

        // 批量保存数据
        return saveStatsData(statsList);
    }

    /**
     * 执行周期性业务统计(周期和月度共用逻辑)
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  时间周期枚举
     * @return 变更条数
     */
    private int executePeriodicalBusinessStats(String storageDate, String startDate, String endDate,
                                               StatsPeriodEnum periodEnum) {
        // 查询统计数据
        List<StatsUserOperateVO> statsUserOperateVOList = queryStatsUserOperateData(startDate, endDate);
        if (CollectionUtils.isEmpty(statsUserOperateVOList)) {
            return 0;
        }

        // 按userId分组
        Map<Integer, List<StatsUserOperateVO>> userGroupMap = statsUserOperateVOList.stream()
                .collect(Collectors.groupingBy(StatsUserOperateVO::getUserId));

        // 批量查询用户信息
        Map<Integer, UserVO> userMap = queryUserTypeMap(userGroupMap.keySet());

        // 构建总计统计数据
        List<StatsUserOperateVO> statsList = buildTotalStatsData(userGroupMap, userMap, storageDate, periodEnum);

        // 批量保存数据
        return saveStatsData(statsList);
    }

    /**
     * 查询创作批次数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 过滤后的创作批次列表
     */
    private List<CreativeBatchVO> queryCreativeBatchData(String startDate, String endDate) {
        // 封装查询条件
        CreativeBatchQuery creativeBatchQuery = new CreativeBatchQuery();
        creativeBatchQuery.setStartTime(startDate);
        creativeBatchQuery.setEndTime(endDate);
        creativeBatchQuery.setStatus(CreativeStatusEnum.FINISHED.getCode());

        // 查询出符合要求的数据
        List<CreativeBatchVO> creativeBatchVOList = creativeBatchService.queryCreativeBatchList(creativeBatchQuery);

        if (CollectionUtils.isEmpty(creativeBatchVOList)) {
            log.info("没有查询到创作批次数据，查询条件：startDate={}, endDate={}", startDate, endDate);
            return new ArrayList<>();
        }

        return creativeBatchVOList;
    }

    /**
     * 查询用户信息映射
     *
     * @param userIds 用户ID集合
     * @return 用户ID到用户信息的映射
     */
    private Map<Integer, UserVO> queryUserTypeMap(Set<Integer> userIds) {
        List<Integer> userIdList = new ArrayList<>(userIds);
        List<UserVO> userList = userService.batchQueryById(userIdList);
        if (CollectionUtils.isEmpty(userList)) {
            return new HashMap<>();
        }

        return userList.stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
    }

    /**
     * 构建每日统计数据
     *
     * @param userGroupMap 用户分组的批次数据
     * @param userMap      用户信息映射
     * @param storageDate  存储日期
     * @param periodEnum   时间周期枚举
     * @return 统计数据列表
     */
    private List<StatsUserOperateVO> buildDailyStatsData(
            Map<Integer, List<CreativeBatchVO>> userGroupMap,
            Map<Integer, UserVO> userMap,
            Map<Integer, MaterialModelVO> modelMap,
            String storageDate,
            StatsPeriodEnum periodEnum) {

        List<StatsUserOperateVO> statsList = new ArrayList<>();

        // 处理每个用户的数据
        for (Map.Entry<Integer, List<CreativeBatchVO>> userEntry : userGroupMap.entrySet()) {
            Integer userId = userEntry.getKey();
            List<CreativeBatchVO> userBatches = userEntry.getValue();

            // 获取用户信息，增加对userMap的null检查
            String userType = "MASTER"; // 默认值，与数据库表默认值保持一致
            String userNickname = null;
            String loginId = null;
            if (userMap != null) {
                UserVO userVO = userMap.get(userId);
                userType = userVO != null && userVO.getUserType() != null ? userVO.getUserType().getCode() : "MASTER";
                userNickname = userVO != null ? userVO.getNickName() : null;
                loginId = userVO != null ? userVO.getLoginId() : null;
            }

            // 将modelId为null的批次数据单独收集
            List<CreativeBatchVO> nullModelBatches = new ArrayList<>();
            List<CreativeBatchVO> validModelBatches = new ArrayList<>();

            for (CreativeBatchVO batch : userBatches) {
                if (batch.getModelId() == null) {
                    nullModelBatches.add(batch);
                } else {
                    validModelBatches.add(batch);
                }
            }

            // 处理modelId为null的批次数据
            if (!nullModelBatches.isEmpty()) {
                // 统计创建数和下载数
                int[] counts = calculateCounts(nullModelBatches);
                int createCount = counts[0];
                int downloadCount = counts[1];

                // 收集所有batchId
                List<Integer> batchIds = nullModelBatches.stream().map(CreativeBatchVO::getId)
                        .collect(Collectors.toList());

                // 创建统计对象
                StatsUserOperateVO statsVO = new StatsUserOperateVO();
                statsVO.setStatsType(periodEnum.getCode());
                statsVO.setStatsDate(storageDate);
                statsVO.setUserId(userId);
                statsVO.setMaterialId(-1); // 设置materialId为-1表示null模型
                statsVO.setUserType(userType);
                statsVO.setCreateCount(createCount);
                statsVO.setDownloadCount(downloadCount);
                // 统计用户信息至扩展信息中
                statsVO.addExtInfo(CommonConstants.KEY_BATCH_ID_LIST, batchIds);
                statsVO.addExtInfo(CommonConstants.KEY_NICKNAME, userNickname);
                statsVO.addExtInfo(CommonConstants.KEY_LOGIN_ID, loginId);
                // 统计服装名称信息(modelId 为空不会存在名称)
                statsVO.addExtInfo(CommonConstants.KEY_MODEL_NAME, null);
                statsVO.setCreateTime(new Date());
                statsVO.setModifyTime(new Date());

                // 直接添加到结果列表中
                statsList.add(statsVO);
            }

            // 处理有效的modelId数据
            if (!validModelBatches.isEmpty()) {
                // 按modelId进一步分组
                Map<Integer, List<CreativeBatchVO>> modelGroupMap = validModelBatches.stream()
                        .collect(Collectors.groupingBy(CreativeBatchVO::getModelId));

                for (Map.Entry<Integer, List<CreativeBatchVO>> modelEntry : modelGroupMap.entrySet()) {
                    Integer modelId = modelEntry.getKey();
                    List<CreativeBatchVO> modelBatches = modelEntry.getValue();

                    // 统计创建数和下载数
                    int[] counts = calculateCounts(modelBatches);
                    int createCount = counts[0];
                    int downloadCount = counts[1];

                    // 创建统计对象
                    StatsUserOperateVO statsVO = createStatsVO(
                            userId, modelId, createCount, downloadCount,
                            userType, userNickname, loginId, storageDate, periodEnum, modelBatches, modelMap);

                    statsList.add(statsVO);
                }
            }
        }

        return statsList;
    }

    /**
     * 计算创建数和下载数
     *
     * @param modelBatches 模型批次列表
     * @return 包含创建数和下载数的数组
     */
    private int[] calculateCounts(List<CreativeBatchVO> modelBatches) {
        int createCount = 0;
        int downloadCount = 0;

        for (CreativeBatchVO batch : modelBatches) {
            // 累计创建数 - 使用resultImages长度
            List<String> resultImages = batch.getResultImages();
            if (resultImages != null && !resultImages.isEmpty()) {
                createCount += resultImages.size();
            }

            // 累计下载数 - 从extInfo中获取downloadedImgs
            if (batch.getExtInfo() != null) {
                List<String> downloadedImgs = batch.getExtInfo(CommonConstants.DOWNLOADED_IMAGE,
                        JSONArray.class) != null
                        ? batch.getExtInfo(CommonConstants.DOWNLOADED_IMAGE, JSONArray.class)
                        .toJavaList(String.class)
                        : null;
                if (downloadedImgs != null && !downloadedImgs.isEmpty()) {
                    downloadCount += downloadedImgs.size();
                }
            }
        }

        return new int[]{createCount, downloadCount};
    }

    /**
     * 创建统计对象
     *
     * @param userId        用户ID
     * @param modelId       模型ID
     * @param createCount   创建数
     * @param downloadCount 下载数
     * @param userType      用户类型
     * @param userNickname  用户昵称
     * @param loginId       登录ID
     * @param statsDate     统计日期
     * @param periodEnum    时间周期枚举
     * @param modelBatches  模型批次列表
     * @param modelMap      模型信息映射
     * @return 统计对象
     */
    private StatsUserOperateVO createStatsVO(
            Integer userId, Integer modelId,
            int createCount, int downloadCount,
            String userType, String userNickname,
            String loginId, String statsDate,
            StatsPeriodEnum periodEnum,
            List<CreativeBatchVO> modelBatches,
            Map<Integer, MaterialModelVO> modelMap) {

        // 获取服装名称
        String modelName = null;
        String showImage = null;
        if (modelMap != null) {
            MaterialModelVO modelVO = modelMap.get(modelId);
            modelName = modelVO != null ? modelVO.getName() : null;
            showImage = modelVO != null ? modelVO.getShowImage() : null;
        }

        // 所有的 batchId
        List<Integer> batchIds = modelBatches.stream().map(CreativeBatchVO::getId).collect(Collectors.toList());

        StatsUserOperateVO statsVO = new StatsUserOperateVO();
        statsVO.setStatsType(periodEnum.getCode());
        statsVO.setStatsDate(statsDate);
        statsVO.setUserId(userId);
        // 如果modelId为0，表示这是null模型的汇总记录
        statsVO.setMaterialId(modelId);
        statsVO.setUserType(userType);
        statsVO.setCreateCount(createCount);
        statsVO.setDownloadCount(downloadCount);
        statsVO.addExtInfo(CommonConstants.KEY_BATCH_ID_LIST, batchIds);
        statsVO.addExtInfo(CommonConstants.KEY_MODEL_NAME, modelName);
        statsVO.addExtInfo(CommonConstants.KEY_SHOW_IMAGE, showImage);
        statsVO.addExtInfo(CommonConstants.KEY_NICKNAME, userNickname);
        statsVO.addExtInfo(CommonConstants.KEY_LOGIN_ID, loginId);
        statsVO.setCreateTime(new Date());
        statsVO.setModifyTime(new Date());

        return statsVO;
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    private int saveStatsData(List<StatsUserOperateVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            return statsUserOperateService.batchInsertOrUpdate(statsList);
        }
        return 0;
    }

    /**
     * 查询用户操作统计数据
     *
     * @param storageDate 存储日期
     * @return 统计数据列表
     */
    private List<StatsUserOperateVO> queryStatsUserOperateData(String storageDate) {
        // 封装查询条件
        StatsUserOperateQuery statsUserOperateQuery = new StatsUserOperateQuery();
        statsUserOperateQuery.setStatsDate(storageDate);
        statsUserOperateQuery.setStatsType(StatsPeriodEnum.DAILY.getCode());

        // 查询出符合要求的数据
        List<StatsUserOperateVO> statsUserOperateVOS = statsUserOperateService
                .queryStatsUserOperateList(statsUserOperateQuery);

        if (CollectionUtils.isEmpty(statsUserOperateVOS)) {
            log.info("没有查询到用户操作统计数据，查询条件：storageDate={}", storageDate);
            return new ArrayList<>();
        }

        return statsUserOperateVOS;
    }

    /**
     * 查询用户操作统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计数据列表
     */
    private List<StatsUserOperateVO> queryStatsUserOperateData(String startDate, String endDate) {
        // 封装查询条件
        StatsUserOperateQuery statsUserOperateQuery = new StatsUserOperateQuery();
        statsUserOperateQuery.setStartDate(startDate);
        statsUserOperateQuery.setEndDate(endDate);
        statsUserOperateQuery.setStatsType(StatsPeriodEnum.DAILY.getCode());

        // 查询出符合要求的数据
        List<StatsUserOperateVO> statsUserOperateVOS = statsUserOperateService
                .queryStatsUserOperateList(statsUserOperateQuery);

        if (CollectionUtils.isEmpty(statsUserOperateVOS)) {
            log.info("没有查询到用户操作统计数据，查询条件：startDate={},endDate={}", startDate, endDate);
            return new ArrayList<>();
        }

        return statsUserOperateVOS;
    }

    /**
     * 构建总计统计数据
     *
     * @param userGroupMap 用户分组的统计数据
     * @param userMap      用户信息映射
     * @param storageDate  存储日期
     * @param periodEnum   时间周期枚举
     * @return 统计数据列表
     */
    private List<StatsUserOperateVO> buildTotalStatsData(
            Map<Integer, List<StatsUserOperateVO>> userGroupMap,
            Map<Integer, UserVO> userMap,
            String storageDate,
            StatsPeriodEnum periodEnum) {

        // 使用流操作处理每个用户
        return userGroupMap.entrySet().stream().map(userEntry -> {
            Integer userId = userEntry.getKey();
            List<StatsUserOperateVO> userStatsList = userEntry.getValue();

            // 获取用户信息
            String userType = "MASTER"; // 默认值，与数据库表默认值保持一致
            String userNickname = null;
            String loginId = null;
            if (userMap != null) {
                UserVO userVO = userMap.get(userId);
                userType = userVO != null && userVO.getUserType() != null ? userVO.getUserType().getCode() : "MASTER";
                userNickname = userVO != null ? userVO.getNickName() : null;
                loginId = userVO != null ? userVO.getLoginId() : null;
            }

            // 计算总数
            int createCount = userStatsList.stream()
                    .mapToInt(StatsUserOperateVO::getCreateCount)
                    .sum();

            int downloadCount = userStatsList.stream()
                    .mapToInt(StatsUserOperateVO::getDownloadCount)
                    .sum();

            // 创建总计统计对象
            StatsUserOperateVO statsVO = new StatsUserOperateVO();
            statsVO.setStatsType(periodEnum.getCode());
            statsVO.setStatsDate(storageDate);
            statsVO.setUserId(userId);
            // 总统计时，materialId为0
            statsVO.setMaterialId(0);
            statsVO.setUserType(userType);
            statsVO.setCreateCount(createCount);
            statsVO.setDownloadCount(downloadCount);

            // 添加扩展信息
            statsVO.addExtInfo(CommonConstants.KEY_NICKNAME, userNickname);
            statsVO.addExtInfo(CommonConstants.KEY_LOGIN_ID, loginId);

            statsVO.setCreateTime(new Date());
            statsVO.setModifyTime(new Date());

            return statsVO;
        }).collect(Collectors.toList());
    }

}

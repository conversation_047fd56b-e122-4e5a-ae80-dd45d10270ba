package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.AssessmentPlanDO;
import ai.conrain.aigc.platform.service.enums.AssessStatusEnum;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.enums.SalesAssessTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.DistributorEnhancedVO;
import ai.conrain.aigc.platform.service.model.biz.DistributorSettleConfigVO;
import ai.conrain.aigc.platform.service.model.biz.KPIModel;
import ai.conrain.aigc.platform.service.model.query.AssessmentPlanQuery;
import ai.conrain.aigc.platform.dal.example.AssessmentPlanExample;
import ai.conrain.aigc.platform.service.model.vo.AssessmentPlanVO;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * AssessmentPlanConverter
 *
 * @version AssessmentPlanService.java v 0.1 2025-05-22 07:34:51
 */
public class AssessmentPlanConverter {

    /**
     * DO -> VO
     */
    public static AssessmentPlanVO do2VO(AssessmentPlanDO from) {
        AssessmentPlanVO to = new AssessmentPlanVO();
        to.setId(from.getId());
        to.setPrincipalType(PrincipalTypeEnum.getByCode(from.getPrincipalType()));
        to.setPrincipalId(from.getPrincipalId());
        to.setType(SalesAssessTypeEnum.getByCode(from.getType()));
        to.setStatus(AssessStatusEnum.getByCode(from.getStatus()));
        to.setPlanFromDate(from.getPlanFromDate());
        to.setPlanEndDate(from.getPlanEndDate());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setKpiTarget(JSONObject.parseObject(from.getKpiTarget(), KPIModel.class));
        to.setKpiActual(JSONObject.parseObject(from.getKpiActual(), KPIModel.class));
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static AssessmentPlanDO vo2DO(AssessmentPlanVO from) {
        AssessmentPlanDO to = new AssessmentPlanDO();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType().getCode());
        to.setPrincipalId(from.getPrincipalId());
        to.setType(from.getType().getCode());
        to.setStatus(from.getStatus().getCode());
        to.setPlanFromDate(from.getPlanFromDate());
        to.setPlanEndDate(from.getPlanEndDate());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        if (!ObjectUtils.isEmpty(from.getKpiTarget())) {
            to.setKpiTarget(JSONObject.toJSONString(from.getKpiTarget()));
        }
        if (!ObjectUtils.isEmpty(from.getKpiActual())) {
            to.setKpiActual(JSONObject.toJSONString(from.getKpiActual()));
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static AssessmentPlanQuery do2Query(AssessmentPlanDO from) {
        AssessmentPlanQuery to = new AssessmentPlanQuery();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setPlanFromDate(from.getPlanFromDate());
        to.setPlanEndDate(from.getPlanEndDate());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setKpiTarget(from.getKpiTarget());
        to.setKpiActual(from.getKpiActual());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static AssessmentPlanDO query2DO(AssessmentPlanQuery from) {
        AssessmentPlanDO to = new AssessmentPlanDO();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType());
        to.setPrincipalId(from.getPrincipalId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setPlanFromDate(from.getPlanFromDate());
        to.setPlanEndDate(from.getPlanEndDate());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setKpiTarget(from.getKpiTarget());
        to.setKpiActual(from.getKpiActual());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static AssessmentPlanExample query2Example(AssessmentPlanQuery from) {
        AssessmentPlanExample to = new AssessmentPlanExample();
        AssessmentPlanExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalType())) {
            c.andPrincipalTypeEqualTo(from.getPrincipalType());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalId())) {
            c.andPrincipalIdEqualTo(from.getPrincipalId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getPlanFromDate())) {
            c.andPlanFromDateEqualTo(from.getPlanFromDate());
        }
        if (!ObjectUtils.isEmpty(from.getPlanEndDate())) {
            c.andPlanEndDateEqualTo(from.getPlanEndDate());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorUserId())) {
            c.andCreatorUserIdEqualTo(from.getCreatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getModifyUserId())) {
            c.andModifyUserIdEqualTo(from.getModifyUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (AssessmentPlanExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<AssessmentPlanVO> doList2VOList(List<AssessmentPlanDO> list) {
        return CommonUtil.listConverter(list, AssessmentPlanConverter::do2VO);
    }

}
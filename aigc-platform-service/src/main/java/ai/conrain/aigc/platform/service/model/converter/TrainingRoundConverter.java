package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.dal.example.TrainingRoundExample;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundStatus;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;

import ai.conrain.aigc.platform.service.model.vo.training.TrainingConfig;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * TrainingRoundConverter
 *
 * @version TrainingRoundService.java
 */
public class TrainingRoundConverter {

    /**
     * DO -> VO
     */
    public static TrainingRoundVO do2VO(TrainingRoundDO from) {
        TrainingRoundVO to = new TrainingRoundVO();
        to.setId(from.getId());
        to.setTaskId(from.getTaskId());
        to.setRoundNumber(from.getRoundNumber());
        to.setStatus(TrainingRoundStatus.getByCode(from.getStatus()));
        to.setTrainingConfig(CommonUtil.parseObject(from.getTrainingConfig(), TrainingConfig.class));
        to.setTrainingSampleExport(from.getTrainingSampleExport());
        to.setTrainStatus(from.getTrainStatus());
        to.setTrainStartedTime(from.getTrainStartedTime());
        to.setTrainCompletedTime(from.getTrainCompletedTime());
        to.setModelScene(from.getModelScene());
        to.setModelVersion(from.getModelVersion());
        to.setModelFilePath(from.getModelFilePath());
        to.setModelUrl(from.getModelUrl());
        to.setModelPerformanceMetrics(from.getModelPerformanceMetrics());
        to.setExtInfo(CommonUtil.parseObject(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TrainingRoundDO vo2DO(TrainingRoundVO from) {
        TrainingRoundDO to = new TrainingRoundDO();
        to.setId(from.getId());
        to.setTaskId(from.getTaskId());
        to.setRoundNumber(from.getRoundNumber());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setTrainingConfig(CommonUtil.toJSONString(from.getTrainingConfig()));
        to.setTrainingSampleExport(from.getTrainingSampleExport());
        to.setTrainStatus(from.getTrainStatus());
        to.setTrainStartedTime(from.getTrainStartedTime());
        to.setTrainCompletedTime(from.getTrainCompletedTime());
        to.setModelScene(from.getModelScene());
        to.setModelVersion(from.getModelVersion());
        to.setModelFilePath(from.getModelFilePath());
        to.setModelUrl(from.getModelUrl());
        to.setModelPerformanceMetrics(from.getModelPerformanceMetrics());
        to.setExtInfo(CommonUtil.toJSONString(from.getExtInfo()));
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TrainingRoundExample query2Example(TrainingRoundQuery from) {
        TrainingRoundExample to = new TrainingRoundExample();
        TrainingRoundExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getTaskId())) {
            c.andTaskIdEqualTo(from.getTaskId());
        }
        if (!ObjectUtils.isEmpty(from.getRoundNumber())) {
            c.andRoundNumberEqualTo(from.getRoundNumber());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getTrainingConfig())) {
            c.andTrainingConfigEqualTo(from.getTrainingConfig());
        }
        if (!ObjectUtils.isEmpty(from.getTrainingSampleExport())) {
            c.andTrainingSampleExportEqualTo(from.getTrainingSampleExport());
        }
        if (!ObjectUtils.isEmpty(from.getTrainStatus())) {
            c.andTrainStatusEqualTo(from.getTrainStatus());
        }
        if (!ObjectUtils.isEmpty(from.getTrainStartedTime())) {
            c.andTrainStartedTimeEqualTo(from.getTrainStartedTime());
        }
        if (!ObjectUtils.isEmpty(from.getTrainCompletedTime())) {
            c.andTrainCompletedTimeEqualTo(from.getTrainCompletedTime());
        }
        if (!ObjectUtils.isEmpty(from.getModelScene())) {
            c.andModelSceneEqualTo(from.getModelScene());
        }
        if (!ObjectUtils.isEmpty(from.getModelVersion())) {
            c.andModelVersionEqualTo(from.getModelVersion());
        }
        if (!ObjectUtils.isEmpty(from.getModelFilePath())) {
            c.andModelFilePathEqualTo(from.getModelFilePath());
        }
        if (!ObjectUtils.isEmpty(from.getModelUrl())) {
            c.andModelUrlEqualTo(from.getModelUrl());
        }
        if (!ObjectUtils.isEmpty(from.getModelPerformanceMetrics())) {
            c.andModelPerformanceMetricsEqualTo(from.getModelPerformanceMetrics());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<TrainingRoundVO> doList2VOList(List<TrainingRoundDO> list) {
        return CommonUtil.listConverter(list, TrainingRoundConverter::do2VO);
    }
}
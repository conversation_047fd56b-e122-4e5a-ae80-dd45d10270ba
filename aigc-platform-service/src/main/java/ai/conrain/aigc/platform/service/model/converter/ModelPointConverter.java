package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ModelPointDO;
import ai.conrain.aigc.platform.service.model.query.ModelPointQuery;
import ai.conrain.aigc.platform.dal.example.ModelPointExample;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ModelPointConverter
 *
 * @version ModelPointService.java v 0.1 2024-06-21 12:01:15
 */
public class ModelPointConverter {

    /**
     * DO -> VO
     */
    public static ModelPointVO do2VO(ModelPointDO from) {
        ModelPointVO to = new ModelPointVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ModelPointDO vo2DO(ModelPointVO from) {
        ModelPointDO to = new ModelPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static ModelPointQuery do2Query(ModelPointDO from) {
        ModelPointQuery to = new ModelPointQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static ModelPointDO query2DO(ModelPointQuery from) {
        ModelPointDO to = new ModelPointDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setPoint(from.getPoint());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static ModelPointExample query2Example(ModelPointQuery from) {
        ModelPointExample to = new ModelPointExample();
        ModelPointExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getModelId())) {
            c.andModelIdEqualTo(from.getModelId());
        }
        if (!ObjectUtils.isEmpty(from.getPoint())) {
            c.andPointEqualTo(from.getPoint());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ModelPointVO> doList2VOList(List<ModelPointDO> list) {
        return CommonUtil.listConverter(list, ModelPointConverter::do2VO);
    }
}
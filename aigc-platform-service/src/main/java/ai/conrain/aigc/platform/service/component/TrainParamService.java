package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.vo.TrainParamVO;

import java.util.List;

/**
 *  Service定义
 *
 * <AUTHOR>
 * @version TrainParamService.java v 0.1 2024-11-19 08:34:41
 */
public interface TrainParamService {
	
	/**
	 * 查询对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TrainParamVO selectById(Integer id);

	/**
	 * 删除对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加对象
	 * @param trainParam 对象参数
	 * @return 返回结果
	 */
	TrainParamVO insert(TrainParamVO trainParam);

	/**
	 * 修改对象
	 * @param trainParam 对象参数
	 */
	void updateById(TrainParamVO trainParam);

	/**
	 * 全量查询
	 * return 结果
	 */
	List<TrainParamVO> findAll();

	List<TrainParamVO> findAllByPlanId(Integer planId);
}
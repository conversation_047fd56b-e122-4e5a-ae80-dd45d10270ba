package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.PermissionDO;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.PermissionVO;
import java.util.List;

/**
 * 权限配置 Service定义
 *
 * <AUTHOR>
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
public interface PermissionService extends CachedService<PermissionVO, String, List<RoleTypeEnum>>{
	
	/**
	 * 查询权限配置对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	PermissionVO selectById(Integer id);

	/**
	 * 删除权限配置对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加权限配置对象
	 * @param permission 对象参数
	 * @return 返回结果
	 */
	PermissionVO insert(PermissionVO permission);

	/**
	 * 修改权限配置对象
	 * @param permission 对象参数
	 */
	void updateById(PermissionVO permission);


	/**
	 * 初始化所有的权限模型
	 *
	 * @param list 权限模型列表
	 */
	void initPermissions(List<PermissionDO> list);

	/**
	 * 当前请求是否有权限
	 *
	 * @param requestPath 请求路径
	 * @return 是否有权限
	 */
	boolean hasPermission(String requestPath);

	/**
	 * 判断当前请求是否操作类型
	 *
	 * @param requestPath 请求路径
	 * @return 是否操作类型，true操作类型
	 */
	boolean isOperateAction(String requestPath);

	/**
	 * 解析请求地址
	 *
	 * @param requestPath 原始请求地址
	 * @return 解析后地址
	 */
	String parseRequestPath(String requestPath);
}
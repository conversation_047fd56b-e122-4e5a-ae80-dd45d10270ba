package ai.conrain.aigc.platform.service.model.common;

import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = -6242080705234870445L;

    //是否成功
    private boolean success;

    //返回码
    private ResultCode code;

    //前端文案
    private String message;

    //内部memo,预留字段
    private String memo;

    private T data;

    /** 版本号，某些需要与前端根据版本号拉取数据场景使用 */
    private Long version;

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        Result<T> r = new Result<T>();
        r.setSuccess(true);
        r.setCode(ResultCode.SUCCESS);
        r.setMessage(ResultCode.SUCCESS.getDesc());
        r.setData(data);

        return r;
    }

    public static <T> Result<T> error(String msg){

        Result<T> r = new Result<T>();
        r.setSuccess(false);
        r.setCode(ResultCode.SYS_ERROR);
        r.setMessage(msg);

        return r;
    }

    public static <T> Result<T> success(T data, Long version) {
        Result<T> result = success(data);
        result.setVersion(version);
        return result;
    }

    public static <T> Result<T> failedWithMessage(ResultCode code, String msg) {

        AssertUtil.assertNotNull(code, "code不可为空");

        Result<T> r = new Result<T>();
        r.setSuccess(false);
        r.setCode(code);
        r.setMessage(Objects.toString(msg, code.getDesc()));

        return r;
    }
}

package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.OrderInfoDO;
import ai.conrain.aigc.platform.dal.example.OrderInfoExample;
import ai.conrain.aigc.platform.service.constants.PayType;
import ai.conrain.aigc.platform.service.enums.InvoiceStatus;
import ai.conrain.aigc.platform.service.model.query.OrderInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.OrderInfoVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * OrderInfoConverter
 *
 * @version OrderInfoService.java v 0.1 2024-06-20 11:43:37
 */
public class OrderInfoConverter {

    /**
     * DO -> VO
     */
    public static OrderInfoVO do2VO(OrderInfoDO from) {
        OrderInfoVO to = new OrderInfoVO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setMasterCorpName(from.getMasterCorpName());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setOriginalAmount(from.getOriginalAmount());
        to.setPayAmount(from.getPayAmount());
        to.setPayDetail(from.getPayDetail());
        to.setOrderStatus(from.getOrderStatus());
        to.setProductCode(from.getProductCode());
        to.setProductDetail(from.getProductDetail());
        to.setFinishTime(from.getFinishTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExpireTime(getExpireTime(from.getCreateTime()));

        to.setProductName(CommonUtil.getProductShowName(to));

        to.setDistributorMasterUserId(from.getDistributorMasterUserId());
        to.setDistributorCorpName(from.getDistributorCorpName());

        if (StringUtils.isNotBlank(from.getInvoiceStatus()) && InvoiceStatus.getByCode(from.getInvoiceStatus()) != null) {
            to.setInvoiceStatusName(Objects.requireNonNull(InvoiceStatus.getByCode(from.getInvoiceStatus())).getDesc());
        } else {
            to.setInvoiceStatus(InvoiceStatus.NONE.getCode());
            to.setInvoiceStatusName(InvoiceStatus.NONE.getDesc());
        }
        to.setInvoiceStatus(from.getInvoiceStatus());
        to.setInvoiceFileUrl(from.getInvoiceFileUrl());

        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setDistributorSalesNickName(from.getDistributorSalesNickName());

        to.setDistributorSalesOrgId(from.getDistributorSalesOrgId());
        to.setDistributorSalesOrgName(from.getDistributorSalesOrgName());

        to.setDistributorCorpOrgId(from.getDistributorCorpOrgId());

        to.setPayType(PayType.parsePayTypeByPayDetail(from.getPayDetail()));

        return to;
    }

    private static String getExpireTime(Date createTime){
        if (createTime != null) {
            Date ret = DateUtils.addYears(createTime, 1);
            return DateUtils.formatSimpleDate(ret);
        }
        return null;
    }

    /**
     * VO -> DO
     */
    public static OrderInfoDO vo2DO(OrderInfoVO from) {
        OrderInfoDO to = new OrderInfoDO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterCorpName(from.getMasterCorpName());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setOriginalAmount(from.getOriginalAmount());
        to.setPayAmount(from.getPayAmount());
        to.setPayDetail(from.getPayDetail());
        to.setOrderStatus(from.getOrderStatus());
        to.setProductCode(from.getProductCode());
        to.setProductDetail(from.getProductDetail());
        to.setFinishTime(from.getFinishTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDistributorSalesUserId(from.getDistributorSalesUserId());
        to.setDistributorSalesNickName(from.getDistributorSalesNickName());

        return to;
    }

    /**
     * DO -> Query
     */
    public static OrderInfoQuery do2Query(OrderInfoDO from) {
        OrderInfoQuery to = new OrderInfoQuery();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setOriginalAmount(from.getOriginalAmount());
        to.setPayAmount(from.getPayAmount());
        to.setPayDetail(from.getPayDetail());
        to.setOrderStatus(from.getOrderStatus());
        to.setProductCode(from.getProductCode());
        to.setProductDetail(from.getProductDetail());
        to.setFinishTime(from.getFinishTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static OrderInfoDO query2DO(OrderInfoQuery from) {
        OrderInfoDO to = new OrderInfoDO();
        to.setId(from.getId());
        to.setOrderNo(from.getOrderNo());
        to.setMasterUserId(from.getMasterUserId());
        to.setMasterUserNick(from.getMasterUserNick());
        to.setMasterUserLoginId(from.getMasterUserLoginId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setOperatorUserNick(from.getOperatorUserNick());
        to.setOperatorUserLoginId(from.getOperatorUserLoginId());
        to.setOriginalAmount(from.getOriginalAmount());
        to.setPayAmount(from.getPayAmount());
        to.setPayDetail(from.getPayDetail());
        to.setOrderStatus(from.getOrderStatus());
        to.setProductCode(from.getProductCode());
        to.setProductDetail(from.getProductDetail());
        to.setFinishTime(from.getFinishTime());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static OrderInfoExample query2Example(OrderInfoQuery from) {
        OrderInfoExample to = new OrderInfoExample();
        OrderInfoExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getOrderNo())) {
            c.andOrderNoEqualTo(from.getOrderNo());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserId())) {
            c.andMasterUserIdEqualTo(from.getMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserIds())) {
            c.andMasterUserIdIn(from.getMasterUserIds());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserNick())) {
            c.andMasterUserNickEqualTo(from.getMasterUserNick());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserLoginId())) {
            c.andMasterUserLoginIdEqualTo(from.getMasterUserLoginId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserId())) {
            c.andOperatorUserIdEqualTo(from.getOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserNick())) {
            c.andOperatorUserNickEqualTo(from.getOperatorUserNick());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserLoginId())) {
            c.andOperatorUserLoginIdEqualTo(from.getOperatorUserLoginId());
        }
        if (!ObjectUtils.isEmpty(from.getOriginalAmount())) {
            c.andOriginalAmountEqualTo(from.getOriginalAmount());
        }
        if (!ObjectUtils.isEmpty(from.getPayAmount())) {
            c.andPayAmountEqualTo(from.getPayAmount());
        }
        if (!ObjectUtils.isEmpty(from.getPayDetail())) {
            c.andPayDetailEqualTo(from.getPayDetail());
        }
        if (!ObjectUtils.isEmpty(from.getOrderStatus())) {
            c.andOrderStatusEqualTo(from.getOrderStatus());
        }
        if (!ObjectUtils.isEmpty(from.getProductCode())) {
            c.andProductCodeEqualTo(from.getProductCode());
        }
        if (!ObjectUtils.isEmpty(from.getProductDetail())) {
            c.andProductDetailEqualTo(from.getProductDetail());
        }
        if (!ObjectUtils.isEmpty(from.getFinishTime())) {
            c.andFinishTimeEqualTo(from.getFinishTime());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getDateFrom())) {
            c.andCreateTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getDateFrom()));
        }

        if (!ObjectUtils.isEmpty(from.getDateTo())) {
            c.andCreateTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getDateTo()));
        }

        if (!ObjectUtils.isEmpty(from.getStartDate())) {
            c.andCreateTimeGreaterThanOrEqualTo(DateUtils.parseSimpleDateWithFormats(from.getStartDate()));
        }

        if (!ObjectUtils.isEmpty(from.getEndDate())) {
            c.andCreateTimeLessThanOrEqualTo(DateUtils.parseSimpleLastTime(from.getEndDate()));
        }

        if (!ObjectUtils.isEmpty(from.getBigThanAmount())){
            c.andPayAmountGreaterThanOrEqualTo(from.getBigThanAmount());
        }

        //逻辑删除过滤
        for (OrderInfoExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<OrderInfoVO> doList2VOList(List<OrderInfoDO> list) {
        return CommonUtil.listConverter(list, OrderInfoConverter::do2VO);
    }
}
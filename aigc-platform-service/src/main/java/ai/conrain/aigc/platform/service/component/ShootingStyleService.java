package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.KeyValue;
import ai.conrain.aigc.platform.service.model.query.ShootingStyleQuery;
import ai.conrain.aigc.platform.service.model.vo.ShootingStyleVO;

import java.util.List;
import java.util.Map;

/**
 * 拍摄风格配置 Service定义
 *
 * <AUTHOR>
 * @version ShootingStyleService.java v 0.1 2025-07-04 05:16:37
 */
public interface ShootingStyleService {
	
	/**
	 * 查询拍摄风格配置对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ShootingStyleVO selectById(Integer id);

	/**
	 * 删除拍摄风格配置对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加拍摄风格配置对象
	 * @param shootingStyle 对象参数
	 * @return 返回结果
	 */
	ShootingStyleVO insert(ShootingStyleVO shootingStyle);

	/**
	 * 修改拍摄风格配置对象
	 * @param shootingStyle 对象参数
	 */
	void updateById(ShootingStyleVO shootingStyle);

	/**
	 * 全量查询
	 * return 结果
	 */
	List<ShootingStyleVO> findAll();


	/**
	 * 获取构建prompt数据
	 * @return Map类型数据
	 */
	Map<String,List<KeyValue>> getBuildPromptData();

	/**
	 * 获取缓存的风格分类提示词
	 * 
	 * @return 风格分类提示词
	 */
	String getCachedStylePrompt();

	/**
	 * 刷新风格分类提示词缓存
	 */
	void refreshStylePromptCache();
}
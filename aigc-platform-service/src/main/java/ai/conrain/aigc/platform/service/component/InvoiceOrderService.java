package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.InvoiceOrderQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceOrderVO;

import java.util.List;

/**
 * 发票订单关联 Service定义
 *
 * <AUTHOR>
 * @version InvoiceOrderService.java v 0.1 2024-06-27 12:49:39
 */
public interface InvoiceOrderService {
	
	/**
	 * 查询发票订单关联对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	InvoiceOrderVO selectById(Integer id);

	/**
	 * 删除发票订单关联对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加发票订单关联对象
	 * @param invoiceOrder 对象参数
	 * @return 返回结果
	 */
	InvoiceOrderVO insert(InvoiceOrderVO invoiceOrder);

	/**
	 * 修改发票订单关联对象
	 * @param invoiceOrder 对象参数
	 */
	void updateByIdSelective(InvoiceOrderVO invoiceOrder);

	/**
	 * 带条件批量查询发票订单关联列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<InvoiceOrderVO> queryInvoiceOrderList(InvoiceOrderQuery query);

	/**
	 * 带条件查询发票订单关联数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryInvoiceOrderCount(InvoiceOrderQuery query);

	/**
	 * 带条件分页查询发票订单关联
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<InvoiceOrderVO> queryInvoiceOrderByPage(InvoiceOrderQuery query);

}
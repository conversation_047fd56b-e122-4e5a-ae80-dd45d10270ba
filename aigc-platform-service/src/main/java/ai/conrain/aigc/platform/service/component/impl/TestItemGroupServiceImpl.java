package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.TestResultService;
import ai.conrain.aigc.platform.service.model.query.TestResultQuery;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.TestItemGroupDO;
import ai.conrain.aigc.platform.dal.example.TestItemGroupExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestItemGroupQuery;
import ai.conrain.aigc.platform.service.model.vo.TestItemGroupVO;
import ai.conrain.aigc.platform.service.model.converter.TestItemGroupConverter;
import ai.conrain.aigc.platform.dal.dao.TestItemGroupDAO;
import ai.conrain.aigc.platform.service.component.TestItemGroupService;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_PARAMS;

/**
 * TestItemGroupService实现
 *
 * <AUTHOR>
 * @version TestItemGroupService.java v 0.1 2024-12-19 01:24:06
 */
@Slf4j
@Service
public class TestItemGroupServiceImpl implements TestItemGroupService {

    /** DAO */
    @Autowired
    private TestItemGroupDAO testItemGroupDAO;
    @Autowired
    private TestResultService testResultService;

    @Override
    public TestItemGroupVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestItemGroupDO data = testItemGroupDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TestItemGroupConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testItemGroupDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestItemGroup失败");
    }

    @Override
    public TestItemGroupVO insert(TestItemGroupVO testItemGroup) {
        AssertUtil.assertNotNull(testItemGroup, ResultCode.PARAM_INVALID, "testItemGroup is null");
        AssertUtil.assertTrue(testItemGroup.getId() == null, ResultCode.PARAM_INVALID, "testItemGroup.id is present");

        //创建时间、修改时间兜底
        if (testItemGroup.getCreateTime() == null) {
            testItemGroup.setCreateTime(new Date());
        }

        if (testItemGroup.getModifyTime() == null) {
            testItemGroup.setModifyTime(new Date());
        }

        TestItemGroupDO data = TestItemGroupConverter.vo2DO(testItemGroup);
        int n = testItemGroupDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestItemGroup失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestItemGroup返回id为空");
        testItemGroup.setId(data.getId());
        return testItemGroup;
    }

    @Override
    public void updateByIdSelective(TestItemGroupVO testItemGroup) {
        AssertUtil.assertNotNull(testItemGroup, ResultCode.PARAM_INVALID, "testItemGroup is null");
        AssertUtil.assertTrue(testItemGroup.getId() != null, ResultCode.PARAM_INVALID, "testItemGroup.id is null");

        //修改时间必须更新
        testItemGroup.setModifyTime(new Date());
        TestItemGroupDO data = TestItemGroupConverter.vo2DO(testItemGroup);
        int n = testItemGroupDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestItemGroup失败，影响行数:" + n);
    }

    @Override
    public List<TestItemGroupVO> queryTestItemGroupList(TestItemGroupQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestItemGroupExample example = TestItemGroupConverter.query2Example(query);

        List<TestItemGroupDO> list = testItemGroupDAO.selectByExampleWithBLOBs(example);
        return TestItemGroupConverter.doList2VOList(list);
    }

    @Override
    public Long queryTestItemGroupCount(TestItemGroupQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestItemGroupExample example = TestItemGroupConverter.query2Example(query);
        long c = testItemGroupDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询AB测试项目分组
     */
    @Override
    public PageInfo<TestItemGroupVO> queryTestItemGroupByPage(TestItemGroupQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestItemGroupVO> page = new PageInfo<>();

        TestItemGroupExample example = TestItemGroupConverter.query2Example(query);
        long totalCount = testItemGroupDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestItemGroupDO> list = testItemGroupDAO.selectByExample(example);
        page.setList(TestItemGroupConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public List<TestItemGroupVO> queryWithResultByItemId(Integer itemId) {
        TestItemGroupQuery query = new TestItemGroupQuery();
        query.setItemId(itemId);
        List<TestItemGroupVO> list = queryTestItemGroupList(query);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(e -> {
                TestResultQuery resultQuery = new TestResultQuery();
                resultQuery.setGroupId(e.getId());
                e.setResults(testResultService.queryTestResultList(resultQuery));
            });
        }
        return list;
    }

    @Override
    public void refreshScore(Integer groupId) {
        testItemGroupDAO.refreshScore(groupId);
    }

    @Override
    public void updateComparisonParamsByItemId(Integer itemId, Integer roundsNum, List<JSONObject> comparisonParams,
                                               List<CreateTestParams> testParams) {
        // 参数封装
        TestItemGroupQuery query = new TestItemGroupQuery();
        query.setItemId(itemId);

        // 根据planId获取测试组信息
        List<TestItemGroupVO> testItemGroupVOS = queryTestItemGroupList(query);
        if (CollectionUtils.isEmpty(testItemGroupVOS)) {
            return;
        }

        for (int i = 0; i < testItemGroupVOS.size(); i++) {
            TestItemGroupVO group = testItemGroupVOS.get(i);
            group.setComparisonParams(comparisonParams.get(i));
            group.setRoundsNum(roundsNum);

            if (testParams != null) {
                group.addExtInfo(KEY_TEST_PARAMS,
                    testParams.subList(0, Math.min(group.getRoundsNum(), testParams.size())));
            }

            // 更新记录信息
            updateByIdSelective(group);
        }
    }

    @Override
    public void deleteByTestItemId(Integer testItemId) {
        AssertUtil.assertNotNull(testItemId, ResultCode.PARAM_INVALID, "testItemId is null");

        long n = testItemGroupDAO.deleteByTestItemId(testItemId);

        AssertUtil.assertTrue(n == 2, ResultCode.BIZ_FAIL, "删除TestItemGroup失败");
    }

}
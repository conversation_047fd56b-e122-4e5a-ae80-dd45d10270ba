package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.FavorTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.FavorImageDetail;
import ai.conrain.aigc.platform.service.model.biz.FavorCreationModel;
import ai.conrain.aigc.platform.service.model.biz.FavorImageModel;
import ai.conrain.aigc.platform.service.model.query.UserFavorQuery;
import ai.conrain.aigc.platform.service.model.vo.LoraOption;
import ai.conrain.aigc.platform.service.model.vo.UserFavorVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 用户收藏 Service定义
 *
 * <AUTHOR>
 * @version UserFavorService.java v 0.1 2025-03-04 11:46:07
 */
public interface UserFavorService {
	
	/**
	 * 查询用户收藏对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	UserFavorVO selectById(Integer id);

	/**
	 * 删除用户收藏对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 完整删除用户收藏, 包括关联表中的数据
	 * @param userFavor 用户收藏
	 * @return 返回结果
	 */
	UserFavorVO removeFavor(UserFavorVO userFavor);

	/**
	 * 添加用户收藏
	 * @param userFavor 用户收藏
	 * @return 返回结果
	 */
	UserFavorVO addFavor(UserFavorVO userFavor);

	/**
	 * 添加用户收藏对象
	 * @param userFavor 对象参数
	 * @return 返回结果
	 */
	UserFavorVO insert(UserFavorVO userFavor);

	/**
	 * 修改用户收藏对象
	 * @param userFavor 对象参数
	 */
	void updateByIdSelective(UserFavorVO userFavor);

	/**
	 * 带条件批量查询用户收藏列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<UserFavorVO> queryUserFavorList(UserFavorQuery query);

	/**
	 * 带条件批量查询用户收藏列表, 带扩展信息
	 * @param query 查询条件
	 * return 结果
	 */
	List<UserFavorVO> queryUserFavorListWithBlobs(UserFavorQuery query);

	/**
	 * 带条件查询用户收藏数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryUserFavorCount(UserFavorQuery query);

	/**
	 * 带条件分页查询用户收藏
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<UserFavorVO> queryUserFavorByPage(UserFavorQuery query);


	/**
	 * 根据类型和id查询用户收藏对象
	 * @param type 类型
	 * @param itemId id
	 * @return userFavorVO
	 */
	UserFavorVO validateUserFavor(FavorTypeEnum type, Integer itemId);


	/**
	 * 根据类型和id查询用户收藏对象, 带扩展信息
	 * @param type 类型
	 * @param itemId id
	 * @return userFavorVO
	 */
	UserFavorVO validateUserFavorWithBlobs(FavorTypeEnum type, Integer itemId);

	/**
	 * 分也查询用户收藏图片
	 * @param query 查询条件
	 * @return 分页结果
	 */
	PageInfo<FavorCreationModel> getFavorImg4ModelByPage(UserFavorQuery query);

	/**
	 * 根据模型id查询用户收藏图片详情
	 * @param query 模型id
	 * @return 详情
	 */
	FavorImageDetail queryFavorDetail(UserFavorQuery query);

	/**
	 * 查询没有modelId的收藏图片
	 * @return 结果
	 */
	List<FavorImageModel> queryFavorImgWithoutModelId (UserFavorQuery query);

	/**
	 * 查询当前用户已收藏图片相关联的服装
	 * @return 结果
	 */
	List<LoraOption> getModels4Favor();
}
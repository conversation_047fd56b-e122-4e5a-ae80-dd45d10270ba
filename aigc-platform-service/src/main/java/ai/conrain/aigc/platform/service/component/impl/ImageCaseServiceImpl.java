package ai.conrain.aigc.platform.service.component.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.dal.dao.ImageCaseDAO;
import ai.conrain.aigc.platform.dal.entity.ImageCaseDO;
import ai.conrain.aigc.platform.dal.entity.ImageCaseTagDO;
import ai.conrain.aigc.platform.dal.example.ImageCaseExample;
import ai.conrain.aigc.platform.service.component.CreativeTaskService;
import ai.conrain.aigc.platform.service.component.ImageCaseService;
import ai.conrain.aigc.platform.service.component.PromptDictService;
import ai.conrain.aigc.platform.service.component.TestResultService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.DictTagsEnum;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseAssociatedEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.biz.BadCaseTag;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageCaseConverter;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.vo.PromptDictVO;
import ai.conrain.aigc.platform.service.model.vo.TestResultVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.FileUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_REMOTE_IMAGE_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;

/**
 * ImageCaseService实现
 *
 * <AUTHOR>
 * @version ImageCaseService.java v 0.1 2024-12-09 06:41:17
 */
@Slf4j
@Service
public class ImageCaseServiceImpl implements ImageCaseService {
    @Autowired
    private ImageCaseDAO imageCaseDAO;
    @Autowired
    private PromptDictService promptDictService;
    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private UserService userService;
    @Autowired
    private TestResultService testResultService;

    @Override
    public ImageCaseVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ImageCaseDO data = imageCaseDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ImageCaseConverter.do2VO(data);
    }

    @Override
    public ImageCaseVO selectByImage(String url) {
        AssertUtil.assertTrue(StringUtils.isNotBlank(url), ResultCode.PARAM_INVALID, "url is null");

        ImageCaseExample example = new ImageCaseExample();
        example.createCriteria().andUrlEqualTo(url);
        List<ImageCaseDO> datas = imageCaseDAO.selectByExample(example);
        if (CollectionUtils.isEmpty(datas)) {
            return null;
        }

        return ImageCaseConverter.do2VO(datas.get(0));
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = imageCaseDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCase失败");
    }

    @Override
    public ImageCaseVO insert(ImageCaseVO imageCase) {
        AssertUtil.assertNotNull(imageCase, ResultCode.PARAM_INVALID, "imageCase is null");
        AssertUtil.assertTrue(imageCase.getId() == null, ResultCode.PARAM_INVALID, "imageCase.id is present");

        //创建时间、修改时间兜底
        if (imageCase.getCreateTime() == null) {
            imageCase.setCreateTime(new Date());
        }

        if (imageCase.getModifyTime() == null) {
            imageCase.setModifyTime(new Date());
        }

        ImageCaseDO data = ImageCaseConverter.vo2DO(imageCase);
        int n = imageCaseDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCase失败");
        AssertUtil.assertNotNull(data.getId(), "新建ImageCase返回id为空");
        imageCase.setId(data.getId());
        return imageCase;
    }

    @Override
    public void updateByIdSelective(ImageCaseVO imageCase) {
        AssertUtil.assertNotNull(imageCase, ResultCode.PARAM_INVALID, "imageCase is null");
        AssertUtil.assertTrue(imageCase.getId() != null, ResultCode.PARAM_INVALID, "imageCase.id is null");

        //修改时间必须更新
        imageCase.setModifyTime(new Date());
        ImageCaseDO data = ImageCaseConverter.vo2DO(imageCase);
        int n = imageCaseDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCase失败，影响行数:" + n);
    }

    @Override
    public List<ImageCaseVO> queryImageCaseList(ImageCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaseExample example = ImageCaseConverter.query2Example(query);

        List<ImageCaseDO> list = imageCaseDAO.selectByExample(example);
        return ImageCaseConverter.doList2VOList(list);
    }

    @Override
    public Long queryImageCaseCount(ImageCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaseExample example = ImageCaseConverter.query2Example(query);
        return imageCaseDAO.countByExample(example);
    }

    /**
     * 创建空的分页结果
     */
    private PageInfo<ImageCaseVO> createEmptyPage() {
        PageInfo<ImageCaseVO> emptyPage = new PageInfo<>();
        emptyPage.setList(new ArrayList<>());
        emptyPage.setSize(0);
        emptyPage.setTotalCount(0);
        emptyPage.setHasNextPage(false);
        return emptyPage;
    }

    /**
     * 带条件分页查询图片案例
     */
    @Override
    public PageInfo<ImageCaseVO> queryImageCaseByPage(ImageCaseQuery query) {
        // 1. 参数校验
        validatePageQuery(query);

        // 2. 获取标签ID列表
        List<Integer> promptDictIdList = getPromptDictIds(query.getImageCaseType());
        if (CollectionUtils.isEmpty(promptDictIdList)) {
            return createEmptyPage();
        }

        // 3. 设置查询条件并执行查询
        query.setTagIds(CollectionUtils.isEmpty(query.getTagIds()) ? promptDictIdList : query.getTagIds());
        ImageCaseExample example = ImageCaseConverter.query2Example(query);
        long totalCount = imageCaseDAO.countByExample(example);
        if (totalCount == 0) {
            return createEmptyPage();
        }

        //  4. 获取需要展示的图片案例标签主键
        PromptDictVO promptDictVO = promptDictService.queryByTypeAndPrompt(DictTypeEnum.IMAGE_TAGS,
            CommonConstants.NEED);
        if (promptDictVO != null) {
            promptDictIdList.add(promptDictVO.getId());
        }

        // 5. 查询并过滤数据
        List<ImageCaseVO> filterList = queryAndFilterImageCases(example, promptDictIdList);

        // 6. 填充额外信息
        enrichImageCaseInfo(filterList);

        // 7. 设置分页信息
        return buildPageInfo(filterList, totalCount, example);
    }

    /**
     * 校验分页查询参数
     */
    private void validatePageQuery(ImageCaseQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");
        AssertUtil.assertNotNull(query.getImageCaseType(), ResultCode.PARAM_INVALID, "ImageCaseType is null");
        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());
    }

    /**
     * 获取提示词字典ID列表
     */
    private List<Integer> getPromptDictIds(String imageCaseType) {
        // 查询指定类型字典列表主键集合
        List<Integer> promptDictIdList = promptDictService.queryListByType(DictTypeEnum.IMAGE_TAGS, imageCaseType)
            .stream()
            .map(PromptDictVO::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(promptDictIdList)) {
            log.error("[查询图片案例数据]ImageCaseServiceImpl::getPromptDictIds::图片案例类型：{}不存在，无数据...",
                imageCaseType);
        }

        return promptDictIdList;
    }

    /**
     * 查询并过滤图片案例
     */
    private List<ImageCaseVO> queryAndFilterImageCases(ImageCaseExample example, List<Integer> promptDictIdList) {
        // 获取需要上传的标签ID
        PromptDictVO promptDictVO = promptDictService.queryByTypeAndPrompt(DictTypeEnum.IMAGE_TAGS,
            CommonConstants.NEED);
        Integer uploadTagId = promptDictVO != null ? promptDictVO.getId() : null;

        List<ImageCaseDO> list = imageCaseDAO.selectByExample(example);
        List<ImageCaseVO> result = ImageCaseConverter.doList2VOList(list);

        // 过滤数据：
        // 1. 必须包含标签
        // 2. 必须包含指定类型的标签（promptDictIdList）
        // 3. 过滤掉仅包含上传标签的记录
        return result.stream()
            .filter(item -> {
                if (item.getTags() == null || item.getTags().isEmpty()) {
                    return false;
                }

                // 如果只包含一个标签且是上传标签，则过滤掉
                if (item.getTags().size() == 1 && uploadTagId != null && item.getTags().contains(uploadTagId)) {
                    return false;
                }

                // 必须包含指定类型的标签
                return !Collections.disjoint(item.getTags(), promptDictIdList);
            })
            .collect(Collectors.toList());
    }

    /**
     * 填充图片案例额外信息
     */
    private void enrichImageCaseInfo(List<ImageCaseVO> imageCases) {
        if (CollectionUtils.isEmpty(imageCases)) {
            return;
        }

        // 填充标签详情
        imageCases.forEach(imageCase -> {
            List<PromptDictVO> tags = promptDictService.queryByIds(imageCase.getTags());
            if (CollectionUtils.isNotEmpty(tags)) {
                imageCase.setTagDetails(tags.stream()
                    .map(tag -> new BadCaseTag(tag.getId(), tag.getWord(), true))
                    .collect(Collectors.toList()));
            }
        });

        // 填充操作者信息
        enrichOperatorInfo(imageCases);
    }

    /**
     * 填充操作者信息
     */
    private void enrichOperatorInfo(List<ImageCaseVO> imageCases) {
        List<Integer> operatorIds = imageCases.stream()
            .map(ImageCaseVO::getOperatorId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(operatorIds)) {
            return;
        }

        // 批量查询用户信息
        List<UserVO> userList = userService.batchQueryById(operatorIds);
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        Map<Integer, String> userNickNameMap = userList.stream()
            // 过滤掉空值
            .filter(user -> user.getId() != null && user.getNickName() != null)
            .collect(Collectors.toMap(
                UserVO::getId,
                UserVO::getNickName,
                // 处理重复 key 的策略
                (existing, replacement) -> existing
            ));

        // 设置操作者昵称
        imageCases.forEach(imageCase -> {
            Integer operatorId = imageCase.getOperatorId();
            imageCase.setOperatorNick(operatorId != null ? userNickNameMap.get(operatorId) : null);
        });
    }

    /**
     * 构建分页信息
     */
    private PageInfo<ImageCaseVO> buildPageInfo(List<ImageCaseVO> filterList, long totalCount,
                                                ImageCaseExample example) {
        PageInfo<ImageCaseVO> page = new PageInfo<>();
        page.setTotalCount(totalCount);
        page.setHasNextPage(filterList.size() > (example.getOffset() + example.getRows()));
        page.setList(filterList);
        page.setSize(filterList.size());
        return page;
    }

    @Override
    public List<BadCaseTag> queryBadCaseInfo(String imageUrl) {
        List<PromptDictVO> list = promptDictService.queryByTypeAndTags(DictTypeEnum.IMAGE_TAGS,
            Collections.singletonList(DictTagsEnum.BAD_CASE));

        ImageCaseVO imageCase = selectByImage(imageUrl);

        return ImageCaseConverter.doList2VOList(list, imageCase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBadCaseTag(String imageUrl, Integer taskId, Integer tagId, Boolean isAdd, Integer id, String type) {
        AssertUtil.assertNotNull(imageUrl, ResultCode.PARAM_INVALID, "imageUrl is null");

        ImageCaseVO imageCase = selectByImage(imageUrl);
        //如果已存在，则进行更新操作
        if (imageCase == null) {

            imageCase = new ImageCaseVO();
            imageCase.setUrl(imageUrl);
            if (taskId == null) {
                taskId = FileUtils.getTaskIdByImageName(imageUrl);
            }
            imageCase.setTaskId(taskId);
            CreativeTaskVO task = creativeTaskService.selectById(taskId);

            if (task != null) {
                imageCase.setBatchId(task.getBatchId());
                imageCase.setStorePath(
                    task.getResultPath() + "/" + task.getExtInfo(KEY_ORIGIN_REMOTE_IMAGE_NAME, String.class));
                imageCase.setStoreServer(task.getExtInfo(KEY_SERVER_URL, String.class));
            }

            imageCase.setStatus(MaterialModelStatusEnum.ENABLED);
            String miniUrl = null;
            try {
                miniUrl = ossHelper.resizeAndUpload(imageUrl, 300, 300);
            } catch (IOException e) {
                log.error("重置oss图片文件大小异常" + imageUrl, e);
            }
            imageCase.setMiniUrl(miniUrl);

            imageCase.setOperatorId(OperationContextHolder.getOperatorUserId());
            imageCase.setUserId(OperationContextHolder.getOperatorUserId());

            imageCase.setCreateTime(new Date());
            imageCase.setModifyTime(new Date());
            imageCase.setSyncStatus(false);
            imageCase.setSyncStatus(Boolean.FALSE);
            imageCase.setReSyncCount(0);

            // 插入图片案例
            ImageCaseVO imageCaseVO = insert(imageCase);

            // 填充数据
            fillDataByType(id, type, imageCaseVO);
        }

        //添加 or 删除
        if (CollectionUtils.isNotEmpty(imageCase.getTags()) && imageCase.getTags().contains(tagId)) {

            if (isAdd) {
                return;
            }
            imageCaseDAO.deleteTag(new ImageCaseTagDO(imageCase.getId(), tagId));
        } else if (isAdd) {
            imageCaseDAO.insertTag(new ImageCaseTagDO(imageCase.getId(), tagId));
        }
    }

    @Override
    public List<ImageCaseVO> queryImageCaseListByType(ImageCaseQuery query, DictTypeEnum dictTypeEnum,
                                                      ImageCaseTypeEnum imageCaseTypeEnum, Boolean isPartUpload) {
        // 结果集合
        List<ImageCaseVO> resultList = new ArrayList<>();

        // 查询标签列表
        List<PromptDictVO> promptDictList = promptDictService.queryListByType(dictTypeEnum, imageCaseTypeEnum);

        // 获取字典列表主键集合
        List<Integer> promptDictIdList = promptDictList.stream().map(PromptDictVO::getId).collect(Collectors.toList());

        // 查询标签列表
        PromptDictVO uploadPromptDictVO = promptDictService.queryByTypeAndPrompt(dictTypeEnum, CommonConstants.NEED);

        // 如果需要部分上传，则添加上传标签
        if (isPartUpload) {
            promptDictIdList.add(uploadPromptDictVO.getId());
        }

        // 设置标签id集合
        query.setTagIds(promptDictIdList);

        // 根据标签查询图片列表
        List<ImageCaseVO> list = queryImageCaseList(query);

        // 过滤掉不符合类型的图片记录
        list.forEach(imageCaseDO -> {
            // 获取标签集合
            List<Integer> tags = imageCaseDO.getTags();

            if (CollectionUtils.isNotEmpty(tags)) {
                // 遍历标签集合
                promptDictList.stream()
                    .filter(promptDictVO -> {
                        // 判断是否需要同步到服务器
                        if (isPartUpload) {
                            return tags.contains(promptDictVO.getId()) && tags.contains(uploadPromptDictVO.getId());
                        }
                        // 如果不需要同步到服务器，则直接判断是否包含该标签
                        return tags.contains(promptDictVO.getId());
                    })
                    .forEach(promptDictVO -> imageCaseDO.getTagDetails()
                        .add(ImageCaseConverter.dictToTag(promptDictVO, imageCaseDO)));

                // 判断promptDictIdList 和 tags 是否有交集
                if (!Collections.disjoint(tags, promptDictIdList)) {
                    resultList.add(imageCaseDO);
                }
            }
        });

        // 返回结果
        return resultList;
    }

    /**
     * 填充数据
     *
     * @param id          主键 id
     * @param type        操作类型（具体与哪张表做关联处理）
     * @param imageCaseVO 图片案例对象
     */
    private void fillDataByType(Integer id, String type, ImageCaseVO imageCaseVO) {
        // 如果主键id 活 类型为空，则不填充数据
        if (id == null || type == null) {
            return;
        }

        // 测试结果类型数据填充
        if (ImageCaseAssociatedEnum.TEST_RESULT.getCode().equals(type)) {
            // 获取测试结果对象
            TestResultVO testResultVO = testResultService.selectById(id);

            // 如果测试结果对象为空，则不填充数据
            if (testResultVO == null) {return;}

            // 更新图库关联字段
            testResultVO.setCaseId(imageCaseVO.getId());

            // 更新测试结果对象
            testResultService.updateByIdSelective(testResultVO);
        }

    }
}
package ai.conrain.aigc.platform.service.component.impl;

import java.lang.reflect.Member;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;

import ai.conrain.aigc.platform.dal.dao.DistributorSettlementDAO;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.service.model.biz.*;
import ai.conrain.aigc.platform.service.model.converter.OrganizationConverter;
import ai.conrain.aigc.platform.service.util.*;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.dal.entity.DistributorSettlementDO;
import ai.conrain.aigc.platform.dal.example.DistributorSettlementExample;
import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.component.DistributorSettlementService;
import ai.conrain.aigc.platform.service.component.OrderSettlementService;
import ai.conrain.aigc.platform.service.component.OrganizationService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.converter.DistributorSettlementConverter;
import ai.conrain.aigc.platform.service.model.query.DistributorSettlementQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**   
 * DistributorSettlementService实现
 *
 * <AUTHOR>
 * @version DistributorSettlementService.java v 0.1 2025-05-22 05:39:41
 */
@Slf4j
@Service
public class DistributorSettlementServiceImpl implements DistributorSettlementService {

	/** DAO */
	@Autowired
	private DistributorSettlementDAO distributorSettlementDAO;
	@Autowired
	private SystemConfigService systemConfigService;
	@Autowired
	private OrderSettlementService orderSettlementService;
	@Autowired
	private UserService userService;
	@Autowired
	private OrganizationService organizationService;
    @Autowired
    private UserOrganizationService userOrganizationService;
	@Autowired
	private DistributorService distributorService;
    @Autowired
    private PrincipalInfoService principalInfoService;

	@Override
	public DistributorSettlementVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		DistributorSettlementDO data = distributorSettlementDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return DistributorSettlementConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = distributorSettlementDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除DistributorSettlement失败");
	}

	@Override
	public DistributorSettlementVO insert(DistributorSettlementVO distributorSettlement) {
		AssertUtil.assertNotNull(distributorSettlement, ResultCode.PARAM_INVALID, "distributorSettlement is null");
		AssertUtil.assertTrue(distributorSettlement.getId() == null, ResultCode.PARAM_INVALID, "distributorSettlement.id is present");

		//创建时间、修改时间兜底
		if (distributorSettlement.getCreateTime() == null) {
			distributorSettlement.setCreateTime(new Date());
		}

		if (distributorSettlement.getModifyTime() == null) {
			distributorSettlement.setModifyTime(new Date());
		}

		DistributorSettlementDO data = DistributorSettlementConverter.vo2DO(distributorSettlement);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = distributorSettlementDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建DistributorSettlement失败");
		AssertUtil.assertNotNull(data.getId(), "新建DistributorSettlement返回id为空");
		distributorSettlement.setId(data.getId());
		return distributorSettlement;
	}


	@Override
	public void updateByIdSelective(DistributorSettlementVO distributorSettlement) {
		AssertUtil.assertNotNull(distributorSettlement, ResultCode.PARAM_INVALID, "distributorSettlement is null");
    	AssertUtil.assertTrue(distributorSettlement.getId() != null, ResultCode.PARAM_INVALID, "distributorSettlement.id is null");

		//修改时间必须更新
		distributorSettlement.setModifyTime(new Date());
		DistributorSettlementDO data = DistributorSettlementConverter.vo2DO(distributorSettlement);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = distributorSettlementDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新DistributorSettlement失败，影响行数:" + n);
	}

	@Override
	public List<DistributorSettlementVO> queryDistributorSettlementList(DistributorSettlementQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);

		List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(example);
			return DistributorSettlementConverter.doList2VOList(list);
	}

	@Override
	public void setServiceRate(Integer distributorCorpId, BigDecimal serviceRate, String effectTime) {
		AssertUtil.assertNotNull(distributorCorpId, ResultCode.PARAM_INVALID, "distributorCorpId is null");

		String key = SystemConstants.DISTRIBUTOR_SETTLE_RATE_PREFIX + distributorCorpId;
		SystemConfigVO config = systemConfigService.queryByKey(key);
		if (config == null) {
			config = new SystemConfigVO();

			config.setConfKey(key);
			config.setConfValue(serviceRate.toPlainString());
		}

		Date effectDate = DateUtils.parseShort(effectTime);
		if (DateUtils.beforeNow(effectDate)) {
			config.setStatus(ConfigStatusEnum.ACTIVE);
			config.setConfValue(serviceRate.toPlainString());
		} else {
			config.setStatus(ConfigStatusEnum.PENDING_CHANGE);
			config.setConfValueNext(serviceRate.toPlainString());
		}

		config.setMemo("渠道商结算费率");
		config.setEffectTime(DateUtils.parseShort(effectTime));
		config.setOperatorId(OperationContextHolder.getOperatorUserId());

		if (config.getId() != null) {
			systemConfigService.updateById(config);
		} else {
			systemConfigService.insert(config);
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public Result<?> manualSettle(Integer id, String outBizNo) {
		//1.锁当前商家记录
		DistributorSettlementDO settlement = distributorSettlementDAO.lockById(id);
		if (null == settlement) {
			log.warn("手动结算失败，商家结算数据不存在id={}", id);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "商家结算订单不存在");
		}

		if (DistributorSettleStatusEnum.SETTLE_FINISH == DistributorSettleStatusEnum.getByCode(settlement.getStatus())) {
			log.warn("手动结算失败，该商家结算数据已完成id={}", id);
			return Result.failedWithMessage(ResultCode.SETTLE_ALREADY_FINISHED, "当前商户已完成结算");
		}

		//2.判断银行流水号是否重复
		DistributorSettlementExample example = new DistributorSettlementExample();
		example.createCriteria().andOutBizNoEqualTo(outBizNo);
		long count = distributorSettlementDAO.countByExample(example);
		if (count > 0) {
			log.warn("手动结算异常，银行流水号重复，id={}，outBizNo={}", id, outBizNo);
			throw new BizException(ResultCode.DUPLICATE_TRANSACTION_NO);
		}

		//3.查询所有当前商家记录单下的订单结算数据，并开始结算
		orderSettlementService.finishSettlement(settlement.getSettleId());

		//4.更新商家结算状态
		DistributorSettlementDO data = new DistributorSettlementDO();
		data.setId(settlement.getId());
		data.setStatus(DistributorSettleStatusEnum.SETTLE_FINISH.getCode());
		data.setSettleTime(new Date());
		data.setOutBizNo(outBizNo);

		int cnt = distributorSettlementDAO.updateByPrimaryKeySelective(data);
		if (cnt < 1) {
			log.error("手动结算异常，id={}商家结算失败，未完成更新", data.getId());
			throw new BizException(ResultCode.SYS_ERROR);
		}

		return Result.success();
	}

	@Override
	public SettleConfigModel getSettleConfig(PrincipalModel principal) {
		PrincipalInfoVO principalInfo = principalInfoService.selectByPrincipal(principal, KEY_SETTLE_CONFIG);

		SettleConfigModel settleConfig = null;
		if (principalInfo != null) {
			settleConfig = JSONObject.toJavaObject(principalInfo.getInfoValue(), SettleConfigModel.class);
		}
		return settleConfig;
	}

	@Override
    public DistributorSettleConfigVO queryDistributorSettleConfig(PrincipalModel principal) {
		AssertUtil.assertNotNull(principal, ResultCode.PARAM_INVALID, "principal is null");
		DistributorPrincipalBasicVO basicInfo = queryDistributorPrincipalBasicInfo(principal);
		DistributorSettleConfigVO data = BeanUtils.deepCopy(basicInfo, DistributorSettleConfigVO.class);
		SettleConfigModel settleConfig = getSettleConfig(principal);
		data.setSettleConfig(settleConfig);
		return data;
	}

	@Override
	public DistributorPrincipalBasicVO queryDistributorPrincipalBasicInfo(PrincipalModel principal) {
		AssertUtil.assertNotNull(principal, ResultCode.PARAM_INVALID, "principal is null");

		String contractDate = (principalInfoService.queryContractDate(principal));
		DistributorPrincipalBasicVO data = new DistributorPrincipalBasicVO();
		data.setPrincipalType(principal.getType().getCode());
		data.setPrincipalId(principal.getId());
		data.setPrincipalName(principal.getName());
		data.setContractDate(contractDate);

		OrganizationVO rootOrg = null;
		OrganizationVO subOrg = null;
		UserVO channelAdmin = null;

		switch (principal.getType()) {
			case CORP :
				// 查询组织信息
				rootOrg = organizationService.selectById(principal.getId());
				AssertUtil.assertNotNull(rootOrg, ResultCode.BIZ_FAIL, "组织不存在");
				// 查询渠道管理员信息
				channelAdmin = userService.getChannelAdminByOrgId(principal.getId());
				data.setSalesType(rootOrg.getSalesType().getCode());
				data.setMasterCorpId(principal.getId());
				data.setMasterCorpName(principal.getName());
				data.setCustomRole(channelAdmin.getCustomRole());
				data.setChannelAdminId(channelAdmin.getId());
				data.setChannelAdminNickName(channelAdmin.getNickName());
				break;
			case SUB_CORP:
				// 查询子渠道组织信息
				subOrg = organizationService.selectById(principal.getId());
				AssertUtil.assertNotNull(subOrg, ResultCode.BIZ_FAIL, "子渠道组织不存在");
				data.setSalesType(organizationService.getRootSalesType(subOrg.getId()).getCode());
				data.setSubCorpName(principal.getName());
				data.setSubCorpId(principal.getId());
				data.setSubSalesType(subOrg.getSalesType() != null ?
						subOrg.getSalesType().getCode() : SalesTypeEnum.AGENT_NORMAL.getCode());
				// 查询子渠道管理员信息
				UserVO secondChannelAdmin = userService.getChannelAdminByOrgId(principal.getId());
				if (!ObjectUtils.isEmpty(secondChannelAdmin)) {
					data.setCustomRole(secondChannelAdmin.getCustomRole());
					data.setMasterCorpId(secondChannelAdmin.getCorpOrgId());
					data.setMasterCorpName(secondChannelAdmin.getCorpName());
					data.setChannelAdminId(secondChannelAdmin.getMasterId());
					data.setChannelAdminNickName(secondChannelAdmin.getMasterNick());
					data.setSubChannelAdminId(secondChannelAdmin.getId());
					data.setSubChannelAdminNickName(secondChannelAdmin.getNickName());
				} else {
					// 没有子渠道管理员的信息, 则使用主账号的信息, 主账号必然是有的
					channelAdmin = userService.getChannelAdminByOrgId(subOrg.getParentId());
					data.setMasterCorpId(channelAdmin.getCorpOrgId());
					data.setMasterCorpName(channelAdmin.getCorpName());
					data.setChannelAdminId(channelAdmin.getId());
					data.setChannelAdminNickName(channelAdmin.getNickName());
				}
				break;
			case USER :
				// 查询销售的用户信息
				UserVO user = userService.selectById(principal.getId());
				AssertUtil.assertNotNull(user, ResultCode.BIZ_FAIL, "用户不存在");
				OrganizationVO org = organizationService.queryOrganizationByUserId(user.getId());
				AssertUtil.assertNotNull(org, ResultCode.BIZ_FAIL, "用户组织不存在");
				data.setCustomRole(user.getCustomRole());
				// 当前用户组织是根组织
				if (org.getRoot()) {
					data.setSalesType(org.getSalesType().getCode());
					data.setMasterCorpId(user.getCorpOrgId());
					data.setMasterCorpName(user.getCorpName());
					data.setChannelAdminId(user.getMasterId());
					data.setChannelAdminNickName(user.getMasterNick());
				} else {
					rootOrg = organizationService.queryOrganizationByUserId(user.getMasterId());
					data.setSalesType(rootOrg.getSalesType().getCode());
					data.setMasterCorpId(rootOrg.getId());
					data.setMasterCorpName(rootOrg.getName());
					data.setChannelAdminId(user.getMasterId());
					data.setChannelAdminNickName(user.getMasterNick());
					data.setSubCorpId(org.getId());
					data.setSubCorpName(org.getName());
					// 填充子渠道管理员信息
					if (CustomRoleEnum.SECOND_CHANNEL_ADMIN.getCode().equals(user.getCustomRole())) {
						data.setSubChannelAdminId(user.getId());
						data.setSubChannelAdminNickName(user.getNickName());
					} else {
						UserVO subChannelAdmin = userService.getChannelAdminByOrgId(org.getId());
						if (!ObjectUtils.isEmpty(subChannelAdmin)) {
							data.setSubChannelAdminId(subChannelAdmin.getId());
							data.setSubChannelAdminNickName(subChannelAdmin.getNickName());
						}
					}
					// 子组织的销售类型缺省为 外部渠道, 按组织结算
					data.setSubSalesType(org.getSalesType() != null ?
							org.getSalesType().getCode() : SalesTypeEnum.AGENT_NORMAL.getCode());
				}
				break;
			default :
				break;
		}
		return data;
    }

    @Override
	@Transactional(rollbackFor = Throwable.class)
    public void modifyDistributorSettlementConfig(PrincipalTypeEnum principalType, Integer principalId, SettleConfigModel config) {
		AssertUtil.assertNotNull(principalType, ResultCode.PARAM_INVALID, "[编辑结算配置]principalType is null");
		AssertUtil.assertNotNull(principalId, ResultCode.PARAM_INVALID, "[编辑结算配置]principalId is null");
		AssertUtil.assertNotNull(config, ResultCode.PARAM_INVALID, "[编辑结算配置]config is null");

		// 确保settleDate在0-27范围内
		AssertUtil.assertTrue(config.getCalculationDate() != null && config.getCalculationDate() >= 0 && config.getCalculationDate() <= 27,
				ResultCode.PARAM_INVALID, "[编辑结算配置]结算日期必须在0-27范围内");

		PrincipalInfoVO principalInfo = new PrincipalInfoVO();
		principalInfo.setPrincipalType(principalType);
		principalInfo.setPrincipalId(principalId);
		principalInfo.setInfoKey(KEY_SETTLE_CONFIG);
		principalInfo.setInfoValue((JSONObject) JSONObject.toJSON(config));

		principalInfoService.insertOrUpdate(principalInfo);
    }

	@Override
	public List<DistributorPrincipalBasicVO> queryAllDistributorPrincipalBasicInfo() {
		List<DistributorPrincipalBasicVO> result = new ArrayList<>();
		// 查询所有渠道商组织
		List<OrganizationVO> distributorCorps = organizationService.queryDistributorCorps();
		for (OrganizationVO distributorCorp : distributorCorps) {
			// 查询 渠道商下所有子账号
			List<PrincipalModel> settlePrincipals = new ArrayList<>();
			distributorService.querySettlePrincipalExclusive(settlePrincipals, distributorCorp);
			for (PrincipalModel settlePrincipal : settlePrincipals) {
				try {
					result.add(queryDistributorPrincipalBasicInfo(settlePrincipal));
				} catch (Exception e) {
					log.error("查询渠道商增强配置异常, principal={}, error={}", settlePrincipal, e.getMessage(), e);
				}
			}
		}
		return result;
	}

	@Override
	public List<DistributorOrgTree> getDistributorOrgTrees() {
		List<OrganizationVO> rootOrgs = organizationService.queryDistributorCorps();
		List<DistributorOrgTree> result = CommonUtil.listConverter(rootOrgs, OrganizationConverter::vo2Tree);
		for (DistributorOrgTree tree : result) {
			fillDistributorOrgTree(tree);
		}
		return result;
	}

	@Override
	public DistributorSettlementVO initSettlement(PrincipalModel principal, DistributorSettleConfigVO settleConfigVO, Date startDate, Date endDate) {
		SettleConfigModel configModel = settleConfigVO.getSettleConfig();
		if (ObjectUtils.isEmpty(configModel)) {
			log.warn("[渠道结算]结算主体: {}没有结算配置，跳过", principal.getName());
		}
		// 1. 清理历史结算单
		cleanOutDatedSettlement(principal, startDate, endDate);

		// 3. 创建 data, 初始化数据
		String settleId = CommonUtil.genBizNo(principal.getId());
		log.info("[渠道结算]任务开始, 当前创建的 settleId = {}", settleId);
		DistributorSettlementVO data = new DistributorSettlementVO();
		data.setSettleId(settleId);
		data.setDistributorCorpId(settleConfigVO.getMasterCorpId());
		data.setDistributorCorpName(settleConfigVO.getMasterCorpName());
		data.setPrincipalType(principal.getType());
		data.setPrincipalId(principal.getId());
		data.setStatus(DistributorSettleStatusEnum.PENDING_SETTLE);
		data.setSettleType(SettleTypeEnum.MANUAL_SETTLE.getCode());
		data.putExtInfo(KEY_SETTLE_START_DATE, DateUtils.formatSimpleDate(startDate));
		data.putExtInfo(KEY_SETTLE_END_DATE, DateUtils.formatSimpleDate(endDate));

		// 聚合分佣结算单
		orderSettlementService.aggregateCommissionSettlement4Distributor(startDate, endDate, data, settleConfigVO);

		// 聚合抽成结算单, 目前只有渠道管理员才有抽成的逻辑
		if (settleConfigVO.isChannelAdmin()) {
			orderSettlementService.aggregateCommissionRelateSettlement(principal, startDate, endDate, data);
		}

		log.info("[渠道结算]任务结束, 当前销售渠道 聚合结算单数据成功, distributorSettle = {}", data);

		// 5. 插入 销售渠道 结算单数据, 都为空就不结算了
		if (!BigDecimalUtils.equalsZero(data.getTotalAmount()) && !BigDecimalUtils.equalsZero(data.getSettleAmount())) {
			this.insert(data);
		}
		log.info("[渠道结算]任务结束, 当前销售渠道 结算单数据插入成功, principalId = {}, settleId = {}", settleConfigVO.getPrincipalId(), settleId);
		return data;
	}

	/**
	 * 带条件分页查询商户结算明细
	 */
	@Override
	public Result<PageInfo<DistributorSettlementVO>> queryByPage(DistributorSettlementQuery query) {
		if (query == null) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "query is null");
		}

		if (query.getPageNum() == null || query.getPageSize() == null || query.getPageNum() < 1
				|| query.getPageSize() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID,
					"pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());
		}

		if (RoleTypeEnum.DISTRIBUTOR == OperationContextHolder.getContext().getRoleType()) {
			query.setDistributorCorpId(OperationContextHolder.getCorpOrgId());
		}

		try {
			PageInfo<DistributorSettlementVO> page = new PageInfo<>();

			DistributorSettlementExample example = DistributorSettlementConverter.query2Example(query);
			long totalCount = distributorSettlementDAO.countByExample(example);
			if (totalCount == 0) {
				page.setList(new ArrayList<>());
				page.setSize(0);
				page.setTotalCount(0);
				page.setHasNextPage(false);

				return Result.success(page);
			}

			List<DistributorSettlementDO> list = distributorSettlementDAO.selectByExample(example);
			if (list != null) {
				page.setList(DistributorSettlementConverter.doList2VOList(list));
				page.setSize(list.size());
				page.setTotalCount(totalCount);
				page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

				return Result.success(page);
			} else {
				return Result.failedWithMessage(ResultCode.BIZ_FAIL, "分页查询MerchantSettlement失败，返回null");
			}

		} catch (Throwable t) {
			log.error("DistributorSettlementService.queryMerchantSettlementByPage异常:", t);
			return Result.failedWithMessage(ResultCode.SYS_ERROR, "MerchantSettlement分页查询异常:" + t.getMessage());
		}
	}

	/**
	 * 清理已经存在的商家结算单, 只清理结算周期完全重合的
	 *
	 * @param principal 商户id
	 */
	private void cleanOutDatedSettlement(PrincipalModel principal, Date startDate, Date endDate) {
		DistributorSettlementQuery query = new DistributorSettlementQuery();
		query.setPrincipalType(principal.getType().getCode());
		query.setPrincipalId(principal.getId());
		query.setStatus(DistributorSettleStatusEnum.PENDING_SETTLE.getCode());
		List<DistributorSettlementVO> list = this.queryDistributorSettlementList(query);

		if (CollectionUtils.isEmpty(list)) {
			log.info("[渠道结算]清理已经存在的商家结算单结束，未查到历史存量结算单，principal={}", principal);
			return;
		}
		// 遍历历史结算单，如果结算单状态是结算完成，则跳过，否则，如果结算周期完全重合，则删除该结算单
		for (DistributorSettlementVO oldSettlement : list) {
			Date oldStartDate = getSettlePeriodDate(oldSettlement, KEY_SETTLE_START_DATE);
			Date oldEndDate = getSettlePeriodDate(oldSettlement, KEY_SETTLE_END_DATE);
			if (DateUtils.isSameDay(oldStartDate, startDate) && DateUtils.isSameDay(oldEndDate, endDate)) {
				log.warn("[渠道结算]清理已经存在的商家结算单，结算单id={}, 结算周期完全重合, 且没有完成结算, 逻辑删除", oldSettlement.getId());
				//逻辑删除当前商户结算单
				distributorSettlementDAO.logicalDeleteByPrimaryKey(oldSettlement.getId());
			} else if (!DateUtils.afterDate(endDate, oldStartDate) && !DateUtils.afterDate(oldEndDate, startDate)) {
				// 如果两个结算单有重叠部分 (不完全重叠), 此时无法结算, 抛出异常
				throw new BizException(ResultCode.BIZ_FAIL, "结算周期有重叠, 请手动处理, principal = " + principal);
			}
		}
	}

	private Date getSettlePeriodDate(DistributorSettlementVO oldSettlement, String key) {
		String endDate = oldSettlement.getExtInfo(key, String.class);
		if (StringUtils.isNotBlank(endDate)) {
			return DateUtils.parseSimpleDate(endDate);
		}
		Date createTime = oldSettlement.getCreateTime();
		// 获取 createTime 上个月 月初/月底 的日期
		Calendar cal = Calendar.getInstance();
		cal.setTime(createTime);
		cal.add(Calendar.MONTH, -1);
		int dayOfMonth = StringUtils.equals(KEY_SETTLE_START_DATE, key) ? 1 : cal.getActualMaximum(Calendar.DAY_OF_MONTH);
		cal.set(Calendar.DAY_OF_MONTH, dayOfMonth);
		return cal.getTime();
	}

	private void fillDistributorOrgTree(DistributorOrgTree tree) {
		List<UserVO> userVOs = userService.selectByOrgId(tree.getOrgId());
		for (UserVO userVO : userVOs) {
			tree.addMember(userVO);
		}
		List<OrganizationVO> subOrgs = organizationService.querySubOrg(tree.getOrgId());
		for (OrganizationVO subOrg : subOrgs) {
			DistributorOrgTree subTree = OrganizationConverter.vo2Tree(subOrg);
			fillDistributorOrgTree(subTree);
			tree.addChild(subTree);
		}
	}
}
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_ID;

/**
 * model server 服务分发, 不加锁
 */
@Slf4j
@Component
public class ModelServerDispatch extends AbstractTaskDispatch {

    @Lazy
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Override
    public String dispatch(IExtModel task) {

        Integer dispatchUserId = getDispatchUserId(task);
        PipelineVO pipeline = fetchPipeline(dispatchUserId);
        AssertUtil.assertNotNull(pipeline, ResultCode.BIZ_FAIL, "【任务分发】用户未配置管道, userId=" + dispatchUserId);

        List<ServerVO> modelServers = serverHelper.getServersByType(ServerTypeEnum.MODEL_SERVER, pipeline);
        AssertUtil.assertNotEmpty(modelServers, ResultCode.BIZ_FAIL, "【任务分发】当前管道无可用模型服务, pipelineId=" + pipeline.getId());

        // 随机获取一个模型服务
        Collections.shuffle(modelServers);
        ServerVO modelServer = modelServers.get(0);

        return serverHelper.getServerUrl(modelServer);
    }

    @Override
    public String dispatch(IExtModel task, ServerVO server) {
        String serverUrl = null;
        String serverIdStr = task.getStringFromExtInfo(KEY_SERVER_ID);
        if (StringUtils.isNotBlank(serverIdStr)) {
            int serverId = Integer.parseInt(serverIdStr);
            ServerVO serverVO = serverService.queryByKey(serverId);
            if (serverHelper.isEnable(serverVO)) {
                serverUrl = serverHelper.getServerUrl(serverVO);
            }
        } else if (serverHelper.isEnable(server)) {
            serverUrl = serverHelper.getServerUrl(server);
        }

        if (StringUtils.isNotBlank(serverUrl)) {
            log.info("【任务分发】命中服务，计算获取到可用服务,type={},taskId={},serverUrl={}", getType(), task.getId(), serverUrl);
        } else {
            log.warn("任务分发】未命中服务,type={},taskId={}", getType(), task.getId());
        }

        return serverUrl;
    }

    @Override
    public void release(IExtModel task) {
        // 当前分发类型不加锁, 不需要释放
    }

    @Override
    protected List<? extends IExtModel> queryUnProcessedData(Integer pipelineId, int idleNum) {
        return List.of();
    }

    @Override
    protected boolean isTaskFinished(Integer taskId) {
        if (taskId == null) {
            return true;
        }
        CreativeBatchVO batch = creativeBatchService.selectById(taskId);
        return batch == null || batch.getStatus().isEnd();
    }

    @Override
    public DispatchTypeEnum getType() {
        return DispatchTypeEnum.MODEL_SERVER;
    }

}

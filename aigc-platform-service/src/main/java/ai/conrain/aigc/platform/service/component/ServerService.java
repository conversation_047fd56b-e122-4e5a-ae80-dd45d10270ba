package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import java.util.List;

/**
 * 服务器配置 Service定义
 *
 * <AUTHOR>
 * @version ServerService.java v 0.1 2024-06-15 04:58:30
 */
public interface ServerService extends CachedService<ServerVO, Integer, String> {

    /**
     * 查询服务器配置对象
     *
     * @param id 主键
     * @return 返回结果
     */
    ServerVO selectById(Integer id);

    /**
     * 删除服务器配置对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加服务器配置对象
     *
     * @param server 对象参数
     * @return 返回结果
     */
    ServerVO insert(ServerVO server);

    /**
     * 修改服务器配置对象
     *
     * @param server 对象参数
     */
    void updateById(ServerVO server);

    /**
     * 根据管道id查询服务列表
     *
     * @param id 管道id
     * @return 服务列表
     */
    List<ServerVO> queryByPipeline(Integer id);

    /**
     * 测试并保存服务端口状态
     *
     * @param serverId 服务id
     * @return 返回数据
     */
    ServerVO test(Integer serverId);

    /**
     * 测试服务端口状态
     *
     * @param server 服务
     */
    ServerStatusEnum test(ServerVO server);

    /**
     * 通过url反向解析出server
     *
     * @param url 带端口的url
     * @return server
     */
    ServerVO parseByUrl(String url);

    /**
     * 初始化机器房信息
     */
    @Deprecated
    void initDeviceInfo();

    /**
     * 获取端口运行日志
     *
     * @param id 服务id
     * @return 返回结果
     */
    String fetchRunLog(Integer id);

    /**
     * 重启端口
     *
     * @param id 服务id
     * @return 状态
     */
    boolean restartPort(Integer id);

    /**
     * 重启docker服务
     *
     * @param id   服务id
     * @param type 服务类型
     * @return 状态
     */
    boolean restartServer(Integer id, String type);

    /**
     * 更新comfyui-tools
     *
     * @param id 服务id
     * @return 状态
     */
    boolean updateCreativeNode(Integer id);
}
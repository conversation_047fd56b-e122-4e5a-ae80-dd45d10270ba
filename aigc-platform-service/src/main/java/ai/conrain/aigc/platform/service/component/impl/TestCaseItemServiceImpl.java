package ai.conrain.aigc.platform.service.component.impl;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.dao.TestCaseItemDAO;
import ai.conrain.aigc.platform.dal.entity.TestCaseItemDO;
import ai.conrain.aigc.platform.dal.example.TestCaseItemExample;
import ai.conrain.aigc.platform.service.component.TestCaseItemService;
import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.helper.FileHelper;
import ai.conrain.aigc.platform.service.model.biz.abtest.BaseCaseItemDetail;
import ai.conrain.aigc.platform.service.model.biz.abtest.PromptCreativeItemDetail;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TestCaseItemConverter;
import ai.conrain.aigc.platform.service.model.query.TestCaseItemQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseItemVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * TestCaseItemService实现
 *
 * <AUTHOR>
 * @version TestCaseItemService.java v 0.1 2025-08-12 07:18:08
 */
@Slf4j
@Service
public class TestCaseItemServiceImpl implements TestCaseItemService {
    @Autowired
    private TestCaseItemDAO testCaseItemDAO;
    @Autowired
    private FileHelper fileHelper;

    @Override
    public TestCaseItemVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestCaseItemDO data = testCaseItemDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return TestCaseItemConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testCaseItemDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestCaseItem失败");
    }

    @Override
    public TestCaseItemVO insert(TestCaseItemVO testCaseItem) {
        AssertUtil.assertNotNull(testCaseItem, ResultCode.PARAM_INVALID, "testCaseItem is null");
        AssertUtil.assertTrue(testCaseItem.getId() == null, ResultCode.PARAM_INVALID, "testCaseItem.id is present");

        //创建时间、修改时间兜底
        if (testCaseItem.getCreateTime() == null) {
            testCaseItem.setCreateTime(new Date());
        }

        if (testCaseItem.getModifyTime() == null) {
            testCaseItem.setModifyTime(new Date());
        }

        TestCaseItemDO data = TestCaseItemConverter.vo2DO(testCaseItem);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = testCaseItemDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestCaseItem失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestCaseItem返回id为空");
        testCaseItem.setId(data.getId());
        return testCaseItem;
    }

    @Override
    public void updateByIdSelective(TestCaseItemVO testCaseItem) {
        AssertUtil.assertNotNull(testCaseItem, ResultCode.PARAM_INVALID, "testCaseItem is null");
        AssertUtil.assertTrue(testCaseItem.getId() != null, ResultCode.PARAM_INVALID, "testCaseItem.id is null");

        //修改时间必须更新
        testCaseItem.setModifyTime(new Date());
        TestCaseItemDO data = TestCaseItemConverter.vo2DO(testCaseItem);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = testCaseItemDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestCaseItem失败，影响行数:" + n);
    }

    @Override
    public List<TestCaseItemVO> queryTestCaseItemList(TestCaseItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestCaseItemExample example = TestCaseItemConverter.query2Example(query);

        List<TestCaseItemDO> list = testCaseItemDAO.selectByExampleWithBLOBs(example);
        return TestCaseItemConverter.doList2VOList(list);
    }

    @Override
    public Long queryTestCaseItemCount(TestCaseItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestCaseItemExample example = TestCaseItemConverter.query2Example(query);
        return testCaseItemDAO.countByExample(example);
    }

    /**
     * 带条件分页查询测试用例项
     */
    @Override
    public PageInfo<TestCaseItemVO> queryTestCaseItemByPage(TestCaseItemQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestCaseItemVO> page = new PageInfo<>();

        TestCaseItemExample example = TestCaseItemConverter.query2Example(query);
        long totalCount = testCaseItemDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestCaseItemDO> list = testCaseItemDAO.selectByExampleWithBLOBs(example);
        page.setList(TestCaseItemConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void batchInsert(List<TestCaseItemVO> items) {
        List<TestCaseItemDO> list = TestCaseItemConverter.voList2DOList(items);
        testCaseItemDAO.batchInsert(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchInitByExcel(Integer caseId, TestCaseTypeEnum caseType, List<List<String>> lines) {
        List<TestCaseItemVO> items = new ArrayList<>();

        final String[] fromUrl = {null};
        lines.forEach(e -> {
            TestCaseItemVO item = new TestCaseItemVO();

            item.setCaseId(caseId);
            item.setType(caseType);
            item.setRunTimes(1);
            //item.setName(e.get(0));
            BaseCaseItemDetail detail = TestCaseItemConverter.convertDetail(caseType, e);
            if (detail instanceof PromptCreativeItemDetail) {
                PromptCreativeItemDetail promptDetail = (PromptCreativeItemDetail)detail;

                if (StringUtils.isNotBlank(promptDetail.getImagePath())) {
                    fromUrl[0] = fileHelper.fetchFileUrlByFileExists(promptDetail.getImagePath(), fromUrl[0]);
                    if (StringUtils.isNotBlank(fromUrl[0])) {
                        String ossUrl = fileHelper.uploadToOssWithoutCheck(promptDetail.getImagePath(), fromUrl[0]);
                        promptDetail.setImageUrl(ossUrl);
                    } else {
                        log.error("上传测试用例的图片失败，文件未在服务器上找到{}", promptDetail.getImagePath());
                        throw new BizException(ResultCode.SYS_ERROR);
                    }
                }
            }
            item.setExtInfo(JSONObject.parseObject(JSONObject.toJSONString(detail)));
            items.add(item);
        });

        batchInsert(items);
    }

    @Override
    public List<TestCaseItemVO> queryByCaseId(Integer id) {
        TestCaseItemQuery query = new TestCaseItemQuery();
        query.setCaseId(id);
        return queryTestCaseItemList(query);
    }

}
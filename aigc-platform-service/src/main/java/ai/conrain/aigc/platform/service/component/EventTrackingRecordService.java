package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.EventTrackingRecordDO;
import ai.conrain.aigc.platform.service.model.vo.EventTrackingRecordVO;

import java.util.List;

/**
 * Service定义
 *
 * <AUTHOR>
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
public interface EventTrackingRecordService {

    /**
     * 查询对象
     *
     * @param id 主键
     * @return 返回结果
     */
    EventTrackingRecordVO selectById(Integer id);

    /**
     * 删除对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加对象
     *
     * @param eventTrackingRecord 对象参数
     */
    void insert(EventTrackingRecordVO eventTrackingRecord);

    /**
     * 修改对象
     *
     * @param eventTrackingRecord 对象参数
     */
    void updateById(EventTrackingRecordVO eventTrackingRecord);

    /**
     * 埋点信息缓存
     *
     * @param eventTrackingRecord 对象参数
     */
    void record(EventTrackingRecordVO eventTrackingRecord);

    /**
     * 批量插入埋点列表
     *
     * @param list 埋点列表记录
     */
    void batchInsert(List<EventTrackingRecordDO> list);
}
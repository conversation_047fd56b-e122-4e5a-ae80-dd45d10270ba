/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;

/**
 * 待命名的 BatchToAsyncExecutor
 *
 * <AUTHOR>
 * @version : NamedBatchToAsyncExecutor.java, v 0.1 2024/12/6 18:28 renxiao.wu Exp $
 */
public interface NamedBatchToAsyncExecutor extends BatchToAsyncExecutor {
    /**
     * 是否需要执行
     *
     * @param request 请求
     * @param batch
     * @return true需要执行
     */
    boolean aboutTo(CreativeRequest request, CreativeBatchVO batch);

    /**
     * 获取原始值的key
     *
     * @return 原始值的key
     */
    String getOriginKey();

    /**
     * 获取转换后的key
     *
     * @return 转换后的key
     */
    String getTransKey();
}

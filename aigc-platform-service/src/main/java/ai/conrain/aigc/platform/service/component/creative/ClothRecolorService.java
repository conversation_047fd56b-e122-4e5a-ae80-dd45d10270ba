package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.request.ClothRecolorRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class ClothRecolorService extends AbstractCreativeService<ClothRecolorRequest>{

    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.CLOTH_RECOLOR;
    }

    @Override
    protected CreativeBatchVO buildData(ClothRecolorRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO batch = new CreativeBatchVO();

        // 设置类型
        batch.setType(getCreativeType());
        // 设置用户id
        batch.setUserId(OperationContextHolder.getMasterUserId());
        // 设置操作者id
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        // 设置展示图片
        batch.setShowImage(request.getOriginImgUrl());
        // 设置批次数量
        batch.setBatchCnt(1);
        // 设置状态(排队中)
        batch.setStatus(CreativeStatusEnum.QUEUE);
        //默认按参考图片比例
        batch.setImageProportion(CommonConstants.NONE);
        batch.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE, request.getOriginImgUrl());
        batch.addExtInfo(CommonConstants.KEY_ORIGIN_MASK_IMAGE, request.getOriginMaskImgUrl());
        batch.addExtInfo(CommonConstants.KEY_MASK_IMAGE, request.getMaskImgUrl());
        batch.addExtInfo(CommonConstants.KEY_TARGET_HEX_COLOR, request.getTargetHexColor());
        batch.addExtInfo(CommonConstants.KEY_SMART_RECOLOR_MODE, request.getSmartRecolorMode());
        // imageSource 字段 和 taskId 双重验证
        if (StringUtils.equals(HISTORY, request.getImageSource())
                && ObjectUtils.isNotEmpty(batchFillHelper.getTaskByUrl(request.getOriginImgUrl()))) {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        try {
            // 上传图片至 ComfyUI
            batch.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH, comfyUIHelper.upLoadImage(request.getOriginImgUrl()));
            batch.addExtInfo(CommonConstants.KEY_MASK_IMAGE_PATH, comfyUIHelper.upLoadImage(request.getMaskImgUrl()));
        } catch (Exception e) {
            log.error("图片上传至ComfyUI出现异常", e);
        }
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        target.addExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH, inputPath + batch.getStringFromExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH));
        target.addExtInfo(CommonConstants.KEY_MASK_IMAGE_PATH, inputPath + batch.getStringFromExtInfo(CommonConstants.KEY_MASK_IMAGE_PATH));
        target.addExtInfo(CommonConstants.KEY_TARGET_HEX_COLOR, batch.getStringFromExtInfo(CommonConstants.KEY_TARGET_HEX_COLOR));
        target.addExtInfo(CommonConstants.KEY_SMART_RECOLOR_MODE, batch.getStringFromExtInfo(CommonConstants.KEY_SMART_RECOLOR_MODE));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        context.put(CommonConstants.KEY_ORIGIN_IMAGE_PATH, task.getStringFromExtInfo(CommonConstants.KEY_ORIGIN_IMAGE_PATH));
        context.put(CommonConstants.KEY_MASK_IMAGE_PATH, task.getStringFromExtInfo(CommonConstants.KEY_MASK_IMAGE_PATH));
        context.put(CommonConstants.KEY_TARGET_HEX_COLOR, task.getStringFromExtInfo(CommonConstants.KEY_TARGET_HEX_COLOR));
        context.put(CommonConstants.KEY_SMART_RECOLOR_MODE, task.getStringFromExtInfo(CommonConstants.KEY_SMART_RECOLOR_MODE));
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task, Map<String, Object> context) {
        return SystemConstants.CLOTH_RECOLOR_WORKFLOW;
    }
}

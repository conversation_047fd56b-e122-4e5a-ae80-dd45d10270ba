/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import jakarta.annotation.PostConstruct;

import ai.conrain.aigc.platform.service.model.request.CreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 同步转异步执行器管道
 *
 * <AUTHOR>
 * @version : BatchToAsyncExecutorPipeline.java, v 0.1 2024/12/6 18:26 renxiao.wu Exp $
 */
@Slf4j
@Service("batchToAsyncExecutor")
public class BatchToAsyncExecutorPipeline implements BatchToAsyncExecutor {
    @Autowired
    private ApplicationContext applicationContext;

    private final List<BatchToAsyncExecutor> executors = new ArrayList<>();

    @Override
    public void storeSync(CreativeRequest request, CreativeBatchVO batch) {
        executors.forEach(executor -> executor.storeSync(request, batch));
    }

    @Override
    public void restoreTask(CreativeTaskVO task, CreativeBatchVO batch) {
        executors.forEach(executor -> executor.restoreTask(task, batch));
    }

    @Override
    public void asyncExecAndStore(CreativeTaskVO task, List<CreativeElementVO> elements) {
        executors.forEach(executor -> executor.asyncExecAndStore(task, elements));
    }

    @PostConstruct
    private void init() {
        Map<String, BatchToAsyncExecutor> services = applicationContext.getBeansOfType(BatchToAsyncExecutor.class);
        for (BatchToAsyncExecutor service : services.values()) {
            if (service instanceof BatchToAsyncExecutorPipeline) {
                continue;
            }
            executors.add(service);
        }

        log.info("BatchToAsyncExecutorPipeline init success, executors: {}", executors.size());
    }
}

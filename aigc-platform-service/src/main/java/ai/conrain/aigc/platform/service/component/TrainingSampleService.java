package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.vo.training.LabelingProgressVO;

import java.util.List;

/**
 * 训练样本表，存储训练使用的样本数据 Service定义
 *
 * <AUTHOR>
 * @version TrainingSampleService.java
 */
public interface TrainingSampleService {
	
	/**
	 * 查询训练样本表，存储训练使用的样本数据对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	TrainingSampleVO selectById(Integer id);

	/**
	 * 删除训练样本表，存储训练使用的样本数据对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加训练样本表，存储训练使用的样本数据对象
	 * @param trainingSample 对象参数
	 * @return 返回结果
	 */
	TrainingSampleVO insert(TrainingSampleVO trainingSample);

	/**
	 * 修改训练样本表，存储训练使用的样本数据对象
	 * @param trainingSample 对象参数
	 */
	void updateByIdSelective(TrainingSampleVO trainingSample);

	/**
	 * 带条件批量查询训练样本表，存储训练使用的样本数据列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<TrainingSampleVO> queryTrainingSampleList(TrainingSampleQuery query);

	/**
	 * 带条件查询训练样本表，存储训练使用的样本数据数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryTrainingSampleCount(TrainingSampleQuery query);

    TrainingSampleVO getNextSample(Integer roundId, Integer currentSampleId);

    TrainingSampleVO getPrevSample(Integer roundId, Integer currentSampleId);

	/**
	 * 带条件分页查询训练样本表，存储训练使用的样本数据
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<TrainingSampleVO> queryTrainingSampleByPage(TrainingSampleQuery query);

    LabelingProgressVO getLabelingProgress(Integer roundId);

    List<TrainingSampleVO> getLabeledSampleList(Integer roundId);

    List<TrainingSampleVO> getSamplesByTrainingRoundId(Integer roundId);

    void deleteByTrainingRoundId(Integer roundId);
}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.LabelingService;
import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import ai.conrain.aigc.platform.service.component.TrainingSampleService;
import ai.conrain.aigc.platform.service.component.CaptionAttributeService;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.enums.TrainingSampleIdTypeConstant;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.CaptionAttributeQuery;
import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionAttributeVO;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.ModelSceneEnum;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;
import ai.conrain.aigc.platform.service.model.vo.training.*;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 标注服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelingServiceImpl implements LabelingService {

    @Autowired
    private TrainingRoundService trainingRoundService;

    @Autowired
    private TrainingSampleService trainingSampleService;

    @Autowired
    private CaptionAttributeService captionAttributeService;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private ImageService imageService;

    /**
     * 获取待标注样本
     *
     * @param request
     */
    @Override
    public LabelingSampleSummary getLabelingSample(LabelingSampleRequest request) {
        TrainingRoundVO trainingRoundVO = trainingRoundService.selectById(request.getRoundId());
        AssertUtil.assertNotNull(trainingRoundVO, ResultCode.SYS_ERROR, "未找到该训练轮次");

        TrainingSampleVO sampleVO;

        if (request.getNext()) {
            sampleVO = trainingSampleService.getNextSample(request.getRoundId(), request.getCurrentSampleId());
        } else {
            sampleVO = trainingSampleService.getPrevSample(request.getRoundId(), request.getCurrentSampleId());
        }

        LabelingSampleSummary result = new LabelingSampleSummary();

        if (sampleVO != null) {
            LabelingSample labelingSample = new LabelingSample();
            labelingSample.setId(sampleVO.getId());

            if (sampleVO.getSampleDataId() != null) {
                ImageVO imageVO = imageService.selectById(sampleVO.getSampleDataId());
                labelingSample.setUrl(imageVO.getUrl());
            }

            if (sampleVO.getSampleCaptionId() != null) {
                ImageCaptionUserVO imageCaptionUserVO = imageCaptionUserService.selectById(sampleVO.getSampleCaptionId());
                labelingSample.setCaption(imageCaptionUserVO.getCaption());
            }

            result.setSample(labelingSample);
        }

        TrainingSampleQuery query = new TrainingSampleQuery();
        query.setRelatedTrainingId(request.getRoundId());
        query.setRelatedTrainingIdType(TrainingSampleIdTypeConstant.ROUND_ID);
        Long total = trainingSampleService.queryTrainingSampleCount(query);

        result.setTotalCount(total);
        result.setTaskConfig(trainingRoundVO.getTrainingConfig());

        CaptionAttributeQuery attributeQuery = new CaptionAttributeQuery();
        attributeQuery.setIds(trainingRoundVO.getTrainingConfig().getSelectedAttributes());
        result.setAttributes(captionAttributeService.queryCaptionAttributeList(attributeQuery));

        return result;
    }

    /**
     * 保存标注结果
     *
     * @param request 保存标注请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLabels(SaveLabelsRequest request) {
        TrainingSampleVO sampleVO = trainingSampleService.selectById(request.getSampleId());
        AssertUtil.assertNotNull(sampleVO, ResultCode.SYS_ERROR, "未找到该样本数据");
        AssertUtil.assertNotNull(request.getUserId(), ResultCode.PARAM_INVALID, "用户ID不能为空");

        TrainingRoundVO trainingRoundVO = trainingRoundService.selectById(sampleVO.getRelatedTrainingId());
        AssertUtil.assertNotNull(trainingRoundVO, ResultCode.SYS_ERROR, "未找到该训练轮次");

        // 判断当前样本数据是否已存在标注数据
        ImageCaptionUserVO imageCaption;
        if (sampleVO.getSampleCaptionId() != null) {
            imageCaption = imageCaptionUserService.selectById(sampleVO.getSampleCaptionId());
            AssertUtil.assertNotNull(imageCaption, ResultCode.SYS_ERROR, "未找到该样本数据标注数据");

            imageCaption.setType(trainingRoundVO.getModelScene());
            imageCaption.setImageId(sampleVO.getSampleDataId());
            imageCaption.setUserId(request.getUserId());

            com.alibaba.fastjson2.JSONObject caption = new com.alibaba.fastjson2.JSONObject();
            caption.putAll(imageCaption.getCaption());

            for (LabelRequest label : request.getLabels()) {
                CaptionAttributeVO captionAttributeVO = captionAttributeService.selectById(label.getAttributeId());
                if (captionAttributeVO != null) {
                    //format: string，字符串|radio，单选|switch，布尔型（Y/N）|rating，打分，数值型，这里直接写为key:value，暂时不支持一个key同时有多个value的情况
                    caption.put(captionAttributeVO.getKey(), label.getValue());
                }
            }
            imageCaption.setCaption(caption);

            imageCaptionUserService.updateByIdSelective(imageCaption);

        } else {
            //保存标注到image_caption_user
            imageCaption = createImageCaption(trainingRoundVO.getModelScene(), request, sampleVO);
        }

        //保存样本状态
        TrainingSampleVO target = new TrainingSampleVO();
        target.setId(sampleVO.getId());
        target.setSampleCaptionId(imageCaption.getId());
        target.setSampleCaptionIdType(TrainingSampleIdTypeConstant.IMAGE_CAPTION_USER_ID);

        trainingSampleService.updateByIdSelective(target);
    }

    private ImageCaptionUserVO createImageCaption(String modelScene, SaveLabelsRequest request, TrainingSampleVO sampleVO) {
        ImageCaptionUserVO imageCaptionUserVO = new ImageCaptionUserVO();

        /**
         * 打标类型，直接取模型场景
         * @see ai.conrain.aigc.platform.service.model.vo.ModelSceneEnum
         */
        imageCaptionUserVO.setType(modelScene);
        imageCaptionUserVO.setImageId(sampleVO.getSampleDataId());
        imageCaptionUserVO.setUserId(request.getUserId());

        com.alibaba.fastjson2.JSONObject caption = new com.alibaba.fastjson2.JSONObject();
        for (LabelRequest label : request.getLabels()) {
            CaptionAttributeVO captionAttributeVO = captionAttributeService.selectById(label.getAttributeId());
            if (captionAttributeVO != null) {
                //format: string，字符串|radio，单选|switch，布尔型（Y/N）|rating，打分，数值型，这里直接写为key:value，暂时不支持一个key同时有多个value的情况
                caption.put(captionAttributeVO.getKey(), label.getValue());
            }
        }
        imageCaptionUserVO.setCaption(caption);

        return imageCaptionUserService.insert(imageCaptionUserVO);
    }

    /**
     * 导出标注数据
     *
     * @param roundId 轮次ID
     * @return 导出结果
     */
    @Override
    public ExportResultVO exportLabeledData(Integer roundId) {
        return null;
    }
}
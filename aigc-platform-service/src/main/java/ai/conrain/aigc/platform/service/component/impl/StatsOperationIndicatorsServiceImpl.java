package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;
import java.text.SimpleDateFormat;
import java.text.ParseException;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.query.UserQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsOperationIndicatorsDO;
import ai.conrain.aigc.platform.dal.example.StatsOperationIndicatorsExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsOperationIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.converter.StatsOperationIndicatorsConverter;
import ai.conrain.aigc.platform.dal.dao.StatsOperationIndicatorsDAO;

/**
 * StatsOperationIndicatorsService实现
 *
 * <AUTHOR>
 * @version StatsOperationIndicatorsService.java v 0.1 2025-05-16 11:38:50
 */
@Slf4j
@Service
public class StatsOperationIndicatorsServiceImpl implements StatsOperationIndicatorsService {

    @Autowired
    private UserService userService;

    /** DAO */
    @Autowired
    private StatsOperationIndicatorsDAO statsOperationIndicatorsDAO;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private CreativeElementService creativeElementService;

    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public StatsOperationIndicatorsVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsOperationIndicatorsDO data = statsOperationIndicatorsDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsOperationIndicatorsConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsOperationIndicatorsDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsOperationIndicators失败");
    }

    @Override
    public StatsOperationIndicatorsVO insert(StatsOperationIndicatorsVO statsOperationIndicators) {
        AssertUtil.assertNotNull(statsOperationIndicators, ResultCode.PARAM_INVALID, "statsOperationIndicators is null");
        AssertUtil.assertTrue(statsOperationIndicators.getId() == null, ResultCode.PARAM_INVALID, "statsOperationIndicators.id is present");

        // 创建时间、修改时间兜底
        if (statsOperationIndicators.getCreateTime() == null) {
            statsOperationIndicators.setCreateTime(new Date());
        }

        if (statsOperationIndicators.getModifyTime() == null) {
            statsOperationIndicators.setModifyTime(new Date());
        }

        StatsOperationIndicatorsDO data = StatsOperationIndicatorsConverter.vo2DO(statsOperationIndicators);
        Integer n = statsOperationIndicatorsDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsOperationIndicators失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsOperationIndicators返回id为空");
        statsOperationIndicators.setId(data.getId());
        return statsOperationIndicators;
    }


    @Override
    public void updateByIdSelective(StatsOperationIndicatorsVO statsOperationIndicators) {
        AssertUtil.assertNotNull(statsOperationIndicators, ResultCode.PARAM_INVALID, "statsOperationIndicators is null");
        AssertUtil.assertTrue(statsOperationIndicators.getId() != null, ResultCode.PARAM_INVALID, "statsOperationIndicators.id is null");

        // 修改时间必须更新
        statsOperationIndicators.setModifyTime(new Date());
        StatsOperationIndicatorsDO data = StatsOperationIndicatorsConverter.vo2DO(statsOperationIndicators);
        int n = statsOperationIndicatorsDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsOperationIndicators失败，影响行数:" + n);
    }

    @Override
    public List<StatsOperationIndicatorsVO> queryStatsOperationIndicatorsList(StatsOperationIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsOperationIndicatorsExample example = StatsOperationIndicatorsConverter.query2Example(query);

        List<StatsOperationIndicatorsDO> list = statsOperationIndicatorsDAO.selectByExampleWithBLOBs(example);
        return StatsOperationIndicatorsConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsOperationIndicatorsCount(StatsOperationIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsOperationIndicatorsExample example = StatsOperationIndicatorsConverter.query2Example(query);
        long c = statsOperationIndicatorsDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询
     */
    @Override
    public PageInfo<StatsOperationIndicatorsVO> queryStatsOperationIndicatorsByPage(StatsOperationIndicatorsQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        String statsDate = query.getStatsDate();
        String statsType = query.getStatsType();

        // 判断今天的日期所在的 周/月/季 的第一天是否是statsDate,若是则需要走新查询逻辑，若不是则按照原有逻辑
        boolean isFirstDayOfPeriod = isFirstDayOfCurrentPeriod(statsDate, statsType);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String todayDateStr = dateFormat.format(new Date());


        // 若系统用户ID不为空则说明需要查询汇总数据
        if (query.getSystemUserId() != null) {
            List<StatsOperationIndicatorsVO> dailyStatsList = executeTotalPeriodBusinessStats(statsDate, statsDate, todayDateStr);
            // 根据userId分组并聚合数据
            List<StatsOperationIndicatorsVO> aggregatedList = aggregateDailyStatsByUserId(dailyStatsList, statsDate, statsType);

            PageInfo<StatsOperationIndicatorsVO> page = new PageInfo<>();
            if (CollectionUtils.isEmpty(aggregatedList)) {
                page.setList(new ArrayList<>());
                page.setSize(0);
                page.setTotalCount(0);
                page.setHasNextPage(false);
            } else {
                page.setList(aggregatedList);
                page.setSize(aggregatedList.size());
                page.setTotalCount((long) aggregatedList.size());
                page.setHasNextPage(false);
            }
            return page;
        }


        // 根据 isFirstDayOfPeriod 的值，可以决定走新查询逻辑还是原有逻辑
        if (isFirstDayOfPeriod) {
            log.info("statsDate {} 是今天所在周/月/季的第一天，需要走新查询逻辑", statsDate);


            // 查询从statsDate到今天的所有DAILY数据
            List<StatsOperationIndicatorsVO> dailyStatsList = executePeriodBusinessStats(statsDate, statsDate, todayDateStr, statsType, query);

            // 根据userId分组并聚合数据
            List<StatsOperationIndicatorsVO> aggregatedList = aggregateDailyStatsByUserId(dailyStatsList, statsDate, statsType);

            // 重新排序
            reOrder(aggregatedList, query.getOrderBy());

            PageInfo<StatsOperationIndicatorsVO> page = new PageInfo<>();
            if (CollectionUtils.isEmpty(aggregatedList)) {
                page.setList(new ArrayList<>());
                page.setSize(0);
                page.setTotalCount(0);
                page.setHasNextPage(false);
            } else {
                page.setList(aggregatedList);
                page.setSize(aggregatedList.size());
                page.setTotalCount((long) aggregatedList.size());
                page.setHasNextPage(false); // 聚合结果通常只有一页
            }
            return page;
        }

        PageInfo<StatsOperationIndicatorsVO> page = new PageInfo<>();

        StatsOperationIndicatorsExample example = StatsOperationIndicatorsConverter.query2Example(query);
        long totalCount = statsOperationIndicatorsDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<StatsOperationIndicatorsDO> list = statsOperationIndicatorsDAO.selectByExampleWithBLOBs(example);
        page.setList(StatsOperationIndicatorsConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsOperationIndicatorsVO> statsList) {
        if (CollectionUtils.isEmpty(statsList)) {
            return 0;
        }

        List<StatsOperationIndicatorsDO> statsSaleIndicatorsDOList = new ArrayList<>();
        for (StatsOperationIndicatorsVO statsVO : statsList) {
            StatsOperationIndicatorsDO statsDO = StatsOperationIndicatorsConverter.vo2DO(statsVO);
            statsSaleIndicatorsDOList.add(statsDO);
        }

        return statsOperationIndicatorsDAO.batchInsertOrUpdate(statsSaleIndicatorsDOList);
    }

    @Override
    public List<StatsOperationIndicatorsVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsOperationIndicatorsDO> statsOperationIndicatorsDOList = statsOperationIndicatorsDAO.selectStatsInfoByDateAndPeriod(startDate, endDate, statsType);

        // 转换为VO列表
        return StatsOperationIndicatorsConverter.doList2VOList(statsOperationIndicatorsDOList);
    }

    public List<StatsOperationIndicatorsVO> selectStatsInfoByDateAndCondition(String startDate, String endDate, String statsType, Integer userId, String nickName) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsOperationIndicatorsDO> statsOperationIndicatorsDOList = statsOperationIndicatorsDAO.selectStatsInfoByDateAndCondition(startDate, endDate, statsType, userId, nickName);

        // 转换为VO列表
        return StatsOperationIndicatorsConverter.doList2VOList(statsOperationIndicatorsDOList);
    }


    @Override
    public List<StatsOperationIndicatorsExcelVO> doDownload(StatsOperationIndicatorsQuery query) {
        // 封装处理查询条件
        query.setPageNum(1);
        query.setPageSize(1000);

        // 执行查询
        StatsOperationIndicatorsExample example = StatsOperationIndicatorsConverter.query2Example(query);
        List<StatsOperationIndicatorsDO> dataList = statsOperationIndicatorsDAO.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        // 数据类型转换
        List<StatsOperationIndicatorsVO> statsOperationIndicatorsVOS = StatsOperationIndicatorsConverter.doList2VOList(dataList);
        // 最终结果
        List<StatsOperationIndicatorsExcelVO> resultList = new ArrayList<>();

        // 遍历转换数据
        for (StatsOperationIndicatorsVO vo : statsOperationIndicatorsVOS) {
            try {
                StatsOperationIndicatorsExcelVO excelVO = new StatsOperationIndicatorsExcelVO();

                // 直接属性赋值（属性名一致的字段）
                excelVO.setStatsType(vo.getStatsType());
                // 特殊处理 statsDate 字段
                excelVO.setStatsDate(formatStatsDate(vo.getStatsDate(), query.getStatsType()));
                excelVO.setUserId(vo.getUserId());
                excelVO.setName(vo.getName());
                excelVO.setCustomerTotalCount(vo.getCustomerTotalCount());
                excelVO.setCustomerUploadMaterialCount(vo.getCustomerUploadMaterialCount());
                excelVO.setCustomerConversionCount(vo.getCustomerConversionCount());

                // 安全处理消费点数除法运算
                Integer consumptionPoints = vo.getCustomerConsumptionPoints();
                excelVO.setCustomerConsumptionPoints(consumptionPoints != null ? consumptionPoints / 1000 : 0);

                // 安全处理平均消费点数字符串解析
                String avgStr = vo.getCustomerConsumptionPointsAvg();
                String customerConsumptionPointsAvg = "0.00";
                if (avgStr != null && !avgStr.trim().isEmpty()) {
                    try {
                        // 使用Double.parseDouble处理可能包含小数的字符串
                        double avgValue = Double.parseDouble(avgStr);
                        double result = avgValue / 1000;
                        // 保留两位小数
                        customerConsumptionPointsAvg = String.format("%.2f", result);
                    } catch (NumberFormatException e) {
                        log.warn("解析平均消费点数失败，用户ID: {}, 原始值: {}", vo.getUserId(), avgStr, e);
                        customerConsumptionPointsAvg = "0.00";
                    }
                }
                excelVO.setCustomerConsumptionPointsAvg(customerConsumptionPointsAvg);

                // 安全处理百分比字符串拼接
                String activityRate = vo.getCustomerActivityRate();
                excelVO.setCustomerActivityRate(activityRate != null ? activityRate + "%" : "0%");

                String repurchaseRate = vo.getCustomerRepurchaseRate();
                excelVO.setCustomerRepurchaseRate(repurchaseRate != null ? repurchaseRate + "%" : "0%");

                String customModelCustomers = vo.getCustomModelCustomers();
                excelVO.setCustomModelCustomers(customModelCustomers != null ? customModelCustomers + "%" : "0%");

                String customSceneCustomers = vo.getCustomSceneCustomers();
                excelVO.setCustomSceneCustomers(customSceneCustomers != null ? customSceneCustomers + "%" : "0%");

                excelVO.setVideoCount(vo.getVideoCount());

                String videoCountAvg = vo.getVideoCountAvg();
                excelVO.setVideoCountAvg(videoCountAvg != null ? videoCountAvg + "%" : "0%");

                excelVO.setCustomerProtectionMetrics(vo.getCustomerProtectionMetrics());
                excelVO.setDeliveryClothingCount(vo.getDeliveryClothingCount());
                excelVO.setApproveClothingCount(vo.getApproveClothingCount());

                String garmentRebateRate = vo.getGarmentRebateRate();
                excelVO.setGarmentRebateRate(garmentRebateRate != null ? garmentRebateRate + "%" : "0%");

                // 从 extInfo 中获取分子数据（这些方法已经有默认值处理，相对安全）
                excelVO.setCustomerActivityRateMolecular(vo.getStringFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, "0"));
                excelVO.setCustomerRepurchaseRateMolecular(vo.getStringFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, "0"));
                excelVO.setCustomModelCustomersMolecular(vo.getStringFromExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, "0"));
                excelVO.setCustomSceneCustomersMolecular(vo.getStringFromExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, "0"));

                resultList.add(excelVO);
            } catch (Exception e) {
                log.error("处理统计数据转换异常，用户ID: {}, 异常信息: {}", vo != null ? vo.getUserId() : "unknown", e.getMessage(), e);
                // 继续处理下一条记录，不中断整个导出流程
            }
        }

        // 返回结果
        return resultList;
    }


    private void accumulateMetrics(StatsOperationIndicatorsVO aggregatedStats, StatsOperationIndicatorsVO dailyStats) {
        aggregatedStats.setCustomerTotalCount(aggregatedStats.getCustomerTotalCount() + (dailyStats.getCustomerTotalCount() != null ? dailyStats.getCustomerTotalCount() : 0));
        aggregatedStats.setCustomerUploadMaterialCount(aggregatedStats.getCustomerUploadMaterialCount() + (dailyStats.getCustomerUploadMaterialCount() != null ? dailyStats.getCustomerUploadMaterialCount() : 0));
        aggregatedStats.setCustomerConversionCount(aggregatedStats.getCustomerConversionCount() + (dailyStats.getCustomerConversionCount() != null ? dailyStats.getCustomerConversionCount() : 0));
        aggregatedStats.setCustomerConsumptionPoints(aggregatedStats.getCustomerConsumptionPoints() + (dailyStats.getCustomerConsumptionPoints() != null ? dailyStats.getCustomerConsumptionPoints() : 0));
        aggregatedStats.setVideoCount(aggregatedStats.getVideoCount() + (dailyStats.getVideoCount() != null ? dailyStats.getVideoCount() : 0));
        aggregatedStats.setCustomerProtectionMetrics(aggregatedStats.getCustomerProtectionMetrics() + (dailyStats.getCustomerProtectionMetrics() != null ? dailyStats.getCustomerProtectionMetrics() : 0));
        aggregatedStats.setDeliveryClothingCount(aggregatedStats.getDeliveryClothingCount() + (dailyStats.getDeliveryClothingCount() != null ? dailyStats.getDeliveryClothingCount() : 0));
        aggregatedStats.setApproveClothingCount(aggregatedStats.getApproveClothingCount() + (dailyStats.getApproveClothingCount() != null ? dailyStats.getApproveClothingCount() : 0));
    }

    private void accumulateExtInfo(StatsOperationIndicatorsVO aggregatedStats, StatsOperationIndicatorsVO dailyStats) {
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR);
        accumulateExtInfoValue(aggregatedStats, dailyStats, CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR);
    }

    private void accumulateExtInfoValue(StatsOperationIndicatorsVO aggregatedStats, StatsOperationIndicatorsVO dailyStats, String key) {
        aggregatedStats.addExtInfo(key,
                aggregatedStats.getIntegerFromExtInfo(key, 0) +
                        dailyStats.getIntegerFromExtInfo(key, 0));
    }

    private void calculateAveragesAndPercentages(Map<Integer, StatsOperationIndicatorsVO> aggregatedMap, int days) {
        if (days == 0) {
            return; // Avoid division by zero
        }
        for (StatsOperationIndicatorsVO aggregatedStats : aggregatedMap.values()) {
            aggregatedStats.setCustomerConsumptionPointsAvg(String.format("%.2f", (double) aggregatedStats.getCustomerConsumptionPoints() / (days * 1000)));
            aggregatedStats.setCustomerActivityRate(calculateRate(aggregatedStats, CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR, CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR));
            aggregatedStats.setCustomerRepurchaseRate(calculateRate(aggregatedStats, CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR, CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR));
            aggregatedStats.setCustomModelCustomers(calculateRate(aggregatedStats, CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR));
            aggregatedStats.setCustomSceneCustomers(calculateRate(aggregatedStats, CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR));
            aggregatedStats.setVideoCountAvg(String.format("%.2f", (double) aggregatedStats.getVideoCount() / days));
        }
    }

    private String calculateRate(StatsOperationIndicatorsVO stats, String molecularKey, String denominatorKey) {
        int molecular = stats.getIntegerFromExtInfo(molecularKey, 0);
        int denominator = stats.getIntegerFromExtInfo(denominatorKey, 0);
        return String.format("%.2f", (denominator > 0 ? (double) molecular / denominator * 100 : 0));
    }

    /**
     * 根据userId分组并聚合每日统计数据。
     *
     * @param dailyStatsList 每日统计数据列表
     * @param statsDate 聚合后的统计日期（周/月/季的第一天）
     * @param statsType 聚合后的统计类型（WEEKLY/MONTHLY/QUARTERLY）
     * @return 聚合后的统计数据列表，每个元素代表一个userId的聚合结果
     */
    private List<StatsOperationIndicatorsVO> aggregateDailyStatsByUserId(List<StatsOperationIndicatorsVO> dailyStatsList, String statsDate, String statsType) {
        if (CollectionUtils.isEmpty(dailyStatsList)) {
            return new ArrayList<>();
        }

        // 使用Map按userId分组聚合数据
        Map<Integer, StatsOperationIndicatorsVO> aggregatedMap = new HashMap<>();

        for (StatsOperationIndicatorsVO dailyStats : dailyStatsList) {
            Integer userId = dailyStats.getUserId();
            if (userId == null) {
                log.warn("每日统计数据中存在userId为空的记录，跳过聚合: {}", dailyStats);
                continue;
            }

            StatsOperationIndicatorsVO aggregatedStats = aggregatedMap.computeIfAbsent(userId, k -> {
                StatsOperationIndicatorsVO newAggregatedStats = new StatsOperationIndicatorsVO();
                newAggregatedStats.setUserId(userId);
                newAggregatedStats.setName(dailyStats.getName()); // 假设name在同一userId下是一致的
                newAggregatedStats.setStatsDate(statsDate); // 聚合后的日期为周/月/季的第一天
                newAggregatedStats.setStatsType(statsType); // 聚合后的类型为周/月/季
                return newAggregatedStats;
            });

            accumulateMetrics(aggregatedStats, dailyStats);
            accumulateExtInfo(aggregatedStats, dailyStats);
        }

        // 重新计算平均值和百分比
        calculateAveragesAndPercentages(aggregatedMap, dailyStatsList.size());

        return new ArrayList<>(aggregatedMap.values());
    }


    /**
     * 判断给定的日期是否是当前日期所在周/月/季的第一天。
     * statsDate为需要对比的日期，statsDate必定为周一，每个月的第一天或季度的第一天，且格式为yyyy-MM-DD。
     * statsType为时间类型（WEEKLY/MONTHLY/QUARTERLY）需要根据type进行判断取哪个的第一天。
     *
     * @param statsDate 统计日期，格式为yyyy-MM-DD
     * @param statsType 统计类型 (WEEKLY/MONTHLY/QUARTERLY)
     * @return 如果是当前周/月/季的第一天，则返回true，否则返回false
     */
    private boolean isFirstDayOfCurrentPeriod(String statsDate, String statsType) {
        if (StringUtils.isBlank(statsDate) || StringUtils.isBlank(statsType)) {
            return false;
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parsedStatsDate = dateFormat.parse(statsDate);

            // 当天时间
            Calendar calToday = Calendar.getInstance();
            Date today = new Date();
            calToday.setTime(today);

            Calendar calStatsDate = Calendar.getInstance();
            calStatsDate.setTime(parsedStatsDate);

            switch (statsType) {
                case "WEEKLY":
                    // 判断是否是本周第一天 (周一)
                    Calendar firstDayOfWeek = (Calendar) calToday.clone();
                    firstDayOfWeek.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                    // 确保年份和月份也匹配，因为set(DAY_OF_WEEK, MONDAY)可能会跨月或跨年
                    if (firstDayOfWeek.get(Calendar.DAY_OF_MONTH) > calToday.get(Calendar.DAY_OF_MONTH)) {
                        firstDayOfWeek.add(Calendar.WEEK_OF_YEAR, -1);
                    }
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfWeek.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfWeek.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfWeek.get(Calendar.DAY_OF_MONTH);

                case "MONTHLY":
                    // 判断是否是本月第一天
                    Calendar firstDayOfMonth = (Calendar) calToday.clone();
                    firstDayOfMonth.set(Calendar.DAY_OF_MONTH, 1);
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfMonth.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfMonth.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfMonth.get(Calendar.DAY_OF_MONTH);

                case "QUARTERLY":
                    // 判断是否是本季度第一天
                    Calendar firstDayOfQuarter = (Calendar) calToday.clone();
                    int currentMonth = firstDayOfQuarter.get(Calendar.MONTH);
                    int quarterFirstMonth = (currentMonth / 3) * 3; // 0, 3, 6, 9
                    firstDayOfQuarter.set(Calendar.MONTH, quarterFirstMonth);
                    firstDayOfQuarter.set(Calendar.DAY_OF_MONTH, 1);
                    return calStatsDate.get(Calendar.YEAR) == firstDayOfQuarter.get(Calendar.YEAR) &&
                            calStatsDate.get(Calendar.MONTH) == firstDayOfQuarter.get(Calendar.MONTH) &&
                            calStatsDate.get(Calendar.DAY_OF_MONTH) == firstDayOfQuarter.get(Calendar.DAY_OF_MONTH);

                default:
                    return false;
            }
        } catch (ParseException e) {
            log.warn("解析statsDate失败: {}", statsDate, e);
            return false;
        }
    }

    /**
     * 格式化统计日期
     *
     * @param statsDate 统计日期
     * @param statsType 统计类型
     * @return 格式化后的统计日期
     */
    private String formatStatsDate(String statsDate, String statsType) {
        if (statsDate == null || statsType == null) {
            return statsDate;
        }

        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = dateFormat.parse(statsDate);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);

            switch (statsType) {
                case "WEEKLY":
                    // 周：显示为 "开始日期～结束日期"
                    Calendar endCal = Calendar.getInstance();
                    endCal.setTime(date);
                    endCal.add(Calendar.DAY_OF_MONTH, 6); // 加6天得到一周的结束日期
                    return statsDate + "～" + dateFormat.format(endCal.getTime());

                case "MONTHLY":
                    // 月：显示为 "月初～月末"
                    Calendar monthStart = Calendar.getInstance();
                    monthStart.setTime(date);
                    monthStart.set(Calendar.DAY_OF_MONTH, 1);

                    Calendar monthEnd = Calendar.getInstance();
                    monthEnd.setTime(date);
                    monthEnd.set(Calendar.DAY_OF_MONTH, monthEnd.getActualMaximum(Calendar.DAY_OF_MONTH));

                    return dateFormat.format(monthStart.getTime()) + "～" + dateFormat.format(monthEnd.getTime());

                case "QUARTERLY":
                    // 季度：显示为 "季度开始～季度结束"
                    Calendar quarterStart = Calendar.getInstance();
                    quarterStart.setTime(date);
                    int currentMonth = quarterStart.get(Calendar.MONTH);
                    int quarterFirstMonth = (currentMonth / 3) * 3;
                    quarterStart.set(Calendar.MONTH, quarterFirstMonth);
                    quarterStart.set(Calendar.DAY_OF_MONTH, 1);

                    Calendar quarterEnd = Calendar.getInstance();
                    quarterEnd.setTime(date);
                    int quarterLastMonth = (currentMonth / 3) * 3 + 2;
                    quarterEnd.set(Calendar.MONTH, quarterLastMonth);
                    quarterEnd.set(Calendar.DAY_OF_MONTH, quarterEnd.getActualMaximum(Calendar.DAY_OF_MONTH));

                    return dateFormat.format(quarterStart.getTime()) + "～" + dateFormat.format(quarterEnd.getTime());

                case "TOTAL":
                    // TOTAL：置空
                    return "";

                case "DAILY":
                default:
                    // 日：保持原样
                    return statsDate;
            }
        } catch (ParseException e) {
            log.warn("解析日期失败: {}", statsDate, e);
            return statsDate;
        }
    }


    private List<StatsOperationIndicatorsVO> executePeriodBusinessStats(String storageDate, String startDate, String endDate, String statsType, StatsOperationIndicatorsQuery query) {
        // 查询某个时间段内的统计数据
        List<StatsOperationIndicatorsVO> sourceStatsList = selectStatsInfoByDateAndCondition(startDate, endDate, StatsPeriodEnum.DAILY.getCode(), query.getUserId(), query.getName());

        // 如果统计数据为空，则直接返回0
        if (org.apache.commons.collections.CollectionUtils.isEmpty(sourceStatsList)) {
            log.info("时间范围{}至{}内未找到日统计数据", startDate, endDate);
            return null;
        }

        // 查询所有运营信息（管理员）
        List<UserVO> adminUserList = queryAdminUsers();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(adminUserList)) {
            return null;
        }
        List<Integer> adminUserIdList = adminUserList.stream().map(UserVO::getId).collect(Collectors.toList());
        Map<Integer, List<UserVO>> customerUSerList = queryCustomerUsers(adminUserIdList);

        // 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 根据用户ID进行分组
        Map<Integer, List<StatsOperationIndicatorsVO>> userIdStatsMap = sourceStatsList.stream()
                .collect(Collectors.groupingBy(StatsOperationIndicatorsVO::getUserId));

        // 汇总每个用户的统计数据
        List<StatsOperationIndicatorsVO> targetStatsList = new ArrayList<>();

        for (Map.Entry<Integer, List<StatsOperationIndicatorsVO>> entry : userIdStatsMap.entrySet()) {
            Integer userId = entry.getKey();
            List<StatsOperationIndicatorsVO> userStats = entry.getValue();

            // 如果系统调度记录则进行跳过
            if (Objects.equals(userId, CommonUtil.mockSystemContext().getMasterUser())) {
                continue;
            }

            // 提取当前运营的客户列表
            List<UserVO> customerUserList = customerUSerList.get(userId);
            List<Integer> distinctCustomerIds = new ArrayList<>();
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(customerUserList)) {
                distinctCustomerIds = customerUserList.stream().map(UserVO::getId).distinct()
                        .collect(Collectors.toList());
            }

            // 确保有数据
            if (org.apache.commons.collections.CollectionUtils.isEmpty(userStats)) {
                continue;
            }

            // 获取最新的一条记录，用于提取用户名称等基本信息
            StatsOperationIndicatorsVO firstStat = userStats.stream()
                    .max(Comparator.comparing(StatsOperationIndicatorsVO::getStatsDate))
                    .orElse(userStats.get(0));

            // 进行累加以及比例计算
            StatsOperationIndicatorsVO targetStats = new StatsOperationIndicatorsVO();
            targetStats.setUserId(userId);
            targetStats.setName(firstStat.getName());
            targetStats.setStatsDate(storageDate);
            targetStats.setStatsType(statsType);
            targetStats.setCreateTime(new Date());
            targetStats.setModifyTime(new Date());

            // 初始化各项指标的默认值，避免空值问题
            targetStats.setCustomerConversionCount(0);
            targetStats.setCustomerConsumptionPoints(0);
            targetStats.setCustomerConsumptionPointsAvg("0.00");
            targetStats.setCustomerActivityRate("0.00");
            targetStats.setCustomerRepurchaseRate("0.00");
            targetStats.setCustomModelCustomers("0.00");
            targetStats.setCustomSceneCustomers("0.00");
            targetStats.setCustomerProtectionMetrics(0);
            targetStats.setDeliveryClothingCount(0);
            targetStats.setApproveClothingCount(0);
            targetStats.setGarmentRebateRate("0.00");
            targetStats.setCustomerTotalCount(firstStat.getCustomerTotalCount());

            // 客户转换量 汇总
            int totalCustomerConversionCount = userStats.stream()
                    .filter(data -> data.getCustomerConversionCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getCustomerConversionCount)
                    .sum();

            // 客户消耗点数 汇总
            int totalCustomerConsumptionPoints = userStats.stream()
                    .filter(data -> data.getCustomerConsumptionPoints() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getCustomerConsumptionPoints)
                    .sum();

            // 交付服装量 汇总
            int totalDeliveryClothingCount = userStats.stream()
                    .filter(data -> data.getDeliveryClothingCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getDeliveryClothingCount)
                    .sum();

            // 审核服装量 汇总
            int totalApproveClothingCount = userStats.stream()
                    .filter(data -> data.getApproveClothingCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getApproveClothingCount)
                    .sum();

            // 视频数量 汇总
            int totalVideoCount = userStats.stream()
                    .filter(data -> data.getVideoCount() != null)
                    .mapToInt(StatsOperationIndicatorsVO::getVideoCount)
                    .sum();

            // 待保护客户 (取 最后一天 的数据)
            targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());

            // 设置累加的数值型指标
            targetStats.setCustomerConversionCount(totalCustomerConversionCount);
            targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
            targetStats.setDeliveryClothingCount(totalDeliveryClothingCount);
            targetStats.setApproveClothingCount(totalApproveClothingCount);
            targetStats.setVideoCount(totalVideoCount);

            // 计算加权平均的比率指标
            calculateWeightedAverageRatios(userStats, targetStats);

            // 重新计算模型比例
            getCustomModelRatio(userId, distinctCustomerIds, userFaceSceneList, targetStats);
            // 重新计算场景比例
            getCustomSceneRatio(userId, distinctCustomerIds, userFaceSceneList, targetStats);
            // 重新计算上传服装数量
            getCustomerUploadMaterialCount(startDate, endDate, userId, distinctCustomerIds, targetStats);

            // 将汇总的统计数据添加到结果列表中
            targetStatsList.add(targetStats);
        }
        return targetStatsList;
    }


    private List<StatsOperationIndicatorsVO> executeTotalPeriodBusinessStats(String storageDate, String startDate, String endDate) {
        // 查询某个时间段内的统计数据
        List<StatsOperationIndicatorsVO> sourceStatsList = selectStatsInfoByDateAndPeriod(startDate, endDate, StatsPeriodEnum.DAILY.getCode());

        // 如果统计数据为空，则直接返回0
        if (CollectionUtils.isEmpty(sourceStatsList)) {
            return null;
        }

        // 查询所有运营信息（管理员）
        List<UserVO> adminUserList = queryAdminUsers();
        if (CollectionUtils.isEmpty(adminUserList)) {
            return null;
        }

        // 获取所有用户的定制元素信息
        List<UserFaceSceneVO> userFaceSceneList = creativeElementService.selectAllUserElementByDate(null, endDate);

        // 根据用户ID进行分组
        Map<Integer, List<StatsOperationIndicatorsVO>> userIdStatsMap = sourceStatsList.stream()
                .collect(Collectors.groupingBy(StatsOperationIndicatorsVO::getUserId));


        // 提取系统调度的记录
        List<StatsOperationIndicatorsVO> statsOperationIndicatorsVOList = userIdStatsMap
                .get(CommonUtil.mockSystemContext().getMasterUser());

        // 汇总系统调度的数据
        StatsOperationIndicatorsVO statsOperationIndicatorsVO = buildTotalPeriodBusinessStats(storageDate, startDate,
                endDate, StatsPeriodEnum.TOTAL, statsOperationIndicatorsVOList, userFaceSceneList);


        return Collections.singletonList(statsOperationIndicatorsVO);
    }


    /**
     * 查询管理员用户
     *
     * @return 管理员用户列表
     */
    private List<UserVO> queryAdminUsers() {
        UserQuery userQuery = new UserQuery();
        userQuery.setRoleType(RoleTypeEnum.ADMIN.getCode());
        return userService.queryUsers(userQuery);
    }

    /**
     * 根据用户 id 查询客户 Id 信息
     *
     * @param adminUserIdList 运营 id 列表
     * @return 客户集合
     */
    private Map<Integer, List<UserVO>> queryCustomerUsers(List<Integer> adminUserIdList) {
        // 首先查询userProfile表
        UserProfileQuery userProfileQuery = new UserProfileQuery();
        userProfileQuery.setProfileKey(CommonConstants.KEY_PROMPT_USER_ID);
        userProfileQuery.setProfileValList(adminUserIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        List<UserProfileVO> userProfileVOList = userProfileService.queryUserProfileList(userProfileQuery);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(userProfileVOList)) {
            return null;
        }
        List<Integer> userIdList = userProfileVOList.stream().map(UserProfileVO::getUid).collect(Collectors.toList());

        // 查询用户列表
        UserQuery userQuery = new UserQuery();
        userQuery.setIds(userIdList);
        userQuery.setRefundedMemo(Boolean.TRUE);
        List<UserVO> userVOList = userService.queryUsers(userQuery);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(userVOList)) {
            return null;
        }

        // 按照profileVal分组
        Map<String, List<UserProfileVO>> profileValMap = userProfileVOList.stream()
                .collect(Collectors.groupingBy(UserProfileVO::getProfileVal));

        // 构建最终结果 adminUserId -> 对应的客户用户列表
        Map<Integer, List<UserVO>> customerUserMap = new HashMap<>();
        profileValMap.forEach((adminUserId, profiles) -> {
            // 获取该运营关联的所有客户ID
            List<Integer> customerIds = profiles.stream().map(UserProfileVO::getUid).collect(Collectors.toList());

            // 从userVOList中筛选出这些客户
            List<UserVO> customers = userVOList.stream().filter(user -> customerIds.contains(user.getId()))
                    .collect(Collectors.toList());

            // 添加到结果Map中
            customerUserMap.put(Integer.parseInt(adminUserId), customers);
        });

        // 返回结果
        return customerUserMap;
    }

    /**
     * 计算加权平均比率指标
     *
     * @param sourceStatsList 源统计数据列表
     * @param targetStats     目标统计数据
     */
    private void calculateWeightedAverageRatios(List<StatsOperationIndicatorsVO> sourceStatsList,
                                                StatsOperationIndicatorsVO targetStats) {
        // 累计所有指标的分子和分母
        int totalActivityRateMolecular = 0; // 活跃率分子（活跃客户数）
        int totalActivityRateDenominator = 0; // 活跃率分母（总客户数）

        int totalRepurchaseRateMolecular = 0; // 复购率分子（复购客户数）

        int totalGarmentRebateRateMolecular = 0; // 服装返点率分子（退点数量）
        int totalGarmentRebateRateDenominator = 0; // 服装返点率分母（总数量）

        // 从每个统计记录的extInfo中获取分子和分母信息，分别累加
        for (StatsOperationIndicatorsVO data : sourceStatsList) {
            if (data.getExtInfo() == null) {
                continue;
            }

            // 客户活跃率
            Integer activityRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR);
            Integer activityRateDenominator = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);
            if (activityRateMolecular != null && activityRateDenominator != null) {
                totalActivityRateMolecular += activityRateMolecular;
                totalActivityRateDenominator += activityRateDenominator;
            }

            // 客户复购率（复购客户数/所有客户数）

            Integer repurchaseRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR);
            if (repurchaseRateMolecular != null) {
                totalRepurchaseRateMolecular += repurchaseRateMolecular;
            }

            // 服装返点率
            Integer garmentRebateRateMolecular = data
                    .getIntegerFromExtInfo(CommonConstants.GARMENT_REBATE_RATE_MOLECULAR);
            Integer garmentRebateRateDenominator = data
                    .getIntegerFromExtInfo(CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR);
            if (garmentRebateRateMolecular != null && garmentRebateRateDenominator != null) {
                totalGarmentRebateRateMolecular += garmentRebateRateMolecular;
                totalGarmentRebateRateDenominator += garmentRebateRateDenominator;
            }
        }

        // 计算各项指标的比率并设置到结果对象中
        // 1. 计算客户活跃率
        calculateRatio(totalActivityRateMolecular, totalActivityRateDenominator,
                "客户活跃率", "活跃客户总数", "客户总数",
                targetStats,
                targetStats::setCustomerActivityRate,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_ACTIVITY_RATE_DENOMINATOR);

        // 2. 计算客户复购率
        calculateRatio(totalRepurchaseRateMolecular, targetStats.getCustomerTotalCount(),
                "客户复购率", "复购客户总数", "有首付客户总数",
                targetStats,
                targetStats::setCustomerRepurchaseRate,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_MOLECULAR,
                CommonConstants.CUSTOMER_REPURCHASE_RATE_DENOMINATOR);

        // 5. 计算服装返点率
        calculateRatio(totalGarmentRebateRateMolecular, totalGarmentRebateRateDenominator,
                "服装返点率", "退点数量", "总服装数量",
                targetStats,
                targetStats::setGarmentRebateRate,
                CommonConstants.GARMENT_REBATE_RATE_MOLECULAR,
                CommonConstants.GARMENT_REBATE_RATE_DENOMINATOR);

        // 计算客户消耗点数平均值（总消耗点数/客户总数）
        if (targetStats.getCustomerConsumptionPoints() != null && targetStats.getCustomerConsumptionPoints() > 0) {
            targetStats.setCustomerConsumptionPointsAvg(String.format("%.2f",
                    (double) targetStats.getCustomerConsumptionPoints() / targetStats.getCustomerTotalCount()));
        } else {
            targetStats.setCustomerConsumptionPointsAvg("0.00");
        }

        // 计算视频数量平均值（视频数量/客户总数）
        if (targetStats.getVideoCount() != null && targetStats.getVideoCount() > 0) {
            targetStats.setVideoCountAvg(
                    String.format("%.2f", (double) targetStats.getVideoCount() / targetStats.getCustomerTotalCount()));
        } else {
            targetStats.setVideoCountAvg("0.00");
        }

    }

    /**
     * 计算比率并设置到结果对象中
     *
     * @param molecular       分子值
     * @param denominator     分母值
     * @param indicatorName   指标名称
     * @param molecularDesc   分子描述
     * @param denominatorDesc 分母描述
     * @param target          目标对象
     * @param setter          设置方法
     * @param molecularKey    分子键名
     * @param denominatorKey  分母键名
     */
    private void calculateRatio(int molecular, int denominator,
                                String indicatorName, String molecularDesc, String denominatorDesc,
                                StatsOperationIndicatorsVO target, Consumer<String> setter,
                                String molecularKey, String denominatorKey) {

        // 如果分母大于0，计算比率
        if (denominator > 0) {
            double ratio = (double) molecular / denominator * 100;
            String formattedRatio = String.format("%.2f", ratio);

            // 设置结果
            setter.accept(formattedRatio);

            // 保存分子分母到target的extInfo中
            target.addExtInfo(molecularKey, molecular);
            target.addExtInfo(denominatorKey, denominator);

            log.info("计算{} - {}:{}, {}:{}, 计算结果:{}%",
                    indicatorName, molecularDesc, molecular, denominatorDesc, denominator, formattedRatio);
        } else {
            // 设置默认值
            setter.accept("0.00");

            // 保存默认分子分母
            target.addExtInfo(molecularKey, 0);
            target.addExtInfo(denominatorKey, 0);

            log.info("计算{} - 无有效数据，设置为0.00", indicatorName);
        }
    }

    /**
     * 获取定制模特比例
     *
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户用户列表
     * @param userFaceSceneList          用户定制元素列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomModelRatio(Integer adminUserId, List<Integer> distinctCustomerIds,
                                     List<UserFaceSceneVO> userFaceSceneList, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(distinctCustomerIds) || org.apache.commons.collections.CollectionUtils.isEmpty(userFaceSceneList)) {
                setDefaultCustomModelRatio(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计使用定制模特的客户数
            int customModelCustomerCount = 0;

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制模特
                boolean hasCustomModel = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getFaceCnt() != null && element.getFaceCnt() > 0);

                if (hasCustomModel) {
                    customModelCustomerCount++;
                }
            }

            // 计算定制模特比例 = 使用定制模特的客户数 / 总客户数
            double customModelRatio = (double) customModelCustomerCount / distinctCustomerIds.size() * 100;
            String formattedRatio = String.format("%.2f", customModelRatio);

            log.info("定制模特比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制模特客户数:{}, 定制模特比例:{}%",
                    adminUserId, distinctCustomerIds.size(), customModelCustomerCount, formattedRatio);

            // 设置定制模特比例
            statsOperationIndicatorsVO.setCustomModelCustomers(formattedRatio);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR,
                    customModelCustomerCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR,
                    distinctCustomerIds.size());

        } catch (Exception e) {
            log.error("计算定制模特比例异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomModelRatio(statsOperationIndicatorsVO, distinctCustomerIds);
        }
    }

    /**
     * 设置定制模特比例默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomModelRatio(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                            List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomModelCustomers("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_MODEL_CUSTOMERS_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 设置定制场景比例默认值
     *
     * @param statsOperationIndicatorsVO 运营指标数据
     * @param distinctCustomerIds        去重后的客户ID列表
     */
    private void setDefaultCustomSceneRatio(StatsOperationIndicatorsVO statsOperationIndicatorsVO,
                                            List<Integer> distinctCustomerIds) {
        statsOperationIndicatorsVO.setCustomSceneCustomers("0.00");
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR, 0);
        statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR,
                Objects.isNull(distinctCustomerIds) ? 0 : distinctCustomerIds.size());
    }

    /**
     * 获取定制场景比例
     *
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户用户列表
     * @param userFaceSceneList          用户定制元素列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomSceneRatio(Integer adminUserId, List<Integer> distinctCustomerIds,
                                     List<UserFaceSceneVO> userFaceSceneList, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {
        try {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(distinctCustomerIds) || distinctCustomerIds.isEmpty()
                    || org.apache.commons.collections.CollectionUtils.isEmpty(userFaceSceneList)) {
                setDefaultCustomSceneRatio(statsOperationIndicatorsVO, distinctCustomerIds);
                return;
            }

            // 统计使用定制场景的客户数
            int customSceneCustomerCount = 0; // 使用了定制场景的客户数

            for (Integer customerId : distinctCustomerIds) {
                // 查找该客户是否有定制场景
                boolean hasCustomScene = userFaceSceneList.stream()
                        .filter(element -> customerId.equals(element.getUserId()))
                        .anyMatch(element -> element.getSceneCnt() != null && element.getSceneCnt() > 0);

                if (hasCustomScene) {
                    customSceneCustomerCount++;
                }
            }

            // 计算定制场景比例 = 使用定制场景的客户数 / 总客户数
            double customSceneRatio = (double) customSceneCustomerCount / distinctCustomerIds.size() * 100;
            String formattedRatio = String.format("%.2f", customSceneRatio);

            log.info("定制场景比例统计 - 销售ID:{}, 实际客户总数:{}, 使用定制场景客户数:{}, 定制场景比例:{}%",
                    adminUserId, distinctCustomerIds.size(), customSceneCustomerCount, formattedRatio);

            // 设置定制场景比例
            statsOperationIndicatorsVO.setCustomSceneCustomers(formattedRatio);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_MOLECULAR,
                    customSceneCustomerCount);
            statsOperationIndicatorsVO.addExtInfo(CommonConstants.CUSTOM_SCENE_CUSTOMERS_DENOMINATOR,
                    distinctCustomerIds.size());

        } catch (Exception e) {
            log.error("计算定制场景比例异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            setDefaultCustomSceneRatio(statsOperationIndicatorsVO, distinctCustomerIds);
        }
    }

    /**
     * 获取上传服装总数
     *
     * @param startDate                  开始日期
     * @param endDate                    结束日期
     * @param adminUserId                运营用户id
     * @param distinctCustomerIds        客户id 列表
     * @param statsOperationIndicatorsVO 运营指标数据
     */
    private void getCustomerUploadMaterialCount(String startDate, String endDate, Integer adminUserId,
                                                List<Integer> distinctCustomerIds, StatsOperationIndicatorsVO statsOperationIndicatorsVO) {

        try {
            // 如果客户列表为空，直接返回
            if (org.apache.commons.collections.CollectionUtils.isEmpty(distinctCustomerIds)) {
                statsOperationIndicatorsVO.setCustomerUploadMaterialCount(0);
                return;
            }

            // 查询指定时间范围内已交付服装模型
            MaterialModelQuery materialModelQuery = new MaterialModelQuery();
            materialModelQuery.setStartDate(startDate);
            materialModelQuery.setEndDate(endDate);
            materialModelQuery.setUserIds(distinctCustomerIds);
            List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);

            // 设置上传服装总数
            statsOperationIndicatorsVO.setCustomerUploadMaterialCount(materialModelVOList.size());
        } catch (Exception e) {
            log.error("获取上传服装总数异常，用户ID:{}, 异常:{}", adminUserId, e.getMessage(), e);
            statsOperationIndicatorsVO.setCustomerUploadMaterialCount(0);
        }
    }

    /**
     * 构建累计汇总数据
     *
     * @param storageDate       存储日期
     * @param startDate         开始日期
     * @param endDate           结束日期
     * @param periodEnum        统计周期
     * @param userStats         运营指标数据列表
     * @param userFaceSceneList 素材模型列表
     * @return 运营指标数据
     */
    private StatsOperationIndicatorsVO buildTotalPeriodBusinessStats(String storageDate, String startDate,
                                                                     String endDate, StatsPeriodEnum periodEnum,
                                                                     List<StatsOperationIndicatorsVO> userStats,
                                                                     List<UserFaceSceneVO> userFaceSceneList) {

        // 查询 vip  用户列表
        List<UserVO> vipUserList = userService.queryAllVIPOrPaidCustomer();

        // vip用户 id 列表
        List<Integer> vipUserIdList = vipUserList.stream().map(UserVO::getId).distinct().collect(Collectors.toList());

        // 用户Id / 昵称
        Integer masterUserId = CommonUtil.mockSystemContext().getMasterUser();
        String masterNickname = CommonUtil.mockSystemContext().getMasterNick();


        // 获取最新的一条记录，用于提取用户名称等基本信息
        StatsOperationIndicatorsVO firstStat = userStats.stream()
                .max(Comparator.comparing(StatsOperationIndicatorsVO::getStatsDate))
                .orElse(userStats.get(0));


        StatsOperationIndicatorsVO targetStats = new StatsOperationIndicatorsVO();
        targetStats.setUserId(masterUserId);
        targetStats.setName(masterNickname);
        targetStats.setStatsDate(storageDate);
        targetStats.setStatsType(periodEnum.getCode());
        targetStats.setCreateTime(new Date());
        targetStats.setModifyTime(new Date());

        // 初始化各项指标的默认值，避免空值问题
        targetStats.setCustomerConversionCount(0);
        targetStats.setCustomerConsumptionPoints(0);
        targetStats.setCustomerConsumptionPointsAvg("0.00");
        targetStats.setCustomerActivityRate("0.00");
        targetStats.setCustomerRepurchaseRate("0.00");
        targetStats.setCustomModelCustomers("0.00");
        targetStats.setCustomSceneCustomers("0.00");
        targetStats.setCustomerProtectionMetrics(0);
        targetStats.setDeliveryClothingCount(0);
        targetStats.setApproveClothingCount(0);
        targetStats.setGarmentRebateRate("0.00");
        targetStats.setCustomerTotalCount(firstStat.getCustomerTotalCount());

        // 客户消耗点数 汇总
        int totalCustomerConsumptionPoints = userStats.stream()
                .filter(data -> data.getCustomerConsumptionPoints() != null)
                .mapToInt(StatsOperationIndicatorsVO::getCustomerConsumptionPoints)
                .sum();

        // 交付服装量 汇总
        int totalDeliveryClothingCount = userStats.stream()
                .filter(data -> data.getDeliveryClothingCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getDeliveryClothingCount)
                .sum();

        // 审核服装量 汇总
        int totalApproveClothingCount = userStats.stream()
                .filter(data -> data.getApproveClothingCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getApproveClothingCount)
                .sum();

        // 视频数量 汇总
        int totalVideoCount = userStats.stream()
                .filter(data -> data.getVideoCount() != null)
                .mapToInt(StatsOperationIndicatorsVO::getVideoCount)
                .sum();

        // 待保护客户 (取 最后一天 的数据)
        targetStats.setCustomerProtectionMetrics(firstStat.getCustomerProtectionMetrics());

        // 设置累加的数值型指标
        targetStats.setCustomerConsumptionPoints(totalCustomerConsumptionPoints);
        targetStats.setDeliveryClothingCount(totalDeliveryClothingCount);
        targetStats.setApproveClothingCount(totalApproveClothingCount);
        targetStats.setVideoCount(totalVideoCount);

        // 计算加权平均的比率指标
        calculateWeightedAverageRatios(userStats, targetStats);

        // 重新计算模型比例
        getCustomModelRatio(masterUserId, vipUserIdList, userFaceSceneList, targetStats);
        // 重新计算场景比例
        getCustomSceneRatio(masterUserId, vipUserIdList, userFaceSceneList, targetStats);
        // 重新计算上传服装数量
        getCustomerUploadMaterialCount(startDate, endDate, masterUserId, vipUserIdList, targetStats);

        return targetStats;
    }

    /**
     * 重新进行排序
     */
    private void reOrder(List<StatsOperationIndicatorsVO> aggregatedList, String orderBy) {
        if (StringUtils.isEmpty(orderBy) || aggregatedList.isEmpty()) {
            return;
        }

        // 默认降序
        boolean ascending = orderBy.toLowerCase().contains("asc");
        Comparator<StatsOperationIndicatorsVO> comparator = null;

        if (orderBy.contains("customer_total_count")) {
            comparator = Comparator.comparing(StatsOperationIndicatorsVO::getCustomerTotalCount, Comparator.nullsLast(Integer::compareTo));
        } else if (orderBy.contains("customer_conversion_count")) {
            comparator = Comparator.comparing(StatsOperationIndicatorsVO::getCustomerConversionCount, Comparator.nullsLast(Integer::compareTo));
        } else if (orderBy.contains("customer_consumption_points")) {
            comparator = Comparator.comparing(StatsOperationIndicatorsVO::getCustomerConsumptionPoints, Comparator.nullsLast(Integer::compareTo));
        } else if (orderBy.contains("delivery_clothing_count")) {
            comparator = Comparator.comparing(StatsOperationIndicatorsVO::getDeliveryClothingCount, Comparator.nullsLast(Integer::compareTo));
        } else if (orderBy.contains("customer_protection_metrics")) {
            comparator = Comparator.comparing(StatsOperationIndicatorsVO::getCustomerProtectionMetrics, Comparator.nullsLast(Integer::compareTo));
        }

        if (comparator != null) {
            if (!ascending) {
                comparator = comparator.reversed();
            }
            aggregatedList.sort(comparator);
        }
    }

}

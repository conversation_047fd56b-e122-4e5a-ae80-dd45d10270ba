package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.ShootingStyleDAO;
import ai.conrain.aigc.platform.dal.entity.ShootingStyleDO;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.ShootingStyleService;
import ai.conrain.aigc.platform.service.model.common.KeyValue;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ShootingStyleConverter;
import ai.conrain.aigc.platform.service.model.vo.ShootingStyleVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.prompt.StyleTypePromptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * ShootingStyleService实现
 *
 * <AUTHOR>
 * @version ShootingStyleService.java v 0.1 2025-07-04 05:16:37
 */
@Slf4j
@Service
public class ShootingStyleServiceImpl implements ShootingStyleService {

    /**
     * DAO
     */
    @Autowired
    private ShootingStyleDAO shootingStyleDAO;

    /**
     * 缓存服务
     */
    @Autowired
    private TairService tairService;

    /**
     * 风格提示词工具类
     */
    private final StyleTypePromptUtils styleTypePromptUtils = new StyleTypePromptUtils();

    /** 缓存key */
    private static final String STYLE_PROMPT_CACHE_KEY = "_style_type_prompt_cache";
    /** 缓存过期时间：24小时 */
    private static final int CACHE_EXPIRE_TIME = 24 * 60 * 60;

    @Override
    public ShootingStyleVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ShootingStyleDO data = shootingStyleDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ShootingStyleConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = shootingStyleDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ShootingStyle失败");
        
        // 删除成功后刷新缓存
        refreshStylePromptCache();
    }

    @Override
    public ShootingStyleVO insert(ShootingStyleVO shootingStyle) {
        AssertUtil.assertNotNull(shootingStyle, ResultCode.PARAM_INVALID, "shootingStyle is null");
        AssertUtil.assertTrue(shootingStyle.getId() == null, ResultCode.PARAM_INVALID, "shootingStyle.id is present");

        //创建时间、修改时间兜底
        if (shootingStyle.getCreateTime() == null) {
            shootingStyle.setCreateTime(new Date());
        }

        if (shootingStyle.getModifyTime() == null) {
            shootingStyle.setModifyTime(new Date());
        }

        if (shootingStyle.getCreateBy() == null) {
            shootingStyle.setCreateBy(OperationContextHolder.getOperatorUserId());
        }
        if (shootingStyle.getModifyBy() == null) {
            shootingStyle.setModifyBy(OperationContextHolder.getOperatorUserId());
        }

        ShootingStyleDO data = ShootingStyleConverter.vo2DO(shootingStyle);
        Integer n = shootingStyleDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ShootingStyle失败");
        AssertUtil.assertNotNull(data.getId(), "新建ShootingStyle返回id为空");
        
        // 设置返回的ID
        shootingStyle.setId(data.getId());
        
        // 新增成功后刷新缓存
        refreshStylePromptCache();
  
        return shootingStyle;
    }

    @Override
    public void updateById(ShootingStyleVO shootingStyle) {
        AssertUtil.assertNotNull(shootingStyle, ResultCode.PARAM_INVALID, "shootingStyle is null");
        AssertUtil.assertTrue(shootingStyle.getId() != null, ResultCode.PARAM_INVALID, "shootingStyle.id is null");
        //修改时间必须更新
        shootingStyle.setModifyTime(new Date());
        if (shootingStyle.getModifyBy() == null) {
            shootingStyle.setModifyBy(OperationContextHolder.getOperatorUserId());
        }
        ShootingStyleDO data = ShootingStyleConverter.vo2DO(shootingStyle);
        int n = shootingStyleDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ShootingStyle失败，影响行数:" + n);
        
        // 更新成功后刷新缓存
        refreshStylePromptCache();
    }

    @Override
    public List<ShootingStyleVO> findAll() {
        List<ShootingStyleDO> list = shootingStyleDAO.selectAll();
        return ShootingStyleConverter.doList2VOList(list);
    }

    @Override
    public Map<String, List<KeyValue>> getBuildPromptData() {
        // 查询所有的场景标签
        List<ShootingStyleVO> shootingStyleVOList = findAll();

        // 按照type1Name进行分组
        Map<String, List<KeyValue>> resultMap = new LinkedHashMap<>();
        
        for (ShootingStyleVO shootingStyleVO : shootingStyleVOList) {
            String type1Name = shootingStyleVO.getType1Name();
            String type2Name = shootingStyleVO.getType2Name();
            String styleDesc = shootingStyleVO.getStyleDesc();
            
            // 跳过空值数据
            if (StringUtils.isBlank(type1Name) || StringUtils.isBlank(type2Name)) {
                continue;
            }
            
            // 获取或创建对应一级分类的列表
            List<KeyValue> keyValueList = resultMap.computeIfAbsent(type1Name, k -> new ArrayList<>());
            
            // 创建KeyValue对象并添加到列表中
            KeyValue keyValue = new KeyValue(type2Name, StringUtils.isBlank(styleDesc) ? "" : styleDesc);
            keyValueList.add(keyValue);
        }
        
        return resultMap;
    }

    /**
     * 项目启动后初始化风格提示词缓存
     */
    @PostConstruct
    public void initStylePromptCache() {
        try {
            long startTime = System.currentTimeMillis();
            log.info("开始初始化风格分类提示词缓存...");
            
            // 获取数据并构建prompt
            Map<String, List<KeyValue>> styleData = getBuildPromptData();
            String prompt = styleTypePromptUtils.buildPrompt(styleData);
            
            if (StringUtils.isNotBlank(prompt)) {
                // 将prompt缓存到Redis中
                boolean success = tairService.setString(STYLE_PROMPT_CACHE_KEY, prompt, CACHE_EXPIRE_TIME);
                
                if (success) {
                    log.info("风格分类提示词缓存初始化成功，数据量: {} 个一级分类，耗时: {}ms", 
                            styleData.size(), System.currentTimeMillis() - startTime);
                } else {
                    log.error("风格分类提示词缓存保存失败");
                }
            } else {
                log.warn("风格分类提示词为空，跳过缓存");
            }
        } catch (Exception e) {
            log.error("初始化风格分类提示词缓存失败", e);
        }
    }

    /**
     * 获取缓存的风格分类提示词
     * 
     * @return 风格分类提示词
     */
    public String getCachedStylePrompt() {
        return tairService.getString(STYLE_PROMPT_CACHE_KEY);
    }

    /**
     * 刷新风格分类提示词缓存
     */
    public void refreshStylePromptCache() {
        log.info("自动刷新风格分类提示词缓存");
        initStylePromptCache();
    }
}
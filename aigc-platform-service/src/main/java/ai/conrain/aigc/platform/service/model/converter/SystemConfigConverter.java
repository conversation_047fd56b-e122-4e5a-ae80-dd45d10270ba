package ai.conrain.aigc.platform.service.model.converter;

import java.util.List;

import ai.conrain.aigc.platform.dal.entity.SystemConfigDO;
import ai.conrain.aigc.platform.service.enums.ConfigStatusEnum;
import ai.conrain.aigc.platform.service.model.query.SystemConfigQuery;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;

/**
 * SystemConfigConverter
 *
 * @version SystemConfigService.java v 0.1 2024-01-20 01:21:37
 */
public class SystemConfigConverter {

    /**
     * DO -> VO
     */
    public static SystemConfigVO do2VO(SystemConfigDO from) {
        SystemConfigVO to = new SystemConfigVO();
        to.setId(from.getId());
        to.setConfKey(from.getConfKey());
        to.setConfValue(from.getConfValue());
        to.setConfValueNext(from.getConfValueNext());
        to.setStatus(ConfigStatusEnum.getByCode(from.getStatus()));
        to.setEffectTime(from.getEffectTime());
        to.setMemo(from.getMemo());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static SystemConfigDO vo2DO(SystemConfigVO from) {
        SystemConfigDO to = new SystemConfigDO();
        to.setId(from.getId());
        to.setConfKey(from.getConfKey());
        to.setConfValue(from.getConfValue());
        to.setConfValueNext(from.getConfValueNext());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setEffectTime(from.getEffectTime());
        to.setMemo(from.getMemo());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static SystemConfigQuery do2Query(SystemConfigDO from) {
        SystemConfigQuery to = new SystemConfigQuery();
        to.setId(from.getId());
        to.setConfKey(from.getConfKey());
        to.setConfValue(from.getConfValue());
        to.setConfValueNext(from.getConfValueNext());
        to.setStatus(from.getStatus());
        to.setEffectTime(from.getEffectTime());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static SystemConfigDO query2DO(SystemConfigQuery from) {
        SystemConfigDO to = new SystemConfigDO();
        to.setId(from.getId());
        to.setConfKey(from.getConfKey());
        to.setConfValue(from.getConfValue());
        to.setConfValueNext(from.getConfValueNext());
        to.setStatus(from.getStatus());
        to.setEffectTime(from.getEffectTime());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<SystemConfigVO> doList2VOList(List<SystemConfigDO> list) {
        return CommonUtil.listConverter(list, SystemConfigConverter::do2VO);
    }
}
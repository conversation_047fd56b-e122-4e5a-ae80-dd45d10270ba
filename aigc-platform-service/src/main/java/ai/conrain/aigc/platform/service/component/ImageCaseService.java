package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.ImageCaseDO;
import ai.conrain.aigc.platform.service.enums.DictTypeEnum;
import ai.conrain.aigc.platform.service.enums.ImageCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.BadCaseTag;
import ai.conrain.aigc.platform.service.model.query.ImageCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 图片案例 Service定义
 *
 * <AUTHOR>
 * @version ImageCaseService.java v 0.1 2024-12-09 06:41:17
 */
public interface ImageCaseService {

    /**
     * 查询图片案例对象
     *
     * @param id 主键
     * @return 返回结果
     */
    ImageCaseVO selectById(Integer id);

    /**
     * 查询图片案例对象
     *
     * @param url 图片地址
     * @return 返回结果
     */
    ImageCaseVO selectByImage(String url);

    /**
     * 删除图片案例对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加图片案例对象
     *
     * @param imageCase 对象参数
     * @return 返回结果
     */
    ImageCaseVO insert(ImageCaseVO imageCase);

    /**
     * 修改图片案例对象
     *
     * @param imageCase 对象参数
     */
    void updateByIdSelective(ImageCaseVO imageCase);

    /**
     * 带条件批量查询图片案例列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<ImageCaseVO> queryImageCaseList(ImageCaseQuery query);

    /**
     * 带条件查询图片案例数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryImageCaseCount(ImageCaseQuery query);

    /**
     * 带条件分页查询图片案例
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<ImageCaseVO> queryImageCaseByPage(ImageCaseQuery query);

    /**
     * 查询图片不良标签
     *
     * @param imageUrl 图片地址
     * @return 范围标签
     */
    List<BadCaseTag> queryBadCaseInfo(String imageUrl);

    /**
     * 添加or删除标签
     *
     * @param imageUrl 图片地址
     * @param taskId   任务id
     * @param tagId    标签id
     * @param isAdd    是否添加
     * @param id       主键 id 【非必填】
     * @param type     操作类型（具体与哪张表做关联处理）【非必填】
     */
    void addBadCaseTag(String imageUrl, Integer taskId, Integer tagId, Boolean isAdd, Integer id, String type);

    /**
     * 带条件批量查询图片案例列表
     *
     * @param query             查询条件
     * @param dictTypeEnum      类型
     * @param imageCaseTypeEnum 类型
     * @param isPartUpload      是否分片上传
     *                          return 结果
     */
    List<ImageCaseVO> queryImageCaseListByType(ImageCaseQuery query, DictTypeEnum dictTypeEnum,
                                               ImageCaseTypeEnum imageCaseTypeEnum, Boolean isPartUpload);

}
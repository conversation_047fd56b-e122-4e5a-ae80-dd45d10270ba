package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupCaptionUserConverter;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;
import com.alibaba.fastjson2.TypeReference;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO;
import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionExample;
import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionUserExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionVO;
import ai.conrain.aigc.platform.service.model.converter.ImageGroupCaptionConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageGroupCaptionUserDAO;
import ai.conrain.aigc.platform.service.component.ImageGroupCaptionService;
import com.alibaba.fastjson2.JSON;

/**   
 * ImageGroupCaptionService实现
 *
 * <AUTHOR>
 * @version ImageGroupCaptionService.java v 0.1 2025-08-14 11:09:51
 */
@Slf4j
@Service
public class ImageGroupCaptionServiceImpl implements ImageGroupCaptionService {

	/** DAO */
	@Autowired
	private ImageGroupCaptionDAO imageGroupCaptionDAO;

	@Autowired
	private ImageGroupCaptionUserDAO imageGroupCaptionUserDAO;

    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;

	@Override
	public ImageGroupCaptionVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		ImageGroupCaptionDO data = imageGroupCaptionDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return ImageGroupCaptionConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = imageGroupCaptionDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageGroupCaption失败");
	}

	@Override
	public ImageGroupCaptionVO insert(ImageGroupCaptionVO imageGroupCaption) {
		AssertUtil.assertNotNull(imageGroupCaption, ResultCode.PARAM_INVALID, "imageGroupCaption is null");
		AssertUtil.assertTrue(imageGroupCaption.getId() == null, ResultCode.PARAM_INVALID, "imageGroupCaption.id is present");

		//创建时间、修改时间兜底
		if (imageGroupCaption.getCreateTime() == null) {
			imageGroupCaption.setCreateTime(new Date());
		}

		if (imageGroupCaption.getModifyTime() == null) {
			imageGroupCaption.setModifyTime(new Date());
		}

		ImageGroupCaptionDO data = ImageGroupCaptionConverter.vo2DO(imageGroupCaption);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = imageGroupCaptionDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageGroupCaption失败");
		AssertUtil.assertNotNull(data.getId(), "新建ImageGroupCaption返回id为空");
		imageGroupCaption.setId(data.getId());
		return imageGroupCaption;
	}


	@Override
	public void updateByIdSelective(ImageGroupCaptionVO imageGroupCaption) {
		AssertUtil.assertNotNull(imageGroupCaption, ResultCode.PARAM_INVALID, "imageGroupCaption is null");
    	AssertUtil.assertTrue(imageGroupCaption.getId() != null, ResultCode.PARAM_INVALID, "imageGroupCaption.id is null");

		//修改时间必须更新
		imageGroupCaption.setModifyTime(new Date());
		ImageGroupCaptionDO data = ImageGroupCaptionConverter.vo2DO(imageGroupCaption);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = imageGroupCaptionDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageGroupCaption失败，影响行数:" + n);
	}

	@Override
	public List<ImageGroupCaptionVO> queryImageGroupCaptionList(ImageGroupCaptionQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupCaptionExample example = ImageGroupCaptionConverter.query2Example(query);

		List<ImageGroupCaptionDO> list = imageGroupCaptionDAO.selectByExample(example);
		return ImageGroupCaptionConverter.doList2VOList(list);
	}

	@Override
	public Long queryImageGroupCaptionCount(ImageGroupCaptionQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		ImageGroupCaptionExample example = ImageGroupCaptionConverter.query2Example(query);
		return imageGroupCaptionDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询图像组标注表，pair对打标的最终数据
	 */
	@Override
	public PageInfo<ImageGroupCaptionVO> queryImageGroupCaptionByPage(ImageGroupCaptionQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<ImageGroupCaptionVO> page = new PageInfo<>();

		ImageGroupCaptionExample example = ImageGroupCaptionConverter.query2Example(query);
		long totalCount = imageGroupCaptionDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<ImageGroupCaptionDO> list = imageGroupCaptionDAO.selectByExample(example);
		page.setList(ImageGroupCaptionConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aggregateCaptionResult(List<Integer> userIds) {
        aggregateCaptionResultInternal(userIds, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void aggregateCaptionResult(List<Integer> userIds, boolean forceUpdate) {
        aggregateCaptionResultInternal(userIds, forceUpdate);
    }

    /**
     * 聚合标注结果的内部实现（支持强制更新和分页处理）
     */
    private void aggregateCaptionResultInternal(List<Integer> userIds, boolean forceUpdate) {
        log.info("开始聚合标注结果，用户ID列表: {}, 强制更新: {}", userIds, forceUpdate);

        if (CollectionUtils.isEmpty(userIds) || userIds.size() != 3) {
            log.warn("用户ID列表为空或数量不等于3，跳过处理");
            return;
        }

        // 先查询总数
        Long totalCount = imageGroupCaptionDAO.countImageGroupIdsForUpdate(userIds, userIds.size(), forceUpdate);

        if (totalCount == 0) {
            log.info("没有找到需要处理的图片组");
            return;
        }

        log.info("找到 {} 个需要处理的图片组，开始分页处理", totalCount);

        // 分页处理，避免一次性加载过多数据
        int batchSize = 100; // 每批处理100个
        int processedCount = 0;
        int offset = 0;

        while (offset < totalCount) {
            List<Integer> batchImageGroupIds = imageGroupCaptionDAO.selectImageGroupIdsForUpdate(
                userIds, userIds.size(), forceUpdate, batchSize, offset);

            if (CollectionUtils.isEmpty(batchImageGroupIds)) {
                break;
            }

            log.info("处理第 {} 批，包含 {} 个图片组", (offset / batchSize) + 1, batchImageGroupIds.size());

            for (Integer imageGroupId : batchImageGroupIds) {
                try {
                    processImageGroupCaption(imageGroupId, userIds);
                    processedCount++;
                } catch (Exception e) {
                    log.error("处理图片组 {} 时发生错误", imageGroupId, e);
                }
            }

            offset += batchSize;

            // 避免长时间占用数据库连接，每批处理后短暂休息
            if (offset < totalCount) {
                try {
                    Thread.sleep(10); // 休息10毫秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("成功处理了 {} 个图片组的标注结果", processedCount);
    }



    /**
     * 处理单个图片组的标注聚合
     */
    private void processImageGroupCaption(Integer imageGroupId, List<Integer> userIds) {
        log.info("开始处理图片组 {} 的标注聚合", imageGroupId);

        // 查询该图片组的所有用户标注
        ImageGroupCaptionUserExample userExample = new ImageGroupCaptionUserExample();
        userExample.createCriteria()
                .andImageGroupIdEqualTo(imageGroupId)
                .andUserIdIn(userIds)
                .andDeletedEqualTo(false);

        List<ImageGroupCaptionUserVO> userCaptions = imageGroupCaptionUserDAO.selectByExample(userExample).stream().map(
            ImageGroupCaptionUserConverter::do2VO).collect(Collectors.toList());

        if (userCaptions.size() != 3) {
            log.warn("图片组 {} 的用户标注数量不等于3，实际数量: {}", imageGroupId, userCaptions.size());
            return;
        }

        // 解析用户标注数据
        List<Map<String, String>> captionDataList = userCaptions.stream()
            .map(ImageGroupCaptionUserVO::getCaption)
            .map(caption -> JSON.parseObject(caption.toJSONString(), new TypeReference<Map<String, String>>() {}))
            .collect(Collectors.toList());

        // 聚合标注结果
        AggregationResult aggregationResult = aggregateCaptions(captionDataList);

        // 创建或更新最终标注结果
        ImageGroupCaptionDO finalCaption = createOrUpdateFinalCaption(
                imageGroupId, aggregationResult, userCaptions);

        log.info("成功处理图片组 {} 的标注聚合，结果状态: {}", imageGroupId, aggregationResult.getResultStatus());
    }

    /**
     * 聚合多个用户的标注数据
     */
    private AggregationResult aggregateCaptions(List<Map<String, String>> captionDataList) {
        if (captionDataList.size() != 3) {
            throw new IllegalArgumentException("标注数据数量必须为3");
        }

        AggregationResult result = new AggregationResult();
        Map<String, String> finalCaption = new HashMap<>();
        Set<String> resultStatus = new HashSet<>();

        // 获取所有可能的键
        Set<String> allKeys = new HashSet<>();
        for (Map<String, String> captionData : captionDataList) {
            if (captionData != null) {
                allKeys.addAll(captionData.keySet());
            }
        }

        // 检查是否有低质量标记
        boolean hasLowQuality = captionDataList.stream()
                .anyMatch(caption -> "yes".equals(caption.get("low_quality")));

        if (hasLowQuality) {
            resultStatus.add("low_quality");
            result.setResultStatus(resultStatus);
            result.setFinalCaption(finalCaption);
            return result;
        }

        // 处理每个属性
        for (String key : allKeys) {
            List<String> values = captionDataList.stream()
                    .map(caption -> StringUtils.stripToEmpty(caption.get(key)))
                    .collect(Collectors.toList());

            if ("total_score".equals(key)) {
                processTotalScore(key, values, finalCaption, resultStatus);
            } else {
                processRegularAttribute(key, values, finalCaption, resultStatus);
            }
        }

        result.setResultStatus(resultStatus);
        result.setFinalCaption(finalCaption);
        return result;
    }

    /**
     * 处理total_score属性
     */
    private void processTotalScore(String key, List<String> values, Map<String, String> finalCaption, Set<String> resultStatus) {
        // 检查是否所有值都相同
        boolean allSame = values.stream().allMatch(val -> val.equals(values.getFirst()));

        // 检查至少两个相同
        Set<String> uniqueValues = new HashSet<>(values);

        if (allSame) {
            resultStatus.add("total_score_high_same");
            finalCaption.put(key, values.getFirst());
        } else {
            resultStatus.add("total_score_high_diff");
            resultStatus.add("total_score_least_one_diff");
            finalCaption.put(key, "");
        }

        // 至少两个相同
        if (uniqueValues.size() < values.size()) {
            resultStatus.add("total_score_least_two_same");

            // 找到出现至少两次的值
            String sameValue = values.stream()
                .filter(val -> values.stream().filter(v -> v.equals(val)).count() >= 2)
                .findFirst()
                .orElse("");
            finalCaption.put(key, sameValue);
        }

        // 检查只有两个相同
        if (uniqueValues.size() == 2) {
            resultStatus.add("total_score_only_two_same");
        }
    }

    /**
     * 处理普通属性
     */
    private void processRegularAttribute(String key, List<String> values, Map<String, String> finalCaption, Set<String> resultStatus) {
        // 检查至少两个相同
        Set<String> uniqueValues = new HashSet<>(values);
        boolean atLeastTwoSame = uniqueValues.size() < values.size();

        if (atLeastTwoSame) {
            // 找到出现至少两次的值
            String finalValue = values.stream()
                    .filter(val -> values.stream().filter(v -> v != null && v.equals(val)).count() >= 2)
                    .findFirst()
                    .orElse("");
            finalCaption.put(key, finalValue);
        } else {
            // 所有值都不同
            resultStatus.add("dimension_diff");
            // 记录具体是哪个维度不同
            resultStatus.add(key + "_high_diff");
            finalCaption.put(key, "");
        }
    }

    /**
     * 创建或更新最终标注结果
     */
    private ImageGroupCaptionDO createOrUpdateFinalCaption(Integer imageGroupId,
                                                           AggregationResult aggregationResult,
                                                           List<ImageGroupCaptionUserVO> captionLogList) {
        Date now = new Date();
        // 查询是否已存在最终结果
        ImageGroupCaptionExample example = new ImageGroupCaptionExample();
        example.createCriteria()
                .andImageGroupIdEqualTo(imageGroupId)
                .andDeletedEqualTo(false);

        List<ImageGroupCaptionDO> existingCaptions = imageGroupCaptionDAO.selectByExample(example);

        ImageGroupCaptionDO finalCaption;
        if (CollectionUtils.isNotEmpty(existingCaptions)) {
            // 更新现有记录
            finalCaption = existingCaptions.getFirst();
            finalCaption.setResult(JSON.toJSONString(aggregationResult.getResultStatus()));
            finalCaption.setCaption(JSON.toJSONString(aggregationResult.getFinalCaption()));
            finalCaption.setCaptionLog(JSON.toJSONString(captionLogList));
            finalCaption.setModifyTime(now);

            imageGroupCaptionDAO.updateByPrimaryKeySelective(finalCaption);
        } else {
            // 创建新记录
            finalCaption = new ImageGroupCaptionDO();
            finalCaption.setImageGroupId(imageGroupId);
            finalCaption.setResult(JSON.toJSONString(aggregationResult.getResultStatus()));
            finalCaption.setCaption(JSON.toJSONString(aggregationResult.getFinalCaption()));
            finalCaption.setCaptionLog(JSON.toJSONString(captionLogList));
            finalCaption.setCreateTime(now);
            finalCaption.setModifyTime(now);
            finalCaption.setDeleted(false);

            imageGroupCaptionDAO.insertSelective(finalCaption);
        }

        return finalCaption;
    }

    /**
     * 聚合结果类
     */
    @Setter
    @Getter
    private static class AggregationResult {
        private Set<String> resultStatus = new HashSet<>();
        private Map<String, String> finalCaption = new HashMap<>();
    }

    @Override
    public void processSpecificImageGroupCaption(Integer imageGroupId, List<Integer> userIds) {
        log.info("开始处理指定图片组 {} 的标注聚合", imageGroupId);
        processImageGroupCaption(imageGroupId, userIds);
    }

    @Override
    public Long countImageGroupsForUpdate(List<Integer> userIds, boolean forceUpdate) {
        if (CollectionUtils.isEmpty(userIds) || userIds.size() != 3) {
            log.warn("用户ID列表为空或数量不等于3，返回0");
            return 0L;
        }

        return imageGroupCaptionDAO.countImageGroupIdsForUpdate(userIds, userIds.size(), forceUpdate);
    }

}
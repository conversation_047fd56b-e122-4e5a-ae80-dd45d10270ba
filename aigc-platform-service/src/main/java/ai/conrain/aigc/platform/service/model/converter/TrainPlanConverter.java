package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TrainPlanDO;
import ai.conrain.aigc.platform.service.model.query.TrainPlanQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainPlanVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.common.utils.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * TrainPlanConverter
 *
 * @version TrainPlanService.java v 0.1 2024-11-19 08:34:41
 */
public class TrainPlanConverter {

    /**
     * DO -> VO
     */
    public static TrainPlanVO do2VO(TrainPlanDO from) {
        TrainPlanVO to = new TrainPlanVO();
        to.setId(from.getId());
        to.setPlanName(from.getPlanName());
        to.setClothingId(from.getClothingId());
        to.setClothingName(from.getClothingName());
        to.setRemarks(from.getRemarks());
        to.setImagesPerCombination(from.getImagesPerCombination());
        if (StringUtils.isNotBlank(from.getFaceModels())) {
            to.setFaceModels(JSONArray.parseArray(from.getFaceModels(), Integer.class));
        }
        if (StringUtils.isNotBlank(from.getScenes())) {
            to.setScenes(JSONArray.parseArray(from.getScenes(), Integer.class));
        }
        if (StringUtils.isNotBlank(from.getSizes())) {
            to.setSizes(JSONArray.parseArray(from.getSizes(), String.class));
        }
        to.setExtInfo(from.getExtInfo());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setCreatorUserName(from.getCreatorUserName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TrainPlanDO vo2DO(TrainPlanVO from) {
        TrainPlanDO to = new TrainPlanDO();
        to.setId(from.getId());
        to.setPlanName(from.getPlanName());
        to.setClothingId(from.getClothingId());
        to.setClothingName(from.getClothingName());
        to.setRemarks(from.getRemarks());
        to.setImagesPerCombination(from.getImagesPerCombination());
        if (!ObjectUtils.isEmpty(from.getFaceModels())) {
            to.setFaceModels(JSONArray.toJSONString(from.getFaceModels()));
        }
        if (CollectionUtils.isNotEmpty(from.getSizes())) {
            to.setSizes(JSONArray.toJSONString(from.getSizes()));
        }
        if (CollectionUtils.isNotEmpty(from.getScenes())) {
            to.setScenes(JSONArray.toJSONString(from.getScenes()));
        }
        to.setExtInfo(from.getExtInfo());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setCreatorUserName(from.getCreatorUserName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static TrainPlanQuery do2Query(TrainPlanDO from) {
        TrainPlanQuery to = new TrainPlanQuery();
        to.setId(from.getId());
        to.setPlanName(from.getPlanName());
        to.setClothingId(from.getClothingId());
        to.setClothingName(from.getClothingName());
        to.setRemarks(from.getRemarks());
        to.setImagesPerCombination(from.getImagesPerCombination());
        to.setFaceModels(from.getFaceModels());
        to.setScenes(from.getScenes());
        to.setSizes(from.getSizes());
        to.setExtInfo(from.getExtInfo());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setCreatorUserName(from.getCreatorUserName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TrainPlanDO query2DO(TrainPlanQuery from) {
        TrainPlanDO to = new TrainPlanDO();
        to.setId(from.getId());
        to.setPlanName(from.getPlanName());
        to.setClothingId(from.getClothingId());
        to.setClothingName(from.getClothingName());
        to.setRemarks(from.getRemarks());
        to.setImagesPerCombination(from.getImagesPerCombination());
        to.setFaceModels(from.getFaceModels());
        to.setScenes(from.getScenes());
        to.setSizes(from.getSizes());
        to.setExtInfo(from.getExtInfo());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setCreatorUserName(from.getCreatorUserName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * do list -> vo list
     */
    public static List<TrainPlanVO> doList2VOList(List<TrainPlanDO> list) {
        return CommonUtil.listConverter(list, TrainPlanConverter::do2VO);
    }
}
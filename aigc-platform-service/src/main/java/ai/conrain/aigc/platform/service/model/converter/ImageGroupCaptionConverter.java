package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.pgsql.entity.ImageGroupCaptionDO;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionQuery;
import ai.conrain.aigc.platform.dal.example.ImageGroupCaptionExample;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionVO;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ImageGroupCaptionConverter
 *
 * @version ImageGroupCaptionService.java v 0.1 2025-08-14 11:09:51
 */
public class ImageGroupCaptionConverter {

    /**
     * DO -> VO
     */
    public static ImageGroupCaptionVO do2VO(ImageGroupCaptionDO from) {
        ImageGroupCaptionVO to = new ImageGroupCaptionVO();
        to.setId(from.getId());
        to.setImageGroupId(from.getImageGroupId());
        to.setImageCaptionIds(from.getImageCaptionIds());
        to.setResult(JSONArray.parseArray(from.getResult()));
        to.setCaption(JSONObject.parseObject(from.getCaption()));
        to.setCaptionLog(JSONArray.parseArray(from.getCaptionLog()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageGroupCaptionDO vo2DO(ImageGroupCaptionVO from) {
        ImageGroupCaptionDO to = new ImageGroupCaptionDO();
        to.setId(from.getId());
        to.setImageGroupId(from.getImageGroupId());
        to.setImageCaptionIds(from.getImageCaptionIds());
        to.setResult(JSONArray.toJSONString(from.getResult()));
        to.setCaption(JSONObject.toJSONString(from.getCaption()));
        to.setCaptionLog(JSONArray.toJSONString(from.getCaptionLog()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageGroupCaptionExample query2Example(ImageGroupCaptionQuery from) {
        ImageGroupCaptionExample to = new ImageGroupCaptionExample();
        ImageGroupCaptionExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getImageGroupId())) {
            c.andImageGroupIdEqualTo(from.getImageGroupId());
        }
        if (!ObjectUtils.isEmpty(from.getImageCaptionIds())) {
            c.andImageCaptionIdsEqualTo(from.getImageCaptionIds());
        }
        if (!ObjectUtils.isEmpty(from.getResult())) {
            c.andResultEqualTo(from.getResult());
        }
        if (!ObjectUtils.isEmpty(from.getCaption())) {
            c.andCaptionEqualTo(from.getCaption());
        }
        if (!ObjectUtils.isEmpty(from.getCaptionLog())) {
            c.andCaptionLogEqualTo(from.getCaptionLog());
        }
        if (!ObjectUtils.isEmpty(from.getCaptionVersion())) {
            c.andCaptionVersionEqualTo(from.getCaptionVersion());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (ImageGroupCaptionExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageGroupCaptionVO> doList2VOList(List<ImageGroupCaptionDO> list) {
        return CommonUtil.listConverter(list, ImageGroupCaptionConverter::do2VO);
    }
}
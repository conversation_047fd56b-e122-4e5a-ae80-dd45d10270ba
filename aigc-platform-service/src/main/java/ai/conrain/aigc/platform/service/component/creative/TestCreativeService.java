/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.component.TestCaseItemService;
import ai.conrain.aigc.platform.service.component.TestCaseService;
import ai.conrain.aigc.platform.service.constants.BizConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.abtest.BaseCaseItemDetail;
import ai.conrain.aigc.platform.service.model.biz.abtest.PromptCreativeItemDetail;
import ai.conrain.aigc.platform.service.model.biz.abtest.TestCreativeRequest;
import ai.conrain.aigc.platform.service.model.request.CreateTestParams;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.TestCaseItemVO;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.ContextUtils;
import ai.conrain.aigc.platform.service.util.CreativeUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.COLON;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_BASE_MODEL_VERSION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FLOW_TYPE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_GUIDANCE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_PROMPT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SAMPLER_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SCHEDULE_NAME;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SEED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CASE_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CASE_ITEM_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_CONTEXT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_GROUP_ID;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_IMAGE_CNT;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_PARAMS;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TEST_ROUND_ID;
import static ai.conrain.aigc.platform.service.util.CommonUtil.mockSpecialTestContext;

/**
 * 测试创作服务实现
 *
 * <AUTHOR>
 * @version : TestCreativeService.java, v 0.1 2025/8/13 19:21 renxiao.wu Exp $
 */
@Slf4j
@Service
public class TestCreativeService extends AbstractCreativeService<TestCreativeRequest> {
    private final static List<String> COPY_TO_TASK_KEY = Arrays.asList(KEY_TEST_CONTEXT, KEY_TEST_CASE_ID,
        KEY_TEST_PARAMS, KEY_TEST_GROUP_ID, KEY_TEST_IMAGE_CNT, KEY_FLOW_TYPE);
    @Autowired
    private TestCaseService testCaseService;
    @Autowired
    private TestCaseItemService testCaseItemService;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.TEST_CREATIVE_FLOW;
    }

    @Override
    protected CreativeBatchVO buildData(TestCreativeRequest request, MaterialModelVO modelVO) throws IOException {
        CreativeBatchVO batch = new CreativeBatchVO();

        Integer mockUserId = mockSpecialTestContext().getCurrentUserId();
        batch.setOperatorId(mockUserId);
        batch.setUserId(mockUserId);
        batch.setStatus(CreativeStatusEnum.QUEUE);
        batch.setType(getCreativeType());
        if (StringUtils.isNotBlank(request.getBizTag())) {
            batch.addExtInfo(BizConstants.BIZ_TAG, request.getBizTag());
        }

        int batchCnt;
        if (request.getTestCaseId() != null) {
            TestCaseVO testCase = testCaseService.selectById(request.getTestCaseId());
            batchCnt = testCase.getCaseNum() * request.getImageNum();
            batch.addExtInfo(KEY_TEST_IMAGE_CNT, request.getImageNum());
        } else {
            batchCnt = request.getImageNum();
        }
        batch.setBatchCnt(batchCnt);

        JSONObject context = request.getTestContext();
        if (context != null) {
            String baseModelVersion = context.getString(KEY_BASE_MODEL_VERSION);
            if (StringUtils.contains(baseModelVersion, COLON)) {
                String[] split = baseModelVersion.split(COLON);
                context.put(KEY_BASE_MODEL_VERSION, split[0]);

                String guidance = context.getString(KEY_GUIDANCE);
                if (StringUtils.isBlank(guidance)) {
                    context.put(KEY_GUIDANCE, split[1]);
                }
            }
        }
        batch.addExtInfo(KEY_TEST_CONTEXT, context);
        batch.addExtInfo(KEY_TEST_CASE_ID, request.getTestCaseId());
        batch.addExtInfo(KEY_TEST_PARAMS, request.getTestParams());
        batch.addExtInfo(KEY_TEST_GROUP_ID, request.getTestGroupId());
        batch.addExtInfo(KEY_FLOW_TYPE, request.getFlowType());

        batch.setImageProportion(ProportionTypeEnum.P_1152_1536.getCode());
        return batch;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {

        // 非用例测试，直接使用原有的处理流程
        Integer testCaseId = batch.getExtInfo(KEY_TEST_CASE_ID, Integer.class);
        if (testCaseId == null) {
            return super.buildTasks(batch, elements);
        }

        List<CreativeTaskVO> tasks = new ArrayList<>();

        TestCaseVO testCase = testCaseService.selectById(testCaseId);
        List<TestCaseItemVO> items = testCaseItemService.queryByCaseId(testCase.getId());

        Integer testNum = batch.getExtInfo(KEY_TEST_IMAGE_CNT, Integer.class);
        testNum = testNum == null ? 1 : testNum;

        for (int i = 0; i < items.size(); i++) {
            TestCaseItemVO item = items.get(i);

            for (int j = 0; j < testNum; j++) {
                int idx = i * testNum + j;
                tasks.add(buildTaskFromCaseItem(item, batch, elements, idx));
            }
        }

        creativeTaskService.batchInsert(tasks);

        return tasks;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        JSONObject extInfo = batch.getExtInfo();
        for (String key : COPY_TO_TASK_KEY) {
            Object value = extInfo.get(key);
            if (value != null) {
                target.addExtInfo(key, value);
            }
        }

        @SuppressWarnings("unchecked")
        List<Object> testParams = batch.getExtValue(KEY_TEST_PARAMS, List.class);
        if (CollectionUtils.isNotEmpty(testParams)) {
            CreateTestParams params = null;
            Object jsonObject = testParams.get(idx);
            if (jsonObject instanceof CreateTestParams) {
                params = (CreateTestParams)jsonObject;
            } else {
                params = ((JSONObject)jsonObject).toJavaObject(CreateTestParams.class);
            }

            target.addExtInfo(KEY_TEST_PARAMS, params);
            target.addExtInfo(KEY_TEST_ROUND_ID, idx);
        }
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        return task.getExtInfo(KEY_FLOW_TYPE, String.class);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        super.preParse(task, elements, modelVO, context);

        JSONObject testContext = task.getExtValue(KEY_TEST_CONTEXT, JSONObject.class);
        if (testContext != null) {
            log.info("识别到测试场景,需变更上下文,id={},testContext={}", task.getBatchId(), testContext);
            ContextUtils.updateContext(context, testContext);
        }

        long promptSeed;
        CreateTestParams testParams = task.getExtValue(KEY_TEST_PARAMS, CreateTestParams.class);
        if (testParams != null) {
            context.put(KEY_SEED, testParams.getSeed());
            promptSeed = Long.parseLong(testParams.getPromptSeed());
        } else {
            promptSeed = Long.parseLong(RandomStringUtils.randomNumeric(15));//1-2048的随机数字,无法发挥随机数seed，调整成15位
        }
        context.put("promptSeed", promptSeed);

        String samplerName = (String)context.get(KEY_SAMPLER_NAME);
        String scheduleName = null;

        if (StringUtils.contains(samplerName, "|")) {
            String[] split = StringUtils.split(samplerName, "|");
            AssertUtil.assertTrue(split.length == 2, "samplerName|scheduleName格式错误");
            samplerName = split[0];
            scheduleName = split[1];
        }

        context.put(KEY_SAMPLER_NAME, samplerName);
        context.put(KEY_SCHEDULE_NAME, scheduleName);
        String prompt = task.getExtValue(KEY_PROMPT, String.class);
        prompt = ComfyUIUtils.removeConflictingKey(prompt);
        prompt = ComfyUIUtils.parseParams(prompt);
        prompt = prompt.replaceAll("\r\n|\r|\n", "");
        context.put(KEY_PROMPT, prompt);
    }

    /**
     * 构建用例任务
     *
     * @param item     测试用例项
     * @param batch    批次
     * @param elements 元素
     * @param idx      索引
     * @return 任务
     */
    private CreativeTaskVO buildTaskFromCaseItem(TestCaseItemVO item, CreativeBatchVO batch,
                                                 List<CreativeElementVO> elements, Integer idx) {
        CreativeTaskVO task = new CreativeTaskVO();
        fillTaskExt(task, batch, elements, idx);

        task.setBatchId(batch.getId());
        task.setModelId(batch.getModelId());
        task.setType(batch.getType());

        BaseCaseItemDetail itemDetail = item.getDetail();
        if (item.getType() == TestCaseTypeEnum.PROMPT_CREATE_IMAGE) {
            PromptCreativeItemDetail detail = (PromptCreativeItemDetail)itemDetail;
            task.setImageProportion(CreativeUtils.buildProportion(detail.getWidth(), detail.getHeight()));
            task.addExtInfo(KEY_PROMPT, detail.getPrompt());
            task.addExtInfo(KEY_TEST_CASE_ITEM_ID, item.getId());
            task.addExtInfo(KEY_ORIGIN_IMAGE, detail.getImageUrl());
        } else {
            task.setImageProportion(batch.getImageProportion());
        }

        task.setBatchCnt(1);
        task.setPromptId(item.getName());
        task.setStatus(CreativeStatusEnum.INIT);
        task.setOperatorId(batch.getOperatorId());
        task.setUserId(batch.getUserId());
        task.setResultPath(batch.getResultPath());
        return task;
    }
}

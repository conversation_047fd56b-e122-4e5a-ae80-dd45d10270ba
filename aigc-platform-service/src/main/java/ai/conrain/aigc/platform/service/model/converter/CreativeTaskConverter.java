package ai.conrain.aigc.platform.service.model.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.CreativeTaskDO;
import ai.conrain.aigc.platform.dal.example.CreativeTaskExample;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ComfyuiTplInfo;
import ai.conrain.aigc.platform.service.model.query.CreativeTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_LOGO_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_REPAIR_IMAGE;

/**
 * CreativeTaskConverter
 *
 * @version CreativeTaskService.java v 0.1 2024-05-22 12:04:27
 */
public class CreativeTaskConverter {

    public static CreativeTaskVO request2VO(CreativeBatchVO batch) {
        CreativeTaskVO data = new CreativeTaskVO();
        data.setBatchId(batch.getId());
        data.setUserId(batch.getUserId());
        data.setOperatorId(batch.getOperatorId());
        data.setBatchCnt(batch.getType() == CreativeTypeEnum.REPAIR_HANDS ? 4 : 1);
        data.setModelId(batch.getModelId());
        data.setImageProportion(batch.getImageProportion());
        data.setStatus(CreativeStatusEnum.INIT);
        data.setResultPath(batch.getResultPath());
        data.addExtInfo(KEY_REPAIR_IMAGE, batch.getStringFromExtInfo(KEY_REPAIR_IMAGE));
        data.addExtInfo(KEY_LOGO_IMAGE, batch.getStringFromExtInfo(KEY_LOGO_IMAGE));
        data.setType(batch.getType());
        return data;
    }

    /**
     * DO -> VO
     */
    public static CreativeTaskVO do2VO(CreativeTaskDO from) {
        CreativeTaskVO to = new CreativeTaskVO();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setType(CreativeTypeEnum.getByCode(from.getType()));
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setStatus(CreativeStatusEnum.getByCode(from.getStatus()));
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAigcRequest(from.getAigcRequest());

        List<String> resultImages = StringUtils.isBlank(from.getResultImages()) ? null : JSONArray.parseArray(
            from.getResultImages()).toJavaList(String.class);
        to.setResultImages(resultImages);
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        to.setTplInfo(CommonUtil.parseObject(from.getTplInfo(), ComfyuiTplInfo.class));
        to.setPreTaskId(from.getPreTaskId());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CreativeTaskDO vo2DO(CreativeTaskVO from) {
        CreativeTaskDO to = new CreativeTaskDO();
        to.setId(from.getId());
        to.setBatchId(from.getBatchId());
        to.setUserId(from.getUserId());
        to.setModelId(from.getModelId());
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setImageProportion(from.getImageProportion());
        to.setBatchCnt(from.getBatchCnt());
        to.setPromptId(from.getPromptId());
        to.setResultPath(from.getResultPath());
        to.setStatus(from.getStatus() != null ? from.getStatus().getCode() : null);
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAigcRequest(from.getAigcRequest());
        to.setResultImages(from.getResultImages() != null ? JSONObject.toJSONString(from.getResultImages()) : null);
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);

        to.setTplInfo(CommonUtil.toJSONString(from.getTplInfo()));
        to.setPreTaskId(from.getPreTaskId());
        return to;
    }

    /**
     * Query -> Example
     */
    public static CreativeTaskExample query2Example(CreativeTaskQuery from) {
        CreativeTaskExample to = new CreativeTaskExample();
        CreativeTaskExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getIds())) {
            c.andIdIn(from.getIds());
        }
        if (!ObjectUtils.isEmpty(from.getBatchId())) {
            c.andBatchIdEqualTo(from.getBatchId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getModelId())) {
            c.andModelIdEqualTo(from.getModelId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getImageProportion())) {
            c.andImageProportionEqualTo(from.getImageProportion());
        }
        if (!ObjectUtils.isEmpty(from.getBatchCnt())) {
            c.andBatchCntEqualTo(from.getBatchCnt());
        }
        if (!ObjectUtils.isEmpty(from.getPromptId())) {
            c.andPromptIdEqualTo(from.getPromptId());
        }
        if (!ObjectUtils.isEmpty(from.getResultPath())) {
            c.andResultPathEqualTo(from.getResultPath());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getElementIds())) {
            c.andElementIdIn(from.getElementIds());
        }
        //逻辑删除过滤
        for (CreativeTaskExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<CreativeTaskVO> doList2VOList(List<CreativeTaskDO> list) {
        return CommonUtil.listConverter(list, CreativeTaskConverter::do2VO);
    }

    public static List<CreativeTaskDO> voList2DOList(List<CreativeTaskVO> list) {
        return CommonUtil.listConverter(list, CreativeTaskConverter::vo2DO);
    }
}
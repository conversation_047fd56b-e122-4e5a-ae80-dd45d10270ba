package ai.conrain.aigc.platform.service.model.request;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import lombok.Data;

@Data
public class PoseSampleDiagramRequest {

    /** 模特 id 集合 */
    @NotNull(message = "模特不能为空！")
    private List<Integer> faceIdList;

    /** 配置（1 模特 2 场景） */
    private Map<Integer, List<Integer>> configs;

    /** 图片数量 */
    @NotNull(message = "图片数量不能为空！")
    private Integer imgNum;

    /** 图片比例 */
    @NotBlank(message = "图片比例不能为空！")
    private String proportion;

    /** 场景 id */
    @NotNull(message = "场景 id 不能为空！")
    private Integer sceneId;

    /** 当前姿势场景 id */
    private Integer currentPoseId;


    // 设置场景 id 设置进入configs
    public void setConfigSceneId(Integer sceneId) {
        if (sceneId == null) {
            return;
        }

        if (configs == null) {
            configs = new HashMap<>();
        }

        // 场景的 key 为 2
        List<Integer> sceneIds = new ArrayList<>();
        sceneIds.add(sceneId);
        configs.put(2, sceneIds);
    }


    // 更新配置中的模特 id
    public void setConfigFaceId(Integer faceId) {
        if (sceneId == null) {
            return;
        }

        if (configs == null) {
            configs = new HashMap<>();
        }

        // 模特的 key 为  1
        List<Integer> faceIds = new ArrayList<>();
        faceIds.add(faceId);
        configs.put(1, faceIds);
    }


}

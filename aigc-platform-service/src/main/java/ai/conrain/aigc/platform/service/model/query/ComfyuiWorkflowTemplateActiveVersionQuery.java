package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ComfyuiWorkflowTemplateActiveVersionQuery
 *
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
@Data
public class ComfyuiWorkflowTemplateActiveVersionQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Integer id;

    /** 模板key */
    private String templateKey;

    /** 模板描述 */
    private String templateDesc;

    /** 模板版本，如20250610.1 */
    private String activeVersion;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;

    /** 创建人id */
    private Integer createBy;

    /** 修改人id */
    private Integer modifyBy;

    /** 测试模板版本，如20250610.1 */
    private String testVersion;

    /** 测试模板开放范围 */
    private String testOpenScope;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}

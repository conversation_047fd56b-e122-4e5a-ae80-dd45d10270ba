package ai.conrain.aigc.platform.service.component.agent;

import ai.conrain.aigc.platform.service.enums.AgeGroupEnum;
import org.apache.commons.lang3.StringUtils;

public class AgentUtil {
    public static AgeGroupEnum getAgeGroupEnum(String captionAge) {
        AgeGroupEnum ageGroupEnum = AgeGroupEnum.UNKNOWN;

        // 打标服务：0-3、3-5、5-9、10-14、15-25、26-45、46-60、60+
        if (StringUtils.isNotBlank(captionAge)) {
            switch (captionAge) {
                case "0-3":
                    ageGroupEnum = AgeGroupEnum.YOUNG;
                    break;
                case "3-5":
                case "5-9":
                    ageGroupEnum = AgeGroupEnum.CHILD;
                    break;
                case "10-14":
                    ageGroupEnum = AgeGroupEnum.TEENAGER;
                    break;
                case "15-25":
                case "26-45":
                case "46-60":
                    ageGroupEnum = AgeGroupEnum.ADULT;
                    break;
                case "60+":
                    ageGroupEnum = AgeGroupEnum.ELDERLY;
                    break;
            }
        }
        return ageGroupEnum;
    }
}

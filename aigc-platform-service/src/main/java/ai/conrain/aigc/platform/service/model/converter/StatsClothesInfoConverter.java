package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.service.model.query.StatsClothesInfoQuery;
import ai.conrain.aigc.platform.dal.example.StatsClothesInfoExample;
import ai.conrain.aigc.platform.service.model.vo.StatsClothesInfoVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * StatsClothesInfoConverter
 *
 * @version StatsClothesInfoService.java v 0.1 2025-04-22 05:07:33
 */
public class StatsClothesInfoConverter {

    /**
     * DO -> VO
     */
    public static StatsClothesInfoVO do2VO(StatsClothesInfoDO from) {
        StatsClothesInfoVO to = new StatsClothesInfoVO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setVipClothesCount(from.getVipClothesCount());
        to.setAutoTrainCount(from.getAutoTrainCount());
        to.setManualDeliveryCount(from.getManualDeliveryCount());
        to.setAutoTrainAndDeliveryCount(from.getAutoTrainAndDeliveryCount());
        to.setRetryMattingCount(from.getRetryMattingCount());
        to.setUpdatePromptCount(from.getUpdatePromptCount());
        to.setCopyCount(from.getCopyCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * VO -> DO
     */
    public static StatsClothesInfoDO vo2DO(StatsClothesInfoVO from) {
        StatsClothesInfoDO to = new StatsClothesInfoDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setVipClothesCount(from.getVipClothesCount());
        to.setAutoTrainCount(from.getAutoTrainCount());
        to.setManualDeliveryCount(from.getManualDeliveryCount());
        to.setAutoTrainAndDeliveryCount(from.getAutoTrainAndDeliveryCount());
        to.setRetryMattingCount(from.getRetryMattingCount());
        to.setUpdatePromptCount(from.getUpdatePromptCount());
        to.setCopyCount(from.getCopyCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * DO -> Query
     */
    public static StatsClothesInfoQuery do2Query(StatsClothesInfoDO from) {
        StatsClothesInfoQuery to = new StatsClothesInfoQuery();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setVipClothesCount(from.getVipClothesCount());
        to.setAutoTrainCount(from.getAutoTrainCount());
        to.setManualDeliveryCount(from.getManualDeliveryCount());
        to.setAutoTrainAndDeliveryCount(from.getAutoTrainAndDeliveryCount());
        to.setRetryMattingCount(from.getRetryMattingCount());
        to.setUpdatePromptCount(from.getUpdatePromptCount());
        to.setCopyCount(from.getCopyCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static StatsClothesInfoDO query2DO(StatsClothesInfoQuery from) {
        StatsClothesInfoDO to = new StatsClothesInfoDO();
        to.setId(from.getId());
        to.setStatsType(from.getStatsType());
        to.setStatsDate(from.getStatsDate());
        to.setVipClothesCount(from.getVipClothesCount());
        to.setAutoTrainCount(from.getAutoTrainCount());
        to.setManualDeliveryCount(from.getManualDeliveryCount());
        to.setAutoTrainAndDeliveryCount(from.getAutoTrainAndDeliveryCount());
        to.setRetryMattingCount(from.getRetryMattingCount());
        to.setUpdatePromptCount(from.getUpdatePromptCount());
        to.setCopyCount(from.getCopyCount());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }


    /**
     * Query -> Example
     */
    public static StatsClothesInfoExample query2Example(StatsClothesInfoQuery from) {
        StatsClothesInfoExample to = new StatsClothesInfoExample();
        StatsClothesInfoExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getStatsType())) {
            c.andStatsTypeEqualTo(from.getStatsType());
        }
        if (!ObjectUtils.isEmpty(from.getStatsDate())) {
            c.andStatsDateEqualTo(from.getStatsDate());
        }
        if (!ObjectUtils.isEmpty(from.getVipClothesCount())) {
            c.andVipClothesCountEqualTo(from.getVipClothesCount());
        }
        if (!ObjectUtils.isEmpty(from.getAutoTrainCount())) {
            c.andAutoTrainCountEqualTo(from.getAutoTrainCount());
        }
        if (!ObjectUtils.isEmpty(from.getManualDeliveryCount())) {
            c.andManualDeliveryCountEqualTo(from.getManualDeliveryCount());
        }
        if (!ObjectUtils.isEmpty(from.getAutoTrainAndDeliveryCount())) {
            c.andAutoTrainAndDeliveryCountEqualTo(from.getAutoTrainAndDeliveryCount());
        }
        if (!ObjectUtils.isEmpty(from.getRetryMattingCount())) {
            c.andRetryMattingCountEqualTo(from.getRetryMattingCount());
        }
        if (!ObjectUtils.isEmpty(from.getUpdatePromptCount())) {
            c.andUpdatePromptCountEqualTo(from.getUpdatePromptCount());
        }
        if (!ObjectUtils.isEmpty(from.getCopyCount())) {
            c.andCopyCountEqualTo(from.getCopyCount());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getStartDate()) && !ObjectUtils.isEmpty(from.getEndDate())){
            c.andStatsDateBetween(from.getStartDate(), from.getEndDate());
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<StatsClothesInfoVO> doList2VOList(List<StatsClothesInfoDO> list) {
        return CommonUtil.listConverter(list, StatsClothesInfoConverter::do2VO);
    }
}
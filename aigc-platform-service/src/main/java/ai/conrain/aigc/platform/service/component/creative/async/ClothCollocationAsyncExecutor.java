/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.helper.ClothCollocationHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.AddCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CLOTH_COLLOCATION;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_CLOTH_COLLOCATION;

/**
 * 服装搭配异步执行器
 *
 * <AUTHOR>
 * @version : ClothCollocationAsyncExecutor.java, v 0.1 2024/12/6 18:37 renxiao.wu Exp $
 */
@Slf4j
@Service
public class ClothCollocationAsyncExecutor
    extends AbstractBatchToAsyncExecutor<AddCreativeRequest, ClothCollocationModel> {

    @Autowired
    private ClothCollocationHelper clothCollocationHelper;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private ServerHelper serverHelper;

    @Override
    protected Object buildOriginValue(AddCreativeRequest request) {
        if (request.getClothCollocation() == null) {
            return null;
        }

        ClothCollocationModel clothCollocation = ClothCollocationHelper.formatClothCollocation(
            request.getClothCollocation());

        if (ClothCollocationHelper.isEmpty(clothCollocation)) {
            return null;
        }

        return clothCollocation;
    }

    @Override
    protected ClothCollocationModel buildTransValue(CreativeTaskVO task, String origin) {
        ClothCollocationModel originModel = JSONObject.parseObject(origin, ClothCollocationModel.class);
        //对已知的提示词进行翻译
        clothCollocationHelper.transKnownPrompt(originModel);

        JSONObject trans = null;
        ClothCollocationModel transModel = null;
        if (ClothCollocationHelper.isNeedTrans(originModel)) {
            log.info("需要翻译，将调用接口将origin进行翻译，origin={}", originModel);
            String fileServerUrl = serverHelper.getFileServerUrlByUser(task.getUserId());
            trans = comfyUIService.transClothCollocation(JSONObject.toJSONString(originModel), fileServerUrl);
            AssertUtil.assertNotNull(trans, ResultCode.BIZ_FAIL, "transClothCollocation failed");

            transModel = trans.toJavaObject(ClothCollocationModel.class);
            //保存调用后的提示词结果
            clothCollocationHelper.storeNewPrompt(originModel, transModel);
        } else {
            log.info("无需翻译，直接将origin设置为trans，origin={}", originModel);
            transModel = originModel;
        }

        return transModel;
    }

    @Override
    protected Class<ClothCollocationModel> getModelClass() {
        return ClothCollocationModel.class;
    }

    @Override
    public String getOriginKey() {
        return KEY_ORIGIN_CLOTH_COLLOCATION;
    }

    @Override
    public String getTransKey() {
        return KEY_TRANS_CLOTH_COLLOCATION;
    }
}

package ai.conrain.aigc.platform.service.model.query;

import ai.conrain.aigc.platform.service.model.request.BasicChangingClothesRequest;
import com.alibaba.fastjson.JSONObject;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.Date;

import java.io.Serializable;
import java.util.List;

/**
 * FixedCreativeTemplateQuery
 *
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
@Data
public class FixedCreativeTemplateQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 模板名称 */
    private String templateName;

    /** 创建时间（收藏时间） */
    private Date createTime;

    /** 更新时间 */
    private Date modifyTime;

    /** 模板图片列表 */
    private String templateList;

    /** 扩展信息 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;
}

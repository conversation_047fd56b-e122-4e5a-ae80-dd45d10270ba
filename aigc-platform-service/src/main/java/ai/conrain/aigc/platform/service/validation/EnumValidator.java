/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.validation;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import lombok.extern.slf4j.Slf4j;

/**
 * 枚举值校验器
 *
 * <AUTHOR>
 * @version : EnumValidator.java, v 0.1 2023/9/11 22:03 renxiao.wu Exp $
 */
@Slf4j
public class EnumValidator implements ConstraintValidator<EnumValid, Object> {

    private static Map<Class<? extends Enum<?>>, Method> cache = new HashMap<>();

    private Class<? extends Enum<?>> enumClz;

    private boolean nullable = false;

    public void initialize(EnumValid constraintAnnotation) {
        enumClz = constraintAnnotation.value();
        nullable = constraintAnnotation.nullable();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (null == value) {
            if (nullable) {
                return true;
            }

            log.warn("枚举值{}校验失败，枚举为空", enumClz);
            return false;
        }

        try {
            Method method = null;
            if (!cache.containsKey(enumClz)) {
                Field code = enumClz.getDeclaredField("code");
                method = enumClz.getMethod("getByCode", code.getType());
                cache.put(enumClz, method);
            } else {
                method = cache.get(enumClz);
            }

            Object invoke = method.invoke(null, value);
            if (null == invoke) {
                log.warn("枚举值校验失败，未找到对应枚举，value={}，enum={}", value, enumClz.getSimpleName());
                return false;
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchFieldException | NoSuchMethodException e) {
            log.error("", e);
        }

        return true;
    }
}

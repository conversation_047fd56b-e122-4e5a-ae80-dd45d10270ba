package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsMaterialOwnerDO;
import ai.conrain.aigc.platform.dal.example.StatsMaterialOwnerExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsMaterialOwnerQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsMaterialOwnerVO;
import ai.conrain.aigc.platform.service.model.converter.StatsMaterialOwnerConverter;
import ai.conrain.aigc.platform.dal.dao.StatsMaterialOwnerDAO;
import ai.conrain.aigc.platform.service.component.StatsMaterialOwnerService;
import com.alibaba.fastjson.JSONObject;

/**
 * StatsMaterialOwnerService实现
 *
 * <AUTHOR>
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
@Slf4j
@Service
public class StatsMaterialOwnerServiceImpl implements StatsMaterialOwnerService {

    /** DAO */
    @Autowired
    private StatsMaterialOwnerDAO statsMaterialOwnerDAO;

    @Autowired
    private UserService userService;

    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public StatsMaterialOwnerVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsMaterialOwnerDO data = statsMaterialOwnerDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsMaterialOwnerConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsMaterialOwnerDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsMaterialOwner失败");
    }

    @Override
    public StatsMaterialOwnerVO insert(StatsMaterialOwnerVO statsMaterialOwner) {
        AssertUtil.assertNotNull(statsMaterialOwner, ResultCode.PARAM_INVALID, "statsMaterialOwner is null");
        AssertUtil.assertTrue(statsMaterialOwner.getId() == null, ResultCode.PARAM_INVALID, "statsMaterialOwner.id is present");

        // 创建时间、修改时间兜底
        if (statsMaterialOwner.getCreateTime() == null) {
            statsMaterialOwner.setCreateTime(new Date());
        }

        if (statsMaterialOwner.getModifyTime() == null) {
            statsMaterialOwner.setModifyTime(new Date());
        }

        StatsMaterialOwnerDO data = StatsMaterialOwnerConverter.vo2DO(statsMaterialOwner);
        Integer n = statsMaterialOwnerDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsMaterialOwner失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsMaterialOwner返回id为空");
        statsMaterialOwner.setId(data.getId());
        return statsMaterialOwner;
    }


    @Override
    public void updateByIdSelective(StatsMaterialOwnerVO statsMaterialOwner) {
        AssertUtil.assertNotNull(statsMaterialOwner, ResultCode.PARAM_INVALID, "statsMaterialOwner is null");
        AssertUtil.assertTrue(statsMaterialOwner.getId() != null, ResultCode.PARAM_INVALID, "statsMaterialOwner.id is null");

        // 修改时间必须更新
        statsMaterialOwner.setModifyTime(new Date());
        StatsMaterialOwnerDO data = StatsMaterialOwnerConverter.vo2DO(statsMaterialOwner);
        int n = statsMaterialOwnerDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsMaterialOwner失败，影响行数:" + n);
    }

    @Override
    public List<StatsMaterialOwnerVO> queryStatsMaterialOwnerList(StatsMaterialOwnerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsMaterialOwnerExample example = StatsMaterialOwnerConverter.query2Example(query);

        List<StatsMaterialOwnerDO> list = statsMaterialOwnerDAO.selectByExample(example);
        return StatsMaterialOwnerConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsMaterialOwnerCount(StatsMaterialOwnerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsMaterialOwnerExample example = StatsMaterialOwnerConverter.query2Example(query);
        long c = statsMaterialOwnerDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询服装负责人
     */
    @Override
    public PageInfo<StatsMaterialOwnerVO> queryStatsMaterialOwnerByPage(StatsMaterialOwnerQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<StatsMaterialOwnerVO> page = new PageInfo<>();

        StatsMaterialOwnerExample example = StatsMaterialOwnerConverter.query2Example(query);


        // 统计类型为TOTAL时，重新进行分页查询
        if (query.getStatsType().equals(StatsPeriodEnum.TOTAL.getCode())){
            return queryTotalStatsByPage(query);
        }

        long totalCount = statsMaterialOwnerDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<StatsMaterialOwnerDO> list = statsMaterialOwnerDAO.selectByExampleWithBLOBs(example);
        List<StatsMaterialOwnerVO> statsMaterialOwnerVOS = StatsMaterialOwnerConverter.doList2VOList(list);

        // 提取所有的服装 Id(位于 extInfo 的 materialIdList数组中)提取为set 集合
        List<Integer> materialIdSet = statsMaterialOwnerVOS.stream()
                .filter(vo -> vo.getExtInfo() != null && vo.getExtInfo().containsKey("materialIdList"))
                .flatMap(vo -> {
                    try {
                        List<Integer> materialIdList = vo.getExtInfo().getJSONArray("materialIdList").toJavaList(Integer.class);
                        return materialIdList != null ? materialIdList.stream() : Stream.empty();
                    } catch (Exception e) {
                        log.warn("提取materialIdList异常：{}", e.getMessage());
                        return Stream.empty();
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());


        // 批量查询服装信息
        List<MaterialModelVO> materialModelVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialIdSet)) {
            MaterialModelQuery materialModelQuery = new MaterialModelQuery();
            materialModelQuery.setIds(materialIdSet);
            materialModelQuery.setStatus(MaterialModelStatusEnum.ENABLED.getCode());
            materialModelVOS = materialModelService.queryMaterialModelList(materialModelQuery);
        }

        // 重新设置值
        List<MaterialModelVO> finalMaterialModelVOS = materialModelVOS;

        // 直接遍历处理，不需要收集结果，因为是直接修改vo对象
        statsMaterialOwnerVOS.forEach(vo -> {
            // 赋值服装信息
            if (!CollectionUtils.isEmpty(finalMaterialModelVOS) && vo.getExtInfo() != null && vo.getExtInfo().containsKey("materialIdList")) {
                try {
                    List<Integer> voMaterialIds = vo.getExtInfo().getJSONArray("materialIdList").toJavaList(Integer.class);
                    if (!CollectionUtils.isEmpty(voMaterialIds)) {
                        List<MaterialModelVO> matchedMaterials = finalMaterialModelVOS.stream()
                                .filter(material -> voMaterialIds.contains(material.getId()))
                                .collect(Collectors.toList());
                        vo.setMaterialList(matchedMaterials);
                    }
                } catch (Exception e) {
                    log.warn("解析materialIdList异常：{}", e.getMessage());
                }
            }
        });

        page.setList(statsMaterialOwnerVOS);
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsMaterialOwnerVO> statsList) {
        if (CollectionUtils.isEmpty(statsList)) {
            return 0;
        }

        List<StatsMaterialOwnerDO> statsMaterialOwnerDOList = new ArrayList<>();
        for (StatsMaterialOwnerVO statsVO : statsList) {
            StatsMaterialOwnerDO statsDO = StatsMaterialOwnerConverter.vo2DO(statsVO);
            statsMaterialOwnerDOList.add(statsDO);
        }

        return statsMaterialOwnerDAO.batchInsertOrUpdate(statsMaterialOwnerDOList);
    }

    @Override
    public List<StatsMaterialOwnerVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsMaterialOwnerDO> statsMaterialOwnerDOList = statsMaterialOwnerDAO.selectStatsInfoByDateAndPeriod(startDate, endDate, statsType);

        // 转换为VO列表
        return StatsMaterialOwnerConverter.doList2VOList(statsMaterialOwnerDOList);
    }

    /**
     * 查询TOTAL类型的统计数据（分页）
     *
     * @param query 查询条件
     * @return 分页结果
     */
    private PageInfo<StatsMaterialOwnerVO> queryTotalStatsByPage(StatsMaterialOwnerQuery query) {
        PageInfo<StatsMaterialOwnerVO> page = new PageInfo<>();


        Integer offset = (query.getPageNum() - 1) * query.getPageSize();
        // 根据用户 id 进行分组分页查询
        Long totalCount = statsMaterialOwnerDAO.countPageStatsMaterialOwner(query.getUserId(), query.getNickname());
        if (totalCount == 0){
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);
            return page;
        }


        List<Map<String, Object>>  statsMaterialOwnerData = statsMaterialOwnerDAO.selectPageStatsMaterialOwner(query.getPageSize(), offset, query.getUserId(), query.getNickname());

        List<StatsMaterialOwnerDO> statsMaterialOwnerDOList = new ArrayList<>();
        statsMaterialOwnerData.forEach(map -> {
            StatsMaterialOwnerDO statsMaterialOwnerDO = new StatsMaterialOwnerDO();
            statsMaterialOwnerDO.setUserId((Integer) map.get("user_id"));
            statsMaterialOwnerDO.setNickname((String) map.get("nickname"));
            statsMaterialOwnerDO.setDeliveryCount(Integer.valueOf( map.get("delivery_count").toString()));
            statsMaterialOwnerDOList.add(statsMaterialOwnerDO);
        });

        // 重新查询并填充数据
        List<StatsMaterialOwnerVO> statsMaterialOwnerVOS = StatsMaterialOwnerConverter.doList2VOList(statsMaterialOwnerDOList);
        statsMaterialOwnerVOS.forEach(vo -> {
            // 获取用户 id
            Integer userId = vo.getUserId();
            // 获取用户所有的统计数据
            List<StatsMaterialOwnerDO> materialOwnerDOList = statsMaterialOwnerDAO.selectAllByUserId(userId);

            List<StatsMaterialOwnerVO.DateCount> dateCountList = new ArrayList<>();
            materialOwnerDOList.forEach(materialOwnerDO -> {
                StatsMaterialOwnerVO.DateCount dateCount = new StatsMaterialOwnerVO.DateCount();
                dateCount.setCount(materialOwnerDO.getDeliveryCount());
                dateCount.setDate(materialOwnerDO.getStatsDate());
                dateCountList.add(dateCount);
            });

            // 设置统计数据
            vo.setDateCountList(dateCountList);
        });


        page.setList(statsMaterialOwnerVOS);
        page.setSize(CollectionUtils.size(statsMaterialOwnerData));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (long) query.getPageNum() * query.getPageSize());

        return page;
    }

}
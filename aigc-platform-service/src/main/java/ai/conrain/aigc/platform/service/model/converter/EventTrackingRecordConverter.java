package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.EventTrackingRecordDO;
import ai.conrain.aigc.platform.service.model.query.EventTrackingRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.EventTrackingRecordVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * EventTrackingRecordConverter
 *
 * @version EventTrackingRecordService.java v 0.1 2024-12-07 02:51:15
 */
public class EventTrackingRecordConverter {

    /**
     * DO -> VO
     */
    public static EventTrackingRecordVO do2VO(EventTrackingRecordDO from) {
        EventTrackingRecordVO to = new EventTrackingRecordVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTempUserUuid(from.getTempUserUuid());
        to.setIpAddress(from.getIpAddress());
        to.setSessionId(from.getSessionId());
        to.setEventType(from.getEventType());
        to.setEventContent(from.getEventContent());
        to.setPageTitle(from.getPageTitle());
        to.setPageUrl(from.getPageUrl());
        to.setUserRetentionTime(from.getUserRetentionTime());
        to.setOs(from.getOs());
        to.setBrowser(from.getBrowser());
        to.setRequestResource(from.getRequestResource());
        to.setPreReferrer(from.getPreReferrer());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAdditionalData(from.getAdditionalData());

        return to;
    }

    /**
     * VO -> DO
     */
    public static EventTrackingRecordDO vo2DO(EventTrackingRecordVO from) {
        EventTrackingRecordDO to = new EventTrackingRecordDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTempUserUuid(from.getTempUserUuid());
        to.setIpAddress(from.getIpAddress());
        to.setSessionId(from.getSessionId());
        to.setEventType(from.getEventType());
        to.setEventContent(from.getEventContent());
        to.setPageTitle(from.getPageTitle());
        to.setPageUrl(from.getPageUrl());
        to.setUserRetentionTime(from.getUserRetentionTime());
        to.setOs(from.getOs());
        to.setBrowser(from.getBrowser());
        to.setRequestResource(from.getRequestResource());
        to.setPreReferrer(from.getPreReferrer());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAdditionalData(from.getAdditionalData());

        return to;
    }

    /**
     * DO -> Query
     */
    public static EventTrackingRecordQuery do2Query(EventTrackingRecordDO from) {
        EventTrackingRecordQuery to = new EventTrackingRecordQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTempUserUuid(from.getTempUserUuid());
        to.setIpAddress(from.getIpAddress());
        to.setSessionId(from.getSessionId());
        to.setEventType(from.getEventType());
        to.setEventContent(from.getEventContent());
        to.setPageTitle(from.getPageTitle());
        to.setPageUrl(from.getPageUrl());
        to.setUserRetentionTime(from.getUserRetentionTime());
        to.setOs(from.getOs());
        to.setBrowser(from.getBrowser());
        to.setRequestResource(from.getRequestResource());
        to.setPreReferrer(from.getPreReferrer());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAdditionalData(from.getAdditionalData());

        return to;
    }

    /**
     * Query -> DO
     */
    public static EventTrackingRecordDO query2DO(EventTrackingRecordQuery from) {
        EventTrackingRecordDO to = new EventTrackingRecordDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTempUserUuid(from.getTempUserUuid());
        to.setIpAddress(from.getIpAddress());
        to.setSessionId(from.getSessionId());
        to.setEventType(from.getEventType());
        to.setEventContent(from.getEventContent());
        to.setPageTitle(from.getPageTitle());
        to.setPageUrl(from.getPageUrl());
        to.setUserRetentionTime(from.getUserRetentionTime());
        to.setOs(from.getOs());
        to.setBrowser(from.getBrowser());
        to.setRequestResource(from.getRequestResource());
        to.setPreReferrer(from.getPreReferrer());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setAdditionalData(from.getAdditionalData());

        return to;
    }


    /**
     * do list -> vo list
     */
    public static List<EventTrackingRecordVO> doList2VOList(List<EventTrackingRecordDO> list) {
        return CommonUtil.listConverter(list, EventTrackingRecordConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserPointLogDO;
import ai.conrain.aigc.platform.dal.example.UserPointLogExample;
import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.model.query.UserPointLogQuery;
import ai.conrain.aigc.platform.service.model.vo.UserPointLogVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * UserPointLogConverter
 *
 * @version UserPointLogService.java v 0.1 2024-06-21 04:14:12
 */
public class UserPointLogConverter {

    /**
     * DO -> VO
     */
    public static UserPointLogVO do2VO(UserPointLogDO from) {
        UserPointLogVO to = new UserPointLogVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(PointLogTypeEnum.getByCode(from.getType()));
        to.setRelatedId(from.getRelatedId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setModelPoint(from.getModelPoint());
        to.setOperatorId(from.getOperatorId());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserPointLogDO vo2DO(UserPointLogVO from) {
        UserPointLogDO to = new UserPointLogDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setRelatedId(from.getRelatedId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setModelPoint(from.getModelPoint());
        to.setOperatorId(from.getOperatorId());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toJSONString() : null);

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserPointLogQuery do2Query(UserPointLogDO from) {
        UserPointLogQuery to = new UserPointLogQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType());
        to.setRelatedId(from.getRelatedId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setModelPoint(from.getModelPoint());
        to.setOperatorId(from.getOperatorId());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserPointLogDO query2DO(UserPointLogQuery from) {
        UserPointLogDO to = new UserPointLogDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setType(from.getType());
        to.setRelatedId(from.getRelatedId());
        to.setPoint(from.getPoint());
        to.setGivePoint(from.getGivePoint());
        to.setExperiencePoint(from.getExperiencePoint());
        to.setModelPoint(from.getModelPoint());
        to.setOperatorId(from.getOperatorId());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> Example
     */
    public static UserPointLogExample query2Example(UserPointLogQuery from) {
        UserPointLogExample to = new UserPointLogExample();
        UserPointLogExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getUserIds())) {
            c.andUserIdIn(from.getUserIds());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getTypeList())) {
            c.andTypeIn(from.getTypeList());
        }

        if (!ObjectUtils.isEmpty(from.getRelatedId())) {
            c.andRelatedIdEqualTo(from.getRelatedId());
        }
        if (!ObjectUtils.isEmpty(from.getPoint())) {
            c.andPointEqualTo(from.getPoint());
        }
        if (!ObjectUtils.isEmpty(from.getGivePoint())) {
            c.andGivePointEqualTo(from.getGivePoint());
        }
        if (!ObjectUtils.isEmpty(from.getExperiencePoint())) {
            c.andExperiencePointEqualTo(from.getExperiencePoint());
        }
        if (!ObjectUtils.isEmpty(from.getModelPoint())) {
            c.andModelPointEqualTo(from.getModelPoint());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }

        if (!ObjectUtils.isEmpty(from.getDateFrom())) {
            c.andCreateTimeGreaterThanOrEqualTo(DateUtils.parseShort(from.getDateFrom()));
        }

        if (!ObjectUtils.isEmpty(from.getDateTo())) {
            c.andCreateTimeLessThanOrEqualTo(DateUtils.parseShortLastTime(from.getDateTo()));
        }

        if (from.isConsumeMusePoint()) {
            c.andPointLessThan(0);
        }

        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<UserPointLogVO> doList2VOList(List<UserPointLogDO> list) {
        return CommonUtil.listConverter(list, UserPointLogConverter::do2VO);
    }
}
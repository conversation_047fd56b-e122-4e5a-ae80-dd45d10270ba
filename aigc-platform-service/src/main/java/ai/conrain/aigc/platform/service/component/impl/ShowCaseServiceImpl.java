package ai.conrain.aigc.platform.service.component.impl;

import java.io.IOException;
import java.util.List;
import java.util.Date;

import ai.conrain.aigc.platform.service.enums.ShowCaseTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.ShowCaseDO;
import ai.conrain.aigc.platform.service.model.vo.ShowCaseVO;
import ai.conrain.aigc.platform.service.model.converter.ShowCaseConverter;
import ai.conrain.aigc.platform.dal.dao.ShowCaseDAO;
import ai.conrain.aigc.platform.service.component.ShowCaseService;

/**
 * ShowCaseService实现
 *
 * <AUTHOR>
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
@Slf4j
@Service
public class ShowCaseServiceImpl extends AbstractCachedService<ShowCaseVO, Integer, ShowCaseVO>
    implements ShowCaseService {

    /** DAO */
    @Autowired
    private ShowCaseDAO showCaseDAO;
    @Autowired
    private OssHelper ossHelper;

    @Override
    public ShowCaseVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ShowCaseDO data = showCaseDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ShowCaseConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = showCaseDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ShowCase失败");

        forceRefresh();
    }

    @Override
    public ShowCaseVO insert(ShowCaseVO showCase) throws IOException {
        AssertUtil.assertNotNull(showCase, ResultCode.PARAM_INVALID, "showCase is null");
        AssertUtil.assertTrue(showCase.getId() == null, ResultCode.PARAM_INVALID, "showCase.id is present");

        //创建时间、修改时间兜底
        if (showCase.getCreateTime() == null) {
            showCase.setCreateTime(new Date());
        }

        if (showCase.getModifyTime() == null) {
            showCase.setModifyTime(new Date());
        }

        //设置缩略图
        upscaleImage(showCase, null);

        ShowCaseDO data = ShowCaseConverter.vo2DO(showCase);
        int n = showCaseDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ShowCase失败");
        AssertUtil.assertNotNull(data.getId(), "新建ShowCase返回id为空");
        showCase.setId(data.getId());

        refresh();
        return showCase;
    }

    @Override
    public void updateById(ShowCaseVO showCase) throws IOException {
        AssertUtil.assertNotNull(showCase, ResultCode.PARAM_INVALID, "showCase is null");
        AssertUtil.assertTrue(showCase.getId() != null, ResultCode.PARAM_INVALID, "showCase.id is null");
        //修改时间必须更新
        showCase.setModifyTime(new Date());

        ShowCaseVO origin = queryByKey(showCase.getId());

        //设置缩略图
        upscaleImage(showCase, origin);

        ShowCaseDO data = ShowCaseConverter.vo2DO(showCase);
        int n = showCaseDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ShowCase失败，影响行数:" + n);

        refresh();
    }

    @Override
    protected List<ShowCaseVO> loadAll() {
        List<ShowCaseDO> list = showCaseDAO.selectAll();
        return ShowCaseConverter.doList2VOList(list);
    }

    @Override
    protected Integer getKey(ShowCaseVO cache) {
        return cache.getId();
    }

    @Override
    protected ShowCaseVO getValue(ShowCaseVO cache) {
        return cache;
    }

    /**
     * 图片处理
     *
     * @param showCase 优秀案例
     * @param origin   原始图片
     * @throws IOException 异常
     */
    private void upscaleImage(ShowCaseVO showCase, ShowCaseVO origin) throws IOException {
        if (showCase.getType() == ShowCaseTypeEnum.VIDEO) {
            return;
        }
        if (StringUtils.isNotBlank(showCase.getMainUrl()) && (origin == null || !StringUtils.equals(
            showCase.getMainUrl(), origin.getMainUrl()))) {
            String imageUrl = ossHelper.resizeAndUpload(showCase.getMainUrl(), 284 * 2, 284 * 2);
            showCase.setShowImage(imageUrl);
        }

        if (StringUtils.isNotBlank(showCase.getModelUrl()) && (origin == null || !StringUtils.equals(
            showCase.getModelUrl(), origin.getModelUrl()))) {
            String imageUrl = ossHelper.resizeAndUpload(showCase.getModelUrl(), 40 * 2, 40 * 2);
            showCase.setModelMiniUrl(imageUrl);
        }
    }
}
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.converter.CreativeTaskConverter;
import ai.conrain.aigc.platform.service.model.request.FaceSceneSwitchRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ElementUtils;
import ai.conrain.aigc.platform.service.util.FaceSceneFlowUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;
import static ai.conrain.aigc.platform.service.enums.CameraAngleEnum.FRONT_VIEW;
import static ai.conrain.aigc.platform.service.enums.ModelVersionEnum.FLUX;

@Slf4j
@Service
public class FaceSceneSwitchCreativeService extends AbstractCreativeService<FaceSceneSwitchRequest> {

    private Random random = new Random();
    @Autowired
    private OssService ossService;

    @Autowired
    private BatchToAsyncExecutor customSceneAsyncExecutor;

    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    protected CreativeBatchVO buildData(FaceSceneSwitchRequest request, MaterialModelVO modelVO) throws IOException {
        if (StringUtils.isBlank(request.getCustomSceneImg())) {
            AssertUtil.assertNotNull(request.getConfigs(), ResultCode.PARAM_INVALID, "请求参数异常");
            AssertUtil.assertTrue(!request.getConfigs().isEmpty(), ResultCode.PARAM_INVALID, "请求参数异常");
        }

        CreativeBatchVO data = CreativeBatchConverter.request2VO(request);
        batchFillHelper.fillOriginBatchInfo(request.getImage(), null, data);
        customSceneAsyncExecutor.storeSync(request, data);
        data.addExtInfo(KEY_ORIGIN_IMAGE, request.getImage());
        data.addExtInfo(KEY_TARGET_IMAGE, batchFillHelper.uploadImageForcePng(request.getImage()));
        if (StringUtils.isNotBlank(request.getCustomSceneImg())) {
            data.addExtInfo(KEY_ORIGIN_CUSTOM_SCENE_IMG, request.getCustomSceneImg());
            data.addExtInfo(KEY_CUSTOM_SCENE_IMG, batchFillHelper.uploadImageForcePng(request.getCustomSceneImg()));
        }
        if (ObjectUtils.isEmpty(batchFillHelper.getTaskByUrl(request.getImage()))) {
            data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        return data;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        List<CreativeTaskVO> result = new ArrayList<>();

        //每张图生成一个任务
        for (int i = 0; i < batch.getBatchCnt(); i++) {
            //插入任务数据
            CreativeTaskVO target = CreativeTaskConverter.request2VO(batch);
            fillTaskExt(target, batch, elements, i);

            // 填充前置任务结果信息
            fillPreTaskResultKey(target, getCreativeType());

            CreativeTaskVO data = creativeTaskService.insert(target);
            result.add(data);
        }

        return result;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        super.fillTaskExt(target, batch, elements, idx);
        target.addExtInfo(KEY_TARGET_IMAGE, batch.getStringFromExtInfo(KEY_TARGET_IMAGE));
        target.addExtInfo(KEY_CUSTOM_SCENE_IMG, batch.getStringFromExtInfo(KEY_CUSTOM_SCENE_IMG));

        customSceneAsyncExecutor.restoreTask(target, batch);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FACE_SCENE_SWITCH;
    }

    @Override
    protected Map<Integer, List<Integer>> getConfigs(FaceSceneSwitchRequest request) {
        return request.getConfigs();
    }

    @Override
    protected CreativeElementVO fetchElementById(Integer id, FaceSceneSwitchRequest request, MaterialModelVO modelVO) {
        CreativeElementVO element = super.fetchElementById(id, request, modelVO);
        // 拦截非 Lora 模特
        AssertUtil.assertNotNull(element, ResultCode.BIZ_FAIL, "当前模特状态异常, 请使用其他模特");
        if (ElementConfigKeyEnum.FACE.name().equals(element.getConfigKey())) {
            AssertUtil.assertTrue(ElementUtils.isLoraFace(element), ResultCode.BIZ_FAIL, "当前模特状态异常, 请使用其他模特");
        }
        List<CreativeElementVO> children = element.getChildren().stream().filter(
                e -> new HashSet<>(e.getType()).containsAll(Arrays.asList(FRONT_VIEW.getCode(), FLUX.getCode()))
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(children)) {
            return element;
        }
        return children.get(0);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(KEY_TARGET_IMAGE, task.getStringFromExtInfo(KEY_TARGET_IMAGE));
        context.put(KEY_CUSTOM_SCENE_IMG, task.getStringFromExtInfo(KEY_CUSTOM_SCENE_IMG));
        if (CollectionUtils.isNotEmpty(elements)) {
            CreativeElementVO faceElement = elements.stream().filter(
                            element -> element.getConfigKey().equals(ElementConfigKeyEnum.FACE.name()))
                    .findFirst().orElse(null);
            CreativeElementVO sceneElement = elements.stream().filter(
                            element -> element.getConfigKey().equals(ElementConfigKeyEnum.SCENE.name()))
                    .findFirst().orElse(null);
            context.put(ElementConfigKeyEnum.FACE.name(), faceElement);
            context.put(ElementConfigKeyEnum.SCENE.name(), sceneElement);
        }
        customSceneAsyncExecutor.asyncExecAndStore(task, elements);
        // 06-30 换头流程，固定降噪0.9
        context.put("denoised", 0.9);
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());
        CreativeElementVO sceneElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.SCENE.name());
        // 1. 是否换头
        boolean isSwapFace = Objects.nonNull(faceElement);
        // 1.1 loraSwapFace
        boolean isLoraSwapFace = ElementUtils.isLoraFace(faceElement) && StringUtils.equals(YES,
                faceElement.getExtInfo(KEY_LORA_SWAP_FACE, String.class));
        // 2. 是否换背景
        // 2.1 生成背景
        boolean isCreateScene = Objects.nonNull(sceneElement);
        // 2.2 上传背景
        boolean isUploadScene = StringUtils.isNotBlank(task.getStringFromExtInfo(KEY_CUSTOM_SCENE_IMG));

        if (isSwapFace && !(isCreateScene || isUploadScene)) {
            if (isLoraSwapFace) {
                log.info("[模特换头]当前模特isLoraSwapFace=Y, taskId={}, batchId={}", task.getId(), task.getBatchId());
                return SystemConstants.SWAP_FACE_INSTANT_ID_FLOW_PARAMS;
            } else {
                log.info("[模特换头]当前模特isLoraSwapFace=N, taskId={}, batchId={}", task.getId(), task.getBatchId());
            }
            return SystemConstants.SWAP_FACE_FLOW_PARAMS;
        }
        return super.getFlowKey(elements, modelVO, task, context);
    }

    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        CreativeElementVO faceElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.FACE.name());
        CreativeElementVO sceneElement = (CreativeElementVO) context.get(ElementConfigKeyEnum.SCENE.name());
        // 1. 是否换头
        boolean isSwapFace = Objects.nonNull(faceElement);
        // 2. 是否换背景
        // 2.1 生成背景
        boolean isCreateScene = Objects.nonNull(sceneElement);
        // 2.2 上传背景
        boolean isUploadScene = StringUtils.isNotBlank(task.getStringFromExtInfo(KEY_CUSTOM_SCENE_IMG));
        return FaceSceneFlowUtil.correctFlow(flow, flowKey, isSwapFace, isCreateScene, isUploadScene, context);
    }

    @Override
    protected String postParse(String prompt) {
        prompt = prompt.replaceAll("\r\n|\r|\n", "");
        prompt = prompt.replaceAll("\\\\n", "\\\\n");
        return prompt;
    }

    @Override
    public void fillPreTaskResultKey(CreativeTaskVO creativeTaskVO, CreativeTypeEnum creativeType) {
        // 当前类型不关心传递进入的creativeType的值

        // 绘蛙前置任务返回结果为原始图片，需要设置在 KEY_TARGET_IMAGE
        creativeTaskVO.addExtInfo(KEY_PRE_TASK_RESULT_KEY, KEY_TARGET_IMAGE);
        // 目前只取一张结果图片
        creativeTaskVO.addExtInfo(KEY_PRE_TASK_RESULT_SIZE, 1);
    }

    private Double getDenoise() {
        int randomNumber = random.nextInt(10);
        return randomNumber < 6 ? 0.8 : 0.9;
    }

}

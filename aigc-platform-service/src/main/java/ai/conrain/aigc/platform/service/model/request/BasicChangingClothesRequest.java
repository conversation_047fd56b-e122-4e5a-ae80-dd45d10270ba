package ai.conrain.aigc.platform.service.model.request;

import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;

/**
 * 基础款换衣请求入参
 */
@Data
public class BasicChangingClothesRequest implements CreativeRequest {

    private static final long serialVersionUID = 2060239558687266585L;

    /** 衣服图片（url 或 Base64）*/
    @NotBlank
    private String clotheImage;

    /** 涂层图片（url 或 Base64）*/
    @NotBlank
    private String maskImage;

    /**
     * 抠图批次id
     */
    @NotNull
    private Integer mattingId;

    /** 衣服类型（UPPER：上装  LOWER：下装）*/
    @NotBlank
    private String clotheType;

    /** 配置（0 场景  1 模特） */
    private Map<Integer, List<Integer>> configs;

    /** 图片比例 */
    private String proportion;

    /** 参考图信息列表 */
    @NotEmpty
    private List<ReferenceInfo> referenceInfoList;

    /** 参考图信息 */
    @Data
    public static class ReferenceInfo {

        /** 参考图图片 */
        @NotBlank
        private String imageUrl;

        /** 参考图配置 */
        @NotBlank
        private JSONObject referenceConfig;

        /** 背景标签 */
        private  String backTags;

        /** 背景扩展标签 */
        private  String backExtTags;

        /** LoraId */
        private Integer loraId;

        /** lora地址 */
        private String loraPath;

        /** 原始参考图 */
        private String originalImageUrl;
    }
}

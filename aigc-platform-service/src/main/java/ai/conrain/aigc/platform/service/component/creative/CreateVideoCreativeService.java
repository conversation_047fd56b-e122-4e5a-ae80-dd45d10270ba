/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.request.CreateVideoRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TIME_SECS_4_VIDEO;

/**
 * 图生视频创作服务
 *
 * <AUTHOR>
 * @version : CreateVideoCreativeService.java, v 0.1 2024/8/20 20:10 renxiao.wu Exp $
 */
@Service
public class CreateVideoCreativeService extends AbstractCreativeService<CreateVideoRequest> {
    @Autowired
    private UserService userService;

    @Override
    protected CreativeBatchVO buildData(CreateVideoRequest request, MaterialModelVO modelVO) {
        CreativeBatchVO data = new CreativeBatchVO();
        data.setType(getCreativeType());
        data.setStatus(CreativeStatusEnum.PROCESSING);
        data.setBatchCnt(request.getImages().size());
        data.setShowImage(request.getImages().get(0));
        data.setImageProportion(ProportionTypeEnum.THREE_FOUR.getCode());
        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.addExtInfo(KEY_ORIGIN_IMAGE, request.getImages());

        Integer time4Video = request.getTimeSecs4Video() != null ? request.getTimeSecs4Video() : 5;
        data.addExtInfo(KEY_TIME_SECS_4_VIDEO, time4Video);
        data.setTitle(request.getTitle());
        //注意，这里只是视图，并不保存到数据库
        data.setTimeSecs4Video(time4Video);
        return data;
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.CREATE_VIDEO;
    }
}

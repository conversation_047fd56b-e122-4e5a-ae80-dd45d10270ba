package ai.conrain.aigc.platform.service.component.creative.async;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.TextModerationService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.enums.ElementConfigKeyEnum;
import ai.conrain.aigc.platform.service.model.biz.CustomSceneModel;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.CustomSceneCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_CAMERA_ANGLE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_CUSTOM_SCENE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_CUSTOM_SCENE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.clothStyleType;

/**
 * 用户自定义场景异步执行器
 *
 * <AUTHOR>
 * @version 2024/12/6
 */
@Slf4j
@Service("customSceneAsyncExecutor")
public class CustomSceneAsyncExecutor extends AbstractBatchToAsyncExecutor<CustomSceneCreativeRequest, CustomSceneModel> {

    @Autowired
    ComfyUIService comfyUIService;
    @Autowired
    ServerHelper serverHelper;
    @Autowired
    private TextModerationService textModerationService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    protected Object buildOriginValue(CustomSceneCreativeRequest request) {
        if (StringUtils.isBlank(request.getCustomScene())) {
            return null;
        }
        AssertUtil.assertTrue(moderate(request.getCustomScene()), ResultCode.TEXT_MODERATION,
            "[异步翻译自定义场景]您输入的文本中包含敏感词，请优化");
        return request.getCustomScene();
    }

    @Override
    protected CustomSceneModel buildTransValue(CreativeTaskVO task, String origin) {
        List<String> cameraAngle = task.getExtInfo(KEY_CAMERA_ANGLE, List.class);

        JSONObject transParams = new JSONObject();
        transParams.put("text", origin);
        transParams.put("gender", task.getExtValue(clothStyleType, String.class));
        transParams.put("view", CameraAngleEnum.getOrientationByStr(cameraAngle));
        transParams.put("shot", CameraAngleEnum.getBodyPositionByStr(cameraAngle));

        String fileServerUrl = serverHelper.getFileServerUrlByUser(task.getUserId());
        JSONObject trans = comfyUIService.transCustomScene(transParams.toString(), fileServerUrl);
        AssertUtil.assertNotNull(trans, ResultCode.BIZ_FAIL, "transCustomScene failed");
        log.info("[异步翻译自定义场景]transCustomScene result:{}", trans);
        CustomSceneModel scene = new CustomSceneModel();
        try {
            scene = trans.toJavaObject(CustomSceneModel.class);
        } catch (Exception e) {
            log.error("[异步翻译自定义场景]transCustomScene failed, trans={}", trans);
        }
        return scene;
    }

    @Override
    protected Class<CustomSceneModel> getModelClass() {
        return CustomSceneModel.class;
    }

    @Override
    protected boolean aboutToExt() {
        return true;
    }

    @Override
    protected void fillExtInfo(CustomSceneModel transValue, CreativeTaskVO task, List<CreativeElementVO> elements) {
        log.info("[异步翻译自定义场景]transValue={}", transValue);
        if (transValue == null) {
            return;
        }
        CreativeElementVO element = new CreativeElementVO();
        element.setName("customScene");
        element.setLevel(2);
        element.setConfigKey(ElementConfigKeyEnum.SCENE.name());
        // extInfo
        JSONObject extInfo = new JSONObject();
        extInfo.put("posture", transValue.getPose());
        extInfo.put("style", transValue.getStyle());
        extInfo.put("lens", transValue.getComposition());
        extInfo.put("opScope", "ALL");
        // tags
        String tags = transValue.getBackground();
        // type
        List<String> type = new ArrayList<>();
        type.add("v_2");
        type.add(task.getExtInfo(clothStyleType, String.class));
        List<?> cameraAngles = task.getExtInfo(KEY_CAMERA_ANGLE, List.class);
        type.addAll(cameraAngles.stream().map(Object::toString).collect(Collectors.toList()));

        element.setExtInfo(extInfo);
        element.setTags(tags);
        element.setType(type);

        log.info("[异步翻译自定义场景] add element:{}", element);
        elements.add(element);
    }

    @Override
    public String getOriginKey() {
        return KEY_ORIGIN_CUSTOM_SCENE;
    }

    @Override
    public String getTransKey() {
        return KEY_TRANS_CUSTOM_SCENE;
    }

    /**
     * 文本安全校验
     *
     * @param text 文本内容
     * @return true审核通过
     */
    private boolean moderate(String text) {
        if (StringUtils.isBlank(text)) {
            return true;
        }
        List<String> labels = textModerationService.textModeratePlus(text);
        if (CollectionUtils.isEmpty(labels)) {
            log.info("文本内容安全审核通过，审核结果为空");
            return true;
        }

        List<String> configLabels = systemConfigService.queryTextModerationForbidLabel();
        for (String label : labels) {
            if (configLabels.contains(label)) {
                log.warn("文本内容安全审核未通过, 命中规则: {}, 文本内容: {}", label, text);
                return false;
            }
        }
        log.info("文本内容安全审核通过,审核结果: {}, 文本内容: {}", labels, text);
        return true;
    }
}

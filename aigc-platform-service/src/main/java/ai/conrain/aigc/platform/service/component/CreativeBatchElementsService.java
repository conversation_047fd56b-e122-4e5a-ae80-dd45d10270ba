package ai.conrain.aigc.platform.service.component;

import java.util.List;
import java.util.Map;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchElementsQuery;
import ai.conrain.aigc.platform.service.model.query.CreativeElementQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchElementsVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;

/**
 * 创作批次属性 Service定义
 *
 * <AUTHOR>
 * @version CreativeBatchElementsService.java v 0.1 2024-05-08 03:35:57
 */
public interface CreativeBatchElementsService {

    /**
     * 查询创作批次属性对象
     *
     * @param id 主键
     * @return 返回结果
     */
    CreativeBatchElementsVO selectById(Integer id);

    /**
     * 删除创作批次属性对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加创作批次属性对象
     *
     * @param creativeBatchElements 对象参数
     * @return 返回结果
     */
    CreativeBatchElementsVO insert(CreativeBatchElementsVO creativeBatchElements);

    /**
     * 修改创作批次属性对象
     *
     * @param creativeBatchElements 对象参数
     */
    void updateByIdSelective(CreativeBatchElementsVO creativeBatchElements);

    /**
     * 带条件批量查询创作批次属性列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<CreativeBatchElementsVO> queryCreativeBatchElementsList(CreativeBatchElementsQuery query);

    /**
     * 带条件批量查询近期创做的元素列表
     *
     * @param query 查询条件
     * @return 结果
     */
    List<CreativeElementVO> queryRecentElements(CreativeBatchElementsQuery query);

    /**
     * 查询最近使用的 faceLora
     *
     * @param userId 用户id
     * @return 结果
     */
    List<CreativeElementVO> queryRecentFaceLora(Integer userId);

    /**
     * 带条件查询创作批次属性数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryCreativeBatchElementsCount(CreativeBatchElementsQuery query);

    /**
     * 带条件分页查询创作批次属性
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<CreativeBatchElementsVO> queryCreativeBatchElementsByPage(CreativeBatchElementsQuery query);

    /**
     * 批量插入
     *
     * @param batch   批次
     * @param configs 配置信息
     */
    void batchInsert(CreativeBatchVO batch, List<CreativeElementVO> configs);

    /**
     * 根据批次id查询元素配置
     *
     * @param batchId          批次id
     * @param includesChildren 是否包含子元素
     * @return 元素配置
     */
    List<CreativeElementVO> queryBatchElements(Integer batchId, boolean includesChildren);

    /**
     * 根据批次id列表批量查询元素配置
     *
     * @param batchIds 批次id列表
     * @return 结果
     */
    Map<Integer, List<CreativeElementVO>> batchQueryBatchElements(List<Integer> batchIds);

    /**
     * 根据批次id查询元素配置(不可为空，为空直接返回)
     *
     * @param batchId          批次id
     * @param includesChildren 是否包含子元素
     * @return 元素配置
     */
    List<CreativeElementVO> queryBatchElementsNotEmpty(Integer batchId, boolean includesChildren);

    /**
     * 查询有图片的子元素id
     *
     * @param query           查询条件
     * @param needResultCount 需要的结果数量
     * @return 含有图片的子元素id
     */
    List<Integer> queryHasShowImageChildrenIds(CreativeElementQuery query, Integer needResultCount);

}
package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.UserOpLogDO;
import ai.conrain.aigc.platform.dal.example.UserOpLogExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.UserOpLogQuery;
import ai.conrain.aigc.platform.service.model.vo.UserOpLogVO;
import ai.conrain.aigc.platform.service.model.converter.UserOpLogConverter;
import ai.conrain.aigc.platform.dal.dao.UserOpLogDAO;
import ai.conrain.aigc.platform.service.component.UserOpLogService;

/**   
 * UserOpLogService实现
 *
 * <AUTHOR>
 * @version UserOpLogService.java v 0.1 2024-01-25 09:31:00
 */
@Slf4j
@Service
public class UserOpLogServiceImpl implements UserOpLogService {

	/** DAO */
	@Autowired
	private UserOpLogDAO userOpLogDAO;

	@Override
	public UserOpLogVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		UserOpLogDO data = userOpLogDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return UserOpLogConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = userOpLogDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除UserOpLog失败");
	}

	@Override
	public UserOpLogVO insert(UserOpLogVO userOpLog) {
		AssertUtil.assertNotNull(userOpLog, ResultCode.PARAM_INVALID, "userOpLog is null");
		AssertUtil.assertTrue(userOpLog.getId() == null, ResultCode.PARAM_INVALID, "userOpLog.id is present");

		//创建时间、修改时间兜底
		if (userOpLog.getCreateTime() == null) {
			userOpLog.setCreateTime(new Date());
		}

		if (userOpLog.getModifyTime() == null) {
			userOpLog.setModifyTime(new Date());
		}

		UserOpLogDO data = UserOpLogConverter.vo2DO(userOpLog);
		Integer n = userOpLogDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建UserOpLog失败");
		AssertUtil.assertNotNull(data.getId(), "新建UserOpLog返回id为空");
		userOpLog.setId(data.getId());
		return userOpLog;
	}


	@Override
	public void updateByIdSelective(UserOpLogVO userOpLog) {
		AssertUtil.assertNotNull(userOpLog, ResultCode.PARAM_INVALID, "userOpLog is null");
    	AssertUtil.assertTrue(userOpLog.getId() != null, ResultCode.PARAM_INVALID, "userOpLog.id is null");

		//修改时间必须更新
		userOpLog.setModifyTime(new Date());
		UserOpLogDO data = UserOpLogConverter.vo2DO(userOpLog);
		int n = userOpLogDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新UserOpLog失败，影响行数:" + n);
	}

	@Override
	public List<UserOpLogVO> queryUserOpLogList(UserOpLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		UserOpLogExample example = UserOpLogConverter.query2Example(query);

		List<UserOpLogDO> list = userOpLogDAO.selectByExample(example);
			return UserOpLogConverter.doList2VOList(list);
	}

	@Override
	public Long queryUserOpLogCount(UserOpLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		UserOpLogExample example = UserOpLogConverter.query2Example(query);
		long c = userOpLogDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询用户操作记录
	 */
	@Override
	public PageInfo<UserOpLogVO> queryUserOpLogByPage(UserOpLogQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<UserOpLogVO> page = new PageInfo<>();

		UserOpLogExample example = UserOpLogConverter.query2Example(query);
		long totalCount = userOpLogDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<UserOpLogDO> list = userOpLogDAO.selectByExample(example);
		page.setList(UserOpLogConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

/**
 * BigDecimal反序列化器
 *
 * <AUTHOR>
 * @version : BigDecimalDeserializer.java, v 0.1 2023/10/20 00:35 renxiao.wu Exp $
 */
public class BigDecimalDeserializer extends JsonDeserializer<BigDecimal> {

    private final DecimalFormat decimalFormat;

    public BigDecimalDeserializer() {
        decimalFormat = new DecimalFormat();
        decimalFormat.setParseBigDecimal(true);
    }

    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
        String value = p.getText();
        if (StringUtils.isBlank(value)) {
            return null;
        }

        try {
            return (BigDecimal)decimalFormat.parse(value);
        } catch (Throwable e) {
            throw new RuntimeException("Failed to parse BigDecimal", e);
        }
    }
}
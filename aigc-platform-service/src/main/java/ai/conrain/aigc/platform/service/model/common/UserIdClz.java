/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

/**
 * 带userId的接口
 *
 * <AUTHOR>
 * @version : UserIdClz.java, v 0.1 2024/6/27 16:52 renxiao.wu Exp $
 */
public interface UserIdClz {
    /**
     * 获取userId
     *
     * @return 用户id
     */
    Integer getUserId();

    /**
     * 设置userId
     *
     * @param userId 用户id
     */
    void setUserId(Integer userId);
}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TestPlanQuery;
import ai.conrain.aigc.platform.service.model.request.AddTestPlanRequest;
import ai.conrain.aigc.platform.service.model.vo.AnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoAnalysisRatioVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemVO;
import ai.conrain.aigc.platform.service.model.vo.TestPlanVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * AB测试计划 Service定义
 *
 * <AUTHOR>
 * @version TestPlanService.java v 0.1 2024-12-19 01:24:06
 */
public interface TestPlanService {

    /**
     * 查询AB测试计划对象
     *
     * @param id 主键
     * @return 返回结果
     */
    TestPlanVO selectById(Integer id);

    /**
     * 删除AB测试计划对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加AB测试计划对象
     *
     * @param testPlan 对象参数
     * @return 返回结果
     */
    TestPlanVO insert(TestPlanVO testPlan);

    /**
     * 创建AB测试计划对象
     *
     * @param request 对象参数
     * @return 返回结果
     */
    TestPlanVO create(AddTestPlanRequest request);

    /**
     * 修改AB测试计划对象
     *
     * @param testPlan 对象参数
     */
    void updateByIdSelective(TestPlanVO testPlan);

    /**
     * 带条件批量查询AB测试计划列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<TestPlanVO> queryTestPlanList(TestPlanQuery query);

    /**
     * 带条件查询AB测试计划数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryTestPlanCount(TestPlanQuery query);

    /**
     * 带条件分页查询AB测试计划
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<TestPlanVO> queryTestPlanByPage(TestPlanQuery query);

    /**
     * 初始化关键任务
     *
     * @param plan 测试计划
     */
    void initTask(TestPlanVO plan) throws IOException;

    /**
     * 同步状态
     *
     * @param plan 测试计划
     */
    void syncStatus(TestPlanVO plan);

    /**
     * 对结果进行打分
     *
     * @param resultId 结果id
     * @param isDraw   是否平局
     * @param score    分数
     */
    void scoring(Integer resultId, Boolean isDraw, boolean score);

    /**
     * 停用/启用
     *
     * @param id     id
     * @param enable 是否可用
     */
    void enable(Integer id, boolean enable);

    /**
     * 完结
     *
     * @param id id
     */
    void finish(Integer id);

    /**
     * 删除测试计划实验项
     *
     * @param testItemId 测试计划实验项id
     */
    void deleteTestItemById(Integer testItemId);

    /**
     * 启用
     *
     * @param planId planId
     * @param itemId itemId
     */
    void begin(Integer planId, Integer itemId) throws IOException;

    /**
     * 获取指定测试计划实验项的 BadCase 比例
     *
     * @param id 测试计划 Id
     * @return {@link AnalysisRatioVO} 打分比例数据
     */
    ExtInfoAnalysisRatioVO extInfoAnalysisRatio(Integer id);

    /**
     * 创建素材信息
     *
     * @param item 测试计划实验项
     * @return 素材信息
     */
    Map<String, Integer> createMaterialInfoWithNewTransaction(TestItemVO item);

    /**
     * 初始化测试计划关联任务
     *
     * @param plan            测试计划
     * @param item            测试计划实验项
     * @param materialInfoMap 素材信息
     */
    void initPlanTaskWithNewTransaction(TestPlanVO plan, TestItemVO item, Map<String, Integer> materialInfoMap)
        throws IOException;

    /**
     * 更新测试计划状态
     *
     * @param plan           测试计划
     * @param testStatusEnum 测试计划状态
     */
    void updateTestPlanStatusWithNewTransaction(TestPlanVO plan, TestStatusEnum testStatusEnum);

    /**
     * 导入实验结果并更新实验结果
     *
     * @param file   文件
     * @param itemId 实验项 Id
     */
    void importAndUpdateExpResult(MultipartFile file, Integer itemId) throws IOException;
}
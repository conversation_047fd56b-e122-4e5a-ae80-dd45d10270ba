package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.PermissionDAO;
import ai.conrain.aigc.platform.dal.entity.PermissionDO;
import ai.conrain.aigc.platform.service.component.PermissionService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.ActionTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.common.Version;
import ai.conrain.aigc.platform.service.model.converter.PermissionConverter;
import ai.conrain.aigc.platform.service.model.vo.PermissionVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PermissionService实现
 *
 * <AUTHOR>
 * @version PermissionService.java v 0.1 2024-01-20 01:21:37
 */
@Slf4j
@Service
public class PermissionServiceImpl extends AbstractCachedService<PermissionVO, String, List<RoleTypeEnum>>
    implements PermissionService {

    /** DAO */
    @Autowired
    private PermissionDAO permissionDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public PermissionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        PermissionDO data = permissionDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return PermissionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = permissionDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Permission失败");
        //强制刷新
        forceRefresh();
    }

    @Override
    public PermissionVO insert(PermissionVO permission) {
        AssertUtil.assertNotNull(permission, ResultCode.PARAM_INVALID, "permission is null");
        AssertUtil.assertTrue(permission.getId() == null, ResultCode.PARAM_INVALID, "permission.id is present");

        //创建时间、修改时间兜底
        if (permission.getCreateTime() == null) {
            permission.setCreateTime(new Date());
        }

        if (permission.getModifyTime() == null) {
            permission.setModifyTime(new Date());
        }

        PermissionDO data = PermissionConverter.vo2DO(permission);
        int n = permissionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Permission失败");
        AssertUtil.assertNotNull(data.getId(), "新建Permission返回id为空");
        permission.setId(data.getId());
        return permission;
    }

    @Override
    public void updateById(PermissionVO permission) {
        AssertUtil.assertNotNull(permission, ResultCode.PARAM_INVALID, "permission is null");
        AssertUtil.assertTrue(permission.getId() != null, ResultCode.PARAM_INVALID, "permission.id is null");
        //修改时间必须更新
        permission.setModifyTime(new Date());

        PermissionDO data = PermissionConverter.vo2DO(permission);
        int n = permissionDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Permission失败，影响行数:" + n);
    }

    @Override
    public void initPermissions(List<PermissionDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        
        // 批量更新权限
        list.stream()
            .map(each -> {
                PermissionVO cachedPermission = queryByKey(each.getAction());
                if (cachedPermission == null) {
                    return null;
                }
                if (!Version.ofNullable(each.getVersion()).isGreaterThan(Version.ofNullable(cachedPermission.getVersion()))) {
                    return null;
                }
                PermissionDO permissionDO = PermissionConverter.vo2DO(cachedPermission);
                permissionDO.setConfig(each.getConfig());
                permissionDO.setVersion(each.getVersion());
                permissionDO.setModifyTime(new Date());
                return permissionDO;
            })
            .filter(Objects::nonNull)
            .forEach(permissionDAO::updateByPrimaryKey);

        long start = System.currentTimeMillis();

        List<PermissionDO> initList = list.stream()
            .filter(each -> queryByKey(each.getAction()) == null)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(initList)) {
            return;
        }

        // 批量初始化权限
        permissionDAO.batchInitPermissions(initList);
        // 刷新缓存
        refresh();
        log.warn("批量初始化角色权限数据, size={}, 耗时{}ms", initList.size(), System.currentTimeMillis() - start);
    }

    @Override
    public boolean hasPermission(String requestPath) {
        PermissionVO permission = queryByPath(requestPath);

        if (null == permission) {
            log.error("当前请求{}未在系统中配置，请检查", requestPath);
            return false;
        }

        List<RoleTypeEnum> roleConfig = permission.getPermissionRoles();
        if (CollectionUtils.isEmpty(roleConfig)) {
            log.error("当前请求{}未配置权限", requestPath);
            return true;
        }

        if (roleConfig.contains(RoleTypeEnum.NONE)) {
            log.info("当前请求{}不需要权限控制", requestPath);
            return true;
        }

        //检查权限时，间隔X分钟更新用户信息，防止过期的用户信息影响安全性，如子账户被停用
        if (OperationContextHolder.getContext().isLoggedIn()) {
            refreshSessionUser();
        }

        RoleTypeEnum roleType = OperationContextHolder.getContext().getRoleType();
        if (!roleConfig.contains(roleType)) {
            log.warn("当前请求{}权限校验不通过,currentRole={},配置权限={}", requestPath, roleType,
                permission.getConfig());
            return false;
        }

        if (!OperationContextHolder.getContext().isMasterUser() && !permission.isAllowedSub()) {
            log.warn("当前请求{}权限校验不通过,当前用户不是主账号", requestPath);
            return false;
        }

        return true;
    }

    @Override
    public boolean isOperateAction(String requestPath) {
        PermissionVO permissionVO = queryByPath(requestPath);

        String memo = permissionVO != null ? permissionVO.getMemo() : null;
        //默认都是操作型
        if (StringUtils.isBlank(memo)) {
            return true;
        }

        JSONObject json = JSONObject.parseObject(memo);
        String actionType = json.getString("actionType");

        return ActionTypeEnum.OPERATE == ActionTypeEnum.getByCode(actionType);
    }

    @Override
    public String parseRequestPath(String requestPath) {
        PermissionVO permissionVO = queryByPath(requestPath);
        return permissionVO != null ? permissionVO.getAction() : requestPath;
    }

    @Override
    protected List<PermissionVO> loadAll() {
        List<PermissionDO> list = permissionDAO.selectAll();
        return PermissionConverter.doList2VOList(list);
    }

    @Override
    protected String getKey(PermissionVO cache) {
        return cache.getAction();
    }

    @Override
    protected List<RoleTypeEnum> getValue(PermissionVO cache) {
        return cache.getPermissionRoles();
    }

    /**
     * 检查权限时，间隔X分钟更新用户信息，防止过期的用户信息影响安全性，如子账户被停用
     */
    private void refreshSessionUser() {
        long maxInterval = systemConfigService.queryLongValue(SystemConstants.MAX_REFRESH_USER_INTERVAL,
            CommonConstants.MAX_REFRESH_USER_INTERVAL);

        long lastRefreshUserTime = OperationContextHolder.getContext().getOperationSession().getLastRefreshUserTime();
        if (System.currentTimeMillis() - lastRefreshUserTime > maxInterval) {
            Integer operatorId = OperationContextHolder.getOperatorUserId();
            UserVO current = userService.selectById(operatorId);
            OperationContextHolder.getContext().setLoginUser(current);
            log.info("权限检查时发现用户信息缓存已超过最大时间{}min,刷新缓存用户{}", maxInterval / 1000 / 60,
                operatorId);
        }
    }

    /**
     * 根据请求路径获取权限配置
     *
     * @param requestPath 请求路径
     * @return 权限配置
     */
    private PermissionVO queryByPath(String requestPath) {
        PermissionVO permission = queryByKey(requestPath);

        if (null == permission) {
            //特殊处理：/users/getUsersById/100005 --> /users/getUsersById/{id} --> /users/getUsersById/*
            String path = StringUtils.substringBeforeLast(requestPath, "/") + "/*";
            permission = queryByKey(path);
        }

        return permission;
    }
}
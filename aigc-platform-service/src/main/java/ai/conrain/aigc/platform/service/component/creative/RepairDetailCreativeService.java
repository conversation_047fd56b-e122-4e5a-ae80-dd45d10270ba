package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.request.RepairDetailRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonFlowUtil;
import com.alibaba.excel.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Service
public class RepairDetailCreativeService extends AbstractCreativeService<RepairDetailRequest>{

    @Autowired
    private TairService tairService;
    @Autowired
    private BatchToAsyncExecutor maskMergeAsyncExecutor;
    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    protected CreativeBatchVO buildData(RepairDetailRequest request, MaterialModelVO modelVO) throws IOException {

        CreativeBatchVO batch = CreativeBatchConverter.request2VO(request);
        // 尝试获取原始task, 并获取原始图片
        batchFillHelper.fillOriginBatchInfo(request.getImage(), null, batch);
        batch.addExtInfo(KEY_ORIGIN_IMAGE, request.getImage());
        batch.addExtInfo(KEY_CLOTH_ORIGIN_IMAGE, request.getClothImage());
        if (StringUtils.equals(HISTORY, request.getImageSource())) {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        maskMergeAsyncExecutor.storeSync(request, batch);
        return batch;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        maskMergeAsyncExecutor.restoreTask(target, batch);
        Integer taskIndex = getTaskIndex(target);
        target.addExtInfo(KEY_GROW_MASK, getGrowMask(taskIndex));
        target.addExtInfo(KEY_CFG, getCFG(taskIndex));
        target.setBatchCnt(2);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        maskMergeAsyncExecutor.asyncExecAndStore(task, elements);
        context.put(KEY_MERGED_IMAGE_COMFY_UI + "_" + "origin", task.getStringFromExtInfo(KEY_MERGED_IMAGE_COMFY_UI + "_" + "origin"));
        context.put(KEY_MERGED_IMAGE_COMFY_UI + "_" + "cloth", task.getStringFromExtInfo(KEY_MERGED_IMAGE_COMFY_UI + "_" + "cloth"));
        context.put(KEY_GROW_MASK, task.getStringFromExtInfo(KEY_GROW_MASK));
        context.put(KEY_CFG, task.getStringFromExtInfo(KEY_CFG));
    }

    @Override
    public String correctFlow(String flow, Map<String, Object> context, CreativeTaskVO task, String flowKey) {
        return CommonFlowUtil.correctNumber(flow, context);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.REPAIR_DETAIL;
    }

    private Integer getGrowMask(Integer taskIndex) {
        return Math.floorMod((taskIndex - 1), REPAIR_DETAIL_TASK_NUM) * 150;
    }

    private Double getCFG(Integer taskIndex) {
        return (taskIndex % 2 == 0) ? 1 : 2.5;
    }

    private Integer getTaskIndex(CreativeTaskVO task) {
        String cacheKey = KEY_TASK_INDEX + "_" + CreativeTypeEnum.REPAIR_DETAIL.getCode() +"_" + task.getBatchId();
        return tairService.incr(cacheKey, 120).intValue();
    }
}

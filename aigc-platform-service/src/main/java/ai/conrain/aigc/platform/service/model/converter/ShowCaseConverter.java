package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ShowCaseDO;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.ShowCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import ai.conrain.aigc.platform.service.model.query.ShowCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.ShowCaseVO;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSONObject;

/**
 * ShowCaseConverter
 *
 * @version ShowCaseService.java v 0.1 2024-11-25 05:41:27
 */
public class ShowCaseConverter {

    /**
     * DO -> VO
     */
    public static ShowCaseVO do2VO(ShowCaseDO from) {
        ShowCaseVO to = new ShowCaseVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(ShowCaseTypeEnum.getByCode(from.getType()));
        to.setMainUrl(from.getMainUrl());
        to.setShowImage(from.getShowImage());
        to.setFaceId(from.getFaceId());
        to.setSceneId(from.getSceneId());
        to.setModelId(from.getModelId());
        to.setModelUrl(from.getModelUrl());
        to.setModelMiniUrl(from.getModelMiniUrl());
        to.setOrder(from.getOrder());
        if (StringUtils.isNotBlank(from.getTags())) {
            to.setTags(Arrays.asList(from.getTags().split(",")));
        }
        to.setStatus(MaterialModelStatusEnum.getByCode(from.getStatus()));
        to.setTopped(from.isTopped());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (StringUtils.isNotBlank(from.getClothCollocation())) {
            to.setClothCollocation(JSONObject.parseObject(from.getClothCollocation(), ClothCollocationModel.class));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static ShowCaseDO vo2DO(ShowCaseVO from) {
        ShowCaseDO to = new ShowCaseDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType().getCode());
        to.setMainUrl(from.getMainUrl());
        to.setShowImage(from.getShowImage());
        to.setFaceId(from.getFaceId());
        to.setSceneId(from.getSceneId());
        to.setModelId(from.getModelId());
        to.setModelUrl(from.getModelUrl());
        to.setModelMiniUrl(from.getModelMiniUrl());
        to.setOrder(from.getOrder());
        if (CollectionUtils.isNotEmpty(from.getTags())) {
            to.setTags(String.join(",", from.getTags()));
        }
        to.setStatus(from.getStatus().getCode());
        to.setTopped(from.isTopped());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (from.getClothCollocation() != null) {
            to.setClothCollocation(JSONObject.toJSONString(from.getClothCollocation()));
        }

        return to;
    }

    /**
     * DO -> Query
     */
    public static ShowCaseQuery do2Query(ShowCaseDO from) {
        ShowCaseQuery to = new ShowCaseQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setMainUrl(from.getMainUrl());
        to.setShowImage(from.getShowImage());
        to.setFaceId(from.getFaceId());
        to.setSceneId(from.getSceneId());
        to.setModelId(from.getModelId());
        to.setModelUrl(from.getModelUrl());
        to.setOrder(from.getOrder());
        to.setTags(from.getTags());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothCollocation(from.getClothCollocation());

        return to;
    }

    /**
     * Query -> DO
     */
    public static ShowCaseDO query2DO(ShowCaseQuery from) {
        ShowCaseDO to = new ShowCaseDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setMainUrl(from.getMainUrl());
        to.setShowImage(from.getShowImage());
        to.setFaceId(from.getFaceId());
        to.setSceneId(from.getSceneId());
        to.setModelId(from.getModelId());
        to.setModelUrl(from.getModelUrl());
        to.setOrder(from.getOrder());
        to.setTags(from.getTags());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setClothCollocation(from.getClothCollocation());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ShowCaseVO> doList2VOList(List<ShowCaseDO> list) {
        return CommonUtil.listConverter(list, ShowCaseConverter::do2VO);
    }
}
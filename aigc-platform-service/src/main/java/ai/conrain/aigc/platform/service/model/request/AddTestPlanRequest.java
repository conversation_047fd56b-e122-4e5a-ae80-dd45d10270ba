/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import java.io.Serializable;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.vo.TestItemVO;

import lombok.Data;

/**
 * 添加测试任务请求
 *
 * <AUTHOR>
 * @version : AddTestPlanRequest.java, v 0.1 2024/12/19 23:01 renxiao.wu Exp $
 */
@Data
public class AddTestPlanRequest implements Serializable {
    private static final long serialVersionUID = 5146120656987762272L;
    /** 测试计划名称 */
    private String name;

    /** 类型，TRAIN、CREATIVE */
    private TestTypeEnum type = TestTypeEnum.CREATIVE;

    /** 状态，DISABLED、ENABLED、PROCESSING、COMPARING、FINISHED */
    private TestStatusEnum status = TestStatusEnum.ENABLED;

    /** 备注 */
    private String memo;

    private JSONObject extInfo;

    private List<TestItemVO> items;
}

package ai.conrain.aigc.platform.service.model.query;


import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * SalesSuccessStoriesQuery
 *
 * @version SalesSuccessStoriesService.java v 0.1 2025-06-26 05:49:50
 */
@Data
public class SalesSuccessStoriesQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 案例名称 */
    private String name;

    /** 是否置顶 */
    private Boolean topped;

    /** 客户id */
    private Integer customerId;

    /** 客户名称 */
    private String customerName;

    /** 创作批次id */
    private Integer batchId;

    /** 服装id */
    private Integer modelId;

    /** 服装展示图片 */
    private String modelUrl;

    /** 备注 */
    private String memo;

    /** 归属主账号id */
    private Integer userId;

    /** 操作员/子账号id */
    private Integer operatorId;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;


    /** 图片地址列表，jsonArray格式 */
    private String imageUrls;

    /** 扩展 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

}

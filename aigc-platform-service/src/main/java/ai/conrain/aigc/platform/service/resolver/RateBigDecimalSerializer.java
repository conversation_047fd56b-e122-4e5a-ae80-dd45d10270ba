/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;
import java.math.BigDecimal;

import ai.conrain.aigc.platform.service.util.BigDecimalUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.RATE_DECIMAL_FORMAT;

/**
 * 费率型BigDecimal序列化器
 *
 * <AUTHOR>
 * @version : RateBigDecimalSerializer.java, v 0.1 2023/9/25 10:57 renxiao.wu Exp $
 */
public class RateBigDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
        if (null == bigDecimal) {
            return;
        }

        if (BigDecimalUtils.equalsZero(bigDecimal)) {
            jsonGenerator.writeString("0%");
            return;
        }

        // 格式化保留两位小数
        jsonGenerator.writeString(RATE_DECIMAL_FORMAT.format(bigDecimal));
    }
}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.SalesSuccessStoriesDAO;
import ai.conrain.aigc.platform.dal.entity.SalesSuccessStoriesDO;
import ai.conrain.aigc.platform.dal.example.SalesSuccessStoriesExample;
import ai.conrain.aigc.platform.service.component.CreativeBatchService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SalesSuccessStoriesService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.SalesSuccessStoriesConverter;
import ai.conrain.aigc.platform.service.model.query.SalesSuccessStoriesQuery;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.SalesSuccessStoriesVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SalesSuccessStoriesService实现
 *
 * <AUTHOR>
 * @version SalesSuccessStoriesService.java v 0.1 2025-06-26 05:49:50
 */
@Slf4j
@Service
public class SalesSuccessStoriesServiceImpl implements SalesSuccessStoriesService {

    /** DAO */
    @Autowired
    private SalesSuccessStoriesDAO salesSuccessStoriesDAO;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private UserService userService;
    @Autowired
    private CreativeBatchService creativeBatchService;

    @Override
    public SalesSuccessStoriesVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        SalesSuccessStoriesDO data = salesSuccessStoriesDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return SalesSuccessStoriesConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = salesSuccessStoriesDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除SalesSuccessStories失败");
    }

    @Override
    public SalesSuccessStoriesVO insert(SalesSuccessStoriesVO salesSuccessStories) {
        AssertUtil.assertNotNull(salesSuccessStories, ResultCode.PARAM_INVALID, "salesSuccessStories is null");
        AssertUtil.assertTrue(salesSuccessStories.getId() == null, ResultCode.PARAM_INVALID,
            "salesSuccessStories.id is present");

        //创建时间、修改时间兜底
        if (salesSuccessStories.getCreateTime() == null) {
            salesSuccessStories.setCreateTime(new Date());
        }

        if (salesSuccessStories.getModifyTime() == null) {
            salesSuccessStories.setModifyTime(new Date());
        }

        initReferInfo(salesSuccessStories);

        SalesSuccessStoriesDO data = SalesSuccessStoriesConverter.vo2DO(salesSuccessStories);
        //逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        int n = salesSuccessStoriesDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建SalesSuccessStories失败");
        AssertUtil.assertNotNull(data.getId(), "新建SalesSuccessStories返回id为空");
        salesSuccessStories.setId(data.getId());
        return salesSuccessStories;
    }

    @Override
    public void updateByIdSelective(SalesSuccessStoriesVO salesSuccessStories) {
        AssertUtil.assertNotNull(salesSuccessStories, ResultCode.PARAM_INVALID, "salesSuccessStories is null");
        AssertUtil.assertTrue(salesSuccessStories.getId() != null, ResultCode.PARAM_INVALID,
            "salesSuccessStories.id is null");

        initReferInfo(salesSuccessStories);
        //修改时间必须更新
        salesSuccessStories.setModifyTime(new Date());
        SalesSuccessStoriesDO data = SalesSuccessStoriesConverter.vo2DO(salesSuccessStories);
        //逻辑删除标过滤
        data.setDeleted(false);
        int n = salesSuccessStoriesDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新SalesSuccessStories失败，影响行数:" + n);
    }

    @Override
    public List<SalesSuccessStoriesVO> querySalesSuccessStoriesList(SalesSuccessStoriesQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        SalesSuccessStoriesExample example = SalesSuccessStoriesConverter.query2Example(query);

        List<SalesSuccessStoriesDO> list = salesSuccessStoriesDAO.selectByExample(example);
        return SalesSuccessStoriesConverter.doList2VOList(list);
    }

    @Override
    public Long querySalesSuccessStoriesCount(SalesSuccessStoriesQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        SalesSuccessStoriesExample example = SalesSuccessStoriesConverter.query2Example(query);
        long c = salesSuccessStoriesDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询销售成功案例
     */
    @Override
    public PageInfo<SalesSuccessStoriesVO> querySalesSuccessStoriesByPage(SalesSuccessStoriesQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<SalesSuccessStoriesVO> page = new PageInfo<>();

        if (StringUtils.isBlank(query.getOrderBy())) {
            query.setOrderBy("topped desc, id desc");
        }

        SalesSuccessStoriesExample example = SalesSuccessStoriesConverter.query2Example(query);
        long totalCount = salesSuccessStoriesDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<SalesSuccessStoriesDO> list = salesSuccessStoriesDAO.selectByExampleWithBLOBs(example);
        page.setList(SalesSuccessStoriesConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        if (CollectionUtils.isNotEmpty(page.getList())) {
            List<Integer> ids = page.getList().stream().map(SalesSuccessStoriesVO::getOperatorId).collect(
                Collectors.toList());
            List<UserVO> users = userService.batchQueryById(ids);
            if (CollectionUtils.isNotEmpty(users)) {
                page.getList().forEach(e -> users.stream().filter(
                        i -> e.getOperatorId() != null && e.getOperatorId().equals(i.getId())).findFirst()
                    .ifPresent(userVO -> e.setOperatorName(userVO.getNickName())));
            }
        }

        return page;
    }

    /**
     * 初始化关联信息
     */
    private void initReferInfo(SalesSuccessStoriesVO salesSuccessStories) {
        if (salesSuccessStories.getModelId() != null) {
            MaterialModelVO model = materialModelService.selectById(salesSuccessStories.getModelId());
            salesSuccessStories.setModelUrl(model.getShowImage());
            salesSuccessStories.setModelName(model.getName());
        }

        Integer operatorUserId = OperationContextHolder.getOperatorUserId();
        salesSuccessStories.setOperatorId(operatorUserId);

        if (salesSuccessStories.getCustomerId() != null) {
            UserVO user = userService.selectById(salesSuccessStories.getCustomerId());
            salesSuccessStories.setCustomerName(user.getNickName());
        }

        if (salesSuccessStories.getBatchId() != null) {
            CreativeBatchVO batch = creativeBatchService.selectById(salesSuccessStories.getBatchId());
            salesSuccessStories.setImageUrls(batch.getResultImages());
        }

    }
}
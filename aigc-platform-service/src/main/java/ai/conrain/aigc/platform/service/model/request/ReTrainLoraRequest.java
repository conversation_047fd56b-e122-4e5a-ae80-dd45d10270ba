package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotNull;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重新训练Lora统一请求对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReTrainLoraRequest extends ReTrainLoraParams {
    /**
     * lora model id
     */
    @NotNull
    private Integer id;

    /** 是否批量确认 */
    private boolean batchConfirm;
}
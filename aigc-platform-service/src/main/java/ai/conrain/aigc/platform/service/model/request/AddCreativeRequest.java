/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.service.enums.CameraAngleEnum;
import ai.conrain.aigc.platform.service.model.biz.ClothCollocationModel;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 新建创作请求
 *
 * <AUTHOR>
 * @version : AddCreativeRequest.java, v 0.1 2024/5/9 14:20 renxiao.wu Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddCreativeRequest extends TestSupportCreativeRequest implements CustomSceneCreativeRequest {
    private static final long serialVersionUID = 2255953201510411266L;
    /** loraId */
    private Integer loraId;

    /** 相关配置 */
    private Map<Integer, List<Integer>> configs;

    /** 图片数量，默认5张 */
    private int imageNum = 5;

    /** 图片比例 */
    private String proportion;

    /** 位置角度 */
    private String position = CameraAngleEnum.FRONT_VIEW.getCode();

    /** 身体部位类型 */
    private String bodyType = CameraAngleEnum.WHOLE_BODY.getCode();

    /** 是否全身照 */
    @Deprecated
    private boolean wholeBody = true;

    /** 服装搭配 */
    private ClothCollocationModel clothCollocation;

    /** 已翻译的服装搭配 */
    private ClothCollocationModel transClothCollocation;

    /** 服装颜色索引 */
    private Integer colorIndex;

    /** 服装颜色 */
    private String clothColor;

    // 是否开启防模糊Lora
    private Boolean enableAntiBlurLora = true;

    /** 是否开启新模型训练 */
    private Boolean enableNewModel = false;

    /** 用户自定义场景 */
    private String customScene;

    /** 扣除归属用户点数 */
    private boolean withoutDeduction = false;

    /** 自定义表情 */
    private String expression;

    /** 是否使用场景的配饰,Y/N */
    private String useSceneAccessories;

    /** 业务类型 */
    private String bizType;

    /** 参考图信息列表 */
    private List<ReferenceInfo> referenceInfoList;

    /** 姿势示例图信息 */
    private PoseSampleDiagramRequest poseSampleDiagram;

    /** 衣服类型（UPPER：上装  LOWER：下装） */
    private String clotheType;

    /** 衣服描述 */
    private String clothDesc;

    /** 抠图批次id */
    private Integer mattingId;

    /** 衣服图片（url 或 Base64） */
    private String clotheImage;

    /** 已经上传至 ComfyUI 的衣服图片地址 */
    private String uploadComfyUIClotheImage;

    /** 涂层图片（url 或 Base64） */
    private String maskImage;

    /** 参考图信息 */
    @Data
    public static class ReferenceInfo {

        /** 参考图图片 */
        private String imageUrl;

        /** 已经上传至 ComfyUI 的图片地址 */
        private String uploadComfyUIImageUrl;

        /** 参考图配置 */
        private JSONObject referenceConfig;

        /** 背景标签 */
        private String backTags;

        /** 背景扩展标签 */
        private String backExtTags;

        /** LoraId */
        private Integer loraId;

        /** lora地址 */
        private String loraPath;

        /** 原始参考图 */
        private String originalImageUrl;
    }
}

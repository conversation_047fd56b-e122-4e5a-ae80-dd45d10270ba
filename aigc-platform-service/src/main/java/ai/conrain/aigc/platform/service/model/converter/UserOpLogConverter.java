package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.UserOpLogDO;
import ai.conrain.aigc.platform.service.model.query.UserOpLogQuery;
import ai.conrain.aigc.platform.dal.example.UserOpLogExample;
import ai.conrain.aigc.platform.service.model.vo.UserOpLogVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * UserOpLogConverter
 *
 * @version UserOpLogService.java v 0.1 2024-01-25 09:31:00
 */
public class UserOpLogConverter {

    /**
     * DO -> VO
     */
    public static UserOpLogVO do2VO(UserOpLogDO from) {
        UserOpLogVO to = new UserOpLogVO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setRoleType(from.getRoleType());
        to.setOpType(from.getOpType());
        to.setOpBizType(from.getOpBizType());
        to.setOpBizNo(from.getOpBizNo());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetailBefore(from.getDetailBefore());
        to.setDetailAfter(from.getDetailAfter());

        return to;
    }

    /**
     * VO -> DO
     */
    public static UserOpLogDO vo2DO(UserOpLogVO from) {
        UserOpLogDO to = new UserOpLogDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setRoleType(from.getRoleType());
        to.setOpType(from.getOpType());
        to.setOpBizType(from.getOpBizType());
        to.setOpBizNo(from.getOpBizNo());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetailBefore(from.getDetailBefore());
        to.setDetailAfter(from.getDetailAfter());

        return to;
    }

    /**
     * DO -> Query
     */
    public static UserOpLogQuery do2Query(UserOpLogDO from) {
        UserOpLogQuery to = new UserOpLogQuery();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setRoleType(from.getRoleType());
        to.setOpType(from.getOpType());
        to.setOpBizType(from.getOpBizType());
        to.setOpBizNo(from.getOpBizNo());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetailBefore(from.getDetailBefore());
        to.setDetailAfter(from.getDetailAfter());

        return to;
    }

    /**
     * Query -> DO
     */
    public static UserOpLogDO query2DO(UserOpLogQuery from) {
        UserOpLogDO to = new UserOpLogDO();
        to.setId(from.getId());
        to.setMasterUserId(from.getMasterUserId());
        to.setOperatorUserId(from.getOperatorUserId());
        to.setRoleType(from.getRoleType());
        to.setOpType(from.getOpType());
        to.setOpBizType(from.getOpBizType());
        to.setOpBizNo(from.getOpBizNo());
        to.setMemo(from.getMemo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setDetailBefore(from.getDetailBefore());
        to.setDetailAfter(from.getDetailAfter());

        return to;
    }


    /**
     * Query -> Example
     */
    public static UserOpLogExample query2Example(UserOpLogQuery from) {
        UserOpLogExample to = new UserOpLogExample();
        UserOpLogExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getMasterUserId())) {
            c.andMasterUserIdEqualTo(from.getMasterUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorUserId())) {
            c.andOperatorUserIdEqualTo(from.getOperatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getRoleType())) {
            c.andRoleTypeEqualTo(from.getRoleType());
        }
        if (!ObjectUtils.isEmpty(from.getOpType())) {
            c.andOpTypeEqualTo(from.getOpType());
        }
        if (!ObjectUtils.isEmpty(from.getOpBizType())) {
            c.andOpBizTypeEqualTo(from.getOpBizType());
        }
        if (!ObjectUtils.isEmpty(from.getOpBizNo())) {
            c.andOpBizNoEqualTo(from.getOpBizNo());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<UserOpLogVO> doList2VOList(List<UserOpLogDO> list) {
        return CommonUtil.listConverter(list, UserOpLogConverter::do2VO);
    }
}
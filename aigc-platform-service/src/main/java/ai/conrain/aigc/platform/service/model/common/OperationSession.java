package ai.conrain.aigc.platform.service.model.common;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import ai.conrain.aigc.platform.service.model.vo.UserVO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 操作会话
 */
@Data
public class OperationSession implements Serializable {

    private static final long serialVersionUID = -1236767914171069378L;

    /**
     * session id
     */
    private String sessionId;

    /** 登录用户，登录才非空，没登录则为null */
    private UserVO loginUser;

    /** 业务上下文 */
    private Map<String, String> attributes;

    /** 上一次检查权限时间 */
    private long lastRefreshUserTime = 0L;

    /** 是否有变更 */
    private boolean changed;

    public void setAttr(String key, String val) {
        if (key == null || val == null) {
            throw new NullPointerException("setAttr error, empty params not allowed!");
        }

        if (attributes == null) {
            attributes = new HashMap<>();
        }

        changed = true;
        attributes.put(key, val);
    }

    public String getAttr(String key) {
        if (key == null) {
            throw new NullPointerException("getAttr error, empty key not allowed!");
        }
        if (attributes == null) {
            return null;
        }
        return attributes.get(key);
    }

    public void removeAttr(String key) {
        if (StringUtils.isBlank(key)) {
            throw new NullPointerException("removeAttr error, empty key not allowed!");
        }
        if (attributes != null) {
            attributes.remove(key);
        }
    }

    /**
     * 设置登录用户
     *
     * @param usersVO 用户信息
     */
    public void setLoginUser(UserVO usersVO) {
        changed = true;
        this.loginUser = usersVO;
    }
}

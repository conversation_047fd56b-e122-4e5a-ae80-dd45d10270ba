package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.model.request.TryonCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * try on
 */
@Service
public class TryonCreativeService extends AbstractCreativeService<TryonCreativeRequest> {

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.TRYON;
    }

    @Override
    protected CreativeBatchVO buildData(TryonCreativeRequest request, MaterialModelVO modelVO) throws IOException {

        CreativeBatchVO batch = new CreativeBatchVO();
        batch.setType(CreativeTypeEnum.TRYON);
        batch.setUserId(OperationContextHolder.getMasterUserId());
        batch.setShowImage(request.getPersonImgUrl());

        if (StringUtils.isNotBlank(request.getImgNum())) {
            batch.setBatchCnt(Integer.valueOf(request.getImgNum()));
        } else {
            batch.setBatchCnt(1);
        }
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        batch.setExtInfo(CommonUtil.java2JSONObject(request));
        batch.addExtInfo(CommonConstants.tryonPersonImgName, "/home/<USER>/aigc/ComfyUI/input/" + CommonUtil.getFileNameFromUrl(request.getPersonImgUrl()));
        //默认按参考图片比例
        batch.setImageProportion("NONE");

        //todo 当前tryon能力上只支持输入一张图，无法同时支持上装和下装图片，因此这里只选一张图，后面能力补全后，需要修改此处代码
        if (StringUtils.isNotBlank(request.getTopUrl())) {
            batch.addExtInfo(CommonConstants.tryonGarmentImgName,"/home/<USER>/aigc/ComfyUI/input/" +  CommonUtil.getFileNameFromUrl(request.getTopUrl()));
        } else if (StringUtils.isNotBlank(request.getBottomUrl())) {
            batch.addExtInfo(CommonConstants.tryonGarmentImgName, "/home/<USER>/aigc/ComfyUI/input/" + CommonUtil.getFileNameFromUrl(request.getBottomUrl()));
        }

        return batch;
    }

    @Override
    protected String getFlowKey(List<CreativeElementVO> elements, MaterialModelVO modelVO, CreativeTaskVO task,
                                Map<String, Object> context) {
        return SystemConstants.TRYON_COMFYUI_WORKFLOW;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        JSONObject ext = new JSONObject();
        if (batch.getExtInfo() != null) {
            ext.putAll(batch.getExtInfo());
        }
        if (target.getExtInfo() != null) {
            ext.putAll(target.getExtInfo());
        }
        target.setExtInfo(ext);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO, Map<String, Object> context) {
        context.put(CommonConstants.tryonPersonImgName, task.getStringFromExtInfo(CommonConstants.tryonPersonImgName));
        context.put(CommonConstants.tryonGarmentImgName, task.getStringFromExtInfo(CommonConstants.tryonGarmentImgName));
        context.put(CommonConstants.cutoutKeyword, task.getStringFromExtInfo(CommonConstants.cutoutKeyword));
    }
}

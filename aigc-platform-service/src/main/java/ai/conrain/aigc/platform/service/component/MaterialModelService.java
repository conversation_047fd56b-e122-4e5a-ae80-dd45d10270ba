package ai.conrain.aigc.platform.service.component;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.dal.entity.StatsQueuedModelDO;
import ai.conrain.aigc.platform.service.model.biz.LoraTrainDetail;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import ai.conrain.aigc.platform.service.model.request.ConfirmLoraReq;
import ai.conrain.aigc.platform.service.model.request.ReTrainLoraRequest;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.LabelFileEditReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelTrainDetailVO;
import ai.conrain.aigc.platform.service.model.vo.SyncImageCaseReq;
import java.io.IOException;
import java.util.List;

/**
 * 素材模型 Service定义
 *
 * <AUTHOR>
 * @version MaterialModelService.java v 0.1 2024-05-09 06:10:02
 */
public interface MaterialModelService {

    /**
     * 查询素材模型对象
     *
     * @param id 主键
     * @return 返回结果
     */
    MaterialModelVO selectById(Integer id);

    //锁住数据
    MaterialModelVO lockById(Integer id);

    /**
     * 克隆lora模型
     *
     * @param id            id
     * @param cloneLoraName 克隆后的服装名称，可空
     * @param extInfo       扩展信息
     * @param fullCopy      完整复制
     * @param userId        用户id
     * @return 返回结果
     */
    MaterialModelVO cloneLora(Integer id, String cloneLoraName, JSONObject extInfo, boolean fullCopy, Integer userId);

    /**
     * copy到体验服装
     *
     * @param modelId 模型id
     * @return 新的模型结果
     */
    MaterialModelVO copyToSystem(Integer modelId);

    /**
     * 删除素材模型对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加素材模型对象
     *
     * @param materialModel 对象参数
     * @return 返回结果
     */
    MaterialModelVO insert(MaterialModelVO materialModel);

    /**
     * 修改素材模型对象
     *
     * @param materialModel 对象参数
     */
    void updateByIdSelective(MaterialModelVO materialModel);

    /**
     * 修改素材模型对象拓展信息
     *
     * @param materialModel 对象参数
     */
    void updateExtInfoByIdSelective(MaterialModelVO materialModel);

    /**
     * 修改素材模型对象（内部方法）
     *
     * @param materialModel 对象参数
     */
    void innerUpdate(MaterialModelVO materialModel);

    /**
     * 带条件批量查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<MaterialModelVO> queryMaterialModelList(MaterialModelQuery query);

    /**
     * 带条件批量查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<MaterialModelVO> queryListWithBlogs(MaterialModelQuery query);

    /**
     * 带条件批量分页查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    PageInfo<MaterialModelVO> queryMetaInfoByPage(MaterialModelQuery query);

    /**
     * 带条件批量分页查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    PageInfo<MaterialModelVO> queryPageWithBlobs(MaterialModelQuery query);

    /**
     * 带条件批量分页查询素材模型列表
     *
     * @param query 查询条件
     *              return 结果
     */
    PageInfo<MaterialModelVO> queryPageWithBlobs4DistributorModels(MaterialModelQuery query);

    /**
     * 带条件查询素材模型数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryMaterialModelCount(MaterialModelQuery query);

    /**
     * 带条件分页查询素材模型
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<MaterialModelVO> queryMaterialModelByPage(MaterialModelQuery query);

    void pollingLoraStatus(Integer modelId);

    /**
     * 轮询主模型状态
     *
     * @param modelId 模型id
     */
    void pollingMainStatus(Integer modelId);

    /**
     * 更新服装模型扩展信息
     *
     * @param req 请求入参
     */
    void updateExtInfo(MaterialModelReq req);

    /**
     * 同步服装模型到图库中
     *
     * @param req 请求入参
     */
    void syncToImageCase(SyncImageCaseReq req);

    /**
     * 将lora模型指定给某个商家
     *
     * @param modelId 模型id
     * @param userId  用户id
     */
    void assignTo(Integer modelId, Integer userId);

    /**
     * 将模特lora或场景lora模型，指定给某个商家
     */
    void assignElementModelToUser(Integer modelId, Integer userId, boolean exclusive, boolean free);

    void startCreateTestImages(LoraTrainDetail detail, MaterialModelVO target, boolean forceSend) throws IOException;

    /**
     * 修改素材模型示例图片
     *
     * @param materialModel 模型
     */
    void changeExampleImages(MaterialModelVO materialModel);

    /**
     * 清空示例图片
     *
     * @param modelId 模型id
     */
    void clearExampleImages(Integer modelId);

    /**
     * 批量修改服装类型
     *
     * @param idList id列表
     */
    void batchModifyGarment(List<Integer> idList);

    /**
     * 更新多色图片
     *
     * @param id     模型id
     * @param index  索引
     * @param imgUrl 图片url
     */
    void updateColorImage(Integer id, Integer index, String imgUrl);

    /**
     * 设置颜色是否可用
     *
     * @param id     模型id
     *               * @param index  索引
     * @param enable 是否可用
     */
    void enableColor(Integer id, Integer index, Boolean enable);

    /**
     * 确认训练
     *
     * @param req          请求
     * @param operatorId   操作用户id
     * @param operatorNick 操作用户昵称
     */
    void confirmTrainLora(ConfirmLoraReq req, Integer operatorId, String operatorNick);

    /**
     * 确认可交付
     *
     * @param loraId
     * @param operatorId
     * @param operatorNick
     */
    void confirmCanDeliver(Integer loraId, Integer operatorId, String operatorNick);

    /**
     * 补充颜色
     *
     * @param ids id列表
     */
    void supplyColor(List<Integer> ids);

    /**
     * 批量上传lora到oss
     *
     * @param ids id列表
     */
    void batchUploadOss(List<Integer> ids);

    /**
     * 批量自动生成图片
     *
     * @param ids id列表
     */
    void batchCreateTestImages(List<Integer> ids);

    /**
     * 重新训练Lora
     */
    void retrainLora(ReTrainLoraRequest request);

    /**
     * 获取训练明细信息
     *
     * @param id id
     * @return 训练详情
     */
    ModelTrainDetailVO getTrainDetail(Integer id);

    /**
     * 更新打标文件
     *
     * @param req 请求
     */
    void updateLabelFiles(LabelFileEditReq req);

    /**
     * 添加演示标记，如果已经有标记，则取消
     *
     * @param id id
     */
    void addDemoTag(Integer id);

    /**
     * 初始化关联的元素配置
     *
     * @param modelId 模型id
     * @return 初始化结果
     */
    CreativeElementVO initRelatedElementConfig(Integer modelId);

    /**
     * 同步测试图片生成状态
     *
     * @param model 模型
     */
    void syncTestImageStatus(MaterialModelVO model);

    /**
     * 交付素材模型
     *
     * @param model 模型
     */
    void deliver(MaterialModelVO model);

    /**
     * 更新实验标记
     *
     * @param id           id
     * @param experimental 实验标记
     */
    void updateExperimental(Integer id, boolean experimental);

    /**
     * 根据模型id获取详情展示图片
     *
     * @param modelId 模型id
     * @return 图片url
     */
    String queryDetailShowImage(Integer modelId);

    /**
     * 根据模型id获取详情展示图片
     *
     * @param modelId 模型id
     * @return 图片url
     */
    List<String> queryDetailShowImages(Integer modelId);

    /**
     * 重新初始化训练后的模型
     *
     * @param modelId 模型id
     */
    void reInitTrainedModel(int modelId);

    /**
     * 根据交付日期查询素材模型
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 素材模型列表
     */
    List<MaterialModelVO> queryMaterialModelByDeliveryDate(String startDate, String endDate);

    /**
     * 查询所有子模型id列表
     *
     * @param id 主模型id
     * @return 子模型id列表
     */
    List<Integer> querySubIds(Integer id);

    /**
     * 查询关联的子模型列表
     *
     * @param modelId 主模型id
     * @return 子模型
     */
    List<MaterialModelVO> querySubModel(Integer modelId);

    /**
     * 统计等待队列中的模型数量
     *
     * @return 统计结果
     */
    StatsQueuedModelDO statsQueuedModel();

    /**
     * 根据lora地址查询模型
     *
     * @param loraName lora地址
     * @return 模型
     */
    MaterialModelVO queryByLoraName(String loraName);

    /**
     * 自动生成图片
     *
     * @param id id
     */
    void autoCreateImages(int id);

    /**
     * 同步训练文件夹
     *
     * @param id 模型id
     */
    void syncTrainFolder(int id);

    /**
     * 批量自动交付
     *
     * @param id 模型id
     */
    void batchAutoDelivery(int id);

    /**
     * 批量查询
     *
     * @param ids 模型id列表
     * @return 模型列表
     */
    List<MaterialModelVO> batchQueryByIds(List<Integer> ids);

    /**
     * 添加问题标记
     *
     * @param id    模型id
     * @param tagId 标记id
     */
    void addProblemTag(Integer id, Integer tagId);

    /**
     * 创建自动生成图片任务
     *
     * @param loraModel       模型
     * @param autoGenImgParam 参数
     * @return 任务
     */
    CommonTaskVO createAutoGemImgTask(MaterialModelVO loraModel, AutoGenImgParam autoGenImgParam);

    /**
     * 强制交付
     *
     * @param id id
     */
    void forceToDelivery(Integer id);

    /**
     * 修改使用场景
     *
     * @param id        id
     * @param usageType 使用场景
     * @param usageMemo 使用场景描述
     */
    void modifyUsage(Integer id, String usageType, String usageMemo);

    /**
     * 查询常用loradir
     *
     * @return 常用loradir
     */
    List<String> queryCommonlyUsedLoraDir();

}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.CodeDO;
import ai.conrain.aigc.platform.service.model.query.CodeQuery;
import ai.conrain.aigc.platform.service.model.vo.CodeVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 码 Service定义
 *
 * <AUTHOR>
 * @version CodeService.java v 0.1 2025-05-19 02:24:41
 */
public interface CodeService {
	
	/**
	 * 查询码对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	CodeVO selectById(Integer id);

	/**
	 * 删除码对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加码对象
	 * @param code 对象参数
	 * @return 返回结果
	 */
	CodeVO insert(CodeVO code);

	//邀请注册码
	CodeVO createRegisterPromotionCode(Integer relatedUserId, String externalNick);

	/**
	 * 修改码对象
	 * @param code 对象参数
	 */
	void updateByIdSelective(CodeVO code);

	/**
	 * 带条件批量查询码列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<CodeVO> queryCodeList(CodeQuery query);

	/**
	 * 带条件查询码数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryCodeCount(CodeQuery query);

	/**
	 * 带条件分页查询码
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<CodeVO> queryCodeByPage(CodeQuery query);
}
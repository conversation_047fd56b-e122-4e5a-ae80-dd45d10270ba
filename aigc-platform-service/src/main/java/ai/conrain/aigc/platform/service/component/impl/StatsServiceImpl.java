package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.entity.StatsUserPointDO;
import ai.conrain.aigc.platform.service.component.StatsService;
import ai.conrain.aigc.platform.service.component.StatsUserPointService;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.model.converter.StatsUserPointConverter;
import ai.conrain.aigc.platform.service.util.strategy.StatsPeriodStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 统计服务实现类 - 提供统计相关操作
 */
@Slf4j
@Service
public class StatsServiceImpl implements StatsService {

    @Autowired
    private StatsPeriodStrategy dailyStatsStrategy;

    @Autowired
    private StatsPeriodStrategy weeklyStatsStrategy;

    @Autowired
    private StatsPeriodStrategy monthlyStatsStrategy;

    @Autowired
    private StatsPeriodStrategy totalStatsStrategy;

    @Autowired
    private StatsUserPointService statsUserPointService;

    /** 用于单个用户的统计生成 */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int generateStatsForPeriod(Date date, StatsPeriodStrategy strategy) {
        String statsDateStr = strategy.getStatsDateStr(date);
        log.info("【点数消耗统计定时任务】开始生成{}的统计数据: {}", strategy.getPeriodType().getDesc(), statsDateStr);

        // 是否从已有的日统计聚合
        if (strategy.generateFromExisting()) {

            List<StatsUserPointDO> existingStats = strategy.getExistingStats(statsDateStr);

            if (existingStats.isEmpty()) {
                log.info("【点数消耗统计定时任务】没有找到{}的日统计数据，跳过生成统计", statsDateStr);
                return 0;
            }

            // 按用户ID分组
            Map<Integer, List<StatsUserPointDO>> userStatsMap = new HashMap<>();
            for (StatsUserPointDO stat : existingStats) {
                userStatsMap.computeIfAbsent(stat.getUserId(), k -> new ArrayList<>()).add(stat);
            }

            List<StatsUserPointDO> periodStatsList = new ArrayList<>();

            // 为每个用户聚合统计
            for (Map.Entry<Integer, List<StatsUserPointDO>> entry : userStatsMap.entrySet()) {
                Integer userId = entry.getKey();
                List<StatsUserPointDO> userStats = entry.getValue();

                if (!userStats.isEmpty()) {
                    // 创建聚合对象
                    StatsUserPointDO periodStat = aggregateStats(userStats);
                    periodStat.setUserId(userId);
                    periodStat.setStatsType(strategy.getPeriodType().getCode());
                    periodStat.setStatsDate(statsDateStr);
                    periodStat.setCreateTime(new Date());
                    periodStat.setModifyTime(new Date());

                    periodStatsList.add(periodStat);
                }
            }

            // 批量保存
            if (!periodStatsList.isEmpty()) {
                return statsUserPointService.batchInsertOrUpdateStats(periodStatsList);
            }
        } else {
            // 对于日统计和总计统计，直接查询用户消费记录
            List<Integer> userIds = statsUserPointService.findUsersWithPointConsumption(strategy.getStartDate(date), strategy.getEndDate(date));

            if (userIds.isEmpty()) {
                return 0;
            }

            int batchSize = 100;
            int processedUsers = 0;

            for (int i = 0; i < userIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, userIds.size());
                List<Integer> batchUserIds = userIds.subList(i, endIndex);

                // 批量获取用户点数消费数据
                List<StatsUserPointDO> batchConsumptions = statsUserPointService.getUsersPointConsumptionBatch(
                        batchUserIds, strategy.getStartDate(date), strategy.getEndDate(date));
                if (CollectionUtils.isNotEmpty(batchConsumptions)) {
                    batchConsumptions.forEach(consumption -> {
                        consumption.setStatsType(strategy.getPeriodType().getCode());
                        consumption.setStatsDate(statsDateStr);
                        consumption.setCreateTime(new Date());
                        consumption.setModifyTime(new Date());
                    });
                    statsUserPointService.batchInsertOrUpdateStats(batchConsumptions);
                    processedUsers += batchUserIds.size();
                }
                log.info("【点数消耗统计定时任务】已处理{}/{}个用户的{}统计数据", processedUsers, userIds.size(), strategy.getPeriodType().getDesc());
            }
            return processedUsers;
        }
        return 0;
    }

    // 模板方法 - 用于所有用户汇总的统计生成
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int generateAllUserStatsForPeriod(Date date, StatsPeriodStrategy strategy) {
        String statsDateStr = strategy.getStatsDateStr(date);
        log.info("【点数消耗统计定时任务】开始生成{}的所有用户汇总统计数据: {}",
                strategy.getPeriodType().getDesc(), statsDateStr);

        StatsUserPointDO stats = null;

        if (strategy.generateFromExisting()) {
            // 对于周期型统计，从日汇总统计聚合
            List<StatsUserPointDO> existingStats = strategy.getAllUserExistingStats(statsDateStr);

            if (existingStats == null || existingStats.isEmpty()) {
                log.info("【点数消耗统计定时任务】没有找到{}的日汇总统计数据，跳过生成汇总统计", statsDateStr);
                return 0;
            }

            // 聚合为单个统计记录
            stats = aggregateStats(existingStats);
        } else {
            // 对于日统计和总计统计，直接查询所有用户消费记录
            stats = statsUserPointService.getAllUserPointConsumption( strategy.getStartDate(date), strategy.getEndDate(date));

            if (Objects.isNull(stats) || !StatsUserPointConverter.checkStatsFieldsNotEmpty(stats)) {
                log.info("【点数消耗统计定时任务】没有找到任何用户消费记录，跳过生成汇总统计");
                return 0;
            }
        }

        stats.setUserId(0); // 使用0表示所有用户汇总
        stats.setStatsType(strategy.getPeriodType().getCode());
        stats.setStatsDate(statsDateStr);
        stats.setCreateTime(new Date());
        stats.setModifyTime(new Date());

        // 保存统计数据
        List<StatsUserPointDO> statsList = new ArrayList<>();
        statsList.add(stats);
        return statsUserPointService.batchInsertOrUpdateStats(statsList);
    }

    // 辅助方法 - 聚合多个统计记录
    private StatsUserPointDO aggregateStats(List<StatsUserPointDO> statsList) {
        StatsUserPointDO aggregated = new StatsUserPointDO();

        int pointConsumed = 0;
        int givePointConsumed = 0;
        int expPointConsumed = 0;
        int modelPointConsumed = 0;
        BigDecimal rechargeAmount = BigDecimal.ZERO;

        for (StatsUserPointDO stat : statsList) {
            pointConsumed += (stat.getPointConsumed() != null ? stat.getPointConsumed() : 0);
            givePointConsumed += (stat.getGivePointConsumed() != null ? stat.getGivePointConsumed() : 0);
            expPointConsumed += (stat.getExpPointConsumed() != null ? stat.getExpPointConsumed() : 0);
            modelPointConsumed += (stat.getModelPointConsumed() != null ? stat.getModelPointConsumed() : 0);
            if (stat.getRechargeAmount() != null) {
                rechargeAmount = rechargeAmount.add(stat.getRechargeAmount());
            }
        }

        aggregated.setPointConsumed(pointConsumed);
        aggregated.setGivePointConsumed(givePointConsumed);
        aggregated.setExpPointConsumed(expPointConsumed);
        aggregated.setModelPointConsumed(modelPointConsumed);
        aggregated.setRechargeAmount(rechargeAmount);

        return aggregated;
    }

    @Override
    @Transactional
    public int generateStatsForPeriod(Date date, StatsPeriodEnum period) {

        switch (period) {
            case DAILY:
                return generateStatsForPeriod(date, dailyStatsStrategy);
            case WEEKLY:
                return generateStatsForPeriod(date, weeklyStatsStrategy);
            case MONTHLY:
                return generateStatsForPeriod(date, monthlyStatsStrategy);
            case TOTAL:
                return generateStatsForPeriod(date, totalStatsStrategy);
            default:
                return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int generateAllUserStatsForPeriod(Date date, StatsPeriodEnum period) {
        switch (period) {
            case DAILY:
                return generateAllUserStatsForPeriod(date, dailyStatsStrategy);
            case WEEKLY:
                return generateAllUserStatsForPeriod(date, weeklyStatsStrategy);
            case MONTHLY:
                return generateAllUserStatsForPeriod(date, monthlyStatsStrategy);
            case TOTAL:
                return generateAllUserStatsForPeriod(date, totalStatsStrategy);
            default:
                return 0;
        }
    }
}

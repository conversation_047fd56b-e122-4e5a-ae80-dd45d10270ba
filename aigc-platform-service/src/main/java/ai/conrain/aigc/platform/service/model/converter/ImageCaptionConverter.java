package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * ImageCaptionConverter
 *
 * @version ImageCaptionService.java v 0.1 2025-08-06 06:04:01
 */
public class ImageCaptionConverter {

    /**
     * DO -> VO
     */
    public static ImageCaptionVO do2VO(ImageCaptionDO from) {
        ImageCaptionVO to = new ImageCaptionVO();
        to.setId(from.getId());
        to.setImageId(from.getImageId());
        to.setCaption(CommonUtil.parseObject(from.getCaption(), ImageAnalysisCaption.class));
        to.setPreCaption(CommonUtil.parseObject(from.getPreCaption(), ImageSimpleCaption.class));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setImgEmb(from.getImgEmb());
        to.setBgImgEmb(from.getBgImgEmb());
        to.setModelFacialImgEmb(from.getModelFacialImgEmb());
        to.setModelPoseImgEmb(from.getModelPoseImgEmb());
        to.setClothStyleTextEmb(from.getClothStyleTextEmb());
        to.setClothTextEmb(from.getClothTextEmb());
        to.setBgTextEmb(from.getBgTextEmb());
        to.setAccessoriesTextEmb(from.getAccessoriesTextEmb());
        to.setHairstyleTextEmb(from.getHairstyleTextEmb());
        to.setPoseTextEmb(from.getPoseTextEmb());
        to.setSortBgTextEmb(from.getSortBgTextEmb());
        to.setSortFacialExpressionTextEmb(from.getSortFacialExpressionTextEmb());
        to.setSortAccessoriesTextEmb(from.getSortAccessoriesTextEmb());
        to.setSortPoseTextEmb(from.getSortPoseTextEmb());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setImageType(from.getImageType());
        to.setClothGenderType(from.getClothGenderType());
        to.setAgeGroup(from.getAgeGroup());
        to.setQualityScore(from.getQualityScore());
        to.setGenre(from.getGenre());

        to.setClothStyleSimilarity(from.getClothStyleSimilarity());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageCaptionDO vo2DO(ImageCaptionVO from) {
        ImageCaptionDO to = new ImageCaptionDO();
        to.setId(from.getId());
        to.setImageId(from.getImageId());
        to.setCaption(CommonUtil.toJSONString(from.getCaption()));
        to.setPreCaption(CommonUtil.toJSONString(from.getPreCaption()));
        to.setCaptionVersion(from.getCaptionVersion());
        to.setImgEmb(from.getImgEmb());
        to.setBgImgEmb(from.getBgImgEmb());
        to.setModelFacialImgEmb(from.getModelFacialImgEmb());
        to.setModelPoseImgEmb(from.getModelPoseImgEmb());
        to.setClothStyleTextEmb(from.getClothStyleTextEmb());
        to.setClothTextEmb(from.getClothTextEmb());
        to.setBgTextEmb(from.getBgTextEmb());
        to.setAccessoriesTextEmb(from.getAccessoriesTextEmb());
        to.setHairstyleTextEmb(from.getHairstyleTextEmb());
        to.setPoseTextEmb(from.getPoseTextEmb());
        to.setSortBgTextEmb(from.getSortBgTextEmb());
        to.setSortFacialExpressionTextEmb(from.getSortFacialExpressionTextEmb());
        to.setSortAccessoriesTextEmb(from.getSortAccessoriesTextEmb());
        to.setSortPoseTextEmb(from.getSortPoseTextEmb());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setImageType(from.getImageType());
        to.setClothGenderType(from.getClothGenderType());
        to.setAgeGroup(from.getAgeGroup());
        to.setGenre(from.getGenre());
        to.setQualityScore(from.getQualityScore());

        return to;
    }

    /**
     * Query -> Example
     */
    public static ImageCaptionExample query2Example(ImageCaptionQuery from) {
        ImageCaptionExample to = new ImageCaptionExample();
        ImageCaptionExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getImageId())) {
            c.andImageIdEqualTo(from.getImageId());
        }
        if (!ObjectUtils.isEmpty(from.getCaption())) {
            c.andCaptionEqualTo(from.getCaption());
        }
        if (!ObjectUtils.isEmpty(from.getCaptionVersion())) {
            c.andCaptionVersionEqualTo(from.getCaptionVersion());
        }
        if (!ObjectUtils.isEmpty(from.getImgEmb())) {
            c.andImgEmbEqualTo(from.getImgEmb());
        }
        if (!ObjectUtils.isEmpty(from.getBgImgEmb())) {
            c.andBgImgEmbEqualTo(from.getBgImgEmb());
        }
        if (!ObjectUtils.isEmpty(from.getModelFacialImgEmb())) {
            c.andModelFacialImgEmbEqualTo(from.getModelFacialImgEmb());
        }
        if (!ObjectUtils.isEmpty(from.getModelPoseImgEmb())) {
            c.andModelPoseImgEmbEqualTo(from.getModelPoseImgEmb());
        }
        if (!ObjectUtils.isEmpty(from.getClothStyleTextEmb())) {
            c.andClothStyleTextEmbEqualTo(from.getClothStyleTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getClothTextEmb())) {
            c.andClothTextEmbEqualTo(from.getClothTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getBgTextEmb())) {
            c.andBgTextEmbEqualTo(from.getBgTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getAccessoriesTextEmb())) {
            c.andAccessoriesTextEmbEqualTo(from.getAccessoriesTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getHairstyleTextEmb())) {
            c.andHairstyleTextEmbEqualTo(from.getHairstyleTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getPoseTextEmb())) {
            c.andPoseTextEmbEqualTo(from.getPoseTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getSortBgTextEmb())) {
            c.andSortBgTextEmbEqualTo(from.getSortBgTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getSortFacialExpressionTextEmb())) {
            c.andSortFacialExpressionTextEmbEqualTo(from.getSortFacialExpressionTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getSortAccessoriesTextEmb())) {
            c.andSortAccessoriesTextEmbEqualTo(from.getSortAccessoriesTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getSortPoseTextEmb())) {
            c.andSortPoseTextEmbEqualTo(from.getSortPoseTextEmb());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getImageType())) {
            c.andImageTypeEqualTo(from.getImageType());
        }
        if (!ObjectUtils.isEmpty(from.getClothGenderType())) {
            c.andClothGenderTypeEqualTo(from.getClothGenderType());
        }
        if (!ObjectUtils.isEmpty(from.getGenre())) {
            c.andGenreEqualTo(from.getGenre());
        }
        if (!ObjectUtils.isEmpty(from.getAgeGroup())) {
            c.andAgeGroupEqualTo(from.getAgeGroup());
        }
        //逻辑删除过滤
        for (ImageCaptionExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<ImageCaptionVO> doList2VOList(List<ImageCaptionDO> list) {
        return CommonUtil.listConverter(list, ImageCaptionConverter::do2VO);
    }
}
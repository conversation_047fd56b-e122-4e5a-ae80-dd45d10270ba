package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TrainParamDO;
import ai.conrain.aigc.platform.service.model.query.TrainParamQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainParamVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * TrainParamConverter
 *
 * @version TrainParamService.java v 0.1 2024-11-19 08:34:41
 */
public class TrainParamConverter {

    /**
     * DO -> VO
     */
    public static TrainParamVO do2VO(TrainParamDO from) {
        TrainParamVO to = new TrainParamVO();
        to.setId(from.getId());
        to.setTrainPlanId(from.getTrainPlanId());
        to.setTrainResolution(from.getTrainResolution());
        to.setContentOrStyle(from.getContentOrStyle());
        to.setRelatedLoraModelId(from.getRelatedLoraModelId());
        to.setRelatedLoraModelName(from.getRelatedLoraModelName());
        to.setLoraRank(from.getLoraRank());
        to.setAlpha(from.getAlpha());
        to.setTrainStep(from.getTrainStep());
        to.setLr(from.getLr());
        to.setDropout(from.getDropout());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TrainParamDO vo2DO(TrainParamVO from) {
        TrainParamDO to = new TrainParamDO();
        to.setId(from.getId());
        to.setTrainPlanId(from.getTrainPlanId());
        to.setTrainResolution(from.getTrainResolution());
        to.setContentOrStyle(from.getContentOrStyle());
        to.setRelatedLoraModelId(from.getRelatedLoraModelId());
        to.setRelatedLoraModelName(from.getRelatedLoraModelName());
        to.setLoraRank(from.getLoraRank());
        to.setAlpha(from.getAlpha());
        to.setTrainStep(from.getTrainStep());
        to.setLr(from.getLr());
        to.setDropout(from.getDropout());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static TrainParamQuery do2Query(TrainParamDO from) {
        TrainParamQuery to = new TrainParamQuery();
        to.setId(from.getId());
        to.setTrainPlanId(from.getTrainPlanId());
        to.setTrainResolution(from.getTrainResolution());
        to.setContentOrStyle(from.getContentOrStyle());
        to.setRelatedLoraModelId(from.getRelatedLoraModelId());
        to.setRelatedLoraModelName(from.getRelatedLoraModelName());
        to.setLoraRank(from.getLoraRank());
        to.setAlpha(from.getAlpha());
        to.setTrainStep(from.getTrainStep());
        to.setLr(from.getLr());
        to.setDropout(from.getDropout());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TrainParamDO query2DO(TrainParamQuery from) {
        TrainParamDO to = new TrainParamDO();
        to.setId(from.getId());
        to.setTrainPlanId(from.getTrainPlanId());
        to.setTrainResolution(from.getTrainResolution());
        to.setContentOrStyle(from.getContentOrStyle());
        to.setRelatedLoraModelId(from.getRelatedLoraModelId());
        to.setRelatedLoraModelName(from.getRelatedLoraModelName());
        to.setLoraRank(from.getLoraRank());
        to.setAlpha(from.getAlpha());
        to.setTrainStep(from.getTrainStep());
        to.setLr(from.getLr());
        to.setDropout(from.getDropout());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * do list -> vo list
     */
    public static List<TrainParamVO> doList2VOList(List<TrainParamDO> list) {
        return CommonUtil.listConverter(list, TrainParamConverter::do2VO);
    }
}
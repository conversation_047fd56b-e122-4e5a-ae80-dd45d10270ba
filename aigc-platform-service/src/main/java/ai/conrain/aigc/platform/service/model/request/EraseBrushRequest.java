package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;

@Data
public class EraseBrushRequest implements CreativeRequest {
    private static final long serialVersionUID = 1L;

    Integer taskId;

    Integer modelId;

    @Positive
    Integer imageNum;

    // 原图
    @NotBlank
    String originImage;

    // 蒙版
    @NotBlank
    String maskImage;

    // TODO by祝余: 临时兼容
    String removeWrinkleType;
    Integer relatedRemoveWrinkleId;
}
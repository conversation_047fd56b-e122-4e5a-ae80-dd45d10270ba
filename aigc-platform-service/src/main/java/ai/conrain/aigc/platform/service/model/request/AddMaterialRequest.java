/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.model.biz.MaterialDetail;

/**
 * 素材上传请求接口
 *
 * <AUTHOR>
 * @version : AddMaterialRequest.java, v 0.1 2025/2/23 17:55 renxiao.wu Exp $
 */
public interface AddMaterialRequest {
    /** 模型名称 */
    String getName();

    /** 素材类型 */
    String getMaterialType();

    /** 素材子类型 */
    String getMaterialSubType();

    /** 素材详情 */
    MaterialDetail getMaterialDetail();

    //服装的男女款式描述（man/woman/child，默认值是woman，解决历史数据为空的情况）
    String getClothStyleType4TrainParam();
}
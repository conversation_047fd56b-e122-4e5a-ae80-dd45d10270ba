package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.query.CaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.CaptionUserVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.List;

/**
 * 用户基础信息 Service定义
 *
 * <AUTHOR>
 * @version CaptionUserService.java v 0.1 2025-08-12 12:55:30
 */
public interface CaptionUserService {

	/**
	 * 查询用户基础信息对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	CaptionUserVO selectById(Integer id);

	/**
	 * 查询用户基础信息对象
	 * @param   username 用户名
	 * @return 返回结果
	 */
	CaptionUserVO selectByUsername(String username);

	/**
	 * 删除用户基础信息对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加用户基础信息对象
	 * @param captionUser 对象参数
	 * @return 返回结果
	 */
	CaptionUserVO create(CaptionUserVO captionUser);

	/**
	 * 添加用户基础信息对象
	 * @param captionUser 对象参数
	 * @return 返回结果
	 */
	CaptionUserVO insert(CaptionUserVO captionUser);

	/**
	 * 修改用户基础信息对象
	 * @param captionUser 对象参数
	 */
	void updateByIdSelective(CaptionUserVO captionUser);

	/**
	 * 带条件批量查询用户基础信息列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<CaptionUserVO> queryCaptionUserList(CaptionUserQuery query);

	/**
	 * 带条件查询用户基础信息数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryCaptionUserCount(CaptionUserQuery query);

	/**
	 * 带条件分页查询用户基础信息
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<CaptionUserVO> queryCaptionUserByPage(CaptionUserQuery query);

    /**
     * 创建或获取用户
     *
     * @param username
     * @return
     */
    CaptionUserVO getOrCreateByUsername(String username);
}
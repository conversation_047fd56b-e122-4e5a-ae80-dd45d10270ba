package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.ai.imageAnalysis.ImageAnalysisService;
import ai.conrain.aigc.platform.integration.ai.imageAnalysis.SearchUtil;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.embedding.ComposedEmbeddingService;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageGroupService;
import ai.conrain.aigc.platform.service.component.ImageSearchService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.MaterialInfoService;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.SearchResultImgService;
import ai.conrain.aigc.platform.service.component.SearchTaskService;
import ai.conrain.aigc.platform.service.component.agent.ClusteringUtil;
import ai.conrain.aigc.platform.service.component.agent.DiversifyUtil;
import ai.conrain.aigc.platform.service.component.agent.PCAUtil;
import ai.conrain.aigc.platform.service.component.onnx.SortModelService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.constants.RecallRulesUtil;
import ai.conrain.aigc.platform.service.enums.AgeGroupEnum;
import ai.conrain.aigc.platform.service.enums.AgeRangeEnum;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.enums.SearchModeEnum;
import ai.conrain.aigc.platform.service.enums.SearchTaskStatusEnum;
import ai.conrain.aigc.platform.service.model.ClothRecallRules;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothImgItem;
import ai.conrain.aigc.platform.service.model.biz.agent.ClusterOptions;
import ai.conrain.aigc.platform.service.model.biz.agent.CutByQuotaResult;
import ai.conrain.aigc.platform.service.model.biz.agent.DiversifyOptions;
import ai.conrain.aigc.platform.service.model.biz.agent.ImageMatchScope;
import ai.conrain.aigc.platform.service.model.biz.agent.ProcessedInput;
import ai.conrain.aigc.platform.service.model.biz.agent.RecallOptions;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageSearchRequest;
import ai.conrain.aigc.platform.service.model.biz.agent.UserRefImgDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.UserUploadStyleImg;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.query.ImageGroupQuery;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.query.SearchResultImgQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.SearchResultImgVO;
import ai.conrain.aigc.platform.service.model.vo.SearchSummaryInfo;
import ai.conrain.aigc.platform.service.model.vo.SearchTaskVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendation;
import ai.conrain.aigc.platform.service.model.vo.StyleImageSearchResult;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.AsyncContextHelper;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pgvector.PGvector;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ImageSearchServiceImpl implements ImageSearchService {

    private static final int TEXT_EMB_DIMENSION = 256;
    @Autowired
    private ComposedEmbeddingService embeddingService;
    @Autowired
    private ImageCaptionService imageCaptionService;
    @Autowired
    private ImageService imageService;
    // 线程池
    private ExecutorService executorService;
    @Autowired
    private SortModelService sortModelService;

    @Autowired
    private SearchTaskService searchTaskService;

    @Autowired
    private SearchResultImgService searchResultImgService;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private MaterialInfoService materialInfoService;

    @Autowired
    private ImageAnalysisService imageAnalysisService;

    @Autowired
    private TairService tairService;

    @Autowired
    private ImageGroupService imageGroupService;

    @NotNull
    private static ClothGenderEnum getClothGenderEnum(MaterialInfoVO materialInfoVO) {
        ClothGenderEnum gender = ClothGenderEnum.FEMALE;
        String genderType = materialInfoVO.getExtInfo().getString(CommonConstants.KEY_CLOTH_STYLE_TYPE);
        if (StringUtils.isNotBlank(genderType) && StringUtils.equalsIgnoreCase(genderType, CommonConstants.male)) {
            gender = ClothGenderEnum.MALE;
        }
        return gender;
    }

    @NotNull
    private static ImageVO getImageVO(StyleImageSearchRequest param, MaterialModelVO cloth) {
        ImageVO clothImageVO = new ImageVO();
        clothImageVO.setUrl(param.getClothImgItem().getClothImgUrl());
        clothImageVO.setType("cloth");

        // 构建服装图片的metadata
        com.alibaba.fastjson2.JSONObject clothMetadata = new com.alibaba.fastjson2.JSONObject();
        clothMetadata.put("materialModelId", cloth.getId());
        clothMetadata.put("source", "auto-gen-pair-data");
        if (param.getClothImgItem().getClothType() != null) {
            clothMetadata.put("clothType", param.getClothImgItem().getClothType().getCode());
            clothMetadata.put("clothTypeDesc", param.getClothImgItem().getClothType().getDesc());
        }
        if (param.getClothImgItem().getClothGender() != null) {
            clothMetadata.put("clothGender", param.getClothImgItem().getClothGender().getCode());
        }
        clothImageVO.setMetadata(clothMetadata);
        return clothImageVO;
    }

    private static void sortByScore(ClothShootGenreEnum genre, List<List<StyleImageCandidate>> backgroundClusters) {

        int sortLimit = 3;

        backgroundClusters.forEach(cluster -> {
            log.info("【背景聚类-类内排序前】流派{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), cluster.size(), sortLimit,
                    String.format("%.3f",
                            cluster.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore).average()
                                    .orElse(0.0)),
                    sortLimit,
                    cluster.stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit).toArray());

            cluster.sort(
                    (candidate1, candidate2) -> Double.compare(candidate2.getMatchScore(), candidate1.getMatchScore()));

            log.info("【背景聚类-类内排序后】流派{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), cluster.size(), sortLimit,
                    String.format("%.3f",
                            cluster.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore).average()
                                    .orElse(0.0)),
                    sortLimit,
                    cluster.stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit).toArray());
        });

        // 按聚类内部取前n名元素的平均匹配分进行排序，匹配度高的聚类优先分配
        backgroundClusters.sort((cluster1, cluster2) -> {
            double avgScore1 = cluster1.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore)
                    .average().orElse(0.0);
            double avgScore2 = cluster2.stream().limit(sortLimit).mapToDouble(StyleImageCandidate::getMatchScore)
                    .average().orElse(0.0);
            return Double.compare(avgScore2, avgScore1);
        });

        for (int i = 0; i < backgroundClusters.size(); i++) {
            log.info("【背景聚类-类间排序后】流派{}聚类{}，大小：{}，前{}平均分：{}，前{}名分数:{}", genre.getDisplayName(), i,
                    backgroundClusters.get(i).size(), sortLimit,
                    String.format("%.3f",
                            backgroundClusters.get(i).stream().limit(sortLimit)
                                    .mapToDouble(StyleImageCandidate::getMatchScore).average().orElse(0.0)),
                    sortLimit,
                    backgroundClusters.get(i).stream().map(StyleImageCandidate::getMatchScore).limit(sortLimit)
                            .toArray());
        }
    }

    @NotNull
    private static SearchResultImgVO getSearchResultImgVO(StyleImageRecommendation recommendation,
                                                          SearchTaskVO savedTask) {
        SearchResultImgVO resultImg = new SearchResultImgVO();
        resultImg.setSearchId(savedTask.getId());
        resultImg.setImageId(recommendation.getImageId());
        resultImg.setImageUrl(recommendation.getImageUrl());
        resultImg.setImageShowUrl(recommendation.getImageShowUrl());
        resultImg.setImageCaptionId(recommendation.getImageCaptionId());
        resultImg.setGenre(
                recommendation.getClothShootGenreEnum() != null ? recommendation.getClothShootGenreEnum().getCode()
                        : null);
        resultImg.setBgClusterKey(recommendation.getBgClusterKey());
        resultImg.setIdxInCluster(recommendation.getIdxInCluster());
        resultImg.setStyleSimilarity(recommendation.getStyleSimilarity());
        resultImg.setMatchScore(recommendation.getMatchScore());
        return resultImg;
    }

    @NotNull
    private static SearchSummaryInfo getSearchSummaryInfo(StyleImageSearchRequest param,
                                                          StyleImageSearchResult result, Map<String, Long> performanceMetrics) {
        // 构建搜索汇总信息
        SearchSummaryInfo summaryInfo = new SearchSummaryInfo();

        // 统计请求图片数量
        int clothImgCount = 1; // 服装图固定为1张
        int refImgCount = param != null && CollectionUtils.isNotEmpty(param.getUserUploadStyleImgs())
                ? param.getUserUploadStyleImgs().size()
                : 0;
        summaryInfo.setClothImgCount(clothImgCount);
        summaryInfo.setRefImgCount(refImgCount);

        // 统计返回结果
        int genreGroupCount = 0;
        int totalCandidateCount = 0;
        if (result != null && CollectionUtils.isNotEmpty(result.getItems())) {
            genreGroupCount = result.getItems().size();
            for (List<List<StyleImageRecommendation>> genreList : result.getItems()) {
                if (CollectionUtils.isNotEmpty(genreList)) {
                    for (List<StyleImageRecommendation> clusterList : genreList) {
                        if (CollectionUtils.isNotEmpty(clusterList)) {
                            totalCandidateCount += clusterList.size();
                        }
                    }
                }
            }
        }
        summaryInfo.setGenreGroupCount(genreGroupCount);
        summaryInfo.setTotalCandidateCount(totalCandidateCount);

        // 设置性能指标
        if (performanceMetrics != null) {
            summaryInfo.setTotalDurationMs(performanceMetrics.getOrDefault("总耗时", 0L));

            // 构建各环节耗时详情
            Map<String, Long> stepDurations = new HashMap<>();
            stepDurations.put("inputProcessing", performanceMetrics.getOrDefault("输入处理模块", 0L));
            stepDurations.put("clothFeatureRecall", performanceMetrics.getOrDefault("服装图特征召回", 0L));
            stepDurations.put("genreMMRDiversify", performanceMetrics.getOrDefault("流派并行MMR打散", 0L));
            stepDurations.put("genreBackgroundClustering", performanceMetrics.getOrDefault("流派背景聚类", 0L));
            summaryInfo.setStepDurations(stepDurations);
        }
        return summaryInfo;
    }

    private static void checkSearchRequest(StyleImageSearchRequest req) {
        AssertUtil.assertNotNull(req, "参数不能为空");

        AssertUtil.assertNotNull(req.getSearchMode(), "searchMode不能为空");
        switch (req.getSearchMode()) {
            case STYLE:
                break;
            case MORE_DIFFERENT:
                AssertUtil.assertNotNull(req.getExcludeRetInSearchIds(), "excludeRetInSearchIds is empty");
                break;
            case MORE_WITH_GENRE:
                AssertUtil.assertNotNull(req.getSpecifiedSearchId(), "specifiedSearchId is null");
                AssertUtil.assertNotNull(req.getSpecifiedGenre(), "specifiedGenre is null");
                break;
            case MORE_WITH_BG_CLUSTER:
                AssertUtil.assertNotNull(req.getSpecifiedSearchId(), "specifiedSearchId is null");
                AssertUtil.assertNotNull(req.getSpecifiedGenre(), "specifiedGenre is null");
                AssertUtil.assertNotNull(req.getSpecifiedRetImgCaptionId(), "specifiedRetImgCaptionId is null");
                break;
            default:
                throw new IllegalArgumentException("无效的searchMode:" + req.getSearchMode());
        }

        AssertUtil.assertNotNull(req.getClothImgItem(), "服装图片不能为空");
        AssertUtil.assertNotBlank(req.getClothImgItem().getClothImgUrl(), "服装图片URL不能为空");
        AssertUtil.assertNotNull(req.getClothImgItem().getClothType(), "服装类型不能为空");
        AssertUtil.assertNotNull(req.getClothImgItem().getClothGender(), "服装性别不能为空");
        AssertUtil.assertNotNull(req.getClothImgItem().getAgeGroup(), "服装年龄段不能为空");

        AssertUtil.assertTrue(SearchUtil.isValidImageAnalysisCloth(req.getClothImgItem().getClothAnalysis()),
                "服装标注不能为空");

        AssertUtil.assertNotNull(req.getClothImgItem().getPreCaption(), "服装图预打标结果不能为空");
    }

    @Nullable
    private static List<StyleImageCandidate> getStyleImageCandidates(StyleImageSearchRequest req,
                                                                     List<List<StyleImageCandidate>> cachedBgClusters) {
        List<StyleImageCandidate> targetCluster = null;
        for (List<StyleImageCandidate> cluster : cachedBgClusters) {
            for (StyleImageCandidate candidate : cluster) {
                if (candidate.getImageCaption().getId().equals(req.getSpecifiedRetImgCaptionId())) {
                    targetCluster = cluster;
                    break;
                }
            }
            if (targetCluster != null)
                break;
        }
        return targetCluster;
    }

    @PostConstruct
    public void init() {
        try {
            // 初始化线程池
            executorService = Executors.newVirtualThreadPerTaskExecutor();
        } catch (Exception e) {
            log.error("初始化失败", e);
            throw new RuntimeException("初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (executorService != null) {
                executorService.shutdown(); // 优雅关闭
                try {
                    // 虚拟线程关闭更快，等待30秒即可
                    if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                        executorService.shutdownNow(); // 强制关闭
                        // 再等待30秒
                        if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                            log.warn("虚拟线程执行器未能完全关闭");
                        }
                    }
                    log.info("虚拟线程执行器已关闭");
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                    log.warn("虚拟线程执行器关闭过程中被中断", e);
                }
            }
        } catch (Exception e) {
            log.error("资源释放失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void searchByClothImgAndBuildPairs(MaterialModelVO cloth) throws Exception {
        String clothUrl = materialModelService.queryDetailShowImage(cloth.getId());
        if (StringUtils.isBlank(clothUrl)) {
            log.error("服装图不存在，id={}", cloth.getId());
            return;
        }
        if (cloth.getClothLoraTrainDetail() == null) {
            log.error("服装模型不存在，id={}", cloth.getId());
            return;
        }
        if (cloth.getClothLoraTrainDetail().getOriginalMaterialId() == null) {
            log.error("服装模型原始素材不存在，id={}", cloth.getId());
            return;
        }

        MaterialInfoVO materialInfoVO = materialInfoService
                .selectById(cloth.getClothLoraTrainDetail().getOriginalMaterialId());
        if (materialInfoVO == null) {
            log.error("服装模型原始素材不存在，id={}", cloth.getClothLoraTrainDetail().getOriginalMaterialId());
            return;
        }

        // 解析服装图，获得文本特征json
        ImageAnalysisResult clothAnalysisResult = imageAnalysisService.syncImageAnalysis(clothUrl, true);
        if (clothAnalysisResult == null || clothAnalysisResult.getAnalysis() == null) {
            log.error("服装图解析失败，url={}", clothUrl);
            return;
        }

        ImageAnalysisCaption clothAnalysis = clothAnalysisResult.getAnalysis();

        if (!SearchUtil.isValidImageAnalysisCloth(clothAnalysis)) {
            log.error("服装图解析失败，服装图无效，cloth id={}, url={}", cloth.getId(), clothUrl);
            return;
        }

        // 获取服装图预打标结果
        ImageAnalysisResult imagePreCaption = imageAnalysisService.syncImagePreCaption(clothUrl, true);
        if (imagePreCaption == null || imagePreCaption.getPreCaption() == null) {
            log.error("服装图预打标失败，url={}", clothUrl);
            return;
        }

        // 组装搜索参数
        StyleImageSearchRequest param = new StyleImageSearchRequest();

        param.setSearchMode(SearchModeEnum.STYLE);

        ClothImgItem clothImgItem = new ClothImgItem();
        clothImgItem.setClothImgUrl(clothUrl);
        clothImgItem.setClothType(ClothTypeEnum.getByCode(materialInfoVO.getSubType()));
        clothImgItem.setClothGender(getClothGenderEnum(materialInfoVO));
        clothImgItem.setClothAnalysis(clothAnalysis);
        clothImgItem.setPreCaption(imagePreCaption.getPreCaption());

        //将cloth.extInfo["ageRange"]通过AgeRangeEnum转换为搜索需要的AgeGroupEnum，如果没有，则设置为UNKNOWN
        clothImgItem.setAgeGroup(getClothGenderEnum(cloth));

        param.setClothImgItem(clothImgItem);

        // 开始搜索
        StyleImageSearchResult result = this.searchAndRecommend(param);
        log.info("搜索结果处理完成，result={}", result);

        // 根据搜索结果创建pair对样本，并将服装记录写入image表，结合搜索结果各个流派的top1构建匹配度记录写入image_group表
        if (result != null && CollectionUtils.isNotEmpty(result.getItems())) {
            buildPairs(param, cloth, result);
        }
    }

    private AgeGroupEnum getClothGenderEnum(MaterialModelVO cloth) {
        String ageRange = cloth.getExtInfo(CommonConstants.KEY_AGE_RANGE, String.class);
        if (ageRange != null) {
            AgeRangeEnum ageRangeEnum = AgeRangeEnum.getByCode(ageRange);
            if (ageRangeEnum != null) {
                switch (ageRangeEnum) {
                    case AgeRangeEnum.BIG_CHILD:
                    case AgeRangeEnum.MEDIUM_CHILD:
                    case AgeRangeEnum.SMALL_CHILD:
                        return AgeGroupEnum.CHILD;
                    case AgeRangeEnum.INFANT_CHILD:
                        return AgeGroupEnum.YOUNG;
                    case AgeRangeEnum.TEENAGER:
                        return AgeGroupEnum.TEENAGER;
                    case AgeRangeEnum.ADULT:
                        return AgeGroupEnum.ADULT;
                }
            }
        }

        return AgeGroupEnum.UNKNOWN;
    }


    private void buildPairs(StyleImageSearchRequest param, MaterialModelVO cloth,
                            StyleImageSearchResult result) {
        // 检查是否已存在相同URL的服装图片
        ImageQuery existingClothQuery = new ImageQuery();
        existingClothQuery.setUrl(param.getClothImgItem().getClothImgUrl());
        existingClothQuery.setType("cloth");
        List<ImageVO> existingClothImages = imageService.queryImageList(existingClothQuery);

        // 1.保存服装图片
        ImageVO clothImage;
        if (CollectionUtils.isNotEmpty(existingClothImages)) {
            clothImage = existingClothImages.getFirst();
            log.info("服装图片已存在，使用现有记录，imageId={}", clothImage.getId());
        } else {
            // 1. 将服装记录写入image表
            ImageVO clothImageVO = getImageVO(param, cloth);
            clothImage = imageService.insert(clothImageVO);
            log.info("服装图片已保存到image表，imageId={}", clothImage.getId());
        }

        // 2. 遍历搜索结果各个流派，获取每个流派的top1图片，构建pair对
        for (List<List<StyleImageRecommendation>> genreGroups : result.getItems()) {
            if (CollectionUtils.isNotEmpty(genreGroups)) {
                // 获取该流派第一个背景聚类的第一张图片作为top1
                List<StyleImageRecommendation> firstCluster = genreGroups.getFirst();
                if (CollectionUtils.isNotEmpty(firstCluster)) {
                    StyleImageRecommendation top1Recommendation = firstCluster.getFirst();
                    createPair(top1Recommendation, clothImage, cloth);
                }
            }
        }
    }

    private void createPair(StyleImageRecommendation top1Recommendation, ImageVO clothImage, MaterialModelVO cloth) {
        // 3. 创建ImageGroup记录，构建服装图片与场景图片的pair对
        ImageGroupVO imageGroupVO = new ImageGroupVO();
        List<Integer> imageIds = new ArrayList<>();
        imageIds.add(top1Recommendation.getImageId()); // 场景图片ID
        imageIds.add(clothImage.getId()); // 服装图片ID
        imageGroupVO.setImageIds(imageIds);

        // 构建ImageGroup的metadata，记录匹配度信息
        com.alibaba.fastjson2.JSONObject groupMetadata = new com.alibaba.fastjson2.JSONObject();

        groupMetadata.put("tags", List.of("auto-gen-" + DateUtils.formatShort(new Date())));
        groupMetadata.put("materialModelId", cloth.getId());
        groupMetadata.put("genre",
                top1Recommendation.getClothShootGenreEnum() != null
                        ? top1Recommendation.getClothShootGenreEnum().getCode()
                        : null);
        groupMetadata.put("genreDesc",
                top1Recommendation.getClothShootGenreEnum() != null
                        ? top1Recommendation.getClothShootGenreEnum().getDisplayName()
                        : null);
        groupMetadata.put("matchScore", top1Recommendation.getMatchScore());
        groupMetadata.put("styleSimilarity", top1Recommendation.getStyleSimilarity());
        groupMetadata.put("bgClusterId", top1Recommendation.getBgClusterKey());
        imageGroupVO.setMetadata(groupMetadata);

        // 检查是否已存在相同的ImageGroup
        ImageGroupQuery existingGroupQuery = new ImageGroupQuery();
        existingGroupQuery.setImageIds(imageIds);
        List<ImageGroupVO> existingGroups = imageGroupService.queryImageGroupList(existingGroupQuery);

        if (CollectionUtils.isEmpty(existingGroups)) {
            ImageGroupVO savedGroup = imageGroupService.insert(imageGroupVO);
            log.info("创建pair对成功，imageGroupId={}, 服装图片ID={}, 场景图片ID={}, 流派={}, 匹配分数={}",
                    savedGroup.getId(), clothImage.getId(), top1Recommendation.getImageId(),
                    top1Recommendation.getClothShootGenreEnum() != null
                            ? top1Recommendation.getClothShootGenreEnum().getDisplayName()
                            : "未知",
                    top1Recommendation.getMatchScore());
        } else {
            log.info("pair对已存在，跳过创建，服装图片ID={}, 场景图片ID={}",
                    clothImage.getId(), top1Recommendation.getImageId());
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public StyleImageSearchResult searchAndRecommend(StyleImageSearchRequest req) throws Exception {
        checkSearchRequest(req);

        log.info("🎯 开始服装拍摄风格图片推荐算法,req:{}", JSONObject.toJSONString(req));

        // 整体耗时统计开始
        long totalStartTime = System.currentTimeMillis();
        Map<String, Long> performanceMetrics = new LinkedHashMap<>();

        // 0. 创建初始搜索任务流水
        SearchTaskVO searchTask = createInitialSearchTask(req);

        // 缓存查询逻辑：如果指定了流派，尝试从缓存获取结果
        if (req.getSpecifiedGenre() != null) {
            StyleImageSearchResult result = searchImageWithSpecifiedGenre(req, searchTask, totalStartTime,
                    performanceMetrics);
            if (result != null)
                return result;
        }

        // 1. 输入处理模块，计算各种向量
        long step1StartTime = System.currentTimeMillis();
        ProcessedInput processedInput = processInput(req);
        long step1EndTime = System.currentTimeMillis();
        long step1Duration = step1EndTime - step1StartTime;
        performanceMetrics.put("输入处理模块", step1Duration);
        log.info("📥 输入处理完成 | 耗时:{}ms | 服装类型:{} | 参考图:{}张",
                step1Duration,
                req.getClothImgItem().getClothType().getDesc(),
                CollectionUtils.size(processedInput.getUserRefImgDescriptions()));

        // 2. 服装图特征召回（100w->保留2000条）
        long step2StartTime = System.currentTimeMillis();
        List<StyleImageCandidate> candidates = recallByClothFeatures(processedInput,
                req.getRecallOptions(), req.getClothImgItem().getAgeGroup(), req.getExcludeRetInSearchIds());
        long step2EndTime = System.currentTimeMillis();
        long step2Duration = step2EndTime - step2StartTime;
        performanceMetrics.put("服装图特征召回", step2Duration);
        Map<String, Integer> recallGenreStats = getGenreStats(candidates);
        log.info("🔍 服装特征召回完成 | 耗时:{}ms | 候选数:{} | 流派分布:{}",
                step2Duration, candidates.size(), recallGenreStats);

        // 3. 流派分组和过滤（先按流派分组，过滤掉不需要的流派）
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = groupByGenre(candidates, processedInput,
                req.getSpecifiedGenre());
        // 过滤掉空的流派组
        genreGroups.entrySet().removeIf(entry -> entry.getValue().isEmpty());

        // 4. 对每个流派并行进行MMR打散过滤（2000->1000）
        long step4StartTime = System.currentTimeMillis();
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifiedGenreGroups = diversifyByGenre(genreGroups,
                processedInput, req.getDiversifyOptions());
        long step4EndTime = System.currentTimeMillis();
        long step4Duration = step4EndTime - step4StartTime;
        performanceMetrics.put("流派并行MMR打散", step4Duration);
        int totalDiversified = diversifiedGenreGroups.values().stream().mapToInt(List::size).sum();
        log.info("🎯 流派并行MMR打散完成 | 耗时:{}ms | 输出总数:{} | 流派数:{}",
                step4Duration, totalDiversified, diversifiedGenreGroups.size());

        // 5. 对每个流派内按背景聚类
        long step5StartTime = System.currentTimeMillis();
        StyleImageSearchResult result = clusterByGenreAndBackground(diversifiedGenreGroups, processedInput,
                req.getClusterOptions(), searchTask.getId());
        long step5EndTime = System.currentTimeMillis();
        long step5Duration = step5EndTime - step5StartTime;
        performanceMetrics.put("流派背景聚类", step5Duration);
        log.info("🔗 流派背景聚类完成 | 耗时:{}ms | 输入流派数:{} → 聚类结果:{}项",
                step5Duration, diversifiedGenreGroups.size(),
                result.getItems() != null ? result.getItems().size() : 0);

        // 计算总耗时
        long totalEndTime = System.currentTimeMillis();
        long totalDuration = totalEndTime - totalStartTime;
        performanceMetrics.put("总耗时", totalDuration);

        result.setPerformanceReport(logPerformanceReport(performanceMetrics, result));

        log.info("【耗时统计】:{}", result.getPerformanceReport());

        // 记录搜索推荐流水到SearchTask和SearchResultImg
        SearchTaskVO searchTaskVO = saveSearchResult(searchTask, req, result, performanceMetrics);

        result.setSearchTaskVO(searchTaskVO);

        return result;

    }

    @Nullable
    private StyleImageSearchResult searchImageWithSpecifiedGenre(StyleImageSearchRequest req, SearchTaskVO searchTask,
                                                                 long totalStartTime, Map<String, Long> performanceMetrics) throws Exception {
        String cacheKey = buildGenreCandidatesCacheKey(req.getSpecifiedSearchId(), req.getSpecifiedGenre());
        List<List<StyleImageCandidate>> cachedBgClusters = getBgClusterFromCache(cacheKey);

        if (cachedBgClusters != null && !cachedBgClusters.isEmpty()) {
            log.info("🎯 从缓存获取到流派候选数据 | 流派:{} | 背景聚类数:{}",
                    req.getSpecifiedGenre().getDisplayName(), cachedBgClusters.size());

            List<List<List<StyleImageRecommendation>>> resultItems;

            // 如果指定了特定的图片ID，则定位到对应的背景聚类簇
            if (req.getSpecifiedRetImgCaptionId() != null) {
                log.info("🎯 指定了图片ID:{}, 定位对应的背景聚类簇", req.getSpecifiedRetImgCaptionId());

                // 查找包含指定图片ID的聚类簇
                List<StyleImageCandidate> targetCluster = getStyleImageCandidates(req, cachedBgClusters);

                if (targetCluster != null) {
                    log.info("🎯 找到包含指定图片的聚类簇，簇大小:{}", targetCluster.size());

                    List<StyleImageCandidate> limitedCluster = targetCluster.stream()
                            .limit(30)
                            .toList();

                    // 转换为StyleImageRecommendation
                    ImageQuery imageQuery = new ImageQuery();
                    imageQuery.setIds(limitedCluster.stream()
                            .map(c -> c.getImageCaption().getImageId())
                            .collect(Collectors.toList()));
                    List<ImageVO> imageVOList = imageService.queryImageList(imageQuery);
                    Map<Integer, ImageVO> imageMap = imageVOList.stream()
                            .collect(Collectors.toMap(ImageVO::getId, v -> v));

                    String bgClusterKey = buildBgClusterKey(searchTask.getId(), req.getSpecifiedGenre(), 0);
                    List<StyleImageRecommendation> recommendations = new ArrayList<>();
                    for (int i = 0; i < limitedCluster.size(); i++) {
                        StyleImageRecommendation item = convert2RecommendationItem(
                                limitedCluster.get(i), req.getSpecifiedGenre(), bgClusterKey, i, imageMap);
                        if (item != null) {
                            recommendations.add(item);
                        }
                    }

                    // 包装成三层结构：流派 -> 背景聚类 -> 推荐项
                    resultItems = List.of(List.of(recommendations));
                } else {
                    log.warn("🎯 未找到包含指定图片ID:{}的聚类簇", req.getSpecifiedRetImgCaptionId());
                    throw new Exception("未找到包含指定图片ID的聚类簇:" + req.getSpecifiedRetImgCaptionId());
                }
            } else {
                // 通过cutByQuota进行截断处理
                CutByQuotaResult cutResult = cutByQuota(req.getSpecifiedGenre(), cachedBgClusters,
                        req.getClusterOptions(), searchTask.getId());
                // 包装成三层结构：流派 -> 背景聚类 -> 推荐项
                resultItems = List.of(cutResult.getDisplayItems());
            }

            StyleImageSearchResult result = new StyleImageSearchResult();
            result.setItems(resultItems);

            // 记录整体耗时
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;
            performanceMetrics.put("总耗时(缓存命中)", totalDuration);

            // 保存搜索任务和性能日志
            SearchTaskVO savedTask = saveSearchResult(searchTask, req, result, performanceMetrics);
            logPerformanceReport(performanceMetrics, result);

            result.setSearchTaskVO(savedTask);

            return result;
        }
        return null;
    }

    /**
     * 获取流派统计信息
     */
    private Map<String, Integer> getGenreStats(List<StyleImageCandidate> candidates) {
        Map<String, Integer> tagCountMap = new HashMap<>();
        candidates.forEach(c -> {
            String genre = c.getGenreStr();
            if (StringUtils.isNotBlank(genre)) {
                try {
                    String k = ClothShootGenreEnum.getByCode(genre).getDisplayName();
                    tagCountMap.put(k, tagCountMap.getOrDefault(k, 0) + 1);
                } catch (Exception e){
                    log.error("无效的流派代码:{}", genre);
                }
            }
        });
        return tagCountMap;
    }

    /**
     * 1. 输入处理模块（并行优化版本）
     */
    private ProcessedInput processInput(StyleImageSearchRequest param) throws Exception {

        ProcessedInput input = new ProcessedInput();

        // 如果指定了返回结果图片ID，将其作为参考图片添加到列表中
        if (param.getSpecifiedRetImgCaptionId() != null) {
            UserUploadStyleImg specifiedImg = createUserUploadStyleImgFromImageId(param.getSpecifiedRetImgCaptionId());
            if (param.getUserUploadStyleImgs() == null) {
                param.setUserUploadStyleImgs(new ArrayList<>());
            }
            param.getUserUploadStyleImgs().add(specifiedImg);
        }

        // 并行处理：服装图片向量计算 & 参考图片向量计算
        CompletableFuture<ClothDescription> clothFuture = CompletableFuture.supplyAsync(
                AsyncContextHelper.wrapSupplier(() -> {
                    long clothProcessStartTime = System.currentTimeMillis();
                    ClothDescription clothDescription = processClothImage(param);
                    long clothProcessEndTime = System.currentTimeMillis();
                    log.info("【耗时统计】服装图片处理完成，耗时：{}ms", clothProcessEndTime - clothProcessStartTime);
                    return clothDescription;
                }), executorService);

        if (CollectionUtils.isNotEmpty(param.getUserUploadStyleImgs())) {
            CompletableFuture<Pair<List<UserRefImgDescription>, Map<String, PGvector>>> refImagesFuture = CompletableFuture
                    .supplyAsync(AsyncContextHelper.wrapSupplier(() -> {
                        long refProcessStartTime = System.currentTimeMillis();
                        List<UserRefImgDescription> userRefImgDescriptions = getUserUploadStyleImgDescriptionsParallel(
                                param);
                        long refProcessEndTime = System.currentTimeMillis();
                        log.info("【耗时统计】参考图片并行处理{}张图片，耗时：{}ms",
                                param.getUserUploadStyleImgs() != null ? param.getUserUploadStyleImgs().size() : 0,
                                refProcessEndTime - refProcessStartTime);

                        // 计算参考图的各维度的平均向量，用于优化MMR算法性能
                        Map<String, PGvector> averageDimensionVectors = calculateAverageDimensionVectors(
                                userRefImgDescriptions);

                        return Pair.of(userRefImgDescriptions, averageDimensionVectors);
                    }), executorService);

            // 等待服装图片和参考图片处理完成
            Pair<List<UserRefImgDescription>, Map<String, PGvector>> refImagesResult = refImagesFuture.get();

            input.setUserRefImgDescriptions(refImagesResult.getLeft());
            input.setAverageDimensionVectors(refImagesResult.getRight());
        }

        input.setClothDescription(clothFuture.get());

        // 计算动态权重
        input.setDynamicWeights(calculateDynamicWeights(param.getUserUploadStyleImgs()));

        return input;
    }

    /**
     * 根据imageId创建UserUploadStyleImg对象
     */
    private UserUploadStyleImg createUserUploadStyleImgFromImageId(Integer imageCaptionId) {
        // 查询图片标注信息
        ImageCaptionVO imageCaptionVO = imageCaptionService.selectById(imageCaptionId);
        AssertUtil.assertNotNull(imageCaptionVO, "图片标注信息不存在");

        ImageVO imageVO = imageService.selectById(imageCaptionVO.getImageId());

        // 构造UserUploadStyleImg对象
        UserUploadStyleImg styleImg = new UserUploadStyleImg();
        styleImg.setImgUrl(imageVO.getUrl());
        styleImg.setImgAnalysis(imageCaptionVO.getCaption());

        // 设置默认的匹配范围为全局匹配
        styleImg.setImageMatchScopeList(List.of(ImageMatchScope.ALL));

        return styleImg;
    }

    /**
     * 计算动态权重
     */
    private Map<String, Double> calculateDynamicWeights(List<UserUploadStyleImg> userUploadStyleImgs) {
        Map<String, Double> weights = new HashMap<>();

        // 默认权重
        weights.put(ImageMatchScope.BG.name(), 0.3);
        weights.put(ImageMatchScope.ACCESSORIES.name(), 0.05);
        weights.put(ImageMatchScope.MODEL_FACIAL.name(), 0.05);
        weights.put(ImageMatchScope.MODEL_POSE.name(), 0.05);
        weights.put(ImageMatchScope.LIGHT.name(), 0.05);

        if (CollectionUtils.isEmpty(userUploadStyleImgs)) {
            return weights;
        }

        // 统计用户选择的维度
        Set<String> selectedDimensions = new HashSet<>();
        for (UserUploadStyleImg styleImg : userUploadStyleImgs) {
            if (CollectionUtils.isNotEmpty(styleImg.getImageMatchScopeList())) {
                for (ImageMatchScope scope : styleImg.getImageMatchScopeList()) {
                    selectedDimensions.add(scope.name());
                }
            }
        }

        // 重新分配权重
        if (!selectedDimensions.isEmpty()) {
            double totalWeight = 0.5;
            double weightPerDimension = totalWeight / selectedDimensions.size();

            // 重置权重
            weights.replaceAll((k, v) -> 0.0);

            // 分配权重给选中的维度
            for (String dimension : selectedDimensions) {
                weights.put(dimension, weightPerDimension);
            }
        }

        return weights;
    }

    /**
     * 计算各维度的平均向量
     *
     * @param userRefImgDescriptions 用户参考图描述列表
     * @return 各维度的平均向量映射
     */
    private Map<String, PGvector> calculateAverageDimensionVectors(List<UserRefImgDescription> userRefImgDescriptions) {
        Map<String, PGvector> averageDimensionVectors = new HashMap<>();

        if (CollectionUtils.isEmpty(userRefImgDescriptions)) {
            return averageDimensionVectors;
        }

        // 按维度分组收集向量
        Map<String, List<PGvector>> dimensionVectorsMap = new HashMap<>();

        for (UserRefImgDescription refDesc : userRefImgDescriptions) {
            if (refDesc.getDimensionVectors() != null) {
                for (Map.Entry<String, PGvector> entry : refDesc.getDimensionVectors().entrySet()) {
                    String dimensionName = entry.getKey();
                    PGvector vector = entry.getValue();

                    if (vector != null) {
                        dimensionVectorsMap.computeIfAbsent(dimensionName, k -> new ArrayList<>()).add(vector);
                    }
                }
            }
        }

        // 计算每个维度的平均向量
        for (Map.Entry<String, List<PGvector>> entry : dimensionVectorsMap.entrySet()) {
            String dimensionName = entry.getKey();
            List<PGvector> vectors = entry.getValue();

            if (!vectors.isEmpty()) {
                try {
                    PGvector averageVector = VectorUtil.calculateAverageVector(vectors);
                    averageDimensionVectors.put(dimensionName, averageVector);
                    log.debug("维度{}计算平均向量完成，包含{}个向量", dimensionName, vectors.size());
                } catch (Exception e) {
                    log.warn("计算维度{}的平均向量时发生异常: {}", dimensionName, e.getMessage());
                }
            }
        }

        log.info("计算各维度平均向量完成，共{}个维度", averageDimensionVectors.size());
        return averageDimensionVectors;
    }

    /**
     * 并行处理参考图片向量计算
     */
    @NotNull
    private List<UserRefImgDescription> getUserUploadStyleImgDescriptionsParallel(
            StyleImageSearchRequest param) {
        List<UserRefImgDescription> userRefImgDescriptions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getUserUploadStyleImgs())) {
            try {
                // 创建并行任务列表
                List<CompletableFuture<UserRefImgDescription>> futures = new ArrayList<>();

                // 为每个参考图创建并行处理任务
                for (int i = 0; i < param.getUserUploadStyleImgs().size(); i++) {
                    final int index = i;
                    UserUploadStyleImg styleImg = param.getUserUploadStyleImgs().get(index);
                    CompletableFuture<UserRefImgDescription> future = CompletableFuture
                            .supplyAsync(AsyncContextHelper.wrapSupplier(
                                    () -> processReferenceImage(styleImg, index)), executorService);
                    futures.add(future);
                }

                // 等待所有任务完成并收集结果
                for (CompletableFuture<UserRefImgDescription> future : futures) {
                    UserRefImgDescription result = future.get();
                    if (result != null) {
                        userRefImgDescriptions.add(result);
                    }
                }

            } catch (Exception e) {
                log.error("参考图片并行处理失败", e);
                throw new RuntimeException("参考图片并行处理失败", e);
            }
        }
        return userRefImgDescriptions;
    }

    /**
     * 处理单个参考图片（并行版本）
     */
    private UserRefImgDescription processReferenceImage(UserUploadStyleImg styleImg, int index) {
        try {
            AssertUtil.assertNotNull(styleImg.getImgAnalysis(), ResultCode.PARAM_INVALID, "参考图片的视觉打标不能为空");
            AssertUtil.assertNotEmpty(styleImg.getImageMatchScopeList(), "imageMatchScopeList不能为空");

            // 统一处理：全局参考时，替换为4个子维度进行计算
            if (styleImg.getImageMatchScopeList().contains(ImageMatchScope.ALL)) {
                styleImg.getImageMatchScopeList().clear();
                styleImg.getImageMatchScopeList().addAll(List.of(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE));
            }

            // 收集当前参考图的所有维度文本
            List<String> currentImgTexts = new ArrayList<>();
            List<ImageMatchScope> currentImgScopes = new ArrayList<>();

            for (ImageMatchScope scope : styleImg.getImageMatchScopeList()) {
                String dimensionText = extractDimensionText(styleImg.getImgAnalysis(), scope);
                if (StringUtils.isNotBlank(dimensionText)) {
                    currentImgTexts.add(dimensionText);
                    currentImgScopes.add(scope);
                }
            }

            // 为当前参考图计算向量（每次调用不超过10个文本）
            Map<String, PGvector> dimensionVectorMap = new HashMap<>();
            if (!currentImgTexts.isEmpty()) {
                List<PGvector> currentImgVectors = embeddingService.getEmbeddingByTexts(currentImgTexts,
                        TEXT_EMB_DIMENSION);

                // 构建维度向量映射
                for (int j = 0; j < currentImgScopes.size(); j++) {
                    dimensionVectorMap.put(currentImgScopes.get(j).name(), currentImgVectors.get(j));
                }
            }

            // 创建参考图描述对象
            UserRefImgDescription refImgDescription = BeanUtils.deepCopy(styleImg, UserRefImgDescription.class);
            refImgDescription.setDimensionVectors(dimensionVectorMap);

            return refImgDescription;
        } catch (Exception e) {
            log.error("处理参考图片{}时发生异常", index, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 服装图片理解
     */
    private ClothDescription processClothImage(StyleImageSearchRequest request) {
        try {
            ClothDescription description = BeanUtils.deepCopy(request.getClothImgItem(), ClothDescription.class);
            // 并行计算3个向量
            CompletableFuture<PGvector> clothTextVectorFuture = CompletableFuture.supplyAsync(
                    AsyncContextHelper.wrapSupplier(
                            () -> imageCaptionService.calcClothTextVector(request.getClothImgItem().getClothAnalysis(),
                                    request.getClothImgItem().getClothType())),
                    executorService);
            CompletableFuture<PGvector> styleTextVectorFuture = CompletableFuture.supplyAsync(
                    AsyncContextHelper.wrapSupplier(() -> {
                        String styleDescription = imageCaptionService
                                .getClothStyleDescription(request.getClothImgItem().getClothAnalysis());
                        return embeddingService.getEmbeddingByText(styleDescription, TEXT_EMB_DIMENSION);
                    }), executorService);

            CompletableFuture<PGvector> clothImgVectorFuture = CompletableFuture.supplyAsync(
                    AsyncContextHelper.wrapSupplier(() -> embeddingService
                            .getImgEmbeddingByMultiModalModel(request.getClothImgItem().getClothImgUrl())),
                    executorService);

            // 等待所有任务完成并设置结果
            description.setClothTextVector(clothTextVectorFuture.get());
            description.setStyleTextVector(styleTextVectorFuture.get());
            description.setClothImgVector(clothImgVectorFuture.get());

            return description;
        } catch (Exception e) {
            log.error("服装图片并行处理失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 2. 服装图特征召回（100w->保留2000条）
     * 只支持并发查询所有流派
     */
    private List<StyleImageCandidate> recallByClothFeatures(ProcessedInput input, RecallOptions recallOptions,
                                                            AgeGroupEnum ageGroup, List<Integer> excludeRetInSearchIds) {

        AssertUtil.assertNotNull(input.getClothDescription().getPreCaption(), "服装预打标描述不存在");
        AssertUtil.assertNotNull(input.getClothDescription().getClothAnalysis(), "服装打标描述不存在");

        List<StyleImageCandidate> candidates = new ArrayList<>();

        // 获取需要排除的imageCaptionId集合
        Set<Integer> excludeImageCaptionIds = getExcludeImageCaptionIds(excludeRetInSearchIds);

        // 2.1 获取所有可用流派枚举（时尚大片和对镜自拍，产品功能上不需要考虑了）
        ClothShootGenreEnum[] allGenres = {ClothShootGenreEnum.LOOKBOOK, ClothShootGenreEnum.SOCIAL_NETWORK,
                ClothShootGenreEnum.COMMERCIAL_PHOTO, ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum.SHOW};

        int genreCount = allGenres.length;
        int limitPerGenre = Math.max(recallOptions.getStyleRecallCount() / genreCount, 500); // 每个流派分配的查询数量

        // 2.2 并发查询所有流派
        List<CompletableFuture<List<ImageCaptionVO>>> futures = new ArrayList<>();

        for (ClothShootGenreEnum genre : allGenres) {
            CompletableFuture<List<ImageCaptionVO>> future = CompletableFuture.supplyAsync(
                    AsyncContextHelper.wrapSupplier(() -> {
                        try {
                            // 根据服装预标注信息一次性构建所有规则参数
                            ClothRecallRules allRules = RecallRulesUtil.buildAllRules(input.getClothDescription().getPreCaption());
                            Map<String, List<String>> styleWhitelistRules = allRules.getStyleWhitelistRules();
                            Map<String, List<String>> functionWhitelistRules = allRules.getFunctionWhitelistRules();
                            Map<String, List<String>> compositionBlacklistRules = allRules.getCompositionBlacklistRules();

                            // 使用统一的查询方法，优先使用缓存
                            return imageCaptionService.queryByStyleVectorSimilarity(
                                    input.getClothDescription().getStyleTextVector(),
                                    input.getClothDescription().getClothGender(),
                                    recallOptions.getStyleSimilarityThreshold(),
                                    limitPerGenre,
                                    genre.getCode(), ageGroup, excludeImageCaptionIds,
                                    styleWhitelistRules, null,
                                    functionWhitelistRules, null,
                                    compositionBlacklistRules,
                                    true); // 优先使用缓存
                        } catch (Exception e) {
                            log.error("查询流派{}数据时发生异常", genre.getCode(), e);
                            return new ArrayList<>();
                        }
                    }), executorService);
            futures.add(future);
        }

        // 2.3 等待所有查询完成并合并结果
        List<ImageCaptionVO> allMatchedImages = new ArrayList<>();
        for (CompletableFuture<List<ImageCaptionVO>> future : futures) {
            try {
                List<ImageCaptionVO> genreResults = future.get();
                allMatchedImages.addAll(genreResults);
            } catch (Exception e) {
                log.error("获取流派查询结果时发生异常", e);
            }
        }

        // 2.4 按相似度排序并限制总数量(limit)
        allMatchedImages.sort((a, b) -> Double.compare(
                b.getClothStyleSimilarity() != null ? b.getClothStyleSimilarity() : 0.0,
                a.getClothStyleSimilarity() != null ? a.getClothStyleSimilarity() : 0.0));

        if (allMatchedImages.size() > recallOptions.getStyleRecallCount()) {
            allMatchedImages = allMatchedImages.subList(0, recallOptions.getStyleRecallCount());
        }

        // 候选项处理和维度向量构建
        buildCandidates(allMatchedImages, candidates);

        return candidates;
    }

    @NotNull
    private Set<Integer> getExcludeImageCaptionIds(List<Integer> excludeRetInSearchIds) {
        Set<Integer> excludeImageCaptionIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(excludeRetInSearchIds)) {
            SearchResultImgQuery query = new SearchResultImgQuery();
            query.setSearchIds(excludeRetInSearchIds);
            List<SearchResultImgVO> excludeResults = searchResultImgService.querySearchResultImgList(query);
            excludeImageCaptionIds
                    .addAll(excludeResults.stream().map(SearchResultImgVO::getImageCaptionId).distinct().toList());
        }
        return excludeImageCaptionIds;
    }

    private void buildCandidates(List<ImageCaptionVO> allMatchedImages, List<StyleImageCandidate> candidates) {

        for (ImageCaptionVO imageCaption : allMatchedImages) {
            // 解析图像标注
            ImageAnalysisCaption captionModel = imageCaption.getCaption();
            if (captionModel == null) {
                continue;
            }

            StyleImageCandidate candidate = new StyleImageCandidate();
            candidate.setImageCaption(imageCaption);
            candidate.setStyleSimilarity(imageCaption.getClothStyleSimilarity());

            // 计算其他维度向量
            buildDimensionVectorsMap(candidate);

            candidates.add(candidate);
        }
    }

    // 删除串行查询方法，只保留并行查询路径

    /**
     * 对每个流派并行进行MMR打散过滤
     */
    private Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifyByGenre(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups, ProcessedInput input,
            DiversifyOptions diversifyOptions) {

        Map<ClothShootGenreEnum, List<StyleImageCandidate>> result = new HashMap<>();

        // 创建并行任务列表
        List<CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>>> futures = new ArrayList<>();

        // 为每个流派创建并行MMR处理任务
        for (Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry : genreGroups.entrySet()) {
            ClothShootGenreEnum genre = entry.getKey();
            List<StyleImageCandidate> candidates = entry.getValue();

            if (CollectionUtils.isNotEmpty(candidates)) {
                CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>> future = CompletableFuture
                        .supplyAsync(AsyncContextHelper.wrapSupplier(() -> {
                            long genreStartTime = System.currentTimeMillis();
                            List<StyleImageCandidate> diversified = DiversifyUtil.diversifyByMMRWithWindow(candidates,
                                    input, diversifyOptions.getMmrLambda(), diversifyOptions.getMmrWindowSize());
                            long genreEndTime = System.currentTimeMillis();
                            log.info("【耗时统计】流派{}MMR打散完成，输入:{}条 → 输出:{}条，耗时:{}ms",
                                    genre.getDisplayName(), candidates.size(), diversified.size(),
                                    genreEndTime - genreStartTime);
                            return Map.entry(genre, diversified);
                        }), executorService);

                futures.add(future);
            }
        }

        // 等待所有任务完成并收集结果
        try {
            for (CompletableFuture<Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>>> future : futures) {
                Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry = future.get();
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    result.put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("流派并行MMR打散处理失败", e);
            throw new RuntimeException("流派并行MMR打散处理失败", e);
        }

        return result;
    }

    /**
     * 4. 按流派与背景聚类
     */
    private StyleImageSearchResult clusterByGenreAndBackground(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> diversifiedGenreGroups, ProcessedInput input,
            ClusterOptions clusterOptions, Integer searchId)
            throws Exception {

        // 确定流派列表
        Set<ClothShootGenreEnum> validGenres = getValidGenreQuotas(input);

        // 流派内按背景聚类并排序截断
        List<List<List<StyleImageRecommendation>>> resultItems = new ArrayList<>();

        // 创建并行任务列表
        List<CompletableFuture<CutByQuotaResult>> futures = new ArrayList<>();

        // 为每个流派创建并行处理任务
        for (Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry : diversifiedGenreGroups.entrySet()) {
            ClothShootGenreEnum genre = entry.getKey();
            List<StyleImageCandidate> genreCandidates = entry.getValue();
            if (CollectionUtils.isNotEmpty(genreCandidates) && validGenres.contains(genre)) {
                CompletableFuture<CutByQuotaResult> future = CompletableFuture
                        .supplyAsync(AsyncContextHelper.wrapSupplier(() -> {
                            long singleGenreStartTime = System.currentTimeMillis();
                            try {
                                // 按背景聚类并排序截断
                                CutByQuotaResult cutResult = clusterByBgInGenre(
                                        genreCandidates,
                                        genre, input, clusterOptions, searchId);
                                long singleGenreEndTime = System.currentTimeMillis();
                                log.info("【耗时统计】流派{}背景聚类完成，耗时：{}ms，生成{}个推荐",
                                        genre.getDisplayName(), singleGenreEndTime - singleGenreStartTime,
                                        cutResult != null && cutResult.getDisplayItems() != null
                                                ? cutResult.getDisplayItems().size()
                                                : 0);
                                return cutResult;
                            } catch (Exception e) {
                                log.error("流派{}背景聚类处理失败", genre.getDisplayName(), e);
                                return null;
                            }
                        }), executorService);

                futures.add(future);
            }
        }

        // 等待所有任务完成并收集结果
        for (CompletableFuture<CutByQuotaResult> future : futures) {
            CutByQuotaResult result = future.get();
            if (result != null && CollectionUtils.isNotEmpty(result.getDisplayItems())) {
                resultItems.add(result.getDisplayItems());
            }
        }

        StyleImageSearchResult result = new StyleImageSearchResult();
        result.setItems(resultItems);

        return result;
    }

    private String extractDimensionText(ImageAnalysisCaption caption, ImageMatchScope scope) {
        // 根据维度提取对应文本
        if (caption == null) {
            throw new IllegalArgumentException("Invalid caption model");
        }

        switch (scope) {
            case BG:
                if (caption.getShootingTheme() != null) {
                    return caption.getShootingTheme().getShootingScene();
                }
                break;
            case ACCESSORIES:
                if (caption.getClothing() != null) {
                    return caption.getClothing().getAccessories();
                }
                break;
            case MODEL_FACIAL:
                if (caption.getModel() != null) {
                    return caption.getModel().getHairstyle() + "," + caption.getModel().getFacialExpression();
                }
                break;
            case MODEL_POSE:
                if (caption.getModel() != null) {
                    return caption.getModel().getPosture();
                }
                break;
            case ALL: {
                StringBuilder sb = new StringBuilder();
                List<ImageMatchScope> otherScopes = List.of(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE);
                otherScopes.forEach(s -> {
                    String text = extractDimensionText(caption, s);
                    if (StringUtils.isNotBlank(text)) {
                        sb.append(text).append(" ");
                    }
                });
                return sb.toString();
            }
            default:
                throw new IllegalArgumentException("Invalid image match scope: " + scope);
        }

        return "";
    }

    private void buildDimensionVectorsMap(StyleImageCandidate candidate) {
        // 计算候选图片的各维度向量
        Map<String, PGvector> dimensionVectors = new HashMap<>();

        // 获取背景向量
        if (candidate.getImageCaption().getBgTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.BG.name(), candidate.getImageCaption().getBgTextEmb());
        }

        // 获取配饰向量
        if (candidate.getImageCaption().getAccessoriesTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.ACCESSORIES.name(),
                    candidate.getImageCaption().getAccessoriesTextEmb());
        }

        // 获取发型向量
        if (candidate.getImageCaption().getHairstyleTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_FACIAL.name(),
                    candidate.getImageCaption().getHairstyleTextEmb());
        }

        // 获取姿势向量
        if (candidate.getImageCaption().getPoseTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_POSE.name(), candidate.getImageCaption().getPoseTextEmb());
        }

        candidate.setDimensionVectors(dimensionVectors);
    }

    private Map<ClothShootGenreEnum, List<StyleImageCandidate>> groupByGenre(List<StyleImageCandidate> candidates,
                                                                             ProcessedInput input, ClothShootGenreEnum specifiedGenre) {
        // 按流派分组
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = new HashMap<>();

        Set<ClothShootGenreEnum> targetGenres = new HashSet<>();

        if (specifiedGenre != null) {
            // 如果指定了特定流派，只使用指定的流派
            targetGenres.add(specifiedGenre);
        } else {
            // 初始化默认流派组
            Set<ClothShootGenreEnum> defaultGenres = new HashSet<>();
            defaultGenres.add(ClothShootGenreEnum.LOOKBOOK);
            defaultGenres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
            defaultGenres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
            defaultGenres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

            // 确定扩展流派（用户参考图中的走秀和对镜自拍）
            Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
            if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
                for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                    ImageAnalysisCaption captionModel = refDesc.getImgAnalysis();
                    if (captionModel != null && captionModel.getShootingTheme() != null
                            && StringUtils.isNotBlank(captionModel.getShootingTheme().getGenre())) {

                        String genreStr = captionModel.getShootingTheme().getGenre();
                        ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                        if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                            extendedGenres.add(genreEnum);
                        }
                    }
                }
            }

            // 初始化所有流派组
            targetGenres = new HashSet<>(defaultGenres);
            targetGenres.addAll(extendedGenres);
        }

        for (ClothShootGenreEnum genre : targetGenres) {
            genreGroups.put(genre, new ArrayList<>());
        }

        // 将候选图片按流派分组
        for (StyleImageCandidate candidate : candidates) {
            ClothShootGenreEnum genreEnum = candidate.getGenreEnum();

            // 只有在允许的流派中才加入分组
            if (genreEnum != null && genreGroups.containsKey(genreEnum)) {
                genreGroups.get(genreEnum).add(candidate);
            }
        }

        return genreGroups;
    }

    private Set<ClothShootGenreEnum> getValidGenreQuotas(ProcessedInput input) {

        // 统计各类流派数量
        Set<ClothShootGenreEnum> genres = new HashSet<>();
        genres.add(ClothShootGenreEnum.LOOKBOOK);
        genres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
        genres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
        genres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

        Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
            for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                ImageAnalysisCaption caption = refDesc.getImgAnalysis();
                if (caption != null && caption.getShootingTheme() != null
                        && StringUtils.isNotBlank(caption.getShootingTheme().getGenre())) {
                    String genreStr = caption.getShootingTheme().getGenre();
                    ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                    if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                        extendedGenres.add(genreEnum);
                    }
                }
            }
        }

        genres.addAll(extendedGenres);

        return genres;
    }

    private CutByQuotaResult clusterByBgInGenre(
            List<StyleImageCandidate> candidates, ClothShootGenreEnum genre, ProcessedInput input,
            ClusterOptions clusterOptions, Integer searchId)
            throws Exception {

        log.info("【耗时统计】开始对流派{}进行背景聚类，候选图片数量：{}", genre.getDisplayName(), candidates.size());

        // 4.3.1 计算匹配度评分
        long matchScoreStartTime = System.currentTimeMillis();
        if (candidates.getFirst().getImageCaption().getImgEmb() == null) {
            imageCaptionService.fillSortVectors(candidates);
        }
        sortModelService.calculateMatchScoresBatch(candidates, input);
        long matchScoreEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}匹配度批量计算完成，耗时：{}ms", genre.getDisplayName(), matchScoreEndTime - matchScoreStartTime);

        if (candidates.isEmpty()) {
            log.warn("流派{}经过匹配度过滤后无候选图片", genre.getDisplayName());
            return new CutByQuotaResult(new ArrayList<>(), genre);
        }

        // 去除匹配分小于阈值0.3的图片（如果匹配分小于阈值0.3的图片数量小于图片数量一半，则去除匹配分小于阈值的图片，保护测试期数据量小的情况）
        double th = 0.3;
        int n = candidates.size();
        if (candidates.stream().filter(candidate -> candidate.getMatchScore() < th).count() < n / 2) {
            candidates = candidates.stream().filter(candidate -> candidate.getMatchScore() >= th).toList();
            log.info("【背景聚类-低匹配分过滤】流派{}进行低匹配分过滤后，th={}, 图片数量由{} -> {}，过滤掉{}张图片", genre.getDisplayName(), th, n,
                    candidates.size(), n - candidates.size());
        }

        // 4.3.2 背景聚类
        long clusteringStartTime = System.currentTimeMillis();
        List<List<StyleImageCandidate>> backgroundClusters = clusterByBg(candidates);
        long clusteringEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}背景聚类完成，生成{}个聚类，耗时：{}ms",
                genre.getDisplayName(), backgroundClusters.size(), clusteringEndTime - clusteringStartTime);

        if (backgroundClusters.isEmpty()) {
            log.warn("流派{}没有满足最小聚类大小要求的聚类", genre.getDisplayName());
            return new CutByQuotaResult(new ArrayList<>(), genre);
        }

        // 4.3.3 聚类排序（类内排序和类间排序）
        sortByScore(genre, backgroundClusters);

        // 4.3.4 聚类裁剪和缓存key生成
        return cutByQuota(genre, backgroundClusters, clusterOptions, searchId);
    }

    @NotNull
    private CutByQuotaResult cutByQuota(ClothShootGenreEnum genre,
                                        List<List<StyleImageCandidate>> bgClusters, ClusterOptions clusterOptions, Integer searchId) {

        // 4.3.4 聚类排序和配额分配
        List<List<StyleImageRecommendation>> items = new ArrayList<>();

        ImageQuery imageQuery = new ImageQuery();
        imageQuery.setIds(bgClusters.stream().flatMap(List::stream).map(c -> c.getImageCaption().getImageId())
                .collect(Collectors.toList()));
        List<ImageVO> imageVOList = imageService.queryImageList(imageQuery);
        Map<Integer, ImageVO> imageMap = imageVOList.stream().collect(Collectors.toMap(ImageVO::getId, v -> v));

        // 4.3.5 从聚类中选择最佳候选
        for (int cIdx = 0; cIdx < bgClusters.size() && cIdx < clusterOptions.getBgGroupCountLimit(); cIdx++) {
            List<StyleImageCandidate> cluster = bgClusters.get(cIdx);

            // 用户展示数据（前bgSzShow个）
            List<StyleImageRecommendation> displayItemsOfCluster = new ArrayList<>();

            // 添加聚类特征信息用于生成唯一标识 - 为每个背景聚类簇生成唯一的缓存key
            String bgClusterKey = buildBgClusterKey(searchId, genre, cIdx);

            for (int i = 0, selectedFromCluster = 0; i < cluster.size()
                    && selectedFromCluster < clusterOptions.getBgSzCountLimit(); i++) {
                StyleImageRecommendation item = convert2RecommendationItem(cluster.get(i), genre, bgClusterKey, i,
                        imageMap);
                if (item != null) {
                    // 只有前bgSzShow个添加到展示列表
                    if (selectedFromCluster < clusterOptions.getBgSzShow()) {
                        displayItemsOfCluster.add(item);
                    }
                    selectedFromCluster++;
                }
            }

            items.add(displayItemsOfCluster);
        }

        //todo 这里会java.lang.OutOfMemoryError
        // 将整个流派候选集合进行缓存，用于两个推荐更多功能快速返回（流派的更多、基于单图查看套图）
//        tairService.setObject(buildGenreCandidatesCacheKey(searchId, genre), bgClusters,
//                2 * CommonConstants.ONE_DAY_SECONDS);

        for (int i = 0; i < items.size(); i++) {
            log.info("【背景聚类-结果】流派{}聚类{}，展示大小：{}，前2平均分：{}，前5名分数:{}",
                    genre.getDisplayName(), i, items.get(i).size(),
                    String.format("%.3f",
                            items.get(i).stream().limit(2).mapToDouble(StyleImageRecommendation::getMatchScore)
                                    .average().orElse(0.0)),
                    items.get(i).stream().map(StyleImageRecommendation::getMatchScore).limit(5).toArray());
        }

        return new CutByQuotaResult(items, genre);
    }

    private String buildGenreCandidatesCacheKey(Integer searchId, ClothShootGenreEnum genre) {
        return String.format("%d_%s", searchId, genre.getCode());
    }

    private String buildBgClusterKey(Integer searchId, ClothShootGenreEnum genre, int clusterIdx) {
        return String.format("%d_%s_%d", searchId, genre.getCode(), clusterIdx);
    }

    /**
     * 从tair缓存中获取背景聚类数据
     *
     * @param cacheKey 缓存key
     * @return 缓存的背景聚类推荐数据，如果缓存不存在或已过期则返回null
     */
    public List<List<StyleImageCandidate>> getBgClusterFromCache(String cacheKey) {
        return tairService.getObject(cacheKey, List.class);
    }

    /**
     * 将StyleImageCandidate转换为ClothShootStyleImageRecommendationItem
     */
    private StyleImageRecommendation convert2RecommendationItem(
            StyleImageCandidate candidate, ClothShootGenreEnum genre, String bgClusterKey, int i,
            Map<Integer, ImageVO> imageMap) {
        try {
            StyleImageRecommendation item = new StyleImageRecommendation();
            item.setClothShootGenreEnum(genre);
            item.setImageId(candidate.getImageCaption().getImageId());
            item.setImageCaptionId(candidate.getImageCaption().getId());
            item.setBgClusterKey(bgClusterKey);
            item.setIdxInCluster(i);
            item.setMatchScore(candidate.getMatchScore());
            item.setStyleSimilarity(candidate.getStyleSimilarity());

            ImageVO imageVO = imageMap.get(candidate.getImageCaption().getImageId());
            if (imageVO != null) {
                item.setImageUrl(imageVO.getUrl());
                item.setImageShowUrl(imageVO.getShowImgUrl());
            }

            return item;
        } catch (Exception e) {
            log.error("转换推荐项失败，候选图片ID：{}", candidate.getImageCaption().getImageId(), e);
            return null;
        }
    }

    /**
     * 执行背景聚类
     */
    private List<List<StyleImageCandidate>> clusterByBg(List<StyleImageCandidate> candidates) {
        // 提取有效候选者和背景向量
        List<StyleImageCandidate> validCandidates = new ArrayList<>();
        List<double[]> vectorList = new ArrayList<>();

        for (StyleImageCandidate candidate : candidates) {
            PGvector bgVector = candidate.getImageCaption().getBgTextEmb();
            if (bgVector != null) {
                validCandidates.add(candidate);
                // 直接转换为double[]，避免中间变量
                float[] floatVector = bgVector.toArray();
                double[] doubleVector = new double[floatVector.length];
                for (int j = 0; j < floatVector.length; j++) {
                    doubleVector[j] = floatVector[j];
                }
                vectorList.add(doubleVector);
            }
        }

        AssertUtil.assertNotEmpty(validCandidates, "背景聚类:没有有效的候选者");

        // 转换为二维数组
        double[][] vectors = vectorList.toArray(new double[0][]);

        double[][] reducedVectors;

        // PCA降维（超过512维时）
        if (vectors[0].length >= 512) {
            long pcaStart = System.currentTimeMillis();
            reducedVectors = PCAUtil.performPCA(vectors, 128);
            log.info("PCA降维耗时：{}ms，维度：{} -> {}",
                    System.currentTimeMillis() - pcaStart, vectors[0].length, reducedVectors[0].length);
        } else {
            reducedVectors = vectors;
        }

        // HDBSCAN进行聚类
        return ClusteringUtil.performHDBSCANClustering(validCandidates, reducedVectors);
    }

    /**
     * 输出详细的耗时分析报告
     */
    private String logPerformanceReport(Map<String, Long> performanceMetrics, StyleImageSearchResult result) {
        StringBuilder report = new StringBuilder();
        report.append("\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");
        report.append("【服装拍摄风格图片推荐算法 - 耗时分析报告】\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");

        long totalTime = performanceMetrics.get("总耗时");

        // 1. 各环节耗时详情（按耗时排序）
        report.append("📊 各算法环节耗时详情：\n");
        performanceMetrics.entrySet().stream()
                .filter(entry -> !"总耗时".equals(entry.getKey()))
                .forEach(entry -> {
                    String stepName = entry.getKey();
                    Long duration = entry.getValue();
                    double percentage = (duration * 100.0) / totalTime;
                    String icon = percentage > 30 ? "🔴" : percentage > 15 ? "🟡" : "🟢";
                    report.append(
                            String.format("  %s %-15s: %6d ms (%5.1f%%)\n", icon, stepName, duration, percentage));
                });
        report.append(String.format("  └─ %-15s: %6d ms (100.0%%)\n", "总耗时", totalTime));

        return report.toString();
    }

    /**
     * 记录搜索推荐流水到SearchTask和SearchResultImg
     */
    private SearchTaskVO saveSearchResult(SearchTaskVO searchTask, StyleImageSearchRequest req,
                                          StyleImageSearchResult ret,
                                          Map<String, Long> perf) {

        // 1. 更新搜索任务状态和结果
        SearchTaskVO searchTaskVO = updateSearchTask(req, searchTask, ret, perf);

        // 2. 记录搜索结果图片到SearchResultImg
        saveSearchResultImgs(ret, searchTaskVO);

        return searchTaskVO;
    }

    private void saveSearchResultImgs(StyleImageSearchResult ret, SearchTaskVO searchTaskVO) {
        if (ret != null && CollectionUtils.isNotEmpty(ret.getItems())) {
            List<SearchResultImgVO> resultImgs = new ArrayList<>();
            for (List<List<StyleImageRecommendation>> genreList : ret.getItems()) {
                if (CollectionUtils.isNotEmpty(genreList)) {
                    for (List<StyleImageRecommendation> clusterList : genreList) {
                        if (CollectionUtils.isNotEmpty(clusterList)) {
                            for (StyleImageRecommendation recommendation : clusterList) {
                                resultImgs.add(getSearchResultImgVO(recommendation, searchTaskVO));
                            }
                        }
                    }
                }
            }

            // 批量插入搜索结果图片
            if (CollectionUtils.isNotEmpty(resultImgs)) {
                int insertedCount = searchResultImgService.batchInsert(resultImgs);
                log.info("批量插入搜索结果图片成功，插入数量: {}", insertedCount);
            }

            log.info("记录搜索推荐流水成功，taskId: {}, 结果图片数量: {}", searchTaskVO.getId(), resultImgs.size());
        }
    }

    /**
     * 创建初始搜索任务记录（init状态）
     */
    private SearchTaskVO createInitialSearchTask(StyleImageSearchRequest req) {
        SearchTaskVO searchTask = new SearchTaskVO();
        searchTask.setUserId(req.getUserId());
        searchTask.setUserSessionId(req.getUserSessionId());
        searchTask.setUserSessionTaskId(req.getUserSessionTaskId());

        // 设置衣服图片详情
        ClothImgItem newClothImgItem = BeanUtils.deepCopy(req.getClothImgItem());
        newClothImgItem.setClothAnalysis(null);
        searchTask.setClothImgDetail(CommonUtil.toJSONString(newClothImgItem));

        // 设置衣服分析结果
        if (req.getClothImgItem().getClothAnalysis() != null) {
            searchTask.setClothAnalysis(JSONObject.toJSONString(req.getClothImgItem().getClothAnalysis()));
        }

        // 设置参考图片详情
        if (CollectionUtils.isNotEmpty(req.getUserUploadStyleImgs())) {
            JSONArray refImgDetailArray = new JSONArray();
            JSONArray refImgAnalysisArray = new JSONArray();

            for (UserUploadStyleImg styleImg : req.getUserUploadStyleImgs()) {
                JSONObject refImgDetail = new JSONObject();
                refImgDetail.put("imgUrl", styleImg.getImgUrl());
                if (styleImg.getImageMatchScopeList() != null) {
                    refImgDetail.put("imageMatchScopeList", styleImg.getImageMatchScopeList());
                }
                refImgDetailArray.add(refImgDetail);

                if (styleImg.getImgAnalysis() != null) {
                    refImgAnalysisArray.add(styleImg.getImgAnalysis());
                }
            }

            searchTask.setRefImgDetail(refImgDetailArray.toJSONString());
            if (!refImgAnalysisArray.isEmpty()) {
                searchTask.setRefImgAnalysis(refImgAnalysisArray.toJSONString());
            }
        }

        // 设置扩展搜索参数
        StyleImageSearchRequest newParam = BeanUtils.deepCopy(req);
        newParam.setClothImgItem(null);
        newParam.setUserUploadStyleImgs(null);
        searchTask.setSearchOptions(CommonUtil.toJSONString(newParam));

        // 设置初始状态
        searchTask.setStatus(SearchTaskStatusEnum.INIT.getCode());

        return searchTaskService.insert(searchTask);
    }

    /**
     * 更新搜索任务记录（完成状态和搜索结果）
     */
    private SearchTaskVO updateSearchTask(StyleImageSearchRequest req, SearchTaskVO searchTask,
                                          StyleImageSearchResult ret,
                                          Map<String, Long> perf) {

        // 设置搜索汇总信息
        searchTask.setRetSummary(JSONObject.toJSONString(getSearchSummaryInfo(req, ret, perf)));

        // 设置搜索状态为完成
        searchTask.setStatus(SearchTaskStatusEnum.COMPLETED.getCode());

        searchTaskService.updateByIdSelective(searchTask);
        return searchTask;
    }








}
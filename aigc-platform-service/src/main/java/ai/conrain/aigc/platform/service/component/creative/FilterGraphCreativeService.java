package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.FilterGraphRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class FilterGraphCreativeService extends AbstractCreativeService<FilterGraphRequest> {

    @Autowired
    OssHelper ossHelper;


    @Override
    protected CreativeBatchVO buildData(FilterGraphRequest request, MaterialModelVO modelVO) throws IOException {
        // 上传封面到 oss
        String showImage = ossHelper.uploadBase64(request.getShowImage(), "image/jpeg");
        // 上传视频到 oss, 最大并行数量 4
        List<String> videoUrls = ossHelper.uploadParallel(request.getFiles(), OperationContextHolder.getMasterUserId(), 4);
        AssertUtil.assertTrue(!CollectionUtils.isEmpty(videoUrls) && (request.getFiles().size() == videoUrls.size()),
                ResultCode.BIZ_FAIL, "上传失败");

        CreativeBatchVO data = new CreativeBatchVO();
        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setType(getCreativeType());
        data.setShowImage(showImage);
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setImageProportion("NONE");
        data.setBatchCnt(videoUrls.size());
        data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        data.addExtInfo(KEY_VIDEO_URLS, videoUrls);
        data.addExtInfo(KEY_SECOND, request.getSecond());
        data.addExtInfo(KEY_FRAME, request.getFrame());
        data.addExtInfo(KEY_FILTER_LOW_QUALITY, request.getFilterLowQuality() ? YES : NO);

        return data;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        List<String> videoUrls = batch.getExtInfoList(KEY_VIDEO_URLS, String.class);
        AssertUtil.assertNotEmpty(videoUrls, ResultCode.BIZ_FAIL, "video urls is empty");
        AssertUtil.assertTrue(videoUrls.size() > idx, ResultCode.BIZ_FAIL, "video url is invalid");

        String videoUrl = videoUrls.get(idx);
        AssertUtil.assertNotBlank(videoUrl, ResultCode.BIZ_FAIL, "video url is blank");

        String second = batch.getExtInfo(KEY_SECOND);
        String frame = batch.getExtInfo(KEY_FRAME);
        AssertUtil.assertNotNull(second, ResultCode.BIZ_FAIL, "second info is empty");
        AssertUtil.assertNotNull(frame, ResultCode.BIZ_FAIL, "frame is empty");

        target.setBatchCnt(4);
        target.addExtInfo(KEY_VIDEO_URL, videoUrl);
        target.addExtInfo(KEY_SECOND, second);
        target.addExtInfo(KEY_FRAME, frame);
        target.addExtInfo(KEY_FILTER_LOW_QUALITY, batch.getExtInfo(KEY_FILTER_LOW_QUALITY));
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FILTER_GRAPH;
    }
}

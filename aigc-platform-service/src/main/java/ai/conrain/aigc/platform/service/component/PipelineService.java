package ai.conrain.aigc.platform.service.component;

import java.util.List;

import ai.conrain.aigc.platform.service.model.vo.PipelineVO;

/**
 * 服务管道 Service定义
 *
 * <AUTHOR>
 * @version PipelineService.java v 0.1 2024-06-15 04:58:30
 */
public interface PipelineService extends CachedService<PipelineVO, Integer, PipelineVO> {

    /**
     * 查询服务管道对象
     *
     * @param id 主键
     * @return 返回结果
     */
    PipelineVO selectById(Integer id);

    /**
     * 删除服务管道对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加服务管道对象
     *
     * @param pipeline 对象参数
     * @return 返回结果
     */
    PipelineVO insert(PipelineVO pipeline);

    /**
     * 修改服务管道对象
     *
     * @param pipeline 对象参数
     */
    void updateById(PipelineVO pipeline);

    /**
     * 根据用户id获取管道
     *
     * @param userId 用户id
     * @return 返回结果
     */
    PipelineVO fetchByUserId(Integer userId);

    /**
     * 获取当前管道关联的用户列表
     *
     * @param pipeline 管道
     * @return 关联用户列表
     */
    List<Integer> fetchRelatedUsers(PipelineVO pipeline);
}
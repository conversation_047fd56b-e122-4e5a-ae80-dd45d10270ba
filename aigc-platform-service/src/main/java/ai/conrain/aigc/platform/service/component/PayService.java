package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.request.CreateQrCodeRequest;
import ai.conrain.aigc.platform.service.model.vo.PayQRCode;
import ai.conrain.aigc.platform.service.model.vo.PayQueryRet;

public interface PayService {

    PayQRCode createPayQRCode(CreateQrCodeRequest request);

    PayQueryRet queryPayResult(String orderNo);

    boolean onPaySuccess(String orderNo);
}

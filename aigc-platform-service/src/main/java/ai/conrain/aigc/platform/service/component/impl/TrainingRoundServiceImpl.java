package ai.conrain.aigc.platform.service.component.impl;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.ModelValidationRequest;
import ai.conrain.aigc.platform.integration.ai.model.ModelValidationResponse;
import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationRequest;
import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationResponse;
import ai.conrain.aigc.platform.integration.ai.model.TrainModelResponse;
import ai.conrain.aigc.platform.integration.ai.model.TrainNextModelRequest;
import ai.conrain.aigc.platform.integration.ai.model.UploadFileResult;
import ai.conrain.aigc.platform.integration.ai.training.TrainingJobService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.constant.TairConstant;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.TrainingSampleService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.constants.RoundExtInfoKeys;
import ai.conrain.aigc.platform.service.enums.ImageTypeEnum;
import ai.conrain.aigc.platform.service.enums.TrainJobStatus;
import ai.conrain.aigc.platform.service.enums.TrainingSampleIdTypeConstant;
import ai.conrain.aigc.platform.service.helper.DingTalkNoticeHelper;
import ai.conrain.aigc.platform.service.model.common.FilePathInfo;
import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.ModelSceneEnum;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundStatus;
import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;
import ai.conrain.aigc.platform.service.model.vo.training.TrainingSamplesModel;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.FilePathParseUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingRoundDO;
import ai.conrain.aigc.platform.dal.example.TrainingRoundExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TrainingRoundQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingRoundVO;
import ai.conrain.aigc.platform.service.model.converter.TrainingRoundConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.TrainingRoundDAO;
import ai.conrain.aigc.platform.service.component.TrainingRoundService;
import org.springframework.transaction.annotation.Transactional;

/**
 * TrainingRoundService实现
 *
 * <AUTHOR>
 * @version TrainingRoundService.java
 */
@Slf4j
@Service
public class TrainingRoundServiceImpl implements TrainingRoundService {

	/** DAO */
	@Autowired
	private TrainingRoundDAO trainingRoundDAO;

    @Autowired
    private TrainingJobService trainingJobService;

    @Autowired
    private TairService tairService;

    @Autowired
    private ComfyUIService comfyUIService;

    @Autowired
    private ServerHelper serverHelper;

    @Value("${ai.training.fileServerUrl}")
    private String trainingJobFileServiceUrl;

    @Autowired
    private TrainingSampleService trainingSampleService;

    @Autowired
    private ImageService imageService;

	@Override
	public TrainingRoundVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		TrainingRoundDO data = trainingRoundDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return TrainingRoundConverter.do2VO(data);
	}

    @Override
    public TrainingRoundVO lockById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TrainingRoundDO data = trainingRoundDAO.lockByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TrainingRoundConverter.do2VO(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        trainingSampleService.deleteByTrainingRoundId(id);

        int n = trainingRoundDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TrainingRound失败");
	}

	@Override
	public TrainingRoundVO insert(TrainingRoundVO trainingRound) {
		AssertUtil.assertNotNull(trainingRound, ResultCode.PARAM_INVALID, "trainingRound is null");
		AssertUtil.assertTrue(trainingRound.getId() == null, ResultCode.PARAM_INVALID, "trainingRound.id is present");

		//创建时间、修改时间兜底
		if (trainingRound.getCreateTime() == null) {
			trainingRound.setCreateTime(new Date());
		}

		if (trainingRound.getModifyTime() == null) {
			trainingRound.setModifyTime(new Date());
		}

		TrainingRoundDO data = TrainingRoundConverter.vo2DO(trainingRound);
		Integer n = trainingRoundDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TrainingRound失败");
		AssertUtil.assertNotNull(data.getId(), "新建TrainingRound返回id为空");
		trainingRound.setId(data.getId());
		return trainingRound;
	}


	@Override
	public void updateByIdSelective(TrainingRoundVO trainingRound) {
		AssertUtil.assertNotNull(trainingRound, ResultCode.PARAM_INVALID, "trainingRound is null");
    	AssertUtil.assertTrue(trainingRound.getId() != null, ResultCode.PARAM_INVALID, "trainingRound.id is null");

		//修改时间必须更新
		trainingRound.setModifyTime(new Date());
		TrainingRoundDO data = TrainingRoundConverter.vo2DO(trainingRound);
		int n = trainingRoundDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TrainingRound失败，影响行数:" + n);
	}

	@Override
	public List<TrainingRoundVO> queryTrainingRoundList(TrainingRoundQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingRoundExample example = TrainingRoundConverter.query2Example(query);

		List<TrainingRoundDO> list = trainingRoundDAO.selectByExample(example);
		return TrainingRoundConverter.doList2VOList(list);
	}

	@Override
	public Long queryTrainingRoundCount(TrainingRoundQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingRoundExample example = TrainingRoundConverter.query2Example(query);
		return trainingRoundDAO.countByExample(example);
	}

	/**
	 * 带条件分页查询训练轮次表，记录每轮训练的详细信息和作业状态
	 */
	@Override
	public PageInfo<TrainingRoundVO> queryTrainingRoundByPage(TrainingRoundQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<TrainingRoundVO> page = new PageInfo<>();

		TrainingRoundExample example = TrainingRoundConverter.query2Example(query);
		long totalCount = trainingRoundDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<TrainingRoundDO> list = trainingRoundDAO.selectByExample(example);
		page.setList(TrainingRoundConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startSelectSamples(Integer roundId) {
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, ResultCode.PARAM_INVALID, "轮次不存在");

        //删除当前轮次下的所有样本
        trainingSampleService.deleteByTrainingRoundId(roundId);

        //创建挑选样本任务
        SamplePreparationRequest samplePreparationRequest = new SamplePreparationRequest();

        String taskId = genTaskId(roundId, "select_samples");
        samplePreparationRequest.setTaskId(taskId);
        samplePreparationRequest.setModelScene(round.getModelScene());
        samplePreparationRequest.setDataSource("local");

        AssertUtil.assertNotBlank(round.getModelFilePath(), ResultCode.PARAM_INVALID, "当前轮次不存在模型文件");
        samplePreparationRequest.setModelDir(round.getModelFilePath());

        SamplePreparationRequest.Config cfg = getConfig4SampleSelect(round);
        samplePreparationRequest.setConfig(cfg);

        trainingJobService.prepareSamples(samplePreparationRequest);

        //更新round进展
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(roundId);
        target.setStatus(TrainingRoundStatus.sampling);
        JSONObject extInfo = new JSONObject();
        extInfo.putAll(round.getExtInfo());
        extInfo.put(RoundExtInfoKeys.PREPARE_SAMPLES_TASK_ID, taskId);
        target.setExtInfo(extInfo);

        updateByIdSelective(target);

        tairService.setString(taskId, roundId.toString(), TairConstant.oneDayInSecs);
    }

    private static SamplePreparationRequest.Config getConfig4SampleSelect(TrainingRoundVO round) {
        SamplePreparationRequest.Config cfg = new SamplePreparationRequest.Config();
        cfg.setTargetSamples(round.getTrainingConfig().getSamplesNum());
        SamplePreparationRequest.RuleFilter ruleFilter = new SamplePreparationRequest.RuleFilter();
        ruleFilter.setRemoveMultiPerson(round.getTrainingConfig().getRemoveMultiPerson());
        ruleFilter.setRemovePersonWithHeightRatio(round.getTrainingConfig().getRemovePersonWithHeightRatio());
        cfg.setRuleFilter(ruleFilter);

        cfg.setSamplingStrategy("voting");
        cfg.setVotingLabels(List.of("all"));

        return cfg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onLabelSamplesFinished(SamplePreparationResponse notify) {

        //检查响应是否正常
        AssertUtil.assertNotNull(notify, "响应为空");
        AssertUtil.assertTrue(notify.getStatus().equals("success"), "响应状态异常");
        AssertUtil.assertNotBlank(notify.getOutputFile(), "响应outputFile为空");

        AssertUtil.assertNotBlank(trainingJobFileServiceUrl, "fileServerUrl为空");

        boolean existed = comfyUIService.checkFileExists(notify.getOutputFile(), trainingJobFileServiceUrl);
        AssertUtil.assertTrue(existed, "采样结果中指定的文件不存在:" + notify.getOutputFile());

        AssertUtil.assertNotNull(notify.getTaskId(), "响应taskId为空");

        String roundIdStr = tairService.getString(notify.getTaskId());
        AssertUtil.assertNotBlank(roundIdStr, "roundId不存在");

        TrainingRoundVO round = lockById(Integer.parseInt(roundIdStr));
        AssertUtil.assertNotNull(round, "轮次不存在");

        //获取outputFile对应的文件内容，解析为样本列表，落入TrainingSample表
        FilePathInfo filePathInfo = FilePathParseUtil.parseFilePath(notify.getOutputFile());
        String fileContent = comfyUIService.fetchFileContent(filePathInfo.directory(), filePathInfo.fileName(),filePathInfo.extensionNoDot(), trainingJobFileServiceUrl);

        parseAndCreateSamples(notify, round, fileContent);

        //发钉钉通知，可以开始打标 todo测试完后，改为洛奇、小洋、路微等
        DingTalkNoticeHelper.sendMsg2DevGroup(String.format("%s-轮次%d-已完成采样，请开始打标",
                Objects.requireNonNull(ModelSceneEnum.getByCode(round.getModelScene())).getDesc(),
                round.getRoundNumber()), List.of("15906660486"));
    }

    @Override
    public void querySelectedSampleResult(Integer roundId) {
        AssertUtil.assertNotNull(roundId, "轮次id为空");
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "轮次不存在");

        String taskId = round.getExtInfo().getString(RoundExtInfoKeys.PREPARE_SAMPLES_TASK_ID);
        AssertUtil.assertNotBlank(taskId, "taskId为空");

        SamplePreparationResponse samplePreparationResponse = trainingJobService.querySamplePreparation(taskId);
        if (samplePreparationResponse != null && samplePreparationResponse.getStatus().equals("success")){
            this.onLabelSamplesFinished(samplePreparationResponse);
        }
    }

    private void parseAndCreateSamples(SamplePreparationResponse notify, TrainingRoundVO round, String fileContent) {
        parseFileContentAndCreateSamples(round, fileContent);
        updateRoundStatus2Labeling(notify, round);
    }

    private void updateRoundStatus2Labeling(SamplePreparationResponse notify, TrainingRoundVO round) {
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(round.getId());
        target.setStatus(TrainingRoundStatus.labeling);
        JSONObject extInfo = new JSONObject();
        extInfo.putAll(round.getExtInfo());
        extInfo.put(RoundExtInfoKeys.PREPARE_SAMPLES_RESULT, notify);
        target.setExtInfo(extInfo);

        updateByIdSelective(target);
    }

    private void parseFileContentAndCreateSamples(TrainingRoundVO round, String content) {
        TrainingSamplesModel samplesModel = JSONObject.parseObject(content, TrainingSamplesModel.class);
        AssertUtil.assertNotNull(samplesModel, "json文件内容解析失败");
        AssertUtil.assertNotEmpty(samplesModel.getRecords(), "json文件内容解析为空");

        if (CollectionUtils.isNotEmpty(samplesModel.getRecords())) {
            for (TrainingSamplesModel.SampleRecordItem record : samplesModel.getRecords()) {
                String imagePath = record.getImagePath();
                if (StringUtils.isNotBlank(imagePath)){
                    ImageVO img = imageService.getByHashOrPath(null, imagePath);
                    if (img == null) {
                        log.warn("image not found: {}", imagePath);
                        UploadFileResult uploadResult = comfyUIService.uploadToOss(imagePath, FilePathParseUtil.getFileNameWithExtension(imagePath), trainingJobFileServiceUrl);
                        if (uploadResult != null && StringUtils.isNotBlank(uploadResult.getUrl())){
                            img = new ImageVO();
                            img.setUrl(uploadResult.getUrl());
                            img.setImagePath(imagePath);
                            img.setImageHash(uploadResult.getMd5());
                            img.setType(ImageTypeEnum.SCENE.getCode());

                            img = imageService.insert(img);

                            log.info("sync image success: {},{}", img.getId(), imagePath);
                        }
                    }

                    TrainingSampleQuery trainingSampleQuery = new TrainingSampleQuery();
                    trainingSampleQuery.setSampleDataId(img.getId());
                    trainingSampleQuery.setSampleDataIdType(TrainingSampleIdTypeConstant.IMAGE_ID);
                    trainingSampleQuery.setRelatedTrainingId(round.getId());
                    trainingSampleQuery.setRelatedTrainingIdType(TrainingSampleIdTypeConstant.ROUND_ID);
                    if (trainingSampleService.queryTrainingSampleCount(trainingSampleQuery) > 0){
                        continue;
                    }

                    TrainingSampleVO sample = new TrainingSampleVO();
                    sample.setSampleDataId(img.getId());
                    sample.setSampleDataIdType(TrainingSampleIdTypeConstant.IMAGE_ID);
                    sample.setRelatedTrainingId(round.getId());
                    sample.setRelatedTrainingIdType(TrainingSampleIdTypeConstant.ROUND_ID);

                    trainingSampleService.insert(sample);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startVerifyModelPerformance(Integer roundId) {
        //获取轮次信息
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "轮次不存在");
        AssertUtil.assertNotEmpty(round.getExtInfo(), "轮次扩展信息为空");

        //获取轮次关联的样本列表
        List<TrainingSampleVO> samples = trainingSampleService.getLabeledSampleList(roundId);
        AssertUtil.assertTrue(CollectionUtils.isNotEmpty(samples), "本轮打标的样本列表为空");

        //将样本列表转换成模型验证任务需要的数据格式，并写成json文件发送到模型训练服务器
        TrainingSamplesModel trainingSamplesModel = getTrainingSamplesModel(samples);

        String labeledSampleFilePath = String.format("/mnt/vdb/dev/data_hub/active/%s/label/%s_%s_labeled_samples.json", round.getModelScene(), round.getTaskId(), round.getRoundNumber());

        String samplesContent = JSONObject.toJSONString(trainingSamplesModel);
        boolean success = comfyUIService.writeToFile(labeledSampleFilePath, samplesContent, trainingJobFileServiceUrl);
        AssertUtil.assertTrue(success, "打标结果写入失败");

        //创建模型验证任务
        String modelValidateTaskId = startVerify(round, labeledSampleFilePath);

        //更新本轮次状态
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(round.getId());
        target.setStatus(TrainingRoundStatus.evaluating);
        target.setTrainingSampleExport(labeledSampleFilePath);

        JSONObject extInfo = new JSONObject();
        extInfo.putAll(round.getExtInfo());
        extInfo.put(RoundExtInfoKeys.MODEL_VALIDATION_TASK_ID, modelValidateTaskId);
        target.setExtInfo(extInfo);

        this.updateByIdSelective(target);

        log.info("模型验证完成，id:{}", round.getId());

        //save to tair
        tairService.setString(modelValidateTaskId, round.getId().toString(), TairConstant.oneDayInSecs);
    }

    private String genTaskId(Integer roundId, String prefix){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return String.format("%s_%d_%s", prefix, roundId, sdf.format(new Date()));
    }

    @NotNull
    private String startVerify(TrainingRoundVO round, String labeledSampleFilePath) {
        ModelValidationRequest modelValidationRequest = new ModelValidationRequest();

        String taskId = genTaskId(round.getId(), "model_verify");

        modelValidationRequest.setTaskId(taskId);
        modelValidationRequest.setModelScene(round.getModelScene());
        modelValidationRequest.setModelDir(round.getModelFilePath());
        modelValidationRequest.setDataSource(labeledSampleFilePath);
        modelValidationRequest.setClassificationLabel(List.of("all"));

        //distributionTotal从模型样本回传结果里取
        AssertUtil.assertTrue(round.getExtInfo() != null && round.getExtInfo().containsKey(RoundExtInfoKeys.PREPARE_SAMPLES_RESULT), "轮次信息异常，需要先挑选样本");
        SamplePreparationResponse sampleTaskResult = round.getExtInfo().getObject(RoundExtInfoKeys.PREPARE_SAMPLES_RESULT, SamplePreparationResponse.class);
        modelValidationRequest.setDistributionTotal(sampleTaskResult.getDistributionTotal());

        trainingJobService.validateModel(modelValidationRequest);
        return taskId;
    }
    @NotNull
    private static TrainingSamplesModel getTrainingSamplesModel(List<TrainingSampleVO> samples) {
        TrainingSamplesModel trainingSamplesModel = new TrainingSamplesModel();
        List<TrainingSamplesModel.SampleRecordItem> records = new ArrayList<>();

        for (TrainingSampleVO sample : samples) {
            TrainingSamplesModel.SampleRecordItem recordItem = new TrainingSamplesModel.SampleRecordItem();
            recordItem.setSampleId(sample.getId());
            if (sample.getImageVO() != null) {
                recordItem.setImagePath(sample.getImageVO().getImagePath());
                recordItem.setImageHash(sample.getImageVO().getImageHash());
            }
            if (sample.getCaption() != null){
                List<String> label = new ArrayList<>();

                //这里只考虑标签为Y的key作为label，暂时不存在其它情况，仅日志监控作为提醒
                for (Map.Entry<String, Object> e : sample.getCaption().entrySet()) {
                    if (e.getValue() instanceof String val){
                        if (StringUtils.equalsIgnoreCase(val,"Y")){
                            label.add(e.getKey());
                        } else {
                            log.error("label value is not Y, {}:{}", e.getKey(), val);
                        }
                    } else {
                        log.error("label value is not String, {}:{}", e.getKey(), e.getValue());
                    }
                }

                recordItem.setLabel(label);
            }

            records.add(recordItem);
        }

        trainingSamplesModel.setRecords(records);
        return trainingSamplesModel;
    }

    @Override
    public void onModelPerformanceVerified(ModelValidationResponse modelValidationResponse) {
        AssertUtil.assertNotNull(modelValidationResponse, "modelValidationResponse is null");
        AssertUtil.assertNotNull(modelValidationResponse.getTaskId(), "modelValidationResponse.taskId is null");
        AssertUtil.assertTrue(StringUtils.equals(modelValidationResponse.getStatus(), "success"), "modelValidationResponse.status is not success");

        String roundIdStr = tairService.getString(modelValidationResponse.getTaskId());
        AssertUtil.assertNotNull(roundIdStr, "roundIdStr is null");
        Integer roundId = Integer.parseInt(roundIdStr);

        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "round is null");

        //更新本轮的模型验证结果
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(round.getId());
        target.setModelPerformanceMetrics(JSONObject.toJSONString(modelValidationResponse.getData()));
        target.setStatus(TrainingRoundStatus.evaluate_review);
        JSONObject extInfo = new JSONObject();
        extInfo.putAll(round.getExtInfo());
        extInfo.put(RoundExtInfoKeys.MODEL_VALIDATION_RESULT, modelValidationResponse);
        target.setExtInfo(extInfo);

        updateByIdSelective(target);

        //notify by dingtalk
        DingTalkNoticeHelper.sendMsg2DevGroup("模型验证完成，请查看指标，是否继续下一轮训练", List.of("15906660486"));
    }

    @Override
    public void queryModelPerformanceResult(Integer roundId) {
        AssertUtil.assertNotNull(roundId, "轮次id为空");
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "轮次不存在");

        AssertUtil.assertTrue(round.getExtInfo() != null && round.getExtInfo().containsKey(RoundExtInfoKeys.MODEL_VALIDATION_TASK_ID), "轮次扩展异常，需要先验证模型");

        String taskId = round.getExtInfo().getString(RoundExtInfoKeys.MODEL_VALIDATION_TASK_ID);
        AssertUtil.assertNotBlank(taskId, "model validate taskId为空");

        ModelValidationResponse modelValidationResponse = trainingJobService.queryValidateModel(taskId);
        if (modelValidationResponse != null && modelValidationResponse.getStatus().equals("success")){
            this.onModelPerformanceVerified(modelValidationResponse);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startTrainJob(Integer roundId) {
        //fetch current round by roundId
        AssertUtil.assertNotNull(roundId, "roundId is null");
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "round is null");

        AssertUtil.assertNotNull(round.getModelPerformanceMetrics(), "round.modelPerformanceMetrics is null");

        //create next round based on current round
        TrainingRoundVO nextRound = new TrainingRoundVO();
        nextRound.setTaskId(round.getTaskId());
        nextRound.setModelScene(round.getModelScene());
        nextRound.setRoundNumber(round.getRoundNumber() + 1);
        nextRound.setStatus(TrainingRoundStatus.training);
        nextRound.setTrainingConfig(round.getTrainingConfig());
        nextRound.setTrainStartedTime(new Date());

        nextRound = this.insert(nextRound);

        //start train next round
        String trainTaskId = genTaskId(roundId, "train");
        doTrainJob(trainTaskId, round);

        tairService.setString(trainTaskId, nextRound.getId().toString(), TairConstant.oneDayInSecs);

        //update trainTaskId for next round
        TrainingRoundVO target4NextRound = new TrainingRoundVO();
        target4NextRound.setId(nextRound.getId());
        JSONObject extInfo = new JSONObject();
        extInfo.putAll(nextRound.getExtInfo());
        extInfo.put(RoundExtInfoKeys.TRAIN_MODEL_TASK_ID, trainTaskId);
        target4NextRound.setExtInfo(extInfo);
        updateByIdSelective(target4NextRound);

        //update current round status to completed
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(round.getId());
        target.setStatus(TrainingRoundStatus.completed);
        updateByIdSelective(target);
    }

    private void doTrainJob(String trainTaskId, TrainingRoundVO round) {
        TrainNextModelRequest trainNextModelRequest = new TrainNextModelRequest();
        trainNextModelRequest.setTaskId(trainTaskId);

        //模型导出的文件路径
        AssertUtil.assertNotBlank(round.getTrainingSampleExport(), "round.trainingSampleExport is null");
        trainNextModelRequest.setDataSource(round.getTrainingSampleExport());

        TrainNextModelRequest.TaskConfig taskConfig = new TrainNextModelRequest.TaskConfig();
        taskConfig.setModelScene(round.getModelScene());
        taskConfig.setModelDir(round.getModelFilePath());
        taskConfig.setTrainingWay(round.getTrainingConfig().getTrainingWay());
        trainNextModelRequest.setTaskConfig(taskConfig);

        TrainNextModelRequest.TrainingConfig trainingConfig = new TrainNextModelRequest.TrainingConfig();
        trainingConfig.setEpochs(round.getTrainingConfig().getEpochs());
        trainingConfig.setBatchSize(round.getTrainingConfig().getBatchSize());
        trainingConfig.setLearningRate(round.getTrainingConfig().getLearningRate());
        trainNextModelRequest.setTrainingConfig(trainingConfig);

        trainingJobService.trainNextModel(trainNextModelRequest);
    }

    @Override
    public void queryTrainJobResult(Integer roundId) {
        AssertUtil.assertNotNull(roundId, "轮次id为空");
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "轮次不存在");

        String taskId = round.getExtInfo().getString(RoundExtInfoKeys.TRAIN_MODEL_TASK_ID);
        AssertUtil.assertNotBlank(taskId, "taskId为空");

        TrainModelResponse trainModelResponse = trainingJobService.queryTrainNextModel(taskId);
        if (trainModelResponse != null && trainModelResponse.getStatus().equals("success")){
            this.onTrainJobFinished(trainModelResponse);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onTrainJobFinished(TrainModelResponse trainModelResponse) {
        AssertUtil.assertNotNull(trainModelResponse, "trainModelResponse is null");
        AssertUtil.assertNotNull(trainModelResponse.getTaskId(), "trainModelResponse.taskId is null");
        AssertUtil.assertTrue(StringUtils.equals(trainModelResponse.getStatus(), "success"), "trainModelResponse.status is not success");
        AssertUtil.assertNotNull(trainModelResponse.getModelSaveDir(), "trainModelResponse.modelSaveDir is null");

        String roundIdStr = tairService.getString(trainModelResponse.getTaskId());
        AssertUtil.assertNotNull(roundIdStr, "roundIdStr is null");
        Integer roundId = Integer.parseInt(roundIdStr);
        TrainingRoundVO round = lockById(roundId);
        AssertUtil.assertNotNull(round, "round is null");

        //更新本轮的模型训练结果
        TrainingRoundVO target = new TrainingRoundVO();
        target.setId(round.getId());
        target.setModelFilePath(trainModelResponse.getModelSaveDir());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        target.setModelVersion(String.format("%s.%s.%d", sdf.format(new Date()), round.getTaskId(), round.getRoundNumber()));
        target.setTrainStatus(TrainJobStatus.success.getCode());
        target.setTrainCompletedTime(new Date());
        target.setStatus(TrainingRoundStatus.sampling);
        JSONObject extInfo = new JSONObject();
        extInfo.putAll(round.getExtInfo());
        extInfo.put(RoundExtInfoKeys.TRAIN_MODEL_RESULT, trainModelResponse);
        target.setExtInfo(extInfo);

        updateByIdSelective(target);

        //自动发起本轮采样
        log.info("模型训练完成，自动发起本轮采样，roundId:{}", roundId);
        startSelectSamples(round.getId());
    }
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;

import ai.conrain.aigc.platform.service.enums.ModelVersionEnum;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

/**
 * 模型版本序列化器
 *
 * <AUTHOR>
 * @version : ModelVersionSerializer.java, v 0.1 2024/9/12 15:16 renxiao.wu Exp $
 */
public class ModelVersionSerializer extends JsonSerializer<ModelVersionEnum> {

    @Override
    public void serialize(ModelVersionEnum modelVersionEnum, JsonGenerator jsonGenerator,
                          SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeString(modelVersionEnum.getCode());
    }
}

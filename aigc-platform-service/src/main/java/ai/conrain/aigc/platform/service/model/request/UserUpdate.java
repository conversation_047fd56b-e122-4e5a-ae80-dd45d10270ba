/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.validation.EnumValid;
import ai.conrain.aigc.platform.service.validation.Mobile;

import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 用户更新模型
 *
 * <AUTHOR>
 * @version : UserUpdate.java, v 0.1 2023/9/8 17:54 renxiao.wu Exp $
 */
@Data
public class UserUpdate implements Serializable {
    private static final long serialVersionUID = 6185563864525015077L;
    /** 用户id */
    @NotNull(message = "用户id不能为空")
    private Integer userId;

    /** 手机号 */
    @Mobile(nullable = true)
    private String mobile;

    /** 昵称 */
    @Size(min = 2, max = 50, message = "昵称长度必须在2-50之间")
    private String nickName;

    /** 角色类型 */
    @EnumValid(value = RoleTypeEnum.class, nullable = true)
    private String roleType;

    //@see ai.conrain.aigc.platform.service.enums.UserStatusEnum
    private String status;

    private String memo;

    private String corpName;

    private Integer promptUserId;
}

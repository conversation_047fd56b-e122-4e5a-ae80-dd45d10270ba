package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.WorkScheduleDO;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.dal.example.WorkScheduleExample;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * WorkScheduleConverter
 *
 * @version WorkScheduleService.java v 0.1 2025-04-01 03:40:32
 */
public class WorkScheduleConverter {

    /**
     * DO -> VO
     */
    public static WorkScheduleVO do2VO(WorkScheduleDO from) {
        WorkScheduleVO to = new WorkScheduleVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setStartTime(from.getStartTime());
        to.setEndTime(from.getEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static WorkScheduleDO vo2DO(WorkScheduleVO from) {
        WorkScheduleDO to = new WorkScheduleDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setStartTime(from.getStartTime());
        to.setEndTime(from.getEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static WorkScheduleQuery do2Query(WorkScheduleDO from) {
        WorkScheduleQuery to = new WorkScheduleQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setStartTime(from.getStartTime());
        to.setEndTime(from.getEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static WorkScheduleDO query2DO(WorkScheduleQuery from) {
        WorkScheduleDO to = new WorkScheduleDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setStartTime(from.getStartTime());
        to.setEndTime(from.getEndTime());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static WorkScheduleExample query2Example(WorkScheduleQuery from) {
        WorkScheduleExample to = new WorkScheduleExample();
        WorkScheduleExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getStartTime())) {
            c.andStartTimeGreaterThanOrEqualTo(from.getStartTime());
        }
        if (!ObjectUtils.isEmpty(from.getEndTime())) {
            c.andEndTimeLessThanOrEqualTo(from.getEndTime());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<WorkScheduleVO> doList2VOList(List<WorkScheduleDO> list) {
        return CommonUtil.listConverter(list, WorkScheduleConverter::do2VO);
    }
}
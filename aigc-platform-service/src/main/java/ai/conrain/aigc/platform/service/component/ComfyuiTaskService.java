package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.LabelExtTagsDetail;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ComfyuiTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;

import java.util.List;

/**
 * comfyui任务 Service定义
 *
 * <AUTHOR>
 * @version ComfyuiTaskService.java v 0.1 2024-05-30 04:05:20
 */
public interface ComfyuiTaskService {

    /**
     * 查询comfyui任务对象
     *
     * @param id 主键
     * @return 返回结果
     */
    ComfyuiTaskVO selectById(Integer id);

    ComfyuiTaskVO insert(ComfyuiTaskVO task);

    /**
     * 删除comfyui任务对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加comfyui任务对象
     */
    ComfyuiTaskVO createTask(ComfyuiTaskVO comfyuiTask);
    /**
     * 查询任务状态
     */
    ComfyuiTaskVO pollingTaskStatus(Integer taskId);

    /**
     * 重试任务
     */
    ComfyuiTaskVO retryTask(ComfyuiTaskVO task, boolean needNotify);

    /**
     * 修改comfyui任务对象
     *
     * @param comfyuiTask 对象参数
     */
    void updateByIdSelective(ComfyuiTaskVO comfyuiTask);

    /**
     * 带条件批量查询comfyui任务列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<ComfyuiTaskVO> queryComfyuiTaskList(ComfyuiTaskQuery query);

    /**
     * 带条件查询comfyui任务数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryComfyuiTaskCount(ComfyuiTaskQuery query);

    /**
     * 带条件分页查询comfyui任务
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<ComfyuiTaskVO> queryComfyuiTaskByPage(ComfyuiTaskQuery query);

    /**
     * 根据id列表批量查询
     *
     * @param ids id列表
     * @return 结果
     */
    List<ComfyuiTaskVO> batchQueryByIds(List<Integer> ids);

    /**
     * 根据打标结果获取服装对应正面图url，作为服装高清封面使用
     *
     * @param task
     * @return
     */
    String getFullBodyFrontImgUrlByLabelRet(ComfyuiTaskVO task);

    /**
     * 加载扩展标签
     *
     * @param id taskId
     * @return 扩展标签
     */
    List<LabelExtTagsDetail> loadExtTags(Integer id);
}
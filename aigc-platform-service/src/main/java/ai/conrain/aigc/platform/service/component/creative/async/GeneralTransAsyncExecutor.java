package ai.conrain.aigc.platform.service.component.creative.async;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.aliyun.TextModerationService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.PartialRedrawRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_REDRAW_DESC;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TRANS_REDRAW_DESC;

@Slf4j
@Service("generalTransAsyncExecutor")
public class GeneralTransAsyncExecutor extends AbstractBatchToAsyncExecutor<PartialRedrawRequest, String>{

    @Autowired
    ComfyUIService comfyUIService;
    @Autowired
    private TextModerationService textModerationService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ServerHelper serverHelper;

    @Override
    protected Object buildOriginValue(PartialRedrawRequest request) {

        if (StringUtils.isBlank(request.getRedrawDesc())) {
            return null;
        }
        AssertUtil.assertTrue(moderate(request.getRedrawDesc()), ResultCode.TEXT_MODERATION,
            "您输入的文本中包含敏感词，请优化");
        return request.getRedrawDesc();
    }

    @Override
    protected String buildTransValue(CreativeTaskVO task, String origin) {
        String fileServerUrl = serverHelper.getFileServerUrlByUser(task.getUserId());
        JSONObject trans = comfyUIService.generalTranslate(origin, fileServerUrl);
        AssertUtil.assertNotNull(trans, ResultCode.BIZ_FAIL, "重绘描述翻译失败");
        log.info("重绘描述翻译成功，翻译结果: {}", trans);
        return trans.getString("result");
    }

    @Override
    public String getOriginKey() {
        return KEY_ORIGIN_REDRAW_DESC;
    }

    @Override
    public String getTransKey() {
        return KEY_TRANS_REDRAW_DESC;
    }
    @Override
    protected Class<String> getModelClass() {
        return String.class;
    }

    /**
     * 文本安全校验
     *
     * @param text 文本内容
     * @return true审核通过
     */
    private boolean moderate(String text) {
        if (StringUtils.isBlank(text)) {
            return true;
        }
        List<String> labels = textModerationService.textModeratePlus(text);
        if (CollectionUtils.isEmpty(labels)) {
            log.info("文本内容安全审核通过，审核结果为空");
            return true;
        }

        List<String> configLabels = systemConfigService.queryTextModerationForbidLabel();
        for (String label : labels) {
            if (configLabels.contains(label)) {
                log.warn("文本内容安全审核未通过, 命中规则: {}, 文本内容: {}", label, text);
                return false;
            }
        }
        log.info("文本内容安全审核通过,审核结果: {}, 文本内容: {}", labels, text);
        return true;
    }
}

package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.biz.PrincipalModel;
import ai.conrain.aigc.platform.service.model.query.PrincipalInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;

import java.util.Date;
import java.util.List;

/**
 * 通用主体属性 Service定义
 *
 * <AUTHOR>
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
public interface PrincipalInfoService {
	
	/**
	 * 查询通用主体属性对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	PrincipalInfoVO selectById(Integer id);

	/**
	 * 删除通用主体属性对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加通用主体属性对象
	 * @param principalInfo 对象参数
	 * @return 返回结果
	 */
	PrincipalInfoVO insert(PrincipalInfoVO principalInfo);

	/**
	 * 插入或更新通用主体属性对象
	 * @param principalInfo 对象参数
	 * @return 返回结果
	 */
	PrincipalInfoVO insertOrUpdate(PrincipalInfoVO principalInfo);

	/**
	 * 修改通用主体属性对象
	 * @param principalInfo 对象参数
	 */
	void updateByIdSelective(PrincipalInfoVO principalInfo);

	/**
	 * 带条件批量查询通用主体属性列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<PrincipalInfoVO> queryPrincipalInfoList(PrincipalInfoQuery query);

	/**
	 * 带条件查询通用主体属性数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryPrincipalInfoCount(PrincipalInfoQuery query);

	/**
	 * 带条件分页查询通用主体属性
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<PrincipalInfoVO> queryPrincipalInfoByPage(PrincipalInfoQuery query);

	/**
	 * 根据 主体类型和主体id查询
	 *
	 * @param principal 主体
	 * @param key
	 * @return 结果
	 */
	PrincipalInfoVO selectByPrincipal(PrincipalModel principal, String key);

	/**
	 * 查询销售关联的抽成主体
	 */
	List<PrincipalInfoVO> queryRelatedCommissionPrincipal(PrincipalModel principal);

	/**
	 * 编辑合同录入日期
	 * @param userId 用户 id
	 * @param contractDate 日期
	 */
    void modifyContractDate(Integer userId, String contractDate);

	/**
	 * 查询合同录入日期
	 * @param principal 主体
	 * @return 日期 yyyy-MM-dd
	 */
	String queryContractDate(PrincipalModel principal);

	/**
	 * 是否签约首年
	 * @param principal 主体
	 * @return 是否签约首年
	 */
	boolean isInitYear(PrincipalModel principal);

}
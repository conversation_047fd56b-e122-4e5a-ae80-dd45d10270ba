package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.InvoiceTitleDO;
import ai.conrain.aigc.platform.dal.example.InvoiceTitleExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.InvoiceTitleQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;
import ai.conrain.aigc.platform.service.model.converter.InvoiceTitleConverter;
import ai.conrain.aigc.platform.dal.dao.InvoiceTitleDAO;
import ai.conrain.aigc.platform.service.component.InvoiceTitleService;

/**   
 * InvoiceTitleService实现
 *
 * <AUTHOR>
 * @version InvoiceTitleService.java v 0.1 2024-06-27 01:42:09
 */
@Slf4j
@Service
public class InvoiceTitleServiceImpl implements InvoiceTitleService {

	/** DAO */
	@Autowired
	private InvoiceTitleDAO invoiceTitleDAO;

	@Override
	public InvoiceTitleVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		InvoiceTitleDO data = invoiceTitleDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
		if (null == data) {
			return null;
		}

		return InvoiceTitleConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = invoiceTitleDAO.logicalDeleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除InvoiceTitle失败");
	}

	@Override
	public InvoiceTitleVO insert(InvoiceTitleVO invoiceTitle) {
		AssertUtil.assertNotNull(invoiceTitle, ResultCode.PARAM_INVALID, "invoiceTitle is null");
		AssertUtil.assertTrue(invoiceTitle.getId() == null, ResultCode.PARAM_INVALID, "invoiceTitle.id is present");

		//创建时间、修改时间兜底
		if (invoiceTitle.getCreateTime() == null) {
			invoiceTitle.setCreateTime(new Date());
		}

		if (invoiceTitle.getModifyTime() == null) {
			invoiceTitle.setModifyTime(new Date());
		}

		InvoiceTitleDO data = InvoiceTitleConverter.vo2DO(invoiceTitle);
		//逻辑删除字段初始化（虽然一般表字段有默认值）
		data.setDeleted(false);
		Integer n = invoiceTitleDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建InvoiceTitle失败");
		AssertUtil.assertNotNull(data.getId(), "新建InvoiceTitle返回id为空");
		invoiceTitle.setId(data.getId());
		return invoiceTitle;
	}


	@Override
	public void updateByIdSelective(InvoiceTitleVO invoiceTitle) {
		AssertUtil.assertNotNull(invoiceTitle, ResultCode.PARAM_INVALID, "invoiceTitle is null");
    	AssertUtil.assertTrue(invoiceTitle.getId() != null, ResultCode.PARAM_INVALID, "invoiceTitle.id is null");

		//修改时间必须更新
		invoiceTitle.setModifyTime(new Date());
		InvoiceTitleDO data = InvoiceTitleConverter.vo2DO(invoiceTitle);
		//逻辑删除标过滤
		data.setDeleted(false);
		int n = invoiceTitleDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新InvoiceTitle失败，影响行数:" + n);
	}

	@Override
	public List<InvoiceTitleVO> queryInvoiceTitleList(InvoiceTitleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceTitleExample example = InvoiceTitleConverter.query2Example(query);

		List<InvoiceTitleDO> list = invoiceTitleDAO.selectByExample(example);
			return InvoiceTitleConverter.doList2VOList(list);
	}

	@Override
	public Long queryInvoiceTitleCount(InvoiceTitleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		InvoiceTitleExample example = InvoiceTitleConverter.query2Example(query);
		long c = invoiceTitleDAO.countByExample(example);
		return c;
	}

	/**
	 * 带条件分页查询发票抬头
	 */
	@Override
	public PageInfo<InvoiceTitleVO> queryInvoiceTitleByPage(InvoiceTitleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<InvoiceTitleVO> page = new PageInfo<>();

		InvoiceTitleExample example = InvoiceTitleConverter.query2Example(query);
		long totalCount = invoiceTitleDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<InvoiceTitleDO> list = invoiceTitleDAO.selectByExample(example);
		page.setList(InvoiceTitleConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.vo.training.*;

/**
 * 标注服务接口
 *
 * <AUTHOR>
 */
public interface LabelingService {
    /**
     * 获取待标注样本
     */
    LabelingSampleSummary getLabelingSample(LabelingSampleRequest request);

    /**
     * 保存标注结果
     *
     * @param request 保存标注请求
     */
    void saveLabels(SaveLabelsRequest request);

    /**
     * 导出标注数据
     *
     * @param roundId 轮次ID
     * @return 导出结果
     */
    ExportResultVO exportLabeledData(Integer roundId);
}
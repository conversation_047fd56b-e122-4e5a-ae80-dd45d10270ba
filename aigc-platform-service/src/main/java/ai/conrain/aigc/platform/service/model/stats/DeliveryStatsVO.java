/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.stats;

import java.io.Serializable;

import lombok.Data;

/**
 * 交付数据统计vo
 *
 * <AUTHOR>
 * @version : DeliveryStatsVO.java, v 0.1 2024/9/14 11:59 renxiao.wu Exp $
 */
@Data
public class DeliveryStatsVO implements Serializable {
    private static final long serialVersionUID = 1700212783116513854L;
    /** 日期 */
    private String date;
    /** 总数 */
    private int total;
    /** 自动交付数 */
    private int autoDelivery;
    /** vip交付情况 */
    private DeliveryStatsItem vip;
    /** 普通用户交付情况 */
    private DeliveryStatsItem normal;
}

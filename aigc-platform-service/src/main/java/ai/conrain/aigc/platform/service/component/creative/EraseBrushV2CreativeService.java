package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CreativeBatchConverter;
import ai.conrain.aigc.platform.service.model.query.CreativeBatchQuery;
import ai.conrain.aigc.platform.service.model.request.EraseBrushV2Request;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.nacos.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class EraseBrushV2CreativeService extends AbstractCreativeService<EraseBrushV2Request> {

    @Value("${comfyui.input.path}")
    private String inputPath;

    @Autowired
    private BatchFillHelper batchFillHelper;

    @Autowired
    private PipelineService pipelineService;

    @Autowired
    private ServerHelper serverHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreativeBatchVO create(EraseBrushV2Request request) throws IOException {
        // 关闭为结束的任务
        closeUnfinished();
        // 删除空任务
        cleanEmpty();

        CreativeBatchVO data = buildData(request, null);
        creativeBatchService.insert(data);
        return data;
    }

    @Override
    protected CreativeBatchVO buildData(EraseBrushV2Request request, MaterialModelVO modelVO) throws IOException {
        AssertUtil.assertNotBlank(request.getOriginImage(), ResultCode.BIZ_FAIL, "图片不能为空");
        CreativeBatchVO data = CreativeBatchConverter.request2VO(request);
        data.addExtInfo(KEY_ORIGIN_IMAGE, request.getOriginImage());
        // 服务分发
        ServerVO server = fetchServer(data);
        String originImageComfyUIPath = batchFillHelper.uploadImageKeepFormat(request.getOriginImage(), OperationContextHolder.getMasterUserId(), server);

        // 分割图片路径
        int lastSlashIndex = originImageComfyUIPath.lastIndexOf("/");
        String path = originImageComfyUIPath.substring(0, lastSlashIndex);
        String fileName = originImageComfyUIPath.substring(lastSlashIndex + 1);
        String filePrefix = fileName.split("\\.")[0];

        data.addExtInfo(KEY_ORIGIN_IMAGE_PATH, inputPath + path);
        data.addExtInfo(KEY_ORIGIN_IMAGE_PREFIX, filePrefix);
        data.addExtInfo(KEY_CREATIVE_TASK_STACK, Collections.emptyList());
        batchFillHelper.fillOriginBatchInfo(request.getOriginImage(), null, data);

        return data;
    }

    @Override
    public List<CreativeTaskVO> buildTasks(CreativeBatchVO batch, List<CreativeElementVO> elements) {
        return Collections.emptyList();
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.ERASE_BRUSH_V2;
    }

    /**
     * 只要有创建任务的动作就关闭未结束的任务
     */
    private void closeUnfinished() {
        // 查询当前是否存在空任务
        CreativeBatchQuery query = new CreativeBatchQuery();
        query.setOperatorId(OperationContextHolder.getOperatorUserId());
        query.setType(getCreativeType().getCode());
        query.setBatchCnt(1);
        query.setOrderBy("create_time DESC");
        query.setPageSize(1);
        query.setPageNum(1);
        List<CreativeBatchVO> list = creativeBatchService.queryCreativeBatchList(query);
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("[消除笔V2]查询到未关闭的任务 {} 条, customStatus 置为 FINISHED", list.size());
            list.forEach(item -> {
                item.addExtInfo(KEY_CUSTOM_STATUS, "FINISHED");
                creativeBatchService.updateByIdSelective(item);
            });
        }
    }

    /**
     * 删除已经存在的空任务
     */
    private void cleanEmpty() {
        // 查询当前是否存在空任务
        CreativeBatchQuery query = new CreativeBatchQuery();
        query.setOperatorId(OperationContextHolder.getOperatorUserId());
        query.setType(getCreativeType().getCode());
        query.setBatchCnt(0);
        query.setOrderBy("create_time DESC");
        List<CreativeBatchVO> list = creativeBatchService.queryCreativeBatchList(query);

        // 理论上, 空任务最多只有一个,
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("[消除笔V2]查询到现有空任务 {} 条, 直接删除", list.size());
            list.forEach(item -> {
                creativeBatchService.deleteById(item.getId());
            });
        }
    }

    private ServerVO fetchServer(CreativeBatchVO batch) {
        PipelineVO pipeline = pipelineService.fetchByUserId(OperationContextHolder.getMasterUserId());
        List<ServerVO> modelServers = serverHelper.getServersByType(ServerTypeEnum.MODEL_SERVER, pipeline);
        AssertUtil.assertNotEmpty(modelServers, ResultCode.BIZ_FAIL, "暂无模型服务");

        // 随机获取一个模型服务
        Collections.shuffle(modelServers);
        ServerVO modelServer = modelServers.get(0);
        batch.addExtInfo(KEY_SERVER_ID, modelServer.getId());
        batch.addExtInfo(KEY_SERVER_URL, serverHelper.getServerUrl(modelServer));

        // 获取该模型服务同服务器上的文件服务
        List<ServerVO> fileServers = serverHelper.getServersByType(ServerTypeEnum.FILE_SERVICE, pipeline);
        AssertUtil.assertNotEmpty(fileServers, ResultCode.BIZ_FAIL, "暂无文件服务");

        // 返回文件服务
        return fileServers.stream().
                filter(item -> item.getParentId().equals(modelServer.getId()))
                .findFirst().orElse(null);
    }

}
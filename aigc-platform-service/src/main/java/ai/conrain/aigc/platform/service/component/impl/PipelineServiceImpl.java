package ai.conrain.aigc.platform.service.component.impl;

import java.util.Date;
import java.util.List;

import ai.conrain.aigc.platform.dal.dao.PipelineDAO;
import ai.conrain.aigc.platform.dal.entity.PipelineDO;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.PipelineConverter;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * PipelineService实现
 *
 * <AUTHOR>
 * @version PipelineService.java v 0.1 2024-06-15 04:58:30
 */
@Slf4j
@Service
public class PipelineServiceImpl extends AbstractCachedService<PipelineVO, Integer, PipelineVO>
    implements PipelineService {
    @Autowired
    private PipelineDAO pipelineDAO;
    @Lazy
    @Autowired
    private ServerService serverService;
    @Lazy
    @Autowired
    private UserService userService;

    @Override
    public PipelineVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        PipelineDO data = pipelineDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return PipelineConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = pipelineDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Pipeline失败");

        //强制刷新
        forceRefresh();
    }

    @Override
    public PipelineVO insert(PipelineVO pipeline) {
        AssertUtil.assertNotNull(pipeline, ResultCode.PARAM_INVALID, "pipeline is null");
        AssertUtil.assertTrue(pipeline.getId() == null, ResultCode.PARAM_INVALID, "pipeline.id is present");

        //创建时间、修改时间兜底
        if (pipeline.getCreateTime() == null) {
            pipeline.setCreateTime(new Date());
        }

        if (pipeline.getModifyTime() == null) {
            pipeline.setModifyTime(new Date());
        }

        PipelineDO data = PipelineConverter.vo2DO(pipeline);
        int n = pipelineDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Pipeline失败");
        AssertUtil.assertNotNull(data.getId(), "新建Pipeline返回id为空");
        pipeline.setId(data.getId());

        refresh();

        return pipeline;
    }

    @Override
    public void updateById(PipelineVO pipeline) {
        AssertUtil.assertNotNull(pipeline, ResultCode.PARAM_INVALID, "pipeline is null");
        AssertUtil.assertTrue(pipeline.getId() != null, ResultCode.PARAM_INVALID, "pipeline.id is null");
        //修改时间必须更新
        pipeline.setModifyTime(new Date());

        PipelineDO data = PipelineConverter.vo2DO(pipeline);
        int n = pipelineDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Pipeline失败，影响行数:" + n);

        refresh();
    }

    @Override
    public PipelineVO fetchByUserId(Integer userId) {
        Integer pipelineId = pipelineDAO.selectByUserId(userId);
        if (pipelineId == null) {
            return null;
        }
        PipelineVO pipelineVO = queryByKey(pipelineId);

        if (CollectionUtils.isEmpty(pipelineVO.getServers())) {
            pipelineVO.setServers(serverService.queryByPipeline(pipelineId));
        }
        return pipelineVO;
    }

    @Override
    public List<Integer> fetchRelatedUsers(PipelineVO pipeline) {
        AssertUtil.assertNotNull(pipeline, ResultCode.PARAM_INVALID, "pipeline is null");

        return pipelineDAO.selectRelatedUsers(pipeline.getId());
    }

    @Override
    public List<PipelineVO> queryAll() {
        List<PipelineVO> list = super.queryAll();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(each -> {
                each.setServers(serverService.queryByPipeline(each.getId()));
            });
        }
        return list;
    }

    @Override
    protected List<PipelineVO> loadAll() {
        List<PipelineDO> list = pipelineDAO.selectAll();
        return PipelineConverter.doList2VOList(list);
    }

    @Override
    protected Integer getKey(PipelineVO cache) {
        return cache.getId();
    }

    @Override
    protected PipelineVO getValue(PipelineVO cache) {
        return cache;
    }

}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.service.component.PipelineService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.constants.EventConstants;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.event.BaseEvent;
import ai.conrain.aigc.platform.service.model.event.FileOperateEvent;
import ai.conrain.aigc.platform.service.model.event.FileOperateEvent.FileOperateType;
import ai.conrain.aigc.platform.service.model.event.FileTransEvent;
import ai.conrain.aigc.platform.service.model.event.FolderCopyEvent;
import ai.conrain.aigc.platform.service.model.vo.PipelineVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.EventConstants.TOPIC_FILE_OPERATE;

/**
 * 文件派发服务实现
 *
 * <AUTHOR>
 * @version : FileDispatchServiceImpl.java, v 0.1 2024/9/27 10:37 renxiao.wu Exp $
 */
@Slf4j
@Service
public class FileDispatchImpl implements FileDispatch {
    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private PipelineService pipelineService;
    @Autowired
    private ServerService serverService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public void uploadFile(String path, InputStream inputStream, String fileServerUrl, Integer serverId)
        throws IOException {
        fileServerUrl = serverHelper.getFileServerUrl(fileServerUrl);
        //2.调用comfyui的文件服务进行上传
        comfyUIService.uploadInputImage(path, inputStream, fileServerUrl);

        //3.异步事件处理文件同步
        notifyFileSync(fileServerUrl, inputPath + path, false);
    }

    @Override
    public void uploadFile(String path, InputStream inputStream, Integer masterUserId) throws IOException {
        uploadFile(path, inputStream, masterUserId, false);
    }

    @Override
    public void uploadFile(String path, InputStream inputStream, Integer masterUserId, boolean orderly)
        throws IOException {
        uploadFile(path, inputStream, masterUserId, orderly, null);
    }

    /**
     * 上传文件
     *
     * @param path         文件名+文件路径
     * @param inputStream  输入流
     * @param masterUserId 主账号id
     * @param orderly      是否需要排序
     * @param fileServer   指定的server
     */
    @Override
    public void uploadFile(String path, InputStream inputStream, Integer masterUserId, boolean orderly,
                           ServerVO fileServer) throws IOException {
        if (fileServer == null) {
            fileServer = fetchFileServer(masterUserId);
        }
        if (fileServer == null) {
            throw new BizException("未找到文件服务器地址");
        }

        String fileServerUrl = serverHelper.getServerUrl(fileServer);

        //2.调用comfyui的文件服务进行上传
        comfyUIService.uploadInputImage(path, inputStream, fileServerUrl);

        String fullPath = path;
        if (!StringUtils.startsWith(path, inputPath)) {
            fullPath = inputPath + path;
        }

        //3.异步事件处理文件同步
        notifyFileSync(fileServerUrl, fullPath, orderly);
    }

    @Override
    public void uploadFile(String path, InputStream inputStream, String ossUrl, Integer masterUserId, boolean orderly,
                           ServerVO fileServer, String md5) throws IOException {
        if (fileServer == null) {
            fileServer = fetchFileServer(masterUserId);
        }
        if (fileServer == null) {
            throw new BizException("未找到文件服务器地址");
        }

        String fileServerUrl = serverHelper.getServerUrl(fileServer);

        //2.调用comfyui的文件服务进行上传
        comfyUIService.uploadInputImage(path, inputStream, fileServerUrl);

        String fullPath = path;
        if (!StringUtils.startsWith(path, inputPath)) {
            fullPath = inputPath + path;
        }

        //3.异步事件处理文件同步
        notifyFileSync(fileServerUrl, fullPath, ossUrl, md5);
    }

    @Override
    public void uploadFilePath(String path, InputStream inputStream, Integer masterUserId) throws IOException {
        String fileServerUrl = serverHelper.getFileServerUrlByUser(masterUserId);
        if (StringUtils.isBlank(fileServerUrl)) {
            throw new BizException("未找到文件服务器地址");
        }

        //2.调用comfyui的文件服务进行上传
        comfyUIService.uploadFile(path, inputStream, fileServerUrl);

        //3.异步事件处理文件同步
        notifyFileSync(fileServerUrl, path, false);
    }

    @Override
    public void folderCopy(String path, String newPath, String fileServerUrl) {
        AssertUtil.assertNotBlank(fileServerUrl, ResultCode.SYS_ERROR, "文件服务器地址不能为空");
        boolean success = comfyUIService.folderCopy(path, newPath, serverHelper.getFileServerUrl(fileServerUrl));
        AssertUtil.assertTrue(success, ResultCode.SYS_ERROR, "文件夹复制失败");

        notifySync(fileServerUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FolderCopyEvent(path, newPath, targetServer);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FOLDER_COPY;
            }

            @Override
            public String getTitle() {
                return "文件拷贝";
            }

            @Override
            public String getOrderlyHashKey() {
                return null;
            }

            @Override
            public String getLogContent() {
                return path + "->" + newPath;
            }
        }, false);
    }

    @Override
    public void notifyFileSync(String serverUrl, String path, boolean orderly) {
        notifySync(serverUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FILE_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件";
            }

            @Override
            public String getOrderlyHashKey() {
                return "multi-upload";
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void notifyFileSync(String serverUrl, String path, String ossUrl) {
        notifySync(serverUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer, ossUrl);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FILE_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件";
            }

            @Override
            public String getOrderlyHashKey() {
                return null;
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void notifyFileSync(String serverUrl, String path, String ossUrl, String md5) {
        notifySync(serverUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer, ossUrl, md5);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FILE_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件";
            }

            @Override
            public String getOrderlyHashKey() {
                return null;
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void notifyFileSync(String serverUrl, String path, String ossUrl, String md5, Integer loraId) {
        notifySync(serverUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer, ossUrl, md5, loraId);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FILE_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件";
            }

            @Override
            public String getOrderlyHashKey() {
                return "multi-upload";
            }

            @Override
            public String getLogContent() {
                return path;
            }

            @Override
            public boolean c2cAndDelaySend() {return true;}
        }, false);
    }

    @Override
    public void notifyFolderSync(String serverUrl, String path, boolean orderly) {
        notifySync(serverUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FOLDER_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件夹";
            }

            @Override
            public String getOrderlyHashKey() {
                return "multi-upload-folder";
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void notifyFolderSync(String fileServerUrl, String path, String md5) {
        notifySync(fileServerUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer, null, md5);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FOLDER_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件夹";
            }

            @Override
            public String getOrderlyHashKey() {
                return "multi-upload-folder";
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void notifyFolderSync(String fileServerUrl, String path, String md5, boolean samePipeline) {
        notifySync(fileServerUrl, new DispatchCallback() {
            @Override
            public BaseEvent buildEvent(String originServer, String targetServer) {
                return new FileTransEvent(path, originServer, targetServer, null, md5);
            }

            @Override
            public String getTopic() {
                return EventConstants.TOPIC_FOLDER_SYNC;
            }

            @Override
            public String getTitle() {
                return "文件夹";
            }

            @Override
            public String getOrderlyHashKey() {
                return "multi-upload-folder";
            }

            @Override
            public String getLogContent() {
                return path;
            }
        }, false);
    }

    @Override
    public void renameFile(String path, String newPath) {
        callAtAllServers(serverUrl -> {
            boolean success = comfyUIService.rename(path, newPath, serverUrl);
            log.info("目标服务重命名{},已将{}重命名为{}，目标服务器{}", success ? "成功" : "失败", path, newPath,
                serverUrl);
            AssertUtil.assertTrue(success, ResultCode.SYS_ERROR, "重命名失败");
        }, null);
    }

    @Override
    public void updateTextFileContent(String filePath, String content, String fileServerUrl) {
        callAtAllServers(new MsgOnewayCallback() {
            @Override
            public void sendMsg(String serverUrl) {
                BaseEvent event = new FileOperateEvent(FileOperateType.UPDATE_CONTENT, serverUrl, filePath, content);
                rocketMQTemplate.send(TOPIC_FILE_OPERATE, MessageBuilder.withPayload(event).build());
                log.info("目标服务更新文件内容，文件地址{},发送文件删除事件成功，目标服务器{}", filePath, serverUrl);
            }

            @Override
            public void call(String serverUrl) {
                comfyUIService.updateTextFileContent(filePath, content, serverUrl);
                log.info("目标服务更新文件内容成功,文件地址{}，目标服务器{}", filePath, serverUrl);
            }
        }, fileServerUrl);
    }

    @Override
    public void removeImageOrTxtFile(String dir, String fileNamePrefix, String fileServerUrl) {
        callAtAllServers(new MsgOnewayCallback() {
            @Override
            public void sendMsg(String serverUrl) {
                BaseEvent event = new FileOperateEvent(FileOperateType.REMOVE, serverUrl, dir, fileNamePrefix);
                rocketMQTemplate.send(TOPIC_FILE_OPERATE, MessageBuilder.withPayload(event).build());
                log.info("目标服务删除文件，{}/{},发送文件删除事件成功，目标服务器{}", dir, fileNamePrefix, serverUrl);
            }

            @Override
            public void call(String serverUrl) {
                boolean success = comfyUIService.removeImageOrTxtFile(dir, fileNamePrefix, serverUrl);
                AssertUtil.assertTrue(success, "文件删除失败path=" + dir);
                log.info("目标服务删除文件成功,文件地址{}/{}已被删除，目标服务器{}", dir, fileNamePrefix, serverUrl);
            }
        }, fileServerUrl);
    }

    /**
     * 直接调用所有服务执行某个命令
     *
     * @param callback  回调函数
     * @param serverUrl 服务地址，可空
     */
    private void callAtAllServers(OnewayCallback callback, String serverUrl) {
        if (StringUtils.isNotBlank(serverUrl)) {
            callback.call(serverUrl);
        }

        List<ServerVO> serverList = serverService.queryAll();
        //筛选出是端口的服务 & 是文件服务 & 非当前文件服务（当前服务不需要通知） & 机器状态未关闭
        List<ServerVO> filter = serverList.stream().filter(
            e -> e.getLevel() == 2 && e.getType() == ServerTypeEnum.FILE_SERVICE).filter(
            e -> e.getStatus() != ServerStatusEnum.DISABLE).collect(Collectors.toList());

        List<String> calledList = new ArrayList<>();
        if (StringUtils.isNotBlank(serverUrl)) {
            calledList.add(serverUrl);
        }

        for (ServerVO serverVO : filter) {
            String fileServerUrl = serverHelper.getFileServerUrl(serverVO);
            if (StringUtils.isBlank(fileServerUrl)) {
                log.warn("文件服务{}地址为空，跳过", serverVO.getId());
                continue;
            }
            if (calledList.contains(fileServerUrl)) {
                continue;
            }

            if (callback instanceof MsgOnewayCallback && !EnvUtil.isLocalEnv()) {
                ((MsgOnewayCallback)callback).sendMsg(fileServerUrl);
            } else {
                callback.call(fileServerUrl);
            }

            calledList.add(fileServerUrl);
        }
    }

    /**
     * 发送文件夹/文件同步事件
     *
     * @param serverUrl    当前服务地址
     * @param callback     回调函数
     * @param samePipeline 是否同管道下
     */
    private void notifySync(String serverUrl, DispatchCallback callback, boolean samePipeline) {
        if (EnvUtil.isLocalEnv()) {
            log.info("local环境不发送文件/夹同步事件");
            return;
        }

        //可能传入的服务地址是非文件地址
        String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);

        List<String> sendList = new ArrayList<>(Collections.singletonList(fileServerUrl));

        //c2c发送模式
        if (callback.c2cAndDelaySend()) {
            sendByC2CAndDelay(callback, fileServerUrl, sendList);
            return;
        }

        String title = callback.getTitle();
        String topic = callback.getTopic();
        String hashKey = callback.getOrderlyHashKey();
        boolean orderly = StringUtils.isNotBlank(hashKey);
        String keyContent = callback.getLogContent();

        List<ServerVO> serverList = serverService.queryAll();
        //筛选出是端口的服务 & 是文件服务 & 非当前文件服务（当前服务不需要通知） & 机器状态未关闭
        List<ServerVO> filter = serverList.stream().filter(
            e -> e.getLevel() == 2 && e.getType() == ServerTypeEnum.FILE_SERVICE && !StringUtils.equals(
                serverHelper.getFileServerUrl(e), serverUrl)).filter(e -> serverHelper.isEnable(e)).collect(
            Collectors.toList());
        if (samePipeline) {
            ServerVO server = serverService.parseByUrl(fileServerUrl);
            if (server != null) {
                filter = filter.stream().filter(e -> e.getPipelineId().equals(server.getPipelineId())).collect(
                    Collectors.toList());
            }
        }
        log.info("【{}】{}已传输至{},待同步到文件服务数{}", title, keyContent, fileServerUrl,
            CollectionUtils.size(filter));

        if (CollectionUtils.isEmpty(filter)) {
            return;
        }

        filter.forEach(e -> {
            String targetUrl = serverHelper.getServerUrl(e);
            String transUrl = serverHelper.getTransServerUrl(fileServerUrl, e);
            //有可能两个集群配置的服务地址一致，则不进行传输
            if (!sendList.contains(targetUrl)) {
                log.info("【{}】{}已传输至{},发送异步消息，通知需要同步至{}", title, keyContent, transUrl, targetUrl);
                BaseEvent event = callback.buildEvent(transUrl, targetUrl);
                if (orderly) {
                    log.info("【{}】发送顺序消息", title);
                    rocketMQTemplate.syncSendOrderly(topic, MessageBuilder.withPayload(event).build(), hashKey);
                } else {
                    rocketMQTemplate.send(topic, MessageBuilder.withPayload(event).build());
                }

                sendList.add(targetUrl);
            } else {
                log.info("【{}】{}已传输至{},目标服务地址一致,跳过", title, keyContent, transUrl);
            }
        });
    }

    /**
     * 发送C2C消息
     *
     * @param callback      回调
     * @param fileServerUrl 当前已发送文件服务地址
     * @param sendList      已发送列表
     */
    private void sendByC2CAndDelay(DispatchCallback callback, String fileServerUrl, List<String> sendList) {
        String title = callback.getTitle();
        String topic = callback.getTopic();
        String hashKey = callback.getOrderlyHashKey();
        String keyContent = callback.getLogContent();

        log.info("【{}】{},进入C2C文件同步流程,fileServerUrl={}", title, keyContent, fileServerUrl);

        List<String> fileServerUrls = serverHelper.getAllFileServerUrl();
        fileServerUrls.remove(fileServerUrl);

        List<String> remainList = new CopyOnWriteArrayList<>(fileServerUrls);
        sendList = new CopyOnWriteArrayList<>(sendList);
        int round = 0;
        int startLevel = 4;
        while (CollectionUtils.isNotEmpty(remainList)) {
            for (String sendUrl : sendList) {
                if (CollectionUtils.isEmpty(remainList)) {
                    break;
                }

                // 随机
                Collections.shuffle(remainList, new Random());
                String targetUrl = remainList.get(0);

                String transUrl = serverHelper.getTransServerUrl(sendUrl, targetUrl);
                int delayLevel = round == 0 ? 1 : startLevel + round;

                log.info("【{}】{},发送C2C消息，delayLevel={}的等待时间后，由{}的文件服务传输到{}", title, keyContent,
                    delayLevel, transUrl, targetUrl);
                BaseEvent event = callback.buildEvent(transUrl, targetUrl);

                if (!EnvUtil.isLocalEnv()) {
                    rocketMQTemplate.syncSendOrderly(topic, MessageBuilder.withPayload(event).build(), hashKey, 3000,
                        delayLevel);
                }

                remainList.remove(targetUrl);
                sendList.add(targetUrl);
            }
            round++;
        }
    }

    /**
     * 获取文件上传地址
     *
     * @param masterUserId 主账户id
     * @return 文件服务
     */
    private ServerVO fetchFileServer(Integer masterUserId) {
        PipelineVO pipeline = pipelineService.fetchByUserId(masterUserId);
        List<ServerVO> servers = serverHelper.getServersByType(ServerTypeEnum.FILE_SERVICE, pipeline);

        if (CollectionUtils.isEmpty(servers)) {
            log.warn("pipeline {} has no servers", pipeline.getId());
            return null;
        }

        if (servers.size() > 1) {
            Random random = new Random();
            int index = random.nextInt(servers.size());
            return servers.get(index);
        }

        return servers.get(0);
    }

    interface DispatchCallback {
        /**
         * 构建派发事件
         *
         * @param originServer 源服务地址
         * @param targetServer 目标服务地址
         * @return 事件主体
         */
        BaseEvent buildEvent(String originServer, String targetServer);

        /**
         * 获取主题
         *
         * @return 主题
         */
        String getTopic();

        /**
         * 获取标题
         *
         * @return 标题
         */
        String getTitle();

        /**
         * 获取hash key用于消息分桶
         * 如果为null时不进行分桶
         *
         * @return hash key
         */
        String getOrderlyHashKey();

        /**
         * 获取需要打印的日志内容
         *
         * @return 日志内容
         */
        String getLogContent();

        /**
         * 是否c2c分发并延迟发送
         *
         * @return true，c2c分发并延迟发送
         */
        default boolean c2cAndDelaySend() {
            return false;
        }
    }

    interface OnewayCallback {
        /**
         * 调用服务
         *
         * @param serverUrl 服务地址
         */
        void call(String serverUrl);
    }

    interface MsgOnewayCallback extends OnewayCallback {
        /**
         * 调用服务
         *
         * @param serverUrl 服务地址
         */
        void sendMsg(String serverUrl);
    }
}

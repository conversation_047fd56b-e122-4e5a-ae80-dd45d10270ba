package ai.conrain.aigc.platform.service.constants;

import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.util.AssertUtil;

public class LoraActivateKeys {
    public static final String LORA_KEY_FACE = "linrun0901";
    public static final String LORA_KEY_SCENE = "mgs2222";
    public static final String LORA_KEY_CLOTH = "linrun2111";
    public static final String LORA_KEY_CLOTH_NEW = "Clothing from the linrun2111 Collection";

    public static String getActivateKey(String materialType, boolean noCutout) {
        AssertUtil.assertNotNull(materialType, "materialType must not be null");
        MaterialType type = MaterialType.valueOf(materialType);
        AssertUtil.assertNotNull(type, "unknown materialType:" + materialType);

        switch (type) {
            case cloth:
                if (noCutout) {
                    return LORA_KEY_CLOTH_NEW;
                }
                return LORA_KEY_CLOTH;
            case face:
                return LORA_KEY_FACE;
            case scene:
                return LORA_KEY_SCENE;
            default:
                throw new IllegalArgumentException("unknown materialType:" + materialType);
        }
    }
}

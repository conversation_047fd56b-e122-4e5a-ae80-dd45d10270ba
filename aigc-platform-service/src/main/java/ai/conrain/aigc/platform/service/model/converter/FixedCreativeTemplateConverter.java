package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.FixedCreativeTemplateDO;
import ai.conrain.aigc.platform.service.model.query.FixedCreativeTemplateQuery;
import ai.conrain.aigc.platform.dal.example.FixedCreativeTemplateExample;
import ai.conrain.aigc.platform.service.model.vo.FixedCreativeTemplateVO;

import com.alibaba.fastjson.JSON;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * FixedCreativeTemplateConverter
 *
 * @version FixedCreativeTemplateService.java v 0.1 2025-05-27 05:39:17
 */
public class FixedCreativeTemplateConverter {

    /**
     * DO -> VO
     */
    public static FixedCreativeTemplateVO do2VO(FixedCreativeTemplateDO from) {
        FixedCreativeTemplateVO to = new FixedCreativeTemplateVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTemplateName(from.getTemplateName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setTemplateList(from.getTemplateList());
        
        // 反向处理：将 templateList JSON 字符串解析为 referenceInfoList
        if (StringUtils.isNotBlank(from.getTemplateList())) {
            try {
                List<FixedCreativeTemplateVO.ReferenceInfo> referenceInfoList = 
                    JSON.parseArray(from.getTemplateList(), FixedCreativeTemplateVO.ReferenceInfo.class);
                to.setReferenceInfoList(referenceInfoList);
            } catch (Exception e) {
                // 如果解析失败，说明 templateList 可能不是 ReferenceInfo 格式，保持原样
                // 这里可以记录日志但不抛异常，保证向后兼容
            }
        }
        
        if (StringUtils.isNotBlank(from.getExtInfo())) {
            to.setExtInfo(JSON.parseObject(from.getExtInfo()));
        }

        return to;
    }

    /**
     * VO -> DO
     */
    public static FixedCreativeTemplateDO vo2DO(FixedCreativeTemplateVO from) {
        FixedCreativeTemplateDO to = new FixedCreativeTemplateDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setTemplateName(from.getTemplateName());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        
        // 处理 templateList 字段
        // 优先使用 referenceInfoList，如果为空则使用 templateList
        if (from.getReferenceInfoList() != null && !from.getReferenceInfoList().isEmpty()) {
            to.setTemplateList(JSON.toJSONString(from.getReferenceInfoList()));
        } else if (from.getTemplateList() != null) {
            to.setTemplateList(from.getTemplateList());
        }
        
        // 如果extInfo为空，则添加默认值
        if (from.getExtInfo() != null) {
            to.setExtInfo(JSON.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * Query -> Example
     */
    public static FixedCreativeTemplateExample query2Example(FixedCreativeTemplateQuery from) {
        FixedCreativeTemplateExample to = new FixedCreativeTemplateExample();
        FixedCreativeTemplateExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getTemplateName())) {
            c.andTemplateNameEqualTo(from.getTemplateName());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<FixedCreativeTemplateVO> doList2VOList(List<FixedCreativeTemplateDO> list) {
        return CommonUtil.listConverter(list, FixedCreativeTemplateConverter::do2VO);
    }
}
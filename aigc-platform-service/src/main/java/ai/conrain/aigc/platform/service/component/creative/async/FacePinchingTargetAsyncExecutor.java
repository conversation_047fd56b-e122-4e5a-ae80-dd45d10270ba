/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative.async;

import com.alibaba.fastjson.JSONArray;

import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.OssHelper;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.FacePinchingCreativeRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RACE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_SERVER_URL;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TARGET_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_TARGET_IMAGE_PATH;
import static ai.conrain.aigc.platform.service.constants.SystemConstants.FACE_RACE_PROMPT;

/**
 * 模特捏脸目标人脸图片异步处理器
 *
 * <AUTHOR>
 * @version : FacePinchingTargetAsyncExecutor.java, v 0.1 2025/4/9 01:11 renxiao.wu Exp $
 */
@Service
public class FacePinchingTargetAsyncExecutor extends AbstractBatchToAsyncExecutor<FacePinchingCreativeRequest, String> {
    @Value("${comfyui.input.path}")
    private String inputPath;
    @Autowired
    private OssHelper ossHelper;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private ServerHelper serverHelper;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    protected Class<String> getModelClass() {
        return String.class;
    }

    @Override
    protected Object buildOriginValue(FacePinchingCreativeRequest request) {
        return request.getTargetImages();
    }

    @Override
    protected String buildTransValue(CreativeTaskVO task, String origin) {
        List<String> list = JSONArray.parseArray(origin).toJavaList(String.class);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        String path = "face_pinching/" + sdf.format(new Date()) + task.getUserId() + "/" + task.getBatchId() + "/faces";
        String serverUrl = task.getExtInfo(KEY_SERVER_URL, String.class);
        String fileServerUrl = serverHelper.getFileServerUrl(serverUrl);

        boolean success = ossHelper.downloadAndUploadToPath(list, path, task.getUserId(), serverUrl, false);
        if (!success) {
            throw new BizException(ResultCode.SYS_ERROR);
        }
        String fullPath = inputPath + path;

        String fileName = StringUtils.substringBeforeLast(StringUtils.substringAfterLast(list.get(0), "/"), "?");

        String prompt = systemConfigService.queryValueByKey(FACE_RACE_PROMPT);
        String result = comfyUIService.callLLM(prompt, fullPath + "/" + fileName, fileServerUrl);
        AssertUtil.assertNotBlank(result, "callLLM result is blank");

        task.addExtInfo(KEY_RACE, result);

        return fullPath;
    }

    @Override
    public String getOriginKey() {
        return KEY_TARGET_IMAGE;
    }

    @Override
    public String getTransKey() {
        return KEY_TARGET_IMAGE_PATH;
    }

    @Override
    protected CreativeTypeEnum aboutToType() {
        return CreativeTypeEnum.FACE_PINCHING;
    }
}

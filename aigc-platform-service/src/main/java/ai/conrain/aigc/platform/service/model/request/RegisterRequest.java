package ai.conrain.aigc.platform.service.model.request;

import ai.conrain.aigc.platform.service.validation.Mobile;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 注册请求
 */
@Data
public class RegisterRequest {

    /** 企业名称 */
    @NotBlank(message = "企业名称不能为空！")
    private String corpName;

    /** 手机号 */
    @NotBlank(message = "手机号不能为空！")
    @Mobile
    private String mobile;

    /** 昵称 */
    private String nickName;

    /** 验证码 */
    @NotBlank(message = "短信验证码不能为空！")
    private String code;

    // 邀请码
    private String inviteCode;

}

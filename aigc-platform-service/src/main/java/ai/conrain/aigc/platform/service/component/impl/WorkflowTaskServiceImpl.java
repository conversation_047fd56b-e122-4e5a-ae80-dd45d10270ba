package ai.conrain.aigc.platform.service.component.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.dal.dao.WorkflowTaskDAO;
import ai.conrain.aigc.platform.dal.entity.WorkflowTaskDO;
import ai.conrain.aigc.platform.dal.example.WorkflowTaskExample;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.component.WorkScheduleService;
import ai.conrain.aigc.platform.service.component.WorkflowTaskService;
import ai.conrain.aigc.platform.service.enums.CommonTaskEnums.TaskStatus;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import ai.conrain.aigc.platform.service.enums.WorkflowTaskEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.WorkflowTaskConverter;
import ai.conrain.aigc.platform.service.model.query.WorkScheduleQuery;
import ai.conrain.aigc.platform.service.model.query.WorkflowTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import ai.conrain.aigc.platform.service.model.vo.WorkScheduleVO;
import ai.conrain.aigc.platform.service.model.vo.WorkflowTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.NumberCounter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WorkflowTaskService实现
 *
 * <AUTHOR>
 * @version WorkflowTaskService.java v 0.1 2025-04-02 04:59:35
 */
@Slf4j
@Service
public class WorkflowTaskServiceImpl implements WorkflowTaskService {

    /** DAO */
    @Autowired
    private WorkflowTaskDAO workflowTaskDAO;

    @Autowired
    private WorkScheduleService workScheduleService;

    @Autowired
    private UserService userService;

    @Override
    public WorkflowTaskVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        WorkflowTaskDO data = workflowTaskDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return WorkflowTaskConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = workflowTaskDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除WorkflowTask失败");
    }

    @Override
    public WorkflowTaskVO insert(WorkflowTaskVO workflowTask) {
        AssertUtil.assertNotNull(workflowTask, ResultCode.PARAM_INVALID, "workflowTask is null");
        AssertUtil.assertTrue(workflowTask.getId() == null, ResultCode.PARAM_INVALID, "workflowTask.id is present");

        //创建时间、修改时间兜底
        if (workflowTask.getCreateTime() == null) {
            workflowTask.setCreateTime(new Date());
        }

        if (workflowTask.getModifyTime() == null) {
            workflowTask.setModifyTime(new Date());
        }

        WorkflowTaskDO data = WorkflowTaskConverter.vo2DO(workflowTask);
        Integer n = workflowTaskDAO.insert(data);
        AssertUtil.assertTrue(n > 0, ResultCode.BIZ_FAIL, "创建WorkflowTask失败");
        AssertUtil.assertNotNull(data.getId(), "新建WorkflowTask返回id为空");
        workflowTask.setId(data.getId());
        return workflowTask;
    }

    @Override
    public void updateByIdSelective(WorkflowTaskVO workflowTask) {
        AssertUtil.assertNotNull(workflowTask, ResultCode.PARAM_INVALID, "workflowTask is null");
        AssertUtil.assertTrue(workflowTask.getId() != null, ResultCode.PARAM_INVALID, "workflowTask.id is null");

        //修改时间必须更新
        workflowTask.setModifyTime(new Date());
        WorkflowTaskDO data = WorkflowTaskConverter.vo2DO(workflowTask);
        int n = workflowTaskDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新WorkflowTask失败，影响行数:" + n);
    }

    @Override
    public void updateByBizIdSelective(WorkflowTaskVO workflowTask) {
        AssertUtil.assertNotNull(workflowTask, ResultCode.PARAM_INVALID, "workflowTask is null");
        AssertUtil.assertTrue(workflowTask.getBizId() != null, ResultCode.PARAM_INVALID, "workflowTask.bizId is null");
        AssertUtil.assertTrue(workflowTask.getType() != null, ResultCode.PARAM_INVALID, "workflowTask.type is null");

        //修改时间必须更新
        workflowTask.setModifyTime(new Date());
        WorkflowTaskDO data = WorkflowTaskConverter.vo2DO(workflowTask);
        int n = workflowTaskDAO.updateByUniqueKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新WorkflowTask失败，影响行数:" + n);
    }

    @Override
    public List<WorkflowTaskVO> queryWorkflowTaskList(WorkflowTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        WorkflowTaskExample example = WorkflowTaskConverter.query2Example(query);

        List<WorkflowTaskDO> list = workflowTaskDAO.selectByExample(example);
        return WorkflowTaskConverter.doList2VOList(list);
    }

    @Override
    public Long queryWorkflowTaskCount(WorkflowTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        WorkflowTaskExample example = WorkflowTaskConverter.query2Example(query);
        long c = workflowTaskDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询
     */
    @Override
    public PageInfo<WorkflowTaskVO> queryWorkflowTaskByPage(WorkflowTaskQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:"
            + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<WorkflowTaskVO> page = new PageInfo<>();

        WorkflowTaskExample example = WorkflowTaskConverter.query2Example(query);
        long totalCount = workflowTaskDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<WorkflowTaskDO> list = workflowTaskDAO.selectByExample(example);
        page.setList(WorkflowTaskConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void createReviewTaskFor(List<Integer> modelIds, WorkflowTaskEnum taskType) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return;
        }
        // 分配给审核，策略-均匀分配
        WorkScheduleQuery workScheduleQuery = new WorkScheduleQuery();
        workScheduleQuery.setStartTime(DateUtils.getFirstOfDay());
        workScheduleQuery.setEndTime(DateUtils.getLastOfDay());
        List<WorkScheduleVO> workSchedules = workScheduleService.queryWorkScheduleList(workScheduleQuery);
        if (workSchedules.isEmpty()) {
            log.warn("审核人员未排班");
            return;
        }
        // 筛选出审核员
        List<Integer> userIds = workSchedules.stream().map(WorkScheduleVO::getUserId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            log.warn("审核人员未排班");
            return;
        }

        WorkflowTaskQuery workflowTaskQuery = new WorkflowTaskQuery();
        workflowTaskQuery.setOnlyToday(true);
        workflowTaskQuery.setType(WorkflowTaskEnum.REVIEW.getType());
        workflowTaskQuery.setOperatorIds(userIds);
        List<WorkflowTaskVO> workflowTasks = queryWorkflowTaskList(workflowTaskQuery);
        // 分组筛选出任务量最小的
        NumberCounter numberCounter = new NumberCounter(userIds);
        workflowTasks.stream()
            .map(WorkflowTaskVO::getOperatorId)
            .forEach(numberCounter::add);
        // 生成工作流任务
        for (Integer modelId : modelIds) {
            Integer targetReviewerId = numberCounter.getLeast();
            numberCounter.add(targetReviewerId);
            WorkflowTaskVO workflowTaskVO = new WorkflowTaskVO();
            workflowTaskVO.setBizId(modelId);
            workflowTaskVO.setType(taskType.getType());
            workflowTaskVO.setStatus(TaskStatus.RUNNING.name());
            workflowTaskVO.setOperatorId(targetReviewerId);
            insert(workflowTaskVO);
        }
    }
}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.TestPlanDAO;
import ai.conrain.aigc.platform.dal.entity.TestPlanDO;
import ai.conrain.aigc.platform.dal.example.TestPlanExample;
import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.component.train.ClothLoraTrainService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.enums.*;
import ai.conrain.aigc.platform.service.helper.media.ZipHelper;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialDetail;
import ai.conrain.aigc.platform.service.model.biz.ClothMaterialImg;
import ai.conrain.aigc.platform.service.model.biz.abtest.TestCreativeRequest;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TestPlanConverter;
import ai.conrain.aigc.platform.service.model.query.TestItemQuery;
import ai.conrain.aigc.platform.service.model.query.TestPlanQuery;
import ai.conrain.aigc.platform.service.model.query.TestResultQuery;
import ai.conrain.aigc.platform.service.model.request.*;
import ai.conrain.aigc.platform.service.model.vo.*;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoAnalysisRatioVO.AnalysisCountVO;
import ai.conrain.aigc.platform.service.model.vo.ExtInfoAnalysisRatioVO.AnalysisVO;
import ai.conrain.aigc.platform.service.util.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static ai.conrain.aigc.platform.service.constants.BizConstants.TEST_CASE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

/**
 * TestPlanService实现
 *
 * <AUTHOR>
 * @version TestPlanService.java v 0.1 2024-12-19 01:24:06
 */
@Slf4j
@Service
public class TestPlanServiceImpl implements TestPlanService {
    private static final List<String> ELEMENT_KEY_LIST = Arrays.asList("scene", "face");
    /** DAO */
    @Autowired
    private TestPlanDAO testPlanDAO;
    @Autowired
    private TestItemService testItemService;
    @Autowired
    private TestItemGroupService testItemGroupService;
    @Autowired
    private TestResultService testResultService;
    @Autowired
    private UserService userService;
    @Autowired
    private CreativeBatchService creativeBatchService;
    @Autowired
    private CreativeTaskService creativeTaskService;
    @Autowired
    private CreativeElementService creativeElementService;
    @Autowired
    private ClothLoraTrainService clothLoraTrainService;
    @Autowired
    private TestPlanService self;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private MaterialInfoService materialInfoService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private ZipHelper zipHelper;

    @Override
    public TestPlanVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TestPlanDO data = testPlanDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TestPlanConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = testPlanDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TestPlan失败");
    }

    @Override
    public TestPlanVO insert(TestPlanVO testPlan) {
        AssertUtil.assertNotNull(testPlan, ResultCode.PARAM_INVALID, "testPlan is null");
        AssertUtil.assertTrue(testPlan.getId() == null, ResultCode.PARAM_INVALID, "testPlan.id is present");

        // 创建时间、修改时间兜底
        if (testPlan.getCreateTime() == null) {
            testPlan.setCreateTime(new Date());
        }

        if (testPlan.getModifyTime() == null) {
            testPlan.setModifyTime(new Date());
        }

        TestPlanDO data = TestPlanConverter.vo2DO(testPlan);
        int n = testPlanDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TestPlan失败");
        AssertUtil.assertNotNull(data.getId(), "新建TestPlan返回id为空");
        testPlan.setId(data.getId());
        return testPlan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TestPlanVO create(AddTestPlanRequest request) {
        TestPlanVO plan = TestPlanConverter.request2VO(request);

        //如果整组实验需要固定seed，则开始初始化seed
        List<CreateTestParams> testParams = null;
        if (StringUtils.equals(YES, plan.getExtInfo(KEY_KEEP_SEED, String.class))) {
            int maxRound = calcMaxRound(request.getItems());

            boolean useSeedStore = StringUtils.equals(YES, plan.getExtInfo(KEY_USE_SEED_STORE, String.class));
            testParams = buildTestParams(maxRound, useSeedStore);
            plan.addExtInfo(KEY_TEST_PARAMS, testParams);
        }

        insert(plan);

        testItemService.create(plan.getId(), plan.getType(), request.getItems(), testParams);

        return plan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByIdSelective(TestPlanVO testPlan) {
        AssertUtil.assertNotNull(testPlan, ResultCode.PARAM_INVALID, "testPlan is null");
        AssertUtil.assertTrue(testPlan.getId() != null, ResultCode.PARAM_INVALID, "testPlan.id is null");

        TestPlanVO origin = TestPlanConverter.do2VO(testPlanDAO.lockById(testPlan.getId()));

        //noinspection unchecked
        List<CreateTestParams> testParams = origin.getExtInfo(KEY_TEST_PARAMS, List.class);

        if (StringUtils.equals(YES, testPlan.getExtInfo(KEY_KEEP_SEED, String.class))) {
            int maxRound = calcMaxRound(testPlan.getItems());
            boolean useSeedStore = StringUtils.equals(YES, testPlan.getExtInfo(KEY_USE_SEED_STORE, String.class));

            if (testParams == null) {
                testParams = buildTestParams(maxRound, useSeedStore);
            } else {
                testParams.addAll(buildTestParams(maxRound - testParams.size(), useSeedStore));
            }
        } else {
            testParams = null;
        }

        for (TestItemVO item : testPlan.getItems()) {
            if (item.getId() == null) {
                // 若 id 为空则说明为新增实验项目
                testItemService.create(testPlan.getId(), testPlan.getType(), Collections.singletonList(item),
                    testParams);
                testPlan.setStatus(TestStatusEnum.ENABLED);
            } else {
                // 更新实验项目
                testItemService.updateByIdSelective(item);

                if (item.getStatus() == TestStatusEnum.ENABLED) {
                    testPlan.setStatus(TestStatusEnum.ENABLED);
                }

                // 更新组合测试组
                List<JSONObject> comparisonParams = item.getComparisonParams();
                if (comparisonParams != null && !comparisonParams.isEmpty()) {
                    testItemGroupService.updateComparisonParamsByItemId(item.getId(), item.getRoundsNum(),
                        comparisonParams, testParams);
                }
            }
        }

        // 修改时间必须更新
        testPlan.setModifyTime(new Date());
        testPlan.addExtInfo(KEY_TEST_PARAMS, testParams);
        TestPlanDO data = TestPlanConverter.vo2DO(testPlan);
        int n = testPlanDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TestPlan失败，影响行数:" + n);
    }

    @Override
    public List<TestPlanVO> queryTestPlanList(TestPlanQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestPlanExample example = TestPlanConverter.query2Example(query);

        List<TestPlanDO> list = testPlanDAO.selectByExample(example);
        return TestPlanConverter.doList2VOList(list);
    }

    @Override
    public Long queryTestPlanCount(TestPlanQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        TestPlanExample example = TestPlanConverter.query2Example(query);
        return testPlanDAO.countByExample(example);
    }

    /**
     * 带条件分页查询AB测试计划
     */
    @Override
    public PageInfo<TestPlanVO> queryTestPlanByPage(TestPlanQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<TestPlanVO> page = new PageInfo<>();

        TestPlanExample example = TestPlanConverter.query2Example(query);
        long totalCount = testPlanDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<TestPlanDO> list = testPlanDAO.selectByExample(example);
        page.setList(TestPlanConverter.doList2VOList(list));

        List<TestPlanVO> voList = page.getList();
        if (CollectionUtils.isNotEmpty(voList)) {
            TestItemQuery itemQuery = new TestItemQuery();
            itemQuery.setPlanIds(voList.stream().map(TestPlanVO::getId).collect(Collectors.toList()));
            List<TestItemVO> items = testItemService.queryTestItemList(itemQuery);

            List<UserVO> users = userService.batchQueryById(
                voList.stream().map(TestPlanVO::getUserId).collect(Collectors.toList()));

            if (CollectionUtils.isNotEmpty(items)) {
                voList.forEach(e -> e.setItems(
                    items.stream().filter(i -> e.getId().equals(i.getPlanId())).collect(Collectors.toList())));
            }

            if (CollectionUtils.isNotEmpty(users)) {
                voList.forEach(e -> {
                    users.stream().filter(i -> e.getUserId().equals(i.getId())).findFirst().ifPresent(
                        userVO -> e.setUserNick(userVO.getNickName()));
                });
            }
        }

        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void initTask(TestPlanVO target) throws IOException {
        TestPlanVO plan = TestPlanConverter.do2VO(testPlanDAO.lockById(target.getId()));

        AssertUtil.assertNotNull(plan, ResultCode.BIZ_FAIL, "测试计划不存在");
        AssertUtil.assertTrue(plan.getStatus() == TestStatusEnum.ENABLED, ResultCode.BIZ_FAIL, "测试计划状态不正确");
        log.info("开始初始化测试计划关联任务，id={},type={}", plan.getId(), plan.getType());

        plan.setItems(testItemService.queryByPlanId(plan.getId()));

        // 遍历实验项目
        List<TestItemVO> items = plan.getItems();
        for (TestItemVO item : items) {
            if (item.getStatus() != TestStatusEnum.ENABLED) {
                continue;
            }

            // 若为训练项目则进行处理
            Map<String, Integer> materialInfoMap = null;

            // 创建模型数据 判断是否为训练类型的测试项
            if (TestTypeEnum.getByCode(item.getType()) == TestTypeEnum.TRAIN) {
                log.info("当前测试项为[训练项]，planId={},itemId={}", plan.getId(), item.getId());
                try {
                    materialInfoMap = self.createMaterialInfoWithNewTransaction(item);
                } catch (Exception e) {
                    log.error("创建模型数据失败，planId={},itemId={}", plan.getId(), item.getId(), e);
                    throw e;
                }
            }

            if (materialInfoMap != null) {
                // 实验组模型id
                Integer experimentalLoraId = materialInfoMap.get(TestGroupTypeEnum.EXPERIMENTAL.getCode());
                // 对照组模型id
                Integer controlLoraId = materialInfoMap.get(TestGroupTypeEnum.CONTROL.getCode());

                boolean experimentalIsEnabled = checkMaterialModalStatus(experimentalLoraId);
                boolean controlIsEnabled = checkMaterialModalStatus(controlLoraId);

                // 如果两个模型都不存在,则跳出方法
                if (!experimentalIsEnabled && !controlIsEnabled) {
                    continue;
                }
            }

            // 初始化测试计划关联任务
            self.initPlanTaskWithNewTransaction(plan, item, materialInfoMap);
        }

        // 更新测试计划状态
        self.updateTestPlanStatusWithNewTransaction(plan, TestStatusEnum.PROCESSING);
        log.info("初始化测试计划关联任务完成，id={},type={}", plan.getId(), plan.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncStatus(TestPlanVO plan) {
        log.info("【AB测试轮询定时任务】，开始执行同步状态，id={},type={}", plan.getId(), plan.getType());

        for (TestItemVO item : plan.getItems()) {
            // 跳过非处理中状态
            if (item.getStatus() != TestStatusEnum.PROCESSING) {
                log.info("当前测试项目状态非处理中，直接跳过，itemId={},status={}", item.getId(), item.getStatus());
                continue;
            }

            // 获取测试组
            List<TestItemGroupVO> groups = item.getGroups();
            for (TestItemGroupVO group : groups) {
                if (group.getStatus() != TestStatusEnum.PROCESSING) {
                    log.info("当前分组状态非处理中，直接跳过，groupId={},status={}", group.getId(), group.getStatus());
                    continue;
                }

                // 获取创作任务
                CreativeBatchVO batch = creativeBatchService.selectById(group.getBatchId());
                AssertUtil.assertNotNull(batch, ResultCode.BIZ_FAIL, "创作任务不存在，id=" + group.getBatchId());

                // 跳过非完成状态
                if (batch.getStatus() != CreativeStatusEnum.FINISHED) {
                    log.info("当前分组对应的任务状态非完成，直接跳过，groupId={},batchId={},batchStatus={}",
                        group.getId(), batch.getId(), batch.getStatus());
                    continue;
                }

                // 打上风格lora的标
                if (batch.getExtValue(KEY_IS_STYLE_SCENE, Boolean.class) != null && batch.getExtValue(
                    KEY_IS_STYLE_SCENE, Boolean.class)) {
                    group.addExtInfo(KEY_IS_STYLE_SCENE, true);
                }

                // 初始化测试结果
                List<CreativeTaskVO> tasks = creativeTaskService.queryTaskByBatchId(group.getBatchId());
                testResultService.initByTasks(tasks, group);

                // 更新测试组状态
                group.setStatus(TestStatusEnum.COMPARING);
                testItemGroupService.updateByIdSelective(group);
            }

            // 更新测试项目状态
            if (groups.stream().noneMatch(e -> e.getStatus() == TestStatusEnum.PROCESSING)) {
                item.setStatus(TestStatusEnum.COMPARING);
                testItemService.updateByIdSelective(item);
            }
        }

        // 更新测试计划状态
        if (plan.getItems().stream().noneMatch(e -> e.getStatus() == TestStatusEnum.PROCESSING)) {
            plan.setStatus(TestStatusEnum.COMPARING);
            updateByIdSelective(plan);
            log.info("同步状态完成，状态已变更为可比对，id={},type={}", plan.getId(), plan.getType());
        }
        log.info("同步状态调度完成，id={},type={}", plan.getId(), plan.getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scoring(Integer resultId, Boolean isDraw, boolean score) {
        TestResultVO result = testResultService.selectById(resultId);
        AssertUtil.assertNotNull(result, ResultCode.BIZ_FAIL, "测试结果不存在，id=" + resultId);

        // 1.变更自身，加分
        result.setScore(score);
        testResultService.updateByIdSelective(result);

        // 2.变更当前分组的分数
        testItemGroupService.refreshScore(result.getGroupId());

        // 3.变更对照/实验组，减分
        TestResultQuery resultQuery = new TestResultQuery();
        resultQuery.setItemId(result.getItemId());
        resultQuery.setRoundId(result.getRoundId());
        List<TestResultVO> others = testResultService.queryTestResultList(resultQuery);

        for (TestResultVO other : others) {
            if (other.getId().equals(result.getId())) {
                continue;
            }

            // 3.1 如果当前为平局，则为对照组加分
            other.setScore((isDraw != null && isDraw) == score);

            testResultService.updateByIdSelective(other);

            // 4.变更当前分组的分数
            testItemGroupService.refreshScore(other.getGroupId());
        }

    }

    @Override
    public void enable(Integer id, boolean enable) {
        TestPlanDO plan = testPlanDAO.selectByPrimaryKey(id);
        TestStatusEnum status = TestStatusEnum.getByCode(plan.getStatus());
        if (enable && status == TestStatusEnum.DISABLED) {
            plan.setStatus(TestStatusEnum.ENABLED.getCode());
            testPlanDAO.updateByPrimaryKeySelective(plan);
        } else if (!enable && status != TestStatusEnum.DISABLED) {
            plan.setStatus(TestStatusEnum.DISABLED.getCode());
            testPlanDAO.updateByPrimaryKeySelective(plan);
        }
    }

    @Override
    public void finish(Integer id) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteTestItemById(Integer testItemId) {
        // 删除测试项目实验项
        testItemService.deleteById(testItemId);

        // 删除测试项目实验组
        testItemGroupService.deleteByTestItemId(testItemId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void begin(Integer planId, Integer itemId) throws IOException {

        // 获取测试计划信息
        TestPlanVO testPlanVO = selectByIdAndItemId(planId, itemId);

        // 若数据为空则直接返回
        if (testPlanVO == null) {
            log.info("【AB测试】TestPlanServiceImpl::begin::当前待执行数据数量=0，直接结束");
            return;
        }

        // 由于是单个实验项目启动，故将测试计划状态设置为启用
        testPlanVO.setStatus(TestStatusEnum.ENABLED);

        // 初始化任务
        initTask(testPlanVO);
    }

    @Override
    public ExtInfoAnalysisRatioVO extInfoAnalysisRatio(Integer id) {
        // 参数校验
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");
        log.info("【扩展信息数据分析】TestPlanServiceImpl::extInfoAnalysisRatio::开始分析测试计划id={}的扩展信息", id);

        // 初始化返回值
        ExtInfoAnalysisRatioVO extInfoAnalysisRatioVO = new ExtInfoAnalysisRatioVO();

        // 获取测试项列表并校验
        List<TestItemVO> testItemVOList = testItemService.queryByPlanId(id);
        if (CollectionUtils.isEmpty(testItemVOList)) {
            log.info("【扩展信息数据分析】TestPlanServiceImpl::extInfoAnalysisRatio::测试项数据为空，数据分析终止...");
            return extInfoAnalysisRatioVO;
        }

        // 初始化子级数据分析Map
        Map<Integer, AnalysisVO> childAnalysisRatioMap = new HashMap<>();

        // 遍历测试项进行数据分析
        testItemVOList.forEach(testItem -> {
            log.info("【扩展信息数据分析】TestPlanServiceImpl::extInfoAnalysisRatio::开始分析测试项id={}的扩展信息...",
                testItem.getId());

            // 获取测试结果
            List<TestResultVO> testResultVOList = getTestResults(id, testItem.getId());
            if (CollectionUtils.isEmpty(testResultVOList)) {
                log.info(
                    "【扩展信息数据分析】TestPlanServiceImpl::extInfoAnalysisRatio::测试项id={}的测试结果为空，跳过分析",
                    testItem.getId());
                return;
            }

            // 分析单个测试项
            AnalysisVO analysisVO = analyzeTestItem(testItem.getId(), testResultVOList);
            // 直接添加到Map中
            childAnalysisRatioMap.put(testItem.getId(), analysisVO);
        });

        // 汇总所有测试项的分析结果
        AnalysisVO resultAnalysisRatioVO = summarizeAllTestItems(new ArrayList<>(childAnalysisRatioMap.values()));

        // 设置分析结果
        extInfoAnalysisRatioVO.setChildAnalysisRatioMap(childAnalysisRatioMap);
        extInfoAnalysisRatioVO.setResultAnalysisRatioVO(resultAnalysisRatioVO);

        log.info("【扩展信息数据分析】TestPlanServiceImpl::extInfoAnalysisRatio::测试计划id={}的扩展信息分析完成", id);
        return extInfoAnalysisRatioVO;
    }

    /**
     * 分析单个测试项
     */
    private AnalysisVO analyzeTestItem(Integer itemId, List<TestResultVO> testResultVOList) {
        // 获取测试项信息
        TestItemVO testItem = testItemService.selectById(itemId);

        // 若测试项不存在，则直接返回
        if (testItem == null) {
            log.info("【扩展信息数据分析】TestPlanServiceImpl::analyzeTestItem::测试项不存在，id={}", itemId);
            return null;
        }

        // 初始化分析结果对象
        AnalysisVO analysisVO = new AnalysisVO();
        analysisVO.setId(itemId);
        analysisVO.setExperimentalGroupMap(new HashMap<>());
        analysisVO.setControlGroupMap(new HashMap<>());
        analysisVO.setTotalMap(new HashMap<>());

        // 按实验组和对照组分类
        Map<TestGroupTypeEnum, List<TestResultVO>> groupedResults = AnalysisUtil.groupTestResults(testResultVOList);
        // 获取实验组
        List<TestResultVO> experimentalGroup = groupedResults.getOrDefault(TestGroupTypeEnum.EXPERIMENTAL,
            new ArrayList<>());
        // 获取对照组
        List<TestResultVO> controlGroup = groupedResults.getOrDefault(TestGroupTypeEnum.CONTROL, new ArrayList<>());

        // 分析扩展字段
        analyzeExtInfoFields(analysisVO, experimentalGroup, controlGroup, testItem.getRoundsNum());

        // 返回分析结果
        return analysisVO;
    }

    /**
     * 分析扩展字段
     */
    private void analyzeExtInfoFields(AnalysisVO analysisVO, List<TestResultVO> experimentalGroup,
                                      List<TestResultVO> controlGroup, Integer roundsNum) {
        for (TestResultExtInfoEnum extInfoEnum : TestResultExtInfoEnum.values()) {
            // 获取扩展字段名称
            String extInfoKey = extInfoEnum.getCode();

            // 分别统计实验组和对照组
            AnalysisCountVO experimentalCountVO = analyzeGroupExtInfo(experimentalGroup, extInfoKey, roundsNum);
            AnalysisCountVO controlCountVO = analyzeGroupExtInfo(controlGroup, extInfoKey, roundsNum);

            // 计算总计数据并保存结果
            analysisVO.getExperimentalGroupMap().put(extInfoKey, experimentalCountVO);
            analysisVO.getControlGroupMap().put(extInfoKey, controlCountVO);
            analysisVO.getTotalMap().put(extInfoKey,
                AnalysisUtil.calculateTotalCount(experimentalCountVO, controlCountVO));
        }
    }

    /**
     * 分析组的扩展信息
     */
    private AnalysisCountVO analyzeGroupExtInfo(List<TestResultVO> group, String extInfoKey, Integer roundsNum) {
        AnalysisCountVO countVO = AnalysisUtil.initAnalysisCount();
        countVO.setTotalCount((long)roundsNum);

        // 统计每个测试结果
        group.forEach(result -> AnalysisUtil.analyzeExtInfoValue(countVO, result.getExtInfo(), extInfoKey));

        // 计算未操作数量
        countVO.setNotOperationCount(countVO.getTotalCount() - countVO.getGoodCount() - countVO.getBadCount());
        return countVO;
    }

    /**
     * 汇总所有测试项的分析结果
     */
    private AnalysisVO summarizeAllTestItems(List<AnalysisVO> childAnalysisRatioVOList) {
        AnalysisVO resultAnalysisRatioVO = new AnalysisVO();
        resultAnalysisRatioVO.setExperimentalGroupMap(new HashMap<>());
        resultAnalysisRatioVO.setControlGroupMap(new HashMap<>());
        resultAnalysisRatioVO.setTotalMap(new HashMap<>());

        // 遍历每个扩展字段类型进行汇总
        for (TestResultExtInfoEnum extInfoEnum : TestResultExtInfoEnum.values()) {
            String extInfoKey = extInfoEnum.getCode();
            AnalysisCountVO totalExperimentalCount = AnalysisUtil.initAnalysisCount();
            AnalysisCountVO totalControlCount = AnalysisUtil.initAnalysisCount();

            // 累加每个测试项的统计结果
            for (AnalysisVO analysisVO : childAnalysisRatioVOList) {
                AnalysisUtil.addAnalysisCount(totalExperimentalCount,
                    analysisVO.getExperimentalGroupMap().get(extInfoKey));
                AnalysisUtil.addAnalysisCount(totalControlCount, analysisVO.getControlGroupMap().get(extInfoKey));
            }

            // 计算并保存汇总结果
            resultAnalysisRatioVO.getExperimentalGroupMap().put(extInfoKey, totalExperimentalCount);
            resultAnalysisRatioVO.getControlGroupMap().put(extInfoKey, totalControlCount);
            resultAnalysisRatioVO.getTotalMap().put(extInfoKey,
                AnalysisUtil.calculateTotalCount(totalExperimentalCount, totalControlCount));
        }

        return resultAnalysisRatioVO;
    }

    /**
     * 获取测试结果列表
     */
    private List<TestResultVO> getTestResults(Integer planId, Integer itemId) {
        // 初始化查询条件
        TestResultQuery testResultQuery = new TestResultQuery();
        testResultQuery.setItemId(itemId);
        testResultQuery.setPlanId(planId);

        // 获取测试结果
        return testResultService.queryTestResultList(testResultQuery);
    }

    /**
     * 根据测试计划ID和测试项目ID获取测试计划信息
     *
     * @param planId 测试计划ID
     * @param itemId 测试项ID
     * @return 测试计划信息
     */
    private TestPlanVO selectByIdAndItemId(Integer planId, Integer itemId) {
        // 获取测试计划信息
        TestPlanVO testPlanVO = selectById(planId);

        if (testPlanVO != null) {
            // 封装查询条件
            TestItemQuery itemQuery = new TestItemQuery();
            itemQuery.setPlanId(testPlanVO.getId());
            if (itemId != null) {
                itemQuery.setId(itemId);
            }
            // 查询测试项目信息
            List<TestItemVO> items = testItemService.queryTestItemList(itemQuery);

            // 设置测试项目信息
            testPlanVO.setItems(items);

            List<UserVO> users = userService.batchQueryById(Collections.singletonList(testPlanVO.getUserId()));
            // 设置用户昵称
            if (CollectionUtils.isNotEmpty(users)) {
                users.stream().filter(i -> testPlanVO.getUserId().equals(i.getId())).findFirst().ifPresent(
                    userVO -> testPlanVO.setUserNick(userVO.getNickName()));
            }
        }

        // 返回结果
        return testPlanVO;
    }

    /**
     * 构建测试参数列表
     *
     * @param roundsNum    测试轮数
     * @param useSeedStore 是否使用种子库
     * @return 测试参数列表
     */
    private List<CreateTestParams> buildTestParams(int roundsNum, boolean useSeedStore) {
        JSONArray seedStore = useSeedStore ? systemConfigService.queryJsonArrValue(
            SystemConstants.BASE_MODEL_TEST_SEED_STORE) : null;

        List<CreateTestParams> paramsList = new ArrayList<>();
        Set<Integer> usedIndices = new HashSet<>();
        Random random = new Random();

        for (int i = 0; i < roundsNum; i++) {
            CreateTestParams params = new CreateTestParams();

            if (seedStore != null && !seedStore.isEmpty()) {
                int idx;
                if (usedIndices.size() < seedStore.size()) {
                    // 还有未使用的种子
                    do {
                        idx = random.nextInt(seedStore.size());
                    } while (usedIndices.contains(idx));
                    usedIndices.add(idx);
                } else {
                    // 所有种子都已使用，循环使用
                    idx = i % seedStore.size();
                }
                params.setSeed(seedStore.getString(idx));
            } else {
                params.setSeed(RandomStringUtils.randomNumeric(15));
            }

            params.setFaceSeed(RandomStringUtils.randomNumeric(15));
            params.setPromptSeed(RandomStringUtils.randomNumeric(15));
            paramsList.add(params);
        }
        return paramsList;
    }

    /**
     * 初始化测试任务
     *
     * @param plan            测试计划
     * @param item            测试项
     * @param materialInfoMap 服饰信息
     * @throws IOException 异常信息
     */
    private void initPlanTask(TestPlanVO plan, TestItemVO item, Map<String, Integer> materialInfoMap)
        throws IOException {
        // 组合测试组
        List<TestItemGroupVO> groups = item.getGroups();

        // 创建测试任务
        boolean isKeepSeed = StringUtils.equals(YES, plan.getExtInfo(KEY_KEEP_SEED, String.class));
        boolean useSeedStore = StringUtils.equals(YES, plan.getExtInfo(KEY_USE_SEED_STORE, String.class));
        List<CreateTestParams> testParams = null;
        //保持种子一致
        if (!isKeepSeed) {
            testParams = buildTestParams(item.getRoundsNum(), useSeedStore);
        }
        for (TestItemGroupVO group : groups) {
            if (group.getBatchId() != null) {
                continue;
            }

            // 构建共享参数
            JSONObject sharedParams = CommonUtil.deepCopy(item.getSharedParams());
            JSONObject comparisonParams = group.getComparisonParams();

            fillComparisonParams(materialInfoMap, group, comparisonParams, sharedParams);

            // 构建创建创作请求
            String flowType = plan.getExtInfo(KEY_FLOW_TYPE, String.class);
            CreativeTypeEnum creativeType = null;
            TestSupportCreativeRequest request = null;
            if (StringUtils.isBlank(flowType) || StringUtils.equals(CreativeTypeEnum.CREATE_IMAGE.getCode(),
                flowType)) {
                request = sharedParams.toJavaObject(AddCreativeRequest.class);
                creativeType = CreativeTypeEnum.CREATE_IMAGE;
            } else {
                request = sharedParams.toJavaObject(TestCreativeRequest.class);
                creativeType = CreativeTypeEnum.TEST_CREATIVE_FLOW;
                ((TestCreativeRequest)request).setFlowType(flowType);
            }
            request.setBizTag(TEST_CASE);
            request.setTestGroupId(group.getId());

            //保持种子一致
            if (isKeepSeed) {
                //noinspection unchecked
                List<CreateTestParams> extValue = group.getExtValue(KEY_TEST_PARAMS, List.class);
                if (CollectionUtils.isNotEmpty(extValue)) {
                    request.setTestParams(extValue);
                }
            }
            if (CollectionUtils.isEmpty(request.getTestParams())) {
                request.setTestParams(testParams);
            }

            // 开启创作流程
            CreativeBatchVO batch = creativeBatchService.create(creativeType, request);

            // 更新测试组关联的batchId
            group.setBatchId(batch.getId());
            group.setStatus(TestStatusEnum.PROCESSING);
            testItemGroupService.updateByIdSelective(group);

            log.info("创建创作任务成功，id={},type={},groupId={},batchId={}", plan.getId(), plan.getType(),
                group.getId(), batch.getId());
        }

        // 更新测试项目状态
        item.setStatus(TestStatusEnum.PROCESSING);
        testItemService.updateByIdSelective(item);
    }

    /**
     * 使用新事务创建素材信息
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Map<String, Integer> createMaterialInfoWithNewTransaction(TestItemVO testItemVO) {
        AssertUtil.assertNotNull(testItemVO, ResultCode.PARAM_INVALID, "测试项不能为空");
        AssertUtil.assertNotNull(testItemVO.getSharedParams(), ResultCode.PARAM_INVALID, "公共参数不能为空");

        log.info("开始创建服装，testItemId={}", testItemVO.getId());
        try {
            return createMaterialInfo(testItemVO);
        } catch (Exception e) {
            log.error("创建浮窗失败，testItemId={}, error={}", testItemVO.getId(), e.getMessage(), e);
            throw new RuntimeException("创建素材信息失败", e);
        }
    }

    /**
     * 创建服装
     */
    private Map<String, Integer> createMaterialInfo(TestItemVO testItemVO) {
        // 获取并验证基础参数
        JSONObject sharedParams = validateAndGetSharedParams(testItemVO);
        if (sharedParams == null) {
            return null;
        }

        // 获取训练类型和服装ID
        String trainType = sharedParams.getString(TRAIN_TYPE);
        Integer clothingLoraId = sharedParams.getInteger(CLOTHING_LORA_ID);

        // 根据训练类型选择处理方式
        switch (trainType) {
            case CLONE:
                return handleCloneMaterialInfo(sharedParams, testItemVO.getGroups(), clothingLoraId);
            case CREATE:
                return handleCreateMaterialModel(sharedParams, testItemVO.getGroups());
            default:
                log.warn("不支持的训练类型: {}, testItemId={}", trainType, testItemVO.getId());
                return null;
        }
    }

    /**
     * 验证并获取共享参数
     */
    private JSONObject validateAndGetSharedParams(TestItemVO testItemVO) {
        JSONObject sharedParams = CommonUtil.deepCopy(testItemVO.getSharedParams());
        String trainType = sharedParams.getString(TRAIN_TYPE);

        if (StringUtils.isBlank(trainType)) {
            log.error("训练类型参数无效，testItemId={}", testItemVO.getId());
            return null;
        }

        return sharedParams;
    }

    /**
     * 处理克隆类型的服装信息创建
     *
     * @param sharedParams   公共参数
     * @param groups         测试组列表
     * @param clothingLoraId 服装模型ID
     */
    private Map<String, Integer> handleCloneMaterialInfo(JSONObject sharedParams, List<TestItemGroupVO> groups,
                                                         Integer clothingLoraId) {
        Map<String, Integer> resultMap = new HashMap<>();
        MaterialModelVO originModel = materialModelService.selectById(clothingLoraId);

        for (TestItemGroupVO group : groups) {
            // 处理对照组
            if (group.getGroupType().getCode().equals(TestGroupTypeEnum.CONTROL.getCode())) {
                updateGroupExtInfoAndStatus(group, clothingLoraId, resultMap);
                continue;
            }

            // 跳过已处理的组
            if (shouldSkipGroup(group, resultMap)) {
                continue;
            }

            // 创建材料请求
            // 处理实验组数据
            AddClothMaterialRequest request = processComparisonParamsAndCreateRequest(group, sharedParams);
            fillMaterialRequest(request, originModel);

            if (paramCheck(request, false)) {
                resultMap.put(group.getGroupType().name(), originModel.getId());
                updateGroupExtInfoAndStatus(group, originModel.getId(), resultMap);

                createAndSaveMaterial(group, request, resultMap);
            }
        }

        return resultMap;
    }

    /**
     * 处理创建类型的素材创建
     */
    private Map<String, Integer> handleCreateMaterialModel(JSONObject sharedParams, List<TestItemGroupVO> groups) {
        Map<String, Integer> resultMap = new HashMap<>();

        for (TestItemGroupVO group : groups) {
            if (shouldSkipGroup(group, resultMap)) {
                continue;
            }

            AddClothMaterialRequest request = processComparisonParamsAndCreateRequest(group, sharedParams);
            processDetailImgList(sharedParams, request);

            if (paramCheck(request, true)) {
                createAndSaveMaterial(group, request, resultMap);
            }
        }

        return resultMap;
    }

    /**
     * 判断是否应该跳过当前组的处理
     */
    private boolean shouldSkipGroup(TestItemGroupVO group, Map<String, Integer> resultMap) {
        return group.getBatchId() != null || processGroupLoraId(group, resultMap);
    }

    /**
     * 创建并保存服装
     */
    private void createAndSaveMaterial(TestItemGroupVO group, AddClothMaterialRequest request,
                                       Map<String, Integer> resultMap) {
        if (group.getGroupType() == TestGroupTypeEnum.EXPERIMENTAL) {
            request.setName(request.getName() + "_实验组_" + group.getId());
        } else {
            request.setName(request.getName() + "_对照组_" + group.getId());
        }

        MaterialModelVO model = clothLoraTrainService.create(request);

        if (model != null) {
            resultMap.put(group.getGroupType().name(), model.getId());
            updateGroupExtInfoAndStatus(group, model.getId(), resultMap);
        }
    }

    /**
     * 处理 detailImgList 中的 colorDescriptions 和 materialDetail
     *
     * @param sharedParams 共享参数
     * @param request      创作请求
     */
    private void processDetailImgList(JSONObject sharedParams, AddClothMaterialRequest request) {
        JSONObject detailImgList = sharedParams.getJSONObject("detailImgList");
        if (detailImgList != null) {
            // 获取 colorDescriptions
            List<String> colorDescriptions = detailImgList.getJSONArray("colorDescriptions").toJavaList(String.class);
            request.setColorDescriptions(colorDescriptions);

            // 获取 materialDetail
            ClothMaterialDetail clothMaterialDetail = detailImgList.getJSONObject("materialDetail").toJavaObject(
                ClothMaterialDetail.class);
            request.setMaterialDetail(clothMaterialDetail);
        }
    }

    /**
     * 参数校验
     *
     * @param request               请求入参
     * @param isCheckMaterialDetail 是否校验上传图片
     * @return 校验结果
     */
    private boolean paramCheck(AddClothMaterialRequest request, Boolean isCheckMaterialDetail) {
        // 去除字符串中的所有空白字符，包括空格、换行符、制表符等
        if (StringUtils.isNotBlank(request.getMatchPrefer())) {
            request.setMatchPrefer(request.getMatchPrefer().replaceAll("\\s+", ""));
        }

        // 克隆模式下的校验
        if (!isCheckMaterialDetail) {
            if (request.getMaterialDetail() == null) {
                log.error("[参数校验] 参数校验失败，materialDetailJson为空");
                return false;
            }
            return true;
        }

        // 创建模式下的校验
        ClothMaterialDetail detail = request.getMaterialDetail();
        if (detail == null) {
            log.error("[参数校验] 参数校验失败，materialDetail为空");
            return false;
        }

        // 校验图片列表完整性
        if (CollectionUtils.isEmpty(detail.getFullShotImgList()) || CollectionUtils.isEmpty(
            detail.getDetailShotImgList())) {
            log.error("[参数校验] 上传的素材不完整，全身图或细节图为空");
            return false;
        }

        // 校验全身图片字段
        for (ClothMaterialImg img : detail.getFullShotImgList()) {
            if (StringUtils.isBlank(img.getImgUrl()) || StringUtils.isBlank(img.getViewTags())
                || img.getColorGroupNumber() == null) {
                log.error("[参数校验] 上传的素材不完整，字段值缺失: {}", img);
                return false;
            }
        }

        return true;
    }

    /**
     * 使用新事务初始化测试任务
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void initPlanTaskWithNewTransaction(TestPlanVO plan, TestItemVO item, Map<String, Integer> materialInfoMap)
        throws IOException {
        initPlanTask(plan, item, materialInfoMap);
    }

    /**
     * 使用新事务更新测试计划状态
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateTestPlanStatusWithNewTransaction(TestPlanVO plan, TestStatusEnum status) {
        plan.setStatus(status);
        updateByIdSelective(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importAndUpdateExpResult(MultipartFile file, Integer itemId) throws IOException {
        List<TestResultVO> results = testResultService.queryByItemIdAndType(itemId, TestGroupTypeEnum.EXPERIMENTAL);

        AssertUtil.assertNotEmpty(results, "没有找到对应的实验组结果");

        List<String> ossList = zipHelper.importImagesFromZip(file);

        for (TestResultVO result : results) {
            Integer roundId = result.getRoundId();
            String newImage = ossList.stream().filter(name -> {
                String ossFileName = FileUtils.ossImageUrlToFullFileName(name);
                String currRoundId = StringUtils.substringBefore(ossFileName, "_");
                return StringUtils.equals(currRoundId, roundId.toString());
            }).findFirst().orElse(null);

            AssertUtil.assertNotBlank(newImage, "没有找到对应的图片,itemId=" + itemId + ",roundId=" + roundId);

            result.addExtInfo(KEY_ORIGIN_IMAGE, result.getImageUrl());
            result.setImageUrl(newImage);
            testResultService.updateByIdSelective(result);
        }
    }

    /**
     * 检测素材模态是否可用
     *
     * @param materialModalId id
     * @return boolean
     */
    public boolean checkMaterialModalStatus(Integer materialModalId) {
        // 素材模态id为空，直接返回false
        if (materialModalId == null) {
            return false;
        }

        // 获取素材模型信息
        MaterialModelVO materialModelVO = materialModelService.selectById(materialModalId);
        if (materialModelVO == null) {
            log.error("[checkMaterialModalStatus] 服装模型信息为空，loraId:{}", materialModalId);
            return false;
        }

        // 获取素材模态状态
        String status = materialModelVO.getStatus();

        // 素材模态状态为启用，返回true
        return MaterialModelStatusEnum.ENABLED.getCode().equals(status);
    }

    /**
     * 处理组的 loraId 并添加到结果映射中
     *
     * @param group     测试项组
     * @param resultMap 结果映射
     * @return 如果存在有效的loraId则返回true
     */
    private boolean processGroupLoraId(TestItemGroupVO group, Map<String, Integer> resultMap) {
        JSONObject groupExtInfo = group.getExtInfo();
        if (groupExtInfo != null) {
            Integer loraId = groupExtInfo.getInteger("loraId");
            if (loraId != null) {
                resultMap.put(group.getGroupType().getCode(), loraId);
                return true;
            }
        }
        return false;
    }

    /**
     * 处理比较参数并创建材料请求
     *
     * @param group        测试组
     * @param sharedParams 共享参数
     * @return 材料请求对象
     */
    private AddClothMaterialRequest processComparisonParamsAndCreateRequest(TestItemGroupVO group,
                                                                            JSONObject sharedParams) {
        // 获取实验参数
        JSONObject comparisonParams = group.getComparisonParams();

        // 对比参数 兼容处理
        comparisonParams = parseToNewComparisonParams(comparisonParams);

        // 对configs做特殊处理
        comparisonParams.forEach((key, value) -> {
            ContextUtils.fillWithContext(sharedParams, key, value);
        });

        // 转换为创作请求
        return sharedParams.toJavaObject(AddClothMaterialRequest.class);
    }

    /**
     * 更新测试组的扩展信息和状态
     *
     * @param group     测试组
     * @param modelId   模型idID
     * @param resultMap 材料信息ID
     */
    private void updateGroupExtInfoAndStatus(TestItemGroupVO group, Integer modelId, Map<String, Integer> resultMap) {
        if (modelId != null) {
            JSONObject extInfo = new JSONObject();
            extInfo.put("loraId", modelId);
            group.setExtInfo(extInfo);

            // 添加到结果映射中
            resultMap.put(group.getGroupType().getCode(), modelId);

            // 更新测试组关联的batchId
            testItemGroupService.updateByIdSelective(group);
        }
    }

    /**
     * 填充服装请求信息
     */
    private void fillMaterialRequest(AddClothMaterialRequest request, MaterialModelVO materialModel) {
        request.setName(materialModel.getName());
        request.setClothType(materialModel.getClothLoraTrainDetail().getClothType());
        request.setClothStyleType(materialModel.getClothLoraTrainDetail().getClothStyleType());
        request.setShowImage(materialModel.getShowImage());
        request.setColorDescription(materialModel.getClothLoraTrainDetail().getColorDescriptions());

        MaterialInfoVO materialInfo = materialInfoService.selectById(
            materialModel.getClothLoraTrainDetail().getOriginalMaterialId());
        request.setMaterialDetail(materialInfo.getMaterialDetail().toJavaObject(ClothMaterialDetail.class));
        request.setBizTag(TEST_CASE);
    }

    /**
     * 计算最大轮数
     *
     * @param items 实验项列表
     * @return 最大轮数
     */
    private int calcMaxRound(List<TestItemVO> items) {
        int maxRound = 0;
        for (TestItemVO item : items) {
            int round = testItemService.calcRoundsNum(item);
            maxRound = Math.max(maxRound, round);
        }
        return maxRound;
    }

    /**
     * 填充对比参数到sharedParams中
     *
     * @param materialInfoMap  服装信息
     * @param group            测试组
     * @param comparisonParams 对比参数
     * @param sharedParams     公共参数
     */
    private void fillComparisonParams(Map<String, Integer> materialInfoMap, TestItemGroupVO group,
                                      JSONObject comparisonParams, JSONObject sharedParams) {
        //兼容处理
        comparisonParams = parseToNewComparisonParams(comparisonParams);

        // 对比参数
        comparisonParams.forEach((key, value) -> {
            // 若 map 集合不为空且 map 集合的元素个数等于 2
            if (materialInfoMap != null && materialInfoMap.size() == 2) {
                // 参数替换为 loraId
                key = "loraId";
                // 参数值替换(获取对应组别的服装 id)
                value = materialInfoMap.get(group.getGroupType().getCode());
            }

            // 对configs做特殊处理
            ContextUtils.fillWithContext(sharedParams, key, value);
            // 对configs做特殊处理
            if (ELEMENT_KEY_LIST.contains(key)) {
                CreativeElementVO element = creativeElementService.queryRootKey(
                    ElementConfigKeyEnum.valueOf(StringUtils.upperCase(key)).name());
                JSONObject configs = sharedParams.getJSONObject("configs");
                configs.put(element.getId().toString(), value);
            }
        });
    }

    /**
     * 将老的对比参数转换成新的对比参数
     *
     * @param comparisonParams 对比参数
     * @return 新的对比参数
     */
    private JSONObject parseToNewComparisonParams(JSONObject comparisonParams) {
        //兼容处理
        if (comparisonParams.containsKey("key")) {
            String key = comparisonParams.getString("key");
            Object value = comparisonParams.get("value");
            comparisonParams = new JSONObject();
            comparisonParams.put(key, value);
        }

        return comparisonParams;
    }
}

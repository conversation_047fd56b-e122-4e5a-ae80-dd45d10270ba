package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TestCaseDO;
import ai.conrain.aigc.platform.dal.example.TestCaseExample;
import ai.conrain.aigc.platform.service.enums.TestCaseTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestCaseQuery;
import ai.conrain.aigc.platform.service.model.vo.TestCaseVO;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

/**
 * TestCaseConverter
 *
 * @version TestCaseService.java v 0.1 2025-08-12 07:10:16
 */
public class TestCaseConverter {

    /**
     * DO -> VO
     */
    public static TestCaseVO do2VO(TestCaseDO from) {
        TestCaseVO to = new TestCaseVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(TestCaseTypeEnum.getByCode(from.getType()));
        to.setCaseNum(from.getCaseNum());
        to.setUserId(from.getUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestCaseDO vo2DO(TestCaseVO from) {
        TestCaseDO to = new TestCaseDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType() != null ? from.getType().getCode() : null);
        to.setCaseNum(from.getCaseNum());
        to.setUserId(from.getUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setExtInfo(from.getExtInfo());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestCaseExample query2Example(TestCaseQuery from) {
        TestCaseExample to = new TestCaseExample();
        TestCaseExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getCaseNum())) {
            c.andCaseNumEqualTo(from.getCaseNum());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (TestCaseExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.setOffset((from.getPageNum() - 1) * from.getPageSize());
            to.setRows(from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestCaseVO> doList2VOList(List<TestCaseDO> list) {
        return CommonUtil.listConverter(list, TestCaseConverter::do2VO);
    }
}
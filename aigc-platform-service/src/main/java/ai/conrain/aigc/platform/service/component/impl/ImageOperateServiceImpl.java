package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.aliyun.EraseService;
import ai.conrain.aigc.platform.integration.aliyun.model.ImageOperateOutputModal;
import ai.conrain.aigc.platform.service.component.ImageOperateService;
import ai.conrain.aigc.platform.service.enums.ImageOperateTypeEnum;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ImageOperateVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 图片处理接口实现类
 */
@Slf4j
@Service
public class ImageOperateServiceImpl implements ImageOperateService {

    @Autowired
    private EraseService eraseService;

    @Override
    public String imageErase(ImageOperateVO imageOperateVO, String serverType) {
        // 参数校验
        AssertUtil.assertNotNull(imageOperateVO, ResultCode.PARAM_INVALID, "imageOperateVO is null");
        AssertUtil.assertNotNull(serverType, ResultCode.PARAM_INVALID, "serverType is null");

        // 服务类型校验
        ImageOperateTypeEnum imageOperateTypeEnum = ImageOperateTypeEnum.getByCode(serverType);
        if (imageOperateTypeEnum == null) {
            log.error("[图片擦除]ImageOperateServiceImpl::imageErase::图片处理服务类型：{} 不存在...",
                serverType);
            return null;
        }

        String originalImage = imageOperateVO.getOriginalImage();
        String maskImage = imageOperateVO.getMaskImage();
        String foregroundImage = imageOperateVO.getForegroundImage();
        if (originalImage == null || maskImage == null) {
            log.error("[图片擦除]ImageOperateServiceImpl::imageErase::源图片或Mask图片为空，图片处理终止...");
            return null;
        }

        log.info("[图片擦除]参数校验成功, originImage: {}, maskImage: {}", originalImage, maskImage);

        // 根据服务类型调用对应的服务
        switch (imageOperateTypeEnum) {
            case CUSTOM:
                log.error("[图片擦除]ImageOperateServiceImpl::imageErase::暂不支持自定义服务类型...");
                break;
            case ALIYUN:
                return eraseService.createEraseTask(originalImage, maskImage, foregroundImage);
        }

        return null;
    }

    @Override
    public ImageOperateOutputModal imageEraseQuery(String taskId, String serverType) {
        // 参数校验
        AssertUtil.assertNotNull(taskId, ResultCode.PARAM_INVALID, "taskId is null");
        AssertUtil.assertNotNull(serverType, ResultCode.PARAM_INVALID, "serverType is null");

        // 服务类型校验
        ImageOperateTypeEnum imageOperateTypeEnum = ImageOperateTypeEnum.getByCode(serverType);
        if (imageOperateTypeEnum == null) {
            log.error("[图片擦除]ImageOperateServiceImpl::imageEraseQuery::图片处理服务类型：{} 不存在...",
                serverType);
            return null;
        }

        // 根据服务类型调用对应的服务
        switch (imageOperateTypeEnum) {
            case CUSTOM:
                log.error("[图片擦除]ImageOperateServiceImpl::imageEraseQuery::暂不支持自定义服务类型...");
                break;
            case ALIYUN:
                return eraseService.queryEraseTask(taskId);
        }

        return null;
    }
}

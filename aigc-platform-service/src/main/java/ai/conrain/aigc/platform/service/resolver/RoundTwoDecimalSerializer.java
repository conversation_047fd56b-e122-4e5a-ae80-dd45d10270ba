/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.resolver;

import java.io.IOException;
import java.math.BigDecimal;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.ROUND_TWO_DECIMAL_FORMAT;

/**
 * Decimal格式化保留最多2位小数，如无小数则去掉小数，四舍五入
 *
 * <AUTHOR>
 * @version : RoundTwoDecimalSerializer.java, v 0.1 2023/12/17 17:06 renxiao.wu Exp $
 */
public class RoundTwoDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator j<PERSON><PERSON>enerator, SerializerProvider serializerProvider)
        throws IOException {
        if (null == bigDecimal) {
            return;
        }

        // 格式化保留两位小数
        jsonGenerator.writeString(ROUND_TWO_DECIMAL_FORMAT.format(bigDecimal));
    }
}

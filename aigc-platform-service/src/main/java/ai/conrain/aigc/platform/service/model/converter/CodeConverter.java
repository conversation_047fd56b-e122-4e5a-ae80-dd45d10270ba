package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.CodeDO;
import ai.conrain.aigc.platform.service.model.query.CodeQuery;
import ai.conrain.aigc.platform.dal.example.CodeExample;
import ai.conrain.aigc.platform.service.model.vo.CodeVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * CodeConverter
 *
 * @version CodeService.java v 0.1 2025-05-19 02:24:41
 */
public class CodeConverter {

    /**
     * DO -> VO
     */
    public static CodeVO do2VO(CodeDO from) {
        CodeVO to = new CodeVO();
        to.setId(from.getId());
        to.setCode(from.getCode());
        to.setCodeType(from.getCodeType());
        to.setCodeStatus(from.getCodeStatus());
        to.setCodeInfo(CommonUtil.parseObject(from.getCodeInfo()));
        to.setRelatedUserId(from.getRelatedUserId());
        to.setCreatorMasterId(from.getCreatorMasterId());
        to.setCreatorId(from.getCreatorId());
        to.setModifierId(from.getModifierId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static CodeDO vo2DO(CodeVO from) {
        CodeDO to = new CodeDO();
        to.setId(from.getId());
        to.setCode(from.getCode());
        to.setCodeType(from.getCodeType());
        to.setCodeStatus(from.getCodeStatus());
        to.setCodeInfo(CommonUtil.toJSONString(from.getCodeInfo()));
        to.setRelatedUserId(from.getRelatedUserId());
        to.setCreatorMasterId(from.getCreatorMasterId());
        to.setCreatorId(from.getCreatorId());
        to.setModifierId(from.getModifierId());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static CodeExample query2Example(CodeQuery from) {
        CodeExample to = new CodeExample();
        CodeExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getCode())) {
            c.andCodeEqualTo(from.getCode());
        }
        if (!ObjectUtils.isEmpty(from.getCodeType())) {
            c.andCodeTypeEqualTo(from.getCodeType());
        }
        if (!ObjectUtils.isEmpty(from.getCodeStatus())) {
            c.andCodeStatusEqualTo(from.getCodeStatus());
        }
        if (!ObjectUtils.isEmpty(from.getCodeInfo())) {
            c.andCodeInfoEqualTo(from.getCodeInfo());
        }
        if (!ObjectUtils.isEmpty(from.getRelatedUserId())) {
            c.andRelatedUserIdEqualTo(from.getRelatedUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorMasterId())) {
            c.andCreatorMasterIdEqualTo(from.getCreatorMasterId());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorId())) {
            c.andCreatorIdEqualTo(from.getCreatorId());
        }
        if (!ObjectUtils.isEmpty(from.getModifierId())) {
            c.andModifierIdEqualTo(from.getModifierId());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (CodeExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<CodeVO> doList2VOList(List<CodeDO> list) {
        return CommonUtil.listConverter(list, CodeConverter::do2VO);
    }
}
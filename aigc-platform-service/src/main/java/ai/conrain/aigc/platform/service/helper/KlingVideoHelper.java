package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.integration.kling.KlingProxy;
import ai.conrain.aigc.platform.integration.kling.KlingTaskParams;
import ai.conrain.aigc.platform.integration.kling.KlingTaskRet;
import ai.conrain.aigc.platform.integration.kling.KlingVideoServicePIAPI;
import ai.conrain.aigc.platform.integration.kling.VideoGeneratorService302API;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.constants.SystemConstants;
import ai.conrain.aigc.platform.service.model.vo.SystemConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class KlingVideoHelper implements KlingProxy {

    //302api
    @Autowired
    private VideoGeneratorService302API videoGeneratorService302API;

    //piapi
    @Autowired
    private KlingVideoServicePIAPI klingVideoServicePIAPI;

    @Autowired
    private SystemConfigService systemConfigService;

    private KlingProxy getKlingProxy(boolean getBackup) {
        // kling api名称, "piapi" / "302api"，缺省为piapi
        String apiName = systemConfigService.queryValueByKey(SystemConstants.KLING_API_NAME);
        if (StringUtils.equalsIgnoreCase(apiName,"302api")) {
            return getBackup ? klingVideoServicePIAPI : videoGeneratorService302API;
        } else {
            return getBackup ? videoGeneratorService302API : klingVideoServicePIAPI;
        }
    }

    @Override
    public String createVideoTask(KlingTaskParams params) {
        //按配置代理尝试
        String ret = getKlingProxy(false).createVideoTask(params);
        if (StringUtils.isNotBlank(ret)) {
            return ret;
        }

        //失败后，尝试切换代理
        ret = getKlingProxy(true).createVideoTask(params);

        //切换后成功了，则更新配置
        if (StringUtils.isNotBlank(ret)) {
            SystemConfigVO config = systemConfigService.queryByKey(SystemConstants.KLING_API_NAME);
            String current = config != null ? config.getConfValue() : "piapi";
            if (StringUtils.equalsIgnoreCase(config.getConfValue(), "302api")){
                config.setConfValue("piapi");
            } else {
                config.setConfValue("302api");
            }
            log.info("自动切换kling api: {} -> {}", current, config.getConfValue());
            systemConfigService.updateById(config);

        } else {
            log.error("切换kling api后尝试仍然失败，需要人工排查");
        }

        return ret;
    }

    @Override
    public KlingTaskRet getVideoTask(String taskId) {
        //302api（切换代理配置时，兼容旧的任务）
        if (StringUtils.startsWith(taskId, "kling_")) {
            return videoGeneratorService302API.getVideoTask(taskId);
        } else {
            return klingVideoServicePIAPI.getVideoTask(taskId);
        }
    }

    @Override
    public Float getAccountBalanceUSD() {
        return getKlingProxy(false).getAccountBalanceUSD();
    }
}

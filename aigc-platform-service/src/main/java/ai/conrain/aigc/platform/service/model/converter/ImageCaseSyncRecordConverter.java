package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.ImageCaseSyncRecordDO;
import ai.conrain.aigc.platform.service.model.query.ImageCaseSyncRecordQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseSyncRecordVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * ImageCaseSyncRecordConverter
 *
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
public class ImageCaseSyncRecordConverter {

    /**
     * DO -> VO
     */
    public static ImageCaseSyncRecordVO do2VO(ImageCaseSyncRecordDO from) {
        ImageCaseSyncRecordVO to = new ImageCaseSyncRecordVO();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setTargetServer(from.getTargetServer());
        to.setTargetStorePath(from.getTargetStorePath());
        to.setImageUrl(from.getImageUrl());
        to.setUploadTime(from.getUploadTime());
        to.setIsSuccess(from.getIsSuccess());
        to.setSyncType(from.getSyncType());

        return to;
    }

    /**
     * VO -> DO
     */
    public static ImageCaseSyncRecordDO vo2DO(ImageCaseSyncRecordVO from) {
        ImageCaseSyncRecordDO to = new ImageCaseSyncRecordDO();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setTargetServer(from.getTargetServer());
        to.setTargetStorePath(from.getTargetStorePath());
        to.setImageUrl(from.getImageUrl());
        to.setUploadTime(from.getUploadTime());
        to.setIsSuccess(from.getIsSuccess());
        to.setSyncType(from.getSyncType());

        return to;
    }

    /**
     * DO -> Query
     */
    public static ImageCaseSyncRecordQuery do2Query(ImageCaseSyncRecordDO from) {
        ImageCaseSyncRecordQuery to = new ImageCaseSyncRecordQuery();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setTargetServer(from.getTargetServer());
        to.setTargetStorePath(from.getTargetStorePath());
        to.setImageUrl(from.getImageUrl());
        to.setUploadTime(from.getUploadTime());
        to.setIsSuccess(from.getIsSuccess());
        to.setSyncType(from.getSyncType());

        return to;
    }

    /**
     * Query -> DO
     */
    public static ImageCaseSyncRecordDO query2DO(ImageCaseSyncRecordQuery from) {
        ImageCaseSyncRecordDO to = new ImageCaseSyncRecordDO();
        to.setId(from.getId());
        to.setCaseId(from.getCaseId());
        to.setTargetServer(from.getTargetServer());
        to.setTargetStorePath(from.getTargetStorePath());
        to.setImageUrl(from.getImageUrl());
        to.setUploadTime(from.getUploadTime());
        to.setIsSuccess(from.getIsSuccess());
        to.setSyncType(from.getSyncType());

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<ImageCaseSyncRecordVO> doList2VOList(List<ImageCaseSyncRecordDO> list) {
        return CommonUtil.listConverter(list, ImageCaseSyncRecordConverter::do2VO);
    }
}
package ai.conrain.aigc.platform.service.component.impl;

import java.util.*;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.UserPointService;
import ai.conrain.aigc.platform.service.component.UserService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.UserPointVO;
import ai.conrain.aigc.platform.service.model.vo.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.entity.StatsWarningInfoDO;
import ai.conrain.aigc.platform.dal.example.StatsWarningInfoExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsWarningInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsWarningInfoVO;
import ai.conrain.aigc.platform.service.model.converter.StatsWarningInfoConverter;
import ai.conrain.aigc.platform.dal.dao.StatsWarningInfoDAO;
import ai.conrain.aigc.platform.service.component.StatsWarningInfoService;

/**
 * StatsWarningInfoService实现
 *
 * <AUTHOR>
 * @version StatsWarningInfoService.java v 0.1 2025-05-19 04:36:33
 */
@Slf4j
@Service
public class StatsWarningInfoServiceImpl implements StatsWarningInfoService {

    /** DAO */
    @Autowired
    private StatsWarningInfoDAO statsWarningInfoDAO;
    @Autowired
    private UserService userService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private UserPointService userPointService;

    @Override
    public StatsWarningInfoVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        StatsWarningInfoDO data = statsWarningInfoDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return StatsWarningInfoConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = statsWarningInfoDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除StatsWarningInfo失败");
    }

    @Override
    public StatsWarningInfoVO insert(StatsWarningInfoVO statsWarningInfo) {
        AssertUtil.assertNotNull(statsWarningInfo, ResultCode.PARAM_INVALID, "statsWarningInfo is null");
        AssertUtil.assertTrue(statsWarningInfo.getId() == null, ResultCode.PARAM_INVALID, "statsWarningInfo.id is present");

        // 创建时间、修改时间兜底
        if (statsWarningInfo.getCreateTime() == null) {
            statsWarningInfo.setCreateTime(new Date());
        }

        if (statsWarningInfo.getModifyTime() == null) {
            statsWarningInfo.setModifyTime(new Date());
        }

        StatsWarningInfoDO data = StatsWarningInfoConverter.vo2DO(statsWarningInfo);
        Integer n = statsWarningInfoDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建StatsWarningInfo失败");
        AssertUtil.assertNotNull(data.getId(), "新建StatsWarningInfo返回id为空");
        statsWarningInfo.setId(data.getId());
        return statsWarningInfo;
    }


    @Override
    public void updateByIdSelective(StatsWarningInfoVO statsWarningInfo) {
        AssertUtil.assertNotNull(statsWarningInfo, ResultCode.PARAM_INVALID, "statsWarningInfo is null");
        AssertUtil.assertTrue(statsWarningInfo.getId() != null, ResultCode.PARAM_INVALID, "statsWarningInfo.id is null");

        // 修改时间必须更新
        statsWarningInfo.setModifyTime(new Date());
        StatsWarningInfoDO data = StatsWarningInfoConverter.vo2DO(statsWarningInfo);
        int n = statsWarningInfoDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新StatsWarningInfo失败，影响行数:" + n);
    }

    @Override
    public List<StatsWarningInfoVO> queryStatsWarningInfoList(StatsWarningInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsWarningInfoExample example = StatsWarningInfoConverter.query2Example(query);

        List<StatsWarningInfoDO> list = statsWarningInfoDAO.selectByExample(example);
        return StatsWarningInfoConverter.doList2VOList(list);
    }

    @Override
    public Long queryStatsWarningInfoCount(StatsWarningInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        StatsWarningInfoExample example = StatsWarningInfoConverter.query2Example(query);
        long c = statsWarningInfoDAO.countByExample(example);
        return c;
    }

    /**
     * 带条件分页查询
     */
    @Override
    public PageInfo<StatsWarningInfoVO> queryStatsWarningInfoByPage(StatsWarningInfoQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                && query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
                + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<StatsWarningInfoVO> page = new PageInfo<>();

        StatsWarningInfoExample example = StatsWarningInfoConverter.query2Example(query);
        long totalCount = statsWarningInfoDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<StatsWarningInfoDO> list = statsWarningInfoDAO.selectByExample(example);
        page.setList(StatsWarningInfoConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public int batchInsertOrUpdate(List<StatsWarningInfoVO> statsList) {
        if (CollectionUtils.isEmpty(statsList)) {
            return 0;
        }

        List<StatsWarningInfoDO> statsWarningInfoDOList = new ArrayList<>();
        for (StatsWarningInfoVO statsVO : statsList) {
            StatsWarningInfoDO statsWarningInfoDO = StatsWarningInfoConverter.vo2DO(statsVO);
            statsWarningInfoDOList.add(statsWarningInfoDO);
        }

        return statsWarningInfoDAO.batchInsertOrUpdate(statsWarningInfoDOList);
    }

    @Override
    public List<StatsWarningInfoVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType) {
        // 参数校验
        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate) || StringUtils.isBlank(statsType)) {
            return null;
        }

        // 查询统计数据
        List<StatsWarningInfoDO> statsWarningInfoDOList = statsWarningInfoDAO.selectStatsInfoByDateAndPeriod(startDate, endDate, statsType);

        // 转换为VO列表
        return StatsWarningInfoConverter.doList2VOList(statsWarningInfoDOList);
    }

    @Override
    public StatsWarningInfoVO getWarningInfo(Integer warningId) {
        log.info("开始获取预警信息数据，预警ID: {}", warningId);
        
        // 查询预警信息数据
        StatsWarningInfoDO statsWarningInfoDO = statsWarningInfoDAO.selectByPrimaryKey(warningId);
        if (statsWarningInfoDO == null) {
            log.warn("未找到预警ID: {} 的数据", warningId);
            return null;
        }
        
        // 转换为 VO
        StatsWarningInfoVO statsWarningInfoVO = StatsWarningInfoConverter.do2VO(statsWarningInfoDO);

        // 检查 ExtInfo 是否为空
        if (statsWarningInfoVO.getExtInfo() == null) {
            log.warn("预警ID: {} 的 ExtInfo 为空", warningId);
            return statsWarningInfoVO;
        }

        try {
            log.info("开始提取预警数据，预警ID: {}", warningId);
            
            // 提取周内不消耗客户id列表
            try {
                List<Integer> weeklyUserIdList = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_WEEKLY_USER_ID_LIST, List.class);
                if (weeklyUserIdList != null && !CollectionUtils.isEmpty(weeklyUserIdList)) {
                    log.info("周内不消耗客户数量: {}", weeklyUserIdList.size());
                    List<UserVO> weeklyUsers = userService.batchQueryById(weeklyUserIdList);
                    // 安装创建时间倒序排列（从早到晚）
                    weeklyUsers = weeklyUsers.stream()
                    .sorted(Comparator.comparing(UserVO::getCreateTime).reversed())
                    .collect(Collectors.toList());
                    statsWarningInfoVO.setWeeklyUserList(weeklyUsers);
                    log.info("成功查询到周内不消耗客户: {} 条记录", weeklyUsers.size());
                } else {
                    log.info("周内不消耗客户列表为空");
                    statsWarningInfoVO.setWeeklyUserList(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析周内不消耗客户列表异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setWeeklyUserList(Collections.emptyList());
            }
            
            // 提取月内不消耗客户id列表
            try {
                List<Integer> monthUserIdList = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_MONTH_USER_ID_LIST, List.class);
                if (monthUserIdList != null && !CollectionUtils.isEmpty(monthUserIdList)) {
                    log.info("月内不消耗客户数量: {}", monthUserIdList.size());
                    List<UserVO> monthlyUsers = userService.batchQueryById(monthUserIdList);
                    // 安装创建时间倒序排列（从早到晚）
                    monthlyUsers = monthlyUsers.stream()
                    .sorted(Comparator.comparing(UserVO::getCreateTime).reversed())
                    .collect(Collectors.toList());
                    statsWarningInfoVO.setMonthlyUserList(monthlyUsers);
                    log.info("成功查询到月内不消耗客户: {} 条记录", monthlyUsers.size());
                } else {
                    log.info("月内不消耗客户列表为空");
                    statsWarningInfoVO.setMonthlyUserList(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析月内不消耗客户列表异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setMonthlyUserList(Collections.emptyList());
            }
            
            // 提取退款率高的客户信息
            try {
                Map<String, String> customerRefundUserIdMap = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_CUSTOMER_REFUND_USER_ID_MAP, Map.class);
                if (customerRefundUserIdMap != null && !customerRefundUserIdMap.isEmpty()) {
                    log.info("退款率高的客户数量: {}", customerRefundUserIdMap.size());
                    // 提取用户 id
                    Set<String> userIdList = customerRefundUserIdMap.keySet();
                    List<Integer> userIdListInt = userIdList.stream().map(Integer::parseInt).collect(Collectors.toList());
                    List<UserVO> userVOList = userService.batchQueryById(userIdListInt);
                    
                    // 为用户设置退款率
                    List<UserVO> resultList = userVOList.stream()
                        .peek(userVO -> userVO.setRefundRate(customerRefundUserIdMap.get(userVO.getId().toString())))
                        .collect(Collectors.toList());

                    // 安装创建时间倒序排列（从早到晚）
                    resultList = resultList.stream()
                    .sorted(Comparator.comparing(UserVO::getCreateTime).reversed())
                    .collect(Collectors.toList());
                    
                    statsWarningInfoVO.setCustomerRefundUser(resultList);
                    log.info("成功查询到退款率高的客户: {} 条记录", resultList.size());
                } else {
                    log.info("退款率高的客户列表为空");
                    statsWarningInfoVO.setCustomerRefundUser(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析退款率高的客户信息异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setCustomerRefundUser(Collections.emptyList());
            }
            
            // 提取交付超时的服装信息
            try {
                Map<String, String> deliveryTimeoutMaterialIdMap = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_DELIVER_TIMEOUT_MATERIAL_ID_MAP, Map.class);
                if (deliveryTimeoutMaterialIdMap != null && !deliveryTimeoutMaterialIdMap.isEmpty()) {
                    log.info("交付超时的服装数量: {}", deliveryTimeoutMaterialIdMap.size());
                    // 提取服装 id
                    Set<String> materialIdList = deliveryTimeoutMaterialIdMap.keySet();
                    List<Integer> materialIdListInt = materialIdList.stream().map(Integer::parseInt).collect(Collectors.toList());

                    MaterialModelQuery materialModelQuery = new MaterialModelQuery();
                    materialModelQuery.setIds(materialIdListInt);
                    materialModelQuery.setOrderBy("create_time desc");
                    List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);

                    // 为服装设置超时小时数
                    List<MaterialModelVO> resultList = materialModelVOList.stream()
                        .peek(materialModelVO -> materialModelVO.setTimeoutHours(deliveryTimeoutMaterialIdMap.get(materialModelVO.getId().toString())))
                        .collect(Collectors.toList());
                    
                    statsWarningInfoVO.setDeliveryTimeoutMaterial(resultList);
                    log.info("成功查询到交付超时的服装: {} 条记录", resultList.size());
                } else {
                    log.info("交付超时的服装列表为空");
                    statsWarningInfoVO.setDeliveryTimeoutMaterial(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析交付超时的服装信息异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setDeliveryTimeoutMaterial(Collections.emptyList());
            }
            
            // 提取余额预警的客户信息
            try {
                Map<String, String> balanceWarningUserIdMap = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_BALANCE_WARNING_USER_ID_MAP, Map.class);
                if (balanceWarningUserIdMap != null && !balanceWarningUserIdMap.isEmpty()) {
                    log.info("余额预警的客户数量: {}", balanceWarningUserIdMap.size());
                    // 提取用户 id
                    Set<String> userIdList = balanceWarningUserIdMap.keySet();
                    List<Integer> userIdListInt = userIdList.stream().map(Integer::parseInt).collect(Collectors.toList());
                    UserPointQuery userPointQuery = new UserPointQuery();
                    userPointQuery.setUserIdList(userIdListInt);
                    List<UserPointVO> userPointVOs = userPointService.queryUserPointList(userPointQuery);

                    // 提取用户 id
                    List<UserVO> userVOList = userService.batchQueryById(userIdListInt);

                    // 将用户昵称设置进入userPointVOs
                    userVOList.forEach(userVO -> {
                        userPointVOs.stream()
                            .filter(userPointVO -> userPointVO.getUserId().equals(userVO.getId()))
                            .findFirst()
                            .ifPresent(userPointVO -> userPointVO.setNickName(userVO.getNickName()));
                    });

                    statsWarningInfoVO.setBalanceWarningUser(userPointVOs);
                    log.info("成功查询到余额预警的客户: {} 条记录", userPointVOs.size());
                } else {
                    log.info("余额预警的客户列表为空");
                    statsWarningInfoVO.setBalanceWarningUser(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析余额预警的客户信息异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setBalanceWarningUser(Collections.emptyList());
            }
            
            // 提取入库超过60天未转化的客户id列表
            try {
                List<Integer> sixtyDaysNoConvertUserIdList = statsWarningInfoVO.getExtInfo(CommonConstants.KEY_SIXTY_DAYS_NO_CONVERT_USER_ID_LIST, List.class);
                if (sixtyDaysNoConvertUserIdList != null && !CollectionUtils.isEmpty(sixtyDaysNoConvertUserIdList)) {
                    log.info("入库超过60天未转化的客户数量: {}", sixtyDaysNoConvertUserIdList.size());
                    List<UserVO> noConvertUsers = userService.batchQueryById(sixtyDaysNoConvertUserIdList);

                    // 安装创建时间倒序排列
                    noConvertUsers = noConvertUsers.stream() .sorted(Comparator.comparing(UserVO::getCreateTime).reversed()).collect(Collectors.toList());
                    statsWarningInfoVO.setSixtyDaysNoConvertUserList(noConvertUsers); 
                    log.info("成功查询到入库超过60天未转化的客户: {} 条记录", noConvertUsers.size());
                } else {
                    log.info("入库超过60天未转化的客户列表为空");
                    statsWarningInfoVO.setSixtyDaysNoConvertUserList(Collections.emptyList());
                }
            } catch (Exception e) {
                log.error("解析入库超过60天未转化的客户列表异常: {}", e.getMessage(), e);
                statsWarningInfoVO.setSixtyDaysNoConvertUserList(Collections.emptyList());
            }
            
            log.info("预警数据提取和查询完成，预警ID: {}", warningId);
        } catch (Exception e) {
            log.error("提取预警信息数据异常: {}", e.getMessage(), e);
            // 确保所有列表字段至少为空列表而不是null
            if (statsWarningInfoVO.getWeeklyUserList() == null) {
                statsWarningInfoVO.setWeeklyUserList(Collections.emptyList());
            }
            if (statsWarningInfoVO.getMonthlyUserList() == null) {
                statsWarningInfoVO.setMonthlyUserList(Collections.emptyList());
            }
            if (statsWarningInfoVO.getCustomerRefundUser() == null) {
                statsWarningInfoVO.setCustomerRefundUser(Collections.emptyList());
            }
            if (statsWarningInfoVO.getDeliveryTimeoutMaterial() == null) {
                statsWarningInfoVO.setDeliveryTimeoutMaterial(Collections.emptyList());
            }
            if (statsWarningInfoVO.getBalanceWarningUser() == null) {
                statsWarningInfoVO.setBalanceWarningUser(Collections.emptyList());
            }
            if (statsWarningInfoVO.getSixtyDaysNoConvertUserList() == null) {
                statsWarningInfoVO.setSixtyDaysNoConvertUserList(Collections.emptyList());
            }
        }

        return statsWarningInfoVO;
    }

}
package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.MaterialInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.MaterialInfoVO;

import java.util.List;

/**
 * 素材流水 Service定义
 *
 * <AUTHOR>
 * @version MaterialInfoService.java v 0.1 2024-05-10 10:56:27
 */
public interface MaterialInfoService {

    /**
     * 查询素材流水对象
     *
     * @param id 主键
     * @return 返回结果
     */
    MaterialInfoVO selectById(Integer id);

    /**
     * 删除素材流水对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加素材流水对象
     *
     * @param materialInfo 对象参数
     * @return 返回结果
     */
    MaterialInfoVO insert(MaterialInfoVO materialInfo);

    /**
     * 修改素材流水对象
     *
     * @param materialInfo 对象参数
     */
    void updateByIdSelective(MaterialInfoVO materialInfo);

    /**
     * 带条件批量查询素材流水列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<MaterialInfoVO> queryMaterialInfoList(MaterialInfoQuery query);

    /**
     * 带条件查询素材流水数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryMaterialInfoCount(MaterialInfoQuery query);

    /**
     * 带条件分页查询素材流水
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<MaterialInfoVO> queryMaterialInfoByPage(MaterialInfoQuery query);

    /**
     * 根据id列表批量查询素材流水列表
     *
     * @param ids 查询条件
     *            return 结果
     */
    List<MaterialInfoVO> batchQueryByIds(List<Integer> ids);

    /**
     * 构建激活词
     *
     * @param materialType 素材类型
     * @param labelType    打标类型
     * @param noCutout     不抠图
     * @return 激活词
     */
    String buildActivateKey(String materialType, String labelType, boolean noCutout);
}
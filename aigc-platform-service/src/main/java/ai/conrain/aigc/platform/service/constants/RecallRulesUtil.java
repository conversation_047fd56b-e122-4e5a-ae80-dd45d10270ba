package ai.conrain.aigc.platform.service.constants;

import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.service.enums.CategoryType;
import ai.conrain.aigc.platform.service.enums.ClothStyleFirstCategory;
import ai.conrain.aigc.platform.service.enums.ClothStyleSecondaryCategory;
import ai.conrain.aigc.platform.service.enums.LensCompositionType;
import ai.conrain.aigc.platform.service.enums.UsageMainCategoryEnum;
import ai.conrain.aigc.platform.service.enums.UsageSubCategoryEnum;
import ai.conrain.aigc.platform.service.model.ClothRecallRules;
import ai.conrain.aigc.platform.service.model.ClothingRuleResult;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/mcwagh/lluxyl3ux3dm4tt7#GfnY
 */
@Slf4j
public class RecallRulesUtil {

    private static final Map<ClothStyleFirstCategory, Map<Enum<?>, List<Enum<?>>>> CLOTH_STYLE_WHITE_LIST = new HashMap<>();
    private static final Map<ClothStyleFirstCategory, Map<Enum<?>, List<LensCompositionType>>> SHOOT_COMPOSITION_BLACK_LIST = new HashMap<>();

    static {
        initClothStyleWhiteList();
        initClothCompositionBlackList();
    }

    /**
     * 根据服装预标注信息一次性构建所有规则
     *
     * @param preCaption 服装预标注信息
     * @return 包含样式白名单、功能白名单和构图黑名单的所有规则结果
     */
    public static ClothRecallRules buildAllRules(ImageSimpleCaption preCaption) {

        AssertUtil.assertNotNull(preCaption, "preCaption is null");

        ImageSimpleCaption preCaptionObj = BeanUtils.deepCopy(preCaption, ImageSimpleCaption.class);

        IntegrationUtils.processNoneToNull(preCaptionObj);

        Map<String, List<String>> styleWhitelistRules = new HashMap<>();
        Map<String, List<String>> compositionBlacklistRules = new HashMap<>();
        Map<String, List<String>> functionWhitelistRules = new HashMap<>();

        // 处理上装分类 - 二级分类优先，一级分类兜底
        processClothingCategory(preCaptionObj.getTopCate1(), preCaptionObj.getTopCate2(), styleWhitelistRules, compositionBlacklistRules);

        // 处理下装分类 - 二级分类优先，一级分类兜底
        processClothingCategory(preCaptionObj.getBottomCate1(), preCaptionObj.getBottomCate2(), styleWhitelistRules, compositionBlacklistRules);

        // 处理连体服装分类 - 二级分类优先，一级分类兜底
        processClothingCategory(preCaptionObj.getOnePieceCate1(), preCaptionObj.getOnePieceCate2(), styleWhitelistRules, compositionBlacklistRules);

        // 处理套装分类 - 二级分类优先，一级分类兜底
        processClothingCategory(preCaptionObj.getSuitCate1(), preCaptionObj.getSuitCate2(), styleWhitelistRules, compositionBlacklistRules);

        // 用途分类白名单
        if (StringUtils.isNotBlank(preCaptionObj.getUsageCate1())) {
            processUsageWhitelist(preCaptionObj, functionWhitelistRules);
        }

        ClothRecallRules ret = new ClothRecallRules(styleWhitelistRules, functionWhitelistRules, compositionBlacklistRules);
        log.info("Building all rules for pre-caption: {}, rules result:{}", JSONObject.toJSONString(preCaptionObj), JSONObject.toJSONString(ret));
        return ret;
    }

    private static void processUsageWhitelist(ImageSimpleCaption preCaption, Map<String, List<String>> functionWhitelistRules) {
        UsageMainCategoryEnum mainCategory = UsageMainCategoryEnum.getByCode(preCaption.getUsageCate1());
        if (mainCategory != null) {
            switch (mainCategory) {
                case OUTDOOR_WEAR:
                case UNDERWEAR:
                case HOME_WEAR:
                case SWIMWEAR: {
                    functionWhitelistRules.put(CategoryType.USAGE_CATE_1.getKey(), List.of(mainCategory.getCode()));
                    break;
                }
                case SPORTSWEAR:
                case FUNCTIONAL: {
                    functionWhitelistRules.put(CategoryType.USAGE_CATE_1.getKey(), List.of(mainCategory.getCode()));
                    if (StringUtils.isNotBlank(preCaption.getUsageCate2())) {
                        UsageSubCategoryEnum subCategory = UsageSubCategoryEnum.getByMainCategoryAndCode(mainCategory, preCaption.getUsageCate2());
                        if (subCategory != null) {
                            functionWhitelistRules.put(CategoryType.USAGE_CATE_2.getKey(), List.of(subCategory.getCode()));
                        }
                    }
                    break;
                }
            }
        }
    }

    /**
     * 处理服装分类的通用方法 - 二级分类优先，一级分类兜底
     *
     * @param firstCate                 一级分类代码
     * @param secondCate                二级分类代码
     * @param styleWhitelistRules       样式白名单规则映射
     * @param compositionBlacklistRules 构图黑名单规则映射
     */
    private static void processClothingCategory(String firstCate, String secondCate,
                                                Map<String, List<String>> styleWhitelistRules,
                                              Map<String, List<String>> compositionBlacklistRules) {
        if (StringUtils.isBlank(firstCate)) {
            return;
        }

        ClothingRuleResult rules = null;
        
        // 优先使用二级分类规则
        if (StringUtils.isNotBlank(secondCate)) {
            rules = getStyleRules(firstCate, secondCate);
        }
        
        // 如果二级分类规则为空或不存在，使用一级分类规则
        if (rules == null || rules.isStyleWhiteListEmpty()) {
            ClothStyleFirstCategory firstCategory = ClothStyleFirstCategory.getByCode(firstCate);
            if (firstCategory != null) {
                rules = getStyleRules(firstCategory);
            }
        }
        
        // 处理样式白名单
        if (rules != null && !rules.isStyleWhiteListEmpty()) {
            // 按一级分类分组处理枚举
            Map<ClothStyleFirstCategory, List<String>> categoryGroupedStyles = new HashMap<>();
            
            for (Enum<?> styleEnum : rules.getStyleWhiteList()) {
                ClothStyleFirstCategory belongsToCategory = null;
                String styleCode = null;
                
                if (styleEnum instanceof ClothStyleSecondaryCategory) {
                    ClothStyleSecondaryCategory secondaryStyle = (ClothStyleSecondaryCategory) styleEnum;
                    belongsToCategory = secondaryStyle.getCategory();
                    styleCode = secondaryStyle.getCode();
                } else if (styleEnum instanceof ClothStyleFirstCategory) {
                    ClothStyleFirstCategory firstStyle = (ClothStyleFirstCategory) styleEnum;
                    belongsToCategory = firstStyle;
                    styleCode = firstStyle.getCode();
                }
                
                if (belongsToCategory != null && styleCode != null) {
                    categoryGroupedStyles.computeIfAbsent(belongsToCategory, k -> new ArrayList<>()).add(styleCode);
                }
            }
            
            // 为每个一级分类创建对应的规则映射
            for (Map.Entry<ClothStyleFirstCategory, List<String>> entry : categoryGroupedStyles.entrySet()) {
                ClothStyleFirstCategory category = entry.getKey();
                List<String> styleCodeList = entry.getValue();
                CategoryType categoryType = getCategoryTypeByFirstCategory(category);
                
                if (categoryType != null) {
                    if (styleWhitelistRules.containsKey(categoryType.getKey())) {
                        styleWhitelistRules.get(categoryType.getKey()).addAll(styleCodeList);
                    } else {
                        styleWhitelistRules.put(categoryType.getKey(), new ArrayList<>(styleCodeList));
                    }
                }
            }
        }

        // 处理构图黑名单
        if (rules != null && !rules.isCompositionBlackListEmpty()) {
            // 将枚举转换为字符串列表
            List<String> compositionCodeList = new ArrayList<>();
            for (LensCompositionType compositionEnum : rules.getCompositionBlackList()) {
                compositionCodeList.add(compositionEnum.getKey());
            }
            
            if (compositionBlacklistRules.containsKey(CategoryType.COMPOSITION.getKey())) {
                compositionBlacklistRules.get(CategoryType.COMPOSITION.getKey()).addAll(compositionCodeList);
            } else {
                compositionBlacklistRules.put(CategoryType.COMPOSITION.getKey(), compositionCodeList);
            }
        }
    }

    /**
     * 根据一级分类和二级分类获取服装规则结果
     *
     * @param firstLevel  ClothStyleFirstCategory（英文）
     * @param secondLevel 二级分类（英文）
     * @return 服装规则结果对象，包含样式白名单、构图黑名单和用途白名单
     */
    public static ClothingRuleResult getStyleRules(ClothStyleFirstCategory firstLevel, Enum<?> secondLevel) {
        List<Enum<?>> styleWhiteList = new ArrayList<>();
        List<LensCompositionType> compositionBlackList = new ArrayList<>();
        List<Enum<?>> usageWhiteList = new ArrayList<>();

        // 查询样式白名单
        if (CLOTH_STYLE_WHITE_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<Enum<?>>> secondLevelMap = CLOTH_STYLE_WHITE_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(secondLevel)) {
                styleWhiteList.addAll(secondLevelMap.get(secondLevel));
            }
        }

        // 查询构图黑名单
        if (SHOOT_COMPOSITION_BLACK_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<LensCompositionType>> secondLevelMap = SHOOT_COMPOSITION_BLACK_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(secondLevel)) {
                compositionBlackList.addAll(secondLevelMap.get(secondLevel));
            }
        }

        ClothingRuleResult result = new ClothingRuleResult(styleWhiteList, compositionBlackList, usageWhiteList);
        
        // 判断是否为一级分类规则：检查样式白名单中是否包含一级分类
        if (CLOTH_STYLE_WHITE_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<Enum<?>>> secondLevelMap = CLOTH_STYLE_WHITE_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(secondLevel)) {
                List<Enum<?>> whiteList = secondLevelMap.get(secondLevel);
                styleWhiteList.addAll(whiteList);
            }
        }
        
        return result;
    }

    /**
     * 根据一级分类获取服装规则结果（重载方法）
     * 用于一级分类兜底逻辑，查找以一级分类本身作为key的规则
     *
     * @param firstLevel ClothStyleFirstCategory（英文）
     * @return 服装规则结果对象，包含样式白名单、构图黑名单和用途白名单
     */
    public static ClothingRuleResult getStyleRules(ClothStyleFirstCategory firstLevel) {
        List<Enum<?>> styleWhiteList = new ArrayList<>();
        List<LensCompositionType> compositionBlackList = new ArrayList<>();
        List<Enum<?>> usageWhiteList = new ArrayList<>();

        // 查询样式白名单 - 查找以一级分类本身作为key的规则
        if (CLOTH_STYLE_WHITE_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<Enum<?>>> secondLevelMap = CLOTH_STYLE_WHITE_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(firstLevel)) {
                styleWhiteList.addAll(secondLevelMap.get(firstLevel));
            }
        }

        // 查询构图黑名单
        if (SHOOT_COMPOSITION_BLACK_LIST.containsKey(firstLevel)) {
            Map<Enum<?>, List<LensCompositionType>> secondLevelMap = SHOOT_COMPOSITION_BLACK_LIST.get(firstLevel);
            if (secondLevelMap.containsKey(firstLevel)) {
                compositionBlackList.addAll(secondLevelMap.get(firstLevel));
            }
        }

        return new ClothingRuleResult(styleWhiteList, compositionBlackList, usageWhiteList);
    }

    private static ClothingRuleResult getStyleRules(String firstLevel, String secondLevel) {
        // 根据字符串查找对应的枚举
        ClothStyleFirstCategory primaryCategory = null;
        for (ClothStyleFirstCategory category : ClothStyleFirstCategory.values()) {
            if (category.getCode().equals(firstLevel) || category.getDesc().equals(firstLevel)) {
                primaryCategory = category;
                break;
            }
        }

        if (primaryCategory == null) {
            return new ClothingRuleResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }

        Enum<?> secondaryCategory = findSecondaryCategory(primaryCategory, secondLevel);
        if (secondaryCategory == null) {
            return new ClothingRuleResult(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        }

        return getStyleRules(primaryCategory, secondaryCategory);
    }

    private static Enum<?> findSecondaryCategory(ClothStyleFirstCategory primaryCategory, String secondLevel) {
        // 遍历所有二级分类枚举类
        Class<?>[] enumClasses = {ClothStyleSecondaryCategory.外套类型.class, ClothStyleSecondaryCategory.冬季外套类型.class, ClothStyleSecondaryCategory.上衣类型.class, ClothStyleSecondaryCategory.半裙类型.class, ClothStyleSecondaryCategory.裤子类型.class, ClothStyleSecondaryCategory.连衣裙类型.class, ClothStyleSecondaryCategory.套装类型.class};

        for (Class<?> enumClass : enumClasses) {
            if (enumClass.isEnum()) {
                Enum<?>[] enumConstants = (Enum<?>[]) enumClass.getEnumConstants();
                for (Enum<?> enumConstant : enumConstants) {
                    if (enumConstant instanceof ClothStyleSecondaryCategory) {
                        ClothStyleSecondaryCategory category = (ClothStyleSecondaryCategory) enumConstant;
                        if (category.getCategory().equals(primaryCategory) && category.getCode().equals(secondLevel)) {
                            return enumConstant;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据一级分类枚举获取对应的CategoryType（二级分类）
     *
     * @param firstCategory 一级分类枚举
     * @return 对应的二级CategoryType
     */
    private static CategoryType getCategoryTypeByFirstCategory(ClothStyleFirstCategory firstCategory) {
        switch (firstCategory) {
            case 外套:
            case 冬季外套:
            case 上衣:
                return CategoryType.TOP_CATE_2;
            case 半裙:
            case 裤子:
                return CategoryType.BOTTOM_CATE_2;
            case 连衣裙:
            case 连体衣:
                return CategoryType.ONEPIECE_CATE_2;
            case 套装:
                return CategoryType.SUIT_CATE_2;
            default:
                return null;
        }
    }

    /**
     * 初始化参考图衣服结构白名单
     * 根据服装一级分类和二级分类，定义此类服装可以作为参考图的白名单样式
     */
    private static void initClothStyleWhiteList() {
        // 外套
        {
            Map<Enum<?>, List<Enum<?>>> outerwear = new HashMap<>();

            // 大衣 -> 大衣/风衣/派克大衣/夹克
            outerwear.put(ClothStyleSecondaryCategory.外套类型.大衣, List.of(ClothStyleSecondaryCategory.外套类型.大衣, ClothStyleSecondaryCategory.外套类型.风衣, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.外套类型.夹克));

            // 西装外套 -> 西装外套/夹克/开衫/西装套装
            outerwear.put(ClothStyleSecondaryCategory.外套类型.西装外套, List.of(ClothStyleSecondaryCategory.外套类型.西装外套, ClothStyleSecondaryCategory.外套类型.夹克, ClothStyleSecondaryCategory.外套类型.开衫, ClothStyleSecondaryCategory.套装类型.西装套装));

            // 风衣 -> 大衣/风衣/派克大衣/夹克
            outerwear.put(ClothStyleSecondaryCategory.外套类型.风衣, List.of(ClothStyleSecondaryCategory.外套类型.大衣, ClothStyleSecondaryCategory.外套类型.风衣, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.外套类型.夹克));

            // 夹克 -> 夹克/开衫/西装外套/休闲套装
            outerwear.put(ClothStyleSecondaryCategory.外套类型.夹克, List.of(ClothStyleSecondaryCategory.外套类型.夹克, ClothStyleSecondaryCategory.外套类型.开衫, ClothStyleSecondaryCategory.外套类型.西装外套, ClothStyleSecondaryCategory.套装类型.休闲套装));

            // 开衫 -> 夹克/开衫/西装外套/休闲套装
            outerwear.put(ClothStyleSecondaryCategory.外套类型.开衫, List.of(ClothStyleSecondaryCategory.外套类型.夹克, ClothStyleSecondaryCategory.外套类型.开衫, ClothStyleSecondaryCategory.外套类型.西装外套, ClothStyleSecondaryCategory.套装类型.休闲套装));

            // 新中式外套 -> 新中式外套/新中式套装
            outerwear.put(ClothStyleSecondaryCategory.外套类型.新中式外套, List.of(ClothStyleSecondaryCategory.外套类型.新中式外套, ClothStyleSecondaryCategory.套装类型.新中式套装));

            // 其它外套 -> 所有外套（一级类目为‘外套’，不指定二级类目）
            outerwear.put(ClothStyleSecondaryCategory.外套类型.其它外套, List.of(ClothStyleFirstCategory.外套));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.外套, outerwear);
        }

        // 冬季外套
        {
            Map<Enum<?>, List<Enum<?>>> winterOuterwear = new HashMap<>();

            // 羽绒服 -> 羽绒服/皮草/派克大衣/棉服
            winterOuterwear.put(ClothStyleSecondaryCategory.冬季外套类型.羽绒服, List.of(ClothStyleSecondaryCategory.冬季外套类型.羽绒服, ClothStyleSecondaryCategory.冬季外套类型.皮草, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.冬季外套类型.棉服));

            // 皮草 -> 羽绒服/皮草/派克大衣/棉服
            winterOuterwear.put(ClothStyleSecondaryCategory.冬季外套类型.皮草, List.of(ClothStyleSecondaryCategory.冬季外套类型.羽绒服, ClothStyleSecondaryCategory.冬季外套类型.皮草, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.冬季外套类型.棉服));

            // 派克大衣 -> 羽绒服/皮草/派克大衣/棉服
            winterOuterwear.put(ClothStyleSecondaryCategory.冬季外套类型.派克大衣, List.of(ClothStyleSecondaryCategory.冬季外套类型.羽绒服, ClothStyleSecondaryCategory.冬季外套类型.皮草, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.冬季外套类型.棉服));

            // 棉服 -> 羽绒服/皮草/派克大衣/棉服
            winterOuterwear.put(ClothStyleSecondaryCategory.冬季外套类型.棉服, List.of(ClothStyleSecondaryCategory.冬季外套类型.羽绒服, ClothStyleSecondaryCategory.冬季外套类型.皮草, ClothStyleSecondaryCategory.冬季外套类型.派克大衣, ClothStyleSecondaryCategory.冬季外套类型.棉服));

            // 其它冬季外套 -> 冬季外套
            winterOuterwear.put(ClothStyleSecondaryCategory.冬季外套类型.其它冬季外套, List.of(ClothStyleFirstCategory.冬季外套));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.冬季外套, winterOuterwear);
        }

        // 上衣
        {
            Map<Enum<?>, List<Enum<?>>> tops = new HashMap<>();

            // T恤 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.T恤, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // 短袖T恤 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.短袖T恤, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // 卫衣 -> 卫衣/毛衣
            tops.put(ClothStyleSecondaryCategory.上衣类型.卫衣, List.of(ClothStyleSecondaryCategory.上衣类型.卫衣, ClothStyleSecondaryCategory.上衣类型.毛衣));

            // 毛衣 -> 卫衣/毛衣
            tops.put(ClothStyleSecondaryCategory.上衣类型.毛衣, List.of(ClothStyleSecondaryCategory.上衣类型.卫衣, ClothStyleSecondaryCategory.上衣类型.毛衣));

            // 打底衫 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.打底衫, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // 吊带 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.吊带, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // POLO衫 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.POLO衫, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // 衬衫 -> 吊带/打底衫/T恤/短袖T恤/衬衫/POLO衫
            tops.put(ClothStyleSecondaryCategory.上衣类型.衬衫, List.of(ClothStyleSecondaryCategory.上衣类型.吊带, ClothStyleSecondaryCategory.上衣类型.打底衫, ClothStyleSecondaryCategory.上衣类型.T恤, ClothStyleSecondaryCategory.上衣类型.短袖T恤, ClothStyleSecondaryCategory.上衣类型.衬衫, ClothStyleSecondaryCategory.上衣类型.POLO衫));

            // 新中式上衣 -> 新中式上衣/新中式套装/新中式半裙
            tops.put(ClothStyleSecondaryCategory.上衣类型.新中式上衣, List.of(ClothStyleSecondaryCategory.上衣类型.新中式上衣, ClothStyleSecondaryCategory.套装类型.新中式套装, ClothStyleSecondaryCategory.半裙类型.新中式半裙));

            // 其它 -> 其它上衣
            tops.put(ClothStyleSecondaryCategory.上衣类型.其它上衣, List.of(ClothStyleFirstCategory.上衣));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.上衣, tops);
        }

        // 半裙
        {
            Map<Enum<?>, List<Enum<?>>> skirt = new HashMap<>();

            // 新中式半裙 -> 新中式半裙/新中式套装/新中式上衣/新中式外套
            skirt.put(ClothStyleSecondaryCategory.半裙类型.新中式半裙, List.of(ClothStyleSecondaryCategory.半裙类型.新中式半裙, ClothStyleSecondaryCategory.套装类型.新中式套装, ClothStyleSecondaryCategory.上衣类型.新中式上衣, ClothStyleSecondaryCategory.外套类型.新中式外套));

            // 其它半裙 -> 半裙
            skirt.put(ClothStyleSecondaryCategory.半裙类型.其它半裙, List.of(ClothStyleFirstCategory.半裙));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.半裙, skirt);
        }

        // 裤子
        {
            Map<Enum<?>, List<Enum<?>>> pants = new HashMap<>();

            // 牛仔裤 -> 牛仔裤/运动裤/工装裤/短裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.牛仔裤, List.of(ClothStyleSecondaryCategory.裤子类型.牛仔裤, ClothStyleSecondaryCategory.裤子类型.运动裤, ClothStyleSecondaryCategory.裤子类型.工装裤, ClothStyleSecondaryCategory.裤子类型.短裤));

            // 西装裤 -> 西装裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.西装裤, List.of(ClothStyleSecondaryCategory.裤子类型.西装裤));

            // 运动裤 -> 牛仔裤/运动裤/工装裤/短裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.运动裤, List.of(ClothStyleSecondaryCategory.裤子类型.牛仔裤, ClothStyleSecondaryCategory.裤子类型.运动裤, ClothStyleSecondaryCategory.裤子类型.工装裤, ClothStyleSecondaryCategory.裤子类型.短裤));

            // 短裤 -> 短裤/牛仔裤/运动裤/工装裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.短裤, List.of(ClothStyleSecondaryCategory.裤子类型.短裤, ClothStyleSecondaryCategory.裤子类型.牛仔裤, ClothStyleSecondaryCategory.裤子类型.运动裤, ClothStyleSecondaryCategory.裤子类型.工装裤));

            // 工装裤 -> 牛仔裤/运动裤/工装裤/短裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.工装裤, List.of(ClothStyleSecondaryCategory.裤子类型.牛仔裤, ClothStyleSecondaryCategory.裤子类型.运动裤, ClothStyleSecondaryCategory.裤子类型.工装裤, ClothStyleSecondaryCategory.裤子类型.短裤));

            // 打底裤 ->打底裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.打底裤, List.of(ClothStyleSecondaryCategory.裤子类型.打底裤));

            // 登山裤 -> 登山裤
            pants.put(ClothStyleSecondaryCategory.裤子类型.登山裤, List.of(ClothStyleSecondaryCategory.裤子类型.登山裤));

            // 其它
            pants.put(ClothStyleSecondaryCategory.裤子类型.其它裤子, List.of(ClothStyleFirstCategory.裤子));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.裤子, pants);
        }

        // 连衣裙
        {
            Map<Enum<?>, List<Enum<?>>> dress = new HashMap<>();

            // 新中式连衣裙 -> 新中式连衣裙/新中式套装
            dress.put(ClothStyleSecondaryCategory.连衣裙类型.新中式连衣裙, List.of(ClothStyleSecondaryCategory.连衣裙类型.新中式连衣裙, ClothStyleSecondaryCategory.套装类型.新中式套装));

            // 其它 -> 其它连衣裙
            dress.put(ClothStyleSecondaryCategory.连衣裙类型.其它连衣裙, List.of(ClothStyleFirstCategory.连衣裙));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.连衣裙, dress);
        }

        // 连体衣
        {
            Map<Enum<?>, List<Enum<?>>> jumpsuit = new HashMap<>();
            jumpsuit.put(ClothStyleFirstCategory.连体衣, List.of(ClothStyleFirstCategory.连体衣));
            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.连体衣, jumpsuit);
        }

        // 套装
        {
            Map<Enum<?>, List<Enum<?>>> suit = new HashMap<>();

            // 西装套装 -> 西装套装/外套/上衣/裤子/半裙
            suit.put(ClothStyleSecondaryCategory.套装类型.西装套装, List.of(ClothStyleSecondaryCategory.套装类型.西装套装, ClothStyleFirstCategory.外套, ClothStyleFirstCategory.上衣, ClothStyleFirstCategory.裤子, ClothStyleFirstCategory.半裙));

            // 休闲套装 -> 休闲套装/外套/上衣/裤子/半裙
            suit.put(ClothStyleSecondaryCategory.套装类型.休闲套装, List.of(ClothStyleSecondaryCategory.套装类型.休闲套装, ClothStyleFirstCategory.外套, ClothStyleFirstCategory.上衣, ClothStyleFirstCategory.裤子, ClothStyleFirstCategory.半裙));

            // 新中式套装 -> 新中式套装/新中式外套/新中式上衣/新中式半裙/新中式连衣裙
            suit.put(ClothStyleSecondaryCategory.套装类型.新中式套装, List.of(ClothStyleSecondaryCategory.套装类型.新中式套装, ClothStyleSecondaryCategory.外套类型.新中式外套, ClothStyleSecondaryCategory.上衣类型.新中式上衣, ClothStyleSecondaryCategory.半裙类型.新中式半裙, ClothStyleSecondaryCategory.连衣裙类型.新中式连衣裙));

            // 职业套装 -> 职业套装
            suit.put(ClothStyleSecondaryCategory.套装类型.职业套装, List.of(ClothStyleSecondaryCategory.套装类型.职业套装));

            // 汉服 -> 汉服
            suit.put(ClothStyleSecondaryCategory.套装类型.汉服, List.of(ClothStyleSecondaryCategory.套装类型.汉服));

            // 其它
            suit.put(ClothStyleSecondaryCategory.套装类型.其它套装, List.of(ClothStyleFirstCategory.套装));

            CLOTH_STYLE_WHITE_LIST.put(ClothStyleFirstCategory.套装, suit);
        }
    }

    /**
     * 初始化构图黑名单
     * 根据表格中的"构图黑名单"列，定义不适合的构图方式：
     * - 上半身类服装（外套、冬季外套、上衣）：不适合 Lower_body_close_up
     * - 下半身类服装（半裙、裤子）：不适合 Upper_body_close_up
     */
    private static void initClothCompositionBlackList() {
        // 外套 - 上半身服装
        Map<Enum<?>, List<LensCompositionType>> outerwear = new HashMap<>();
        outerwear.put(ClothStyleFirstCategory.外套, List.of(LensCompositionType.LOWER_BODY_CLOSE_UP));
        SHOOT_COMPOSITION_BLACK_LIST.put(ClothStyleFirstCategory.外套, outerwear);

        // 冬季外套
        Map<Enum<?>, List<LensCompositionType>> winterOuterwear = new HashMap<>();
        winterOuterwear.put(ClothStyleFirstCategory.冬季外套, List.of(LensCompositionType.LOWER_BODY_CLOSE_UP));
        SHOOT_COMPOSITION_BLACK_LIST.put(ClothStyleFirstCategory.冬季外套, winterOuterwear);

        // 上衣
        Map<Enum<?>, List<LensCompositionType>> tops = new HashMap<>();
        tops.put(ClothStyleFirstCategory.上衣, List.of(LensCompositionType.LOWER_BODY_CLOSE_UP));
        SHOOT_COMPOSITION_BLACK_LIST.put(ClothStyleFirstCategory.上衣, tops);

        // 半裙
        Map<Enum<?>, List<LensCompositionType>> skirt = new HashMap<>();
        skirt.put(ClothStyleFirstCategory.半裙, List.of(LensCompositionType.UPPER_BODY_CLOSE_UP));
        SHOOT_COMPOSITION_BLACK_LIST.put(ClothStyleFirstCategory.半裙, skirt);

        // 裤子
        Map<Enum<?>, List<LensCompositionType>> pants = new HashMap<>();
        pants.put(ClothStyleFirstCategory.裤子, List.of(LensCompositionType.UPPER_BODY_CLOSE_UP));
        SHOOT_COMPOSITION_BLACK_LIST.put(ClothStyleFirstCategory.裤子, pants);
    }

}
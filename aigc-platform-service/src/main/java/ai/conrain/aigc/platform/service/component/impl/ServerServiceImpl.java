package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.ServerDAO;
import ai.conrain.aigc.platform.dal.entity.ServerDO;
import ai.conrain.aigc.platform.integration.ai.ComfyUIService;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult.QueueCodeEnum;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.service.component.ServerService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.component.dispatch.ServerCache;
import ai.conrain.aigc.platform.service.component.dispatch.ServerHelper;
import ai.conrain.aigc.platform.service.enums.ServerStatusEnum;
import ai.conrain.aigc.platform.service.enums.ServerTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.MachineInfo;
import ai.conrain.aigc.platform.service.model.biz.MachinePort;
import ai.conrain.aigc.platform.service.model.biz.MachineRoom;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ServerConverter;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.SERVICE_TYPE_CREATIVE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.SERVICE_TYPE_TRAIN;

/**
 * ServerService实现
 *
 * <AUTHOR>
 * @version ServerService.java v 0.1 2024-06-15 04:58:30
 */
@Slf4j
@Service
public class ServerServiceImpl extends AbstractCachedService<ServerVO, Integer, String> implements ServerService {
    @Autowired
    private ServerDAO serverDAO;
    @Autowired
    private ComfyUIService comfyUIService;
    @Autowired
    private TairService tairService;
    @Autowired
    private ServerHelper serverHelper;
    @Value("${comfyui.output.path}")
    private String outputPath;
    @Lazy
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public ServerVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ServerDO data = serverDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ServerConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = serverDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除Server失败");

        //强制刷新
        forceRefresh();
    }

    @Override
    public ServerVO insert(ServerVO server) {
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "server is null");
        AssertUtil.assertTrue(server.getId() == null, ResultCode.PARAM_INVALID, "server.id is present");

        //创建时间、修改时间兜底
        if (server.getCreateTime() == null) {
            server.setCreateTime(new Date());
        }

        if (server.getModifyTime() == null) {
            server.setModifyTime(new Date());
        }

        ServerDO data = ServerConverter.vo2DO(server);
        int n = serverDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建Server失败");
        AssertUtil.assertNotNull(data.getId(), "新建Server返回id为空");
        server.setId(data.getId());

        refresh();

        return server;
    }

    @Override
    public void updateById(ServerVO server) {
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "server is null");
        AssertUtil.assertTrue(server.getId() != null, ResultCode.PARAM_INVALID, "server.id is null");

        ServerVO origin = selectById(server.getId());
        origin.setStatus(server.getStatus() != null ? server.getStatus() : origin.getStatus());
        origin.setConfig(StringUtils.isNotBlank(server.getConfig()) ? server.getConfig() : origin.getConfig());
        origin.setName(StringUtils.isNotBlank(server.getName()) ? server.getName() : origin.getName());
        origin.setType(server.getType() != null ? server.getType() : origin.getType());
        origin.setConfigAlias(server.getConfigAlias());
        origin.setIntranetAddress(server.getIntranetAddress());
        origin.setDeviceId(server.getDeviceId());

        //修改时间必须更新
        origin.setModifyTime(new Date());

        ServerDO data = ServerConverter.vo2DO(origin);
        int n = serverDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新Server失败，影响行数:" + n);

        refresh();
    }

    @Override
    public List<ServerVO> queryByPipeline(Integer id) {
        return ServerConverter.toListByLevel(
            queryAll().stream().filter(s -> s.getPipelineId().equals(id)).collect(Collectors.toList()));
    }

    @Override
    public ServerVO test(Integer serverId) {
        ServerVO target = queryByKey(serverId);
        AssertUtil.assertNotNull(target, ResultCode.PARAM_INVALID, "server.id is null");

        target.setRealtimeStatus(test(target));

        return target;
    }

    @Override
    public ServerStatusEnum test(ServerVO server) {
        ServerStatusEnum status = null;
        ServerVO parent = queryByKey(server.getParentId());

        try {
            if (server.getType() == ServerTypeEnum.FILE_SERVICE || server.getType() == ServerTypeEnum.MODEL_SERVER) {
                int cnt = comfyUIService.queryImageCnt(outputPath, null, serverHelper.getServerUrl(server));
                log.info("测试服务[{}]，返回图片数量[{}]", server.getName(), cnt);
                status = ServerStatusEnum.IDLE;
            } else {
                QueueResult queue = comfyUIService.testByQueue(parent.getConfig(), server.getConfig());

                if (queue.getCode() == QueueCodeEnum.UNKNOWN) {
                    status = ServerStatusEnum.UNUSABLE;
                } else {
                    if (queue.getQueueSize() > 0) {
                        status = ServerStatusEnum.BUSY;
                    } else {
                        status = ServerStatusEnum.IDLE;
                    }
                }
            }
        } catch (Exception e) {
            log.error("测试服务异常", e);
            status = ServerStatusEnum.UNUSABLE;
        }

        ServerCache cache = serverHelper.getFromTair(server.getId());
        if (cache == null) {
            cache = new ServerCache();
            cache.setStatus(server.getStatus());
        }

        if (server.getStatus() != ServerStatusEnum.DISABLE) {

            if (cache.getStatus() == null || cache.getStatus() != status) {
                log.info("测试服务端口serverId:{},{}:{}缓存状态变更:{} -> {}", server.getId(),
                    parent != null ? parent.getName() : null, server.getConfig(), cache.getStatus(), status);
            }

            cache.setStatus(status);
            if (status != ServerStatusEnum.UNUSABLE) {
                cache.setFailTimes(0);
            }
        }

        if (cache.getTaskId() != null) {
            server.setRealtimeTaskId(cache.getTaskId());
        }
        serverHelper.putToTair(server.getId(), cache);
        return status;
    }

    @Override
    public ServerVO parseByUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        boolean hasPort = StringUtils.split(url, ":").length > 2;
        String serverUrl = hasPort ? StringUtils.substringBeforeLast(url, ":") : url;
        String port = hasPort ? StringUtils.substringAfterLast(url, ":") : null;

        //先进行别名匹配
        ServerVO alias = queryAll().stream().filter(
            server -> StringUtils.equals(server.getConfigAlias(), serverUrl) && StringUtils.equals(server.getConfig(),
                port) && server.getLevel() == 2 && serverHelper.isEnable(server)).findFirst().orElse(null);

        if (alias != null) {
            log.warn("【服务路由】获取服务信息成功,命中服务别名{},alias={}", url, alias.getConfigAlias());
            return alias;
        }

        //文件服务需要判断服务可用
        List<ServerVO> servers = queryAll().stream().filter(
            server -> server.getLevel() == 1 && serverHelper.isEnable(server)).filter(
            server -> StringUtils.equals(server.getConfig(), serverUrl) || (StringUtils.isNotBlank(
                server.getIntranetAddress()) && StringUtils.contains(serverUrl, server.getIntranetAddress()))).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(servers)) {
            log.warn("【服务路由】获取服务信息失败,关联的服务列表为空{}", url);
            return null;
        }

        if (StringUtils.isBlank(port)) {
            return servers.get(0);
        }

        //优先遍历可用的节点
        for (ServerVO parent : servers) {
            if (CollectionUtils.isEmpty(parent.getChildren())) {
                continue;
            }

            ServerVO server = parent.getChildren().stream().filter(child -> child.getConfig().equals(port)).filter(
                child -> serverHelper.isEnable(child)).findFirst().orElse(null);

            if (server != null) {
                return server;
            }
        }

        //如果遍历不到可用节点，则针对文件服务不进行状态判断
        //TODO 看线上有没有日志，没有的话，可以把下面这个遍历删除
        for (ServerVO parent : servers) {
            if (CollectionUtils.isEmpty(parent.getChildren())) {
                continue;
            }

            ServerVO server = parent.getChildren().stream().filter(child -> child.getConfig().equals(port)).filter(
                    child -> child.getType() != ServerTypeEnum.FILE_SERVICE || serverHelper.isEnable(child)).findFirst()
                .orElse(null);

            if (server != null) {
                log.info("【服务路由】获取服务信息成功,遍历所有关联服务列表后，未找到对应端口,但找到不可用的节点，{}", url);
                return server;
            }
        }

        log.warn("【服务路由】获取服务信息失败,遍历所有关联服务列表后，未找到对应端口{},servers.size={}", url,
            CollectionUtils.size(servers));

        return null;

    }

    @Override
    public void initDeviceInfo() {
        //TODO by半泉:临时代码
        List<MachineRoom> rooms = systemConfigService.queryDeviceInfo();
        List<ServerVO> servers = queryAll().stream().filter(server -> server.getLevel() == 1).collect(
            Collectors.toList());

        for (ServerVO server : servers) {
            if (StringUtils.isNotBlank(server.getDeviceId())) {
                continue;
            }

            boolean hasIntranetAddress = StringUtils.isNotBlank(server.getIntranetAddress());
            MachineInfo hit = null;

            for (MachineRoom room : rooms) {
                if (hit != null) {
                    break;
                }
                for (MachineInfo machine : room.getMachines()) {
                    if (StringUtils.equals(server.getConfig(), machine.getPublicAddress()) && (!hasIntranetAddress
                                                                                               || StringUtils.equals(
                        server.getIntranetAddress(), machine.getInternalAddress()))) {
                        hit = machine;
                        break;
                    }
                }
            }

            if (hit == null) {
                log.warn("【初始化设备信息】未找到对应服务id={},config={}", server.getId(), server.getConfig());
            } else {

                for (ServerVO child : server.getChildren()) {
                    if (StringUtils.isNotBlank(child.getDeviceId())) {
                        continue;
                    }
                    MachinePort cHit = null;

                    for (MachinePort port : hit.getPorts()) {

                        if (!StringUtils.equals(child.getConfig(), port.getPort().toString())) {
                            continue;
                        }
                        cHit = port;
                        break;
                    }

                    if (cHit == null) {
                        log.warn("【初始化设备信息】未找到对应端口id={},config={}", child.getId(), child.getConfig());
                    } else {
                        child.setDeviceId(cHit.getId());
                        updateById(child);
                    }
                }

                server.setDeviceId(hit.getId());
                updateById(server);
            }
        }
    }

    @Override
    public String fetchRunLog(Integer id) {
        ServerVO server = queryByKey(id);
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "id is null");
        String type = server.getLevel() == 1 ? SERVICE_TYPE_TRAIN : SERVICE_TYPE_CREATIVE;

        String fileServerUrl = serverHelper.getFileServerUrl(server);
        return comfyUIService.fetchRunLog(type, server.getLevel() == 1 ? null : Integer.valueOf(server.getConfig()),
            100, fileServerUrl);
    }

    @Override
    public boolean restartPort(Integer id) {
        ServerVO server = queryByKey(id);
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "id is null");
        AssertUtil.assertTrue(server.getLevel() == 2, ResultCode.PARAM_INVALID, "id is null");

        String fileServerUrl = serverHelper.getFileServerUrl(server);
        return comfyUIService.restartPort(Integer.valueOf(server.getConfig()), fileServerUrl);
    }

    @Override
    public boolean restartServer(Integer id, String type) {
        ServerVO server = queryByKey(id);
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "id is null");
        AssertUtil.assertTrue(server.getLevel() == 1, ResultCode.PARAM_INVALID, "id is null");

        String fileServerUrl = serverHelper.getFileServerUrl(server);
        return comfyUIService.restartServer(type, fileServerUrl);
    }

    @Override
    public boolean updateCreativeNode(Integer id) {
        ServerVO server = queryByKey(id);
        AssertUtil.assertNotNull(server, ResultCode.PARAM_INVALID, "id is null");
        AssertUtil.assertTrue(server.getLevel() == 1, ResultCode.PARAM_INVALID, "id is null");

        String fileServerUrl = serverHelper.getFileServerUrl(server);
        return comfyUIService.updateCreativeNode(fileServerUrl);
    }

    @Override
    protected List<ServerVO> loadAll() {
        List<ServerVO> result = ServerConverter.doList2VOList(serverDAO.selectAll());
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }

        List<MachineRoom> machineRooms = systemConfigService.queryDeviceInfo();
        if (CollectionUtils.isEmpty(machineRooms)) {
            return result;
        }

        for (ServerVO server : result) {
            if (StringUtils.isBlank(server.getDeviceId())) {
                continue;
            }

            String[] split = StringUtils.split(server.getDeviceId(), "-");
            MachineRoom room = machineRooms.stream().filter(e -> StringUtils.equals(e.getId(), split[0])).findFirst()
                .orElse(null);

            if (room == null) {
                continue;
            }

            MachineInfo machine = room.getMachines().stream().filter(
                e -> StringUtils.equals(e.getId(), split[0] + "-" + split[1])).findFirst().orElse(null);

            if (machine == null) {
                continue;
            }

            log.info("填充设备信息,server={},machineInfo={}", server.getId(), machine);
            if (server.getLevel() == 1) {
                server.setIntranetAddress(machine.getInternalAddress());
                server.setConfig(machine.getPublicAddress());
                server.setName(machine.getName());
            } else {
                machine.getPorts().stream().filter(e -> StringUtils.equals(e.getId(), server.getDeviceId())).findFirst()
                    .ifPresent(port -> server.setConfig(port.getPort() + ""));
            }
        }

        return result;
    }

    @Override
    protected Integer getKey(ServerVO cache) {
        return cache.getId();
    }

    @Override
    protected String getValue(ServerVO cache) {
        return cache.getConfig();
    }
}
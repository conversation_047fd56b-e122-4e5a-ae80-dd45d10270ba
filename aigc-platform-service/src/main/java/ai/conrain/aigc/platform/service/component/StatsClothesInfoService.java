package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsClothesInfoDO;
import ai.conrain.aigc.platform.service.model.query.StatsClothesInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsClothesInfoVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 服装信息统计表
 * Service定义
 *
 * <AUTHOR>
 * @version StatsClothesInfoService.java v 0.1 2025-04-22 05:07:33
 */
public interface StatsClothesInfoService {

	/**
	 * 查询服装信息统计表
	 * 对象
	 * 
	 * @param id 主键
	 * @return 返回结果
	 */
	StatsClothesInfoVO selectById(Integer id);

	/**
	 * 删除服装信息统计表
	 * 对象
	 * 
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加服装信息统计表
	 * 对象
	 * 
	 * @param statsClothesInfo 对象参数
	 * @return 返回结果
	 */
	StatsClothesInfoVO insert(StatsClothesInfoVO statsClothesInfo);

	/**
	 * 修改服装信息统计表
	 * 对象
	 * 
	 * @param statsClothesInfo 对象参数
	 */
	void updateByIdSelective(StatsClothesInfoVO statsClothesInfo);

	/**
	 * 带条件批量查询服装信息统计表
	 * 列表
	 * 
	 * @param query 查询条件
	 *              return 结果
	 */
	List<StatsClothesInfoVO> queryStatsClothesInfoList(StatsClothesInfoQuery query);

	/**
	 * 带条件查询服装信息统计表
	 * 数量
	 * 
	 * @param query 查询条件
	 *              return 记录条数
	 */
	Long queryStatsClothesInfoCount(StatsClothesInfoQuery query);

	/**
	 * 带条件分页查询服装信息统计表
	 * 
	 * @param query 查询条件
	 *              return 分页结果
	 */
	PageInfo<StatsClothesInfoVO> queryStatsClothesInfoByPage(StatsClothesInfoQuery query);

	/**
	 * 批量插入或更新
	 * 
	 * @param statsList 统计数据列表
	 * @return 插入或更新数量
	 */
	int batchInsertOrUpdate(List<StatsClothesInfoDO> statsList);

	/**
	 * 根据周查询日统计数据
	 * 
	 * @param startDate 周起始日期
	 * @param endDate   周结束时间
	 * @return 日统计数据列表
	 */
	List<StatsClothesInfoDO> selectDailyStatsByWeek(String startDate, String endDate);

	/**
	 * 根据月查询日统计数据
	 * 
	 * @param startDate 月起始日期
	 * @param endDate   月结束时间
	 * @return 日统计数据列表
	 */
	List<StatsClothesInfoDO> selectDailyStatsByMonth(String startDate, String endDate);

	/**
	 * 根据日期和统计周期查询日统计数据
	 * 
	 * @param date 日期
	 * @param statsType 统计周期
	 * @return 统计数据对象
	 */
	StatsClothesInfoDO selectStatsInfoByDateAndPeriod(String date, String statsType);

	/**
	 * 带条件分页查询服装信息统计表（支持WEEKLY/MONTHLY实时计算）
	 * 对于WEEKLY/MONTHLY类型，会实时计算本周/月的数据
	 * 
	 * @param query 查询条件
	 * @return 分页结果
	 */
	PageInfo<StatsClothesInfoVO> queryStatsClothesInfoByPageWithRealTime(StatsClothesInfoQuery query);
}
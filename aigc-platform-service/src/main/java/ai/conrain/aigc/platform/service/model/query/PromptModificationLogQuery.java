package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * PromptModificationLogQuery
 *
 * @version PromptModificationLogService.java v 0.1 2025-03-24 05:26:36
 */
@Data
public class PromptModificationLogQuery implements Serializable {
    /** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;

    /** 模块代码，如A、B、C */
    private String moduleCode;

    /** 模块主类型，如服装类型、人脸类型、场景类型 */
    private String moduleType;

    /** 父级元素id */
    private Integer parentElementId;

    /** 操作人ID */
    private Integer operatorId;

    /** 操作人姓名 */
    private String operatorName;

    /** 操作时间 */
    private Date operationTime;

    /** 旧数据（修改前的数据 json 格式） */
    private String oldFieldValue;

    /** 新数据（本次修改的数据 json 格式） */
    private String newFieldValue;

    /** 固定属性JSON，记录固定属性值，如{demo:value} */
    private String fixedAttrs;

    /** 扩展信息，存储额外的JSON格式数据 */
    private String extInfo;

    /** 备注信息 */
    private String remark;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

}

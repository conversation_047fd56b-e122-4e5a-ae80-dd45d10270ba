package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.CommonTaskQuery;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;

import java.util.List;

/**
 * 通用任务 Service定义
 */
public interface CommonTaskService {
	
	/**
	 * 查询通用任务对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	CommonTaskVO selectById(Integer id);

	CommonTaskVO lockById(Integer id);

	/**
	 * 删除通用任务对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加通用任务对象
	 * @param commonTask 对象参数
	 * @return 返回结果
	 */
	CommonTaskVO insert(CommonTaskVO commonTask);

	/**
	 * 修改通用任务对象
	 * @param commonTask 对象参数
	 */
	void updateByIdSelective(CommonTaskVO commonTask);

	/**
	 * 带条件批量查询通用任务列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<CommonTaskVO> queryCommonTaskList(CommonTaskQuery query);

	/**
	 * 带条件查询通用任务数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryCommonTaskCount(CommonTaskQuery query);

	/**
	 * 带条件分页查询通用任务
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<CommonTaskVO> queryCommonTaskByPage(CommonTaskQuery query);

	/**
	 * 同步三方任务状态
	 * @param task 三方任务关联的创作任务
	 */
    void syncThirdTaskStatus(CreativeTaskVO task);
}
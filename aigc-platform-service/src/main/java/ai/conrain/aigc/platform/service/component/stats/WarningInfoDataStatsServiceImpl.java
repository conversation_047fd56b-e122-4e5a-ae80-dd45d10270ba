package ai.conrain.aigc.platform.service.component.stats;

import ai.conrain.aigc.platform.service.component.*;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.MaterialModelStatusEnum;
import ai.conrain.aigc.platform.service.enums.MaterialType;
import ai.conrain.aigc.platform.service.enums.StatsPeriodEnum;
import ai.conrain.aigc.platform.service.enums.StatsTypeEnum;
import ai.conrain.aigc.platform.service.model.query.MaterialModelQuery;
import ai.conrain.aigc.platform.service.model.query.UserPointQuery;
import ai.conrain.aigc.platform.service.model.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;
import java.text.SimpleDateFormat;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Service("warningInfoDataStatsServiceImpl")
public class WarningInfoDataStatsServiceImpl extends AbstractDataStatsServiceImpl {

    @Autowired
    private StatsWarningInfoService statsWarningInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserPointLogService userPointLogService;
    @Autowired
    private MaterialModelService materialModelService;
    @Autowired
    private UserPointService userPointService;

    @Override
    public StatsTypeEnum getStatsType() {
        return StatsTypeEnum.WARNING_INFO;
    }

    @Override
    protected int executeBusinessStats(String storageDate, String startDate, String endDate, StatsPeriodEnum periodEnum,
                                       boolean isTotal) {
        // 根据统计周期和是否汇总进行不同处理
        switch (periodEnum) {
            case WEEKLY:
                return executeWeeklyBusinessStats(storageDate, startDate, endDate, periodEnum);
            case MONTHLY:
                return executeMonthlyBusinessStats(storageDate, startDate, endDate, periodEnum);
            default:
                return 0;
        }
    }


    /**
     * 执行每周业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeWeeklyBusinessStats(String storageDate, String startDate, String endDate,
                                           StatsPeriodEnum periodEnum) {
        // 1、构建统计数据
        StatsWarningInfoVO resultData = buildOperationIndicatorsData(storageDate, startDate, endDate,
                periodEnum);

        // 2、保存数据
        return saveStatsData(Collections.singletonList(resultData));
    }

    /**
     * 执行每月业务统计
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计结果
     */
    private int executeMonthlyBusinessStats(String storageDate, String startDate, String endDate,
                                            StatsPeriodEnum periodEnum) {
        // 1、构建统计数据
        StatsWarningInfoVO resultData = buildOperationIndicatorsData(storageDate, startDate, endDate,
                periodEnum);

        // 2、保存数据
        return saveStatsData(Collections.singletonList(resultData));
    }

    /**
     * 构建统计数据
     *
     * @param storageDate 存储日期
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param periodEnum  统计周期
     * @return 统计数据列表
     */
    private StatsWarningInfoVO buildOperationIndicatorsData(String storageDate, String startDate, String endDate,
                                                            StatsPeriodEnum periodEnum) {
        // 初始化数据
        StatsWarningInfoVO statsData = new StatsWarningInfoVO();
        statsData.setStatsType(periodEnum.getCode());
        statsData.setStatsDate(storageDate);
        statsData.setCreateTime(new Date());
        statsData.setModifyTime(new Date());

        // 获取消耗 3999 以上的用户信息列表
        List<UserVO> vipUserList = userService.queryAll3999VIPOrPaidCustomerWithCreateTime(startDate, endDate);
        if (CollectionUtils.isEmpty(vipUserList)) {
            return statsData;
        }
        List<Integer> userIdList = vipUserList.stream().map(UserVO::getId).distinct().collect(Collectors.toList());

        // 1、统计周内不消耗客户
        if (periodEnum == StatsPeriodEnum.WEEKLY) {
            statisticsNotConsumed(startDate, endDate, StatsPeriodEnum.WEEKLY, userIdList, statsData);
        }

        // 2、统计月内不消耗客户
        if (periodEnum == StatsPeriodEnum.MONTHLY) {
            statisticsNotConsumed(startDate, endDate, StatsPeriodEnum.MONTHLY, userIdList, statsData);
        }
        // 3、用户退款率大于百分之五的客户数量
        statisticsCustomerRefundRateCount(startDate, endDate, userIdList, statsData);
        // 4、交付超过24小时的服装量
        statisticsDeliveryTimeout(startDate, endDate, statsData);
        // 5、客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%）
        statisticsCustomerBalanceAlert(startDate, endDate, userIdList, statsData);
        // 6、客户入库时间超过 60 天未转化数量
        statisticsCustomerNotConvert(endDate, statsData);

        // 返回数据
        return statsData;
    }

    /**
     * 统计周内不消耗客户
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsType 统计类型
     * @param statsData 统计数据
     */
    private void statisticsNotConsumed(String startDate, String endDate, StatsPeriodEnum statsType,
                                       List<Integer> userIdList, StatsWarningInfoVO statsData) {
        // 获取一个月内未消费的用户比例
        calculateNotConsumedRate(startDate, endDate, userIdList, statsData, statsType);
    }

    /**
     * 统计客户退款率大于百分之五的客户数量
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 用户 id 列表
     * @param statsData  统计数据
     */
    private void statisticsCustomerRefundRateCount(String startDate, String endDate, List<Integer> userIdList,
                                                   StatsWarningInfoVO statsData) {
        // 查询指定时间范围内VIP客户所有服装模型(包括已交付和已退点的)
        MaterialModelQuery query = new MaterialModelQuery();
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setUserIds(userIdList);
        query.setMaterialType(MaterialType.cloth.name());

        List<MaterialModelVO> materialModelList = materialModelService.queryMaterialModelList(query);
        if (CollectionUtils.isEmpty(materialModelList)) {
            statsData.setCustomerRefundRateCount(0);
            return;
        }

        // 按用户ID分组统计
        Map<Integer, List<MaterialModelVO>> userModelsMap = materialModelList.stream()
                .collect(Collectors.groupingBy(MaterialModelVO::getUserId));

        // 存储用户ID和对应的退款率
        Map<String, String> userRefundRateMap = new HashMap<>();
        // 统计退点率大于5%的用户数量
        int highRefundRateUserCount = 0;

        for (Map.Entry<Integer, List<MaterialModelVO>> entry : userModelsMap.entrySet()) {
            Integer userId = entry.getKey();
            List<MaterialModelVO> userModels = entry.getValue();

            // 统计该用户的模型总数和退点数量
            int totalCount = userModels.size();
            if (totalCount == 0) {
                continue;
            }

            int disabledCount = 0; // 退点数量
            for (MaterialModelVO model : userModels) {
                if (MaterialModelStatusEnum.DISABLED.getCode().equals(model.getStatus())) {
                    disabledCount++;
                }
            }

            // 计算退点率
            double refundRate = (double) disabledCount / totalCount;

            // 如果退点率大于5%，计数加1
            if (refundRate > 0.05) {
                highRefundRateUserCount++;
                // 保存用户ID和退款率到Map中
                userRefundRateMap.put(String.valueOf(userId), disabledCount + "/" + totalCount);
            }
        }

        // 设置退点率大于5%的客户数量
        statsData.setCustomerRefundRateCount(highRefundRateUserCount);

        // 保存退点率大于5%的用户ID列表到扩展信息
        if (!userRefundRateMap.isEmpty()) {
            statsData.addExtInfo(CommonConstants.KEY_CUSTOMER_REFUND_USER_ID_MAP, userRefundRateMap);
        }
    }

    /**
     * 统计交付超过24小时的服装量
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsData 统计数据
     */
    private void statisticsDeliveryTimeout(String startDate, String endDate, StatsWarningInfoVO statsData) {
        MaterialModelQuery materialModelQuery = new MaterialModelQuery();
        materialModelQuery.setType("CUSTOM");
        materialModelQuery.setMaterialType(MaterialType.cloth.name());
        materialModelQuery.setStartDate(startDate);
        materialModelQuery.setEndDate(endDate);
        materialModelQuery.setIsIgnoreCopy(Boolean.TRUE);
        // 查询指定时间范围内的服装
        List<MaterialModelVO> materialModelVOList = materialModelService.queryMaterialModelList(materialModelQuery);

        if (CollectionUtils.isEmpty(materialModelVOList)) {
            statsData.setDeliveryTimeoutCount(0);
            return;
        }

        // 存储超时服装ID及其交付用时(小时)的映射
        Map<String, String> timeoutMaterialMap = new HashMap<>();

        // 计算交付时间超过24小时的服装
        for (MaterialModelVO model : materialModelVOList) {
            // 获取创建时间
            Date createTime = model.getCreateTime();
            // 获取交付时间
            String deliveryTimeStr = null;
            if (model.getExtInfo() != null) {
                try {
                    Object deliveryTimeObj = model.getExtInfo(CommonConstants.KEY_DELIVERY_TIME, String.class);
                    if (deliveryTimeObj != null) {
                        deliveryTimeStr = deliveryTimeObj.toString();
                    }
                } catch (Exception e) {
                    log.error("获取交付时间异常: {}", e.getMessage(), e);
                }
            }

            // 如果没有交付时间，则跳过
            if (createTime == null || deliveryTimeStr == null) {
                continue;
            }

            try {
                // 解析交付时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date deliveryTime = sdf.parse(deliveryTimeStr);

                // 计算交付用时(毫秒)
                long timeDiffMillis = deliveryTime.getTime() - createTime.getTime();
                // 转换为小时
                double timeDiffHours = timeDiffMillis / (1000.0 * 60 * 60);

                // 如果交付用时超过24小时，则加入超时Map
                if (timeDiffHours > 24) {
                    timeoutMaterialMap.put(String.valueOf(model.getId()), String.valueOf(timeDiffHours));
                }
            } catch (Exception e) {
                log.error("解析交付时间异常: {}", e.getMessage(), e);
            }
        }

        // 获取超时服装ID列表
        List<String> timeoutMaterialIds = new ArrayList<>(timeoutMaterialMap.keySet());

        // 设置交付超时的服装数量
        statsData.setDeliveryTimeoutCount(timeoutMaterialIds.size());

        if (!timeoutMaterialMap.isEmpty()) {
            // 保存超时服装ID列表到扩展信息
            statsData.addExtInfo(CommonConstants.KEY_DELIVER_TIMEOUT_MATERIAL_ID_MAP, timeoutMaterialMap);
        }
    }

    /**
     * 统计客户余额预警（缪斯点小于 2000 或 小于累计充值金额为基础的30%）
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 用户 id 列表
     * @param statsData  统计数据
     */
    private void statisticsCustomerBalanceAlert(String startDate, String endDate, List<Integer> userIdList,
                                                StatsWarningInfoVO statsData) {
        // 封装查询条件(查询出这段时间内所有用户的余额信息)
        UserPointQuery userPointQuery = new UserPointQuery();
        userPointQuery.setStartTime(startDate);
        userPointQuery.setEndTime(endDate);
        userPointQuery.setUserIdList(userIdList);
        List<UserPointVO> userPointVOList = userPointService.queryUserPointList(userPointQuery);

        // 存储用户ID和缪斯点余额的映射
        Map<Integer, String> userPointBalanceMap = new HashMap<>();

        // 将用户点数信息转换为Map，方便后续查询
        for (UserPointVO userPoint : userPointVOList) {
            // 将用户ID和缪斯点余额存入映射（转换为缪斯点单位）
            userPointBalanceMap.put(userPoint.getUserId(), String.valueOf(userPoint.getPoint()));
        }

        // 筛选出缪斯点小于 2000 的用户
        List<Integer> lessThan2000UserIdList = userPointVOList.stream()
                .filter(userPointVO -> userPointVO.getPoint() < 2000 * CommonConstants.MUSE_POINT_COEFFICIENT)
                .map(UserPointVO::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 查询出每位用户的累计充值金额
        Map<Integer, Integer> userRechargeMap = userPointService.queryUserAccumulatedRechargeByUserIdList(userIdList);

        // 筛选出余额小于累计充值金额30%的用户
        List<Integer> lessThan30PercentUserIdList = userPointVOList.stream()
                .filter(userPointVO -> {
                    Integer rechargeAmount = userRechargeMap.get(userPointVO.getUserId());
                    if (rechargeAmount == null || rechargeAmount == 0) {
                        return false;
                    }
                    // 计算累计充值金额的30%（以缪斯点为单位）
                    double threshold = rechargeAmount * 0.3;
                    return userPointVO.getPoint() < threshold;
                })
                .map(UserPointVO::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 两个集合取并集并去重
        Set<Integer> warningUserIdSet = new HashSet<>();
        warningUserIdSet.addAll(lessThan2000UserIdList);
        warningUserIdSet.addAll(lessThan30PercentUserIdList);
        List<Integer> warningUserIds = new ArrayList<>(warningUserIdSet);

        // 过滤userPointBalanceMap，只保留预警用户
        Map<String, String> warningUserBalanceMap = new HashMap<>();
        for (Integer userId : warningUserIds) {
            String balance = userPointBalanceMap.get(userId);
            if (balance != null) {
                warningUserBalanceMap.put(String.valueOf(userId), balance);
            }
        }

        // 设置参数
        statsData.setCustomerBalanceAlertCount(warningUserIds.size());
        if (!warningUserBalanceMap.isEmpty()) {
            statsData.addExtInfo(CommonConstants.KEY_BALANCE_WARNING_USER_ID_MAP, warningUserBalanceMap);
        }
    }

    /**
     * 统计客户入库时间超过 60 天未转化数量
     *
     * @param endDate   结束日期
     * @param statsData 统计数据
     */
    private void statisticsCustomerNotConvert(String endDate, StatsWarningInfoVO statsData) {
        List<SalesInfoVO> salesInfoList = userService.queryBefore60DaysByOperate(endDate);
        if (CollectionUtils.isEmpty(salesInfoList)) {
            return;
        }
        List<Integer> userIdlIst = salesInfoList.stream().map(SalesInfoVO::getUserId).distinct()
                .collect(Collectors.toList());

        statsData.setCustomerNotConvertCount(userIdlIst.size());
        if (!userIdlIst.isEmpty()) {
            statsData.addExtInfo(CommonConstants.KEY_SIXTY_DAYS_NO_CONVERT_USER_ID_LIST, userIdlIst);
        }
    }

    /**
     * 计算指定时间范围内未消费的用户比例
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 用户ID列表
     * @param statsData  统计数据
     * @param statsType  统计类型
     */
    private void calculateNotConsumedRate(String startDate, String endDate, List<Integer> userIdList,
                                          StatsWarningInfoVO statsData, StatsPeriodEnum statsType) {
        if (userIdList == null || userIdList.isEmpty()) {
            return;
        }

        // 获取指定时间范围内有消费的用户ID列表
        List<Integer> consumedUserIds = getConsumedUserIds(startDate, endDate, userIdList);

        // 提取出未消费的用户列表
        List<Integer> notConsumedUserIds = userIdList.stream().filter(id -> !consumedUserIds.contains(id))
                .collect(Collectors.toList());

        // 计算未消费用户数量
        int notConsumedCount = notConsumedUserIds.size();

        // 计算比例
        String notConsumedRate = String.format("%.2f", (double) notConsumedCount / userIdList.size() * 100) + "%";

        if (StatsPeriodEnum.WEEKLY == statsType) {
            statsData.setWeeklyNoConsumptionRate(notConsumedRate);
            statsData.addExtInfo(CommonConstants.KEY_WEEKLY_USER_ID_LIST, notConsumedUserIds);
        }

        if (StatsPeriodEnum.MONTHLY == statsType) {
            statsData.setMonthlyNoConsumptionRate(notConsumedRate);
            statsData.addExtInfo(CommonConstants.KEY_MONTH_USER_ID_LIST, notConsumedUserIds);
        }

    }

    /**
     * 获取指定时间范围内有消费的用户ID列表
     *
     * @param startDate  开始日期
     * @param endDate    结束日期
     * @param userIdList 待查询的用户ID列表
     * @return 有消费的用户ID列表
     */
    private List<Integer> getConsumedUserIds(String startDate, String endDate, List<Integer> userIdList) {
        try {
            // 查询在指定时间范围内有消费记录的用户
            return userPointLogService.getPayUserByDate(startDate, endDate, userIdList);
        } catch (Exception e) {
            log.error("获取消费用户列表异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 保存统计数据
     *
     * @param statsList 统计数据列表
     * @return 影响行数
     */
    private int saveStatsData(List<StatsWarningInfoVO> statsList) {
        if (!CollectionUtils.isEmpty(statsList)) {
            try {
                return statsWarningInfoService.batchInsertOrUpdate(statsList);
            } catch (Exception e) {
                log.error("保存统计数据异常: {}", e.getMessage(), e);
                return 0;
            }
        }
        return 0;
    }

}

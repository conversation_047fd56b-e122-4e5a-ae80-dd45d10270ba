package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.CustomerActivityLogDAO;
import ai.conrain.aigc.platform.dal.entity.CustomerActivityLogDO;
import ai.conrain.aigc.platform.dal.entity.CustomerActivityStatsDO;
import ai.conrain.aigc.platform.dal.example.CustomerActivityLogExample;
import ai.conrain.aigc.platform.service.component.CustomerActivityLogService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.CustomerActivityLogConverter;
import ai.conrain.aigc.platform.service.model.query.CustomerActivityLogQuery;
import ai.conrain.aigc.platform.service.model.vo.CustomerActivityLogVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CustomerActivityLogService实现
 *
 * <AUTHOR>
 * @version CustomerActivityLogService.java
 */
@Slf4j
@Service
public class CustomerActivityLogServiceImpl implements CustomerActivityLogService {

    /** DAO */
    @Autowired
    private CustomerActivityLogDAO customerActivityLogDAO;

    @Override
    public CustomerActivityLogVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        CustomerActivityLogDO data = customerActivityLogDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return CustomerActivityLogConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = customerActivityLogDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除CustomerActivityLog失败");
    }

    @Override
    public CustomerActivityLogVO insert(CustomerActivityLogVO customerActivityLog) {
        AssertUtil.assertNotNull(customerActivityLog, ResultCode.PARAM_INVALID, "customerActivityLog is null");
        AssertUtil.assertTrue(customerActivityLog.getId() == null, ResultCode.PARAM_INVALID,
            "customerActivityLog.id is present");

        //创建时间、修改时间兜底
        if (customerActivityLog.getCreateTime() == null) {
            customerActivityLog.setCreateTime(new Date());
        }

        if (customerActivityLog.getModifyTime() == null) {
            customerActivityLog.setModifyTime(new Date());
        }

        CustomerActivityLogDO data = CustomerActivityLogConverter.vo2DO(customerActivityLog);
        Integer n = customerActivityLogDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建CustomerActivityLog失败");
        AssertUtil.assertNotNull(data.getId(), "新建CustomerActivityLog返回id为空");
        customerActivityLog.setId(data.getId());
        return customerActivityLog;
    }

    @Override
    public void updateByIdSelective(CustomerActivityLogVO customerActivityLog) {
        AssertUtil.assertNotNull(customerActivityLog, ResultCode.PARAM_INVALID, "customerActivityLog is null");
        AssertUtil.assertTrue(customerActivityLog.getId() != null, ResultCode.PARAM_INVALID,
            "customerActivityLog.id is null");

        //修改时间必须更新
        customerActivityLog.setModifyTime(new Date());
        CustomerActivityLogDO data = CustomerActivityLogConverter.vo2DO(customerActivityLog);
        int n = customerActivityLogDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新CustomerActivityLog失败，影响行数:" + n);
    }

    @Override
    public List<CustomerActivityLogVO> queryCustomerActivityLogList(CustomerActivityLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CustomerActivityLogExample example = CustomerActivityLogConverter.query2Example(query);

        List<CustomerActivityLogDO> list = customerActivityLogDAO.selectByExample(example);
        return CustomerActivityLogConverter.doList2VOList(list);
    }

    @Override
    public Long queryCustomerActivityLogCount(CustomerActivityLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        CustomerActivityLogExample example = CustomerActivityLogConverter.query2Example(query);
        return customerActivityLogDAO.countByExample(example);
    }

    /**
     * 带条件分页查询客户活跃记录
     */
    @Override
    public PageInfo<CustomerActivityLogVO> queryCustomerActivityLogByPage(CustomerActivityLogQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                              && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
            "pageNum or pageSize is invalid,pageNum:" + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<CustomerActivityLogVO> page = new PageInfo<>();

        CustomerActivityLogExample example = CustomerActivityLogConverter.query2Example(query);
        long totalCount = customerActivityLogDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<CustomerActivityLogDO> list = customerActivityLogDAO.selectByExample(example);
        page.setList(CustomerActivityLogConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    @Override
    public void initDaily() {
        customerActivityLogDAO.initDaily();
    }

    @Override
    public void deleteByDt(String dt) {
        AssertUtil.assertNotBlank(dt, "dt is null");
        int cnt = customerActivityLogDAO.deleteByDt(dt);
        AssertUtil.assertTrue(cnt > 0, "删除失败，cnt:" + cnt);
    }

    @Override
    public List<CustomerActivityStatsDO> statsBySalesArea(String groupType, Boolean onlyDirect) {
        Map<String, Object> params = new HashMap<>();

        String groupBy = "if(s.is_direct,t.sales_area,s.org_name)";
        if (StringUtils.equals("group", groupType)) {
            groupBy = "if(s.sub_org_name='孟肖娜团队','杭州',s.sub_org_name)";
            params.put("isDirect", true);
        } else if (StringUtils.equals("personal", groupType)) {
            groupBy = "s.sales_name";
            params.put("isDirect", true);
        }
        params.put("groupBy", groupBy);

        if (onlyDirect != null && onlyDirect) {
            params.put("isDirect", true);
        }
        //params.put("dt", DateUtil.format(new Date(), "yyyy-MM-dd"));
        return customerActivityLogDAO.statsBySalesArea(params);
    }

}
package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.TrainPlanDAO;
import ai.conrain.aigc.platform.dal.entity.TrainPlanDO;
import ai.conrain.aigc.platform.integration.ai.model.QueueResult;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.TrainParamService;
import ai.conrain.aigc.platform.service.component.TrainPlanService;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.TrainPlanConverter;
import ai.conrain.aigc.platform.service.model.request.ConfirmLoraReq;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.TrainParamVO;
import ai.conrain.aigc.platform.service.model.vo.TrainPlanVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * TrainPlanService实现
 *
 * <AUTHOR>
 * @version TrainPlanService.java v 0.1 2024-11-19 08:34:41
 */
@Slf4j
@Service
public class TrainPlanServiceImpl implements TrainPlanService {

    /** DAO */
    @Autowired
    private TrainPlanDAO trainPlanDAO;

    @Autowired
    private TrainParamService trainParamService;

    @Autowired
    private MaterialModelService materialModelService;

    @Override
    public TrainPlanVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        TrainPlanDO data = trainPlanDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return TrainPlanConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = trainPlanDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TrainPlan失败");
    }

    @Override
    public TrainPlanVO insert(TrainPlanVO trainPlan) {
        AssertUtil.assertNotNull(trainPlan, ResultCode.PARAM_INVALID, "trainPlan is null");
        AssertUtil.assertTrue(trainPlan.getId() == null, ResultCode.PARAM_INVALID, "trainPlan.id is present");

        //创建时间、修改时间兜底
        if (trainPlan.getCreateTime() == null) {
            trainPlan.setCreateTime(new Date());
        }

        if (trainPlan.getModifyTime() == null) {
            trainPlan.setModifyTime(new Date());
        }

        TrainPlanDO data = TrainPlanConverter.vo2DO(trainPlan);
        Integer n = trainPlanDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TrainPlan失败");
        AssertUtil.assertNotNull(data.getId(), "新建TrainPlan返回id为空");
        trainPlan.setId(data.getId());
        return trainPlan;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public TrainPlanVO createPlan(TrainPlanVO trainPlan) {

        AssertUtil.assertNotNull(trainPlan.getClothingId(), ResultCode.PARAM_INVALID, "trainPlan is null");
        MaterialModelVO lora = materialModelService.selectById(trainPlan.getClothingId());

        AssertUtil.assertNotNull(lora, ResultCode.PARAM_INVALID, "lora不存在");
        if (lora.getClothLoraTrainDetail() == null || lora.getClothLoraTrainDetail().getLabel() == null || lora.getClothLoraTrainDetail().getLabel().getStatus() != QueueResult.QueueCodeEnum.COMPLETED) {
            throw new BizException(ResultCode.BIZ_FAIL, "目标服装没有完成打标，无法克隆");
        }

        trainPlan.setClothingName(lora.getName());

        TrainPlanVO plan = this.insert(trainPlan);

        Integer id = plan.getId();
        List<TrainParamVO> params = trainPlan.getTrainParams();
        for (int i = 0; i < params.size(); i++) {
            TrainParamVO param = params.get(i);

            //克隆产生新的训练服装
            String cloneClothName = String.format("%s_%s_%d", lora.getName(), plan.getPlanName(), i + 1);
            MaterialModelVO newLora = materialModelService.cloneLora(trainPlan.getClothingId(), cloneClothName, null,
                false, null);

            //按指定参数去确认训练
            ConfirmLoraReq confirmLoraReq = getConfirmLoraReq(trainPlan, newLora, param);

            // 确认训练
            materialModelService.confirmTrainLora(confirmLoraReq, OperationContextHolder.getOperatorUserId(), OperationContextHolder.getOperatorNick());

            param.setTrainPlanId(id);
            param.setRelatedLoraModelId(newLora.getId());
            param.setRelatedLoraModelName(newLora.getName());

            trainParamService.insert(param);
        }
        return plan;
    }

    @NotNull
    private static ConfirmLoraReq getConfirmLoraReq(TrainPlanVO trainPlan, MaterialModelVO newLora, TrainParamVO param) {
        ConfirmLoraReq confirmLoraReq = new ConfirmLoraReq();
        confirmLoraReq.setId(newLora.getId());
        confirmLoraReq.setTestFaces(trainPlan.getFaceModels());
        confirmLoraReq.setTestScenes(trainPlan.getScenes());
        confirmLoraReq.setTestNum(1);
        confirmLoraReq.setMaxTrainStep(param.getTrainStep());
        confirmLoraReq.setLoraType(CommonConstants.FLUX);
        confirmLoraReq.setLr(param.getLr().toString());
        confirmLoraReq.setContentOrStyle(param.getContentOrStyle());
        confirmLoraReq.setRank(param.getLoraRank().toString());
        confirmLoraReq.setAlpha(param.getAlpha().toString());
        confirmLoraReq.setDropout(param.getDropout().toString());
        confirmLoraReq.setResolution(param.getTrainResolution());
        confirmLoraReq.setTestImgProportions(trainPlan.getSizes());
        confirmLoraReq.setTestImgMode(CommonConstants.TEST_IMG_MODE_CARTESIAN_PRO);
        confirmLoraReq.setImgNumPerGroup(trainPlan.getImagesPerCombination());
        return confirmLoraReq;
    }

    @Override
    public void updateById(TrainPlanVO trainPlan) {
        AssertUtil.assertNotNull(trainPlan, ResultCode.PARAM_INVALID, "trainPlan is null");
        AssertUtil.assertTrue(trainPlan.getId() != null, ResultCode.PARAM_INVALID, "trainPlan.id is null");
        //修改时间必须更新
        trainPlan.setModifyTime(new Date());

        TrainPlanDO data = TrainPlanConverter.vo2DO(trainPlan);
        int n = trainPlanDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TrainPlan失败，影响行数:" + n);
    }

    @Override
    public List<TrainPlanVO> findAll() {
        List<TrainPlanDO> list = trainPlanDAO.selectAll();
        List<TrainPlanVO> ret = TrainPlanConverter.doList2VOList(list);
        fillTrainParams(ret);

        return ret;
    }

    private void fillTrainParams(List<TrainPlanVO> plans) {
        if (plans != null) {
            plans.forEach(p -> {
                p.setTrainParams(trainParamService.findAllByPlanId(p.getId()));
            });
        }
    }
}
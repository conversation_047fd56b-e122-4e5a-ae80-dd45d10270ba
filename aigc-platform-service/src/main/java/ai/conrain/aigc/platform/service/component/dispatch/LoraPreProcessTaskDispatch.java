/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.dispatch;

import ai.conrain.aigc.platform.service.enums.ComfyuiTaskTypeEnum;
import ai.conrain.aigc.platform.service.enums.DispatchTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.IExtModel;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiTaskVO;
import ai.conrain.aigc.platform.service.model.vo.ServerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 训练预处理任务分发
 *
 * <AUTHOR>
 * @version : LoraPreProcessTaskDispatch.java, v 0.1 2024/8/29 16:37 renxiao.wu Exp $
 */
@Slf4j
@Service
public class LoraPreProcessTaskDispatch extends LoraTrainTaskDispatch {

    @Override
    protected boolean checkMachineMatch(DispatchTypeEnum type, IExtModel task, ServerVO server) {
        //prepareView不需要校验机器是否同一台机器
        if (ComfyuiTaskTypeEnum.prepareView == ((ComfyuiTaskVO)task).getTaskType()) {
            return true;
        }

        return super.checkMachineMatch(type, task, server);
    }

    @Override
    protected boolean canSwitchMachine(IExtModel task) {
        return true;
    }

    @Override
    public DispatchTypeEnum getType() {
        return DispatchTypeEnum.LORA_PRE_PROCESS;
    }
}

package ai.conrain.aigc.platform.service.model.query;


import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * StatsSaleIndicatorsQuery
 *
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
@Data
public class StatsSaleIndicatorsQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 统计类型：DAILY/WEEKLY/MONTHLY/TOTAL */
    private String statsType;

    /** 统计日期: 格式为yyyy-MM-dd */
    private String statsDate;

    /** 用户 id（渠道/销售/运营） */
    private Integer userId;

    /** 名称（渠道/销售/运营） */
    private String name;

    /** 父级 id，默认为 0 */
    private Integer parentId;

    /** 服装体验量 */
    private Integer clothesExpCount;

    /** 客户转换量（新签 3999 以上） */
    private Integer customerConversionCount;

    /** 客户消耗点数 */
    private Integer customerConsumptionPoints;

    /** 活跃客户率 */
    private String customerActivityRate;

    /** 客户复购率 */
    private String customerRepurchaseRate;

    /** 定制模特数量 */
    private String customModelCustomers;

    /** 定制场景数量 */
    private String customSceneCustomers;

    /** 大于 60 天未充值的客户 */
    private Integer customerProtectionMetrics;

    /** 销售出图数量 */
    private Integer createCount;

    /** 创建时间 */
    private Date createTime;

    /** 修改时间 */
    private Date modifyTime;

    /** 扩展字段 */
    private String extInfo;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 父级 id 列表 */
    private List<Integer> parentIdList;
}

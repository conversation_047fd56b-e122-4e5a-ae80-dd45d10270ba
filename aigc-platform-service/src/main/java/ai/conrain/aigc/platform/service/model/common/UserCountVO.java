/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.common;

import ai.conrain.aigc.platform.dal.entity.IdModel;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户关联数量vo
 *
 * <AUTHOR>
 * @version : UserCountVO.java, v 0.1 2025/4/7 13:56 renxiao.wu Exp $
 */
@Data
public class UserCountVO implements Serializable, IdModel {
    private static final long serialVersionUID = -9099163419143514139L;
    /** 用户id */
    private Integer userId;
    /** 用户名 */
    private String userName;
    /** 数量 */
    private Integer cnt;

    @Override
    public Integer getId() {
        return userId;
    }
}

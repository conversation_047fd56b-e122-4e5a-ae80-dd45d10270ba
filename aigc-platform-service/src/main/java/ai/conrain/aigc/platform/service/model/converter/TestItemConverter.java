package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TestItemDO;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestItemQuery;
import ai.conrain.aigc.platform.dal.example.TestItemExample;
import ai.conrain.aigc.platform.service.model.vo.ImageCaseVO;
import ai.conrain.aigc.platform.service.model.vo.TestItemVO;

import ai.conrain.aigc.platform.service.model.vo.TestResultVO;
import ai.conrain.aigc.platform.service.util.CustomCollectionUtils;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSONObject;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.CONTROL_GROUP_MAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.EXPERIMENTAL_GROUP_MAP;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.TOTAL_MAP;

/**
 * TestItemConverter
 *
 * @version TestItemService.java v 0.1 2024-12-19 01:24:06
 */
public class TestItemConverter {

    /**
     * DO -> VO
     */
    public static TestItemVO do2VO(TestItemDO from) {
        TestItemVO to = new TestItemVO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setType(from.getType());
        to.setStatus(TestStatusEnum.getByCode(from.getStatus()));
        to.setPreId(from.getPreId());
        to.setPreStatus(TestStatusEnum.getByCode(from.getPreStatus()));
        to.setRoundsNum(from.getRoundsNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setName(from.getName());
        if (StringUtils.isNotBlank(from.getSharedParams())) {
            to.setSharedParams(JSONObject.parseObject(from.getSharedParams()));
        }
        to.setConclusion(from.getConclusion());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestItemDO vo2DO(TestItemVO from) {
        TestItemDO to = new TestItemDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setType(from.getType());
        to.setStatus(from.getStatus().getCode());
        to.setPreId(from.getPreId());
        to.setPreStatus(from.getPreStatus() != null ? from.getPreStatus().getCode() : null);
        to.setRoundsNum(from.getRoundsNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setName(from.getName());
        if (from.getSharedParams() != null) {
            to.setSharedParams(from.getSharedParams().toString());
        }
        to.setConclusion(from.getConclusion());

        return to;
    }

    /**
     * DO -> Query
     */
    public static TestItemQuery do2Query(TestItemDO from) {
        TestItemQuery to = new TestItemQuery();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setPreId(from.getPreId());
        to.setPreStatus(from.getPreStatus());
        to.setRoundsNum(from.getRoundsNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSharedParams(from.getSharedParams());
        to.setName(from.getName());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TestItemDO query2DO(TestItemQuery from) {
        TestItemDO to = new TestItemDO();
        to.setId(from.getId());
        to.setPlanId(from.getPlanId());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setPreId(from.getPreId());
        to.setPreStatus(from.getPreStatus());
        to.setRoundsNum(from.getRoundsNum());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setSharedParams(from.getSharedParams());
        to.setName(from.getName());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestItemExample query2Example(TestItemQuery from) {
        TestItemExample to = new TestItemExample();
        TestItemExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPlanId())) {
            c.andPlanIdEqualTo(from.getPlanId());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getPreId())) {
            c.andPreIdEqualTo(from.getPreId());
        }
        if (!ObjectUtils.isEmpty(from.getPreStatus())) {
            c.andPreStatusEqualTo(from.getPreStatus());
        }
        if (!ObjectUtils.isEmpty(from.getRoundsNum())) {
            c.andRoundsNumEqualTo(from.getRoundsNum());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getPlanIds())) {
            c.andPlanIdIn(from.getPlanIds());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestItemVO> doList2VOList(List<TestItemDO> list) {
        return CommonUtil.listConverter(list, TestItemConverter::do2VO);
    }

    /**
     * 处理每个测试项的数据
     *
     * @param groupedMapByItem   按测试项分组的数据
     * @param imageCaseVOList    图片案例列表
     */
    public static Map<Integer, Map<String, List<Integer>>> processTestItems(
        Map<Integer, List<TestResultVO>> groupedMapByItem,
        List<ImageCaseVO> imageCaseVOList
    ) {
        // 初始化子项标签Map
        Map<Integer, Map<String, List<Integer>>> childBadCaseTagMap = new HashMap<>();

        for (Map.Entry<Integer, List<TestResultVO>> itemEntry : groupedMapByItem.entrySet()) {
            Integer itemId = itemEntry.getKey();
            List<TestResultVO> entryMapByItem = itemEntry.getValue();

            // 构建子项数据
            Map<String, List<Integer>> childBadCaseTagMapItem = processTestItem(entryMapByItem, imageCaseVOList);

            // 填充子项数据
            childBadCaseTagMap.put(itemId, childBadCaseTagMapItem);
        }

        // 返回子项数据
        return childBadCaseTagMap;
    }

    /**
     * 处理单个测试项的数据
     *
     * @param testResults     测试结果列表
     * @param imageCaseVOList 图片案例列表
     * @return 标签统计Map
     */
    public static Map<String, List<Integer>> processTestItem(List<TestResultVO> testResults,
                                                             List<ImageCaseVO> imageCaseVOList) {
        Map<String, List<Integer>> childBadCaseTagMapItem = new HashMap<>();

        // 获取当前测试项的所有case
        List<Integer> currentCaseIdList = testResults.stream()
            .map(TestResultVO::getCaseId)
            .collect(Collectors.toList());

        List<ImageCaseVO> currentImageCaseVOList = imageCaseVOList.stream()
            .filter(imageCaseVO -> currentCaseIdList.contains(imageCaseVO.getId()))
            .collect(Collectors.toList());

        // 填充总数据
        childBadCaseTagMapItem.put(TOTAL_MAP,
            CustomCollectionUtils.flatMapList(currentImageCaseVOList, ImageCaseVO::getTags));

        // 按分组处理数据
        Map<Integer, List<TestResultVO>> groupedMap = testResults.stream()
            .collect(Collectors.groupingBy(TestResultVO::getGroupId));

        // 处理每个分组的数据
        for (Map.Entry<Integer, List<TestResultVO>> groupEntry : groupedMap.entrySet()) {
            Integer groupId = groupEntry.getKey();
            List<TestResultVO> groupResults = groupEntry.getValue();

            List<Integer> tagIds = extractTagsFromTestResults(groupResults, imageCaseVOList);

            // 根据 groupId 的奇偶性填充对应的组
            String mapKey = (groupId % 2 != 0) ? EXPERIMENTAL_GROUP_MAP : CONTROL_GROUP_MAP;
            childBadCaseTagMapItem.put(mapKey, tagIds);
        }

        return childBadCaseTagMapItem;
    }

    /**
     * 从测试结果中提取标签
     *
     * @param testResults     测试结果列表
     * @param imageCaseVOList 图片案例列表
     * @return 标签ID列表
     */
    private static List<Integer> extractTagsFromTestResults(List<TestResultVO> testResults,
                                                            List<ImageCaseVO> imageCaseVOList) {
        return testResults.stream()
            .flatMap(testResult -> imageCaseVOList.stream()
                .filter(imageCaseVO -> imageCaseVO.getId().equals(testResult.getCaseId()))
                .flatMap(imageCaseVO -> {
                    List<Integer> tags = imageCaseVO.getTags();
                    return tags != null ? tags.stream() : Stream.empty();
                }))
            .collect(Collectors.toList());
    }
}
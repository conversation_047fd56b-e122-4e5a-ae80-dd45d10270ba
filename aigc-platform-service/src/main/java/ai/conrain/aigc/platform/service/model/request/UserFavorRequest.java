package ai.conrain.aigc.platform.service.model.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
public class UserFavorRequest {

    private Integer id;

    private Integer modelId;

    @NotNull
    private Integer itemId;

    @NotBlank
    private String type;

    private List<Integer> images;

}

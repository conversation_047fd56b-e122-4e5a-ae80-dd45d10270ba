package ai.conrain.aigc.platform.service.component.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;

import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.enums.TrainingSampleIdTypeConstant;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.query.ImageQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.training.LabelingProgressVO;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.dal.pgsql.entity.TrainingSampleDO;
import ai.conrain.aigc.platform.dal.example.TrainingSampleExample;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.TrainingSampleQuery;
import ai.conrain.aigc.platform.service.model.vo.TrainingSampleVO;
import ai.conrain.aigc.platform.service.model.converter.TrainingSampleConverter;
import ai.conrain.aigc.platform.dal.pgsql.dao.TrainingSampleDAO;
import ai.conrain.aigc.platform.service.component.TrainingSampleService;

/**   
 * TrainingSampleService实现
 *
 * <AUTHOR>
 * @version TrainingSampleService.java
 */
@Slf4j
@Service
public class TrainingSampleServiceImpl implements TrainingSampleService {

	/** DAO */
	@Autowired
	private TrainingSampleDAO trainingSampleDAO;

    @Autowired
    private ImageCaptionUserService imageCaptionUserService;

    @Autowired
    private ImageService imageService;

	@Override
	public TrainingSampleVO selectById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		TrainingSampleDO data = trainingSampleDAO.selectByPrimaryKey(id);
		if (null == data) {
			return null;
		}

		return TrainingSampleConverter.do2VO(data);
	}

	@Override
	public void deleteById(Integer id) {
		AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

		int n = trainingSampleDAO.deleteByPrimaryKey(id);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除TrainingSample失败");
	}

	@Override
	public TrainingSampleVO insert(TrainingSampleVO trainingSample) {
		AssertUtil.assertNotNull(trainingSample, ResultCode.PARAM_INVALID, "trainingSample is null");
		AssertUtil.assertTrue(trainingSample.getId() == null, ResultCode.PARAM_INVALID, "trainingSample.id is present");

		//创建时间、修改时间兜底
		if (trainingSample.getCreateTime() == null) {
			trainingSample.setCreateTime(new Date());
		}

		if (trainingSample.getModifyTime() == null) {
			trainingSample.setModifyTime(new Date());
		}

		TrainingSampleDO data = TrainingSampleConverter.vo2DO(trainingSample);
		Integer n = trainingSampleDAO.insert(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建TrainingSample失败");
		AssertUtil.assertNotNull(data.getId(), "新建TrainingSample返回id为空");
		trainingSample.setId(data.getId());
		return trainingSample;
	}


	@Override
	public void updateByIdSelective(TrainingSampleVO trainingSample) {
		AssertUtil.assertNotNull(trainingSample, ResultCode.PARAM_INVALID, "trainingSample is null");
    	AssertUtil.assertTrue(trainingSample.getId() != null, ResultCode.PARAM_INVALID, "trainingSample.id is null");

		//修改时间必须更新
		trainingSample.setModifyTime(new Date());
		TrainingSampleDO data = TrainingSampleConverter.vo2DO(trainingSample);
		int n = trainingSampleDAO.updateByPrimaryKeySelective(data);
		AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新TrainingSample失败，影响行数:" + n);
	}

	@Override
	public List<TrainingSampleVO> queryTrainingSampleList(TrainingSampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingSampleExample example = TrainingSampleConverter.query2Example(query);

		List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(example);
		return TrainingSampleConverter.doList2VOList(list);
	}

	@Override
	public Long queryTrainingSampleCount(TrainingSampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		TrainingSampleExample example = TrainingSampleConverter.query2Example(query);
		return trainingSampleDAO.countByExample(example);
	}

    @Override
    public TrainingSampleVO getNextSample(Integer roundId, Integer currentSampleId) {
        TrainingSampleExample exam = new TrainingSampleExample();
        exam.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID);
        if (currentSampleId != null) {
            exam.getOredCriteria().get(0).andIdGreaterThan(currentSampleId);
        }
        exam.setOrderByClause("id asc");
        exam.setRows(1);
        List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(exam);
        if (CollectionUtils.isNotEmpty(list)){
            return TrainingSampleConverter.do2VO(list.get(0));
        }
        return null;
    }

    @Override
    public TrainingSampleVO getPrevSample(Integer roundId, Integer currentSampleId) {
        TrainingSampleExample exam = new TrainingSampleExample();
        exam.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID);
        if (currentSampleId != null) {
            exam.getOredCriteria().get(0).andIdLessThan(currentSampleId);
        }
        exam.setOrderByClause("id desc");
        exam.setRows(1);
        List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(exam);
        if (CollectionUtils.isNotEmpty(list)){
            return TrainingSampleConverter.do2VO(list.get(0));
        }
        return null;
    }

    /**
	 * 带条件分页查询训练样本表，存储训练使用的样本数据
	 */
	@Override
	public PageInfo<TrainingSampleVO> queryTrainingSampleByPage(TrainingSampleQuery query) {
		AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

		AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
			&& query.getPageSize() >= 1, ResultCode.PARAM_INVALID, "pageNum or pageSize is invalid,pageNum:"
			+ query.getPageNum() + ",pageSize:" + query.getPageSize());

		PageInfo<TrainingSampleVO> page = new PageInfo<>();

		TrainingSampleExample example = TrainingSampleConverter.query2Example(query);
		long totalCount = trainingSampleDAO.countByExample(example);
		if (totalCount == 0) {
			page.setList(new ArrayList<>());
			page.setSize(0);
			page.setTotalCount(0);
			page.setHasNextPage(false);

			return page;
		}

		List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(example);
		page.setList(TrainingSampleConverter.doList2VOList(list));
		page.setSize(CollectionUtils.size(list));
		page.setTotalCount(totalCount);
		page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

		return page;
	}

    /**
     * 获取标注进度
     *
     * @param roundId
     */
    @Override
    public LabelingProgressVO getLabelingProgress(Integer roundId) {
        TrainingSampleExample exam = new TrainingSampleExample();
        exam.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID);

        long totalCount = trainingSampleDAO.countByExample(exam);

        TrainingSampleExample exam2 = new TrainingSampleExample();
        exam2.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID).andSampleCaptionIdIsNotNull();

        long labeledCount = trainingSampleDAO.countByExample(exam2);

        LabelingProgressVO vo = new LabelingProgressVO();
        vo.setTotalSamples(totalCount);
        vo.setLabeledSamples(labeledCount);
        vo.setProgress(totalCount > 0 ? (double) labeledCount / totalCount : 0.0);
        vo.setCurrentRound(roundId);

        return vo;
    }

    @Override
    public List<TrainingSampleVO> getLabeledSampleList(Integer roundId) {
        AssertUtil.assertNotNull(roundId, ResultCode.PARAM_INVALID, "roundId is null");

        TrainingSampleExample exam = new TrainingSampleExample();
        exam.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID).andSampleCaptionIdIsNotNull();

        List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(exam);
        if (CollectionUtils.isNotEmpty(list)){
            List<TrainingSampleVO> trainingSampleVOS = TrainingSampleConverter.doList2VOList(list);

            //根据sampleDataId查询image
            List<Integer> imageIds = list.stream().map(TrainingSampleDO::getSampleDataId).collect(Collectors.toList());
            if (list.getFirst().getSampleDataIdType().equalsIgnoreCase(TrainingSampleIdTypeConstant.IMAGE_ID)){
                ImageQuery imageQuery = new ImageQuery();
                imageQuery.setIds(imageIds);
                List<ImageVO> imageList = imageService.queryImageList(imageQuery);
                Map<Integer, ImageVO> imageMap = imageList.stream().collect(Collectors.toMap(ImageVO::getId, t -> t));

                for (TrainingSampleVO each : trainingSampleVOS){
                    if (imageMap.containsKey(each.getSampleDataId())) {
                        each.setImageVO(imageMap.get(each.getSampleDataId()));
                    }
                }
            }

            //根据captionId查询caption
            List<Integer> captionIds = list.stream().map(TrainingSampleDO::getSampleCaptionId).collect(Collectors.toList());
            if (list.getFirst().getSampleCaptionIdType().equalsIgnoreCase(TrainingSampleIdTypeConstant.IMAGE_CAPTION_USER_ID)) {
                ImageCaptionUserQuery imageCaptionUserQuery = new ImageCaptionUserQuery();
                imageCaptionUserQuery.setIds(captionIds);
                List<ImageCaptionUserVO> imageCaptionUserList = imageCaptionUserService.queryImageCaptionUserList(imageCaptionUserQuery);
                Map<Integer, ImageCaptionUserVO> imageCaptionUserMap = imageCaptionUserList.stream().collect(Collectors.toMap(ImageCaptionUserVO::getId, t -> t));

                for (TrainingSampleVO each : trainingSampleVOS){
                    if (imageCaptionUserMap.containsKey(each.getSampleCaptionId())) {
                        each.setCaption(imageCaptionUserMap.get(each.getSampleCaptionId()).getCaption());
                    }
                }
                return trainingSampleVOS;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<TrainingSampleVO> getSamplesByTrainingRoundId(Integer roundId) {
        AssertUtil.assertNotNull(roundId, ResultCode.PARAM_INVALID, "roundId is null");

        TrainingSampleExample exam = new TrainingSampleExample();
        exam.createCriteria().andRelatedTrainingIdEqualTo(roundId).andRelatedTrainingIdTypeEqualTo(TrainingSampleIdTypeConstant.ROUND_ID);

        List<TrainingSampleDO> list = trainingSampleDAO.selectByExample(exam);
        if (CollectionUtils.isNotEmpty(list)){
            return TrainingSampleConverter.doList2VOList(list);
        }
        return new ArrayList<>();
    }

    @Override
    public void deleteByTrainingRoundId(Integer roundId) {
        this.getSamplesByTrainingRoundId(roundId).forEach(s -> this.deleteById(s.getId()));
    }
}
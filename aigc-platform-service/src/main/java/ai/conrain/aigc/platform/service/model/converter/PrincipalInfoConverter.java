package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.PrincipalInfoDO;
import ai.conrain.aigc.platform.service.enums.PrincipalTypeEnum;
import ai.conrain.aigc.platform.service.model.query.PrincipalInfoQuery;
import ai.conrain.aigc.platform.dal.example.PrincipalInfoExample;
import ai.conrain.aigc.platform.service.model.vo.PrincipalInfoVO;

import com.alibaba.fastjson.JSONObject;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * PrincipalInfoConverter
 *
 * @version PrincipalInfoService.java v 0.1 2025-06-07 06:21:35
 */
public class PrincipalInfoConverter {

    /**
     * DO -> VO
     */
    public static PrincipalInfoVO do2VO(PrincipalInfoDO from) {
        PrincipalInfoVO to = new PrincipalInfoVO();
        to.setId(from.getId());
        to.setPrincipalType(PrincipalTypeEnum.getByCode(from.getPrincipalType()));
        to.setPrincipalId(from.getPrincipalId());
        to.setInfoKey(from.getInfoKey());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setInfoValue(JSONObject.parseObject(from.getInfoValue()));
        to.setExtInfo(JSONObject.parseObject(from.getExtInfo()));

        return to;
    }

    /**
     * VO -> DO
     */
    public static PrincipalInfoDO vo2DO(PrincipalInfoVO from) {
        PrincipalInfoDO to = new PrincipalInfoDO();
        to.setId(from.getId());
        to.setPrincipalType(from.getPrincipalType().getCode());
        to.setPrincipalId(from.getPrincipalId());
        to.setInfoKey(from.getInfoKey());
        to.setCreatorUserId(from.getCreatorUserId());
        to.setModifyUserId(from.getModifyUserId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        if (!ObjectUtils.isEmpty(from.getInfoValue())) {
            to.setInfoValue(JSONObject.toJSONString(from.getInfoValue()));
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            to.setExtInfo(JSONObject.toJSONString(from.getExtInfo()));
        }

        return to;
    }

    /**
     * Query -> Example
     */
    public static PrincipalInfoExample query2Example(PrincipalInfoQuery from) {
        PrincipalInfoExample to = new PrincipalInfoExample();
        PrincipalInfoExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalType())) {
            c.andPrincipalTypeEqualTo(from.getPrincipalType());
        }
        if (!ObjectUtils.isEmpty(from.getPrincipalId())) {
            c.andPrincipalIdEqualTo(from.getPrincipalId());
        }
        if (!ObjectUtils.isEmpty(from.getInfoKey())) {
            c.andInfoKeyEqualTo(from.getInfoKey());
        }
        if (!ObjectUtils.isEmpty(from.getCreatorUserId())) {
            c.andCreatorUserIdEqualTo(from.getCreatorUserId());
        }
        if (!ObjectUtils.isEmpty(from.getModifyUserId())) {
            c.andModifyUserIdEqualTo(from.getModifyUserId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (PrincipalInfoExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<PrincipalInfoVO> doList2VOList(List<PrincipalInfoDO> list) {
        return CommonUtil.listConverter(list, PrincipalInfoConverter::do2VO);
    }
}
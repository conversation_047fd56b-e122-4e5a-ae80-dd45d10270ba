package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.StatsSaleIndicatorsQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsSaleIndicatorsVO;

import java.util.List;
import java.util.Map;

/**
 * 销售指标 Service定义
 *
 * <AUTHOR>
 * @version StatsSaleIndicatorsService.java v 0.1 2025-05-08 04:38:31
 */
public interface StatsSaleIndicatorsService {

    /**
     * 查询销售指标对象
     *
     * @param id 主键
     * @return 返回结果
     */
    StatsSaleIndicatorsVO selectById(Integer id);

    /**
     * 删除销售指标对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加销售指标对象
     *
     * @param statsSaleIndicators 对象参数
     * @return 返回结果
     */
    StatsSaleIndicatorsVO insert(StatsSaleIndicatorsVO statsSaleIndicators);

    /**
     * 修改销售指标对象
     *
     * @param statsSaleIndicators 对象参数
     */
    void updateByIdSelective(StatsSaleIndicatorsVO statsSaleIndicators);

    /**
     * 带条件批量查询销售指标列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<StatsSaleIndicatorsVO> queryStatsSaleIndicatorsList(StatsSaleIndicatorsQuery query);

    /**
     * 带条件查询销售指标数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryStatsSaleIndicatorsCount(StatsSaleIndicatorsQuery query);

    /**
     * 带条件分页查询销售指标
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<StatsSaleIndicatorsVO> queryStatsSaleIndicatorsByPage(StatsSaleIndicatorsQuery query);

    /**
     * 批量插入或更新
     *
     * @param statsList 批量数据
     * @return 记录条数
     */
    int batchInsertOrUpdate(List<StatsSaleIndicatorsVO> statsList);

    /**
     * 根据父ID列表批量查询子数据
     *
     * @param parentIds 父ID列表
     * @param query     其他查询条件（可选）
     * @return 子数据Map，key为父ID，value为该父ID下的所有子数据列表
     */
    Map<Integer, List<StatsSaleIndicatorsVO>> queryChildrenByParentIds(List<Integer> parentIds, StatsSaleIndicatorsQuery query);

    /**
     * 根据日期和统计周期查询统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsType 统计类型
     * @return 统计数据列表
     */
    List<StatsSaleIndicatorsVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType);
}
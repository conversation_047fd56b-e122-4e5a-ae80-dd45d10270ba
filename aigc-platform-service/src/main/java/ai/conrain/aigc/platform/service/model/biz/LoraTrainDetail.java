package ai.conrain.aigc.platform.service.model.biz;

import java.util.List;

import ai.conrain.aigc.platform.service.model.request.AutoGenImgParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoraTrainDetail extends LoraTaskParams {
    private static final long serialVersionUID = -748265193326378716L;

    private LoraSubTask prepareView;
    private LoraSubTask cutout;
    private LoraSubTask label;
    private LoraSubTask lora;

    private String loraRetFileUrl;

    /** 确认训练 */
    private String loraConfirmed;
    /** 确认训练时间 */
    private String loraConfirmedTime;
    private Integer loraConfirmedOperatorId;
    private String loraConfirmedOperatorNick;

    private Integer relatedOperatorId;
    private String relatedOperatorMobile;
    private String relatedOperatorNick;

    private List<Integer> testFaces;
    private List<Integer> testScenes;
    private Integer testNum;
    // 测试图片比例
    private List<String> testImgProportions;

    //每组图片数，默认20
    private Integer imgNumPerGroup;

    // 测试图片模式，random：随机 | cartesian_product：笛卡尔积
    // @see CommonConstants#TEST_IMG_MODE_RANDOM
    private String testImgMode;

    // 是否用户指定自动生成测试图片
    private Boolean autoGenImg = false;
    private AutoGenImgParam autoGenImgParam;
    private Integer autoGenImgTaskId;

    // 确认可交付
    private ConfirmCanDeliveryInfo confirmCanDeliverInfo;

    /** 测试图搭配 */
    private ClothCollocationModel testClothCollocation;

    private Boolean cutoutAgain;
    private Integer cutoutAgainOperatorId;
    private String cutoutAgainOperatorNick;
    private String cutoutAgainTime;
    private String autoTrain = "N";


    // 带配饰 。保留配饰适用 新工作流必填关键字
    private String withAccessories;

}
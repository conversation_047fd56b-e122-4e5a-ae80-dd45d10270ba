package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.UserProfileQuery;
import ai.conrain.aigc.platform.service.model.vo.UserProfileVO;

import java.util.List;
import java.util.Map;

/**
 * 用户属性 Service定义
 *
 * <AUTHOR>
 * @version UserProfileService.java v 0.1 2024-06-07 07:47:21
 */
public interface UserProfileService {

    /**
     * 查询用户属性对象
     *
     * @param id 主键
     * @return 返回结果
     */
    UserProfileVO selectById(Integer id);

    /**
     * 删除用户属性对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加用户属性对象
     *
     * @param userProfile 对象参数
     * @return 返回结果
     */
    UserProfileVO insert(UserProfileVO userProfile);

    /**
     * 添加或更新用户属性对象
     *
     * @param userProfile 对象参数
     * @return 返回结果
     */
    UserProfileVO insertOrUpdate(UserProfileVO userProfile);

    /**
     * 修改用户属性对象
     *
     * @param userProfile 对象参数
     */
    void updateByIdSelective(UserProfileVO userProfile);

    /**
     * 带条件批量查询用户属性列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<UserProfileVO> queryUserProfileList(UserProfileQuery query);

    /**
     * 带条件查询用户属性数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryUserProfileCount(UserProfileQuery query);

    /**
     * 带条件分页查询用户属性
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<UserProfileVO> queryUserProfileByPage(UserProfileQuery query);

    /**
     * 查询指定用户的指定key
     */
    UserProfileVO selectByUidAndProfileKey(Integer uid, String profileKey);

    /**
     * 初始化收起提醒吊顶
     *
     * @param operatorId 操作员id
     */
    void initRetractNotice(Integer operatorId);

    /**
     * 查询工程师ids
     *
     * @param userIds 用户ID
     * @return 商家对应的工程师
     */
    Map<Integer, Integer> queryPromptEngineers(List<Integer> userIds);

    //根据注册推广码，查询用户ids
    List<Integer> queryUserIdsByPromptionCode(String promotionCode);
}
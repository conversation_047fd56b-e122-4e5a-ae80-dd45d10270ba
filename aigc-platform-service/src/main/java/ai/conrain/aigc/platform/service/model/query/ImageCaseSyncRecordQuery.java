package ai.conrain.aigc.platform.service.model.query;


import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * ImageCaseSyncRecordQuery
 *
 * @version ImageCaseSyncRecordService.java v 0.1 2024-12-16 05:07:05
 */
@Data
public class ImageCaseSyncRecordQuery implements Serializable {
	/** serialVersionUID */
    private static final long serialVersionUID = 1L;

    /** 主键 id */
    private Integer id;

    /** 图片案例id */
    private Integer caseId;

    /** 目标服务器 IP */
    private String targetServer;

    /** 目标存储目录 */
    private String targetStorePath;

    /** 目标图片完整地址 */
    private String imageUrl;

    /** 图片上传时间 */
    private Date uploadTime;

    /** 是否同步成功 */
    private Boolean isSuccess;

    /** 翻页参数：页面大小 */
    private Integer pageSize;

    /** 翻页参数：页数（从1开始） */
    private Integer pageNum;

    /** 排序指令（示例："id asc"） */
    private String orderBy;

    /** 同步类型（badCase、精选图...） */
    private String syncType;

}

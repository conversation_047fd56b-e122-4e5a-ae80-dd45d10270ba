package ai.conrain.aigc.platform.service.helper;

import ai.conrain.aigc.platform.dal.dao.UserPointDAO;
import ai.conrain.aigc.platform.dal.entity.UserPointDO;
import ai.conrain.aigc.platform.dal.example.UserPointExample;
import ai.conrain.aigc.platform.service.component.MaterialModelService;
import ai.conrain.aigc.platform.service.component.ModelPointService;
import ai.conrain.aigc.platform.service.component.SystemConfigService;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import ai.conrain.aigc.platform.service.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static ai.conrain.aigc.platform.service.constants.SystemConstants.SEE_ALL_MODELS_AND_HISTORY;
import static ai.conrain.aigc.platform.service.enums.CreativeTypeEnum.*;

/**
 * 扣费计算Helper
 */
@Slf4j
@Component
public class CalcPredictHelper {

    @Autowired
    private UserPointDAO userPointDAO;

    @Autowired
    private ModelPointService modelPointService;

    @Autowired
    private MaterialModelService materialModelService;

    @Autowired
    private SystemConfigService systemConfigService;

    private static final List<CreativeTypeEnum> HD_DOUBLE_FEE = Arrays.asList( CREATE_IMAGE, FIXED_POSTURE_CREATION, BASIC_CHANGING_CLOTHES );

    /**
     * 新的扣费计算逻辑
     */
    public PredictVO calcPredictNew(CalcContext context) {
        // 1. 特权检查
        if (privilegeProcessor.process(context)) {
            return context.getResult();
        }

        // 2. 根据创作类型选择策略并执行链式处理
        ProcessorChain chain = new ProcessorChain(context);
        switch (context.getType()) {
            case CREATE_IMAGE:
            case FIXED_POSTURE_CREATION:
                chain.imageCreationChain();
                break;
            case CREATE_VIDEO:
                chain.videoCreationChain();
                break;
            case BRAND_TRY_ON:
            case FACE_SCENE_SWITCH:
            case REPAIR_DETAIL:
            case PARTIAL_REDRAW:
            case REPAIR_HANDS:
            case REMOVE_WRINKLE:
            case REMOVE_WRINKLE_4_DC:
            case ERASE_BRUSH:
            case IMAGE_UPSCALE:
            case CLOTH_RECOLOR:
            case IMAGE_EXPAND:
                chain.givePointFirstChain();
                break;
            default:
                chain.directMusePointChain();
                break;
        }

        return context.getResult();
    }

    // ========== 链式原子化处理器 ==========

    /**
     * 处理器链式调用类
     */
    private class ProcessorChain {
        private final CalcContext context;
        private boolean shouldContinue = true;

        private ProcessorChain(CalcContext context) {
            this.context = context;
        }

        /**
         * 图片创作链式处理
         */
        public void imageCreationChain() {
            adjustImageCount().deductModelPoint().deductGivePoint().deductMusePoint();
        }

        /**
         * 视频创作链式处理
         */
        public void videoCreationChain() {
            adjustVideoCount().deductMusePoint();
        }

        /**
         * 赠送积分优先链式处理
         */
        public void givePointFirstChain() {
            adjustImageCount().deductGivePoint().deductMusePoint();
        }

        /**
         * 通用链式处理
         */
        public void directMusePointChain() {
            adjustImageCount().deductMusePoint();
        }

        /**
         * 图片数量调整
         */
        public ProcessorChain adjustImageCount() {
            if (shouldContinue) {
                shouldContinue = !imageCountAdjustProcessor.process(context);
            }
            return this;
        }

        /**
         * 视频数量调整
         */
        public ProcessorChain adjustVideoCount() {
            if (shouldContinue) {
                shouldContinue = !videoCountAdjustProcessor.process(context);
            }
            return this;
        }

        /**
         * 扣除模型积分
         */
        public ProcessorChain deductModelPoint() {
            if (shouldContinue) {
                shouldContinue = !modelPointDeductProcessor.process(context);
            }
            return this;
        }

        /**
         * 扣除赠送积分
         */
        public ProcessorChain deductGivePoint() {
            if (shouldContinue) {
                shouldContinue = !givePointDeductProcessor.process(context);
            }
            return this;
        }

        /**
         * 扣除缪斯点
         */
        public ProcessorChain deductMusePoint() {
            if (shouldContinue) {
                shouldContinue = !musePointDeductProcessor.process(context);
            }
            return this;
        }
    }

    // ========== 处理器 ==========

    /**
     * 特权检查
     */
    private final Processor privilegeProcessor = (context) -> {
        if (OperationContextHolder.isDistributorRole() || OperationContextHolder.isAdmin()
                || systemConfigService.isInJsonArray(SEE_ALL_MODELS_AND_HISTORY,
                OperationContextHolder.getOperatorUserId())) {

            context.getResult().setMusePoint(BigDecimal.ZERO);
            context.setRemainingCount(BigDecimal.ZERO);
            return true; // 停止后续处理
        }
        return false; // 继续处理
    };

    /**
     * 图片数量调整
     */
    private final Processor imageCountAdjustProcessor = (context) -> {
        BigDecimal imageNum = context.getRemainingCount();

        // 固定姿势双倍消耗
        if (context.getType() == FIXED_POSTURE_CREATION) {
            imageNum = BigDecimalUtils.multiply(imageNum, BigDecimal.TWO);
        }

        // 高分辨率双倍计费
        if (HD_DOUBLE_FEE.contains(context.getType()) && ProportionTypeEnum.P_1620_2100.equals(context.getProportionType())) {
            imageNum = BigDecimalUtils.multiply(imageNum, BigDecimal.TWO);
        }

        // 是否按张数计费
        if (!context.getType().isConsumeByImageCnt()) {
            imageNum = BigDecimal.ONE;
        }

        context.setRemainingCount(imageNum);
        return false; // 继续处理
    };

    /**
     * 视频时长调整
     */
    private final Processor videoCountAdjustProcessor = (context) -> {
        Integer timeSecs4Video = context.getTimeSecs4Video();
        AssertUtil.assertTrue(timeSecs4Video != null && (timeSecs4Video == 5 || timeSecs4Video == 10),
                ResultCode.BIZ_FAIL, "视频时长参数错误：" + timeSecs4Video);

        // 10秒视频是5秒的两倍消耗
        BigDecimal count = timeSecs4Video == 10 ? BigDecimalUtils.multiply(context.getRemainingCount(), BigDecimal.TWO) : context.getRemainingCount();
        context.setRemainingCount(count);
        return false; // 继续处理
    };

    /**
     * 套内点数扣除
     */
    private final Processor modelPointDeductProcessor = (context) -> {
        if (BigDecimalUtils.lessThanOrEqualsZero(context.getRemainingCount()) || context.getModelId() == null) {
            return false; // 跳过但继续
        }

        Integer modelId = context.getModelId();
        Integer userId = context.getUserId();

        ModelPointVO modelPoint = context.isNeedLock()
                ? modelPointService.lockByModelId(modelId, userId)
                : modelPointService.selectByModelId(modelId, userId);

        if (modelPoint == null) {
            MaterialModelVO modelVO = materialModelService.selectById(modelId);
            if (OperationContextHolder.isBackRole() || (modelVO != null && modelVO.getType() == ModelTypeEnum.SYSTEM)) {
                modelPoint = new ModelPointVO();
                modelPoint.setPoint(0);
            } else {
                throw new BizException(ResultCode.BIZ_FAIL, "未查询到服装套餐内积分记录");
            }
        } else {
            AssertUtil.assertFrontPermission(modelPoint.getUserId(), "权限检查不通过，非当前模型所有者");
        }

        context.getResult().setCurrentModelPoint(modelPoint.getPoint());

        BigDecimal deductCount = BigDecimalUtils.min(BigDecimal.valueOf(modelPoint.getPoint()), context.getRemainingCount());
        if (BigDecimalUtils.greaterThanZero(deductCount)) {
            // 这一步 modelPoint : remainingCount = 1 : 1, 不会产生小数位
            context.getResult().setModelPoint(BigDecimalUtils.intValue(deductCount));
            context.getResult().setModelPointVO(modelPoint);
            context.setRemainingCount(BigDecimalUtils.subtract(context.getRemainingCount(), deductCount));
        }

        return false; // 继续处理
    };

    /**
     * 赠送积分扣除
     */
    private final Processor givePointDeductProcessor = (context) -> {
        if (BigDecimalUtils.lessThanOrEqualsZero(context.getRemainingCount())) {
            return false; // 跳过但继续
        }

        UserPointDO userPoint = context.getResult().getUserPointDO();
        if (ObjectUtils.isEmpty(userPoint)) {
            userPoint = getUserPointForDeduction(context);
            if (userPoint != null) {
                context.getResult().setUserPointDO(userPoint);
            } else {
                context.getResult().setNeedTopup(true);
                return true; // 结束
            }
        }

        context.getResult().setCurrentDisplayGivePoint(CommonUtil.point2MusePoint(userPoint.getGivePoint()));
        context.getResult().setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));

        // 获取单位赠送点消耗
        BigDecimal unitCost = context.isUpload()
                ? context.getType().getConsumeGivePointForUpload()
                : context.getType().getConsumeGivePoint();

        // 计算总消耗
        BigDecimal totalCost = BigDecimalUtils.multiply(context.getRemainingCount(), unitCost);

        BigDecimal deduct = BigDecimalUtils.min(CommonUtil.point2MusePoint(userPoint.getGivePoint()), totalCost);
        if (BigDecimalUtils.greaterThanZero(deduct)) {
            context.getResult().setDisplayGivePoint(deduct);
            context.setRemainingCount(BigDecimalUtils.subtract(context.getRemainingCount(), BigDecimalUtils.divide(deduct, unitCost)));
        }

        return false; // 继续处理
    };

    /**
     * 缪斯点扣除
     */
    private final Processor musePointDeductProcessor = (context) -> {
        if (BigDecimalUtils.lessThanOrEqualsZero(context.getRemainingCount())) {
            return false; // 跳过但继续
        }

        UserPointDO userPoint = context.getResult().getUserPointDO();
        if (ObjectUtils.isEmpty(userPoint)) {
            userPoint = getUserPointForDeduction(context);
            log.warn("查询userPoint失败, needLock: {}, userId: {}, creativeType: {}", context.isNeedLock(), context.getUserId(), context.getType().getCode());
            if (userPoint != null) {
                context.getResult().setUserPointDO(userPoint);
            } else {
                context.getResult().setNeedTopup(true);
                return true; // 结束
            }
        }

        // 设置当前缪斯点（如果还没设置）
        if (Objects.isNull(context.getResult().getCurrentMusePoint())) {
            context.getResult().setCurrentMusePoint(CommonUtil.point2MusePoint(userPoint.getPoint()));
        }
        if (Objects.isNull(context.getResult().getCurrentDisplayGivePoint())) {
            context.getResult().setCurrentDisplayGivePoint(CommonUtil.point2MusePoint(userPoint.getGivePoint()));
        }

        // 获取单位缪斯点消耗
        BigDecimal unitCost = context.isUpload()
                ? context.getType().getConsumeMusePointsForUpload()
                : context.getType().getConsumeMusePoints();

        // 检查是否不消耗缪斯点
        if (!BigDecimalUtils.greaterThanZero(unitCost)) {
            context.getResult().setMusePoint(BigDecimal.ZERO);
            context.setRemainingCount(BigDecimal.ZERO);
            return false; // 继续处理
        }

        // 计算总消耗
        BigDecimal totalCost = BigDecimalUtils.multiply(context.getRemainingCount(), unitCost);

        int requiredPoint = CommonUtil.musePoint2Point(totalCost);

        // 检查积分是否足够
        if (userPoint.getPoint() < requiredPoint) {
            context.getResult().setMusePoint(totalCost);
            context.getResult().setNeedTopup(true);
            return false; // 继续处理
        }

        context.getResult().setMusePoint(totalCost);
        context.setRemainingCount(BigDecimal.ZERO);
        return false; // 继续处理
    };

    /**
     * 原子处理器接口
     */
    @FunctionalInterface
    private interface Processor {
        /**
         * 处理操作
         *
         * @param context 上下文
         * @return true=停止后续操作，false=继续执行
         */
        boolean process(CalcContext context);
    }

    /**
     * 获取用户积分信息（辅助方法）
     */
    private UserPointDO getUserPointForDeduction(CalcContext context) {
        if (context.getResult().getUserPointDO() != null) {
            return context.getResult().getUserPointDO();
        }

        UserPointDO userPoint = context.isNeedLock()
                ? userPointDAO.lockByUserId(context.getUserId())
                : selectDOByUserId(context.getUserId());

        context.getResult().setUserPointDO(userPoint);

        if (userPoint == null) {
            context.getResult().setNeedTopup(true);
        }

        return userPoint;
    }

    /**
     * 根据用户ID查询用户积分信息
     */
    private UserPointDO selectDOByUserId(Integer userId) {
        AssertUtil.assertNotNull(userId, ResultCode.PARAM_INVALID, "userId is null");
        UserPointExample example = new UserPointExample();
        example.createCriteria().andUserIdEqualTo(userId);
        List<UserPointDO> list = userPointDAO.selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}


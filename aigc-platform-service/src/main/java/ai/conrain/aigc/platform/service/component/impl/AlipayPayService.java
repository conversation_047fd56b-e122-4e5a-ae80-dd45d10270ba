package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.alipay.AlipayService;
import ai.conrain.aigc.platform.service.constants.PayType;
import ai.conrain.aigc.platform.service.model.biz.PayStatusEnum;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AlipayPayService extends AbstractPayService {

    @Autowired
    private AlipayService alipayService;

    @Override
    protected String _createQrCodeUrl(String orderNo, String payAmount, String planCode, Integer masterUserId) {
        return alipayService.tradePreCreate(orderNo, payAmount);
    }

    @Override
    protected String _getPayType() {
        return PayType.ALIPAY;
    }

    @Override
    protected PayStatusEnum _queryPayResultStatus(String orderNo) {

        AlipayTradeQueryResponse queryResult = alipayService.tradeQuery(orderNo);
        AssertUtil.assertNotNull(queryResult, "查询订单失败");

        if (queryResult.isSuccess()) {
            AssertUtil.assertNotBlank(queryResult.getTradeStatus(), "查询订单失败，订单状态为空");
            PayStatusEnum payStatus = PayStatusEnum.getByAlipayTradeStatus(queryResult.getTradeStatus());
            AssertUtil.assertNotNull(payStatus, "查询订单失败，订单状态不合法");
            return payStatus;
        } else {
            //用户还没扫码，支付宝还没有形成订单
            if (StringUtils.equals(queryResult.getSubCode(), "ACQ.TRADE_NOT_EXIST")) {
                return PayStatusEnum.NOTPAY;
            }

            //其它支付宝异常
            return PayStatusEnum.PAYERROR;
        }
    }
}

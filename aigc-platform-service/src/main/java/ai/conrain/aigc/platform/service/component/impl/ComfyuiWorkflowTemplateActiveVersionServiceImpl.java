package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.dao.ComfyuiWorkflowTemplateActiveVersionDAO;
import ai.conrain.aigc.platform.dal.entity.ComfyuiWorkflowTemplateActiveVersionDO;
import ai.conrain.aigc.platform.service.component.ComfyuiWorkflowTemplateActiveVersionService;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ComfyuiWorkflowTemplateActiveVersionConverter;
import ai.conrain.aigc.platform.service.model.vo.ComfyuiWorkflowTemplateActiveVersionVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ComfyuiWorkflowTemplateActiveVersionService实现
 *
 * <AUTHOR>
 * @version ComfyuiWorkflowTemplateActiveVersionService.java v 0.1 2025-06-30 05:46:14
 */
@Slf4j
@Service
public class ComfyuiWorkflowTemplateActiveVersionServiceImpl implements ComfyuiWorkflowTemplateActiveVersionService {

    /** DAO */
    @Autowired
    private ComfyuiWorkflowTemplateActiveVersionDAO comfyuiWorkflowTemplateActiveVersionDAO;

    @Override
    public ComfyuiWorkflowTemplateActiveVersionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ComfyuiWorkflowTemplateActiveVersionDO data = comfyuiWorkflowTemplateActiveVersionDAO.selectByPrimaryKey(id);
        if (null == data) {
            return null;
        }

        return ComfyuiWorkflowTemplateActiveVersionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = comfyuiWorkflowTemplateActiveVersionDAO.deleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ComfyuiWorkflowTemplateActiveVersion失败");
    }

    @Override
    public ComfyuiWorkflowTemplateActiveVersionVO insert(
        ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion) {
        AssertUtil.assertNotNull(comfyuiWorkflowTemplateActiveVersion, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplateActiveVersion is null");
        AssertUtil.assertTrue(comfyuiWorkflowTemplateActiveVersion.getId() == null, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplateActiveVersion.id is present");

        //创建时间、修改时间兜底
        if (comfyuiWorkflowTemplateActiveVersion.getCreateTime() == null) {
            comfyuiWorkflowTemplateActiveVersion.setCreateTime(new Date());
        }

        if (comfyuiWorkflowTemplateActiveVersion.getModifyTime() == null) {
            comfyuiWorkflowTemplateActiveVersion.setModifyTime(new Date());
        }

        ComfyuiWorkflowTemplateActiveVersionDO data = ComfyuiWorkflowTemplateActiveVersionConverter.vo2DO(
            comfyuiWorkflowTemplateActiveVersion);
        Integer n = comfyuiWorkflowTemplateActiveVersionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ComfyuiWorkflowTemplateActiveVersion失败");
        AssertUtil.assertNotNull(data.getId(), "新建ComfyuiWorkflowTemplateActiveVersion返回id为空");
        comfyuiWorkflowTemplateActiveVersion.setId(data.getId());
        return comfyuiWorkflowTemplateActiveVersion;
    }

    @Override
    public void updateById(ComfyuiWorkflowTemplateActiveVersionVO comfyuiWorkflowTemplateActiveVersion) {
        AssertUtil.assertNotNull(comfyuiWorkflowTemplateActiveVersion, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplateActiveVersion is null");
        AssertUtil.assertTrue(comfyuiWorkflowTemplateActiveVersion.getId() != null, ResultCode.PARAM_INVALID,
            "comfyuiWorkflowTemplateActiveVersion.id is null");
        //修改时间必须更新
        comfyuiWorkflowTemplateActiveVersion.setModifyTime(new Date());
        comfyuiWorkflowTemplateActiveVersion.setModifyBy(OperationContextHolder.getOperatorUserId());

        ComfyuiWorkflowTemplateActiveVersionDO data = ComfyuiWorkflowTemplateActiveVersionConverter.vo2DO(
            comfyuiWorkflowTemplateActiveVersion);
        int n = comfyuiWorkflowTemplateActiveVersionDAO.updateByPrimaryKey(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL,
            "更新ComfyuiWorkflowTemplateActiveVersion失败，影响行数:" + n);
    }

    @Override
    public List<ComfyuiWorkflowTemplateActiveVersionVO> findAll() {
        List<ComfyuiWorkflowTemplateActiveVersionDO> list = comfyuiWorkflowTemplateActiveVersionDAO.selectAll();
        return ComfyuiWorkflowTemplateActiveVersionConverter.doList2VOList(list);
    }

    @Override
    public List<ComfyuiWorkflowTemplateActiveVersionVO> batchQueryByTemplateKey(List<String> templateKeys) {
        List<ComfyuiWorkflowTemplateActiveVersionDO> list
            = comfyuiWorkflowTemplateActiveVersionDAO.batchSelectByTemplateKey(templateKeys);
        return ComfyuiWorkflowTemplateActiveVersionConverter.doList2VOList(list);
    }

    @Override
    public ComfyuiWorkflowTemplateActiveVersionVO queryByTemplateKey(String templateKey) {
        AssertUtil.assertNotBlank(templateKey, ResultCode.PARAM_INVALID, "templateKey is null");
        ComfyuiWorkflowTemplateActiveVersionDO data = comfyuiWorkflowTemplateActiveVersionDAO.selectByTemplateKey(
            templateKey);
        return ComfyuiWorkflowTemplateActiveVersionConverter.do2VO(data);
    }
}
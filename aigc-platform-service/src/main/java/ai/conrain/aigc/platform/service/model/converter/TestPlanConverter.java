package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.TestPlanDO;
import ai.conrain.aigc.platform.service.enums.TestStatusEnum;
import ai.conrain.aigc.platform.service.enums.TestTypeEnum;
import ai.conrain.aigc.platform.service.model.query.TestPlanQuery;
import ai.conrain.aigc.platform.dal.example.TestPlanExample;
import ai.conrain.aigc.platform.service.model.request.AddTestPlanRequest;
import ai.conrain.aigc.platform.service.model.vo.TestPlanVO;

import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

import com.alibaba.fastjson.JSONObject;

/**
 * TestPlanConverter
 *
 * @version TestPlanService.java v 0.1 2024-12-19 01:24:06
 */
public class TestPlanConverter {

    /**
     * DO -> VO
     */
    public static TestPlanVO do2VO(TestPlanDO from) {
        if (null == from) {
            return null;
        }
        TestPlanVO to = new TestPlanVO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(TestTypeEnum.getByCode(from.getType()));
        to.setStatus(TestStatusEnum.getByCode(from.getStatus()));
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setExtInfo(StringUtils.isNotBlank(from.getExtInfo()) ? JSONObject.parseObject(from.getExtInfo()) : null);
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setConclusion(from.getConclusion());

        return to;
    }

    /**
     * VO -> DO
     */
    public static TestPlanDO vo2DO(TestPlanVO from) {
        TestPlanDO to = new TestPlanDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType().getCode());
        to.setStatus(from.getStatus().getCode());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setExtInfo(from.getExtInfo() != null ? from.getExtInfo().toString() : null);
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());
        to.setConclusion(from.getConclusion());

        return to;
    }

    public static TestPlanVO request2VO(AddTestPlanRequest from) {
        if (from == null) {
            return null;
        }
        TestPlanVO to = new TestPlanVO();
        to.setName(from.getName());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        Integer userId = OperationContextHolder.getMasterUserId();
        to.setUserId(userId);
        to.setOperatorId(userId);

        return to;
    }

    /**
     * DO -> Query
     */
    public static TestPlanQuery do2Query(TestPlanDO from) {
        TestPlanQuery to = new TestPlanQuery();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static TestPlanDO query2DO(TestPlanQuery from) {
        TestPlanDO to = new TestPlanDO();
        to.setId(from.getId());
        to.setName(from.getName());
        to.setType(from.getType());
        to.setStatus(from.getStatus());
        to.setMemo(from.getMemo());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> Example
     */
    public static TestPlanExample query2Example(TestPlanQuery from) {
        TestPlanExample to = new TestPlanExample();
        TestPlanExample.Criteria c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getName())) {
            c.andNameEqualTo(from.getName());
        }
        if (!ObjectUtils.isEmpty(from.getNameLike())) {
            c.andNameLike(from.getNameLike());
        }
        if (!ObjectUtils.isEmpty(from.getType())) {
            c.andTypeEqualTo(from.getType());
        }
        if (!ObjectUtils.isEmpty(from.getStatus())) {
            c.andStatusEqualTo(from.getStatus());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        if (!ObjectUtils.isEmpty(from.getStatusList())) {
            c.andStatusIn(from.getStatusList());
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }

    /**
     * do list -> vo list
     */
    public static List<TestPlanVO> doList2VOList(List<TestPlanDO> list) {
        return CommonUtil.listConverter(list, TestPlanConverter::do2VO);
    }
}
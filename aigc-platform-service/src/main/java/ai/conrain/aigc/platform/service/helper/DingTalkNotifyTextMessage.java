package ai.conrain.aigc.platform.service.helper;

import lombok.Data;

import java.util.List;

/**
 * https://open.dingtalk.com/document/orgapp/custom-bot-send-message-type
 * {
 *     "at": {
 *         "atMobiles":[
 *             "180xxxxxx"
 *         ],
 *         "atUserIds":[
 *             "user123"
 *         ],
 *         "isAtAll": false
 *     },
 *     "text": {
 *         "content":"我就是我, @user123 是不一样的烟火"
 *     },
 *     "msgtype":"text"
 * }
 */
@Data
public class DingTalkNotifyTextMessage {
    private At at;
    private Text text;
    private String msgtype;

    @Data
    public static class Text {
        private String content;
    }

    @Data
    public static class At {
        private List<String> atMobiles;
        private List<String> atUserIds;
        private boolean isAtAll;
    }
}
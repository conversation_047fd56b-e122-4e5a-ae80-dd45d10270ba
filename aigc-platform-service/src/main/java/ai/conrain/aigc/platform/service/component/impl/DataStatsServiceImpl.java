/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.impl;

import java.util.Date;
import java.util.List;

import ai.conrain.aigc.platform.dal.dao.MaterialModelDAO;
import ai.conrain.aigc.platform.dal.entity.StatsDeliveryDO;
import ai.conrain.aigc.platform.service.component.DataStatsService;
import ai.conrain.aigc.platform.service.model.converter.DataStatsConverter;
import ai.conrain.aigc.platform.service.model.stats.DeliveryStatsVO;
import ai.conrain.aigc.platform.service.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据统计服务实现
 *
 * <AUTHOR>
 * @version : DataStatsServiceImpl.java, v 0.1 2024/9/14 13:02 renxiao.wu Exp $
 */
@Service
public class DataStatsServiceImpl implements DataStatsService {
    @Autowired
    private MaterialModelDAO materialModelDAO;

    @Override
    public List<DeliveryStatsVO> statsDelivery(String startDate, String endDate) {
        if (StringUtils.isBlank(startDate)) {
            startDate = DateUtils.formatSimpleDate(DateUtils.addDays(new Date(), -8));
        }

        if (StringUtils.isBlank(endDate)) {
            endDate = DateUtils.formatSimpleDate(new Date());
        }

        List<StatsDeliveryDO> list = materialModelDAO.statsDelivery(startDate, endDate);
        return DataStatsConverter.doList2VOList(list);
    }
}

package ai.conrain.aigc.platform.service.component.creative.async;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.integration.xiutu.XiutuService;
import ai.conrain.aigc.platform.service.helper.ComfyUIHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkleRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service("removeWrinkleAsyncExecutor")
public class RemoveWrinkleAsyncExecutor extends AbstractBatchToAsyncExecutor<RemoveWrinkleRequest, String>{

    @Autowired
    private XiutuService xiutuService;
    @Autowired
    private OssService ossService;
    @Autowired
    private ComfyUIHelper comfyUIHelper;

    @Override
    protected Class<String> getModelClass() {
        return String.class;
    }

    @Override
    protected Object buildOriginValue(RemoveWrinkleRequest request) {
        if ( request == null || StringUtils.isBlank(request.getOriginImage())) {
            return null;
        }
        String originImgUrl = request.getOriginImage();
        String picId = IntegrationUtils.uuid();
        String targetOssObjectName = String.format("%s/rw_%s.%s", IntegrationUtils.getOssFileDirFromUrl(originImgUrl), picId, IntegrationUtils.getFileExtensionFromUrl(originImgUrl));
        boolean success =  xiutuService.applyRemoveWrinkle(picId, originImgUrl, targetOssObjectName, 100);
        AssertUtil.assertTrue(success, ResultCode.BIZ_FAIL, "创建衣服去皱任务失败");
        return targetOssObjectName;
    }

    @Override
    protected String buildTransValue(CreativeTaskVO task, String origin) {
        if ( StringUtils.isBlank(origin)) {
            return null;
        }
        if (ossService.checkFileExists(origin)) {
            try {
                String url = ossService.getSignedFileUrl(origin);
                AssertUtil.assertNotBlank(url, ResultCode.BIZ_FAIL, "衣服去皱结果为空");
                return comfyUIHelper.upLoadImage(url, task);
            } catch (Exception e) {
                log.error("衣服去皱结果为空", e);
            }
        }
        return null;
    }

    @Override
    public String getOriginKey() {
        return KEY_REMOVE_WRINKLE_OSS_NAME;
    }

    @Override
    public String getTransKey() {
        return KEY_REMOVE_WRINKLE_RESULT;
    }
}

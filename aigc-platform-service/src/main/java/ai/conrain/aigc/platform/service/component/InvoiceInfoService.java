package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.InvoiceInfoQuery;
import ai.conrain.aigc.platform.service.model.vo.InvoiceInfoVO;
import ai.conrain.aigc.platform.service.model.vo.InvoiceTitleVO;

import java.util.List;

/**
 * 发票记录 Service定义
 *
 * <AUTHOR>
 * @version InvoiceInfoService.java v 0.1 2024-06-27 01:42:09
 */
public interface InvoiceInfoService {
	
	/**
	 * 查询发票记录对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	InvoiceInfoVO selectById(Integer id);

	/**
	 * 删除发票记录对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加发票记录对象
	 * @param invoiceInfo 对象参数
	 * @return 返回结果
	 */
	InvoiceInfoVO insert(InvoiceInfoVO invoiceInfo);

	/**
	 * 申请发票
	 */
	Integer applyInvoice(InvoiceTitleVO invoiceTitle, Integer orderId);

	/**
	 * 申请冲销发票
	 * @param id
	 */
	void applyReverseInvoice(Integer id);

	// 重试
	void retry(Integer id);

	/**
	 * 根据订单id查询发票信息
	 * @param orderId
	 * @return
	 */
	InvoiceInfoVO queryInvoiceInfoByOrderId(Integer orderId);

	/**
	 * 修改发票记录对象
	 * @param invoiceInfo 对象参数
	 */
	void updateByIdSelective(InvoiceInfoVO invoiceInfo);

	/**
	 * 带条件批量查询发票记录列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<InvoiceInfoVO> queryInvoiceInfoList(InvoiceInfoQuery query);

	/**
	 * 带条件查询发票记录数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryInvoiceInfoCount(InvoiceInfoQuery query);

	/**
	 * 带条件分页查询发票记录
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<InvoiceInfoVO> queryInvoiceInfoByPage(InvoiceInfoQuery query);
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.request.FixVideoFaceRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.ComfyUIUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_FACE_IMAGE;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_INDEX;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_ORIGIN_TASK;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_RELATED;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_VIDEO;
import static ai.conrain.aigc.platform.service.constants.CommonConstants.KEY_VIDEO_URL;

/**
 * 脸部修复创建服务
 *
 * <AUTHOR>
 * @version : FixVideoFaceCreateService.java, v 0.1 2024/9/7 20:47 renxiao.wu Exp $
 */
@Service
public class FixVideoFaceCreateService extends AbstractCreativeService<FixVideoFaceRequest> {
    @Autowired
    private OssService ossService;

    @Override
    protected CreativeBatchVO buildData(FixVideoFaceRequest request, MaterialModelVO modelVO) throws IOException {
        // 1.将视频文件上传至comfyui
        String imageUrl = CommonUtil.getFilePathAndNameFromURL(request.getVideoUrl());
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);

        File file = new File(tmpUrl);

        Integer masterUserId = OperationContextHolder.getMasterUserId();
        String path = ComfyUIUtils.buildInputPath(masterUserId) + CommonUtil.getFileNameFromUrl(request.getVideoUrl());
        fileDispatch.uploadFile(path, Files.newInputStream(file.toPath()), masterUserId);

        // 2.构建模型数据
        CreativeBatchVO data = new CreativeBatchVO();
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setShowImage(request.getImageUrl());

        data.setImageProportion(ProportionTypeEnum.THREE_FOUR.getCode());
        data.addExtInfo(KEY_ORIGIN_TASK, request.getBatchId());
        data.addExtInfo(KEY_VIDEO_URL, request.getVideoUrl());
        data.addExtInfo(KEY_INDEX, request.getIndex());

        data.addExtInfo(KEY_VIDEO, path);
        CreativeElementVO element = creativeElementService.selectById(request.getFaceId());

        String faceImage = element.getExtInfo(KEY_FACE_IMAGE, String.class);
        AssertUtil.assertNotBlank(faceImage, "模特对应换脸图片未配置！");

        data.addExtInfo(KEY_FACE_IMAGE, faceImage);
        data.setUserId(masterUserId);
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setBatchCnt(1);
        data.setType(getCreativeType());
        return data;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements, int idx) {
        target.addExtInfo(KEY_VIDEO, batch.getStringFromExtInfo(KEY_VIDEO));
        target.addExtInfo(KEY_FACE_IMAGE, batch.getStringFromExtInfo(KEY_FACE_IMAGE));
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        context.put(KEY_VIDEO, task.getStringFromExtInfo(KEY_VIDEO));
        context.put(KEY_FACE_IMAGE, task.getStringFromExtInfo(KEY_FACE_IMAGE));
    }

    @Override
    protected void postProcess(CreativeBatchVO data, FixVideoFaceRequest request) {
        CreativeBatchVO origin = creativeBatchService.selectById(request.getBatchId());
        origin.addExtInfo(KEY_RELATED + request.getIndex(), data.getId());
        creativeBatchService.updateByIdSelective(origin);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.FIX_VIDEO_FACE;
    }
}

package ai.conrain.aigc.platform.service.model.converter;

import ai.conrain.aigc.platform.dal.entity.InvoiceOrderDO;
import ai.conrain.aigc.platform.service.model.query.InvoiceOrderQuery;
import ai.conrain.aigc.platform.dal.example.InvoiceOrderExample;
import ai.conrain.aigc.platform.service.model.vo.InvoiceOrderVO;

import org.springframework.util.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import ai.conrain.aigc.platform.service.util.CommonUtil;

import java.util.*;

/**
 * InvoiceOrderConverter
 *
 * @version InvoiceOrderService.java v 0.1 2024-06-27 12:49:39
 */
public class InvoiceOrderConverter {

    /**
     * DO -> VO
     */
    public static InvoiceOrderVO do2VO(InvoiceOrderDO from) {
        InvoiceOrderVO to = new InvoiceOrderVO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setInvoiceId(from.getInvoiceId());
        to.setOrderId(from.getOrderId());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * VO -> DO
     */
    public static InvoiceOrderDO vo2DO(InvoiceOrderVO from) {
        InvoiceOrderDO to = new InvoiceOrderDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setInvoiceId(from.getInvoiceId());
        to.setOrderId(from.getOrderId());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * DO -> Query
     */
    public static InvoiceOrderQuery do2Query(InvoiceOrderDO from) {
        InvoiceOrderQuery to = new InvoiceOrderQuery();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setInvoiceId(from.getInvoiceId());
        to.setOrderId(from.getOrderId());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }

    /**
     * Query -> DO
     */
    public static InvoiceOrderDO query2DO(InvoiceOrderQuery from) {
        InvoiceOrderDO to = new InvoiceOrderDO();
        to.setId(from.getId());
        to.setUserId(from.getUserId());
        to.setOperatorId(from.getOperatorId());
        to.setInvoiceId(from.getInvoiceId());
        to.setOrderId(from.getOrderId());
        to.setMemo(from.getMemo());
        to.setExtInfo(from.getExtInfo());
        to.setCreateTime(from.getCreateTime());
        to.setModifyTime(from.getModifyTime());

        return to;
    }


    /**
     * Query -> Example
     */
    public static InvoiceOrderExample query2Example(InvoiceOrderQuery from) {
        InvoiceOrderExample to = new InvoiceOrderExample();
        InvoiceOrderExample.Criteria  c = to.createCriteria();

        //各字段条件过滤
        if (!ObjectUtils.isEmpty(from.getId())) {
            c.andIdEqualTo(from.getId());
        }
        if (!ObjectUtils.isEmpty(from.getUserId())) {
            c.andUserIdEqualTo(from.getUserId());
        }
        if (!ObjectUtils.isEmpty(from.getOperatorId())) {
            c.andOperatorIdEqualTo(from.getOperatorId());
        }
        if (!ObjectUtils.isEmpty(from.getInvoiceId())) {
            c.andInvoiceIdEqualTo(from.getInvoiceId());
        }
        if (!ObjectUtils.isEmpty(from.getOrderId())) {
            c.andOrderIdEqualTo(from.getOrderId());
        }
        if (!ObjectUtils.isEmpty(from.getMemo())) {
            c.andMemoEqualTo(from.getMemo());
        }
        if (!ObjectUtils.isEmpty(from.getExtInfo())) {
            c.andExtInfoEqualTo(from.getExtInfo());
        }
        if (!ObjectUtils.isEmpty(from.getCreateTime())) {
            c.andCreateTimeEqualTo(from.getCreateTime());
        }
        if (!ObjectUtils.isEmpty(from.getModifyTime())) {
            c.andModifyTimeEqualTo(from.getModifyTime());
        }
        //逻辑删除过滤
        for (InvoiceOrderExample.Criteria each : to.getOredCriteria()) {
            each.andLogicalDeleted(false);
        }
        //翻页参数
        if (from.getPageSize() != null && from.getPageNum() != null) {
            to.page(from.getPageNum(), from.getPageSize());
        }

        //排序参数
        if (StringUtils.isNotBlank(from.getOrderBy())) {
            to.setOrderByClause(from.getOrderBy());
        }

        return to;
    }
    /**
     * do list -> vo list
     */
    public static List<InvoiceOrderVO> doList2VOList(List<InvoiceOrderDO> list) {
        return CommonUtil.listConverter(list, InvoiceOrderConverter::do2VO);
    }
}
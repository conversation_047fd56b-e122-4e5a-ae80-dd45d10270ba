package ai.conrain.aigc.platform.service.model.request;

import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotBlank;

import lombok.Data;

@Data
public class FaceSceneSwitchRequest implements CustomSceneCreativeRequest {
    private static final long serialVersionUID = -5260397463082869479L;

    /** 原始图片 */
    @NotBlank
    private String image;

    /** 配置 */
    private Map<Integer, List<Integer>> configs;

    /** 用户上传场景 */
    private String customSceneImg;

    /** 用户上传场景描述 */
    private String customScene;

    /** 图片数量 */
    private int imageNum;
}

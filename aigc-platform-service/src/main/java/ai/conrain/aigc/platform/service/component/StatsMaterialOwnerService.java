package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.dal.entity.StatsMaterialOwnerDO;
import ai.conrain.aigc.platform.service.model.query.StatsMaterialOwnerQuery;
import ai.conrain.aigc.platform.service.model.vo.StatsMaterialOwnerVO;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;

import java.util.List;

/**
 * 服装负责人 Service定义
 *
 * <AUTHOR>
 * @version StatsMaterialOwnerService.java v 0.1 2025-04-30 03:31:59
 */
public interface StatsMaterialOwnerService {

    /**
     * 查询服装负责人对象
     *
     * @param id 主键
     * @return 返回结果
     */
    StatsMaterialOwnerVO selectById(Integer id);

    /**
     * 删除服装负责人对象
     *
     * @param id 主键
     */
    void deleteById(Integer id);

    /**
     * 添加服装负责人对象
     *
     * @param statsMaterialOwner 对象参数
     * @return 返回结果
     */
    StatsMaterialOwnerVO insert(StatsMaterialOwnerVO statsMaterialOwner);

    /**
     * 修改服装负责人对象
     *
     * @param statsMaterialOwner 对象参数
     */
    void updateByIdSelective(StatsMaterialOwnerVO statsMaterialOwner);

    /**
     * 带条件批量查询服装负责人列表
     *
     * @param query 查询条件
     *              return 结果
     */
    List<StatsMaterialOwnerVO> queryStatsMaterialOwnerList(StatsMaterialOwnerQuery query);

    /**
     * 带条件查询服装负责人数量
     *
     * @param query 查询条件
     *              return 记录条数
     */
    Long queryStatsMaterialOwnerCount(StatsMaterialOwnerQuery query);

    /**
     * 带条件分页查询服装负责人
     *
     * @param query 查询条件
     *              return 分页结果
     */
    PageInfo<StatsMaterialOwnerVO> queryStatsMaterialOwnerByPage(StatsMaterialOwnerQuery query);

    /**
     * 批量插入或更新服装负责人
     *
     * @param statsList 服装负责人列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(List<StatsMaterialOwnerVO> statsList);

    /**
     * 根据日期和统计类型查询服装负责人信息
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param statsType 统计类型
     * @return 结果
     */
    List<StatsMaterialOwnerVO> selectStatsInfoByDateAndPeriod(String startDate, String endDate, String statsType);
}
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <url>http://maven.apache.org</url>
    <parent>
        <groupId>ai.conrain.aigc.platform</groupId>
        <artifactId>aigc-platform-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>aigc-platform-integration</artifactId>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.aliyun.tair</groupId>
            <artifactId>alibabacloud-tairjedis-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>green20220302</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>sts20150401</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sts</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>ice20201109</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>imm20200930</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pgvector</groupId>
            <artifactId>pgvector</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.9</version>
        </dependency>

        <!-- 绘蛙 SDK -->
        <dependency>
            <groupId>com.taobao.api</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>
                ${project.basedir}/src/main/resources/libs/huiwa/taobao-sdk-java-auto_1742178906955-20250804.jar
            </systemPath>
        </dependency>
        <dependency>
            <groupId>com.taobao.api</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <classifier>sources</classifier>
            <systemPath>
                ${project.basedir}/src/main/resources/libs/huiwa/taobao-sdk-java-auto_1742178906955-20250804-source.jar
            </systemPath>
        </dependency>

        <!-- 美图 SDK -->
        <dependency>
            <groupId>com.meitu</groupId>
            <artifactId>openai-common-signer</artifactId>
            <version>1.0.2</version>
            <scope>system</scope>
            <systemPath>
                ${project.basedir}/src/main/resources/libs/meitu/openai-common-signer-1.0.2.jar
            </systemPath>
        </dependency>

    </dependencies>

</project>
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 服务工具类
 *
 * <AUTHOR>
 * @version : ServiceUtils.java, v 0.1 2024/5/27 16:29 renxiao.wu Exp $
 */
public abstract class ServiceUtils {
    /**
     * 转化服务端口
     *
     * @param url  服务完整地址
     * @param port 端口号
     * @return 转化后结果
     */
    public static String transServicePort(String url, String port) {
        if (StringUtils.isBlank(port)) {
            return url;
        }

        if (StringUtils.countMatches(url, ":") <= 1) {
            return url + ":" + port;
        }

        return StringUtils.substringBeforeLast(url, ":") + ":" + port;
    }

    public static String transServiceToWs(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        if (StringUtils.startsWith(url, "ws:")) {
            return url;
        }
        int index = StringUtils.indexOf(url, ":");

        return "ws" + StringUtils.substring(url, index);
    }
}

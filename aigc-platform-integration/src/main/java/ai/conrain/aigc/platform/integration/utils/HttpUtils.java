package ai.conrain.aigc.platform.integration.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

import java.util.function.Consumer;

/**
 * HTTP 工具类
 */
@Slf4j
public class HttpUtils {

    /**
     * 构建 JSON 对象
     *
     * @param consumer JSON构建器
     * @return JSONObject
     */
    public static JSONObject buildJson(Consumer<JSONObject> consumer) {
        JSONObject json = new JSONObject();
        consumer.accept(json);
        return json;
    }

    /**
     * 构建请求头
     *
     * @param consumer 请求头构建器
     * @return HttpHeaders
     */
    public static HttpHeaders buildHeaders(Consumer<HttpHeaders> consumer) {
        HttpHeaders headers = new HttpHeaders();
        consumer.accept(headers);
        return headers;
    }

    /**
     * 构建 HTTP 请求实体
     *
     * @param body 请求体
     * @param headerBuilder 请求头构建器
     * @return HttpEntity
     * @param <T> 请求体类型
     */
    public static <T> HttpEntity<T> buildHttpEntity(T body, Consumer<HttpHeaders> headerBuilder) {
        return new HttpEntity<>(body, buildHeaders(headerBuilder));
    }
}
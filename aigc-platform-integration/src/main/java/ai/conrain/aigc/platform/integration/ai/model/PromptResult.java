/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai.model;

import java.io.Serializable;

import lombok.Data;

/**
 * prompt请求结果
 *
 * <AUTHOR>
 * @version : PromptResult.java, v 0.1 2024/5/9 21:58 renxiao.wu Exp $
 */
@Data
public class PromptResult implements Serializable {
    private static final long serialVersionUID = -690050511127438395L;
    /** 是否成功 */
    private boolean success = false;
    /** 当次请求响应的唯一id */
    private String promptId;
    /** 节点数量 */
    private Integer number;
    /** 节点异常 */
    private String error;
}

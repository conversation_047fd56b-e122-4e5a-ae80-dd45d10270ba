/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai.model;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * 打标状态信息
 *
 * <AUTHOR>
 * @version : LabelStatusInfo.java, v 0.1 2025/8/22 00:04 renxiao.wu Exp $
 */
@Data
public class LabelStatusInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 6906597517440521001L;

    /** 任务ID */
    private Integer taskId;

    /** 任务状态,COMPLETED,FAILED之外，其他都是处理中 */
    private LabelStatusEnum status;

    public enum LabelStatusEnum {
        APPLY,
        QUEUED,
        PROCESSING,
        PENDING,
        COMPLETED,
        FAILED
    }
}

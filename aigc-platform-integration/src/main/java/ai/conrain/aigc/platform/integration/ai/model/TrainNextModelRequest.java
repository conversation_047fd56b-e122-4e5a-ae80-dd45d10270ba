package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TrainNextModelRequest {
    
    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;

    @JsonProperty("data_source")
    @JSONField(name = "data_source")
    private String dataSource;
    
    @JsonProperty("task_config")
    @JSONField(name = "task_config")
    private TaskConfig taskConfig;
    
    @JsonProperty("training_config")
    @JSONField(name = "training_config")
    private TrainingConfig trainingConfig;
    
    @Data
    public static class TaskConfig {
        @JsonProperty("model_scene")
        @JSONField(name = "model_scene")
        private String modelScene;
        
        @JsonProperty("training_way")
        @JSONField(name = "training_way")
        private String trainingWay;
        
        @JsonProperty("model_dir")
        @J<PERSON>NField(name = "model_dir")
        private String modelDir;
    }
    
    @Data
    public static class TrainingConfig {
        private Integer epochs;
        
        @JsonProperty("batch_size")
        @JSONField(name = "batch_size")
        private Integer batchSize;
        
        @JsonProperty("learning_rate")
        @JSONField(name = "learning_rate")
        private Double learningRate;
    }
}
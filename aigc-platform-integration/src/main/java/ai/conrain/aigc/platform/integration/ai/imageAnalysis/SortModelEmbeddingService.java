package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.StyleImageEmbeddingResult;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/mcwagh/aobz210r4k7r3k6z?singleDoc#
 */
@Slf4j
@Service
public class SortModelEmbeddingService {

    @Value("${ai.styleImage.url}")
    private String styleImageUrl;

    @Value("${ai.styleImage.apiKey}")
    private String apiKey;

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 计算风格图片的嵌入向量
     *
     * @param imageUrl  图片URL
     * @param taskId    任务ID
     * @param analysis  图片分析数据
     * @return          风格图片嵌入结果
     */
    public StyleImageEmbeddingResult calcStyleImageEmbeddings(String imageUrl, String taskId, ImageAnalysisCaption analysis) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("X-API-Key", apiKey);
            
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("id", taskId);
            requestBody.put("image_url", imageUrl);
            
            // 将analysis对象转换为JSONObject并放入请求体
            requestBody.put("analysis", analysis);
            
            // 创建HttpEntity
            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);
            
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(styleImageUrl, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析响应结果
                StyleImageEmbeddingResult ret = JSONObject.parseObject(response.getBody(), StyleImageEmbeddingResult.class);
                if (ret.getEmbeddings() != null){
                    return ret;
                }
            }
        } catch (Exception e) {
            log.error("计算风格图片嵌入向量失败：" + e.getMessage(), e);
        }

        throw new RuntimeException("calcStyleImageEmbeddings请求失败");
    }
}
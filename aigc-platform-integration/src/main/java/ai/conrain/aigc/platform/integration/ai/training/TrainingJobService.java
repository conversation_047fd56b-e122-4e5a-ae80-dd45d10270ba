package ai.conrain.aigc.platform.integration.ai.training;

import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationRequest;
import ai.conrain.aigc.platform.integration.ai.model.ModelValidationRequest;
import ai.conrain.aigc.platform.integration.ai.model.ModelValidationResponse;
import ai.conrain.aigc.platform.integration.ai.model.SamplePreparationResponse;
import ai.conrain.aigc.platform.integration.ai.model.TrainNextModelRequest;
import ai.conrain.aigc.platform.integration.ai.model.TrainModelResponse;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/mcwagh/ehe3fm6g9og9weoh
 */
@Service
@Slf4j
public class TrainingJobService {

    @Value("${ai.training.baseUrl}")
    private String baseUrl;

    @Value("${ai.training.apiKey}")
    private String apiKey;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 调用样本准备API
     *
     * @param request 样本准备请求参数
     * @return 样本准备响应结果
     */
    public void prepareSamples(SamplePreparationRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<SamplePreparationRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<JSONObject> response = restTemplate.postForEntity(
                    baseUrl + "/sample_preparation", entity, JSONObject.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("prepareSamples status code:" + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("调用样本准备API时发生错误", e);
            throw new RuntimeException("调用样本准备API时发生错误: " + e.getMessage(), e);
        }
    }

    public SamplePreparationResponse querySamplePreparation(String taskId) {
        if (StringUtils.isBlank(taskId)){
            throw new IllegalArgumentException("taskId is null");
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<Void> entity = new HttpEntity<>(headers);
            ResponseEntity<SamplePreparationResponse> response = restTemplate.exchange(
                    baseUrl + "/result/" + taskId,
                    HttpMethod.GET,
                    entity, 
                    SamplePreparationResponse.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("prepareSamples status code:" + response.getStatusCode());
            }

            return response.getBody();
        } catch (Exception e) {
            log.error("查询样本准备API时发生错误", e);
            throw new RuntimeException("查询样本准备API时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 调用模型性能验证API
     *
     * @param request 模型性能验证请求参数
     * @return 模型性能验证响应结果
     */
    public void validateModel(ModelValidationRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<ModelValidationRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<ModelValidationResponse> response = restTemplate.postForEntity(
                    baseUrl + "/model/inference", entity, ModelValidationResponse.class);

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("validateModel status code:" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用模型性能验证API时发生错误", e);
            throw new RuntimeException("调用模型性能验证API时发生错误: " + e.getMessage(), e);
        }
    }

    public ModelValidationResponse queryValidateModel(String taskId) {
        if (StringUtils.isBlank(taskId)){
            throw new IllegalArgumentException("taskId is null");
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<Void> entity = new HttpEntity<>(headers);
            ResponseEntity<ModelValidationResponse> response = restTemplate.exchange(
                    baseUrl + "/result/" + taskId,
                    HttpMethod.GET,
                    entity,
                    ModelValidationResponse.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("prepareSamples status code:" + response.getStatusCode());
            }

            return response.getBody();
        } catch (Exception e) {
            log.error("查询模型性能验证API时发生错误", e);
            throw new RuntimeException("查询模型性能验证API时发生错误: " + e.getMessage(), e);
        }
    }

    /**
     * 调用训练下一版模型API
     *
     * @param request 训练下一版模型请求参数
     * @return 训练下一版模型响应结果
     */
    public void trainNextModel(TrainNextModelRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<TrainNextModelRequest> entity = new HttpEntity<>(request, headers);

            ResponseEntity<TrainModelResponse> response = restTemplate.postForEntity(
                    baseUrl + "/model/train", entity, TrainModelResponse.class);

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("trainNextModel status code:" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用训练下一版模型API时发生错误", e);
            throw new RuntimeException("调用训练下一版模型API时发生错误: " + e.getMessage(), e);
        }
    }

    public TrainModelResponse queryTrainNextModel(String taskId) {
        if (StringUtils.isBlank(taskId)){
            throw new IllegalArgumentException("taskId is null");
        }
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("api-key", apiKey);

            HttpEntity<Void> entity = new HttpEntity<>(headers);
            ResponseEntity<TrainModelResponse> response = restTemplate.exchange(
                    baseUrl + "/result/" + taskId,
                    HttpMethod.GET,
                    entity,
                    TrainModelResponse.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("prepareSamples status code:" + response.getStatusCode());
            }

            return response.getBody();
        } catch (Exception e) {
            log.error("查询训练模型API时发生错误", e);
            throw new RuntimeException("调用训练模型API时发生错误: " + e.getMessage(), e);
        }
    }
}
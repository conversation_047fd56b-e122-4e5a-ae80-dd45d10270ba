package ai.conrain.aigc.platform.integration.aliyun;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.aliyun.model.ImageOperateOutputModal;
import ai.conrain.aigc.platform.integration.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static ai.conrain.aigc.platform.integration.constant.ConfigConstant.ALIYUN_IMAGE2IMAGE_AUTH;
import static ai.conrain.aigc.platform.integration.constant.ConfigConstant.ALIYUN_IMAGE2IMAGE_QUERY_URL;
import static ai.conrain.aigc.platform.integration.constant.ConfigConstant.ALIYUN_IMAGE2IMAGE_URL;

/**
 * aliyun Erase api
 */
@Slf4j
@Service
public class EraseService {

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private OssService ossService;

    /**
     * 创建图片擦除任务
     *
     * @param originalImage   原始图片
     * @param maskImage       蒙版图片
     * @param foregroundImage 保留区域掩码图片
     * @return TaskId 任务 Id
     */
    public String createEraseTask(String originalImage, String maskImage, String foregroundImage) {
        try {
            // 构建请求体
            JSONObject requestBody = HttpUtils.buildJson(json -> {
                json.put("model", "image-erase-completion");

                // 构建input
                json.put("input", HttpUtils.buildJson(input -> {
                    input.put("image_url", originalImage);
                    input.put("mask_url", maskImage);
                    input.put("foreground_url", foregroundImage);
                }));

                // 构建parameters
                json.put("parameters", HttpUtils.buildJson(params -> {
                    params.put("dilate_flag", true);
                    params.put("add_watermark", false);
                }));
            });

            log.info("[创建图片擦除任务]构建请求参数成功, parameters: {}", JSON.toJSONString(requestBody));

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(
                ALIYUN_IMAGE2IMAGE_URL,
                HttpUtils.buildHttpEntity(requestBody, headers -> {
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.set("Authorization", ALIYUN_IMAGE2IMAGE_AUTH);
                    headers.set("X-DashScope-Async", "enable");
                }),
                String.class
            );

            // 处理响应
            if (response.getBody() == null) {
                log.error("[创建图片擦除任务]EraseService::createEraseTask响应体为空");
                return null;
            }

            // 处理响应
            JSONObject responseJson = JSONObject.parseObject(response.getBody());
            // 检查 output 是否存在且不为 null
            JSONObject output = responseJson.getJSONObject("output");
            if (output == null) {
                log.error("[创建图片擦除任务]EraseService::createEraseTask响应中缺少 output 字段: {}",
                    response.getBody());
                return null;
            }

            // 获取任务 ID
            String taskId = output.getString("task_id");
            if (taskId == null) {
                log.error("[创建图片擦除任务]EraseService::createEraseTask响应中缺少 task_id 字段: {}",
                    response.getBody());
                return null;
            }

            log.info("[创建图片擦除任务]任务创建成功, response={}", response.getBody());
            return taskId;
        } catch (Exception e) {
            log.error("[创建图片擦除任务]EraseService::createEraseTask任务创建失败,错误信息:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 图片擦除任务状态
     *
     * @param taskId 任务 id
     * @return {@link ImageOperateOutputModal} 输出结果
     */
    public ImageOperateOutputModal queryEraseTask(String taskId) {
        try {
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                ALIYUN_IMAGE2IMAGE_QUERY_URL + taskId,
                org.springframework.http.HttpMethod.GET,
                HttpUtils.buildHttpEntity(null, headers -> {
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    headers.set("Authorization", ALIYUN_IMAGE2IMAGE_AUTH);
                }),
                String.class
            );

            // 处理响应
            if (response.getBody() == null) {
                log.error("[查询图片擦除任务]EraseService::queryEraseTask响应体为空");
                return null;
            }

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response.getBody());
            JSONObject outputJson = responseJson.getJSONObject("output");
            if (outputJson == null) {
                log.error("[查询图片擦除任务]EraseService::queryEraseTask响应中缺少 output 字段: {}",
                    response.getBody());
                return null;
            }

            // 检查字段
            if (StringUtils.isBlank(outputJson.getString("task_status"))) {
                log.error("[查询图片擦除任务]EraseService::queryEraseTask响应中缺少 task_status 字段: {}",
                        response.getBody());
                return null;
            }

            // 将 JSON 转换为 ImageOperateOutputModal 对象
            ImageOperateOutputModal imageOperateOutputModal = JSON.toJavaObject(outputJson,
                ImageOperateOutputModal.class);

            // 如果输出图片 URL 不为空，则从 OSS 中获取流并上传
            if (imageOperateOutputModal != null && StringUtils.isNotBlank(
                imageOperateOutputModal.getOutputImageUrl())) {
                imageOperateOutputModal.setOutputImageUrl(
                    ossService.fetchStreamAndUpload(imageOperateOutputModal.getOutputImageUrl()));
            }

            // 返回处理后的结果
            return imageOperateOutputModal;
        } catch (Exception e) {
            log.error("[查询图片擦除任务]EraseService::queryEraseTask任务查询失败,taskId:{},错误信息:{}", taskId,
                e.getMessage());
            return null;
        }
    }
}

package ai.conrain.aigc.platform.integration.aliyun.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

/**
 * 图片处理输出模型对象
 */
@Data
public class ImageOperateOutputModal {
    @JSONField(name = "task_id")
    private String taskId;

    @JSONField(name = "task_status")
    private String taskStatus;

    @JSONField(name = "submit_time")
    private String submitTime;

    @JSONField(name = "scheduled_time")
    private String scheduledTime;

    @JSONField(name = "end_time")
    private String endTime;

    @JSONField(name = "image_url")
    private String imageUrl;

    @JSONField(name = "output_image_url")
    private String outputImageUrl;
}

/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import lombok.Data;

/**
 * 打标任务申请请求
 *
 * <AUTHOR>
 * @version : LabelApplyRequest.java, v 0.1 2025/8/22 10:56 renxiao.wu Exp $
 */
@Data
public class LabelApplyRequest implements Serializable {
    private static final long serialVersionUID = 7141373497692401424L;
    /** 任务类型 */
    private LabelTypeEnum type;
    /** 外部业务号 */
    private String outBizNo;

    /** 目标目录 */
    private String targetDir;

    /** 扩展信息 */
    private JSONObject extInfo;

    /** 是否多色 */
    private boolean multiColors;

    /** 是否使用镜头描述 */
    private boolean useShot;

    /** 服装男女款式类别 */
    private String genderType;

    /** 服装款式，上装：upper garment，下装：lower garment，其他：outfit */
    private String clothType;

    /** 年龄范围 */
    private String ageRange;

    /** 训练重复次数 */
    private Integer repeatTimes;

    /** 图片大小 */
    private Integer imageSize;

    /** 抠图类型 */
    private String cutoutType;

    public enum LabelTypeEnum {
        CLOTH_LABEL,
        MODEL_LABEL,
        SCENE_LABEL
    }
}

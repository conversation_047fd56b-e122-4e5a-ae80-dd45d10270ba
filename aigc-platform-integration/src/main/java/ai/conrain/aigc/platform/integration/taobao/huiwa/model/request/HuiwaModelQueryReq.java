package ai.conrain.aigc.platform.integration.taobao.huiwa.model.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HuiwaModelQueryReq {

    /**
     * 模型类型，1:模特，2:商品，3:参考图 （默认：参考图）
     */
    private Long type = 3L;

    /**
     * 	模型来源，0:系统预置，1:用户私有（默认：用户私有）
     */
    private Long source = 1L;

    /**
     * 模型id列表
     */
    private List<Long> modelIds;

    /**
     * 当前页
     */
    private Long pageNow;

    /**
     * 分页条数
     */
    private Long pageSize;
}

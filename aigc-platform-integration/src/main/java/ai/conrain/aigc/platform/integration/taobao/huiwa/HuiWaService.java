package ai.conrain.aigc.platform.integration.taobao.huiwa;

import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaModelQueryReq;
import ai.conrain.aigc.platform.integration.taobao.huiwa.model.request.HuiwaTaskImageCreativeRequest;
import com.taobao.api.response.LianfanHuiwaModelGetResponse;
import com.taobao.api.response.LianfanHuiwaTaskImageGetResponse;
import java.util.List;

public interface HuiWaService {

    /**
     * 上传参考图图片
     *
     * @param referenceImage 图片地址
     * @return modelID
     */
    Long uploadPreferenceImage(String referenceImage);

    /**
     * 查询模型状态
     *
     * @param huiwaModelQueryReq 请求入参
     * @return 模型信息
     */
    List<LianfanHuiwaModelGetResponse.ModelDTO> queryModelStatus(HuiwaModelQueryReq huiwaModelQueryReq);

    /**
     * 创建出图任务
     *
     * @param request 请求入参
     * @return 响应数据
     */
    Long createImageTask(HuiwaTaskImageCreativeRequest request);

    /**
     * 查询任务状态
     *
     * @param taskId 任务 id
     * @return 任务信息
     */
    LianfanHuiwaTaskImageGetResponse.BaseImageTaskDTO queryTaskStatus(Integer taskId);

}

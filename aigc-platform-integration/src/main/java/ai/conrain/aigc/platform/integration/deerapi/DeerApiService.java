package ai.conrain.aigc.platform.integration.deerapi;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static ai.conrain.aigc.platform.integration.gpt.AIModel.ApiConfig;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.Base64ImageInfo;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.GenerationConfig;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.GptResponse;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.Status;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createImageContent;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createImageContentForGemini;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createTextContent;
import static ai.conrain.aigc.platform.integration.gpt.AIModel.createTextContentForGemini;

@Slf4j
@Service
public class DeerApiService {

    private static final ApiConfig api = new ApiConfig(null, "sk-oxJh2MZywKKIuLKs6xWDeKtQL0jYnb0PQ6JdePpuf2Ofbanf",
        "https://api2.deerapi.com/v1/chat/completions");
    private static final ApiConfig apiGenerate = new ApiConfig(null,
        "sk-oxJh2MZywKKIuLKs6xWDeKtQL0jYnb0PQ6JdePpuf2Ofbanf", "https://api2.deerapi.com/v1/models/%s:generateContent");
    private static final ApiConfig doubaoGenerate = new ApiConfig(null,
        "sk-oxJh2MZywKKIuLKs6xWDeKtQL0jYnb0PQ6JdePpuf2Ofbanf", "https://api.deerapi.com/v1/images/generations");

    @Autowired
    private RestTemplate restTemplate3min;

    /**
     * 调用大模型
     *
     * @param model   模型类型
     * @param prompt  prompt
     * @param imgUrls 图片列表
     * @return 返回结果
     */
    public GptResponse call(DeerModelEnum model, String prompt, List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        if (model.getSourceType() == SourceTypeEnum.GOOGLE_IMAGE) {
            return callGenerateContent(model, prompt, imgUrls);
        }

        if (model.getSourceType() == SourceTypeEnum.DOUBAO) {
            return callDoubaoContent(model, prompt, imgUrls);
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", api.getApiKey()));

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContent(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContent(url));
            }
        }

        message.put("content", content);
        messages.add(message);
        payload.put("messages", messages);

        try {
            log.info("【请求GPT】，payload={}", payload);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);
            ResponseEntity<String> response = restTemplate3min.exchange(api.getEndpoint(), HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求GPT】，result={}", result);

            String text = null;
            if (result != null) {
                text = result.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            }

            return new GptResponse(text, Status.OK);
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

    /**
     * 调用大模型
     *
     * @param model   模型类型
     * @param prompt  prompt
     * @param imgUrls 图片列表
     * @return 返回结果
     */
    public GptResponse callGenerateContent(DeerModelEnum model, String prompt, List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", apiGenerate.getApiKey()));

        String endpoint = String.format(apiGenerate.getEndpoint(), model.getCode());

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("contents", model.getCode());

        JSONArray messages = new JSONArray();
        JSONObject message = new JSONObject();
        message.put("role", "user");

        JSONArray content = new JSONArray();
        content.add(createTextContentForGemini(prompt));

        if (!CollectionUtils.isEmpty(imgUrls)) {
            for (String url : imgUrls) {
                content.add(createImageContentForGemini(url));
            }
        }

        message.put("parts", content);
        messages.add(message);
        payload.put("contents", messages);
        payload.put("generationConfig", GenerationConfig);

        try {
            log.info("【请求内容生成】，payload={}", prompt);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);

            ResponseEntity<String> response = restTemplate3min.exchange(endpoint, HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求内容生成】，result={}", result);

            AtomicReference<String> text = new AtomicReference<>();
            List<Base64ImageInfo> imageBase64List = new ArrayList<>();
            if (result != null) {
                JSONArray parts = result.getJSONArray("candidates").getJSONObject(0).getJSONObject("content")
                    .getJSONArray("parts");
                parts.forEach(part -> {
                    JSONObject json = (JSONObject)part;
                    if (json.containsKey("text")) {
                        text.set(json.getString("text"));
                    } else if (json.containsKey("inlineData")) {
                        imageBase64List.add(json.getJSONObject("inlineData").toJavaObject(Base64ImageInfo.class));
                    }
                });
            }

            return new GptResponse(text.get(), Status.OK, imageBase64List);
        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

    /**
     * 调用大模型
     *
     * @param model   模型类型
     * @param prompt  prompt
     * @param imgUrls 图片列表
     * @return 返回结果
     */
    public GptResponse callDoubaoContent(DeerModelEnum model, String prompt, List<String> imgUrls) {

        if (StringUtils.isBlank(prompt) || model == null) {
            throw new RuntimeException("prompt is empty");
        }

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", String.format("Bearer %s", doubaoGenerate.getApiKey()));

        String endpoint = doubaoGenerate.getEndpoint();

        // 构建消息体
        JSONObject payload = new JSONObject();
        payload.put("model", model.getCode());
        payload.put("prompt", prompt);
        payload.put("image", imgUrls);
        payload.put("response_format", "url"); //url和b64_json
        payload.put("size", "4K");
        payload.put("watermark", false);
        payload.put("n", CollectionUtils.size(imgUrls));

        try {
            log.info("【请求豆包生成】，payload={}，imgUrls={}", prompt, imgUrls);
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);

            ResponseEntity<String> response = restTemplate3min.exchange(endpoint, HttpMethod.POST, entity,
                String.class);

            JSONObject result = JSON.parseObject(response.getBody());

            log.info("【请求豆包生成】，result={}", result);

            AtomicReference<String> text = new AtomicReference<>();
            List<Base64ImageInfo> imageBase64List = new ArrayList<>();
            List<String> imageUrlList = new ArrayList<>();
            if (result != null) {
                JSONArray parts = result.getJSONArray("data");
                parts.forEach(part -> {
                    JSONObject json = (JSONObject)part;
                    if (json.containsKey("url")) {
                        imageUrlList.add(json.getString("url"));
                    } else if (json.containsKey("b64_json")) {
                        Base64ImageInfo base64ImageInfo = new Base64ImageInfo("image/png", json.getString("b64_json"));
                        imageBase64List.add(base64ImageInfo);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(imageBase64List)) {
                return new GptResponse(text.get(), Status.OK, imageBase64List);
            }

            return new GptResponse(text.get(), imageUrlList);

        } catch (Exception e) {
            log.error("Failed to make the request.", e);
            String errorMsg = "Failed to make the request. Error: " + e.getMessage();
            return new GptResponse(errorMsg, Status.ERROR);
        }
    }

}
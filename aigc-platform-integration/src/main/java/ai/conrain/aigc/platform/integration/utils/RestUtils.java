/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.utils;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 *
 * <AUTHOR>
 * @version : RestUtils.java, v 0.1 2025/8/22 11:01 renxiao.wu Exp $
 */
@Slf4j
public abstract class RestUtils {

    /**
     * 调用json接口
     *
     * @param serverUrl    服务url
     * @param params       参数
     * @param method       请求方法
     * @param restTemplate restTemplate
     * @return json结果
     */
    public static JSONObject callJson(String serverUrl, Object params, HttpMethod method, RestTemplate restTemplate) {
        return callJson(serverUrl, params, method, null, restTemplate);
    }

    /**
     * 调用json接口
     *
     * @param serverUrl    服务url
     * @param params       参数
     * @param method       请求方法
     * @param token        令牌
     * @param restTemplate restTemplate
     * @return json结果
     */
    public static JSONObject callJson(String serverUrl, Object params, HttpMethod method, String token,
                                      RestTemplate restTemplate) {

        HttpHeaders headers = buildTraceHeaders(token);
        HttpEntity<?> entity = new HttpEntity<>(params, headers);
        ResponseEntity<String> httpRes = restTemplate.exchange(serverUrl, method, entity, String.class);

        if (httpRes.getStatusCode() != HttpStatus.OK) {
            log.error("调用{}接口异常,code={}", serverUrl, httpRes.getStatusCode());
            return null;
        }

        String content = httpRes.getBody();
        if (StringUtils.isBlank(content)) {
            log.error("调用{}接口失败,返回的结果为空", serverUrl);
            return null;
        }
        log.info("调用{}接口成功，结果={}", serverUrl, content);

        try {
            return JSONObject.parseObject(content);
        } catch (Exception e) {
            log.error("调用" + serverUrl + "接口失败,解析结果失败,content=" + content, e);
            return null;
        }
    }

    /**
     * 构建带trace的请求头
     *
     * @return 请求头
     */
    public static HttpHeaders buildTraceHeaders() {
        return buildTraceHeaders(null);
    }

    /**
     * 构建带trace和token的请求头
     *
     * @param token 令牌
     * @return 请求头
     */
    public static HttpHeaders buildTraceHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        if (StringUtils.isNotBlank(token)) {
            headers.add("Authorization", token);
        }
        headers.add("traceId", MDC.get("traceId"));
        return headers;
    }
}

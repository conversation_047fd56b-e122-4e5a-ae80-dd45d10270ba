package ai.conrain.aigc.platform.integration.taobao.huiwa.model.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 绘蛙出图任务请求入参
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HuiwaTaskImageCreativeRequest {

    /**
     * 服装图片
     */
    private String clothImage;

    /**
     * 服装类型（1表示上装,2表示下装,3表示连衣裙）
     */
    private String clothType;

    /**
     * 参考图ModelId（此处的参考图应该为场景中的原始图片）
     */
    private Long modelId;

    /**
     * 图片质量，默认为质量最高<br>
     * 0：速度最快（20～40秒）<br>
     * 1：表示均衡（1～2分钟）<br>
     * 2：表示质量更优（4～5分钟）
     */
    private Long taskQuality = 2L;
}

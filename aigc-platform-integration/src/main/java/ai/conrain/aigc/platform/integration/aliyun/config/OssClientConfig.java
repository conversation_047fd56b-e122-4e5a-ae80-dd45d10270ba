package ai.conrain.aigc.platform.integration.aliyun.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class OssClientConfig {

    @Value("${aliyun.oss.endpoint}")
    private String ossEndpoint;

    //ram oss用户的ak/sk
    @Value("${aliyun.oss.accessKeyId}")
    String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    String accessKeySecret;

    @Primary
    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(ossEndpoint, accessKeyId, accessKeySecret);
    }

    @Bean("ossClientWithDataLabel")
    public OSS ossClientWithDataLabel() {
        return new OSSClientBuilder().build("oss-cn-zhangjiakou.aliyuncs.com", "LTAI5t8fQD5gTRHFxMTwyomc", "******************************");
    }
}

package ai.conrain.aigc.platform.integration.aliyun;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.params.SetParams;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <a href="https://github.com/tair-opensource/alibabacloud-tairjedis-sdk/blob/master/README-CN.md">TairJedis</a>
 * <a href="https://redis.io/docs/clients/java/">redis java guide</a>
 */
@Slf4j
@Service
public class TairService {

    @Autowired
    private JedisPool jedisPool;

    private static final String LOCK_SUCCESS = "OK";

    private static final String LOCK_REQUEST_ID = "_default_request_id_";

    private static final String ORDER_NO_KEY = "order_no_counter";
    private static final long MAX_ORDER_NO_SEQ = 99999999L;

    private static final String MOBILE_NO_KEY = "mobile_counter";
    public static final long MAX_MOBILE_SEQ = 9999L;

    // 获取锁
    public boolean acquireLock(String lockKey, String requestId, int expireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            SetParams setParams = new SetParams();
            setParams.nx();
            setParams.px(expireTime); // expireTime 以毫秒为单位
            String result = jedis.set(lockKey, requestId, setParams);
            return LOCK_SUCCESS.equals(result);
        } catch (Exception e) {
            log.error("acquireLock exception", e);
            return false;
        }
    }

    // 释放锁
    public boolean releaseLock(String lockKey, String requestId) {
        try (Jedis jedis = jedisPool.getResource()) {
            String value = jedis.get(lockKey);
            if (requestId.equals(value)) {
                jedis.del(lockKey);
                return true;
            }
        } catch (Exception e) {
            log.error("releaseLock exception", e);
        }
        return false;
    }

    // 获取独占锁
    public boolean acquireLock(String lockKey, int expireTime) {
        try (Jedis jedis = jedisPool.getResource()) {
            SetParams setParams = new SetParams();
            setParams.nx();
            setParams.px(expireTime); // expireTime 以毫秒为单位
            String result = jedis.set(lockKey, LOCK_REQUEST_ID, setParams);
            return LOCK_SUCCESS.equals(result);
        } catch (Exception e) {
            log.error("acquireLock exception", e);
            return false;
        }
    }

    // 释放独占锁
    public boolean releaseLock(String lockKey) {
        try (Jedis jedis = jedisPool.getResource()) {
            String value = jedis.get(lockKey);
            if (LOCK_REQUEST_ID.equals(value)) {
                jedis.del(lockKey);
                return true;
            }
        } catch (Exception e) {
            log.error("releaseLock exception", e);
        }
        return false;
    }

    public String getString(String key) {

        if (StringUtils.isEmpty(key)) {
            return null;
        }

        //try-with，自动close释放连接
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);

        } catch (Exception e) {
            log.error("jedis exception", e);
            return null;
        }
    }

    public <T> T getObject(String key, Class<T> clz) {
        if (clz == null) {
            throw new NullPointerException("clz is null");
        }
        String value = getString(key);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return JSONObject.parseObject(value, clz);
        } catch (Exception e) {
            log.error("parse json object failed,key:" + key + ",value in tair:" + value, e);
            return null;
        }
    }

    public boolean setString(String key, String value, int expireSecs) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value) || expireSecs <= 0) {
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(key, expireSecs, value);
            return true;
        } catch (Exception e) {
            log.error("jedis exception", e);
            return false;
        }
    }

    public boolean setObject(String key, Object obj, int expireSecs) {
        if (obj == null) {
            throw new NullPointerException("obj is null");
        }

        return setString(key, JSONObject.toJSONString(obj), expireSecs);
    }

    /**
     * 向 Redis List 中添加对象
     *
     * @param key       List 键
     * @param object    要添加的对象
     * @param expireSecs 过期时间（秒），如果为0则不过期
     * @return 操作是否成功
     */
    public boolean addToList(String key, Object object, int expireSecs) {
        if (StringUtils.isEmpty(key) || object == null) {
            log.warn("Invalid input parameters: key={}, object={}", key, object);
            return false;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            jedis.rpush(key, JSONObject.toJSONString(object));
            if (expireSecs > 0) {
                jedis.expire(key, expireSecs);
            }
            return true;
        } catch (Exception e) {
            log.error("Failed to add object to list in Redis: key={}, object={}, expireSecs={}", key, object, expireSecs, e);
            return false;
        }
    }

    /**
     * 从 Redis List 中获取所有对象
     *
     * @param key List 键
     * @param clazz 对象类型
     * @return 获取的对象列表
     */
    public <T extends Serializable> List<T> getAllFromList(String key, Class<T> clazz) {
        if (StringUtils.isEmpty(key) || clazz == null) {
            log.warn("Invalid input parameters: key={}, clazz={}", key, clazz);
            return null;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            // 获取所有对象
            List<String> objectList = jedis.lrange(key, 0, -1);

            if (objectList.isEmpty()) {
                return null;
            }

            // 对象参数转换
            return objectList.stream()
                    .map(object -> {
                        try {
                            return JSONObject.parseObject(object, clazz);
                        } catch (Exception e) {
                            log.error("Failed to deserialize object from list in Redis: key={}, serializedObject={}", key, object, e);
                            return null;
                        }
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get objects from list in Redis: key={}", key, e);
            return null;
        }
    }

    /**
     * 根据key清理缓存
     *
     * @param key 缓存key
     */
    public void clear(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.del(key);
        } catch (Exception e) {
            log.error("jedis exception", e);
        }
    }

    /**
     * 重新设置过期时间
     *
     * @param key        缓存key
     * @param expireSecs 过期时间，秒
     */
    public void expire(String key, int expireSecs) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.expire(key, expireSecs);
        } catch (Exception e) {
            log.error("jedis exception", e);
        }
    }

    /**
     * 获取订单seq
     * 自增序号长度不超过10位（即9999999999）超过则重置为0
     */
    public long getNextOrderNoSeq() {
        try (Jedis jedis = jedisPool.getResource()) {
            long count = jedis.incr(ORDER_NO_KEY);
            if (count > MAX_ORDER_NO_SEQ) {
                jedis.set(ORDER_NO_KEY, "0");
                count = jedis.incr(ORDER_NO_KEY);
            }
            return count;
        }
    }

    /**
     * 获取测试手机号
     */
    public String getNextTestMobile() {
        try (Jedis jedis = jedisPool.getResource()) {
            //1~9999
            long count = jedis.incr(MOBILE_NO_KEY);
            if (count > MAX_MOBILE_SEQ) {
                jedis.set(MOBILE_NO_KEY, "0");
                count = jedis.incr(MOBILE_NO_KEY);
            }

            // 4位随机数
            Random random = new Random(System.currentTimeMillis());
            String randomStr = String.format("%04d", random.nextInt(10000));

            //3位（288） + 4位随机 + 4位自增序号
            return "288" + randomStr + String.format("%04d", count);
        }
    }


    public void enqueue(String queueName, String task) {
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.rpush(queueName, task);
        } catch (Exception e) {
            log.error("jedis enqueue exception", e);
        }
    }

    public List<String> dequeue(String queueName, int count) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.lrange(queueName, 0, count - 1);
        } catch (Exception e) {
            log.error("jedis dequeue exception", e);
            return null;
        }
    }

    public void deleteFirstNElements(String queueName, int count) {
        try (Jedis jedis = jedisPool.getResource()) {
            String res = jedis.ltrim(queueName, count, -1);
            log.info("deleteFirstNElements res = {}", res);
        } catch (Exception e) {
            log.error("jedis deleteFirstNElements exception", e);
        }
    }

    /**
     * 原子性地递增计数器
     */
    public Long incr(String key, int expireSecs) {
        try (Jedis jedis = jedisPool.getResource()) {
            // Lua脚本确保incr和expire操作的原子性
            String script = "local value = redis.call('incr', KEYS[1])\n" +
                          "redis.call('expire', KEYS[1], ARGV[1])\n" +
                          "return value";
            
            Object result = jedis.eval(
                script,
                Collections.singletonList(key),  // KEYS列表
                Collections.singletonList(String.valueOf(expireSecs))  // ARGV列表
            );
            
            return (Long) result;
        } catch (Exception e) {
            log.error("Failed to increment counter atomically: key={}, expireSecs={}", key, expireSecs, e);
            return null;
        }
    }
}
package ai.conrain.aigc.platform.integration.kling;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * https://piapi.ai/docs/kling-api/create-task
 */
@Data
public class KlingTaskParams {

    //5/10
    private Integer duration;
    /**
     * End frame of the video. No larger than 10MB, and each side must be greater than 300
     * pixels.Need to be used with `image_url`.
     */
    @JSONField(name = "image_tail_url")
    private String imageTailUrl;
    /**
     * Only required in image-to-video task. Initial frame of the video.No larger than 10MB, and
     * each side must be greater than 300 pixels.
     */
    @JSONField(name = "image_url")
    private String imageUrl;

    /**
     * std
     * pro
     */
    private String mode = "pro";

    private String prompt;

    /**
     * the model version
     * 1.5
     * 1.6
     */
    private String version = "1.6";
}
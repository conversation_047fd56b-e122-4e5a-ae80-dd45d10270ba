package ai.conrain.aigc.platform.integration.wechat;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.wechat.model.QCSVoiceTokenVO;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenRequest;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TencentCloudService {

    @Value("${tencent.cloud.secretId}")
    private String secId;

    @Value("${tencent.cloud.secretKey}")
    private String secKey;

    @Value("${tencent.cloud.region}")
    private String region;

    @Value("${tencent.cloud.appId}")
    private String appId;

    /**
     * 获取临时访问凭证
     * https://cloud.tencent.com/document/product/1312/48195
     */
    public QCSVoiceTokenVO getFederationToken() {
        try {
            StsClient client = getStsClient();

            // 实例化一个请求对象,每个接口都会对应一个request对象
            GetFederationTokenRequest req = new GetFederationTokenRequest();
            req.setName("agri-f2f");
            req.setPolicy("{\"version\":\"2.0\",\"statement\":[{\"effect\":\"allow\",\"action\":[\"name/asr:*\"],\"resource\":\"*\"}]}");
            req.setDurationSeconds(3600L);

            // 返回的resp是一个GetFederationTokenResponse的实例，与请求对象对应
            GetFederationTokenResponse res = client.GetFederationToken(req);
            log.info("腾讯云res:{}", JSONObject.toJSONString(res));

            if (res == null || res.getCredentials() == null || res.getCredentials().getTmpSecretId() == null
                    || res.getCredentials().getTmpSecretKey() == null || res.getCredentials().getToken() == null) {
                log.error("腾讯云返回获取sts token异常");
                return null;
            }

            QCSVoiceTokenVO qcsVoiceTokenVO = new QCSVoiceTokenVO();
            qcsVoiceTokenVO.setToken(res.getCredentials().getToken());
            qcsVoiceTokenVO.setTmpSecretId(res.getCredentials().getTmpSecretId());
            qcsVoiceTokenVO.setTmpSecretKey(res.getCredentials().getTmpSecretKey());
            qcsVoiceTokenVO.setExpiration(res.getExpiration());
            qcsVoiceTokenVO.setExpiredTime(res.getExpiredTime());
            qcsVoiceTokenVO.setAppId(Long.valueOf(appId));

            return qcsVoiceTokenVO;

        } catch (TencentCloudSDKException e) {
            log.error("获取临时密钥异常", e);
            return null;
        }
    }

    @NotNull
    private StsClient getStsClient() {
        Credential cred = new Credential(secId, secKey);
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("sts.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);

        // 实例化要请求产品的client对象,clientProfile是可选的
        return new StsClient(cred, region, clientProfile);
    }
}

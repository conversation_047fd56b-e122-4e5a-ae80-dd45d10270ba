package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.Map;

@Data
public class SamplePreparationResponse {

    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;

    //running|success|failed|error
    private String status;
    
    private String message;
    
    @JsonProperty("distribution_total")
    @JSONField(name = "distribution_total")
    private DistributionTotal distributionTotal;
    
    @JsonProperty("distribution_sample")
    @JSONField(name = "distribution_sample")
    private DistributionSample distributionSample;

    @JsonProperty("rule_filter_number")
    @JSONField(name = "rule_filter_number")
    private Map<String, Integer> ruleFilter;
    
    @JsonProperty("processing_time")
    @JSONField(name = "processing_time")
    private String processingTime;
    
    @JsonProperty("output_file")
    @JSONField(name = "output_file")
    private String outputFile;

    @Data
    public static class DistributionTotal {
        @JsonProperty("total_processed")
        @JSONField(name = "total_processed")
        private Integer totalProcessed;
        
        @JsonProperty("high_divergence")
        @JSONField(name = "high_divergence")
        private Integer highDivergence;
        
        @JsonProperty("low_divergence")
        @JSONField(name = "low_divergence")
        private Integer lowDivergence;
        
        @JsonProperty("middle_divergence")
        @JSONField(name = "middle_divergence")
        private Integer middleDivergence;
    }

    @Data
    public static class DistributionSample {
        @JsonProperty("selected_samples")
        @JSONField(name = "selected_samples")
        private Integer selectedSamples;
        
        @JsonProperty("high_divergence")
        @JSONField(name = "high_divergence")
        private Integer highDivergence;
        
        @JsonProperty("low_divergence")
        @JSONField(name = "low_divergence")
        private Integer lowDivergence;
        
        @JsonProperty("middle_divergence")
        @JSONField(name = "middle_divergence")
        private Integer middleDivergence;
    }
}
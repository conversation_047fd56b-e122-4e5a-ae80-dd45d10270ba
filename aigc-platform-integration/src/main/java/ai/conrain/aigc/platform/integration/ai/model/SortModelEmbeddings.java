package ai.conrain.aigc.platform.integration.ai.model;

import lombok.Data;

@Data
public class SortModelEmbeddings {
    private float[] backgroundImageEmbedding;
    private float[] backgroundTextEmbedding;
    private float[] expressionImageEmbedding;
    private float[] expressionTextEmbedding;
    private float[] matTextEmbedding;
    private float[] optImageEmbedding;
    private float[] poseImageEmbedding;
    private float[] poseTextEmbedding;
}

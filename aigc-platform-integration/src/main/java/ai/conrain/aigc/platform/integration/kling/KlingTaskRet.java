package ai.conrain.aigc.platform.integration.kling;

import lombok.Data;

/**
 * {"status":"completed","taskId":"6ebb5490-ce48-489b-af64-e33bdcf18624","taskType":"video_generation","videoUrl":"https://storage.midjourneyapi.xyz/videos/211658729.mp4"}
 */
@Data
public class KlingTaskRet {
    private String taskId;
    private String taskType;
    /**
     * completed/processing/pending/failed/staged
     */
    private KlingTaskStatusEnum status;
    private String outVideoUrl;
    private String ossVideoUrl;
}

package ai.conrain.aigc.platform.integration.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用的 Bash 命令执行工具类
 * 使用 ProcessBuilder 提供更安全、灵活的命令执行方式
 */
@Slf4j(topic = "[ShellExecutor]")
public class ShellExecutor {

    /**
     * 执行指定的 Bash 命令并返回结果
     *
     * @param command 要执行的命令（多条命令可用 && 或 | 连接）
     * @return CommandResult 包含退出码、标准输出和错误输出的对象
     * @throws IOException          如果发生 I/O 错误
     * @throws InterruptedException 如果执行过程被中断
     */
    public static CommandResult executeCommand(String command) throws IOException, InterruptedException {
        return executeCommand(command, null, null, null, 0);
    }

    /**
     * 执行指定的 Bash 命令并返回结果
     *
     * @param command   要执行的命令（多条命令可用 && 或 | 连接）
     * @param workDir   命令执行的工作目录，null 表示使用当前工作目录
     * @param timeoutMs 超时时间（毫秒），0 表示无超时
     * @return CommandResult 包含退出码、标准输出和错误输出的对象
     * @throws IOException          如果发生 I/O 错误
     * @throws InterruptedException 如果执行过程被中断
     */
    public static CommandResult executeCommand(String command, File workDir, long timeoutMs) throws IOException, InterruptedException {
        return executeCommand(command, workDir, null, null, timeoutMs);
    }

    /**
     * 执行指定的 Bash 命令并返回结果
     *
     * @param command     要执行的命令（多条命令可用 && 或 | 连接）
     * @param workDir     命令执行的工作目录，null 表示使用当前工作目录
     * @param envVars     要添加或修改的环境变量，null 表示不修改
     * @param timeoutMs   超时时间（毫秒），0 表示无超时
     * @return CommandResult 包含退出码、标准输出和错误输出的对象
     * @throws IOException          如果发生 I/O 错误
     * @throws InterruptedException 如果执行过程被中断
     */
    public static CommandResult executeCommand(String command, File workDir, Map<String, String> envVars, long timeoutMs) throws IOException, InterruptedException {
        return executeCommand(command, workDir, envVars, null, timeoutMs);
    }

    /**
     * 执行指定的 Bash 命令并返回结果（完整参数）
     *
     * @param command     要执行的命令（多条命令可用 && 或 | 连接）
     * @param workDir     命令执行的工作目录，null 表示使用当前工作目录
     * @param envVars     要添加或修改的环境变量，null 表示不修改
     * @param input       要传递给命令的输入内容，null 表示无输入
     * @param timeoutMs   超时时间（毫秒），0 表示无超时
     * @return CommandResult 包含退出码、标准输出和错误输出的对象
     * @throws IOException          如果发生 I/O 错误
     * @throws InterruptedException 如果执行过程被中断
     */
    public static CommandResult executeCommand(String command, File workDir, Map<String, String> envVars, String input, long timeoutMs) throws IOException, InterruptedException {
        log.debug("准备执行命令: {}", command); // 记录执行的命令
        log.debug("工作目录: {}, 超时设置: {}ms", workDir, timeoutMs);
        
        // 输出当前工作目录用于调试
        if (workDir == null) {
            String currentDir = System.getProperty("user.dir");
            log.info("使用默认工作目录: {}", currentDir);
        }

        // 使用 bash -c 来执行命令，支持管道和重定向等shell特性
        ProcessBuilder processBuilder = new ProcessBuilder("bash", "-c", command);

        // 设置工作目录
        if (!ObjectUtils.isEmpty(workDir)) {
            processBuilder.directory(workDir);
            log.debug("设置工作目录: {}", workDir.getAbsolutePath());
        }

        // 设置环境变量
        if (!CollectionUtils.isEmpty(envVars)) {
            Map<String, String> environment = processBuilder.environment();
            environment.putAll(envVars);
            log.debug("添加环境变量: {}", envVars);
        }

        // 合并错误流和输出流，简化处理
        processBuilder.redirectErrorStream(true);

        long startTime = System.currentTimeMillis();
        Process process = processBuilder.start();
        log.debug("进程已启动，PID: {}", process.pid());

        // 如果有输入内容，写入进程
        if (input != null) {
            log.debug("向进程输入数据，长度: {}", input.length());
            try (OutputStream os = process.getOutputStream();
                 BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(os))) {
                writer.write(input);
                writer.flush();
            }
        }

        // 读取命令输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.trace("进程输出: {}", line); // 详细跟踪输出
            }
        }

        // 等待进程完成，支持超时
        boolean finished;
        if (timeoutMs > 0) {
            log.debug("设置命令执行超时: {}ms", timeoutMs);
            finished = process.waitFor(timeoutMs, TimeUnit.MILLISECONDS);
            if (!finished) {
                log.warn("命令执行超时，强制终止进程");
                process.destroyForcibly(); // 超时后强制终止进程
                throw new InterruptedException("Command timed out after " + timeoutMs + " ms");
            }
        } else {
            finished = true;
            process.waitFor();
        }

        long executionTime = System.currentTimeMillis() - startTime;
        int exitCode = process.exitValue();

        log.info("命令执行完成, 退出码: {}, 执行时间: {}ms", exitCode, executionTime);
        log.debug("命令输出: {}", output.toString().trim());

        return new CommandResult(exitCode, output.toString());
    }

    /**
     * 命令执行结果封装类
     */
    public static class CommandResult {
        private final int exitCode;
        private final String output;

        public CommandResult(int exitCode, String output) {
            this.exitCode = exitCode;
            this.output = output;
        }

        public int getExitCode() {
            return exitCode;
        }

        public String getOutput() {
            return output;
        }

        public boolean isSuccess() {
            return exitCode == 0;
        }

        @Override
        public String toString() {
            return "Exit code: " + exitCode + "\nOutput: " + output;
        }
    }
}
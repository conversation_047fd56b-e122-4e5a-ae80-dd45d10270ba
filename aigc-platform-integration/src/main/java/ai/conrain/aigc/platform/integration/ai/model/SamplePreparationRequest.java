package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class SamplePreparationRequest {

    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;

    @JsonProperty("model_scene")
    @JSONField(name = "model_scene")
    private String modelScene;
    
    @JsonProperty("data_source")
    @JSONField(name = "data_source")
    private String dataSource;
    
    @JsonProperty("model_dir")
    @JSONField(name = "model_dir")
    private String modelDir;
    
    private Config config;

    @Data
    public static class Config {
        @JsonProperty("target_samples")
        @JSONField(name = "target_samples")
        private Integer targetSamples;

        @JsonProperty("rule_filter")
        @JSONField(name = "rule_filter")
        private RuleFilter ruleFilter;

        //cosine_dist|voting
        @JsonProperty("sampling_strategy")
        @JSONField(name = "sampling_strategy")
        private String samplingStrategy;

        //sampling_strategy='voting'的时候才传
        @JsonProperty("voting_labels")
        @JSONField(name = "voting_labels")
        private List<String> votingLabels;
    }

    @Data
    public static class RuleFilter {
        @JsonProperty("remove_multi_person")
        @JSONField(name = "remove_multi_person")
        private Boolean removeMultiPerson;

        @JsonProperty("remove_person_with_height_ratio")
        @JSONField(name = "remove_person_with_height_ratio")
        private Boolean removePersonWithHeightRatio;
    }
}
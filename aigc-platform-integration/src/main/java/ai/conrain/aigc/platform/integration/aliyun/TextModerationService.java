package ai.conrain.aigc.platform.integration.aliyun;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.TextModerationPlusRequest;
import com.aliyun.green20220302.models.TextModerationPlusResponse;
import com.aliyun.green20220302.models.TextModerationPlusResponseBody;
import com.aliyun.green20220302.models.TextModerationRequest;
import com.aliyun.green20220302.models.TextModerationResponse;
import com.aliyun.green20220302.models.TextModerationResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TextModerationService {
    @Value("${aliyun.security.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.security.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.security.endpoint}")
    private String endpoint;
    @Value("${aliyun.oss.region}")
    private String regionId;

    public List<String> textModeratePlus(String text) {
        TextModerationPlusResponse response;
        try {
            response = callTextModeratePlus(text);
        } catch (Exception e) {
            log.error("调用阿里云的文本内容安全Plus服务异常", e);
            return null;
        }
        if (Objects.isNull(response) || 200 != response.getStatusCode()) {
            log.error("调用阿里云的内容安全Plus服务,返回结果异常,response={}", response);
            return null;
        }
        TextModerationPlusResponseBody body = response.getBody();
        log.info("调用阿里云的文本内容安全Plus服务，返回{}", JSONObject.toJSONString(body));

        if (200 != body.getCode()) {
            log.error("调用阿里云的内容安全Plus服务，返回状态码失败，code={}", body.getCode());
            return null;
        }
        return body.getData().getResult().stream()
            .map(TextModerationPlusResponseBody.TextModerationPlusResponseBodyDataResult::getLabel)
            .collect(Collectors.toList());
    }


    /**
     * 调用阿里云的文本内容进行审核
     * @param text 文本内容
     * @return 审核结果
     * @throws Exception
     */
    public TextModerationPlusResponse callTextModeratePlus(String text) throws Exception {
        Config config = new Config();
        Client client = createClient(config);

        JSONObject serviceParameters = new JSONObject();
        serviceParameters.put("content", text);

        TextModerationPlusRequest request = new TextModerationPlusRequest();
        // 检测类型
        request.setService("llm_query_moderation");
        request.setServiceParameters(serviceParameters.toJSONString());
        TextModerationPlusResponse response = null;
        try {
            response = client.textModerationPlus(request);
        } catch (Exception e) {
            log.error("调用文本审核Plus接口异常", e);
        }
        return response;
    }

    public List<String> textModerate(String text) {
        TextModerationResponse response;
        try {
            response = callTextModerate(text);
        } catch (Exception e) {
            log.error("调用阿里云的文本内容安全服务异常", e);
            return null;
        }
        if (Objects.isNull(response) || 200 != response.getStatusCode()) {
            log.error("调用阿里云的内容安全服务,返回结果异常,response={}", response);
            return null;
        }
        TextModerationResponseBody body = response.getBody();
        log.info("调用阿里云的文本内容安全服务，返回{}", JSONObject.toJSONString(body));

        if (200 != body.getCode()) {
            log.error("调用阿里云的内容安全服务，返回状态码失败，code={}", body.getCode());
            return null;
        }
        return Arrays.asList(body.getData().getLabels().split(","));
    }

    public TextModerationResponse callTextModerate(String text) throws Exception {
        Config config = new Config();
        Client client = createClient(config);

        JSONObject serviceParameters = new JSONObject();
        serviceParameters.put("content", text);

        // 创建RuntimeObject实例并设置运行参数。
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;

        TextModerationRequest request = new TextModerationRequest();
        request.setService("ai_art_detection");
        request.setServiceParameters(serviceParameters.toJSONString());
        TextModerationResponse response = null;
        try {
            response = client.textModerationWithOptions(request, runtime);
            return response;
        } catch (Exception e) {
            log.error("调用文本审核接口异常", e);
        }
        return response;
    }

    /**
     * 请求创建 Client 实例
     * @return Client 实例
     * @throws Exception
     */
    private Client createClient(Config config) throws Exception {
        config.setRegionId(regionId);
        config.setAccessKeyId(accessKeyId);
        config.setAccessKeySecret(accessKeySecret);
        config.setEndpoint(endpoint);
        //连接时超时时间，单位毫秒（ms）。
        config.setReadTimeout(6000);
        //读取时超时时间，单位毫秒（ms）。
        config.setConnectTimeout(3000);
        return new Client(config);
    }
}

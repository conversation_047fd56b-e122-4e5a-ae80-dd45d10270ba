/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 队列查询结果
 *
 * <AUTHOR>
 * @version : QueryResult.java, v 0.1 2024/5/10 11:13 renxiao.wu Exp $
 */
@Data
public class QueueResult implements Serializable {
    private static final long serialVersionUID = -1310018395995383884L;

    /** 状态码 */
    private QueueCodeEnum code = QueueCodeEnum.UNKNOWN;

    /** 队列大小 */
    private int queueSize;

    /** 执行进度 */
    private BigDecimal schedule = BigDecimal.ZERO;

    public QueueResult() {
    }

    public QueueResult(QueueCodeEnum code) {
        this.code = code;
    }

    /**
     * 是否已结束
     *
     * @return true 已结束
     */
    public boolean isEnded() {
        return code == QueueCodeEnum.COMPLETED || code == QueueCodeEnum.FAILED;
    }

    public boolean isClear() {
        return code != QueueCodeEnum.UNKNOWN;
    }

    public enum QueueCodeEnum {
        /** 队列中 */
        QUEUED,
        /** 处理中 */
        RUNNING,
        /** 处理完成 */
        COMPLETED,
        /** 处理失败 */
        FAILED,
        /** 未知 */
        UNKNOWN,
        /** 未查到 */
        NONE
    }
}

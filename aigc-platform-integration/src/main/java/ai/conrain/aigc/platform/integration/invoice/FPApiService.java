package ai.conrain.aigc.platform.integration.invoice;

import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import jakarta.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * https://fpapi.com
 * https://fpapi.com/doc/#/page/kaiJuFaPiao
 */
@Slf4j
@Service
public class FPApiService {

    //https://cms.fpapi.com/cms/#/profile
    private static final String AID = "SnS8mRxTdicp6spGgkqXtNfr";
    private static final String APP_SECRET = "t8RC5cQmWydY2sdHX48Ef0rgppab1Gix";
    private static final String TAX_NO = "91330110MACTWANK7G";
    private static final String COMPANY_NAME = "杭州霖润智能科技有限公司";
    private static final String COMPANY_ID = "a3SEKmrK8xrpFf3Rgh2MNG0Y";

    public static final String INVOICE_EMAIL = "<EMAIL>";
    //税率，填小数形式，这里固定1%
    public static final String TAX_RATE = "0.01";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 创建开票任务（发票api）
     * https://fpapi.com/doc/#/page/kaiJuFaPiao
     * @return task_id 外部任务id
     */
    public String createInvoiceTask(String invoiceNo, String buyerType, String buyerName, String buyerTaxNo, String amount) {
        try {
            //invoiceNo，内部发票号，具备惟一性
            if (StringUtils.isBlank(invoiceNo)) {
                throw new RuntimeException("[自动开票]发票号不能为空");
            }
            if (StringUtils.isBlank(buyerType)) {
                throw new RuntimeException("[自动开票]购方身份类型不能为空");
            }
            if (!StringUtils.equals(buyerType, "1") && !StringUtils.equals(buyerType, "2")) {
                throw new RuntimeException("[自动开票]购方身份类型只能为1或2");
            }
            if (StringUtils.isBlank(buyerName)) {
                throw new RuntimeException("[自动开票]购方名称不能为空");
            }
            if (StringUtils.isBlank(buyerTaxNo)) {
                throw new RuntimeException("[自动开票]购方税号不能为空");
            }
            if (StringUtils.isBlank(amount)) {
                throw new RuntimeException("[自动开票]发票金额不能为空");
            }

            // 添加发票信息
            List<InvoiceItem> invoices = new ArrayList<>();
            InvoiceItem invoice = new InvoiceItem();
            invoice.setCustomInvoiceNo(invoiceNo);
            invoice.setInvoiceType(2); // 普票
            invoice.setIsContainTax(1); // 含税
            invoice.setBuyerType(buyerType); // 购方身份类型，1：企业，2、自然人
            invoice.setBuyerName(buyerName.trim());
            invoice.setBuyerTaxNo(buyerTaxNo.trim());
            invoice.setBuyerEmail(INVOICE_EMAIL);
            invoice.setRemark("");

            // 添加商品信息
            List<Good> goods = new ArrayList<>();
            Good good = new Good();
            good.setGoodsName("信息服务费");
            //固定现代服务对应的税码
            good.setTaxScopeCode("3049900000000000000");
            good.setAmount(amount);
            good.setTaxRate(TAX_RATE);
            good.setUnit("项");
            good.setQuantity("1");
            goods.add(good);
            invoice.setGoods(goods);

            invoices.add(invoice);

            Map<String, Object> body = new LinkedHashMap<>();
            body.put("invoices", invoices);

            SignRet signRet = getSignByReqBody(body);

            // 封装请求
            HttpEntity<String> request = new HttpEntity<>(signRet.bodyStr, signRet.headers);

            // 发起 POST 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    String.format("https://api.fpapi.com/%s/company/%s/invoicing", AID, COMPANY_ID),
                    HttpMethod.POST,
                    request,
                    String.class
            );

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                FPResponseData resBody = JSONObject.parseObject(response.getBody(), FPResponseData.class);
                if (resBody != null && resBody.getData() != null && resBody.getData().containsKey("task_id")) {
                    return resBody.getData().getString("task_id");
                }
            }

        } catch (Exception e) {
            log.error("[自动开票]请求发票开具失败", e);
        }

        return null;
    }

    /**
     * 重试任务
     * https://fpapi.com/doc/#/page/chongFaRenWu
     * @param taskId
     * @return
     */
    public boolean retryInvoice(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("[自动开票]重试任务的task_id不能为空");
        }
        try {
            Map<String, Object> body = new LinkedHashMap<>();
            SignRet signRet = getSignByReqBody(body);

            // 封装请求
            HttpEntity<String> request = new HttpEntity<>(signRet.bodyStr, signRet.headers);

            // 发起 POST 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    String.format("https://api.fpapi.com/%s/company/%s/task/%s/restart", AID, COMPANY_ID, taskId),
                    HttpMethod.POST,
                    request,
                    String.class
            );

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                FPResponseData resBody = JSONObject.parseObject(response.getBody(), FPResponseData.class);
                return resBody != null && resBody.getCode() == 200;
            }

        } catch (Exception e) {
            log.error("[自动开票]重试任务失败", e);
        }

        return false;
    }

    /**
     * 冲正发票（发票api）
     * https://fpapi.com/doc/#/page/hongChongFaPiao
     *
     * @param originInvoiceNo 被冲正的原发票号
     * @param drawDate 原发票开具日期 例：2023-08-08
     * @return task_id
     */
    public String offsetInvoice(String originInvoiceNo, String drawDate){
        if (StringUtils.isBlank(originInvoiceNo)) {
            throw new RuntimeException("[自动开票]被冲正的原发票号不能为空");
        }

        if (StringUtils.isBlank(drawDate)) {
            throw new RuntimeException("[自动开票]原发票开具日期不能为空");
        }

        try {
            Map<String, Object> body = new LinkedHashMap<>();
            body.put("electronic_invoice_no", originInvoiceNo);
            body.put("draw_date", drawDate);
            body.put("email", INVOICE_EMAIL);

            SignRet signRet = getSignByReqBody(body);
            HttpEntity<String> request = new HttpEntity<>(signRet.bodyStr, signRet.headers);
            ResponseEntity<String> response = restTemplate.exchange(
                    String.format("https://api.fpapi.com/%s/company/%s/invoice_offsetting", AID, COMPANY_ID),
                    HttpMethod.POST,
                    request,
                    String.class
            );
            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                FPResponseData resBody = JSONObject.parseObject(response.getBody(), FPResponseData.class);
                if (resBody != null && resBody.getData() != null && resBody.getData().containsKey("task_id")) {
                    return resBody.getData().getString("task_id");
                }
            }
        } catch (Exception e) {
            log.error("[自动开票]冲正发票失败", e);
        }

        return null;
    }

    /**
     * 创建开票公司（发票api）
     * @return company_id
     * @throws Exception
     */
    public String createCompany() throws Exception {

        try {
            Map<String, Object> body = new LinkedHashMap<>();
            body.put("tax_no", TAX_NO);
            body.put("tax_area_code", "3300");
            body.put("company_name", COMPANY_NAME);
            body.put("notify_url", "");

            SignRet signRet = getSignByReqBody(body);

            // 封装请求
            HttpEntity<String> request = new HttpEntity<>(signRet.bodyStr, signRet.headers);

            // 发起 POST 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    String.format("https://api.fpapi.com/%s/companies", AID),
                    HttpMethod.POST,
                    request,
                    String.class
            );

            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                FPResponseData resBody = JSONObject.parseObject(response.getBody(), FPResponseData.class);
                if (resBody != null && resBody.getData() != null && resBody.getData().containsKey("company_id")) {
                    return resBody.getData().getString("company_id");
                }
            }

        } catch (Exception e) {
            log.error("[自动开票]请求创建公司失败", e);
        }

        return null;
    }

    @Data
    public static class InvoiceItem {
        @JSONField(name = "custom_invoice_no")
        private String customInvoiceNo;
        @JSONField(name = "invoice_type")
        private int invoiceType;
        @JSONField(name = "is_contain_tax")
        private int isContainTax;
        @JSONField(name = "buyer_type")
        private String buyerType;
        @JSONField(name = "buyer_name")
        private String buyerName;
        @JSONField(name = "buyer_tax_no")
        private String buyerTaxNo;
        @JSONField(name = "buyer_email")
        private String buyerEmail;
        @JSONField(name = "remark")
        private String remark;
        @JSONField(name = "goods")
        private List<Good> goods;
    }

    @Data
    public static class Good {
        @JSONField(name = "goods_name")
        private String goodsName;
        @JSONField(name = "tax_scope_code")
        private String taxScopeCode;
        @JSONField(name = "amount")
        private String amount;
        @JSONField(name = "tax_rate")
        private String taxRate;

        //单位，如：个， specific_service_type=2001时，仅支持升或吨
        private String unit;

        //数量，如：23
        private String quantity;

        //单价，包含整数部分、小数部分和小数点，加起来最长16个字符，如：33.3333333333333、33333333333333.3
        @JSONField(name = "unit_price")
        private String unitPrice;
    }

    private static class SignRet {
        public final String bodyStr;
        public final HttpHeaders headers;

        public SignRet(String bodyStr, HttpHeaders headers) {
            this.bodyStr = bodyStr;
            this.headers = headers;
        }
    }

    @NotNull
    private static SignRet getSignByReqBody(Map<String, Object> body) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String nonceStr = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
        byte[] appSecret = APP_SECRET.getBytes(StandardCharsets.UTF_8);
        //?号后面的部分，k1=v1&k2=v2
        String queryStr = "";
        String queryHash = hmac256Str(appSecret, queryStr);

        String bodyStr = JSONObject.toJSONString(body);
        String bodyHash = hmac256Str(appSecret, bodyStr);

        String originStr = "app_secret=" + APP_SECRET + "\n"
                + "body=" + bodyHash + "\n"
                + "nonce_str=" + nonceStr + "\n"
                + "query=" + queryHash + "\n"
                + "timestamp=" + timestamp;
        String sign = hmac256Str(appSecret, originStr);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-FP-NonceStr", nonceStr);
        headers.set("X-FP-Timestamp", timestamp);
        headers.set("Authorization", "FP-API-SIGN-HMAC-SHA256 " + sign);
        return new SignRet(bodyStr, headers);
    }

    private static byte[] hmac256(byte[] key, String msg) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }

    private static String hmac256Str(byte[] key, String msg) throws Exception {
        return DatatypeConverter.printHexBinary(hmac256(key, msg)).toLowerCase();
    }

}

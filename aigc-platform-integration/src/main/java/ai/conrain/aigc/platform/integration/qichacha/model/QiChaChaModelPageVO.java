package ai.conrain.aigc.platform.integration.qichacha.model;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class QiChaChaModelPageVO {

    /**
     * 页码
     */
    private Integer pageIndex;

    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 总数
     */
    private Integer total;

    /**
     * 子列表数据
     */
    private List<QiChaChaModel> data;

    @Data
    public static class QiChaChaModel {

        /**
         * 主键
         */
        private String keyNo;

        /**
         * 注册号，若查询企业为中国香港企业时：该字段返回企业编号
         */
        private String no;

        /**
         * 企业名称(eg：杭州霖润智能科技有限公司)
         */
        private String name;

        /**
         * 统一社会信用代码，若查询企业为中国香港企业时：该字段返回商业登记号码
         */
        private String creditCode;

        /**
         * 法定代表人姓名
         */
        private String operName;

        /**
         * 企业状态
         */
        private String status;

        /**
         * 企业注册地
         */
        private String address;

        /**
         * 企业成立时间
         */
        private Date startDate;
    }

}

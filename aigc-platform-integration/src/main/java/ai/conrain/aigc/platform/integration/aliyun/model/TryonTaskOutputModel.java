package ai.conrain.aigc.platform.integration.aliyun.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TryonTaskOutputModel {
    @JSONField(name = "task_id")
    private String taskId;
    @J<PERSON>NField(name = "task_status")
    private String taskStatus;
    @JSONField(name = "submit_time")
    private String submitTime;
    @JSONField(name = "scheduled_time")
    private String scheduledTime;
    @JSONField(name = "end_time")
    private String endTime;
    @JSONField(name = "image_url")
    private String imageUrl;
}

package ai.conrain.aigc.platform.integration.interceptor;

import java.nio.charset.StandardCharsets;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(2000, 2000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    @Bean
    public RestTemplate normalRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(3000, 3000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    @Bean
    public RestTemplate longRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(8000, 8000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    @Bean
    public RestTemplate extraLongRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(20000, 20000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    @Bean("superLongRestTemplate")
    public RestTemplate superLongRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(100000, 100000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    @Bean("restTemplate3min")
    public RestTemplate restTemplate3min() {
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory(180000, 180000));
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        restTemplate.getInterceptors().add(new RestTemplateInterceptor());
        return restTemplate;
    }

    private ClientHttpRequestFactory clientHttpRequestFactory(int readTimeout, int connectTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();

        //请求微信的接口连接池是20秒，所以设置超时时间不要超过20秒 @see https://juejin.cn/post/6865277186438496269
        factory.setReadTimeout(readTimeout);
        factory.setConnectTimeout(connectTimeout);
        return new BufferingClientHttpRequestFactory(factory);
    }
}

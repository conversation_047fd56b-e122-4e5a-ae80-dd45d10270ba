package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class ModelValidationRequest {
    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;

    @JsonProperty("model_scene")
    @JSONField(name = "model_scene")
    private String modelScene;
    
    @JsonProperty("model_dir")
    @JSONField(name = "model_dir")
    private String modelDir;
    
    @JsonProperty("data_source")
    @JSONField(name = "data_source")
    private String dataSource;
    
    @JsonProperty("classification_label")
    @JSONField(name = "classification_label")
    private List<String> classificationLabel;

    @JsonProperty("distribution_total")
    @JSONField(name = "distribution_total")
    private SamplePreparationResponse.DistributionTotal distributionTotal;
}
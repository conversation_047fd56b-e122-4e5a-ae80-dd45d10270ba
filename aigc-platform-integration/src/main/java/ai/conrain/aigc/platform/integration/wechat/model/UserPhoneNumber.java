package ai.conrain.aigc.platform.integration.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class UserPhoneNumber extends BaseResponse {

    private static final long serialVersionUID = 4020675060462890651L;

    @JSONField(name = "phone_info")
    private PhoneInfo phoneInfo;

    @Data
    public static class PhoneInfo {
        //用户绑定的手机号（国外手机号会有区号）
        private String phoneNumber;
        //没有区号的手机号
        private String purePhoneNumber;
        //区号
        private String countryCode;
        //数据水印
        private Watermark watermark;
    }

    @Data
    public static class Watermark {
        //用户获取手机号操作的时间戳
        private Long timestamp;
        //小程序appid
        private String appid;
    }
}

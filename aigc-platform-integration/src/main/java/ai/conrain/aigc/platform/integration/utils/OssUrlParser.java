package ai.conrain.aigc.platform.integration.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.URL;

@Slf4j
public class OssUrlParser {

    // 定义返回结果的类
    public static class ParsedOssUrl {
        private String bucketName;
        private String objectName;

        public ParsedOssUrl(String bucketName, String objectName) {
            this.bucketName = bucketName;
            this.objectName = objectName;
        }

        public String getBucketName() {
            return bucketName;
        }

        public String getObjectName() {
            return objectName;
        }

        @Override
        public String toString() {
            return "ParsedOssUrl{" +
                    "bucketName='" + bucketName + '\'' +
                    ", objectName='" + objectName + '\'' +
                    '}';
        }
    }

    /**
     * 解析 OSS 文件 URL
     *
     * @param fileUrl OSS 文件 URL
     * @return 包含 bucketName 和 objectName 的解析结果
     */
    public static ParsedOssUrl parseOssUrl(String fileUrl) {
        try {
            // 解析 URL
            URL url = new URL(fileUrl);
            String host = url.getHost(); // 获取域名部分
            String path = url.getPath(); // 获取路径部分

            // 提取 Bucket 名称
            String bucketName = host.split("\\.")[0];

            // 提取 Object 路径（去掉开头的 "/"）
            String objectName = path.substring(1);

            // 如果 URL 包含查询参数（签名部分），移除签名部分
            if (url.getQuery() != null) {
                // 确保 objectName 不包含查询参数
                objectName = objectName.split("\\?")[0];
            }

            // 返回解析结果
            return new ParsedOssUrl(bucketName, objectName);
        } catch (Exception e) {
            log.error("Invalid OSS URL: " + fileUrl, e);
            return null;
        }
    }
}
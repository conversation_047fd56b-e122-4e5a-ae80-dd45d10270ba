package ai.conrain.aigc.platform.integration.meitu.model;

import lombok.Data;

/**
 * 衣服去皱请求参数
 * 
 * <AUTHOR> Assistant
 */
@Data
public class MeituRemoveWrinkleRequest {

    /**
     * 图片数据（base64编码）
     */
    private String imageData;

    /**
     * 图片URL（当dataType为url时使用）
     */
    private String imageUrl;

    /**
     * 数据类型：jpg（base64编码）或 url（图片链接）
     */
    private String dataType = "jpg";

    /**
     * 去皱程度（0-100）
     */
    private Integer alpha = 100;

    /**
     * 构造函数 - 使用base64图片数据
     *
     * @param imageData base64编码的图片数据
     */
    public MeituRemoveWrinkleRequest(String imageData) {
        this.imageData = imageData;
        this.dataType = "jpg";
    }

    /**
     * 构造函数 - 使用图片URL
     *
     * @param imageUrl 图片URL
     * @param isUrl    是否为URL类型
     */
    public MeituRemoveWrinkleRequest(String imageUrl, boolean isUrl) {
        if (isUrl) {
            this.imageUrl = imageUrl;
            this.dataType = "url";
        } else {
            this.imageData = imageUrl;
            this.dataType = "jpg";
        }
    }

    /**
     * 构造函数 - 完整参数
     *
     * @param imageData base64编码的图片数据或URL
     * @param dataType  数据类型
     * @param alpha     去皱程度
     */
    public MeituRemoveWrinkleRequest(String imageData, String dataType, Integer alpha) {
        if ("url".equals(dataType)) {
            this.imageUrl = imageData;
        } else {
            this.imageData = imageData;
        }
        this.dataType = dataType;
        this.alpha = alpha;
    }

    /**
     * 静态工厂方法 - 创建base64请求
     *
     * @param imageData base64编码的图片数据
     * @return 请求对象
     */
    public static MeituRemoveWrinkleRequest fromBase64(String imageData) {
        return new MeituRemoveWrinkleRequest(imageData);
    }

    /**
     * 静态工厂方法 - 创建URL请求
     *
     * @param imageUrl 图片URL
     * @return 请求对象
     */
    public static MeituRemoveWrinkleRequest fromUrl(String imageUrl) {
        return new MeituRemoveWrinkleRequest(imageUrl, true);
    }
}
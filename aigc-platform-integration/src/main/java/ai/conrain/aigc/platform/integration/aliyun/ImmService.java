package ai.conrain.aigc.platform.integration.aliyun;

import com.aliyun.imm20200930.models.CreateDecodeBlindWatermarkTaskRequest;
import com.aliyun.imm20200930.models.CreateDecodeBlindWatermarkTaskResponse;
import com.aliyun.imm20200930.models.EncodeBlindWatermarkRequest;
import com.aliyun.imm20200930.models.EncodeBlindWatermarkResponse;
import com.aliyun.imm20200930.models.GetDecodeBlindWatermarkResultResponse;
import com.aliyun.imm20200930.models.GetDecodeBlindWatermarkResultResponseBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 智能媒体管理
 * https://help.aliyun.com/zh/imm/developer-reference/api-imm-2020-09-30-overview?spm=a2c4g.11186623.help-menu-62354.d_5_2_0.37fe1f9794uq7V
 */
@Slf4j
@Service
public class ImmService {

    @Autowired
    private com.aliyun.imm20200930.Client immClient;

    /**
     * 为图像添加电子水印
     * https://help.aliyun.com/zh/imm/developer-reference/api-imm-2020-09-30-encodeblindwatermark?spm=a2c4g.11186623.0.0.1f6275ab1Mlnhk
     * @param sourceURI
     * @param targetURI
     * @param watermark
     * @return
     */
    public boolean encodeBlindWatermark(String sourceURI, String targetURI, String watermark) {

        if (StringUtils.isBlank(sourceURI) || StringUtils.isBlank(targetURI) || StringUtils.isBlank(watermark)) {
            throw new RuntimeException("encodeBlindWatermark params invalid");
        }

        try {
            EncodeBlindWatermarkRequest encodeBlindWatermarkRequest = new EncodeBlindWatermarkRequest();
            /**
             * test是imm开通时默认项目名
             * https://imm.console.aliyun.com/v2/cn-zhangjiakou/projects/test/overview
             */
            encodeBlindWatermarkRequest.projectName = "test";
            encodeBlindWatermarkRequest.sourceURI = sourceURI;
            encodeBlindWatermarkRequest.targetURI = targetURI;
            encodeBlindWatermarkRequest.content = watermark;
            encodeBlindWatermarkRequest.strengthLevel = "medium";
            encodeBlindWatermarkRequest.imageQuality = 100;

            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            EncodeBlindWatermarkResponse res = immClient.encodeBlindWatermarkWithOptions(encodeBlindWatermarkRequest, runtime);
            return res != null && res.statusCode == 200;

        } catch (Exception e){
            log.error("encodeBlindWatermark exception", e);
            return false;
        }
    }

    /**
     * 获取水印，发起解码任务
     * @param sourceURI
     * @return
     */
    public String decodeBlindWatermark(String sourceURI) {

        if (StringUtils.isBlank(sourceURI)) {
            throw new RuntimeException("decodeBlindWatermark params invalid");
        }

        CreateDecodeBlindWatermarkTaskRequest createDecodeBlindWatermarkTaskRequest = new CreateDecodeBlindWatermarkTaskRequest();
        createDecodeBlindWatermarkTaskRequest.projectName = "test";
        createDecodeBlindWatermarkTaskRequest.sourceURI = sourceURI;
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            CreateDecodeBlindWatermarkTaskResponse res = immClient.createDecodeBlindWatermarkTaskWithOptions(createDecodeBlindWatermarkTaskRequest, runtime);
            if (res != null && res.statusCode == 200 && res.body != null && res.body.taskId != null) {
                return res.body.taskId;
            }

        } catch (Exception e){
            log.error("decodeBlindWatermark exception", e);
        }
        throw new RuntimeException("decodeBlindWatermark failed");
    }

    /**
     * 获取水印解码结果
     * GetDecodeBlindWatermarkResultResponseBody#content
     * @param taskId
     * @return
     */
    public GetDecodeBlindWatermarkResultResponseBody getDecodeBlindWatermarkResult(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new RuntimeException("getDecodeBlindWatermarkResult params invalid");
        }

        try {
            com.aliyun.imm20200930.models.GetDecodeBlindWatermarkResultRequest getDecodeBlindWatermarkResultRequest = new com.aliyun.imm20200930.models.GetDecodeBlindWatermarkResultRequest();
            getDecodeBlindWatermarkResultRequest.projectName = "test";
            getDecodeBlindWatermarkResultRequest.taskId = taskId;
            getDecodeBlindWatermarkResultRequest.setTaskType("DecodeBlindWatermark");

            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            GetDecodeBlindWatermarkResultResponse res = immClient.getDecodeBlindWatermarkResultWithOptions(getDecodeBlindWatermarkResultRequest, runtime);
            if (res != null && res.statusCode == 200 && res.body != null) {
                return res.body;
            }
        } catch (Exception e){
            log.error("getDecodeBlindWatermarkResult exception", e);
        }
        throw new RuntimeException("getDecodeBlindWatermarkResult failed");
    }
}

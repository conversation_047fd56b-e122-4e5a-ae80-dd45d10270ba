package ai.conrain.aigc.platform.integration.aliyun;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.ice20201109.models.GetBatchMediaProducingJobRequest;
import com.aliyun.ice20201109.models.GetBatchMediaProducingJobResponse;
import com.aliyun.ice20201109.models.GetBatchMediaProducingJobResponseBody;
import com.aliyun.ice20201109.models.GetMediaProducingJobRequest;
import com.aliyun.ice20201109.models.GetMediaProducingJobResponse;
import com.aliyun.ice20201109.models.GetMediaProducingJobResponseBody;
import com.aliyun.ice20201109.models.SubmitBatchMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitBatchMediaProducingJobResponse;
import com.aliyun.ice20201109.models.SubmitBatchMediaProducingJobResponseBody;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse;
import com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * aliyun智能媒体服务
 * https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-dir/?spm=a2c4g.11186623.help-menu-193643.d_5_0_3.16c1550bcp5L0A&scm=20140722.H_440756._.OR_help-T_cn#DAS#zh-V_1
 */
@Slf4j
@Service
public class IceClientService {

    @Autowired
    private com.aliyun.ice20201109.Client iceClient;

    private static final String SUCCESS = "Success";

    /**
     * https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitmediaproducingjob?spm=a2c4g.11186623.help-menu-193643.d_5_0_3_6_1_0.301a709dy6p1m1&scm=20140722.H_441147._.OR_help-T_cn#DAS#zh-V_1
     */
    public SubmitMediaProducingJobResponseBody submitMediaProducingJob(SubmitMediaProducingJobRequest req) {
        Assert.notNull(req, "req must not be null");
        if (req.getProjectId() == null && req.getTimeline() == null && req.getTemplateId() == null) {
            throw new IllegalArgumentException("必须填写 ProjectId、Timeline、TemplateId 三个参数中的一个，剩余两个参数填写为空。");
        }
        if (req.getTemplateId() != null && StringUtils.isEmpty(req.getClipsParam())) {
            throw new IllegalArgumentException("当 TemplateId 不为空时，ClipsParam 必须填写。");
        }
        Assert.notNull(req.getOutputMediaConfig(), "OutputMediaConfig must not be null");

        try {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            SubmitMediaProducingJobResponse res = iceClient.submitMediaProducingJobWithOptions(req, runtime);
            if (res == null || res.getBody() == null || StringUtils.isEmpty(res.getBody().getJobId())) {
                throw new RuntimeException("提交单个剪辑任务失败:" + res);
            }

            return res.getBody();

        } catch (Throwable e) {
            log.error("iceClient submitMediaProducingJob exception", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getmediaproducingjob?spm=a2c4g.11186623.help-menu-193643.d_5_0_3_6_1_1.300e742eFoMgNH&scm=20140722.H_441149._.OR_help-T_cn#DAS#zh-V_1
     */
    public GetMediaProducingJobResponseBody getMediaProducingJob(String jobId){
        Assert.state(!StringUtils.isEmpty(jobId), "jobId must not be empty");

        try {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            GetMediaProducingJobRequest req = new GetMediaProducingJobRequest();
            req.setJobId(jobId);
            GetMediaProducingJobResponse res = iceClient.getMediaProducingJobWithOptions(req, runtime);
            if (res == null || res.getBody() == null || res.getBody().getMediaProducingJob() == null
                || StringUtils.isEmpty(res.getBody().getMediaProducingJob().getStatus())
                || !SUCCESS.equalsIgnoreCase(res.getBody().getMediaProducingJob().getStatus())) {
                throw new RuntimeException("获取单个剪辑任务状态失败:" + res);
            }
            return res.getBody();

        } catch (Throwable e) {
            log.error("iceClient getMediaProducingJob exception", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-submitbatchmediaproducingjob?spm=a2c4g.11186623.help-menu-193643.d_5_0_3_6_1_4.47097e58JiqLkv
     * @param inputConfig
     * @param editConfig
     * @param outputConfig
     * @param userData
     * @return
     */

    public SubmitBatchMediaProducingJobResponseBody submitBatchMediaProducingJob(JSONObject inputConfig, JSONObject editConfig, JSONObject outputConfig, JSONObject userData){
        Assert.notEmpty(inputConfig, "inputConfig must not be empty");
        Assert.notEmpty(editConfig, "editConfig must not be empty");
        Assert.notEmpty(outputConfig, "outputConfig must not be empty");

        SubmitBatchMediaProducingJobRequest req = new SubmitBatchMediaProducingJobRequest();
        req.setInputConfig(inputConfig.toJSONString());
        req.setEditingConfig(editConfig.toJSONString());
        req.setOutputConfig(outputConfig.toJSONString());
        if (userData != null) {
            req.setUserData(userData.toJSONString());
        }

        try {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            SubmitBatchMediaProducingJobResponse res = iceClient.submitBatchMediaProducingJobWithOptions(req, runtime);
            if (res == null || res.getBody() == null || StringUtils.isEmpty(res.getBody().getJobId())) {
                throw new RuntimeException("提交批量剪辑任务失败:" + res);
            }

            return res.getBody();

        } catch (Throwable e) {
            log.error("iceClient submitBatchMediaProducingJob exception", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * https://help.aliyun.com/zh/ims/developer-reference/api-ice-2020-11-09-getbatchmediaproducingjob?spm=a2c4g.11186623.help-menu-193643.d_5_0_3_6_1_5.45f26681FYgqhF&scm=20140722.H_2693269._.OR_help-T_cn#DAS#zh-V_1
     * @param jobId
     * @return
     */
    public GetBatchMediaProducingJobResponseBody getBatchMediaProducingJob(String jobId){
        Assert.state(!StringUtils.isEmpty(jobId), "jobId must not be empty");

        try {
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            GetBatchMediaProducingJobRequest req = new GetBatchMediaProducingJobRequest();
            req.setJobId(jobId);
            GetBatchMediaProducingJobResponse res = iceClient.getBatchMediaProducingJobWithOptions(req, runtime);
            if (res == null || res.getBody() == null || res.getBody().getEditingBatchJob() == null || !SUCCESS.equalsIgnoreCase(res.getBody().getEditingBatchJob().getStatus())) {
                throw new RuntimeException("获取批量剪辑任务状态失败:" + res);
            }

            return res.getBody();
        } catch (Throwable e) {
            log.error("iceClient getBatchMediaProducingJob exception", e);
            throw new RuntimeException(e);
        }
    }

}
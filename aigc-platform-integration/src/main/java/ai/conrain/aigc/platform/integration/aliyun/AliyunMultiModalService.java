package ai.conrain.aigc.platform.integration.aliyun;

import ai.conrain.aigc.platform.integration.constant.ConfigConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通义千问VL
 * https://help.aliyun.com/zh/model-studio/user-guide/vision/?spm=a2c4g.11186623.help-menu-2400256.d_1_0_1.75cb7ee8NCyr3D#b1997af8cbi78
 */
@Slf4j
@Service
public class AliyunMultiModalService {

    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate restTemplate;

    // https://help.aliyun.com/zh/model-studio/user-guide/vision/?spm=a2c4g.11186623.help-menu-2400256.d_1_0_1.2b6e65c5i9eT2P&scm=20140722.H_2845871._.OR_help-T_cn~zh-V_1#14d646f5a0owq

    /**
     * 调用QWenVL模型，发起视觉理解任务
     * 
     * @param prompt
     * @param imgUrls
     * @return
     */
    public String visualUnderstanding(String prompt, List<String> imgUrls) {
        if (StringUtils.isBlank(prompt) || CollectionUtils.isEmpty(imgUrls)) {
            throw new IllegalArgumentException("prompt/imgUrls must not be blank");
        }

        try {
            // 构建请求URL
            String url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + ConfigConstant.DASHSCOPE_API_KEY);
            headers.set("Content-Type", "application/json");

            // 构建系统消息
            Map<String, Object> systemContent = new HashMap<>();
            systemContent.put("type", "text");
            systemContent.put("text", "You are a helpful assistant.");

            Map<String, Object> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", Collections.singletonList(systemContent));

            // 构建用户消息内容
            List<Map<String, Object>> userContent = new ArrayList<>();

            // 添加图片URL
            for (String imgUrl : imgUrls) {
                Map<String, Object> imageUrlMap = new HashMap<>();
                imageUrlMap.put("url", imgUrl);

                Map<String, Object> imageContent = new HashMap<>();
                imageContent.put("type", "image_url");
                imageContent.put("image_url", imageUrlMap);

                userContent.add(imageContent);
            }

            // 添加文本提示
            Map<String, Object> textContent = new HashMap<>();
            textContent.put("type", "text");
            textContent.put("text", prompt);
            userContent.add(textContent);

            // 构建用户消息
            Map<String, Object> userMessage = new HashMap<>();
            userMessage.put("role", "user");
            userMessage.put("content", userContent);

            // 构建完整请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "qwen-vl-max-latest");
            requestBody.put("messages", Arrays.asList(systemMessage, userMessage));

            // 发送HTTP请求
            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(requestBody), headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity,
                    String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();

                log.info("visualUnderstanding response: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                JSONObject choice = jsonResponse.getJSONArray("choices").getJSONObject(0);
                JSONObject message = choice.getJSONObject("message");
                return message.getString("content");
            } else {
                log.error("visualUnderstanding error: status={}, response={}",
                        responseEntity.getStatusCodeValue(), responseEntity.getBody());
                throw new RuntimeException("QWenVL visualUnderstanding error: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("visualUnderstanding error", e);
            throw new RuntimeException("QWenVL visualUnderstanding error: " + e.getMessage());
        }
    }

    private List<Map<String, Object>> buildUserMessageContent(String prompt, List<String> imgUrls) {
        List<Map<String, Object>> content = new ArrayList<>();
        for (String imgUrl : imgUrls) {
            content.add(Collections.singletonMap("image", imgUrl));
        }
        content.add(Collections.singletonMap("text", prompt));
        return content;
    }
}

package ai.conrain.aigc.platform.integration.meitu;

import ai.conrain.aigc.platform.integration.meitu.config.MeituConfig;
import ai.conrain.aigc.platform.integration.meitu.model.MeituRemoveWrinkleRequest;
import ai.conrain.aigc.platform.integration.meitu.model.MeituRequest;
import ai.conrain.aigc.platform.integration.meitu.model.MeituResponse;
import com.alibaba.fastjson.JSON;
import com.meitu.openai.common.Signer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 美图AI服务
 * 提供图像处理相关功能，包括衣服去皱等
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class MeituService {

    @Autowired
    private MeituConfig meituConfig;

    @Autowired
    @Qualifier("longRestTemplate")
    private RestTemplate longRestTemplate;

    /**
     * 衣服去皱功能
     * 
     * @param request 去皱请求参数
     * @return 处理后的图片base64数据，失败时返回null
     */
    public String removeWrinkle(MeituRemoveWrinkleRequest request) {
        if (request == null) {
            log.error("美图衣服去皱请求参数不能为空");
            return null;
        }

        try {
            // 构建请求
            MeituRequest meituRequest = buildRemoveWrinkleRequest(request);
            
            // 调用API
            MeituResponse response = callRemoveWrinkleApi(meituRequest);
            
            // 处理响应
            return processRemoveWrinkleResponse(response);
            
        } catch (Exception e) {
            log.error("调用美图衣服去皱API异常", e);
            return null;
        }
    }

    /**
     * 构建去皱请求
     * 
     * @param request 去皱请求参数
     * @return 美图API请求对象
     */
    private MeituRequest buildRemoveWrinkleRequest(MeituRemoveWrinkleRequest request) {
        MeituRequest meituRequest = new MeituRequest();
        
        // 构建媒体信息
        MeituRequest.MediaInfo mediaInfo = new MeituRequest.MediaInfo();
        if ("url".equals(request.getDataType())) {
            mediaInfo.setMediaData(request.getImageUrl());
        } else {
            mediaInfo.setMediaData(request.getImageData());
        }
        mediaInfo.setMediaExtra(new HashMap<>());
        
        MeituRequest.MediaProfiles mediaProfiles = new MeituRequest.MediaProfiles();
        mediaProfiles.setMediaDataType(request.getDataType());
        mediaInfo.setMediaProfiles(mediaProfiles);
        
        meituRequest.setMediaInfoList(Collections.singletonList(mediaInfo));
        
        // 构建参数
        MeituRequest.Parameter parameter = new MeituRequest.Parameter();
        parameter.setRspMediaType("jpg");
        parameter.setAlpha(request.getAlpha());
        meituRequest.setParameter(parameter);
        
        return meituRequest;
    }

    /**
     * 调用美图去皱API
     * 
     * @param request 请求对象
     * @return API响应
     * @throws URISyntaxException URI异常
     */
    private MeituResponse callRemoveWrinkleApi(MeituRequest request) throws URISyntaxException {
        String url = buildApiUrl(meituConfig.getRemoveWrinkleEndpoint());
        String method = "POST";
        String requestBody = JSON.toJSONString(request);
        
        // 构建签名头
        Map<String, String> headers = buildRequestHeaders();
        Map<String, String> signedHeaders = signRequest(url, method, headers, requestBody);
        
        // 设置HTTP头
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        signedHeaders.forEach(httpHeaders::set);
        
        // 发送请求
        HttpEntity<String> entity = new HttpEntity<>(requestBody, httpHeaders);
        ResponseEntity<String> responseEntity = longRestTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        
        // 解析响应
        String responseBody = responseEntity.getBody();
        if (StringUtils.isBlank(responseBody)) {
            log.error("美图API返回空响应");
            return null;
        }
        
        return JSON.parseObject(responseBody, MeituResponse.class);
    }

    /**
     * 构建API URL
     * 
     * @param endpoint 端点路径
     * @return 完整URL
     */
    private String buildApiUrl(String endpoint) {
        MeituConfig.Auth auth = meituConfig.getAuth();
        return String.format("%s%s?api_key=%s&api_secret=%s", 
            meituConfig.getBaseUrl(), 
            endpoint, 
            auth.getAppKey(), 
            auth.getSecretId());
    }

    /**
     * 构建请求头
     * 
     * @return 请求头Map
     */
    private Map<String, String> buildRequestHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put(Signer.HeaderHost, "openapi.mtlab.meitu.com");
        headers.put(Signer.HeaderContentSha256, "UNSIGNED-PAYLOAD");
        return headers;
    }

    /**
     * 签名请求
     * 
     * @param url     请求URL
     * @param method  请求方法
     * @param headers 请求头
     * @param body    请求体
     * @return 签名后的请求头
     * @throws URISyntaxException URI异常
     */
    private Map<String, String> signRequest(String url, String method, Map<String, String> headers, String body) 
            throws URISyntaxException {
        MeituConfig.Auth auth = meituConfig.getAuth();
        Signer signer = new Signer(auth.getAppKey(), auth.getSecretId());
        return signer.sign(url, method, headers, body);
    }

    /**
     * 处理去皱响应
     * 
     * @param response API响应
     * @return 处理后的图片base64数据
     */
    private String processRemoveWrinkleResponse(MeituResponse response) {
        if (response == null) {
            log.error("美图API响应为空");
            return null;
        }
        
        log.info("美图API响应解析结果 - 错误码：{}，消息：{}", response.getErrorCode(), response.getErrorMsg());
        
        if (!response.isSuccess()) {
            log.error("美图API调用失败，错误码：{}，错误信息：{}", response.getErrorCode(), response.getErrorMsg());
            return null;
        }
        
        if (response.getMediaInfoList() == null || response.getMediaInfoList().isEmpty()) {
            log.error("美图API返回的媒体信息列表为空");
            return null;
        }
        
        MeituResponse.MediaInfo mediaInfo = response.getMediaInfoList().get(0);
        if (StringUtils.isBlank(mediaInfo.getMediaData())) {
            log.error("美图API返回的媒体数据为空");
            return null;
        }
        
        log.info("美图衣服去皱处理成功，返回数据长度：{} 字符", mediaInfo.getMediaData().length());
        return mediaInfo.getMediaData();
    }

    /**
     * 示例方法 - 保留原有的request方法作为参考
     * 建议后续删除此方法
     */
    @Deprecated
    public void request() throws URISyntaxException {
        log.warn("使用了已废弃的request方法，请使用removeWrinkle方法");
        // 原有实现保持不变，但添加了日志警告
        MeituConfig.Auth auth = meituConfig.getAuth();
        Signer signer = new Signer(auth.getAppKey(), auth.getSecretId());
        // ... 其余代码保持不变但建议迁移到新的实现方式
    }
}

package ai.conrain.aigc.platform.integration.xiutu;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.model.StsCredentials;
import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Base64;
import java.util.Formatter;

/**
 * 咻图-服饰去皱
 * https://docs.aixtsy.com/guide/api#%E6%9C%8D%E9%A5%B0%E5%8E%BB%E7%9A%B1
 */
@Slf4j
@Service
public class XiutuService {

    private static final String HMAC_SHA512 = "HmacSHA512";
    private static final String MD5 = "MD5";

    //咻图AK/SK
    private static final String ak = "rg02eRAWfyNB";
    private static final String sk = "wONzmaHlsnwf";

    //注意这里需要用公网的endpoint，否则咻图任务结果无法回写
    @Value("${aliyun.oss.internetEndpoint}")
    String endpoint;

    @Value("${aliyun.oss.bucket}")
    String bucketName;

    @Value("${aliyun.oss.region}")
    String regionId;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OssService ossService;

    @Value("${app.custom.domain}")
    String domain;

    /**
     * 申请衣服去皱任务，异步写结果文件到oss，约5~8秒
     * 判断是否成功：可以用ossService#checkFileExists(ossFileKey)方法判断图片是否生成成功
     * @param picId 唯一id，用于标识生成的图片
     * @param originImgUrl 原图oss链接
     * @param retOssFileKey 返回的oss文件名（oss key，oss文件路径和名称，开头没有/）
     * @param strength 1-100，默认100，去皱的强度大小，1最小，100最大
     * @return 咻图的图片任务id
     */
    public boolean applyRemoveWrinkle(String picId, String originImgUrl, String retOssFileKey, Integer strength) {

        if (StringUtils.isBlank(picId)) {
            throw new IllegalArgumentException("picId is required");
        }

        if (StringUtils.isBlank(originImgUrl)) {
            throw new IllegalArgumentException("originImgUrl is required");
        }

        if (StringUtils.isBlank(retOssFileKey)) {
            throw new IllegalArgumentException("retOssFileKey is required");
        }

        //强度，缺省100
        if (strength == null) {
            strength = 100;
        }

        if (strength > 100 || strength < 1){
            throw new IllegalArgumentException("strength must be between 1 and 100");
        }

        try {
            //60分钟有效期的临时凭证
            StsCredentials sts = ossService.getStsCredentials(60 * 60L, false);

            JSONObject reqBody = new JSONObject();

            reqBody.put("id", picId);
            reqBody.put("path", originImgUrl);
            String fileName = IntegrationUtils.getFileNameFromUrl(originImgUrl);
            reqBody.put("name", fileName);
            reqBody.put("ext", IntegrationUtils.getFileExtensionFromPath(fileName));
            reqBody.put("packageVersion", 1);
            reqBody.put("retKey", retOssFileKey);

            if (!EnvUtil.isLocalEnv()) {
                reqBody.put("notify", domain + "/notify/xiutu");
            }

            // 创建 params 对象
            JSONObject params = new JSONObject();
            JSONObject flattenClothes = new JSONObject();
            flattenClothes.put("enable", true);
            flattenClothes.put("thinFold", strength);
            flattenClothes.put("thickFold", strength);
            params.put("flattenClothes", flattenClothes);
            params.put("version", 3);
            params.put("keepSize", true);
            reqBody.put("params", params);

            // 创建 auth 对象
            JSONObject auth = new JSONObject();
            auth.put("type", "aliyun");
            auth.put("bucket", bucketName);
            URL url = new URL(originImgUrl);
            auth.put("host", url.getProtocol() + "://" + url.getHost());
            auth.put("token", sts.securityToken);
            auth.put("accessKey", sts.accessKeyId);
            auth.put("accessSecret", sts.accessKeySecret);
            auth.put("regionId", regionId);
            auth.put("endpoint", endpoint);
            reqBody.put("auth", auth);

            // 请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String authContent = ak + ":" + getSignature(reqBody.toJSONString(), sk);
            headers.set("Authorization", "Basic " + Base64.getEncoder().encodeToString(authContent.getBytes()));

            // 封装请求
            HttpEntity<String> request = new HttpEntity<>(reqBody.toJSONString(), headers);

            // 发起 POST 请求
            // {"data":{"id":"8ca981e64ff84cf5b2b8f3d82efb3d42"},"status":200,"message":"成功","st":1737352609.8027,"crt":0.05997204780578613}
            ResponseEntity<String> response = restTemplate.exchange(
                    "https://control.aixtsy.com/api/sdk/photo/add",
                    HttpMethod.POST,
                    request,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && IntegrationUtils.isValidJsonObject(response.getBody())
                && JSONObject.parseObject(response.getBody()).getInteger("status") == 200) {
                return true;
            }

        } catch (Exception e) {
            log.error("请求服装去皱", e);
        }

        return false;
    }

    private String toHexString(byte[] bytes) {
        Formatter formatter = new Formatter();
        for (byte b : bytes) {
            formatter.format("%02x", b);
        }
        return formatter.toString();
    }

    private String md5(String str) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(MD5);
        return toHexString(md.digest(str.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * https://docs.aixtsy.com/tool/calc
     * @param data
     * @param secretKey
     * @return
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     */
    private String getSignature(String data, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {

        // 第一步，时间字符串(毫秒时间戳)
        // 当前毫秒时间换成16进制后补足前导0到12位.
        // 当前时间  -> 十六进制时间 -> 补足前导零到12位
        // 1627443206430 -> 17aeb2c991e -> 017aeb2c991e
        // 注意此处使用的小写16进制

        long millis = Instant.now().toEpochMilli();
        String hexTime = String.format("%1$012x", millis);
        // 第二步, 使用md5计算 时间摘要，注意此处包含`time_`前缀
        String encryptTime = md5("time_" + hexTime);
        // 第三步，签名计算数据
        byte[] payloadInBytes = (data + encryptTime).getBytes(StandardCharsets.UTF_8);
        // 第四步，签名计算密钥
        byte[] keyInBytes = md5("xtsy_" + secretKey).getBytes(StandardCharsets.UTF_8);

        // 最终步，使用SHA512计算签名字符串(注意最终返回签名值需要16进制时间戳放入最后哦)
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyInBytes, HMAC_SHA512);
        Mac mac = Mac.getInstance(HMAC_SHA512);
        mac.init(secretKeySpec);
        byte[] bytes = mac.doFinal(payloadInBytes);

        return toHexString(bytes) + hexTime;
    }
}

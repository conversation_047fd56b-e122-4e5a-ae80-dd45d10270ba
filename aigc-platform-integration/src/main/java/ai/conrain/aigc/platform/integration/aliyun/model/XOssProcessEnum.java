/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.aliyun.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * x-oss-process方法枚举
 *
 * <AUTHOR>
 * @version : XOssProcessEnum.java, v 0.1 2024/8/15 12:11 renxiao.wu Exp $
 */
@Getter
public enum XOssProcessEnum {
    RESIZE_800("image/auto-orient,1/resize,m_lfit,w_800,h_800/quality,q_99", "大边缩放到800以内"),
    RESIZE_1024("image/resize,m_lfit,l_1024", "大边缩放到1024以内"),
    RESIZE_AND_FORMAT("image/resize,m_lfit,w_400/quality,q_99/format,jpg", "重置大小并格式转换为jpg"),
    ;

    /** 枚举码 */
    private String code;

    /** 枚举描述 */
    private String desc;

    private XOssProcessEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static XOssProcessEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (XOssProcessEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}

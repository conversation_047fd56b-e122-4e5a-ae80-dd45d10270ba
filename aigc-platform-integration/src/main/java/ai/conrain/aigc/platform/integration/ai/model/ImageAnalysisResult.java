package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 图像分析结果模型
 * 简化版的ImageAnalysisCaptionModel
 */
@Data
public class ImageAnalysisResult {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务状态
     */
    private String status;

    /**
     * source
     */
    @JSONField(name = "source")
    private String source;

    /**
     * image_path
     */
    @JSONField(name = "image_path")
    private String imagePath;

    /**
     * 详细打标结果
     */
    private ImageAnalysisCaption analysis;

    /**
     * 简单（预）打标结果
     */
    private ImageSimpleCaption preCaption;

    /**
     * 错误消息（如果有）
     */
    private String errorMessage;

    /**
     * 分析结果原始数据
     */
    private String raw;

    //打标模式，默认是"detail"，简单打标模式是"simple"
    private String mode;
}
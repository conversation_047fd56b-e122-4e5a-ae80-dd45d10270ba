package ai.conrain.aigc.platform.integration.bytedance;

import ai.conrain.aigc.platform.integration.embedding.EmbeddingService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * https://www.volcengine.com/docs/82379/1523520
 */
@Slf4j
@Service
public class ByteDanceEmbeddingService implements EmbeddingService {

    private static final String MULTIMODAL_API_URL = "https://ark.cn-beijing.volces.com/api/v3/embeddings/multimodal";
    private static final String TEXT_API_URL = "https://ark.cn-beijing.volces.com/api/v3/embeddings";
    
    private String apiKey = "ff2af769-10cc-4222-8781-3573dfd9d1d6";

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;
    
    /**
     * 获取单个文本的向量
     * @param text 文本内容
     * @param dimension 向量维度
     * @return 向量结果
     */
    @Override
    public PGvector getEmbeddingByText(String text, int dimension) {
        List<PGvector> embeddings = this.getEmbeddingByTexts(Collections.singletonList(text), dimension);
        if (embeddings != null && !embeddings.isEmpty()) {
            return embeddings.get(0);
        }
        throw new RuntimeException("文本向量化失败");
    }

    /**
     * 获取多个文本的向量列表
     * @param texts 文本列表
     * @param dimension 向量维度
     * @return 向量结果列表
     */
    @Override
    public List<PGvector> getEmbeddingByTexts(List<String> texts, int dimension) {
        if (texts == null || texts.isEmpty()) {
            return Collections.emptyList();
        }

        if (dimension != 2056) {
            log.warn("ByteDanceEmbeddingService.getEmbeddingByText，火山云的文本向量服务不支持指定向量长度，只能为2056维");
        }

        // 构建请求体
        TextEmbeddingRequest request = new TextEmbeddingRequest();
        request.model = "doubao-embedding-text-240715";
        request.input = texts;
        request.encodingFormat = "float";
        
        // 构建HTTP请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", "Bearer " + apiKey);
        
        // 序列化请求体
        String requestBody = JSON.toJSONString(request);
        
        // 构建请求实体
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
                TEXT_API_URL, HttpMethod.POST, entity, String.class);
        
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("API request failed with status: " + response.getStatusCode() + 
                    ", body: " + response.getBody());
        }
        
        // 解析响应并转换为PGvector列表
        TextEmbeddingResponse embeddingResponse = JSON.parseObject(response.getBody(), TextEmbeddingResponse.class);
        if (embeddingResponse != null && embeddingResponse.data != null && !embeddingResponse.data.isEmpty()) {
            return embeddingResponse.data.stream()
                    .map(data -> {
                        if (data.embedding != null && data.embedding.length > 0) {
                            return new PGvector(data.embedding);
                        }
                        throw new RuntimeException("Empty embedding vector in response");
                    })
                    .collect(Collectors.toList());
        }
        throw new RuntimeException("Failed to extract embedding vectors from response");
    }

    /**
     * 多模态embedding计算文本向量
     * @param text 文本内容
     * @return 向量结果
     */
    @Override
    public PGvector getTextEmbeddingByMultiModalModel(String text) {
        return getTextImageEmbedding(text, null, null, null);
    }

    /**
     * 多模态embedding计算图像向量
     * @param imgUrl 图像URL
     * @return 向量结果
     */
    @Override
    public PGvector getImgEmbeddingByMultiModalModel(String imgUrl) {
        return getImageEmbedding(imgUrl, null, null);
    }

    /**
     * 多模态向量化处理
     * @param inputContents 输入内容列表，每个元素包含类型和数据
     * @return 向量结果列表
     */
    @Override
    public List<PGvector> getEmbeddingsByMultiModalModel(List<Pair<String, String>> inputContents) {
        if (inputContents == null || inputContents.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<PGvector> results = new ArrayList<>();
        for (Pair<String, String> content : inputContents) {
            String type = content.getLeft();
            String data = content.getRight();
            
            if ("text".equals(type)) {
                results.add(getTextEmbeddingByMultiModalModel(data));
            } else if ("image".equals(type)) {
                results.add(getImgEmbeddingByMultiModalModel(data));
            } else {
                throw new UnsupportedOperationException("不支持的内容类型: " + type);
            }
        }
        return results;
    }

    /**
     * 获取图像向量化结果
     * @param imageUrl 图像URL
     * @param model 模型名称，默认为 doubao-embedding-vision-250615
     * @param dimensions 向量维度，默认为1024
     * @return PGvector向量结果
     */
    public PGvector getImageEmbedding(String imageUrl, String model, Integer dimensions) {
        
        if (model == null) {
            model = "doubao-embedding-vision-250615";
        }
        if (dimensions == null) {
            dimensions = 1024;
        }
        
        // 构建请求体
        EmbeddingRequest request = new EmbeddingRequest();
        request.model = model;
        request.encodingFormat = "float";
        request.dimensions = dimensions;
        
        // 构建图像输入
        InputItem inputItem = new InputItem();
        inputItem.type = "image_url";
        inputItem.imageUrl = new ImageUrl();
        inputItem.imageUrl.url = imageUrl;
        
        request.input = List.of(inputItem);
        
        // 构建HTTP请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", "Bearer " + apiKey);
        
        // 序列化请求体
        String requestBody = JSON.toJSONString(request);
        
        // 构建请求实体
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
                MULTIMODAL_API_URL, HttpMethod.POST, entity, String.class);
        
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("API request failed with status: " + response.getStatusCode() + 
                    ", body: " + response.getBody());
        }
        
        // 解析响应并转换为PGvector
        EmbeddingResponse embeddingResponse = JSON.parseObject(response.getBody(), EmbeddingResponse.class);
        if (embeddingResponse != null && embeddingResponse.data != null && !embeddingResponse.data.isEmpty()) {
            List<Float> embedding = embeddingResponse.data.get(0).embedding;
            if (embedding != null && !embedding.isEmpty()) {
                float[] vectorArray = new float[embedding.size()];
                for (int i = 0; i < embedding.size(); i++) {
                    vectorArray[i] = embedding.get(i);
                }
                return new PGvector(vectorArray);
            }
        }
        throw new RuntimeException("Failed to extract embedding vector from response");
    }
    
    /**
     * 获取文本和图像混合向量化结果
     * @param text 文本内容
     * @param imageUrl 图像URL
     * @param model 模型名称
     * @param dimensions 向量维度
     * @return PGvector向量结果
     */
    public PGvector getTextImageEmbedding(String text, String imageUrl, String model, Integer dimensions) {
        
        if (model == null) {
            model = "doubao-embedding-vision-250615";
        }
        if (dimensions == null) {
            dimensions = 1024;
        }
        
        // 构建请求体
        EmbeddingRequest request = new EmbeddingRequest();
        request.model = model;
        request.encodingFormat = "float";
        request.dimensions = dimensions;
        
        List<InputItem> inputItems = new ArrayList<>();
        
        // 如果有文本内容，添加文本输入
        if (StringUtils.isNotBlank(text)) {
            InputItem textInput = new InputItem();
            textInput.type = "text";
            textInput.text = text;
            inputItems.add(textInput);
        }
        
        // 如果有图像URL，添加图像输入
        if (StringUtils.isNotBlank(imageUrl)) {
            InputItem imageInput = new InputItem();
            imageInput.type = "image_url";
            imageInput.imageUrl = new ImageUrl();
            imageInput.imageUrl.url = imageUrl;
            inputItems.add(imageInput);
        }
        
        if (inputItems.isEmpty()) {
            throw new IllegalArgumentException("文本和图像URL不能同时为空");
        }
        
        request.input = inputItems;
        
        // 构建HTTP请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", "Bearer " + apiKey);
        
        // 序列化请求体
        String requestBody = JSON.toJSONString(request);
        
        // 构建请求实体
        HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.exchange(
                MULTIMODAL_API_URL, HttpMethod.POST, entity, String.class);
        
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("API request failed with status: " + response.getStatusCode() + 
                    ", body: " + response.getBody());
        }
        
        // 解析响应并转换为PGvector
        EmbeddingResponse embeddingResponse = JSON.parseObject(response.getBody(), EmbeddingResponse.class);
        if (embeddingResponse != null && embeddingResponse.data != null && !embeddingResponse.data.isEmpty()) {
            List<Float> embedding = embeddingResponse.data.get(0).embedding;
            if (embedding != null && !embedding.isEmpty()) {
                float[] vectorArray = new float[embedding.size()];
                for (int i = 0; i < embedding.size(); i++) {
                    vectorArray[i] = embedding.get(i);
                }
                return new PGvector(vectorArray);
            }
        }
        throw new RuntimeException("Failed to extract embedding vector from response");
    }
    
    // 文本向量化请求体类
    public static class TextEmbeddingRequest {
        public String model;
        
        public List<String> input;
        
        @JSONField(name = "encoding_format")
        public String encodingFormat;
    }
    
    // 文本向量化响应体类
    public static class TextEmbeddingResponse {
        public String id;
        public String model;
        public Long created;
        public String object;
        public List<TextEmbeddingData> data;
        public Usage usage;
    }
    
    // 文本向量化数据类
    public static class TextEmbeddingData {
        public Integer index;
        public float[] embedding;
        public String object;
    }
    
    // 多模态向量化请求体类
    public static class EmbeddingRequest {
        public String model;
        
        @JSONField(name = "encoding_format")
        public String encodingFormat;
        
        public Integer dimensions;
        
        public List<InputItem> input;
    }
    
    // 输入项类
    public static class InputItem {
        public String type;
        
        public String text;
        
        @JSONField(name = "image_url")
        public ImageUrl imageUrl;
        
        @JSONField(name = "video_url")
        public VideoUrl videoUrl;
    }
    
    // 图像URL类
    public static class ImageUrl {
        public String url;
    }
    
    // 视频URL类
    public static class VideoUrl {
        public String url;
    }
    
    // 响应类
    public static class EmbeddingResponse {
        public String id;
        public String model;
        public Long created;
        public String object;
        public List<EmbeddingData> data;
        public Usage usage;
    }
    
    // 向量数据类
    public static class EmbeddingData {
        public List<Float> embedding;
        public String object;
    }
    
    // 使用量统计类
    public static class Usage {
        @JSONField(name = "prompt_tokens")
        public Integer promptTokens;
        
        @JSONField(name = "total_tokens")
        public Integer totalTokens;
        
        @JSONField(name = "prompt_tokens_details")
        public PromptTokensDetails promptTokensDetails;
    }
    
    // 提示词token详情类
    public static class PromptTokensDetails {
        @JSONField(name = "text_tokens")
        public Integer textTokens;
        
        @JSONField(name = "image_tokens")
        public Integer imageTokens;
        
        @JSONField(name = "video_tokens")
        public Integer videoTokens;
    }
}

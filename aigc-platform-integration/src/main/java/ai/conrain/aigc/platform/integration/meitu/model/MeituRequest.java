package ai.conrain.aigc.platform.integration.meitu.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 美图API请求模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class MeituRequest {

    /**
     * 多媒体信息列表
     */
    @JSONField(name = "media_info_list")
    private List<MediaInfo> mediaInfoList;

    /**
     * 处理参数
     */
    @JSONField(name = "parameter")
    private Parameter parameter;

    /**
     * 额外信息
     */
    @JSONField(name = "extra")
    private Object extra;

    /**
     * 多媒体信息
     */
    @Data
    public static class MediaInfo {
        /**
         * 多媒体数据（base64编码或URL）
         */
        @JSONField(name = "media_data")
        private String mediaData;

        /**
         * 多媒体额外信息
         */
        @JSONField(name = "media_extra")
        private Object mediaExtra;

        /**
         * 多媒体属性信息
         */
        @JSONField(name = "media_profiles")
        private MediaProfiles mediaProfiles;
    }

    /**
     * 多媒体属性信息
     */
    @Data
    public static class MediaProfiles {
        /**
         * 数据类型：jpg/url
         */
        @JSONField(name = "media_data_type")
        private String mediaDataType = "jpg";
    }

    /**
     * 处理参数
     */
    @Data
    public static class Parameter {
        /**
         * 响应媒体类型
         */
        @JSONField(name = "rsp_media_type")
        private String rspMediaType = "jpg";

        /**
         * 去皱程度（0-100）
         */
        @JSONField(name = "alpha")
        private Integer alpha = 100;
    }
}
package ai.conrain.aigc.platform.integration.kling;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * https://piapi.ai/docs/kling-api/create-task
 */
@Data
public class KlingTaskResponseBody {
    private long code;

    @JSONField(name = "data")
    private ResponseData responseData;
    /**
     * If you get non-null error message, here are some steps you chould follow:
     * - Check our [common error
     * message](https://climbing-adapter-afb.notion.site/Common-Error-Messages-6d108f5a8f644238b05ca50d47bbb0f4)
     * - Retry for several times
     * - If you have retried for more than 3 times and still not work, file a ticket on Discord
     * and our support will be with you soon.
     */
    private String message;

    @Data
    public static class ResponseData {
        private Object detail;
        private JSONObject error;
        private Map<String, Object> input;
        private List<Map<String, Object>> logs;
        private JSONObject meta;
        private String model;
        private JSONObject output;
        /**
         * completed/processing/pending/failed/staged
         */
        private String status;
        @JSONField(name = "task_id")
        private String taskId;
        @JSONField(name = "task_type")
        private String taskType;
    }
}


package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
@Service
public class ImageQualityApiService {

    @Value("${ai.imageQuality.url}")
    private String BASE_URL;

    @Value("${ai.imageQuality.apiKey}")
    private String API_KEY;

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 调用图像质量分类API
     * @param imageUrl 图像URL
     * @return high_quality的置信度值
     */
    public Double classifyImageQuality(String imageUrl) {
        try {
            // 构建请求URL
            String url = BASE_URL + "/classify_quality";
            
            // 构建请求体
            ImageQualityRequest request = new ImageQualityRequest();
            request.setImageUrl(imageUrl);
            request.setApiKey(API_KEY);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建HTTP实体
            HttpEntity<ImageQualityRequest> entity = new HttpEntity<>(request, headers);
            
            // 发送POST请求
            ResponseEntity<ImageQualityResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, ImageQualityResponse.class);
            
            ImageQualityResponse responseBody = response.getBody();
            if (responseBody != null && responseBody.getClassification() != null 
                && responseBody.getClassification().getProbabilities() != null) {
                Double highQualityScore = responseBody.getClassification().getProbabilities().get("high_quality");
                log.info("图像质量分析完成，图像URL: {}, high_quality置信度: {}", imageUrl, highQualityScore);
                return highQualityScore;
            }
            
            log.warn("图像质量分析响应异常，图像URL: {}", imageUrl);
            return null;
            
        } catch (Exception e) {
            log.error("调用图像质量分析API失败，图像URL: {}", imageUrl, e);
            return null;
        }
    }
    
    /**
     * 图像质量分析请求模型
     */
    @Data
    public static class ImageQualityRequest {
        @JsonProperty("image_url")
        private String imageUrl;
        @JsonProperty("api_key")
        private String apiKey;
    }
    
    /**
     * 图像质量分析响应模型:
     * {
     *     "classification":
     *     {
     *         "confidence": 0.8242912888526917,
     *         "predicted_class_id": 1,
     *         "predicted_class_name": "high_quality",
     *         "probabilities":
     *         {
     *             "high_quality": 0.8242912888526917,
     *             "low_quality": 0.17570871114730835
     *         }
     *     },
     *     "status": "success"
     * }
     */
    @Data
    public static class ImageQualityResponse {
        private String status;
        private Classification classification;
        
        @Data
        public static class Classification {
            private Double confidence;
            @JsonProperty("predicted_class_id")
            private Integer predictedClassId;
            @JsonProperty("predicted_class_name")
            private String predictedClassName;
            private Map<String, Double> probabilities;
        }
    }
}

package ai.conrain.aigc.platform.integration.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxUser extends BaseResponse {

    private static final long serialVersionUID = 868745406639298891L;

    @JSONField(name = "openid")
    private String openId;

    @JSONField(name = "unionid")
    private String unionId;
}

package ai.conrain.aigc.platform.integration.alipay;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.utils.EnvUtil;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AlipayService {

    @Autowired
    private AlipayClient alipayClient;

    @Value("${alipay.notifyUrl}")
    private String notifyUrl;

    /**
     * 订单码支付-预创建，得到二维码url alipay.trade.precreate 2小时有效期
     * https://opendocs.alipay.com/open/8ad49e4a_alipay.trade.precreate?referPath=05osuz_fa169dd3
     */
    public String tradePreCreate(String outTradeNo, String totalAmount) {

        if (StringUtils.isBlank(outTradeNo) || StringUtils.isBlank(totalAmount)) {
            log.error("调用支付宝接口失败，参数错误，outTradeNo={}, totalAmount={}", outTradeNo, totalAmount);
            return null;
        }

        try {
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            if (!EnvUtil.isLocalEnv()) {
                request.setNotifyUrl(notifyUrl);
            }

            AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();

            // 设置商户订单号
            model.setOutTradeNo(outTradeNo);

            // 设置订单总金额
            model.setTotalAmount(totalAmount);

            // 设置支付宝收银台的订单标题，使用英文描述，避免回调通知中签名乱码
            model.setSubject("MuseGate");

            // 设置产品码
            model.setProductCode("QR_CODE_OFFLINE");

            request.setBizModel(model);

            log.info("支付宝预创建接口请求：{},{}", outTradeNo, JSONObject.toJSONString(request));
            AlipayTradePrecreateResponse res = alipayClient.execute(request);
            log.info("支付宝预创建接口返回结果：{},{}", outTradeNo, JSONObject.toJSONString(res));

            if (res != null && res.isSuccess() && StringUtils.isNotBlank(res.getQrCode())) {
                return res.getQrCode();
            } else if (res != null) {
                log.error("调用支付宝接口失败，错误码：{}，错误信息：{}", res.getCode(), res.getMsg());
            }

        } catch (Exception e) {
            log.error("调用支付宝接口失败", e);
        }

        return null;
    }

    /**
     * 支付状态查询  alipay.trade.query
     * https://opendocs.alipay.com/open/217dd591_alipay.trade.query?referPath=05osuz_fa169dd3
     *
     * 返回示例：
     * {
     *   "body": "{\"alipay_trade_query_response\":{\"code\":\"10000\",\"msg\":\"Success\",\"buyer_logon_id\":\"jac***@foxmail.com\",\"buyer_pay_amount\":\"0.01\",\"fund_bill_list\":[{\"amount\":\"0.01\",\"fund_channel\":\"ALIPAYACCOUNT\"}],\"invoice_amount\":\"0.01\",\"out_trade_no\":\"********\",\"point_amount\":\"0.00\",\"receipt_amount\":\"0.01\",\"send_pay_date\":\"2025-06-16 20:09:45\",\"total_amount\":\"0.01\",\"trade_no\":\"2025061622001432651414093531\",\"trade_status\":\"TRADE_SUCCESS\",\"buyer_open_id\":\"0659XHpWHKl5y7NZVS5lJ1Xt_Ub9mbV1std_aTyYsRYVpo7\"},\"sign\":\"N3suhix5ZPDZB7J/CxWYKx6Zsgv0dZhSUqTp4/HWWUWKbr57IvQb00jltXNEP6C1iYb6svj1dGIBXDdRFyfJUDA1LnOA+muNnsGBZulc8NUei1blTZVRTc5jg+Ecl26Wbxx6Z1229FswV/qc2khXMcGr+0FgyNWDAIWf3tFU/Wwx4hwBIlhZA+N8mnCbwcctbFIfe5vZ+1j0zEpPMsQ5INIfQyXO4Ec465ra16hJicHialzbDTQWSkz+JVRzQ1CXS1GLtakHWMp7g8dsPZUK21dUyyuGU3Fc92mldN2gZC7MfJNfKRN0nqdY3vg7253XqTGrc79vvOqutfBgb9H62g==\"}",
     *   "buyerLogonId": "jac***@foxmail.com",
     *   "buyerOpenId": "0659XHpWHKl5y7NZVS5lJ1Xt_Ub9mbV1std_aTyYsRYVpo7",
     *   "buyerPayAmount": "0.01",
     *   "code": "10000",
     *   "errorCode": "10000",
     *   "fundBillList": [
     *     {
     *       "amount": "0.01",
     *       "fundChannel": "ALIPAYACCOUNT"
     *     }
     *   ],
     *   "invoiceAmount": "0.01",
     *   "msg": "Success",
     *   "outTradeNo": "********",
     *   "params": {
     *     "traceId": "21d63d3417500759562192364ed15a",
     *     "biz_content": "{\"out_trade_no\":\"********\"}"
     *   },
     *   "pointAmount": "0.00",
     *   "receiptAmount": "0.01",
     *   "sendPayDate": *************,
     *   "success": true,
     *   "totalAmount": "0.01",
     *   "tradeNo": "2025061622001432651414093531",
     *   "tradeStatus": "TRADE_SUCCESS"
     * }
     */
    public AlipayTradeQueryResponse tradeQuery(String outTradeNo) {

        if (StringUtils.isBlank(outTradeNo)) {
            log.error("调用支付宝接口失败，参数错误，outTradeNo={}", outTradeNo);
            return null;
        }

        try {
            // 构造请求参数以调用接口
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            AlipayTradeQueryModel model = new AlipayTradeQueryModel();

            // 设置订单支付时传入的商户订单号
            model.setOutTradeNo(outTradeNo);
            request.setBizModel(model);

            AlipayTradeQueryResponse res = alipayClient.execute(request);
            log.info("支付宝支付查询接口返回结果：{},{}", outTradeNo, JSONObject.toJSONString(res));

            return res;
        } catch (Exception e) {
            log.error("调用支付宝接口失败", e);
        }
        return null;
    }
}

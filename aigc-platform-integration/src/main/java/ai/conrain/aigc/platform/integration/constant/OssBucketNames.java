package ai.conrain.aigc.platform.integration.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class OssBucketNames {
    public static final String ONLINE = "aigc-platform-online";
    public static final String DEV = "aigc-platform-dev";
    public static final String DATA_LABEL = "mg-data-label";

    public static final List<String> MG_BUCKETS = List.of(ONLINE, DEV, DATA_LABEL);
    public static boolean isMGBucket(String bucketName){
        return StringUtils.isNotBlank(bucketName) && MG_BUCKETS.contains(bucketName);
    }
}

package ai.conrain.aigc.platform.integration.kling;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * https://302ai.apifox.cn/api-246758657
 */
@Slf4j
@Service
public class VideoGeneratorService302API implements KlingProxy {
    private static final String API_URL_5s = "https://api.302.ai/klingai/m2v_16_img2video_hq_5s";
    private static final String API_URL_10s = "https://api.302.ai/klingai/m2v_16_img2video_hq_10s";

    public static final String BEARER_TOKEN = "sk-jP3mtRGTuLfLPya3ml8LSkh4sRmw8auW8IvjSFeodyHwQsOH";

    @Autowired
    private OssService ossService;

    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate superLongRestTemplate;

    /**
     * 生成视频
     */
    public String createVideoTask(KlingTaskParams params) {
        if (params == null || StringUtils.isBlank(params.getPrompt()) || StringUtils.isBlank(params.getImageUrl())) {
            throw new IllegalArgumentException("prompt or imageUrl is null");
        }

        //缺省值
        if (params.getDuration() == null) {
            params.setDuration(5);
        }

        if (StringUtils.isBlank(params.getMode())) {
            params.setMode("pro");
        }

        if (StringUtils.isBlank(params.getVersion())) {
            params.setVersion("1.6");
        }

        try {
            // 构建多部分表单数据
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("input_image", params.getImageUrl());
            body.add("prompt", params.getPrompt());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.setBearerAuth(BEARER_TOKEN);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<String> response = superLongRestTemplate.exchange(
                    params.getDuration() == 5 ? API_URL_5s : API_URL_10s,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("kling 302 createVideoTask response:{}", response.getBody());

            // 处理响应
            if (response.getStatusCode().is2xxSuccessful() && IntegrationUtils.isValidJsonObject(response.getBody())) {
                return JSONObject.parseObject(response.getBody()).getJSONObject("data").getJSONObject("task").getString("id");
            } else {
                throw new RuntimeException("API请求失败，状态码：" + response.getStatusCodeValue());
            }
        } catch (Throwable e) {
            log.error("视频生成失败：", e);
        }

        return null;
    }

    public KlingTaskRet getVideoTask(String taskId){

        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId is null");
        }

        String url = String.format("https://api.302.ai/klingai/task/%s/fetch", taskId);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(BEARER_TOKEN);

        // 封装请求
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            // 发起 GET 请求
            ResponseEntity<String> response = superLongRestTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    request,
                    String.class
            );

            log.info("kling 302 getVideoTask response:{}", response.getBody());

            KlingTaskRet ret = new KlingTaskRet();

            if (response.getStatusCode().is2xxSuccessful() && IntegrationUtils.isValidJsonObject(response.getBody())) {
                //data.works[0].status：int，5是排队，10是处理中，50是失败，99是成功
                //data.works[0].resource.resource：string，文件url

                if (!CollectionUtils.isEmpty(JSONObject.parseObject(response.getBody()).getJSONObject("data").getJSONArray("works"))) {
                    JSONObject work = JSONObject.parseObject(response.getBody()).getJSONObject("data").getJSONArray("works").getJSONObject(0);
                    Integer statusCode = JSONObject.parseObject(response.getBody()).getJSONObject("data").getInteger("status");
                    KlingTaskStatusEnum status = fromStatusCode(statusCode);
                    ret.setStatus(status);

                    if (status == KlingTaskStatusEnum.completed) {
                        String tmpVideoUrl = work.getJSONObject("resource").getString("resource");
                        ret.setOutVideoUrl(tmpVideoUrl);
                        ret.setOssVideoUrl(ossService.fetchStreamAndUpload(tmpVideoUrl));
                    }

                    return ret;
                }
            }

        } catch (Exception e) {
            log.error("请求可灵失败", e);
        }
        return null;
    }

    //获取代理账号余额
    public Float getAccountBalanceUSD(){

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(BEARER_TOKEN);

        // 封装请求
        HttpEntity<String> request = new HttpEntity<>(headers);
        try {
            // 发起 GET 请求
            ResponseEntity<String> response = superLongRestTemplate.exchange(
                    "https://api.302.ai/dashboard/balance",
                    HttpMethod.GET,
                    request,
                    String.class
            );

            //{"data":{"balance":"2.50"}}
            if (IntegrationUtils.isValidJsonObject(response.getBody())) {
                JSONObject body = JSONObject.parseObject(response.getBody());
                if (body != null && body.getJSONObject("data") != null && body.getJSONObject("data").containsKey("balance")) {
                    Float amount = Float.valueOf(body.getJSONObject("data").getString("balance"));
                    if (amount != null) {
                        return amount;
                    }
                }
            }

        } catch (Exception e) {
            log.error("请求可灵获取账号信息失败", e);
        }
        return null;
    }

    private KlingTaskStatusEnum fromStatusCode(Integer statusCode) {
        if (statusCode == null) {
            throw new IllegalArgumentException("statusCode is null");
        }
        KlingTaskStatusEnum status = null;
        switch (statusCode) {
            case 5:
                status = KlingTaskStatusEnum.pending;
                break;
            case 10:
                status = KlingTaskStatusEnum.processing;
                break;
            case 50:
                status = KlingTaskStatusEnum.failed;
                break;
            case 99:
                status = KlingTaskStatusEnum.completed;
                break;
            default:
                throw new RuntimeException("302api statusCode is not valid:" + statusCode);
        }
        return status;
    }
}
package ai.conrain.aigc.platform.integration.embedding;

import com.pgvector.PGvector;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 向量化服务接口
 * 定义统一的向量化服务方法，支持文本和多模态向量化
 */
public interface EmbeddingService {

    /**
     * 获取单个文本的向量
     * @param text 文本内容
     * @param dimension 向量维度
     * @return 向量结果
     */
    PGvector getEmbeddingByText(String text, int dimension);

    /**
     * 获取多个文本的向量列表
     * @param texts 文本列表
     * @param dimension 向量维度
     * @return 向量结果列表
     */
    List<PGvector> getEmbeddingByTexts(List<String> texts, int dimension);

    /**
     * 多模态embedding计算文本向量
     * @param text 文本内容
     * @return 向量结果
     */
    PGvector getTextEmbeddingByMultiModalModel(String text);

    /**
     * 多模态embedding计算图像向量
     * @param imgUrl 图像URL
     * @return 向量结果
     */
    PGvector getImgEmbeddingByMultiModalModel(String imgUrl);

    /**
     * 多模态向量化处理
     * @param inputContents 输入内容列表，每个元素包含类型和数据
     * @return 向量结果列表
     */
    List<PGvector> getEmbeddingsByMultiModalModel(List<Pair<String, String>> inputContents);
}
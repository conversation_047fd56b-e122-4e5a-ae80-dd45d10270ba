package ai.conrain.aigc.platform.integration.kling;

public enum KlingTaskStatusEnum {
    completed,processing,pending,failed;

    public static KlingTaskStatusEnum getByName(String name) {
        for (KlingTaskStatusEnum status : KlingTaskStatusEnum.values()) {
            if (status.name().equalsIgnoreCase(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException("KlingTaskStatusEnum not found for name: " + name);
    }
}

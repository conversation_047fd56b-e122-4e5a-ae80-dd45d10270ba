package ai.conrain.aigc.platform.integration.utils;

/**
 * 校验工具类
 */
public class HttpVerifyUtils {

    /**
     * 拼装服务地址+路径
     *
     * @param server      服务地址
     * @param address 路径
     */
    public static String assembleIpAddress(String server, String address) {
        // 拼接服务地址和路径
        String ipWithProtocol = ensureHttpPrefix(server);
        String addressWithLeadingSlash = ensureLeadingSlash(address);

        // 构建拼接后的地址
        StringBuilder sb = new StringBuilder();
        sb.append(ipWithProtocol);
        if (!addressWithLeadingSlash.startsWith("/")) {
            sb.append("/");
        }
        sb.append(addressWithLeadingSlash);

        // 返回拼接后的地址
        return sb.toString();
    }

    /**
     * 确保 URL 以 http 或 https 开头
     *
     * @param server 服务地址
     */
    public static String ensureHttpPrefix(String server) {
        if (server == null || server.isEmpty()) {
            return null;
        }

        if (server.toLowerCase().startsWith("http://") || server.toLowerCase().startsWith("https://")) {
            return server;
        }

        return "http://" + server;
    }

    /**
     * 确保路径以 / 开头
     *
     * @param path 路径
     */
    public static String ensureLeadingSlash(String path) {
        if (path == null || path.isEmpty()) {
            return "/";
        }
        return path.startsWith("/") ? path : "/" + path;
    }

}

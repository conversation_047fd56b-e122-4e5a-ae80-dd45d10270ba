package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import org.apache.commons.lang3.StringUtils;

public class SearchUtil {
    public static boolean isValidImageAnalysisCloth(ImageAnalysisCaption analysis) {
        if (analysis != null && analysis.getClothing() != null) {
            boolean validTop = analysis.getClothing().getTop() != null && StringUtils.isNotBlank(analysis.getClothing().getTop().getStyle());
            boolean validBottom = analysis.getClothing().getBottom() != null && StringUtils.isNotBlank(analysis.getClothing().getBottom().getStyle());

            return validTop || validBottom;
        }

        return false;
    }
}

package ai.conrain.aigc.platform.integration.gpt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Options for AI service requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GptOptions {
    
    /**
     * The AI provider to use (e.g., "openai", "azure")
     */
    private String provider;
    
    /**
     * The model to use (e.g., "gpt-4", "gpt-4o")
     */
    private String model;
    
    /**
     * Temperature for response generation (0.0 to 1.0)
     */
    @Builder.Default
    private double temperature = 0.1;
    
    /**
     * Top_p sampling parameter (0.0 to 1.0)
     */
    @Builder.Default
    private double topP = 0.95;
    
    /**
     * Maximum tokens to generate in the response
     */
    @Builder.Default
    private int maxTokens = 1000;
}

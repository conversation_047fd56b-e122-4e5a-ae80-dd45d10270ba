/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.ObjectPool;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

/**
 * 字节池工具类
 *
 * <AUTHOR>
 * @version : BytePoolUtils.java, v 0.1 2024/6/20 11:18 renxiao.wu Exp $
 */
@Slf4j
public abstract class BytePoolUtils {
    private static final int BUFFER_SIZE = 4 * 1024;

    private static final ObjectPool<byte[]> pool = new GenericObjectPool<>(new ByteArrayFactory(BUFFER_SIZE),
        getConfig());

    private static GenericObjectPoolConfig<byte[]> getConfig() {
        GenericObjectPoolConfig<byte[]> config = new GenericObjectPoolConfig<>();
        //一台机器上同时使用连接池的数量，暂定50，图片下载+zip下载，预估最大50个端口同时下载图片
        config.setMaxTotal(40);
        return config;
    }

    /**
     * 使用池获取字节数组
     *
     * @return 字节流
     * @throws Exception 异常
     */
    public static byte[] borrow() throws Exception {
        return pool.borrowObject();
    }

    /**
     * 归还字节数组
     *
     * @param array 字节流
     */
    public static void returnArray(byte[] array) {
        try {
            pool.returnObject(array);
        } catch (Exception e) {
            log.error("return array error", e);
        }
    }

    public static class ByteArrayFactory implements PooledObjectFactory<byte[]> {
        private final int size;

        public ByteArrayFactory(int size) {
            this.size = size;
        }

        @Override
        public PooledObject<byte[]> makeObject() {
            return new DefaultPooledObject<>(new byte[size]);
        }

        @Override
        public void destroyObject(PooledObject<byte[]> pooledObject) {
            // No specific destruction steps needed for byte arrays
        }

        @Override
        public boolean validateObject(PooledObject<byte[]> pooledObject) {
            return pooledObject.getObject() != null;
        }

        @Override
        public void activateObject(PooledObject<byte[]> pooledObject) {
            // No specific activation steps needed for byte arrays
        }

        @Override
        public void passivateObject(PooledObject<byte[]> pooledObject) {
            // No specific passivation steps needed for byte arrays
        }
    }
}

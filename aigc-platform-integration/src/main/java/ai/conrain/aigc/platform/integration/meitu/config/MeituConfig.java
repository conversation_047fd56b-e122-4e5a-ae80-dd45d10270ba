package ai.conrain.aigc.platform.integration.meitu.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 美图AI服务配置类
 * 
 * <AUTHOR> Assistant
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "meitu")
public class MeituConfig {

    /**
     * API配置
     */
    private Api api = new Api();

    /**
     * 认证配置
     */
    private Auth auth = new Auth();

    /**
     * API配置内部类
     */
    @Data
    public static class Api {
        /**
         * API基础URL
         */
        private String url;

        /**
         * 端点配置映射
         */
        private Map<String, String> endpoints;
    }

    /**
     * 认证配置内部类
     */
    @Data
    public static class Auth {
        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appKey;

        /**
         * 密钥ID
         */
        private String secretId;
    }

    /**
     * 获取去皱端点URL
     * 
     * @return 去皱端点路径
     */
    public String getRemoveWrinkleEndpoint() {
        return api.endpoints != null ? api.endpoints.get("removeWrinkle") : "/v1/mtrmwrink";
    }

    /**
     * 获取完整的API基础URL
     * 
     * @return 完整的API基础URL
     */
    public String getBaseUrl() {
        return api.url != null ? api.url : "https://openapi.meitu.com/api";
    }
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.model.LabelApplyRequest;
import ai.conrain.aigc.platform.integration.ai.model.LabelStatusInfo;

/**
 * 打标任务服务
 *
 * <AUTHOR>
 * @version : LabelTaskService.java, v 0.1 2025/8/22 00:03 renxiao.wu Exp $
 */
public interface LabelTaskService {
    /**
     * 创建打标任务
     *
     * @param request 请求
     * @param url     url
     * @return 创建结果
     */
    LabelStatusInfo apply(LabelApplyRequest request, String url);

    /**
     * 查询打标任务
     *
     * @param outTaskId 外部任务id
     * @param taskType  任务类型
     * @param url       url
     * @return 任务状态
     */
    LabelStatusInfo query(Integer outTaskId, String taskType, String url);

    /**
     * json转prompt
     *
     * @param json json
     * @return prompt
     */
    String jsonToPrompt(JSONObject json);

}

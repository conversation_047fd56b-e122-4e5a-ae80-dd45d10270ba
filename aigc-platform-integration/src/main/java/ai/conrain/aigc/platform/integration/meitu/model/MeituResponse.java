package ai.conrain.aigc.platform.integration.meitu.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 美图API响应模型
 * 
 * <AUTHOR> Assistant
 */
@Data
public class MeituResponse {

    /**
     * 多媒体信息列表
     */
    @JSONField(name = "media_info_list")
    private List<MediaInfo> mediaInfoList;

    /**
     * 返回参数
     */
    @JSONField(name = "parameter")
    private Parameter parameter;

    /**
     * 错误码（成功时为0，失败时为非0）
     */
    @JSONField(name = "error_code")
    private Integer errorCode;

    /**
     * 错误信息（成功时为"success"，失败时为具体错误信息）
     */
    @JSONField(name = "message")
    private String errorMsg;

    /**
     * 额外信息
     */
    @JSONField(name = "extra")
    private Object extra;

    /**
     * 持续时间信息
     */
    @JSONField(name = "duration")
    private Object duration;

    /**
     * 具体信息（失败时）
     */
    @JSONField(name = "Data")
    private String data;

    /**
     * 多媒体信息
     */
    @Data
    public static class MediaInfo {
        /**
         * 处理后的多媒体数据（base64编码）
         */
        @JSONField(name = "media_data")
        private String mediaData;

        /**
         * 多媒体属性信息
         */
        @JSONField(name = "media_profiles")
        private MediaProfiles mediaProfiles;
    }

    /**
     * 多媒体属性信息
     */
    @Data
    public static class MediaProfiles {
        /**
         * 数据类型
         */
        @JSONField(name = "media_data_type")
        private String mediaDataType;
    }

    /**
     * 返回参数
     */
    @Data
    public static class Parameter {
        /**
         * 响应媒体类型
         */
        @JSONField(name = "rsp_media_type")
        private String rspMediaType;
    }

    /**
     * 判断是否成功
     * 
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return errorCode != null && errorCode == 0;
    }
}
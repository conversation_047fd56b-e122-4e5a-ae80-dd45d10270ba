package ai.conrain.aigc.platform.integration.aliyun.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliyunClientConfig {

    @Value("${aliyun.api.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.api.accessKeySecret}")
    private String accessKeySecret;

    // Endpoint 请参考 https://api.aliyun.com/product/ICE
    @Value("${aliyun.ice.endpoint}")
    private String iceEndpoint;

    // Endpoint 请参考 https://api.aliyun.com/product/imm
    @Value("${aliyun.imm.endpoint}")
    private String immEndpoint;

    @Bean("iceClient")
    public com.aliyun.ice20201109.Client iceClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config().setEndpoint(iceEndpoint)
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        return new com.aliyun.ice20201109.Client(config);
    }

    @Bean("immClient")
    public com.aliyun.imm20200930.Client immClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config().setEndpoint(immEndpoint)
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        return new com.aliyun.imm20200930.Client(config);
    }
}
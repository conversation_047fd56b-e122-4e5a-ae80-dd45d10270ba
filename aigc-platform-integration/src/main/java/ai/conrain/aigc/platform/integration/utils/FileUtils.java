package ai.conrain.aigc.platform.integration.utils;

import ai.conrain.aigc.platform.integration.ai.model.FileVO;
import lombok.extern.slf4j.Slf4j;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FileUtils {
    public static List<FileVO> sortFileVOs(List<FileVO> fileVOs) {

        if (fileVOs == null) {
            throw new NullPointerException("文件列表为null");
        }

        // 复制原始列表，避免修改
        List<FileVO> sortedList = new ArrayList<>(fileVOs);

        // 使用自定义比较器排序
        sortedList.sort((fileVO1, fileVO2) -> {
            Integer number1 = extractLeadingNumber(fileVO1.getFileName());
            Integer number2 = extractLeadingNumber(fileVO2.getFileName());
            if (number1 != null && number2 != null) {
                return number1.compareTo(number2);
            } else if (number1 != null) {
                return -1; // number1 有值，排在前面
            } else if (number2 != null) {
                return 1; // number2 有值，排在前面
            } else {
                return fileVO1.getFileName().compareTo(fileVO2.getFileName()); // 按字典顺序排序
            }
        });

        return sortedList; // 返回排序后的新列表
    }

    private static Integer extractLeadingNumber(String fileName) {
        Pattern pattern = Pattern.compile("^(\\d+)_");
        Matcher matcher = pattern.matcher(fileName);
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return null; // 没有找到数字前缀，返回 null
    }

    public static String combinePath(String ...paths) {
        if (paths == null || paths.length == 0) {
            return "";
        }
        Path path = Paths.get(paths[0]);
        for (int i = 1; i < paths.length; i++) {
            path = path.resolve(paths[i]);
        }
        return path.toString();
    }

    /**
     * 规范化路径，确保路径分隔符正确
     *
     * @param path 原始路径
     * @return 规范化后的路径
     */
    public static String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }

        // 确保路径以 "/" 结尾
        if (!path.endsWith("/")) {
            path += "/";
        }

        return path;
    }

    /**
     * 规范化相对路径，确保路径分隔符正确
     *
     * @param relativePath 相对路径
     * @return 规范化后的相对路径
     */
    public static String normalizeRelativePath(String relativePath) {
        if (relativePath == null || relativePath.isEmpty()) {
            return "";
        }

        // 移除开头的 "/"
        if (relativePath.startsWith("/")) {
            relativePath = relativePath.substring(1);
        }

        // 确保以 "/" 结尾
        if (!relativePath.endsWith("/")) {
            relativePath += "/";
        }

        return relativePath;
    }
}

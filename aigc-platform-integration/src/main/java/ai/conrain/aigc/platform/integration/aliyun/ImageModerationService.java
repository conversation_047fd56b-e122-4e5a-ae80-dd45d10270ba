/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.aliyun;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.ImageModerationRequest;
import com.aliyun.green20220302.models.ImageModerationResponse;
import com.aliyun.green20220302.models.ImageModerationResponseBody;
import com.aliyun.green20220302.models.ImageModerationResponseBody.ImageModerationResponseBodyData;
import com.aliyun.green20220302.models.ImageModerationResponseBody.ImageModerationResponseBodyDataResult;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 图片过绿霸服务
 *
 * <AUTHOR>
 * @version : ImageModerationService.java, v 0.1 2024/6/3 12:29 renxiao.wu Exp $
 */
@Slf4j
@Service
public class ImageModerationService {
    @Value("${aliyun.security.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.security.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.security.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.bucket}")
    private String bucketName;
    @Value("${aliyun.oss.region}")
    private String regionId;
    @Autowired
    private TairService tairService;

    /**
     * 对OSS上的图片进行内容安全审核
     * <p>
     * 图片地址如：202406/product_20592_0002_jMkiCRBWtSOCmhn.png
     * <p/>
     *
     * @param imagePath oss图片地址
     * @param promptId  promptId作为请求id
     * @return 返回结果
     */
    public List<String> moderateByOSS(String imagePath, String promptId) {
        List<String> cache = getFromCache(imagePath, promptId);
        if (cache != null) {
            return cache;
        }

        ImageModerationResponse response = null;
        // 接入区域和地址请根据实际情况修改。
        try {
            response = callModerateByOSS(imagePath, promptId);
        } catch (Exception e) {
            log.error("调用阿里云的内容安全服务异常,忽略,promptId=" + promptId + ",图片url=" + imagePath, e);
            return null;
        }

        if (null == response || response.getStatusCode() != 200) {
            log.error("调用阿里云的内容安全服务,返回结果异常,response={}", response);
            return null;
        }

        ImageModerationResponseBody body = response.getBody();

        log.info("调用阿里云的内容安全服务，返回{}", JSONObject.toJSONString(body));

        if (body.getCode() != 200) {
            log.warn("调用阿里云的内容安全服务，返回状态码失败，code={}", body.getCode());
            return null;
        }

        List<String> result = new ArrayList<>();
        ImageModerationResponseBodyData data = body.getData();
        List<ImageModerationResponseBodyDataResult> results = data.getResult();
        for (ImageModerationResponseBodyDataResult each : results) {
            result.add(each.getLabel());
        }

        putIntoCache(imagePath, promptId, result);

        return result;
    }

    /**
     * 调用阿里云内容安全服务
     *
     * @param imagePath 图片OSS地址
     * @param promptId  promptId
     * @return 返回结果
     * @throws Exception 异常
     */
    private ImageModerationResponse callModerateByOSS(String imagePath, String promptId) throws Exception {
        //注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能。
        Client client = createClient();

        // 创建RuntimeObject实例并设置运行参数
        RuntimeOptions runtime = new RuntimeOptions();

        // 检测参数构造。
        Map<String, String> serviceParameters = new HashMap<>();
        //待检测数据唯一标识
        serviceParameters.put("dataId", promptId);
        // 待检测文件所在bucket的区域。 示例：cn-shanghai
        serviceParameters.put("ossRegionId", regionId);
        // 待检测文件所在bucket名称。示例：bucket001
        serviceParameters.put("ossBucketName", bucketName);
        // 待检测文件。 示例：image/001.jpg
        serviceParameters.put("ossObjectName", imagePath);

        ImageModerationRequest request = new ImageModerationRequest();
        // 图片检测service：内容安全控制台图片增强版规则配置的serviceCode，示例：baselineCheck
        // 支持service请参考：https://help.aliyun.com/document_detail/467826.html?0#p-23b-o19-gff
        request.setService("aigcCheck");
        request.setServiceParameters(JSON.toJSONString(serviceParameters));

        ImageModerationResponse response = null;
        try {
            response = client.imageModerationWithOptions(request, runtime);
        } catch (Exception e) {
            log.error("调用内容安全异常", e);
        }
        return response;
    }

    /**
     * 创建请求客户端
     *
     * @return 客户端
     * @throws Exception 异常
     */
    private Client createClient() throws Exception {
        Config config = new Config();
        config.setAccessKeyId(accessKeyId);
        config.setAccessKeySecret(accessKeySecret);
        // 设置http代理。
        // config.setHttpProxy("http://10.10.xx.xx:xxxx");
        // 设置https代理。
        // config.setHttpsProxy("https://10.10.xx.xx:xxxx");
        // 接入区域和地址请根据实际情况修改
        config.setEndpoint(endpoint);
        return new Client(config);
    }

    private void putIntoCache(String imagePath, String promptId, List<String> list) {
        if (list != null) {
            return;
        }
        JSONArray array = new JSONArray(list);
        //缓存1小时
        tairService.setObject(getTairKey(imagePath, promptId), array.toString(), 60 * 60);
    }

    private List<String> getFromCache(String imagePath, String promptId) {
        String cache = tairService.getString(getTairKey(imagePath, promptId));
        if (StringUtils.isBlank(cache)) {
            return null;
        }

        JSONArray array = JSONArray.parseArray(cache);
        log.info("调用阿里云的内容安全服务，直接从缓存中获取信息");
        return array.toJavaList(String.class);
    }

    private String getTairKey(String imagePath, String promptId) {
        return "_tair_moderation_" + promptId + "_" + imagePath;
    }
}

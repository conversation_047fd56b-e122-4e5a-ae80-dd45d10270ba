package ai.conrain.aigc.platform.integration.ai.model;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/icf3gp/hb4syge0ifufgn9g#grZG
 * 简易打标字段定义
 */
@Data
public class ImageSimpleCaption {

    //年龄
    @JSONField(name = "age")
    private String age;

    //人种
    @JSONField(name = "race")
    private String race;

    // 性别
    @JSONField(name = "gender")
    private String gender;

    // 体型
    @JSONField(name = "body_shape")
    private String bodyShape;

    // 上装一级类目
    @JSONField(name = "top_cate_1")
    private String topCate1;

    // 上装二级类目
    @JSONField(name = "top_cate_2")
    private String topCate2;

    //套装一级分类
    @JSONField(name = "suit_cate_1")
    private String suitCate1;

    //套装二级分类
    @JSONField(name = "suit_cate_2")
    private String suitCate2;

    //用途一级分类
    @JSONField(name = "usage_cate_1")
    private String usageCate1;

    //用途二级分类
    @JSONField(name = "usage_cate_2")
    private String usageCate2;

    //下装一级分类
    @JSONField(name = "bottom_cate_1")
    private String bottomCate1;

    //下装二级分类
    @JSONField(name = "bottom_cate_2")
    private String bottomCate2;

    //连体服装一级分类
    @JSONField(name = "onepiece_cate_1")
    private String onePieceCate1;

    //连体服装二级分类
    @JSONField(name = "onepiece_cate_2")
    private String onePieceCate2;

    //musegate图片等级，null、unkown、低质量、可用但有水印、可用但有小边框、可用
    @JSONField(name = "mg_image_tags")
    private String mgImageTags;

    //美学应用标准，null、unkown、美学应用的好中差
    @JSONField(name = "mg_aesthetic_level")
    private String mgAestheticLevel;
}
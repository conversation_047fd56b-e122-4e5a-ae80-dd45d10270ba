package ai.conrain.aigc.platform.integration.interceptor;

import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.integration.utils.BeanUtils.Callback;
import ai.conrain.aigc.platform.integration.utils.ContextSecurityUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;

@Slf4j
//@Aspect
//@Component
public class IntegrationServiceInterceptor {
    /** 日志 */
    private static final Logger DIGEST_LOG = LoggerFactory.getLogger("DIGEST_LOG");

    private static final List<String> FIELD_LIST = Arrays.asList("mobile", "loginId", "masterLoginId");

    //*Service类才会被拦截
    @Around("execution(public * ai.conrain.aigc.platform.integration.*.*Service.*(..))")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        boolean succ = true;
        String msg = "-";
        Object result = null;
        String methodName = "-";
        try {
            result = proceedingJoinPoint.proceed();
            return result;
        } catch (Throwable e) {
            succ = false;
            msg = e.getMessage();
            throw e;
        } finally {
            StringBuilder sb = new StringBuilder();
            try {
                Signature signature = proceedingJoinPoint.getSignature();
                MethodSignature methodSignature = (MethodSignature)signature;
                Method targetMethod = methodSignature.getMethod();
                methodName = targetMethod.getName();

                sb.append(targetMethod.getDeclaringClass().getSimpleName()).append(".");
                sb.append(methodName);

                DIGEST_LOG.info(MessageFormat.format("[sal-digest],{0},{1},{2}ms,{3}", sb, succ ? "Y" : "N",
                    "" + (System.currentTimeMillis() - start), msg != null ? msg : "-"));

                sb.append(",");
                if (StringUtils.equals(methodName, "prompt") || StringUtils.equals(methodName, "removeBg")) {
                    sb.append("-,");
                } else {
                    sb.append(formatArgs(proceedingJoinPoint.getArgs())).append(",");
                }
            } catch (Throwable t) {
                log.warn("打印日志异常，忽略", t);
            }

            if (StringUtils.equals(methodName, "batchFetchImageFile") || StringUtils.equals(methodName,
                "uploadInputImage") || StringUtils.equals(methodName, "uploadLora") || StringUtils.equals(methodName,
                "downloadImage") || StringUtils.equals(methodName, "prompt") || StringUtils.equals(methodName, "upload")
                || StringUtils.equals(methodName, "downloadFile") || StringUtils.equals(methodName, "viewFiles")
                || StringUtils.equals(methodName, "removeBg")) {
                sb.append("-");

            } else {
                sb.append(result);
            }

            // 如果 StringBuilder 长度超过1000，进行截断
            if (sb.length() > 1000) {
                sb.setLength(997);
                sb.append("...");
            }

            log.info("[Integration]" + sb);
        }
    }

    private static String formatArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return JSONObject.toJSONString(args);
        }

        Object[] result = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg instanceof JSONObject) {
                result[i] = ((JSONObject)arg).toJSONString();
            } else if (arg instanceof String) {
                Object value = arg;
                if (ContextSecurityUtils.isMobile((String)arg)) {
                    value = ContextSecurityUtils.maskPhoneNumber((String)arg);
                } else if (StringUtils.startsWith((String)arg, "tair_captcha_")) {
                    String mobile = StringUtils.substringAfter((String)arg, "tair_captcha_");
                    value = "tair_captcha_" + ContextSecurityUtils.maskPhoneNumber(mobile);
                }
                result[i] = value;
            } else {

                for (String field : FIELD_LIST) {
                    if (BeanUtils.hasField(arg, field)) {
                        BeanUtils.resetField(arg, field, (Callback<String>)ContextSecurityUtils::maskPhoneNumber);
                    }
                }

                result[i] = arg;
            }
        }

        return JSONObject.toJSONString(result);
    }
}
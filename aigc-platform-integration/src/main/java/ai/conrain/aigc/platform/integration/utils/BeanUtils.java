/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.utils;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSON;

/**
 * bean工具类
 *
 * <AUTHOR>
 * @version : BeanUtils.java, v 0.1 2025/1/9 11:01 renxiao.wu Exp $
 */
public abstract class BeanUtils {
    /**
     * 判断对象是否有指定字段
     *
     * @param target    目标对象
     * @param fieldName 字段名称
     * @return true存在
     */
    public static boolean hasField(Object target, String fieldName) {
        if (target == null || fieldName == null || fieldName.isEmpty()) {
            return false;
        }

        // 获取对象的类
        Class<?> clazz = target.getClass();

        // 遍历类及其父类的字段
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                if (field != null) {
                    return true; // 如果字段存在，返回 true
                }
            } catch (NoSuchFieldException e) {
                // 当前类未找到字段，继续检查父类
                clazz = clazz.getSuperclass();
            }
        }

        return false; // 未找到字段
    }

    /**
     * 赋值目标对象的指定字段
     *
     * @param target    目标对象
     * @param fieldName 字段名称
     * @param callback  回调函数
     */
    public static void resetField(Object target, String fieldName, Callback callback) {
        if (target == null || StringUtils.isBlank(fieldName)) {
            return;
        }

        if (!hasField(target, fieldName)) {
            return;
        }

        // 获取对象的类
        Class<?> clazz = target.getClass();

        // 遍历类及其父类的字段
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true); // 确保私有字段可访问

                Object value = field.get(target);
                field.set(target, callback.call(value));
                return; // 找到字段后直接返回
            } catch (NoSuchFieldException e) {
                // 当前类未找到字段，继续检查父类
                clazz = clazz.getSuperclass();
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access the field: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 使用 alibaba fastjson 进行深度拷贝，支持父类到子类的拷贝
     *
     * @param source      源对象
     * @param targetClass 目标类型
     * @param <T>         目标类型泛型
     * @return 拷贝后的目标对象
     */
    public static <T> T deepCopy(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            // 将源对象序列化为 JSON 字符串
            String jsonString = JSON.toJSONString(source);
            // 将 JSON 字符串反序列化为目标类型对象
            return JSON.parseObject(jsonString, targetClass);
        } catch (Exception e) {
            throw new RuntimeException("深度拷贝失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用 alibaba fastjson 进行同类型深度拷贝
     *
     * @param source 源对象
     * @param <T>    对象类型泛型
     * @return 拷贝后的对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T deepCopy(T source) {
        if (source == null) {
            return null;
        }
        
        try {
            // 将源对象序列化为 JSON 字符串
            String jsonString = JSON.toJSONString(source);
            // 将 JSON 字符串反序列化为同类型对象
            return (T) JSON.parseObject(jsonString, source.getClass());
        } catch (Exception e) {
            throw new RuntimeException("深度拷贝失败: " + e.getMessage(), e);
        }
    }

    /**
     * 回调函数
     */
    public interface Callback<T> {
        /**
         * 对目标值进行处理
         *
         * @param origin 原始值
         * @return 处理后值
         */
        T call(T origin);
    }

}

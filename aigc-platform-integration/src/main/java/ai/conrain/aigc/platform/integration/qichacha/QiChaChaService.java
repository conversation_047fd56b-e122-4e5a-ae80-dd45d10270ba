package ai.conrain.aigc.platform.integration.qichacha;

import ai.conrain.aigc.platform.integration.qichacha.model.QiChaChaModelPageVO;
import ai.conrain.aigc.platform.integration.qichacha.model.QiChaChaModelPageVO.QiChaChaModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@Slf4j
@Component
public class QiChaChaService {

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    @Value("${qichacha.appkey}")
    private String appkey;

    @Value("${qichacha.secretkey}")
    private String secretKey;

    @Value("${qichacha.fuzzySearchUrl}")
    private String fuzzySearchUrl;

    // 正则表达式，用于匹配异常状态码
    private static final String ABNORMAL_REGEX = "(101)|(102)";
    // 正则表达式模式
    private static final Pattern pattern = Pattern.compile(ABNORMAL_REGEX);

    /**
     * 生成认证头信息
     *
     * @return 包含Token和Timespan的数组
     */
    private String[] generateAuthHeader() {
        String timeSpan = String.valueOf(System.currentTimeMillis() / 1000);
        String token = DigestUtils.md5Hex(appkey.concat(timeSpan).concat(secretKey)).toUpperCase();
        return new String[] {token, timeSpan};
    }

    /**
     * 检查请求是否异常
     *
     * @param status 状态码
     * @return 是否异常
     */
    private boolean isAbnormalRequest(String status) {
        return status != null && pattern.matcher(status).matches();
    }

    /**
     * 模糊查询企业信息
     *
     * @param keyword   关键词
     * @param pageIndex 页码
     * @return 企业信息列表
     */
    public QiChaChaModelPageVO fuzzySearch(String keyword, int pageIndex) {
        QiChaChaModelPageVO result = new QiChaChaModelPageVO();
        if (keyword == null || keyword.trim().isEmpty()) {
            log.warn("【企查查】查询关键词为空");
            return result;
        }

        try {
            String[] authHeader = generateAuthHeader();
            String paramStr = "searchKey=" + keyword;
            String pageParam = "pageIndex=" + pageIndex + "&pageSize=20";
            String reqUri = fuzzySearchUrl + "?key=" + appkey + "&" + paramStr + "&" + pageParam;

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Token", authHeader[0]);
            headers.set("Timespan", authHeader[1]);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            log.info("【企查查】开始查询，关键词：{}", keyword);

            // 发送请求
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                reqUri,
                HttpMethod.GET,
                entity,
                String.class
            );

            String response = responseEntity.getBody();
            if (response == null || response.trim().isEmpty()) {
                log.warn("【企查查】响应为空，关键词：{}", keyword);
                return result;
            }

            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject == null) {
                log.warn("【企查查】响应解析失败，响应内容：{}", response);
                return result;
            }

            // 检查状态码
            String status = jsonObject.getString("Status");
            if (status == null || isAbnormalRequest(status)) {
                log.warn("【企查查】查询异常，状态码：{}，响应内容：{}", status, response);
                return result;
            }

            // 从Paging对象中获取分页信息
            JSONObject paging = jsonObject.getJSONObject("Paging");
            if (paging != null) {
                result.setPageSize(paging.getInteger("PageSize"));
                result.setPageIndex(paging.getInteger("PageIndex"));
                result.setTotal(paging.getInteger("TotalRecords"));
            }

            // 获取Result数组
            List<QiChaChaModel> data = JSON.parseArray(
                jsonObject.getJSONArray("Result").toJSONString(),
                QiChaChaModel.class
            );

            if (data != null) {
                result.setData(data);
                log.info("【企查查】查询成功，关键词：{}，查询到{}条结果", keyword, data.size());
            } else {
                log.warn("【企查查】查询结果为空，关键词：{}", keyword);
            }
        } catch (Exception e) {
            log.error("【企查查】查询异常，关键词：{}", keyword, e);
        }

        return result;
    }

} 
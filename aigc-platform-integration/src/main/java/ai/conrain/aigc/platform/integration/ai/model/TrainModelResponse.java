package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class TrainModelResponse {
    
    private String status;
    
    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;
    
    @JsonProperty("model_save_dir")
    @JSONField(name = "model_save_dir")
    private String modelSaveDir;
    
    @JsonProperty("each_model_results")
    @JSONField(name = "each_model_results")
    private List<TrainingResults> trainingResults;

    @Data
    public static class TrainingResults {
        private String label;
        private ModelResultDetail detail;
    }
    
    @Data
    public static class ModelResultDetail {
        @JsonProperty("best_train_loss")
        @JSONField(name = "best_train_loss")
        private Double bestTrainLoss;
        
        @JsonProperty("best_val_loss")
        @JSONField(name = "best_val_loss")
        private Double bestValLoss;
    }

    @JsonProperty("training_volume_each_epoch")
    @JSONField(name = "training_volume_each_epoch")
    private List<Integer> trainingVolumeEachEpoch;
}
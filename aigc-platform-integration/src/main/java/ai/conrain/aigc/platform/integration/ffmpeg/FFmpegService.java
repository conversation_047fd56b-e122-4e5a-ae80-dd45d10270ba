package ai.conrain.aigc.platform.integration.ffmpeg;

import ai.conrain.aigc.platform.integration.ffmpeg.config.FFmpegConfig;
import ai.conrain.aigc.platform.integration.utils.ShellExecutor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;

@Slf4j
@Service
public class FFmpegService {

    @Autowired
    private FFmpegConfig ffmpegConfig;

    public static final String DEFAULT_FILTER_GRAPH_COMMAND = "./bin/ffmpeg -i %s -vf \"fps=%s\" -q:v 2 %s";

    /**
     * 视频抽帧
     * 
     * @param relativePath 相对路径
     * @param inputFile 输入视频文件名
     * @param outputPrefix 输出图片文件前缀
     * @param fps 抽帧帧率，默认为 1/1 (fps)
     * @param scale 输出文件编号位数，默认为4
     * @throws IOException 当执行FFmpeg命令时发生IO异常
     * @throws InterruptedException 当命令执行被中断时
     * @throws IllegalArgumentException 当参数不合法时
     */
    public void filterGraph(String relativePath, String inputFile, String outputPrefix, String outputSuffix, String fps, int scale, String commandPattern) throws IOException, InterruptedException {
        MDC.put("methodPrefix", "[视频抽帧]");
        // 参数校验
        if (!StringUtils.hasText(relativePath)) {
            throw new IllegalArgumentException("相对路径不能为空");
        }
        if (!StringUtils.hasText(inputFile)) {
            throw new IllegalArgumentException("输入文件名不能为空");
        }
        if (!StringUtils.hasText(outputPrefix)) {
            throw new IllegalArgumentException("输出文件前缀不能为空");
        }
        if (!StringUtils.hasText(fps)) {
            fps = "1/1";
        }
        if (scale <= 0) {
            scale = 4; // 默认4位
        }
        if (!StringUtils.hasText(commandPattern)) {
            commandPattern = DEFAULT_FILTER_GRAPH_COMMAND;
        }

        log.info("开始执行FFmpeg视频抽帧，相对路径: {}, 输入文件: {}, 输出前缀: {}, FPS: {}, 编号位数: {}", 
            relativePath, inputFile, outputPrefix, fps, scale);

        // 构建完整文件路径
        String inputFilePath = ffmpegConfig.getInputFilePath(relativePath, inputFile);
        String outputFilePath = ffmpegConfig.getOutputFilePath(relativePath, outputPrefix, outputSuffix, scale);
        
        log.debug("输入文件完整路径: {}", inputFilePath);
        log.debug("输出文件完整路径模式: {}", outputFilePath);

        // 检查输入文件是否存在
        File inputFileObj = new File(inputFilePath);
        if (!inputFileObj.exists()) {
            throw new IllegalArgumentException("输入文件不存在: " + inputFilePath);
        }

        // 确保输出目录存在
        String outputDirPath = ffmpegConfig.getOutputDirectoryPath(relativePath);
        File outputDir = new File(outputDirPath);
        if (!outputDir.exists()) {
            boolean created = outputDir.mkdirs();
            if (!created) {
                throw new IOException("无法创建输出目录: " + outputDir.getAbsolutePath());
            }
            log.info("创建输出目录: {}", outputDir.getAbsolutePath());
        }

        // 构建FFmpeg命令
        File workDir = new File(ffmpegConfig.getWorkDir());
        String command = String.format(commandPattern, inputFilePath, fps, outputFilePath);
        log.info("当前工作目录: {}", System.getProperty("user.dir"));
        log.info("执行FFmpeg命令: workDir: {}, command: {}", workDir.getAbsolutePath(), command);

        try {
            // 设置环境变量，添加库路径
            java.util.Map<String, String> envVars = new java.util.HashMap<>();
            String libPath = workDir.getAbsolutePath() + "/lib";
            envVars.put("LD_LIBRARY_PATH", libPath);
            log.info("设置LD_LIBRARY_PATH: {}", libPath);
            
            // 执行命令，设置30秒超时
            ShellExecutor.CommandResult result = ShellExecutor.executeCommand(command, workDir, envVars, null, 30 * 1000);
            
            if (result.isSuccess()) {
                log.info("FFmpeg视频抽帧执行成功");
                log.debug("命令输出: {}", result.getOutput());
            } else {
                log.error("FFmpeg视频抽帧执行失败，退出码: {}", result.getExitCode());
                log.error("错误输出: {}", result.getOutput());
                throw new RuntimeException("FFmpeg命令执行失败，退出码: " + result.getExitCode());
            }
        } catch (Exception e) {
            log.error("执行FFmpeg命令时发生异常", e);
            throw e;
        } finally {
            MDC.remove("methodPrefix");
        }
    }
}

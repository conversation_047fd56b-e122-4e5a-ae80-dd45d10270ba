package ai.conrain.aigc.platform.integration.ai.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class ModelValidationResponse {
    private String status;
    private String message;
    
    @JsonProperty("task_id")
    @JSONField(name = "task_id")
    private String taskId;

    @JsonProperty("data")
    @JSONField(name = "data")
    private ValidateData data;
    
    @JsonProperty("validation_time")
    @JSONField(name = "validation_time")
    private String validationTime;
    
    @Data
    public static class ValidateData {
        @JsonProperty("all_performance")
        @JSONField(name = "all_performance")
        private Performance allPerformance;
        
        @JsonProperty("each_label_performance")
        @JSONField(name = "each_label_performance")
        private List<ClassificationLabel> singleLabelPerformance;
    }
    
    @Data
    public static class Performance {
        private Double accuracy;
        private Double precision;
        private Double recall;
        @JsonProperty("f1_score")
        @JSONField(name = "f1_score")
        private Double f1Score;
    }
    
    @Data
    public static class ClassificationLabel {
        private String label;
        private Performance detail;
    }
}
package ai.conrain.aigc.platform.integration.aliyun;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import com.alibaba.fastjson.JSONObject;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.core.utils.StringUtils;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 阿里云短信服务
 */
@Slf4j
@Service
public class AliyunSmsService implements InitializingBean {
    @Value("${aliyun.sms.accessKeyId}")
    private String accessKeyId;
    /** 环境 */
    @Value("${aliyun.sms.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.sms.signName}")
    private String signName;
    @Value("${aliyun.sms.regionId}")
    private String regionId;
    @Value("${aliyun.sms.endpoint}")
    private String endpoint;

    /** 服务凭证 */
    private StaticCredentialProvider provider;

    /** 客户端配置 */
    private ClientOverrideConfiguration configuration;

    /**
     * 发送短信
     *
     * @param mobile     手机号
     * @param templateId 模板id,如：SMS_463215743
     * @param jsonParam  json格式的参数，如："{\"code\":\"1234\"}"
     * @return true发送成功，false发送失败
     */
    public boolean sendSms(String mobile, String templateId, String jsonParam) {
        // Configure the Client
        AsyncClient client = AsyncClient.builder()
            .region(regionId) // Region ID
            //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache
            // HttpClient)
            .credentialsProvider(provider)
            //.serviceConfiguration(Configuration.create()) // Service-level configuration
            // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
            .overrideConfiguration(configuration)
            .build();

        // Parameter settings for API request
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
            .signName(signName)
            .templateCode(templateId)
            .phoneNumbers(mobile)
            .templateParam(jsonParam)
            .build();

        // Asynchronously get the return value of the API request
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
        // Synchronously get the return value of the API request
        SendSmsResponse resp = null;
        try {
            resp = response.get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("调用阿里云sms服务异常", e);
            return false;
        } finally {
            client.close();
        }

        if (StringUtils.equals(resp.getBody().getCode(), "OK")) {
            if (log.isInfoEnabled()) {
                log.info("调用阿里云sms服务成功，mobile={}", mobile);
            }
            return true;
        }

        log.warn("调用阿里云sms服务失败，mobile={},response={}", mobile, JSONObject.toJSONString(resp.getBody()));

        return false;
    }

    @Override
    public void afterPropertiesSet() {
        provider = StaticCredentialProvider.create(Credential.builder()
            .accessKeyId(accessKeyId)
            .accessKeySecret(accessKeySecret)
            .build());

        configuration = ClientOverrideConfiguration.create()
            // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
            .setEndpointOverride(endpoint);
        //.setConnectTimeout(Duration.ofSeconds(30))
    }
}

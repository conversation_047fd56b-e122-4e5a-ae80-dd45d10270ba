package ai.conrain.aigc.platform.integration.aliyun.model;

import lombok.Getter;

@Getter
public enum AliyunTryonTaskStatusEnum {
    PENDING("排队中"),
    PRE_PROCESSING("前置处理中"),
    RUNNING("处理中"),
    POST_PROCESSING("后置处理中"),
    SUCCEEDED("成功"),
    FAILED("失败"),
    UNKNOWN("作业不存在或状态未知");

    private final String desc;

    AliyunTryonTaskStatusEnum(String desc) {
        this.desc = desc;
    }

    public static AliyunTryonTaskStatusEnum getByName(String name) {
        for (AliyunTryonTaskStatusEnum status : AliyunTryonTaskStatusEnum.values()) {
            if (status.name().equalsIgnoreCase(name)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}

package ai.conrain.aigc.platform.integration.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.function.Function;

/**
 * 响应处理工具类
 */
@Slf4j
public class ResponseUtils {

    /**
     * 处理 JSON 响应
     *
     * @param response 响应实体
     * @param logPrefix 日志前缀
     * @param handler 响应处理器
     * @return 处理结果
     * @param <T> 返回类型
     */
    public static <T> T handleJsonResponse(ResponseEntity<String> response,
                                         String logPrefix,
                                         Function<JSONObject, T> handler) {
        try {
            // 检查响应体
            if (response.getBody() == null) {
                log.error("[{}]响应体为空", logPrefix);
                return null;
            }

            // 解析响应
            JSONObject responseJson = JSON.parseObject(response.getBody());
            JSONObject outputJson = responseJson.getJSONObject("output");
            if (outputJson == null) {
                log.error("[{}]响应中缺少 output 字段: {}", logPrefix, response.getBody());
                return null;
            }

            // 处理响应
            return handler.apply(outputJson);
        } catch (Exception e) {
            log.error("[{}]处理响应失败,错误信息:{}", logPrefix, e.getMessage());
            return null;
        }
    }

    /**
     * 处理 JSON 响应并转换为指定类型
     *
     * @param response 响应实体
     * @param logPrefix 日志前缀
     * @param clazz 目标类型
     * @return 转换后的对象
     * @param <T> 返回类型
     */
    public static <T> T handleJsonResponse(ResponseEntity<String> response,
                                         String logPrefix,
                                         Class<T> clazz) {
        return handleJsonResponse(response, logPrefix, output -> JSON.toJavaObject(output, clazz));
    }
} 
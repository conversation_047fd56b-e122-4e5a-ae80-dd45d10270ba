package ai.conrain.aigc.platform.integration.aliyun;

import ai.conrain.aigc.platform.integration.aliyun.model.TryonTaskOutputModel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * aliyun try-on api
 */
@Slf4j
@Service
public class AliyunTryonService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OssService ossService;

    private static final String auth = "Bearer sk-ff12a229340744899bfe9b8350a03a2f";

    public String createTryonTask(String topUrl, String bottomUrl, String personImgUrl) {
        if (StringUtils.isBlank(topUrl) || StringUtils.isBlank(personImgUrl)) {
            throw new IllegalArgumentException("topUrl/personImgUrl must not be blank");
        }

        String url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis/";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-DashScope-Async", "enable");
        headers.set("Authorization", auth);

        JSONObject inputJson = new JSONObject();
        inputJson.put("top_garment_url", topUrl);
        inputJson.put("bottom_garment_url", bottomUrl);
        inputJson.put("person_image_url", personImgUrl);

        JSONObject parametersJson = new JSONObject();
        parametersJson.put("resolution", -1);
        parametersJson.put("restore_face", true);

        JSONObject requestJson = new JSONObject();
        requestJson.put("model", "aitryon");
        requestJson.put("input", inputJson);
        requestJson.put("parameters", parametersJson);

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(requestJson, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            JSONObject responseJson = JSONObject.parseObject(response.getBody());
            JSONObject outputJson = responseJson.getJSONObject("output");

            return outputJson.getString("task_id");

        } catch (Exception e) {
            log.error("createTryonTask failed", e);
        }

        return null;
    }

    public TryonTaskOutputModel queryTask(String taskId) {
        String url = "https://dashscope.aliyuncs.com/api/v1/tasks/" + taskId;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", auth);

        HttpEntity<String> requestEntity = new HttpEntity<>(headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, org.springframework.http.HttpMethod.GET, requestEntity, String.class);

            JSONObject responseJson = JSON.parseObject(response.getBody());
            JSONObject outputJson = responseJson.getJSONObject("output");
            TryonTaskOutputModel output = JSON.toJavaObject(outputJson, TryonTaskOutputModel.class);
            if (output != null && StringUtils.isNotBlank(output.getImageUrl())) {
                output.setImageUrl(ossService.fetchStreamAndUpload(output.getImageUrl()));
            }

            return output;

        } catch (Exception e) {
            log.error("getTaskOutput failed", e);
        }

        return null;
    }

    /**
     * https://help.aliyun.com/zh/model-studio/developer-reference/ai-fitting-picture-finishing-api-details?spm=a2c4g.11186623.help-menu-2400256.d_3_3_17_3.10ea7221EoNAA7&scm=20140722.H_2796663._.OR_help-T_cn~zh-V_1
     * @param topUrl
     * @param bottomUrl
     * @param personImgUrl
     * @param tryonResultImgUrl
     * @param personGender
     * @return
     */
    public String createTryonRefinerTask(String topUrl, String bottomUrl, String personImgUrl, String tryonResultImgUrl, String personGender){

        if (StringUtils.isBlank(topUrl) || StringUtils.isBlank(personImgUrl) || StringUtils.isBlank(tryonResultImgUrl)) {
            throw new IllegalArgumentException("topUrl/personImgUrl/tryonResultImgUrl must not be blank");
        }

        //aliyun
        if (!"woman".equals(personGender) && !"man".equals(personGender)) {
            throw new IllegalArgumentException("personGender must be 'woman' or 'man'");
        }

        String url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/image2image/image-synthesis/";

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-DashScope-Async", "enable");
        headers.set("Authorization", auth);

        JSONObject inputJson = new JSONObject();
        inputJson.put("top_garment_url", topUrl);
        inputJson.put("bottom_garment_url", bottomUrl);
        inputJson.put("person_image_url", personImgUrl);
        inputJson.put("coarse_image_url", tryonResultImgUrl);

        JSONObject parametersJson = new JSONObject();
        parametersJson.put("gender", personGender);

        JSONObject requestJson = new JSONObject();
        requestJson.put("model", "aitryon-refiner");
        requestJson.put("input", inputJson);
        requestJson.put("parameters", parametersJson);

        HttpEntity<JSONObject> requestEntity = new HttpEntity<>(requestJson, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            JSONObject responseJson = JSONObject.parseObject(response.getBody());
            JSONObject outputJson = responseJson.getJSONObject("output");

            return outputJson.getString("task_id");

        } catch (Exception e) {
            log.error("createTryonRefinerTask failed", e);
        }

        return null;
    }
}

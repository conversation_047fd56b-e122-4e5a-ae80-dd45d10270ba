package ai.conrain.aigc.platform.integration.utils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EnvUtil {

    @Getter
    private static String env;

    @Getter
    private static String domain;

    @Value("${app.custom.env}")
    public void setEnv(String env) {
        log.info("当前环境：{}", env);
        EnvUtil.env = env;
    }

    @Value("${app.custom.domain}")
    public void setDomain(String domain) {
        log.info("当前域名：{}", domain);
        EnvUtil.domain = domain;
    }

    public static String getEnvUpperCase(){
        return env != null ? env.toUpperCase() : "";
    }

    public static boolean isProdEnv(){
        return "prod".equalsIgnoreCase(EnvUtil.env);
    }

    public static boolean isDevEnv(){
        return "dev".equalsIgnoreCase(EnvUtil.env);
    }

    public static boolean isLocalEnv(){
        return "local".equalsIgnoreCase(EnvUtil.env);
    }

    public static String getAppDomain(){
        return domain;
    }
}
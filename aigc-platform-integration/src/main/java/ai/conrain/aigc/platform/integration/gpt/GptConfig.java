package ai.conrain.aigc.platform.integration.gpt;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for AI services
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "gpt")
public class GptConfig {
    /**
     * Provider configurations
     */
    private List<ProviderConfig> providers = new ArrayList<>();

    public ProviderConfig getProviderConfig(String providerId) {
        return providers.stream()
            .filter(providerConfig -> providerConfig.getId().equals(providerId))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Provider configuration
     */
    @Data
    public static class ProviderConfig {
        /**
         * Id of the provider
         */
        private String id;

        /**
         * Whether the provider is enabled
         */
        private boolean enabled = true;

        /**
         * Type of the provider
         */
        private String type;
        
        /**
         * API key for the provider
         */
        private String apiKey;
        
        /**
         * Endpoint for the provider
         */
        private String endpoint;
        
        /**
         * Default model to use
         */
        private String model;
        
        /**
         * Deployment ID (for Azure)
         */
        private String deploymentId;
        
        /**
         * API version (for Azure)
         */
        private String apiVersion;
    }
}

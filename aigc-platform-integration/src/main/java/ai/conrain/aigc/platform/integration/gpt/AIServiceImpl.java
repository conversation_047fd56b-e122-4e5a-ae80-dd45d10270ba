package ai.conrain.aigc.platform.integration.gpt;

import ai.conrain.aigc.platform.integration.gpt.GptConfig.ProviderConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * Implementation of the unified AI service using RestTemplate
 */
@Slf4j
@Service
public class AIServiceImpl implements AIService {

    @Autowired
    private GptConfig gptConfig;
    
    @Autowired
    @Qualifier("superLongRestTemplate")
    private RestTemplate restTemplate;

    @Override
    public String chat(String prompt) {
        return chat(prompt, (List<String>)null);
    }

    @Override
    public String chat(String prompt, String provider) {
        return chat(prompt, null, provider);
    }

    @Override
    public String chat(String prompt, List<String> imageUrls) {
        return chat(prompt, imageUrls, "azure-openai-4.1-mini");
    }

    @Override
    public String chat(String prompt, List<String> imageUrls, String provider) {
        GptOptions options = GptOptions.builder()
            .provider(provider)
            .build();
        return chat(prompt, imageUrls, options);
    }

    @Override
    public String chat(String prompt, GptOptions options) {
        return chat(prompt, null, options);
    }

    @Override
    public String chat(String prompt, List<String> imageUrls, GptOptions options) {
        if (options == null) {
            options = GptOptions.builder().build();
        }

        // Default to azure-openai-4.1-mini if no provider specified
        String providerKey = StringUtils.hasText(options.getProvider())
            ? options.getProvider()
            : "azure-openai-4.1-mini";

        ProviderConfig providerConfig = gptConfig.getProviderConfig(providerKey);

        if (providerConfig == null) {
            log.error("Provider not found: {}", providerKey);
            throw new RuntimeException("Provider not found: " + providerKey);
        }

        if (!providerConfig.isEnabled()) {
            log.error("Provider is disabled: {}", providerKey);
            throw new RuntimeException("Provider is disabled: " + providerKey);
        }

        try {
            // Determine model to use
            String model = StringUtils.hasText(options.getModel())
                ? options.getModel()
                : providerConfig.getModel();
                
            // Build request headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            if ("azure-openai".equals(providerConfig.getType())) {
                // Azure OpenAI uses api-key header
                headers.set("api-key", providerConfig.getApiKey());
            } else {
                // Standard OpenAI uses Bearer token
                headers.set("Authorization", "Bearer " + providerConfig.getApiKey());
            }
            
            // Build request payload
            JSONObject payload = new JSONObject();
            payload.put("model", model);
            payload.put("temperature", options.getTemperature());
            payload.put("top_p", options.getTopP());
            payload.put("max_tokens", options.getMaxTokens());
            
            // Create messages array
            JSONArray messages = new JSONArray();
            JSONObject message = new JSONObject();
            message.put("role", "user");
            
            // Handle content based on whether images are present
            if (!CollectionUtils.isEmpty(imageUrls)) {
                // For multimodal content, we need to create a content array
                JSONArray content = new JSONArray();
                
                // Add text content
                JSONObject textContent = new JSONObject();
                textContent.put("type", "text");
                textContent.put("text", prompt);
                content.add(textContent);
                
                // Add image content
                for (String imageUrl : imageUrls) {
                    JSONObject imageContent = new JSONObject();
                    imageContent.put("type", "image_url");
                    
                    JSONObject imageUrlObj = new JSONObject();
                    imageUrlObj.put("url", imageUrl);
                    imageContent.put("image_url", imageUrlObj);
                    
                    content.add(imageContent);
                }
                
                message.put("content", content);
            } else {
                // Simple text message
                message.put("content", prompt);
            }
            
            messages.add(message);
            payload.put("messages", messages);
            
            // Determine the endpoint URL
            String endpointUrl;
            if ("azure-openai".equals(providerConfig.getType())) {
                // For Azure OpenAI
                String baseUrl = StringUtils.trimTrailingCharacter(providerConfig.getEndpoint(), '/');
                endpointUrl = String.format("%s/openai/deployments/%s/chat/completions?api-version=%s",
                    baseUrl,
                    providerConfig.getDeploymentId(),
                    providerConfig.getApiVersion());
            } else {
                // For standard OpenAI
                endpointUrl = StringUtils.trimTrailingCharacter(providerConfig.getEndpoint(), '/') + "/chat/completions";
            }
            
            log.info("Sending request to {}: {}", providerKey, payload);
            
            // Send the request
            HttpEntity<String> entity = new HttpEntity<>(payload.toJSONString(), headers);
            ResponseEntity<String> response = restTemplate.exchange(
                endpointUrl,
                HttpMethod.POST,
                entity,
                String.class
            );
            
            // Parse the response
            JSONObject result = JSON.parseObject(response.getBody());
            log.info("Response from {}: {}", providerKey, result);
            
            // Extract the response text
            String responseText = result.getJSONArray("choices")
                .getJSONObject(0)
                .getJSONObject("message")
                .getString("content");
                
            return responseText;

        } catch (Exception e) {
            log.error("Error calling AI service", e);
            return null;
        }
    }

}

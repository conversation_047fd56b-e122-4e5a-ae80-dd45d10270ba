package ai.conrain.aigc.platform.integration.ai.imageAnalysis;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisTaskResponse;
import ai.conrain.aigc.platform.integration.ai.model.ImageSimpleCaption;
import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 图像分析服务实现类
 * 调用外部OpenAPI进行图像打标分析
 * https://conrain.yuque.com/org-wiki-conrain-pcgdb4/icf3gp/ih2z7d775fuo2rnv?singleDoc#
 */
@Slf4j
@Service
public class ImageAnalysisService {

    @Value("${ai.imageAnalysis.baseUrl}")
    private String BASE_URL;

    @Value("${ai.imageAnalysis.apiKey}")
    private String API_KEY = "${apikey}";

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private OssService ossService;

    @Autowired
    private TairService tairService;

    public ImageAnalysisTaskResponse createAnalysisTaskWithNotify(String imageUrl, boolean clothOnly) {
        return this.createAnalysisTask(imageUrl, "gemini-flash", clothOnly, true, null, true, false);
    }

    public ImageAnalysisTaskResponse createAnalysisTask(String imageUrl, boolean clothOnly) {
        return this.createAnalysisTask(imageUrl, "gemini-flash", clothOnly, true, null, false, false);
    }

    public ImageAnalysisTaskResponse createSimpleCaptionTask(String imageUrl, boolean clothOnly) {
        return this.createAnalysisTask(imageUrl, "gemini-flash", clothOnly, true, null, false, true);
    }

    public ImageAnalysisTaskResponse createAnalysisTask(String imageUrl, String model, boolean clothOnly, boolean useGenreClassifyModel, String clothType, boolean asyncNotify, boolean simpleCaption) {
        if (StringUtils.isBlank(imageUrl)) {
            throw new IllegalArgumentException("imageUrl must not be blank");
        }
        if (StringUtils.isBlank(model)) {
            model = "gemini-flash";
        }

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + API_KEY);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("image_input", ossService.resize1024(imageUrl));
            requestBody.put("model", model);
            requestBody.put("cloth_only", clothOnly);
            requestBody.put("use_intended_use_classifier", useGenreClassifyModel);
            if (StringUtils.isNotBlank(clothType)) {
                requestBody.put("cloth_type", clothType);
            }
            requestBody.put("notify_return", asyncNotify);

            if (simpleCaption) {
                requestBody.put("fast_mode", true);
            }

            // 发送HTTP请求
            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(requestBody), headers);

            String CREATE_TASK_URL = BASE_URL + "/tasks";
            ResponseEntity<String> responseEntity = restTemplate.exchange(CREATE_TASK_URL, HttpMethod.POST, requestEntity, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("createAnalysisTask response: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                ImageAnalysisTaskResponse response = new ImageAnalysisTaskResponse();
                response.setTaskId(jsonResponse.getString("task_id"));
                response.setStatus(jsonResponse.getString("status"));
                response.setMessage(jsonResponse.getString("message"));
                response.setCreatedAt(jsonResponse.getString("created_at"));

                return response;
            } else {
                log.error("createAnalysisTask error: status={}, response={}", responseEntity.getStatusCodeValue(), responseEntity.getBody());
                throw new RuntimeException("Image analysis task creation failed: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("createAnalysisTask error", e);
            throw new RuntimeException("Image analysis task creation error: " + e.getMessage());
        }
    }

    /**
     * 将新的API响应结构转换为ImagePreCaption对象
     * 处理Pascal Case到snake_case的字段映射
     */
    private ImageSimpleCaption convertToImageSimpleCaption(JSONObject analysis) {

        ImageSimpleCaption preCaption = new ImageSimpleCaption();

        // 解析模特信息
        if (analysis.containsKey("Model")) {
            JSONObject model = analysis.getJSONObject("Model");

            if (model.containsKey("Race")) {
                preCaption.setRace(model.getString("Race"));
            }

            if (model.containsKey("Age")) {
                preCaption.setAge(model.getString("Age"));
            }

            if (model.containsKey("Body_Shape")) {
                preCaption.setBodyShape(model.getString("Body_Shape"));
            }

            if (model.containsKey("Gender")) {
                preCaption.setGender(model.getString("Gender"));
            }
        }

        // 解析服装信息
        if (analysis.containsKey("Clothing")) {
            JSONObject clothing = analysis.getJSONObject("Clothing");

            // 处理Style部分
            if (clothing.containsKey("Style")) {
                JSONObject style = clothing.getJSONObject("Style");

                // 处理Upper_Garment
                if (style.containsKey("Upper_Garment")) {
                    JSONObject upperGarment = style.getJSONObject("Upper_Garment");
                    for (String key : upperGarment.keySet()) {
                        String value = upperGarment.getString(key);
                        preCaption.setTopCate1(key);
                        preCaption.setTopCate2(value);
                        break; // 只取第一个键值对
                    }
                }

                // 处理Lower_Garment
                if (style.containsKey("Lower_Garment")) {
                    JSONObject lowerGarment = style.getJSONObject("Lower_Garment");
                    for (String key : lowerGarment.keySet()) {
                        String value = lowerGarment.getString(key);
                        preCaption.setBottomCate1(key);
                        preCaption.setBottomCate2(value);
                        break; // 只取第一个键值对
                    }
                }

                // 处理One_Piece
                if (style.containsKey("One_Piece")) {
                    JSONObject onePiece = style.getJSONObject("One_Piece");
                    for (String key : onePiece.keySet()) {
                        String value = onePiece.getString(key);
                        preCaption.setOnePieceCate1(key);
                        preCaption.setOnePieceCate2(value);
                        break; // 只取第一个键值对
                    }
                }
            }

            // 处理Suit部分
            if (clothing.containsKey("Suit")) {
                JSONObject suit = clothing.getJSONObject("Suit");
                for (String key : suit.keySet()) {
                    String value = suit.getString(key);
                    preCaption.setSuitCate1(key);
                    preCaption.setSuitCate2(value);
                    break; // 只取第一个键值对
                }
            }

            // 处理Usage部分
            if (clothing.containsKey("Usage")) {
                JSONObject usage = clothing.getJSONObject("Usage");
                for (String key : usage.keySet()) {
                    String value = usage.getString(key);
                    preCaption.setUsageCate1(key);
                    preCaption.setUsageCate2(value);
                    break; // 只取第一个键值对
                }
            }
        }

        return preCaption;
    }

    public ImageAnalysisResult getAnalysisResult(String taskId) {
        if (StringUtils.isBlank(taskId)) {
            throw new IllegalArgumentException("taskId must not be blank");
        }

        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + API_KEY);

            // 发送HTTP请求
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);

            String GET_TASK_URL = BASE_URL + "/tasks/";
            ResponseEntity<String> responseEntity = restTemplate.exchange(GET_TASK_URL + taskId, HttpMethod.GET, requestEntity, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                String responseBody = responseEntity.getBody();
                log.info("getAnalysisResult response: {}", responseBody);

                JSONObject jsonResponse = JSON.parseObject(responseBody);
                ImageAnalysisResult result = new ImageAnalysisResult();
                result.setTaskId(taskId);
                result.setStatus(jsonResponse.getString("status"));

                // 如果任务完成，解析分析结果
                if ("completed".equals(jsonResponse.getString("status"))) {
                    String mode = jsonResponse.getJSONObject("result").getString("mode");
                    result.setMode(mode);

                    JSONObject analysis = jsonResponse.getJSONObject("result").getJSONObject("analysis");
                    // 处理"None"字符串，转换为null
                    IntegrationUtils.processNoneToNull(analysis);

                    if (analysis != null) {

                        // 判断数据结构类型
                        if (StringUtils.equalsIgnoreCase(mode, "simple")) {
                            result.setPreCaption(convertToImageSimpleCaption(analysis));
                        } else {
                            // 详细打标结构 "detail"
                            result.setAnalysis(analysis.toJavaObject(ImageAnalysisCaption.class));
                        }
                    }

                    result.setRaw(jsonResponse.getJSONObject("result").toJSONString());
                } else if ("failed".equals(jsonResponse.getString("status"))) {
                    result.setErrorMessage(jsonResponse.getString("error"));
                }

                return result;
            } else {
                log.error("getAnalysisResult error: status={}, response={}", responseEntity.getStatusCodeValue(), responseEntity.getBody());
                throw new RuntimeException("Get analysis result failed: " + responseEntity.getBody());
            }
        } catch (Exception e) {
            log.error("getAnalysisResult error", e);
            throw new RuntimeException("Get analysis result error: " + e.getMessage());
        }
    }

    /**
     * 同步获取图片分析结果
     *
     * @param clothUrl
     * @param clothOnly
     * @return
     * @throws InterruptedException
     */
    public ImageAnalysisResult syncImagePreCaption(String clothUrl, boolean clothOnly) {

        ImageAnalysisTaskResponse task = this.createSimpleCaptionTask(clothUrl, clothOnly);

        // 添加超时计时器，最大等待60秒
        long startTime = System.currentTimeMillis();
        final long MAX_WAIT_TIME = 60 * 1000; // 60秒

        for (;;) {

            // 检查是否超时
            if (System.currentTimeMillis() - startTime > MAX_WAIT_TIME) {
                throw new RuntimeException("Image analysis processing timeout after 60 seconds");
            }

            ImageAnalysisResult result = this.getAnalysisResult(task.getTaskId());

            if (result != null && result.getStatus().equals("completed")) {
                return result;
            } else if (result != null && result.getStatus().equals("failed")) {
                return result;
            } else if (result != null && result.getStatus().equalsIgnoreCase("processing")) {
                try {
                    Thread.sleep(5 * 1000);
                } catch (InterruptedException e) {
                    // ignore
                }
            }
        }
    }


    /**
     * 同步获取图片分析结果
     *
     * @param clothUrl
     * @param clothOnly
     * @return
     * @throws InterruptedException
     */
    public ImageAnalysisResult syncImagePreCaptionWithCache(String clothUrl, boolean clothOnly) {

        String cacheKey = DigestUtils.md5Hex(clothUrl + "pre");
        ImageAnalysisResult result = tairService.getObject(cacheKey, ImageAnalysisResult.class);
        log.info("syncImageAnalysisWithCache cacheKey: {}, result: {}", cacheKey, result);

        if (result == null || result.getAnalysis() == null || !SearchUtil.isValidImageAnalysisCloth(result.getAnalysis())) {

            result = this.syncImagePreCaption(clothUrl, clothOnly);
            if (result != null && result.getStatus().equals("completed")) {
                tairService.setObject(cacheKey, result, 60 * 60 * 24);
            }
        }

        return result;
    }

    /**
     * 同步获取图片分析结果
     *
     * @param clothUrl
     * @param clothOnly
     * @return
     * @throws InterruptedException
     */
    public ImageAnalysisResult syncImageAnalysis(String clothUrl, boolean clothOnly) {

        ImageAnalysisTaskResponse task = this.createAnalysisTask(clothUrl, clothOnly);

        // 添加超时计时器，最大等待60秒
        long startTime = System.currentTimeMillis();
        final long MAX_WAIT_TIME = 60 * 1000; // 60秒

        for (; ; ) {

            // 检查是否超时
            if (System.currentTimeMillis() - startTime > MAX_WAIT_TIME) {
                throw new RuntimeException("Image analysis processing timeout after 60 seconds");
            }

            ImageAnalysisResult result = this.getAnalysisResult(task.getTaskId());

            if (result != null && result.getStatus().equals("completed")) {
                return result;
            } else if (result != null && result.getStatus().equals("failed")) {
                return result;
            } else if (result != null && result.getStatus().equalsIgnoreCase("processing")) {
                try {
                    Thread.sleep(5 * 1000);
                } catch (InterruptedException e) {
                    // ignore
                }
            }
        }
    }

    /**
     * 同步获取图片分析结果
     *
     * @param clothUrl
     * @param clothOnly
     * @return
     * @throws InterruptedException
     */
    public ImageAnalysisResult syncImageAnalysisWithCache(String clothUrl, boolean clothOnly) {
        String cacheKey = DigestUtils.md5Hex(clothUrl);
        ImageAnalysisResult result = tairService.getObject(cacheKey, ImageAnalysisResult.class);
        log.info("syncImageAnalysisWithCache cacheKey: {}, result: {}", cacheKey, result);

        if (result == null || result.getAnalysis() == null || !SearchUtil.isValidImageAnalysisCloth(result.getAnalysis())) {

            result = this.syncImageAnalysis(clothUrl, clothOnly);
            if (result != null && result.getStatus().equals("completed")) {
                tairService.setObject(cacheKey, result, 60 * 60 * 24);
            }
        }

        return result;
    }
}
/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.deerapi;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 来源类型枚举
 *
 * <AUTHOR>
 * @version : SourceTypeEnum.java, v 0.1 2025/9/18 14:50 renxiao.wu Exp $
 */
@Getter
public enum SourceTypeEnum {
    COMMON("COMMON", "通用"),

    GOOGLE_IMAGE("GOOGLE_IMAGE", "谷歌内容生成"),

    DOUBAO("DOUBAO", "豆包"),

    ;

    /** 枚举码 */
    private final String code;

    /** 枚举描述 */
    private final String desc;

    private SourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据枚举码获取枚举
     *
     * @param code 枚举码
     * @return 对应枚举
     */
    public static SourceTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (SourceTypeEnum item : values()) {
            if (StringUtils.equals(item.getCode(), code)) {
                return item;
            }
        }

        return null;
    }
}

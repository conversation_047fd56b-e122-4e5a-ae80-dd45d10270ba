package ai.conrain.aigc.platform.integration.ffmpeg.config;

import ai.conrain.aigc.platform.integration.utils.FileUtils;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.InputStream;
import java.net.JarURLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Enumeration;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * FFmpeg配置类
 * 映射application.yml中的ffmpeg配置
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "ffmpeg")
public class FFmpegConfig {

    private String binPath;

    /**
     * FFmpeg库配置
     */
    private Lib lib = new Lib();

    /**
     * 输入文件配置
     */
    private Input input = new Input();

    /**
     * 输出文件配置
     */
    private Output output = new Output();

    /**
     * FFmpeg库配置内部类
     */
    @Data
    public static class Lib {
        /**
         * FFmpeg可执行文件路径
         */
        private String path;
    }

    /**
     * 输入文件配置内部类
     */
    @Data
    public static class Input {
        /**
         * 输入文件目录路径
         */
        private String path;
    }

    /**
     * 输出文件配置内部类
     */
    @Data
    public static class Output {
        /**
         * 输出文件目录路径
         */
        private String path;
    }


    /**
     * 获取FFmpeg工作目录
     * 动态解析操作系统并定位FFmpeg工具包
     *
     * @return FFmpeg工作目录
     */
    public String getWorkDir() {
        return binPath;
    }

    public String getExecutablePath(){
        return binPath + File.separator + "ffmpeg";
    }


    /**
     * 获取输入文件的完整路径（包含相对路径）
     *
     * @param relativePath 相对路径
     * @param inputFile 输入文件名
     * @return 输入文件完整路径
     */
    public String getInputFilePath(String relativePath, String inputFile) {
        String inputPath = FileUtils.normalizePath(input.path);
        String normalizedRelativePath = FileUtils.normalizeRelativePath(relativePath);
        return inputPath + normalizedRelativePath + inputFile;
    }

    /**
     * 获取输出文件的完整路径（包含相对路径）
     *
     * @param relativePath 相对路径
     * @param outputPrefix 输出文件前缀
     * @param scale 数字位数，默认为4
     * @return 输出文件完整路径模式
     */
    public String getOutputFilePath(String relativePath, String outputPrefix, String outputSuffix, int scale) {
        String outputPath = FileUtils.normalizePath(output.path);
        String normalizedRelativePath = FileUtils.normalizeRelativePath(relativePath);
        return outputPath + normalizedRelativePath + outputPrefix + "_%0" + scale + "d_" + outputSuffix + ".jpg";
    }

    /**
     * 获取输出目录路径（包含相对路径）
     *
     * @param relativePath 相对路径
     * @return 输出目录完整路径
     */
    public String getOutputDirectoryPath(String relativePath) {
        String outputPath = FileUtils.normalizePath(output.path);
        String normalizedRelativePath = FileUtils.normalizeRelativePath(relativePath);
        return outputPath + normalizedRelativePath;
    }

    @PostConstruct
    public void init() {
        try {
            // 检测是否在JAR包中运行
            String classPathRoot = this.getClass().getClassLoader().getResource("").getPath();
            log.info("FFmpeg初始化 - 类路径根目录: {}", classPathRoot);
            
            boolean isJarEnvironment = classPathRoot.contains("!") || classPathRoot.contains("jar:");
            
            if (isJarEnvironment) {
                log.info("检测到JAR包环境，将提取FFmpeg可执行文件到临时目录");
                this.binPath = extractFFmpegFromJar();
            } else {
                log.info("检测到文件系统环境，使用类路径根目录");
                this.binPath = FileUtils.combinePath(classPathRoot, lib.path);
            }
            
            // 检查FFmpeg可执行文件是否存在
            String ffmpegExecutablePath = FileUtils.combinePath(binPath, "bin/ffmpeg");
            File ffmpegExecutable = new File(ffmpegExecutablePath);
            
            if (!ffmpegExecutable.exists()) {
                log.error("FFmpeg可执行文件未找到: {}", ffmpegExecutablePath);
                throw new RuntimeException("无法找到FFmpeg可执行文件，请检查配置和文件路径");
            }
            
            // 设置可执行权限
            if (!ffmpegExecutable.canExecute()) {
                boolean success = ffmpegExecutable.setExecutable(true);
                if (!success) {
                    log.warn("无法设置FFmpeg可执行文件的执行权限: {}", ffmpegExecutablePath);
                }
            }
            
            log.info("FFmpeg初始化成功 - 工作目录: {}", binPath);
            
        } catch (Exception e) {
            log.error("FFmpeg初始化失败", e);
        }
    }
    
    /**
     * 从JAR包中提取FFmpeg可执行文件到临时目录
     * 
     * @return 临时目录路径
     */
    private String extractFFmpegFromJar() {
        try {
            // 创建临时目录
            Path tempDir = Files.createTempDirectory("ffmpeg-extract-");
            String tempDirPath = tempDir.toString();
            log.info("创建FFmpeg临时目录: {}", tempDirPath);
            
            // 使用配置文件中的路径，递归提取整个目录
            String libPath = lib.path;
            if (!libPath.endsWith("/")) {
                libPath += "/";
            }
            
            // 递归提取整个FFmpeg目录结构
            extractDirectoryRecursively(libPath, tempDirPath);
            
            log.info("FFmpeg文件提取完成，临时目录: {}", tempDirPath);
            return tempDirPath;
            
        } catch (Exception e) {
            log.error("从JAR包提取FFmpeg文件失败", e);
            throw new RuntimeException("从JAR包提取FFmpeg文件失败", e);
        }
    }
    
    /**
     * 递归提取目录中的所有文件
     * 
     * @param sourceDir 源目录路径（相对于classpath）
     * @param targetDir 目标目录路径
     */
    private void extractDirectoryRecursively(String sourceDir, String targetDir) {
        try {
            // 移除末尾的斜杠
            if (sourceDir.endsWith("/")) {
                sourceDir = sourceDir.substring(0, sourceDir.length() - 1);
            }
            
            // 获取JAR包中的资源列表
            String resourcePath = sourceDir.startsWith("/") ? sourceDir.substring(1) : sourceDir;
            java.net.URL resourceUrl = this.getClass().getClassLoader().getResource(resourcePath);
            
            if (resourceUrl == null) {
                log.warn("资源目录不存在: {}", sourceDir);
                return;
            }
            
            // 如果是JAR包中的资源，使用特殊处理
            if (resourceUrl.getProtocol().equals("jar")) {
                extractFromJarResource(resourcePath, targetDir);
            } else {
                // 文件系统资源，直接复制
                extractFromFileSystem(resourcePath, targetDir);
            }
            
        } catch (Exception e) {
            log.error("递归提取目录失败: {} -> {}", sourceDir, targetDir, e);
        }
    }
    
    /**
     * 从JAR包资源中提取文件
     */
    private void extractFromJarResource(String resourcePath, String targetDir) {
        try {
            // 获取JAR包中的资源
            JarURLConnection jarConnection = (JarURLConnection) 
                this.getClass().getClassLoader().getResource(resourcePath).openConnection();
            JarFile jarFile = jarConnection.getJarFile();
            
            int extractedCount = 0;
            // 遍历JAR包中的条目
            Enumeration<JarEntry> entries = jarFile.entries();
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                String entryName = entry.getName();
                
                // 只处理指定目录下的文件
                if (entryName.startsWith(resourcePath + "/") && !entry.isDirectory()) {
                    String relativePath = entryName.substring(resourcePath.length() + 1);
                    String targetPath = targetDir + "/" + relativePath;
                    
                    // 创建目标目录
                    Path targetFile = Paths.get(targetPath);
                    Files.createDirectories(targetFile.getParent());
                    
                    // 提取文件
                    try (InputStream inputStream = jarFile.getInputStream(entry)) {
                        Files.copy(inputStream, targetFile, StandardCopyOption.REPLACE_EXISTING);
                        log.debug("提取文件: {} -> {}", entryName, targetPath);
                        extractedCount++;
                    }
                }
            }
            
            log.info("从JAR包提取完成，共提取 {} 个文件", extractedCount);
            
        } catch (Exception e) {
            log.error("从JAR包提取资源失败: {}", resourcePath, e);
        }
    }
    
    /**
     * 从文件系统提取文件
     */
    private void extractFromFileSystem(String resourcePath, String targetDir) {
        try {
            java.nio.file.Path sourcePath = Paths.get(this.getClass().getClassLoader().getResource(resourcePath).toURI());
            java.nio.file.Path targetPath = Paths.get(targetDir);
            
            Files.walk(sourcePath)
                .forEach(source -> {
                    try {
                        java.nio.file.Path dest = targetPath.resolve(sourcePath.relativize(source));
                        if (Files.isDirectory(source)) {
                            Files.createDirectories(dest);
                        } else {
                            Files.createDirectories(dest.getParent());
                            Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                            log.debug("复制文件: {} -> {}", source, dest);
                        }
                    } catch (Exception e) {
                        log.error("复制文件失败: {} -> {}", source, targetPath, e);
                    }
                });
                
        } catch (Exception e) {
            log.error("从文件系统提取资源失败: {}", resourcePath, e);
        }
    }
    
    
    
}

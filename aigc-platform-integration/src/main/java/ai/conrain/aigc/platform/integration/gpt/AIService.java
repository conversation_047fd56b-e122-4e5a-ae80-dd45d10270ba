package ai.conrain.aigc.platform.integration.gpt;

import java.util.List;

/**
 * Unified AI service interface for different AI providers
 */
public interface AIService {
    
    /**
     * Send a text prompt to the AI service
     * 
     * @param prompt The text prompt
     * @return The AI response text
     */
    String chat(String prompt);
    
    /**
     * Send a text prompt to the AI service with specific provider
     * 
     * @param prompt The text prompt
     * @param provider The provider to use
     * @return The AI response text
     */
    String chat(String prompt, String provider);
    
    /**
     * Send a text prompt with image URLs to the AI service
     * 
     * @param prompt The text prompt
     * @param imageUrls List of image URLs
     * @return The AI response text
     */
    String chat(String prompt, List<String> imageUrls);
    
    /**
     * Send a text prompt with image URLs to the AI service with specific provider
     * 
     * @param prompt The text prompt
     * @param imageUrls List of image URLs
     * @param provider The provider to use
     * @return The AI response text
     */
    String chat(String prompt, List<String> imageUrls, String provider);
    
    /**
     * Send a text prompt to the AI service with advanced options
     * 
     * @param prompt The text prompt
     * @param options Advanced options for the request
     * @return The AI response text
     */
    String chat(String prompt, GptOptions options);
    
    /**
     * Send a text prompt with image URLs to the AI service with advanced options
     * 
     * @param prompt The text prompt
     * @param imageUrls List of image URLs
     * @param options Advanced options for the request
     * @return The AI response text
     */
    String chat(String prompt, List<String> imageUrls, GptOptions options);
}

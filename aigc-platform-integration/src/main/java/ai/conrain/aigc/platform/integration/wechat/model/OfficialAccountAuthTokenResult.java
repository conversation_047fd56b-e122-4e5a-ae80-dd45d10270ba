package ai.conrain.aigc.platform.integration.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OfficialAccountAuthTokenResult extends BaseResponse {

    //token
    @JSONField(name = "access_token")
    private String accessToken;

    //token有效期，单位秒
    @JSONField(name = "expires_in")
    private Integer expiresIn;

    @JSONField(name = "refresh_token")
    private String refreshToken;

    @JSONField(name = "openid")
    private String openId;

    @JSONField(name = "unionid")
    private String unionId;
}

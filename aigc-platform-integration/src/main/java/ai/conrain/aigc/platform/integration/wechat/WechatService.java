package ai.conrain.aigc.platform.integration.wechat;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.integration.aliyun.TairService;
import ai.conrain.aigc.platform.integration.wechat.model.AccessToken;
import ai.conrain.aigc.platform.integration.wechat.model.OfficialAccountAuthTokenResult;
import ai.conrain.aigc.platform.integration.wechat.model.UserPhoneNumber;
import ai.conrain.aigc.platform.integration.wechat.model.WeChatJsApiTicketResponse;
import ai.conrain.aigc.platform.integration.wechat.model.WxUser;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class WechatService {

    @Autowired
    private OssService ossService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TairService tairService;

    //10分钟
    private static final Integer TEN_MINUTES = 10 * 60;

    private static final String ACCESS_TOKEN_REQ = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    private static final String PHONE_NUMBER_REQ = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=";

    private static final String GET_USER_OPEN_REQ = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    public static final String QR_CODE_REQ = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=";

    public static final String OA_AUTH_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";

    //正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
    public static enum WX_ENT_TYPE{
        release, trial, develop;
    }

    /**
     * 获取微信公众号用户授权token/openId
     * https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html
     *
     * @param appId
     * @param sec
     * @param code
     * @return
     */
    public OfficialAccountAuthTokenResult getUserTokenInfo4OfficialAccount(String appId, String sec, String code){

        HttpEntity<Map> entity = new HttpEntity<>(new HttpHeaders());
        ResponseEntity<String> httpRes = restTemplate.exchange(String.format(OA_AUTH_URL, appId, sec, code), HttpMethod.GET, entity, String.class);
        if (httpRes.getStatusCode() == HttpStatus.OK && !StringUtils.isEmpty(httpRes.getBody())) {
            OfficialAccountAuthTokenResult res = JSONObject.parseObject(httpRes.getBody(), OfficialAccountAuthTokenResult.class);
            if (res != null && res.getOpenId() != null) {
                return res;
            }
        }
        throw new RuntimeException("获取公众号用户授权信息失败,http res body:" + httpRes.getBody());
    }

    /**
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html">...</a>
     * 获取小程序全局唯一后台接口调用凭据，token有效期为7200s，开发者需要进行妥善保存
     */
    public String getAccessToken(String appId, String sec, boolean forceRefresh) {

        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(sec)) {
            throw new IllegalArgumentException("appId/sec不可为空");
        }

        String cacheKey = "AT_" + appId + sec;
        AccessToken cache = tairService.getObject(cacheKey, AccessToken.class);

        //没有缓存则刷新token
        if (cache == null || StringUtils.isEmpty(cache.getAccessToken()) || forceRefresh) {
            return refreshAccessToken(cacheKey, appId, sec);
        } else {
            log.info("不需要刷新，缓存中存在token");
            return cache.getAccessToken();
        }
    }

    private String refreshAccessToken(String cacheKey, String appId, String sec) {
        String url = String.format(ACCESS_TOKEN_REQ, appId, sec);
        try {
            HttpEntity<String> entity = new HttpEntity<>(new HttpHeaders());
            ResponseEntity<String> httpRes = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            if (httpRes.getStatusCode() == HttpStatus.OK && !StringUtils.isEmpty(httpRes.getBody())) {
                AccessToken accessToken = JSON.parseObject(httpRes.getBody(), AccessToken.class);

                //token 7200秒过期，缓存10分钟600秒
                if (accessToken != null && !StringUtils.isEmpty(accessToken.getAccessToken())) {
                    //缓存过期时间戳
                    accessToken.setExpiresTimeMills(System.currentTimeMillis() + accessToken.getExpiresIn() * 1000);
                    tairService.setObject(cacheKey, accessToken, TEN_MINUTES);
                    return accessToken.getAccessToken();
                }
            }
        } catch (Throwable t) {
            log.error("getAccessToken http error", t);
        }
        throw new RuntimeException("系统异常，调微信获取access token失败");
    }

    /**
     * code只能使用一次，code的有效期为5min
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html">手机号验证</a>
     */
    public String getPhoneNumber(String appId, String appSec, String code){
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSec) || StringUtils.isEmpty(code)) {
            throw new IllegalArgumentException("appId/appSec/code不可为空");
        }

        String accessToken = getAccessToken(appId, appSec, false);

        String phone = doGetPhoneNumber(code, accessToken);
        if (StringUtils.isEmpty(phone)) {
            phone = doGetPhoneNumber(code, getAccessToken(appId, appSec, true));
            if (StringUtils.isEmpty(phone)) {
                throw new RuntimeException("获取用户手机号失败，重新刷新access token后依然失败");
            }
            log.warn("获取用户手机号失败，重新刷新access token后成功");
        }
        return phone;
    }

    private String doGetPhoneNumber(String code, String accessToken) {
        Map<String, String> body = new HashMap<>();
        body.put("code", code);
        HttpEntity<Map> entity = new HttpEntity<>(body, new HttpHeaders());

        ResponseEntity<String> httpRes = restTemplate.exchange(PHONE_NUMBER_REQ + accessToken, HttpMethod.POST, entity, String.class);
        if (httpRes.getStatusCode() == HttpStatus.OK && !StringUtils.isEmpty(httpRes.getBody())) {
            UserPhoneNumber userPhoneNumber = JSONObject.parseObject(httpRes.getBody(), UserPhoneNumber.class);
            if (userPhoneNumber != null && userPhoneNumber.getPhoneInfo() != null) {
                return userPhoneNumber.getPhoneInfo().getPurePhoneNumber();
            }
        }
        return null;
    }

    /**
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html">...</a>
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/login.html">小程序登录</a>
     * @param appSec
     * @param code
     * @return
     */
    public String getUserOpenId(String appId, String appSec, String code){
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSec) || StringUtils.isEmpty(code)) {
            throw new IllegalArgumentException("appId/appSec/code不可为空");
        }

        HttpEntity<Map> entity = new HttpEntity<>(new HttpHeaders());
        ResponseEntity<String> httpRes = restTemplate.exchange(String.format(GET_USER_OPEN_REQ, appId, appSec, code), HttpMethod.GET, entity, String.class);
        if (httpRes.getStatusCode() == HttpStatus.OK && !StringUtils.isEmpty(httpRes.getBody())) {
            WxUser res = JSONObject.parseObject(httpRes.getBody(), WxUser.class);
            if (res != null && res.getOpenId() != null) {
                return res.getOpenId();
            }
        }
        throw new RuntimeException("获取用户openId失败,http res body:" + httpRes.getBody());
    }

    /**
     * https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html
     * @param appId
     * @param appSec
     * @param page 小程序页面路径，必须是已经发布的小程序存在的页面（否则报错），例如 "pages/index/index"，根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
     * @param param 页面参数 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
     * @param envType 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     * @return
     */
    public String getMiniQRCodeImg(String appId, String appSec, String page, String param, WX_ENT_TYPE envType){
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(appSec) || StringUtils.isBlank(param)) {
            throw new IllegalArgumentException("appId/appSec/page/param不可为空");
        }

        if (StringUtils.length(param) > 32) {
            throw new IllegalArgumentException("param长度不可超过32:" + param);
        }

        if (envType == null) {
            envType = WX_ENT_TYPE.release;
        }

        String accessToken = getAccessToken(appId, appSec, false);

        byte[] img = doGetQR(page, param, envType, accessToken);

        if (ArrayUtils.isEmpty(img)) {
            img = doGetQR(page, param, envType, getAccessToken(appId, appSec, true));
        }
        if (ArrayUtils.isEmpty(img)) {
            throw new RuntimeException("获取小程序码失败，重新刷新access token后依然失败");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM/");
        String fileName = sdf.format(new Date()) + "mini_qrcode_" + appId + "_" + System.currentTimeMillis() + ".jpg";
        String url = ossService.upload(fileName, new ByteArrayInputStream(img));

        if (StringUtils.isBlank(url)) {
            throw new RuntimeException("上传小程序码生成图片失败");
        }

        return url;
    }

    private byte[] doGetQR(String page, String param, WX_ENT_TYPE envType, String accessToken) {
        Map<String, Object> body = new HashMap<>();
        body.put("scene", param);

        if (StringUtils.isNotBlank(page)) {
            body.put("page", page);
        }

        if (envType != null) {
            body.put("env_version", envType.name());
        }

        body.put("check_path", false);

        HttpEntity<Map> entity = new HttpEntity<>(body, new HttpHeaders());

        ResponseEntity<byte[]> httpRes = restTemplate.exchange(QR_CODE_REQ + accessToken, HttpMethod.POST, entity, byte[].class);

        if (httpRes.getStatusCode() == HttpStatus.OK && httpRes.getBody() != null) {
            if (isJson(httpRes.getBody())) {
                log.info("doGetQR rest response:{}", new String(httpRes.getBody(), StandardCharsets.UTF_8));
                return null;
            }

            return httpRes.getBody();
        } else{
            log.error("获取小程序码失败");
            return null;
        }
    }

    // 使用RestTemplate获取jsapi_ticket
    public String getJsApiTicket(String accessToken) {
        String jsapiTicketUrl = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=" + accessToken + "&type=jsapi";
        WeChatJsApiTicketResponse response = restTemplate.getForObject(jsapiTicketUrl, WeChatJsApiTicketResponse.class);
        // 检查响应并返回jsapi_ticket
        if (response != null && "0".equals(response.getErrcode())) {
            String ticket = response.getTicket();
            if (StringUtils.isNotBlank(ticket)) {
                tairService.setString("JT_" + accessToken, ticket, 3600);
            }

            return ticket;

        } else {
            log.error("获取jsapi ticket失败: {}", response);
            throw new RuntimeException("获取jsapi ticket失败");
        }
    }

    private boolean isJson(byte[] arr) {
        try {
            return JSONObject.parseObject(new String(arr, StandardCharsets.UTF_8)) != null;
        } catch (Exception e) {
            return false;
        }
    }
}

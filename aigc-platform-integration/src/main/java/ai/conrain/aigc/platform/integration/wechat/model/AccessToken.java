package ai.conrain.aigc.platform.integration.wechat.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccessToken extends BaseResponse {

    private static final long serialVersionUID = -6554089241197307215L;

    //token
    @JSONField(name = "access_token")
    private String accessToken;

    //token有效期，单位秒
    @JSONField(name = "expires_in")
    private Integer expiresIn;

    //token失效的时间戳
    private Long expiresTimeMills;
}


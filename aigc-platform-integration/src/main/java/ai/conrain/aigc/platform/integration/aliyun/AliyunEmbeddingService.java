package ai.conrain.aigc.platform.integration.aliyun;

import ai.conrain.aigc.platform.integration.embedding.EmbeddingService;
import ai.conrain.aigc.platform.integration.constant.ConfigConstant;
import com.alibaba.dashscope.embeddings.TextEmbedding;
import com.alibaba.dashscope.embeddings.TextEmbeddingParam;
import com.alibaba.dashscope.embeddings.TextEmbeddingResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.pgvector.PGvector;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿里云向量化服务
 */
@Slf4j
@Service
public class AliyunEmbeddingService implements EmbeddingService {

    @Autowired
    @Qualifier("extraLongRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 获取向量
     * @param text
     * @param dimension
     * @return
     */
    @Override
    public PGvector getEmbeddingByText(String text, int dimension) {
        List<PGvector> embeddings = this.getEmbeddingByTexts(Collections.singletonList(text), dimension);
        if (CollectionUtils.isEmpty(embeddings)) {
            return null;
        }
        return embeddings.get(0);
    }

    /**
     * 获取向量列表
     * https://help.aliyun.com/zh/model-studio/text-embedding-synchronous-api?spm=a2c4g.11186623.help-menu-2400256.d_2_7_0.52e94823jaxxIQ#fd8093725c0px
     *
     * @param texts
     * @param dimension
     * @return
     */
    @Override
    public List<PGvector> getEmbeddingByTexts(List<String> texts, int dimension) {
        log.info("aliyun getEmbeddingByTexts start, texts: {}, dimension: {}", texts, dimension);

        if (CollectionUtils.isEmpty(texts) || texts.size() > 10) {
            throw new RuntimeException("aliyun getEmbeddingByTexts texts.size 必须为1-10");
        }

        try {
            // 构建请求参数
            TextEmbeddingParam param = TextEmbeddingParam
                    .builder()
                    .apiKey(ConfigConstant.DASHSCOPE_API_KEY)
                    .model("text-embedding-v4")  // 使用text-embedding-v4模型
                    .texts(texts)  // 输入文本
                    .parameter("dimension", dimension)  // 指定向量维度（仅 text-embedding-v3及 text-embedding-v4支持该参数）
                    .build();

            // 创建模型实例并调用
            TextEmbedding textEmbedding = new TextEmbedding(ConfigConstant.DASHSCOPE_API_URL);
            TextEmbeddingResult result = textEmbedding.call(param);
            log.info("textEmbedding result: {}", result);
            if (result.getOutput() != null && result.getOutput().getEmbeddings() != null) {
                return result.getOutput().getEmbeddings().stream().map(embedding -> new PGvector(embedding.getEmbedding())).collect(Collectors.toList());
            }

        } catch (ApiException | NoApiKeyException e) {
            log.error("调用失败：{}", e.getMessage());
        }

        throw new RuntimeException("embedding计算调用失败");
    }

    /**
     * 多模态embedding计算文本向量
     * @param text
     * @return
     */
    @Override
    public PGvector getTextEmbeddingByMultiModalModel(String text){
        List<PGvector> list = this.getEmbeddingsByMultiModalModel(Collections.singletonList(Pair.of("text", text)));
        if (list != null){
            return list.get(0);
        }

        throw new RuntimeException("embedding计算调用失败");
    }

    /**
     * 多模态embedding计算图像向量
     * @param imgUrl
     * @return
     */
    @Override
    public PGvector getImgEmbeddingByMultiModalModel(String imgUrl){
        List<PGvector> list = this.getEmbeddingsByMultiModalModel(Collections.singletonList(Pair.of("image", imgUrl)));
        if (list != null){
            return list.get(0);
        }

        throw new RuntimeException("embedding计算调用失败");
    }

    /**
     * 根据阿里云文档实现的多模态向量化API调用方法
     * 支持文本、图像和视频的向量化处理
     * https://help.aliyun.com/zh/model-studio/multimodal-embedding-api-reference?spm=a2c4g.11186623.help-menu-2400256.d_2_8_0.2f124d4dEU6Zun&scm=20140722.H_2712517._.OR_help-T_cn~zh-V_1#0b996e6ff5j2a
     *
     * @param inputContents 包含不同类型内容的列表，每个元素是一个Map，包含"type"和"data"键
     *                     例如: [{"text": "这是一段文本"}, {"image": "base64://..."}]
     * @return 向量化结果，包含索引和向量数组
     */
    @Override
    public List<PGvector> getEmbeddingsByMultiModalModel(List<Pair<String, String>> inputContents) {
        try {
            // 准备请求URL
            String url = ConfigConstant.DASHSCOPE_API_URL + "/services/embeddings/multimodal-embedding/multimodal-embedding";

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + ConfigConstant.DASHSCOPE_API_KEY);
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求体
            JSONObject inputObject = new JSONObject();
            inputObject.put("contents", inputContents);

            JSONObject requestBody = new JSONObject();
            requestBody.put("model", "multimodal-embedding-v1");
            requestBody.put("input", inputObject);

            // 创建HttpEntity
            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析响应结果
                String responseBody = response.getBody();
                JSONObject responseObject = JSON.parseObject(responseBody);

                // 提取embedding数据
                JSONObject outputObject = responseObject.getJSONObject("output");
                JSONArray embeddingsArray = outputObject.getJSONArray("embeddings");
                List<MultiModalEmbeddingResult> embeddings = JSON.parseArray(embeddingsArray.toJSONString(), MultiModalEmbeddingResult.class);

                return embeddings.stream()
                        .map(embedding -> new PGvector(embedding.getEmbedding()))
                        .collect(Collectors.toList());
            } else {
                throw new RuntimeException("API调用失败，状态码：" + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("多模态向量化调用失败", e);
            throw new RuntimeException("多模态向量化调用失败：" + e.getMessage(), e);
        }
    }

    @Data
    private static class MultiModalEmbeddingResult {
        private int index;
        
        @JSONField(name = "embedding")
        private float[] embedding;
        
        private String type;
    }
}


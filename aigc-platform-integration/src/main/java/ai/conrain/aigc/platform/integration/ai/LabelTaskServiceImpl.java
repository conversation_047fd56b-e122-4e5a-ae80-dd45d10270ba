/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.ai;

import com.alibaba.fastjson.JSONObject;

import ai.conrain.aigc.platform.integration.ai.model.LabelApplyRequest;
import ai.conrain.aigc.platform.integration.ai.model.LabelStatusInfo;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static ai.conrain.aigc.platform.integration.utils.RestUtils.callJson;

/**
 * 打标任务服务实现类
 *
 * <AUTHOR>
 * @version : LabelTaskServiceImpl.java, v 0.1 2025/8/22 10:20 renxiao.wu Exp $
 */
@Slf4j
@Service
public class LabelTaskServiceImpl implements LabelTaskService {
    @Value("${label.service.url}")
    private String serviceUrl;
    @Value("${label.service.token}")
    private String token;

    @Autowired
    @Qualifier("longRestTemplate")
    private RestTemplate longRestTemplate;

    @Override
    public LabelStatusInfo apply(LabelApplyRequest request, String url) {
        JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(request));
        return callLabel(url + "/task/apply", params);
    }

    @Override
    public LabelStatusInfo query(Integer outTaskId, String taskType, String url) {
        Map<String, Object> params = new HashMap<>();
        params.put("taskId", outTaskId);
        params.put("type", taskType);
        return callLabel(url + "/task/query", params);
    }

    @Override
    public String jsonToPrompt(JSONObject json) {
        JSONObject params = new JSONObject();
        JSONObject analysis = new JSONObject();
        analysis.putAll(json);

        JSONObject input = new JSONObject();
        input.put("analysis", analysis);
        params.put("json_input", input.toJSONString());

        JSONObject result = callJson(serviceUrl + "/json-to-prompt", params, HttpMethod.POST, "Bearer " + token,
            longRestTemplate);
        return result != null ? result.getString("prompt") : null;
    }

    /**
     * 调用打标服务
     *
     * @param url    请求url
     * @param params 参数
     * @return 结果
     */
    private LabelStatusInfo callLabel(String url, Map<String, Object> params) {
        JSONObject result = callJson(url, params, HttpMethod.POST, longRestTemplate);
        if (result == null) {
            log.warn("调用打标服务失败，返回结果为空，{}", params);
            return null;
        }
        if (!result.getBoolean("success")) {
            log.warn("调用打标服务失败：code={},message={}", result.getString("code"), result.getString("message"));
            return null;
        }
        return result.getJSONObject("data").toJavaObject(LabelStatusInfo.class);
    }

}

package ai.conrain.aigc.platform.integration.wechat.model;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Setter
public class ImageQualityVO {

    /**
     * 是否为不良图像
     */
    @Getter
    @JsonProperty("is_bad_image")
    private Boolean badImage = false;

    /**
     * 错误类型（body_hollow、body_hollow、leg_error）
     */
    @Getter
    @JsonProperty("predicted_class")
    private String predictedClass;

    /**
     * 背景（单色背景、非单色背景）
     */
    @JsonProperty("background")
    private String background;

    // 背景转换
    public String getBackground() {
        switch (background) {
            case "单色背景":
                return "monochrome_background";
            case "非纯色背景":
                return "non_monochromatic_background";
            default:
                return null;
        }
    }

    /**
     * 获取所有的标签
     *
     * @return 标签列表
     */
    public List<String> getTags() {
        if (badImage == null || !badImage) {
            return null;
        }

        List<String> tags = new ArrayList<>();
        if (StringUtils.isNotBlank(predictedClass)) {
            tags.add(predictedClass);
        }
        if (StringUtils.isNotBlank(background)) {
            tags.add(getBackground());
        }

        return tags;
    }
}

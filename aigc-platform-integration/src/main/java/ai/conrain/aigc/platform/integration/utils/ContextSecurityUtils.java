/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2025 All Rights Reserved.
 */
package ai.conrain.aigc.platform.integration.utils;

import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**
 * 上下文安全工具类
 *
 * <AUTHOR>
 * @version : ContextSecurityUtils.java, v 0.1 2025/1/9 13:26 renxiao.wu Exp $
 */
public abstract class ContextSecurityUtils {
    /** 简单的手机号正则 */
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^[1][3-9][0-9]{9}$");

    /**
     * 手机号脱敏：显示前三位和后四位，中间替换为*
     *
     * @param phoneNumber 手机号
     * @return 脱敏后手机号
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber) || StringUtils.length(phoneNumber) < 7) {
            return phoneNumber; // 长度不符合的直接返回
        }
        return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 用户名脱敏：显示首位和末位，其余用*代替
     *
     * @param username 用户名
     * @return 脱敏后用户名
     */
    public static String maskUsername(String username) {
        if (StringUtils.isBlank(username) || StringUtils.length(username) < 2) {
            return username; // 长度不符合的直接返回
        }
        return username.charAt(0) + StringUtils.repeat("*", username.length() - 2) + username.charAt(
            username.length() - 1);
    }

    public static boolean isMobile(String mobile) {
        return MOBILE_PATTERN.matcher(mobile).matches();
    }
}

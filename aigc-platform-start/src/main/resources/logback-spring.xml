<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <springProperty scope="context" name="logging.path" source="logging.path"/>
    <property name="LOG_HOME" value="${logging.path}"/>
    <property name="LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level[%thread][%X{env}][%X{traceId}][%X{sessionId}][%X{userId}]%msg%n%throwable"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/application_%d{yyyy-MM-dd_HH}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <MaxHistory>10</MaxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="WEB-DIGEST-APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/web-digest_%d{yyyy-MM-dd_HH}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="DAL-DIGEST-APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/dal-digest_%d{yyyy-MM-dd_HH}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="SAL-DIGEST-APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/sal-digest_%d{yyyy-MM-dd_HH}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="BIZ-CHECK-APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/biz-check_%d{yyyy-MM-dd_HH}.%i.log</FileNamePattern>
            <MaxFileSize>1GB</MaxFileSize>
            <MaxHistory>10</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <logger name="ai.conrain.aigc" level="INFO" additivity="false">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="web-digest" level="INFO" additivity="false">
        <appender-ref ref="WEB-DIGEST-APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="dal-digest" level="INFO" additivity="false">
        <appender-ref ref="DAL-DIGEST-APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="sal-digest" level="INFO" additivity="false">
        <appender-ref ref="SAL-DIGEST-APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="biz-check" level="INFO" additivity="false">
        <appender-ref ref="BIZ-CHECK-APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="APPLICATION"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
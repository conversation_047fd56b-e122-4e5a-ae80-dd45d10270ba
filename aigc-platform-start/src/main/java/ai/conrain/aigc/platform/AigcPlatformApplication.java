package ai.conrain.aigc.platform;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableAspectJAutoProxy
//排除spring-security框架的默认登录校验相关配置
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class})
@EnableScheduling
public class AigcPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(AigcPlatformApplication.class, args);
    }
}